CMake Warning at CMakeLists.txt:82 (message):
  std::print is not available.  Will fall back to iostream.


CMake Warning (dev) at CMakeLists.txt:90 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- 
-- C++23 Features:
--   std::expected: 1
--   std::format: 1
--   std::print: 
-- ===============================================
-- 
-- Configuring done (0.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
[1/15] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
FAILED: CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o -MF CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o.d -o CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o -c /home/<USER>/database-service-build/src/core/connection_manager.cpp
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ConnectionManager::ConnectionManager(const std::string&, size_t, const dbservice::core::SSLConfig&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:54:63: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   54 |             if (*expected_connection && (*expected_connection)->isOpen()) {
      |                                                               ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:55:39: error: no matching function for call to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::push_back(std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type&)’
   55 |                 connections_.push_back(*expected_connection);
      |                 ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/vector:66,
                 from /home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:4,
                 from /home/<USER>/database-service-build/src/core/connection_manager.cpp:1:
/usr/include/c++/14/bits/stl_vector.h:1283:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(const value_type&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1283 |       push_back(const value_type& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:35: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘const std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&’ {aka ‘const std::shared_ptr<dbservice::core::Connection>&’}
 1283 |       push_back(const value_type& __x)
      |                 ~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_vector.h:1300:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(value_type&&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1300 |       push_back(value_type&& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1300:30: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&&’ {aka ‘std::shared_ptr<dbservice::core::Connection>&&’}
 1300 |       push_back(value_type&& __x)
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:56:24: error: could not convert ‘((std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>*)(& expected_connection))->std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::operator*()’ from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘bool’
   56 |             } else if (*expected_connection) {
      |                        ^~~~~~~~~~~~~~~~~~~~
      |                        |
      |                        std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/src/core/connection_manager.cpp:57:154: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   57 |                  utils::Logger::warning(std::format("Initial connection created but failed to open (ID: {}). Not adding to pool.", (*expected_connection)->getId()));
      |                                                                                                                                                          ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:59:144: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
   59 |                  utils::Logger::error(std::format("Initial connection creation failed to produce a connection object: {}", expected_connection.error()));
      |                                                                                                                                                ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:62:108: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
   62 |             utils::Logger::error(std::format("Initial connection creation failed: {}", expected_connection.error()));
      |                                                                                                            ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ConnectionManager::ConnectionManager(const std::string&, size_t, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:96:63: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   96 |             if (*expected_connection && (*expected_connection)->isOpen()) {
      |                                                               ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:97:39: error: no matching function for call to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::push_back(std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type&)’
   97 |                 connections_.push_back(*expected_connection);
      |                 ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(const value_type&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1283 |       push_back(const value_type& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:35: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘const std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&’ {aka ‘const std::shared_ptr<dbservice::core::Connection>&’}
 1283 |       push_back(const value_type& __x)
      |                 ~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_vector.h:1300:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(value_type&&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1300 |       push_back(value_type&& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1300:30: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&&’ {aka ‘std::shared_ptr<dbservice::core::Connection>&&’}
 1300 |       push_back(value_type&& __x)
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:98:24: error: could not convert ‘((std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>*)(& expected_connection))->std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::operator*()’ from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘bool’
   98 |             } else if (*expected_connection) {
      |                        ^~~~~~~~~~~~~~~~~~~~
      |                        |
      |                        std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/src/core/connection_manager.cpp:99:154: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   99 |                  utils::Logger::warning(std::format("Initial connection created but failed to open (ID: {}). Not adding to pool.", (*expected_connection)->getId()));
      |                                                                                                                                                          ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:101:144: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  101 |                  utils::Logger::error(std::format("Initial connection creation failed to produce a connection object: {}", expected_connection.error()));
      |                                                                                                                                                ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:104:108: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  104 |             utils::Logger::error(std::format("Initial connection creation failed: {}", expected_connection.error()));
      |                                                                                                            ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection()’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:143:22: error: ‘class std::vector<std::shared_ptr<dbservice::core::Connection> >’ has no member named ‘pop_front’
  143 |         connections_.pop_front();
      |                      ^~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:41: error: no match for ‘operator&&’ (operand types are ‘std::shared_ptr<dbservice::core::Connection>’ and ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’})
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                 ~~~~~~~~~~~~~~~~~~~~~~~ ^~ ~~~~~~~~~~~~~~~~~~~~~~~~
      |                 |                          |
      |                 |                          std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
      |                 std::shared_ptr<dbservice::core::Connection>
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:41: note: candidate: ‘operator&&(bool, bool)’ (built-in)
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:41: note:   no known conversion for argument 2 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘bool’
In file included from /usr/include/c++/14/valarray:605,
                 from /usr/include/nlohmann/detail/conversions/from_json.hpp:21,
                 from /usr/include/nlohmann/adl_serializer.hpp:14,
                 from /usr/include/nlohmann/json.hpp:34,
                 from /home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:8,
                 from /home/<USER>/database-service-build/src/core/connection_manager.cpp:5:
/usr/include/c++/14/bits/valarray_after.h:415:5: note: candidate: ‘template<class _Dom1, class _Dom2> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_Expr, std::_Expr, _Dom1, _Dom2>, typename std::__fun<std::__logical_and, typename _Dom1::value_type>::result_type> std::operator&&(const _Expr<_Dom1, typename _Dom1::value_type>&, const _Expr<_Dom2, typename _Dom2::value_type>&)’
  415 |     _DEFINE_EXPR_BINARY_OPERATOR(&&, struct std::__logical_and)
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::shared_ptr<dbservice::core::Connection>’ is not derived from ‘const std::_Expr<_Dom1, typename _Dom1::value_type>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note: candidate: ‘template<class _Dom> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_Expr, std::_Constant, _Dom, typename _Dom::value_type>, typename std::__fun<std::__logical_and, typename _Dom1::value_type>::result_type> std::operator&&(const _Expr<_Dom1, typename _Dom1::value_type>&, const typename _Dom::value_type&)’
  415 |     _DEFINE_EXPR_BINARY_OPERATOR(&&, struct std::__logical_and)
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::shared_ptr<dbservice::core::Connection>’ is not derived from ‘const std::_Expr<_Dom1, typename _Dom1::value_type>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note: candidate: ‘template<class _Dom> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_Constant, std::_Expr, typename _Dom::value_type, _Dom>, typename std::__fun<std::__logical_and, typename _Dom1::value_type>::result_type> std::operator&&(const typename _Dom::value_type&, const _Expr<_Dom1, typename _Dom1::value_type>&)’
  415 |     _DEFINE_EXPR_BINARY_OPERATOR(&&, struct std::__logical_and)
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘const std::_Expr<_Dom1, typename _Dom1::value_type>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note: candidate: ‘template<class _Dom> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_Expr, std::_ValArray, _Dom, typename _Dom::value_type>, typename std::__fun<std::__logical_and, typename _Dom1::value_type>::result_type> std::operator&&(const _Expr<_Dom1, typename _Dom1::value_type>&, const valarray<typename _Dom::value_type>&)’
  415 |     _DEFINE_EXPR_BINARY_OPERATOR(&&, struct std::__logical_and)
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::shared_ptr<dbservice::core::Connection>’ is not derived from ‘const std::_Expr<_Dom1, typename _Dom1::value_type>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note: candidate: ‘template<class _Dom> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_ValArray, std::_Expr, typename _Dom::value_type, _Dom>, typename std::__fun<std::__logical_and, typename _Dom1::value_type>::result_type> std::operator&&(const valarray<typename _Dom::value_type>&, const _Expr<_Dom1, typename _Dom1::value_type>&)’
  415 |     _DEFINE_EXPR_BINARY_OPERATOR(&&, struct std::__logical_and)
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/valarray_after.h:415:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘const std::_Expr<_Dom1, typename _Dom1::value_type>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/valarray:1206:1: note: candidate: ‘template<class _Tp> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_ValArray, std::_ValArray, _Tp, _Tp>, typename std::__fun<std::__logical_and, _Tp>::result_type> std::operator&&(const valarray<_Tp>&, const valarray<_Tp>&)’
 1206 | _DEFINE_BINARY_OPERATOR(&&, __logical_and)
      | ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/valarray:1206:1: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::shared_ptr<dbservice::core::Connection>’ is not derived from ‘const std::valarray<_Tp>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/valarray:1206:1: note: candidate: ‘template<class _Tp> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_ValArray, std::_Constant, _Tp, _Tp>, typename std::__fun<std::__logical_and, _Tp>::result_type> std::operator&&(const valarray<_Tp>&, const typename valarray<_Tp>::value_type&)’
 1206 | _DEFINE_BINARY_OPERATOR(&&, __logical_and)
      | ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/valarray:1206:1: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::shared_ptr<dbservice::core::Connection>’ is not derived from ‘const std::valarray<_Tp>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/valarray:1206:1: note: candidate: ‘template<class _Tp> std::_Expr<std::__detail::_BinClos<std::__logical_and, std::_Constant, std::_ValArray, _Tp, _Tp>, typename std::__fun<std::__logical_and, _Tp>::result_type> std::operator&&(const typename valarray<_Tp>::value_type&, const valarray<_Tp>&)’
 1206 | _DEFINE_BINARY_OPERATOR(&&, __logical_and)
      | ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/valarray:1206:1: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:45: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘const std::valarray<_Tp>’
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                                             ^~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:158:47: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
  158 |                 if ((*new_connection_expected)->isOpen()) {
      |                                               ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:159:38: error: cannot convert ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::shared_ptr<dbservice::core::Connection>’
  159 |                     returnConnection(*new_connection_expected);
      |                                      ^~~~~~~~~~~~~~~~~~~~~~~~
      |                                      |
      |                                      std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:83:55: note:   initializing argument 1 of ‘void dbservice::core::ConnectionManager::returnConnection(std::shared_ptr<dbservice::core::Connection>)’
   83 |     void returnConnection(std::shared_ptr<Connection> connection);
      |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:174:111: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  174 |             std::string errorMsg = std::format("Failed to create new connection: {}", new_connection_expected.error());
      |                                                                                                               ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: error: no match for ‘operator=’ (operand types are ‘std::shared_ptr<dbservice::core::Connection>’ and ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’})
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/memory:80,
                 from /home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:3:
/usr/include/c++/14/bits/shared_ptr.h:417:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<const std::shared_ptr<_Yp>&> std::shared_ptr<_Tp>::operator=(const std::shared_ptr<_Yp>&) [with _Tp = dbservice::core::Connection]’
  417 |         operator=(const shared_ptr<_Yp>& __r) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:417:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘const std::shared_ptr<_Tp>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:428:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<std::auto_ptr<_Up> > std::shared_ptr<_Tp>::operator=(std::auto_ptr<_Up>&&) [with _Tp = dbservice::core::Connection]’
  428 |         operator=(auto_ptr<_Yp>&& __r)
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:428:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘std::auto_ptr<_Up>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:445:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<std::shared_ptr<_Yp> > std::shared_ptr<_Tp>::operator=(std::shared_ptr<_Yp>&&) [with _Tp = dbservice::core::Connection]’
  445 |         operator=(shared_ptr<_Yp>&& __r) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:445:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘std::shared_ptr<_Tp>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:453:9: note: candidate: ‘template<class _Yp, class _Del> std::shared_ptr<_Tp>::_Assignable<std::unique_ptr<_Up, _Ep> > std::shared_ptr<_Tp>::operator=(std::unique_ptr<_Up, _Ep>&&) [with _Del = _Yp; _Tp = dbservice::core::Connection]’
  453 |         operator=(unique_ptr<_Yp, _Del>&& __r)
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:453:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘std::unique_ptr<_Tp, _Dp>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:413:19: note: candidate: ‘std::shared_ptr<_Tp>& std::shared_ptr<_Tp>::operator=(const std::shared_ptr<_Tp>&) [with _Tp = dbservice::core::Connection]’
  413 |       shared_ptr& operator=(const shared_ptr&) noexcept = default;
      |                   ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:413:29: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘const std::shared_ptr<dbservice::core::Connection>&’
  413 |       shared_ptr& operator=(const shared_ptr&) noexcept = default;
      |                             ^~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:437:7: note: candidate: ‘std::shared_ptr<_Tp>& std::shared_ptr<_Tp>::operator=(std::shared_ptr<_Tp>&&) [with _Tp = dbservice::core::Connection]’
  437 |       operator=(shared_ptr&& __r) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:437:30: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::shared_ptr<dbservice::core::Connection>&&’
  437 |       operator=(shared_ptr&& __r) noexcept
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: At global scope:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:398:57: error: no declaration matches ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::createConnection()’
  398 | std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::createConnection() {
      |                                                         ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:161:33: note: candidate is: ‘std::shared_ptr<dbservice::core::Connection> dbservice::core::ConnectionManager::createConnection()’
  161 |     std::shared_ptr<Connection> createConnection();
      |                                 ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:50:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   50 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:532:160: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘getId’
  532 |         utils::Logger::warning(std::format("ManagedConnection: Acquired connection (ID: {}) but it was not open. Returning to pool.", (*connection_expected_)->getId()));
      |                                                                                                                                                                ^~~~~
[2/15] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
FAILED: CMakeFiles/database-service.dir/src/api/api_server.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/api/api_server.cpp.o -MF CMakeFiles/database-service.dir/src/api/api_server.cpp.o.d -o CMakeFiles/database-service.dir/src/api/api_server.cpp.o -c /home/<USER>/database-service-build/src/api/api_server.cpp
/home/<USER>/database-service-build/src/api/api_server.cpp:237:26: error: ‘Response’ is not a member of ‘dbservice::api::ApiServer’
  237 | std::expected<ApiServer::Response, std::string> ApiServer::handleRequest(const ParsedRequest& request) {
      |                          ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:237:47: error: template argument 1 is invalid
  237 | std::expected<ApiServer::Response, std::string> ApiServer::handleRequest(const ParsedRequest& request) {
      |                                               ^
/home/<USER>/database-service-build/src/api/api_server.cpp:237:49: error: no declaration matches ‘int dbservice::api::ApiServer::handleRequest(const dbservice::api::ParsedRequest&)’
  237 | std::expected<ApiServer::Response, std::string> ApiServer::handleRequest(const ParsedRequest& request) {
      |                                                 ^~~~~~~~~
In file included from /home/<USER>/database-service-build/src/api/api_server.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:115:42: note: candidate is: ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(const dbservice::api::ParsedRequest&)’
  115 |     std::expected<Response, std::string> handleRequest(const ParsedRequest& request);
      |                                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:54:7: note: ‘class dbservice::api::ApiServer’ defined here
   54 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:437:30: error: no matching function for call to ‘dbservice::api::ApiServer::parseRequest(std::string&, std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
  437 |             if (!parseRequest(request, method, path, headers, body)) {
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:150:47: note: candidate: ‘std::expected<dbservice::api::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  150 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:150:47: note:   candidate expects 1 argument, 5 provided
/home/<USER>/database-service-build/src/api/api_server.cpp:439:59: error: conversion from ‘dbservice::api::Response’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
  439 |                 std::string response = createErrorResponse(400, "Bad Request", "Invalid request format");
      |                                        ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:446:65: error: no matching function for call to ‘dbservice::api::ApiServer::handleRequest(std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
  446 |             auto [responseHeaders, responseBody] = handleRequest(method, path, headers, body);
      |                                                    ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:115:42: note: candidate: ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(const dbservice::api::ParsedRequest&)’
  115 |     std::expected<Response, std::string> handleRequest(const ParsedRequest& request);
      |                                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:115:42: note:   candidate expects 1 argument, 4 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:495:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’)
  495 |     };
      |     ^
In file included from /usr/include/c++/14/functional:59,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:4:
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
In file included from /usr/include/c++/14/bits/char_traits.h:50,
                 from /usr/include/c++/14/string:42,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:2:
/usr/include/c++/14/type_traits: In substitution of ‘template<bool _Cond, class _Tp> using std::__enable_if_t = typename std::enable_if::type [with bool _Cond = false; _Tp = std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&]’:
/usr/include/c++/14/bits/std_function.h:353:8:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&, const dbservice::api::ParsedRequest&> >; _Tp = std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&; _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |               ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:2:   required by substitution of ‘template<class _Functor> std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>::_Requires<std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>::_Callable<_Functor, typename std::enable_if<(!(bool)std::is_same<typename std::remove_cv<typename std::remove_reference<_Tp>::type>::type, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::value), std::decay<_Ex> >::type::type, std::__invoke_result<typename std::enable_if<(!(bool)std::is_same<typename std::remove_cv<typename std::remove_reference<_Tp>::type>::type, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::value), std::decay<_Ex> >::type::type&, const dbservice::api::ParsedRequest&> >, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&> std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>::operator=(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:495:5:   required from here
  495 |     };
      |     ^
/usr/include/c++/14/type_traits:138:11: error: no type named ‘type’ in ‘struct std::enable_if<false, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&>’
  138 |     using __enable_if_t = typename enable_if<_Cond, _Tp>::type;
      |           ^~~~~~~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:495:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  495 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:506:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>’)
  506 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:506:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  506 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:525:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  525 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:530:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  530 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:530:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:530:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:530:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  530 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:530:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:530:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:542:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>’)
  542 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:542:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  542 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:560:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  560 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:565:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  565 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:565:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:565:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:565:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  565 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:565:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:565:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:577:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>’)
  577 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:577:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  577 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:595:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  595 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:600:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  600 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:600:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:600:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:600:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  600 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:600:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:600:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:612:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>’)
  612 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:612:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  612 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:645:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>’)
  645 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:645:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  645 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:673:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>’)
  673 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:673:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  673 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:690:22: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  690 |         if (userInfo.empty()) {
      |                      ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:695:34: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  695 |             {"username", userInfo["username"]},
      |                                  ^
/home/<USER>/database-service-build/src/api/api_server.cpp:695:34: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:695:34: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:696:31: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [6]’)
  696 |             {"email", userInfo["email"]},
      |                               ^
/home/<USER>/database-service-build/src/api/api_server.cpp:696:31: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:696:31: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:697:34: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  697 |             {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
      |                                  ^
/home/<USER>/database-service-build/src/api/api_server.cpp:697:34: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:697:34: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:697:65: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  697 |             {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
      |                                                                 ^
/home/<USER>/database-service-build/src/api/api_server.cpp:697:65: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:697:65: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:698:36: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [11]’)
  698 |             {"created_at", userInfo["created_at"]}
      |                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:698:36: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:698:36: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:702:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>’)
  702 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:702:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  702 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:720:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  720 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:724:44: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  724 |             std::string username = userInfo["username"];
      |                                            ^
/home/<USER>/database-service-build/src/api/api_server.cpp:724:44: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:724:44: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:739:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>’)
  739 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:739:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  739 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:758:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  758 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:763:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  763 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:763:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:763:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:763:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  763 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:763:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:763:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:795:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:75&, const auto:76&)>’)
  795 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:795:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:75&, const auto:76&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  795 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:75&, const auto:76&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:75&, const auto:76&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:75&, const auto:76&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:813:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  813 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:818:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  818 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:818:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:818:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:818:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  818 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:818:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:818:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:849:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>’)
  849 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:849:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  849 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:867:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  867 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:872:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  872 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:872:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:872:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:872:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  872 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:872:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:872:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:902:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>’)
  902 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:902:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  902 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:925:33: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘size’
  925 |                 {"rows", result.size()},
      |                                 ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: error: no matching function for call to ‘begin(std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >&)’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
In file included from /usr/include/c++/14/bits/range_access.h:36,
                 from /usr/include/c++/14/string:53:
/usr/include/c++/14/initializer_list:88:5: note: candidate: ‘template<class _Tp> constexpr const _Tp* std::begin(initializer_list<_Tp>)’
   88 |     begin(initializer_list<_Tp> __ils) noexcept
      |     ^~~~~
/usr/include/c++/14/initializer_list:88:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::initializer_list<_Tp>’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:52:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(_Container&)’
   52 |     begin(_Container& __cont) -> decltype(__cont.begin())
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:52:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(_Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36:   required from here
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:52:50: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘begin’
   52 |     begin(_Container& __cont) -> decltype(__cont.begin())
      |                                           ~~~~~~~^~~~~
/usr/include/c++/14/bits/range_access.h:63:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(const _Container&)’
   63 |     begin(const _Container& __cont) -> decltype(__cont.begin())
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:63:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(const _Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36:   required from here
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:63:56: error: ‘const class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘begin’
   63 |     begin(const _Container& __cont) -> decltype(__cont.begin())
      |                                                 ~~~~~~~^~~~~
/usr/include/c++/14/bits/range_access.h:95:5: note: candidate: ‘template<class _Tp, long unsigned int _Nm> constexpr _Tp* std::begin(_Tp (&)[_Nm])’
   95 |     begin(_Tp (&__arr)[_Nm]) noexcept
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:95:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   mismatched types ‘_Tp [_Nm]’ and ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
In file included from /usr/include/nlohmann/detail/conversions/from_json.hpp:21,
                 from /usr/include/nlohmann/adl_serializer.hpp:14,
                 from /usr/include/nlohmann/json.hpp:34,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:9:
/usr/include/c++/14/valarray:1227:5: note: candidate: ‘template<class _Tp> _Tp* std::begin(valarray<_Tp>&)’
 1227 |     begin(valarray<_Tp>& __va) noexcept
      |     ^~~~~
/usr/include/c++/14/valarray:1227:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::valarray<_Tp>’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1238:5: note: candidate: ‘template<class _Tp> const _Tp* std::begin(const valarray<_Tp>&)’
 1238 |     begin(const valarray<_Tp>& __va) noexcept
      |     ^~~~~
/usr/include/c++/14/valarray:1238:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::valarray<_Tp>’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: error: no matching function for call to ‘end(std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >&)’
/usr/include/c++/14/initializer_list:99:5: note: candidate: ‘template<class _Tp> constexpr const _Tp* std::end(initializer_list<_Tp>)’
   99 |     end(initializer_list<_Tp> __ils) noexcept
      |     ^~~
/usr/include/c++/14/initializer_list:99:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::initializer_list<_Tp>’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:74:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.end()) std::end(_Container&)’
   74 |     end(_Container& __cont) -> decltype(__cont.end())
      |     ^~~
/usr/include/c++/14/bits/range_access.h:74:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.end()) std::end(_Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36:   required from here
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:74:48: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘end’
   74 |     end(_Container& __cont) -> decltype(__cont.end())
      |                                         ~~~~~~~^~~
/usr/include/c++/14/bits/range_access.h:85:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.end()) std::end(const _Container&)’
   85 |     end(const _Container& __cont) -> decltype(__cont.end())
      |     ^~~
/usr/include/c++/14/bits/range_access.h:85:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.end()) std::end(const _Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36:   required from here
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:85:54: error: ‘const class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘end’
   85 |     end(const _Container& __cont) -> decltype(__cont.end())
      |                                               ~~~~~~~^~~
/usr/include/c++/14/bits/range_access.h:106:5: note: candidate: ‘template<class _Tp, long unsigned int _Nm> constexpr _Tp* std::end(_Tp (&)[_Nm])’
  106 |     end(_Tp (&__arr)[_Nm]) noexcept
      |     ^~~
/usr/include/c++/14/bits/range_access.h:106:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   mismatched types ‘_Tp [_Nm]’ and ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1249:5: note: candidate: ‘template<class _Tp> _Tp* std::end(valarray<_Tp>&)’
 1249 |     end(valarray<_Tp>& __va) noexcept
      |     ^~~
/usr/include/c++/14/valarray:1249:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::valarray<_Tp>’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1265:5: note: candidate: ‘template<class _Tp> const _Tp* std::end(const valarray<_Tp>&)’
 1265 |     end(const valarray<_Tp>& __va) noexcept
      |     ^~~
/usr/include/c++/14/valarray:1265:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:929:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::valarray<_Tp>’
  929 |             for (const auto& row : result) {
      |                                    ^~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:937:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>’)
  937 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:937:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  937 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:971:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>’)
  971 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:971:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  971 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>’ to ‘const std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>’ to ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const dbservice::api::ParsedRequest&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:974:26: error: ‘ParsedRequest’ is not a member of ‘dbservice::api::ApiServer’
  974 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:974:52: error: template argument 1 is invalid
  974 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:974:54: error: no declaration matches ‘int dbservice::api::ApiServer::parseRequest(const std::string&)’
  974 | std::expected<ApiServer::ParsedRequest, std::string> ApiServer::parseRequest(const std::string& request) {
      |                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:150:47: note: candidate is: ‘std::expected<dbservice::api::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  150 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:54:7: note: ‘class dbservice::api::ApiServer’ defined here
   54 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1015:31: error: ‘request’ was not declared in this scope
 1015 |     std::istringstream stream(request);
      |                               ^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1061:39: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::string_view&)’
 1061 |     std::istringstream iss(queryString);
      |                                       ^
In file included from /usr/include/c++/14/bits/quoted_string.h:38,
                 from /usr/include/c++/14/iomanip:50,
                 from /usr/include/c++/14/bits/fs_path.h:38,
                 from /usr/include/c++/14/filesystem:52,
                 from /usr/include/nlohmann/detail/meta/std_fs.hpp:22,
                 from /usr/include/nlohmann/detail/conversions/from_json.hpp:27:
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1061:39: note:   ‘std::string_view’ {aka ‘std::basic_string_view<char>’} is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 1061 |     std::istringstream iss(queryString);
      |                                       ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::string_view’ {aka ‘std::basic_string_view<char>’} to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘std::string dbservice::api::ApiServer::urlDecode(std::string_view)’:
/home/<USER>/database-service-build/src/api/api_server.cpp:1088:58: error: no matching function for call to ‘std::__cxx11::basic_istringstream<char>::basic_istringstream(std::basic_string_view<char>)’
 1088 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:663:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  663 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:663:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:1088:58: note:   ‘std::basic_string_view<char>’ is not derived from ‘const std::__cxx11::basic_string<char, std::char_traits<char>, _Alloc>’
 1088 |             std::istringstream iss(input.substr(i + 1, 2));
      |                                                          ^
/usr/include/c++/14/sstream:655:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  655 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:655:9: note:   candidate expects 3 arguments, 1 provided
/usr/include/c++/14/sstream:649:9: note: candidate: ‘template<class _SAlloc> std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const std::__cxx11::basic_string<_CharT, _Traits, _SAlloc>&, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  649 |         basic_istringstream(const basic_string<_CharT, _Traits, _SAlloc>& __str,
      |         ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:649:9: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:643:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(__string_type&&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  643 |       basic_istringstream(__string_type&& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:643:43: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>::__string_type&&’ {aka ‘std::__cxx11::basic_string<char>&&’}
  643 |       basic_istringstream(__string_type&& __str,
      |                           ~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:638:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode, const allocator_type&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode; allocator_type = std::allocator<char>]’
  638 |       basic_istringstream(ios_base::openmode __mode, const allocator_type& __a)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:638:7: note:   candidate expects 2 arguments, 1 provided
/usr/include/c++/14/sstream:632:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>&&) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:632:49: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::__cxx11::basic_istringstream<char>&&’
  632 |       basic_istringstream(basic_istringstream&& __rhs)
      |                           ~~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:615:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(const __string_type&, std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; __string_type = std::__cxx11::basic_string<char>; std::ios_base::openmode = std::ios_base::openmode]’
  615 |       basic_istringstream(const __string_type& __str,
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:615:48: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘const std::__cxx11::basic_istringstream<char>::__string_type&’ {aka ‘const std::__cxx11::basic_string<char>&’}
  615 |       basic_istringstream(const __string_type& __str,
      |                           ~~~~~~~~~~~~~~~~~~~~~^~~~~
/usr/include/c++/14/sstream:597:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream(std::ios_base::openmode) [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>; std::ios_base::openmode = std::ios_base::openmode]’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:597:46: note:   no known conversion for argument 1 from ‘std::basic_string_view<char>’ to ‘std::ios_base::openmode’
  597 |       basic_istringstream(ios_base::openmode __mode)
      |                           ~~~~~~~~~~~~~~~~~~~^~~~~~
/usr/include/c++/14/sstream:580:7: note: candidate: ‘std::__cxx11::basic_istringstream<_CharT, _Traits, _Alloc>::basic_istringstream() [with _CharT = char; _Traits = std::char_traits<char>; _Alloc = std::allocator<char>]’
  580 |       basic_istringstream()
      |       ^~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/sstream:580:7: note:   candidate expects 0 arguments, 1 provided
[3/15] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
ninja: build stopped: subcommand failed.
