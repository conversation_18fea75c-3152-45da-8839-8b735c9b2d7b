cmake_minimum_required(VERSION 3.20)
project(database-service VERSION 1.0.0 LANGUAGES CXX)

# Require C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Enable coroutines
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    add_compile_options(-fcoroutines)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    add_compile_options(-fcoroutines-ts)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    add_compile_options(/await)
endif()

# Add compiler warnings
add_compile_options(-Wall -Wextra -Wpedantic)

# Add optimization flags for Release builds
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

# Find required packages
find_package(Boost REQUIRED COMPONENTS system program_options)
find_package(PostgreSQL REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)
find_package(CURL REQUIRED)

# Try to find nlohmann_json package, otherwise use bundled version
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann_json package not found, using bundled version")
    # Add include directory for bundled nlohmann_json
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/third_party/nlohmann_json/include)
endif()

# Set include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${Boost_INCLUDE_DIRS}
    ${PostgreSQL_INCLUDE_DIRS}
    ${OPENSSL_INCLUDE_DIR}
    ${CURL_INCLUDE_DIRS}
)

# Create module build directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/modules)

# Module files
file(GLOB MODULE_SOURCES "src/modules/*.cppm")
set(MODULE_PCMS "")

# Define module dependencies
set(MODULE_DEPENDENCIES
    "database.api:database.core"
    "database.schema:database.core"
    "database.security:database.core"
    "database.service:database.core,database.api,database.schema,database.security"
    "database.main:database.service"
)

# Function to get module dependencies
function(get_module_dependencies module_name output_var)
    foreach(dep_entry ${MODULE_DEPENDENCIES})
        string(REPLACE ":" ";" dep_parts ${dep_entry})
        list(GET dep_parts 0 mod)
        if(${mod} STREQUAL ${module_name})
            list(GET dep_parts 1 deps)
            string(REPLACE "," ";" deps_list ${deps})
            set(${output_var} ${deps_list} PARENT_SCOPE)
            return()
        endif()
    endforeach()
    set(${output_var} "" PARENT_SCOPE)
endfunction()

# Generate rules for compiling modules
foreach(MODULE_SOURCE ${MODULE_SOURCES})
    get_filename_component(MODULE_NAME ${MODULE_SOURCE} NAME_WE)
    string(REPLACE "." "_" MODULE_TARGET_NAME ${MODULE_NAME})
    
    # Get module dependencies
    get_module_dependencies(${MODULE_NAME} MODULE_DEPS)
    set(MODULE_DEP_PCMS "")
    foreach(DEP ${MODULE_DEPS})
        list(APPEND MODULE_DEP_PCMS ${CMAKE_CURRENT_BINARY_DIR}/modules/${DEP}.pcm)
    endforeach()
    
    add_custom_command(
        OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/modules/${MODULE_NAME}.pcm
        COMMAND ${CMAKE_CXX_COMPILER} -std=c++23 
                $<$<CXX_COMPILER_ID:GNU>:-fcoroutines>
                $<$<CXX_COMPILER_ID:Clang>:-fcoroutines-ts>
                $<$<CXX_COMPILER_ID:MSVC>:/await>
                -fmodules-ts 
                -c ${MODULE_SOURCE} 
                -o ${CMAKE_CURRENT_BINARY_DIR}/modules/${MODULE_NAME}.pcm
                -I${CMAKE_CURRENT_SOURCE_DIR}/include
                ${Boost_INCLUDE_DIRS}
                ${PostgreSQL_INCLUDE_DIRS}
                ${OPENSSL_INCLUDE_DIR}
                ${CURL_INCLUDE_DIRS}
        DEPENDS ${MODULE_SOURCE} ${MODULE_DEP_PCMS}
        COMMENT "Building module ${MODULE_NAME}"
    )
    
    list(APPEND MODULE_PCMS ${CMAKE_CURRENT_BINARY_DIR}/modules/${MODULE_NAME}.pcm)
    
    # Create a custom target for this module
    add_custom_target(${MODULE_TARGET_NAME}_module DEPENDS ${CMAKE_CURRENT_BINARY_DIR}/modules/${MODULE_NAME}.pcm)
endforeach()

# Create a target for all modules
add_custom_target(modules DEPENDS ${MODULE_PCMS})

# Source files (excluding module files)
file(GLOB_RECURSE SOURCES "src/*.cpp")
list(FILTER SOURCES EXCLUDE REGEX ".*modules/.*")

# Create executable
add_executable(database-service ${SOURCES})
add_dependencies(database-service modules)

# Link libraries
target_link_libraries(database-service
    ${Boost_LIBRARIES}
    ${PostgreSQL_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${CURL_LIBRARIES}
    Threads::Threads
    pqxx
)

# Add nlohmann_json if found as a package
if(nlohmann_json_FOUND)
    target_link_libraries(database-service nlohmann_json::nlohmann_json)
endif()

# Install targets
install(TARGETS database-service
    RUNTIME DESTINATION bin
)

# Install configuration files
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/config/
    DESTINATION config
    FILES_MATCHING PATTERN "*.json"
)

# Install module files
install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/modules/
    DESTINATION lib/modules
    FILES_MATCHING PATTERN "*.pcm"
)
