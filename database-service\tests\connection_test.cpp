#include <gtest/gtest.h>
#include "database-service/core/connection.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/error_codes.hpp"
#include <memory>
#include <thread>
#include <chrono>

namespace dbservice::core {

class ConnectionTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Use a test connection string
        connectionString_ = "host=localhost port=5432 dbname=test_db user=test_user password=test_pass";
        sslConfig_.enabled = false;
    }

    void TearDown() override {
        // Cleanup
    }

    std::string connectionString_;
    SSLConfig sslConfig_;
};

class ConnectionManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        connectionString_ = "host=localhost port=5432 dbname=test_db user=test_user password=test_pass";
        maxConnections_ = 5;
        sslConfig_.enabled = false;
    }

    void TearDown() override {
        if (connectionManager_) {
            connectionManager_->shutdown();
        }
    }

    std::string connectionString_;
    size_t maxConnections_;
    SSLConfig sslConfig_;
    std::unique_ptr<ConnectionManager> connectionManager_;
};

// Connection Tests
TEST_F(ConnectionTest, ConstructorTest) {
    auto connection = std::make_shared<Connection>(connectionString_, false);
    ASSERT_NE(connection, nullptr);
    EXPECT_FALSE(connection->isOpen());
}

TEST_F(ConnectionTest, OpenCloseTest) {
    auto connection = std::make_shared<Connection>(connectionString_, false);
    
    // Note: This test may fail if no actual database is available
    // In a real test environment, you would have a test database running
    bool opened = connection->open();
    if (opened) {
        EXPECT_TRUE(connection->isOpen());
        connection->close();
        EXPECT_FALSE(connection->isOpen());
    } else {
        // If connection fails, that's expected in a test environment without a real DB
        EXPECT_FALSE(connection->isOpen());
    }
}

// Connection Manager Tests
TEST_F(ConnectionManagerTest, ConstructorTest) {
    connectionManager_ = std::make_unique<ConnectionManager>(connectionString_, maxConnections_, sslConfig_);
    ASSERT_NE(connectionManager_, nullptr);
    EXPECT_EQ(connectionManager_->getMaxConnections(), maxConnections_);
}

TEST_F(ConnectionManagerTest, SSLConfigTest) {
    sslConfig_.enabled = true;
    sslConfig_.mode = SSLMode::VerifyFull;
    sslConfig_.certPath = "/path/to/cert.pem";
    sslConfig_.keyPath = "/path/to/key.pem";
    
    connectionManager_ = std::make_unique<ConnectionManager>(connectionString_, maxConnections_, sslConfig_);
    
    const auto& config = connectionManager_->getSSLConfig();
    EXPECT_TRUE(config.enabled);
    EXPECT_EQ(config.mode, SSLMode::VerifyFull);
    EXPECT_EQ(config.certPath, "/path/to/cert.pem");
    EXPECT_EQ(config.keyPath, "/path/to/key.pem");
}

TEST_F(ConnectionManagerTest, SimplifiedSSLConstructorTest) {
    connectionManager_ = std::make_unique<ConnectionManager>(connectionString_, maxConnections_, true);
    
    const auto& config = connectionManager_->getSSLConfig();
    EXPECT_TRUE(config.enabled);
    EXPECT_EQ(config.mode, SSLMode::VerifyFull);
}

TEST_F(ConnectionManagerTest, MetricsTest) {
    connectionManager_ = std::make_unique<ConnectionManager>(connectionString_, maxConnections_, sslConfig_);
    
    // Initial metrics
    EXPECT_EQ(connectionManager_->getActiveConnectionCount(), 0);
    EXPECT_EQ(connectionManager_->getWaitingConnectionCount(), 0);
    EXPECT_EQ(connectionManager_->getMaxConnections(), maxConnections_);
    
    // Update metrics
    connectionManager_->updateMetrics();
    
    // Metrics should still be valid
    EXPECT_GE(connectionManager_->getIdleConnectionCount(), 0);
}

TEST_F(ConnectionManagerTest, ShutdownTest) {
    connectionManager_ = std::make_unique<ConnectionManager>(connectionString_, maxConnections_, sslConfig_);
    
    // Shutdown should not throw
    EXPECT_NO_THROW(connectionManager_->shutdown());
    
    // After shutdown, getting connections should return nullptr
    auto connection = connectionManager_->getConnection();
    EXPECT_EQ(connection, nullptr);
}

TEST_F(ConnectionManagerTest, ConnectionPoolTest) {
    connectionManager_ = std::make_unique<ConnectionManager>(connectionString_, maxConnections_, sslConfig_);
    
    // Try to get a connection (may fail if no DB available)
    auto connection = connectionManager_->getConnection();
    
    if (connection) {
        EXPECT_NE(connection, nullptr);
        EXPECT_GT(connectionManager_->getActiveConnectionCount(), 0);
        
        // Return connection
        connectionManager_->returnConnection(connection);
        
        // Connection should be returned to pool
        EXPECT_EQ(connectionManager_->getActiveConnectionCount(), 0);
    }
}

// Error Code Tests
TEST(ErrorCodeTest, DatabaseErrcTest) {
    auto ec = make_error_code(DatabaseErrc::ConnectionError);
    EXPECT_EQ(ec.category().name(), std::string("database"));
    EXPECT_NE(ec.message(), "");
    
    ec = make_error_code(DatabaseErrc::QueryError);
    EXPECT_EQ(ec.value(), static_cast<int>(DatabaseErrc::QueryError));
}

TEST(ErrorCodeTest, ErrorCategoryTest) {
    const auto& category = database_error_category();
    EXPECT_EQ(category.name(), std::string("database"));
    
    std::string message = category.message(static_cast<int>(DatabaseErrc::Timeout));
    EXPECT_FALSE(message.empty());
    EXPECT_NE(message, "Unknown database error");
}

// SSL Mode Tests
TEST(SSLModeTest, SSLModeEnumTest) {
    // Test that SSL modes are properly defined
    SSLMode mode = SSLMode::Disable;
    EXPECT_EQ(static_cast<int>(mode), 0);
    
    mode = SSLMode::VerifyFull;
    EXPECT_NE(static_cast<int>(mode), 0);
}

} // namespace dbservice::core
