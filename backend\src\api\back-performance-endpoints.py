#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Performance Metrics API Endpoints

This module provides API endpoints for collecting and retrieving performance metrics,
including Git operation metrics.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from ..monitoring.back-git-metrics import GitMetricsProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('performance_api.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('performance_api')

# Create Blueprint
performance_bp = Blueprint('performance', __name__, url_prefix='/api/performance')

# Initialize Git metrics processor
def get_git_metrics_processor():
    """Get or create Git metrics processor instance"""
    if not hasattr(current_app, 'git_metrics_processor'):
        db_config = current_app.config.get('DATABASE')
        redis_config = current_app.config.get('REDIS')
        current_app.git_metrics_processor = GitMetricsProcessor(db_config, redis_config)
    return current_app.git_metrics_processor


@performance_bp.route('/metrics', methods=['POST'])
@jwt_required()
def collect_metrics():
    """Collect performance metrics from clients
    
    Expects a JSON payload with a 'metrics' array containing performance metric objects
    """
    try:
        data = request.get_json()
        if not data or 'metrics' not in data:
            return jsonify({'error': 'Invalid payload format'}), 400
        
        metrics = data['metrics']
        if not isinstance(metrics, list):
            return jsonify({'error': 'Metrics must be an array'}), 400
        
        # Add user ID to each metric
        user_id = get_jwt_identity()
        for metric in metrics:
            if 'metadata' not in metric:
                metric['metadata'] = {}
            metric['metadata']['userId'] = user_id
        
        # Process Git metrics
        git_metrics = [m for m in metrics if m.get('category') == 'git']
        if git_metrics:
            processor = get_git_metrics_processor()
            processor.store_metrics(git_metrics)
        
        # Process other metric types here as needed
        
        return jsonify({'success': True, 'count': len(metrics)}), 201
    except Exception as e:
        logger.error(f"Error collecting metrics: {e}")
        return jsonify({'error': 'Failed to process metrics'}), 500


@performance_bp.route('/git-operations', methods=['GET'])
@jwt_required()
def get_git_operations():
    """Get recent Git operations
    
    Query parameters:
    - operation_type: Filter by operation type
    - limit: Maximum number of operations to return (default: 100)
    """
    try:
        operation_type = request.args.get('operation_type')
        limit = min(int(request.args.get('limit', 100)), 1000)  # Cap at 1000
        
        processor = get_git_metrics_processor()
        operations = processor.get_recent_operations(
            operation_type=operation_type,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'count': len(operations),
            'operations': operations
        })
    except Exception as e:
        logger.error(f"Error retrieving Git operations: {e}")
        return jsonify({'error': 'Failed to retrieve Git operations'}), 500


@performance_bp.route('/git-stats', methods=['GET'])
@jwt_required()
def get_git_stats():
    """Get Git operation statistics
    
    Query parameters:
    - operation_type: Filter by operation type
    - time_period: Time period for aggregation ('hourly', 'daily', 'weekly')
    - days: Number of days to include (default: 30)
    """
    try:
        operation_type = request.args.get('operation_type')
        time_period = request.args.get('time_period', 'daily')
        if time_period not in ['hourly', 'daily', 'weekly']:
            return jsonify({'error': 'Invalid time period'}), 400
            
        days = min(int(request.args.get('days', 30)), 365)  # Cap at 365
        
        processor = get_git_metrics_processor()
        stats = processor.get_operation_stats(
            operation_type=operation_type,
            time_period=time_period,
            days=days
        )
        
        return jsonify({
            'success': True,
            'count': len(stats),
            'time_period': time_period,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"Error retrieving Git statistics: {e}")
        return jsonify({'error': 'Failed to retrieve Git statistics'}), 500


@performance_bp.route('/dashboard-data', methods=['GET'])
@jwt_required()
def get_dashboard_data():
    """Get comprehensive data for the performance dashboard
    
    Returns recent operations and statistics for different time periods
    """
    try:
        processor = get_git_metrics_processor()
        
        # Get recent operations
        recent_operations = processor.get_recent_operations(limit=50)
        
        # Get statistics for different time periods
        hourly_stats = processor.get_operation_stats(time_period='hourly', days=2)
        daily_stats = processor.get_operation_stats(time_period='daily', days=30)
        weekly_stats = processor.get_operation_stats(time_period='weekly', days=90)
        
        # Calculate summary metrics
        operation_counts = {}
        avg_durations = {}
        error_rates = {}
        
        for stat in daily_stats:
            op_type = stat['operation_type']
            if op_type not in operation_counts:
                operation_counts[op_type] = 0
                avg_durations[op_type] = 0
                error_rates[op_type] = 0
            
            operation_counts[op_type] += stat['count']
            avg_durations[op_type] += stat['avg_duration'] * stat['count']
            error_rates[op_type] += stat['error_count']
        
        # Calculate averages
        for op_type in operation_counts:
            if operation_counts[op_type] > 0:
                avg_durations[op_type] /= operation_counts[op_type]
                error_rates[op_type] = error_rates[op_type] / operation_counts[op_type] * 100
        
        return jsonify({
            'success': True,
            'recent_operations': recent_operations,
            'hourly_stats': hourly_stats,
            'daily_stats': daily_stats,
            'weekly_stats': weekly_stats,
            'summary': {
                'operation_counts': operation_counts,
                'avg_durations': avg_durations,
                'error_rates': error_rates
            }
        })
    except Exception as e:
        logger.error(f"Error retrieving dashboard data: {e}")
        return jsonify({'error': 'Failed to retrieve dashboard data'}), 500


@performance_bp.route('/calculate-stats', methods=['POST'])
@jwt_required()
def trigger_stats_calculation():
    """Trigger calculation of Git operation statistics
    
    This endpoint is typically called by a scheduled job
    """
    try:
        # Check if user has admin privileges
        # This would typically use a role-based access control system
        user_id = get_jwt_identity()
        # TODO: Implement proper authorization check
        
        processor = get_git_metrics_processor()
        
        # Calculate stats for different time periods
        processor.calculate_and_store_stats('hourly')
        processor.calculate_and_store_stats('daily')
        processor.calculate_and_store_stats('weekly')
        
        return jsonify({'success': True, 'message': 'Statistics calculation triggered'})
    except Exception as e:
        logger.error(f"Error calculating statistics: {e}")
        return jsonify({'error': 'Failed to calculate statistics'}), 500


@performance_bp.route('/cleanup', methods=['POST'])
@jwt_required()
def trigger_data_cleanup():
    """Trigger cleanup of old performance data
    
    This endpoint is typically called by a scheduled job
    """
    try:
        # Check if user has admin privileges
        user_id = get_jwt_identity()
        # TODO: Implement proper authorization check
        
        # Get retention policy from configuration
        retention_policy = current_app.config.get('PERFORMANCE_RETENTION', {
            'detailed': 30,  # 30 days for detailed metrics
            'hourly': 7,     # 7 days for hourly stats
            'daily': 90      # 90 days for daily stats
        })
        
        processor = get_git_metrics_processor()
        processor.cleanup_old_data(retention_policy)
        
        return jsonify({
            'success': True, 
            'message': 'Data cleanup triggered',
            'retention_policy': retention_policy
        })
    except Exception as e:
        logger.error(f"Error cleaning up data: {e}")
        return jsonify({'error': 'Failed to clean up data'}), 500


# Register the blueprint in your Flask application
def register_performance_endpoints(app):
    app.register_blueprint(performance_bp)
    logger.info("Performance API endpoints registered")
