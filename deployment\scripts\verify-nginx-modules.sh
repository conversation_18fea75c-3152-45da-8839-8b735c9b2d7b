#!/bin/bash
set -e

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    local message="$2"
    
    case "$level" in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    log "ERROR" "This script must be run as root"
    exit 1
fi

# Verify module packages
required_packages=(
    "libnginx-mod-http-brotli-filter"
    "libnginx-mod-http-brotli-static"
    "libnginx-mod-http-image-filter"
)

log "INFO" "Verifying Nginx module packages..."
for pkg in "${required_packages[@]}"; do
    if ! dpkg -l | grep -q "^ii.*$pkg"; then
        log "ERROR" "Package $pkg is not installed"
        exit 1
    fi
    log "INFO" "✓ $pkg installed"
done

# Check Nginx configuration
log "INFO" "Checking Nginx configuration..."
if ! nginx -t &>/dev/null; then
    log "ERROR" "Nginx configuration test failed"
    nginx -t
    exit 1
fi
log "INFO" "✓ Nginx configuration valid"

# Check module loading
log "INFO" "Checking module loading..."
nginx_modules=$(nginx -V 2>&1)

if ! echo "$nginx_modules" | grep -q "http_brotli_module"; then
    log "ERROR" "Brotli module not loaded"
    exit 1
fi
log "INFO" "✓ Brotli module loaded"

if ! echo "$nginx_modules" | grep -q "http_image_filter_module"; then
    log "ERROR" "Image filter module not loaded"
    exit 1
fi
log "INFO" "✓ Image filter module loaded"

# Test Brotli compression
log "INFO" "Testing Brotli compression..."
test_file="/var/www/html/test.html"
echo "<html><body>test</body></html>" > "$test_file"

if ! curl -sI -H "Accept-Encoding: br" "http://localhost/test.html" | grep -qi "content-encoding: br"; then
    log "ERROR" "Brotli compression not working"
    rm -f "$test_file"
    exit 1
fi
rm -f "$test_file"
log "INFO" "✓ Brotli compression working"

# Test image filter
log "INFO" "Testing image filter..."
if ! curl -sI "http://localhost/resize/test.jpg" | grep -qi "image"; then
    log "WARN" "Image filter endpoint not configured"
else
    log "INFO" "✓ Image filter endpoint responding"
fi

log "INFO" "All module checks passed successfully"
