{"database": {"port": 5432, "host": "localhost", "user": "database_service", "password": "password2311", "name": "database_service"}, "project": {"remote_build_dir": "/home/<USER>/database-service-build", "description": "database-service for development environment", "name": "database-service", "remote_install_dir": "/opt/database-service", "local_source_dir": "D:\\Augment\\project-tracker\\database-service"}, "ssh": {"username": "btaylor-admin", "port": 22, "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa", "host": "git.chcit.org", "key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "service": {"group": "database-service", "description": "database-service for development environment", "user": "database-service", "name": "database-service"}, "version": {"number": 2, "created": "2025-04-16 20:58:00", "updated": "2025-04-16 20:58:00"}, "dependencies": [{"command": "apt-get install -y g++-14", "name": "GCC 14 Compiler", "description": "GCC 14 compiler with C++23 support (required for modules and coroutines)", "check": "g++-14 --version"}, {"command": "apt-get install -y cmake", "name": "CMake", "description": "CMake build system (minimum version 3.20.0)", "check": "cmake --version"}, {"command": "apt-get install -y build-essential", "name": "Build Essential", "description": "Build tools (make, etc.)", "check": "dpkg -s build-essential"}, {"command": "apt-get install -y libboost-all-dev", "name": "Boost Libraries", "description": "Boost C++ libraries (system, program_options components required)", "check": "dpkg -l | grep libboost"}, {"command": "apt-get install -y libpq-dev", "name": "PostgreSQL Client Libraries", "description": "PostgreSQL client development libraries", "check": "dpkg -s libpq-dev"}, {"command": "apt-get install -y pkg-config", "name": "pkg-config", "description": "Helper tool for discovering installed libraries", "check": "dpkg -s pkg-config"}, {"command": "apt-get install -y libpqxx-dev", "name": "PostgreSQL C++ API", "description": "C++ client API for PostgreSQL", "check": "dpkg -s libpqxx-dev"}, {"command": "apt-get install -y libssl-dev", "name": "OpenSSL Development", "description": "OpenSSL development libraries", "check": "dpkg -s libssl-dev"}, {"command": "apt-get install -y nlohmann-json3-dev", "name": "JSON Library", "description": "JSON for Modern C++", "check": "dpkg -s n<PERSON><PERSON>-json3-dev"}, {"command": "apt-get install -y postgresql postgresql-contrib", "name": "PostgreSQL Server", "description": "PostgreSQL database server", "check": "psql --version"}, {"command": "apt-get install -y ninja-build", "name": "Ninja Build System", "description": "High-speed build system required for C++ modules support in CMake", "check": "dpkg -s ninja-build"}]}