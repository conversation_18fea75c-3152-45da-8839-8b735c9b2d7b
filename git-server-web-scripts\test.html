<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Repository Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/repositories.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.2.1/dist/chart.umd.min.js"></script>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h1>Git Repository Test Page</h1>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Repository Summary</h5>
                <div id="git-summary-container"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Repository List</h5>
                <div id="repository-content">
                    <div id="repository-list-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal for Repository Details -->
    <div class="modal fade" id="repositoryDetailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Repository Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="repository-details-container">
                    <!-- Content will be added dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Consolidated Git Repository Scripts -->
    <script src="/js/git-repositories-debug.js"></script>
    
    <!-- Debug Script -->
    <script src="/js/git-debug.js"></script>
</body>
</html>
