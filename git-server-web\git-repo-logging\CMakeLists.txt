cmake_minimum_required(VERSION 3.16)
project(git-repo-logging VERSION 1.0.0 LANGUAGES CXX)

# Set C++23 standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Set compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pedantic")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

# Find required packages
find_package(Boost REQUIRED COMPONENTS system filesystem)
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)

# Find JsonCpp
find_package(PkgConfig REQUIRED)
pkg_check_modules(<PERSON><PERSON><PERSON><PERSON> REQUIRED jsoncpp)

# Find PostgreSQL and libpqxx
find_package(PostgreSQL REQUIRED)
if(PostgreSQL_FOUND)
    message(STATUS "PostgreSQL found: ${PostgreSQL_INCLUDE_DIRS}")
else()
    message(FATAL_ERROR "PostgreSQL not found, required for database tier")
endif()

# Find libpqxx
pkg_check_modules(PQXX REQUIRED libpqxx)
if(PQXX_FOUND)
    message(STATUS "libpqxx found: ${PQXX_INCLUDE_DIRS}")
else()
    message(FATAL_ERROR "libpqxx not found, required for database tier")
endif()

# Find zstd
pkg_check_modules(ZSTD libzstd)
if(ZSTD_FOUND)
    message(STATUS "zstd found: ${ZSTD_INCLUDE_DIRS}")
else()
    message(WARNING "zstd not found, compression will use gzip fallback")
endif()

# Set include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${Boost_INCLUDE_DIRS}
    ${OPENSSL_INCLUDE_DIR}
    ${JSONCPP_INCLUDE_DIRS}
)

# Add PostgreSQL and libpqxx include directories if found
if(PostgreSQL_FOUND)
    include_directories(${PostgreSQL_INCLUDE_DIRS})
endif()

if(PQXX_FOUND)
    include_directories(${PQXX_INCLUDE_DIRS})
endif()

if(ZSTD_FOUND)
    include_directories(${ZSTD_INCLUDE_DIRS})
endif()

# Set link directories
link_directories(
    ${JSONCPP_LIBRARY_DIRS}
)

# Add PostgreSQL and libpqxx link directories if found
if(PQXX_FOUND)
    link_directories(${PQXX_LIBRARY_DIRS})
endif()

if(ZSTD_FOUND)
    link_directories(${ZSTD_LIBRARY_DIRS})
endif()

# Add source files
file(GLOB_RECURSE SOURCES "src/*.cpp")

# Add specific source files
list(APPEND SOURCES
    "src/api/api_server_handlers.cpp"
    "src/services/logging_service_discovery.cpp"
)

# Create executable
add_executable(git-repo-logging ${SOURCES})

# Link libraries
target_link_libraries(git-repo-logging
    ${Boost_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    jsoncpp
    pthread
)

# Add PostgreSQL and libpqxx libraries if found
if(PostgreSQL_FOUND AND PQXX_FOUND)
    target_link_libraries(git-repo-logging
        ${PostgreSQL_LIBRARIES}
        ${PQXX_LIBRARIES}
        pqxx
    )

    # Add compile definition to enable database tier
    target_compile_definitions(git-repo-logging PRIVATE ENABLE_DATABASE_TIER)
endif()

# Add zstd library if found
if(ZSTD_FOUND)
    target_link_libraries(git-repo-logging
        ${ZSTD_LIBRARIES}
    )

    # Add compile definition to enable zstd compression
    target_compile_definitions(git-repo-logging PRIVATE ENABLE_ZSTD_COMPRESSION)
endif()

# Install target
install(TARGETS git-repo-logging
    RUNTIME DESTINATION bin
)

# Create systemd service file
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/systemd/git-repo-logging.service.in
    ${CMAKE_CURRENT_BINARY_DIR}/git-repo-logging.service
    @ONLY
)

# Install systemd service file
install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/git-repo-logging.service
    DESTINATION /etc/systemd/system
)

# Install SQL files
install(DIRECTORY
    ${CMAKE_CURRENT_SOURCE_DIR}/sql/
    DESTINATION ${CMAKE_INSTALL_PREFIX}/share/git-repo-logging/sql
    FILES_MATCHING PATTERN "*.sql"
    PATTERN "*.sh"
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)

# Install config files
install(DIRECTORY
    ${CMAKE_CURRENT_SOURCE_DIR}/config/
    DESTINATION ${CMAKE_INSTALL_PREFIX}/share/git-repo-logging/config
    FILES_MATCHING PATTERN "*.json"
)

# Print configuration summary
message(STATUS "Configuration Summary:")
message(STATUS "  C++ Compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "  C++ Standard: C++23")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Boost Include: ${Boost_INCLUDE_DIRS}")
message(STATUS "  JsonCpp Include: ${JSONCPP_INCLUDE_DIRS}")
message(STATUS "  PostgreSQL Include: ${PostgreSQL_INCLUDE_DIRS}")
message(STATUS "  libpqxx Include: ${PQXX_INCLUDE_DIRS}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
