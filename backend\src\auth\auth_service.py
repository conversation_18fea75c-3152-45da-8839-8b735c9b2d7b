"""
Authentication and Authorization Service for Project Tracker
Handles user authentication, JWT token management, and role-based access control
"""

from datetime import datetime, timedelta
import jwt
from functools import wraps
from flask import request, jsonify, current_app
import bcrypt
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

class AuthService:
    def __init__(self, db_pool, config: Dict[str, Any]):
        self.db_pool = db_pool
        self.config = config
        self.secret_key = config.get('JWT_SECRET_KEY', 'your-secret-key')  # Should be set in environment
        self.token_expiry = timedelta(hours=config.get('TOKEN_EXPIRY_HOURS', 24))

    def _get_db(self):
        return self.db_pool.getconn()

    def _return_db(self, conn):
        self.db_pool.putconn(conn)

    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify a password against its hash."""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

    def generate_token(self, user_id: int, roles: List[str]) -> str:
        """Generate a JWT token for a user."""
        payload = {
            'user_id': user_id,
            'roles': roles,
            'exp': datetime.utcnow() + self.token_expiry
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify a JWT token and return its payload."""
        try:
            return jwt.decode(token, self.secret_key, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            logger.warning("Token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {str(e)}")
            return None

    def create_user(self, username: str, password: str, roles: List[str]) -> Optional[int]:
        """Create a new user with the given roles."""
        conn = self._get_db()
        try:
            with conn.cursor() as cursor:
                # Check if user exists
                cursor.execute(
                    "SELECT id FROM users WHERE username = %s",
                    (username,)
                )
                if cursor.fetchone():
                    return None

                # Create user
                cursor.execute("""
                    INSERT INTO users (username, password_hash)
                    VALUES (%s, %s)
                    RETURNING id
                """, (username, self.hash_password(password)))
                user_id = cursor.fetchone()['id']

                # Assign roles
                for role in roles:
                    cursor.execute("""
                        INSERT INTO user_roles (user_id, role)
                        VALUES (%s, %s)
                    """, (user_id, role))

                conn.commit()
                return user_id
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating user: {str(e)}")
            return None
        finally:
            self._return_db(conn)

    def authenticate(self, username: str, password: str) -> Optional[str]:
        """Authenticate a user and return a JWT token."""
        conn = self._get_db()
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT u.id, u.password_hash, array_agg(ur.role) as roles
                    FROM users u
                    LEFT JOIN user_roles ur ON u.id = ur.user_id
                    WHERE u.username = %s
                    GROUP BY u.id, u.password_hash
                """, (username,))
                user = cursor.fetchone()

                if not user or not self.verify_password(password, user['password_hash']):
                    return None

                return self.generate_token(user['id'], user['roles'])
        finally:
            self._return_db(conn)

def require_auth(roles: Optional[List[str]] = None):
    """Decorator to require authentication and optional role-based authorization."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return jsonify({'error': 'No valid authorization token'}), 401

            token = auth_header.split(' ')[1]
            auth_service = current_app.auth_service
            payload = auth_service.verify_token(token)

            if not payload:
                return jsonify({'error': 'Invalid or expired token'}), 401

            if roles and not any(role in payload['roles'] for role in roles):
                return jsonify({'error': 'Insufficient permissions'}), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator
