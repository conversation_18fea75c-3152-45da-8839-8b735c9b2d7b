#include "api_server.hpp"
#include <iostream>
#include <regex>
#include <thread>
#include <sstream>
#include <boost/algorithm/string.hpp>
#include <jsoncpp/json/json.h>

ApiServer::ApiServer(unsigned short port, std::shared_ptr<LoggingService> service)
    : port_(port), loggingService_(service), running_(false),
      ioc_(), acceptor_(ioc_, tcp::endpoint(tcp::v4(), port)) {
    // Setup handlers
    setupEndpoints();
}

ApiServer::~ApiServer() {
    stop();
}

void ApiServer::start() {
    if (running_) return;

    running_ = true;

    // Start accepting connections
    doAccept();

    // Run the I/O service on a separate thread
    acceptorThread_ = std::thread([this]() {
        try {
            ioc_.run();
        } catch (const std::exception& e) {
            std::cerr << "Error in I/O context: " << e.what() << std::endl;
        }
    });
}

void ApiServer::stop() {
    if (!running_) return;

    running_ = false;

    // Close all WebSocket sessions
    {
        std::lock_guard<std::mutex> lock(webSocketSessionsMutex_);
        for (auto& session : webSocketSessions_) {
            session->close();
        }
        webSocketSessions_.clear();
    }

    // Stop the I/O context
    ioc_.stop();

    // Wait for the acceptor thread to finish
    if (acceptorThread_.joinable()) {
        acceptorThread_.join();
    }
}

void ApiServer::setupEndpoints() {
    // HTTP endpoints
    httpHandlers_["/api/logs"] = [this](auto& req, auto& res) { handleGetLogs(req, res); };
    httpHandlers_["/api/logs/sources"] = [this](auto& req, auto& res) { handleGetLogSources(req, res); };
    httpHandlers_["/api/logs/stats"] = [this](auto& req, auto& res) { handleGetLogStats(req, res); };
    httpHandlers_["/api/logs/available"] = [this](auto& req, auto& res) { handleGetAvailableLogs(req, res); };
    httpHandlers_["/api/logs/scan"] = [this](auto& req, auto& res) { handleScanLogs(req, res); };
    httpHandlers_["/api/logs/configure"] = [this](auto& req, auto& res) { handleConfigureLogs(req, res); };
    httpHandlers_["/health"] = [this](auto& req, auto& res) { handleHealthCheck(req, res); };

    // WebSocket endpoints
    webSocketHandlers_["/api/logs/stream"] = [this](auto session, auto& message) { handleWebSocketMessage(session, message); };
}

void ApiServer::doAccept() {
    acceptor_.async_accept(
        [this](beast::error_code ec, tcp::socket socket) {
            if (!ec) {
                // Create a new session and start it
                std::thread(&ApiServer::handleRequest, this, std::move(socket)).detach();
            } else {
                std::cerr << "Accept error: " << ec.message() << std::endl;
            }

            // Accept another connection
            if (running_) {
                doAccept();
            }
        });
}

void ApiServer::handleRequest(tcp::socket socket) {
    try {
        // Buffer for reading
        beast::flat_buffer buffer;

        // Request object
        http::request<http::string_body> req;

        // Read the request
        beast::http::read(socket, buffer, req);

        // Response object
        http::response<http::string_body> res{http::status::ok, req.version()};
        res.set(http::field::server, "Git Repository Logging Agent");
        res.set(http::field::content_type, "application/json");
        res.set(http::field::access_control_allow_origin, "*");

        // Handle WebSocket upgrade request
        if (boost::beast::websocket::is_upgrade(req)) {
            std::string target = req.target().to_string();
            if (webSocketHandlers_.find(target) != webSocketHandlers_.end()) {
                // Create a WebSocket session
                auto session = std::make_shared<WebSocketSession>(std::move(socket), *this);

                // Add to sessions list
                {
                    std::lock_guard<std::mutex> lock(webSocketSessionsMutex_);
                    webSocketSessions_.push_back(session);
                }

                // Start the session
                session->start();
                return;
            }
        }

        // Handle HTTP request
        std::string target = req.target().to_string();

        // Extract path without query parameters
        std::string path = target;
        size_t queryPos = target.find('?');
        if (queryPos != std::string::npos) {
            path = target.substr(0, queryPos);
        }

        // Find handler for the path
        auto it = httpHandlers_.find(path);
        if (it != httpHandlers_.end()) {
            // Call the handler
            it->second(req, res);
        } else {
            // Not found
            res.result(http::status::not_found);
            res.body() = "{\"error\":\"Not found\"}";
        }

        // Write the response
        res.prepare_payload();
        beast::http::write(socket, res);

        // Close the socket
        beast::error_code ec;
        socket.shutdown(tcp::socket::shutdown_both, ec);

    } catch (const std::exception& e) {
        std::cerr << "Error handling request: " << e.what() << std::endl;
    }
}

void ApiServer::handleGetLogs(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Parse query parameters
        std::string target = req.target().to_string();
        size_t queryPos = target.find('?');
        std::unordered_map<std::string, std::string> params;

        if (queryPos != std::string::npos) {
            std::string query = target.substr(queryPos + 1);
            params = parseQueryParams(query);
        }

        // Extract parameters
        int maxCount = 10;
        std::string source = "dashboard";
        std::string level = "all";
        bool getAll = false;

        if (params.find("max") != params.end()) {
            maxCount = std::stoi(params["max"]);
        }

        if (params.find("source") != params.end()) {
            source = params["source"];
        }

        if (params.find("level") != params.end()) {
            level = params["level"];
        }

        if (params.find("get_all") != params.end()) {
            getAll = (params["get_all"] == "true");
        }

        // Get logs from service
        std::string logs = loggingService_->getLogs(source, level, maxCount, getAll);

        // Set response
        res.body() = logs;

    } catch (const std::exception& e) {
        std::cerr << "Error handling getLogs: " << e.what() << std::endl;
        res.result(http::status::internal_server_error);
        res.body() = "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

void ApiServer::handleGetLogSources(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Get log sources from service
        std::string sources = loggingService_->getLogSources();

        // Set response
        res.body() = sources;

    } catch (const std::exception& e) {
        std::cerr << "Error handling getLogSources: " << e.what() << std::endl;
        res.result(http::status::internal_server_error);
        res.body() = "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

void ApiServer::handleGetLogStats(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Get log stats from service
        std::string stats = loggingService_->getLogStats();

        // Set response
        res.body() = stats;

    } catch (const std::exception& e) {
        std::cerr << "Error handling getLogStats: " << e.what() << std::endl;
        res.result(http::status::internal_server_error);
        res.body() = "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
}

void ApiServer::handleWebSocketMessage(std::shared_ptr<WebSocketSession> session, const std::string& message) {
    try {
        // Parse the message as JSON
        Json::Value root;
        Json::CharReaderBuilder builder;
        std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
        std::string errors;

        if (!reader->parse(message.c_str(), message.c_str() + message.size(), &root, &errors)) {
            session->send("{\"error\":\"Invalid JSON: " + errors + "\"}");
            return;
        }

        // Extract parameters
        std::string action = root.get("action", "subscribe").asString();

        if (action == "subscribe") {
            // Subscribe to log updates
            std::string source = root.get("source", "dashboard").asString();
            std::string level = root.get("level", "all").asString();

            // Register subscription with logging service
            loggingService_->subscribeToLogs(session, source, level);

            // Send confirmation
            session->send("{\"status\":\"subscribed\",\"source\":\"" + source + "\",\"level\":\"" + level + "\"}");

        } else if (action == "unsubscribe") {
            // Unsubscribe from log updates
            loggingService_->unsubscribeFromLogs(session);

            // Send confirmation
            session->send("{\"status\":\"unsubscribed\"}");

        } else {
            // Unknown action
            session->send("{\"error\":\"Unknown action: " + action + "\"}");
        }

    } catch (const std::exception& e) {
        std::cerr << "Error handling WebSocket message: " << e.what() << std::endl;
        session->send("{\"error\":\"" + std::string(e.what()) + "\"}");
    }
}

std::unordered_map<std::string, std::string> ApiServer::parseQueryParams(const std::string& query) {
    std::unordered_map<std::string, std::string> params;

    std::vector<std::string> pairs;
    boost::split(pairs, query, boost::is_any_of("&"));

    for (const auto& pair : pairs) {
        std::vector<std::string> keyValue;
        boost::split(keyValue, pair, boost::is_any_of("="));

        if (keyValue.size() == 2) {
            params[keyValue[0]] = keyValue[1];
        }
    }

    return params;
}

// WebSocketSession implementation
WebSocketSession::WebSocketSession(tcp::socket socket, ApiServer& server)
    : ws_(std::move(socket)), server_(server), closed_(false) {
}

WebSocketSession::~WebSocketSession() {
    close();
}

void WebSocketSession::start() {
    try {
        // Accept the WebSocket handshake
        ws_.accept();

        // Start reading
        doRead();

    } catch (const std::exception& e) {
        std::cerr << "Error starting WebSocket session: " << e.what() << std::endl;
        close();
    }
}

void WebSocketSession::send(const std::string& message) {
    try {
        if (closed_) return;

        // Send the message
        ws_.write(net::buffer(message));

    } catch (const std::exception& e) {
        std::cerr << "Error sending WebSocket message: " << e.what() << std::endl;
        close();
    }
}

void WebSocketSession::close() {
    try {
        if (closed_) return;
        closed_ = true;

        // Close the WebSocket connection
        beast::error_code ec;
        ws_.close(websocket::close_code::normal, ec);

    } catch (const std::exception& e) {
        std::cerr << "Error closing WebSocket session: " << e.what() << std::endl;
    }
}

void WebSocketSession::doRead() {
    try {
        if (closed_) return;

        // Read a message
        ws_.async_read(
            buffer_,
            [self = shared_from_this()](beast::error_code ec, std::size_t bytes_transferred) {
                if (ec) {
                    if (ec != websocket::error::closed) {
                        std::cerr << "WebSocket read error: " << ec.message() << std::endl;
                    }
                    self->close();
                    return;
                }

                // Get the message
                std::string message = beast::buffers_to_string(self->buffer_.data());
                self->buffer_.consume(self->buffer_.size());

                // Handle the message
                try {
                    // TODO: Call the appropriate handler based on the WebSocket path
                    // For now, just echo the message
                    self->send(message);

                } catch (const std::exception& e) {
                    std::cerr << "Error handling WebSocket message: " << e.what() << std::endl;
                }

                // Read another message
                self->doRead();
            });

    } catch (const std::exception& e) {
        std::cerr << "Error in WebSocket read: " << e.what() << std::endl;
        close();
    }
}

void ApiServer::handleHealthCheck(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Create health status response
        Json::Value health;
        health["status"] = "ok";
        health["version"] = "1.0.0";
        health["timestamp"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());

        // Check storage tiers status
        Json::Value storage;
        bool dbConnected = false;

        // Check database connection
        auto tiers = loggingService_->getStorageTiers();
        for (const auto& tier : tiers) {
            auto dbTier = std::dynamic_pointer_cast<logging::DatabaseStorage>(tier);
            if (dbTier) {
                dbConnected = dbTier->isConnected();
                storage["database"] = dbConnected ? "connected" : "disconnected";
                storage["database_circuit"] = dbTier->getCircuitStateString();
            }
        }

        // Add storage status to response
        health["storage"] = storage;

        // Add service uptime
        auto uptime = std::chrono::steady_clock::now() - loggingService_->getStartTime();
        health["uptime_seconds"] = static_cast<int>(std::chrono::duration_cast<std::chrono::seconds>(uptime).count());

        // Convert to string
        Json::StreamWriterBuilder writer;
        res.body() = Json::writeString(writer, health);
        res.result(http::status::ok);
    } catch (const std::exception& e) {
        res.result(http::status::internal_server_error);
        res.body() = "{\"error\":\"" + std::string(e.what()) + "\"}";
    }

    res.set(http::field::content_type, "application/json");
}
