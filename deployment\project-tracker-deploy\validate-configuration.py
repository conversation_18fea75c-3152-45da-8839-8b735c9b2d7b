#!/usr/bin/env python3
"""
Project Tracker Ubuntu Server Configuration Validator
Version: 1.0.0

Validates the configuration of the Ubuntu server environment for Project Tracker deployment.
This script is intended to run on the target Ubuntu server, not on the Windows build server.

Requirements:
- Ubuntu 24.04 LTS
- Python 3.12+
- Root/sudo access for system checks
"""

import os
import sys
import logging
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConfigurationValidator:
    """Validates Project Tracker deployment configuration."""
    
    def __init__(self):
        self.deployment_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.critical_vars = [
            'FLASK_APP',
            'SECRET_KEY',
            'DATABASE_URL',
            'JWT_SECRET_KEY',
            'PROJECT_BASE_DIR',
            'LOG_LEVEL',
            'LOG_FILE'
        ]
        self.required_dirs = [
            '/opt/project-tracker',
            '/var/log/project-tracker',
            '/var/lib/project-tracker/data',
            '/etc/project-tracker'
        ]

    def validate_environment_file(self) -> bool:
        """Validate .env file existence and configuration."""
        env_path = os.path.join(self.deployment_dir, '.env')
        if not os.path.exists(env_path):
            logger.error(f"❌ .env file not found at {env_path}")
            logger.info("   Copy .env.example to .env and configure your settings")
            return False

        load_dotenv(env_path)
        return self._validate_env_vars()

    def _validate_env_vars(self) -> bool:
        """Validate required environment variables."""
        missing_vars = []
        for var in self.critical_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            logger.error("❌ Missing required environment variables:")
            for var in missing_vars:
                logger.error(f"   - {var}")
            return False
        return True

    def validate_directories(self) -> bool:
        """Validate required directory structure."""
        for dir_path in self.required_dirs:
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, mode=0o750, exist_ok=True)
                    logger.info(f"✅ Created directory: {dir_path}")
                except Exception as e:
                    logger.error(f"❌ Error creating directory {dir_path}: {e}")
                    return False
            
            # Validate permissions
            try:
                stat = os.stat(dir_path)
                if stat.st_mode & 0o777 != 0o750:
                    logger.warning(f"⚠️  Incorrect permissions on {dir_path}")
                    try:
                        os.chmod(dir_path, 0o750)
                        logger.info(f"✅ Fixed permissions on {dir_path}")
                    except Exception as e:
                        logger.error(f"❌ Error fixing permissions on {dir_path}: {e}")
                        return False
            except Exception as e:
                logger.error(f"❌ Error checking permissions on {dir_path}: {e}")
                return False

        return True

    def validate_database_config(self) -> bool:
        """Validate database configuration."""
        database_url = os.getenv('DATABASE_URL', '')
        
        if database_url.startswith('postgresql://'):
            logger.info("✅ Using PostgreSQL database")
            return True
        else:
            logger.error("❌ Invalid DATABASE_URL format. Only PostgreSQL is supported.")
            return False

    def run_validation(self) -> bool:
        """Run all validation checks."""
        logger.info("🔍 Starting Project Tracker Configuration Validation...")
        
        checks = [
            (self.validate_environment_file, "Environment Configuration"),
            (self.validate_directories, "Directory Structure"),
            (self.validate_database_config, "Database Configuration")
        ]

        all_passed = True
        for check_func, check_name in checks:
            logger.info(f"\n🔍 Validating {check_name}...")
            if not check_func():
                all_passed = False
                logger.error(f"❌ {check_name} validation failed")
            else:
                logger.info(f"✅ {check_name} validation passed")

        if all_passed:
            logger.info("\n✅ All configuration checks passed successfully!")
        else:
            logger.error("\n❌ Some configuration checks failed. Please review the errors above.")
        
        return all_passed

def main():
    """Main entry point for configuration validation."""
    validator = ConfigurationValidator()
    sys.exit(0 if validator.run_validation() else 1)

if __name__ == '__main__':
    main()
