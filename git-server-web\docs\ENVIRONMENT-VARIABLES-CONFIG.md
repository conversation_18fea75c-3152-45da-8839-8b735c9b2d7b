# Environment Variables Configuration for Git Dashboard

## Overview

This document explains how to properly configure environment variables for the Git Dashboard system. Environment variables provide a flexible way to adjust configuration without changing code, ensuring consistency across reboots and simplifying deployment across different environments.

## System-Wide Environment Variables

### Setting Variables in /etc/environment

For system-wide environment variables that persist across reboots and are available to all services:

```bash
# Edit the environment file
sudo nano /etc/environment
```

Add the following variables (customize as needed):

```
GIT_DASHBOARD_BIND_IP="0.0.0.0"
GIT_DASHBOARD_BIND_PORT="8000"
GIT_DASHBOARD_LOG_LEVEL="INFO"
GIT_DASHBOARD_DATA_DIR="/opt/git-dashboard/data"
GIT_DASHBOARD_MAX_HISTORY_DAYS="30"
```

Apply changes immediately without reboot:

```bash
source /etc/environment
```

### Setting Variables in systemd Service

For service-specific environment variables that only affect the Git Dashboard service:

```bash
sudo nano /etc/systemd/system/git-dashboard.service
```

Add or modify the `[Service]` section:

```ini
[Service]
User=root
Group=root
WorkingDirectory=/opt/git-dashboard
ExecStart=/opt/git-dashboard/venv/bin/gunicorn --bind=0.0.0.0:8000 --config /opt/git-dashboard/gunicorn_config.py app:app

# Environment variables
Environment="BIND_IP=0.0.0.0"
Environment="BIND_PORT=8000"
Environment="LOG_LEVEL=INFO"
Environment="DATA_DIR=/opt/git-dashboard/data"
Environment="MAX_HISTORY_DAYS=30"
```

Reload systemd and restart the service to apply changes:

```bash
sudo systemctl daemon-reload
sudo systemctl restart git-dashboard
```

## Application-Specific Environment Variables

### Creating a .env File

For application-specific variables that can be loaded by the Flask application:

```bash
sudo nano /opt/git-dashboard/.env
```

Add configuration variables:

```
# Network configuration
BIND_IP=0.0.0.0
BIND_PORT=8000

# Application settings
FLASK_APP=app.py
FLASK_ENV=production
SECRET_KEY=your-secure-secret-key-here

# Data settings
DATA_DIR=/opt/git-dashboard/data
MAX_HISTORY_DAYS=30

# Git repository settings
GIT_REPO_PATH=/home/<USER>/project-tracker.git
```

Set proper permissions:

```bash
sudo chmod 600 /opt/git-dashboard/.env
sudo chown root:root /opt/git-dashboard/.env
```

### Loading Environment Variables in Flask

Modify `app.py` to load variables from the `.env` file:

```bash
sudo nano /opt/git-dashboard/app.py
```

Add the following at the top of the file:

```python
import os
from dotenv import load_dotenv

# Load environment variables from .env file
dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

# Get configuration from environment
BIND_IP = os.environ.get('BIND_IP', '0.0.0.0')
BIND_PORT = int(os.environ.get('BIND_PORT', 8000))
DATA_DIR = os.environ.get('DATA_DIR', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data'))
MAX_HISTORY_DAYS = int(os.environ.get('MAX_HISTORY_DAYS', 30))
GIT_REPO_PATH = os.environ.get('GIT_REPO_PATH', '/home/<USER>/project-tracker.git')
```

Install the required package:

```bash
sudo /opt/git-dashboard/venv/bin/pip install python-dotenv
```

## Environment Variable Hierarchy

Variables are loaded in the following order (later sources override earlier ones):

1. Default values in the application code
2. System-wide variables from `/etc/environment`
3. Variables in the systemd service file
4. Variables in the `.env` file

## Security Considerations

### Sensitive Information

For sensitive information like API keys or tokens:

1. **NEVER** store sensitive information in version control
2. Use restrictive file permissions (`chmod 600`) for files containing sensitive data
3. Consider using a dedicated secrets management solution for production environments

### Production vs. Development

Use different environment variable configurations for different environments:

```bash
# Create environment-specific files
sudo nano /opt/git-dashboard/.env.production
sudo nano /opt/git-dashboard/.env.development
```

Then symlink the appropriate one:

```bash
# For production
sudo ln -sf /opt/git-dashboard/.env.production /opt/git-dashboard/.env

# For development
# sudo ln -sf /opt/git-dashboard/.env.development /opt/git-dashboard/.env
```

## Troubleshooting Environment Variables

### Verifying Variable Values

To check if environment variables are set correctly:

```bash
# For system-wide variables
printenv | grep GIT_DASHBOARD

# For service variables
sudo systemctl show git-dashboard | grep Environment

# For application variables (inside container)
sudo python3 -c "import os; print(os.environ.get('BIND_IP', 'Not set'))"
```

### Common Issues

1. **Variables Not Persisting After Reboot**
   - Ensure they are properly set in `/etc/environment`
   - Verify file permissions (`644` for `/etc/environment`)

2. **Variables Not Available to Service**
   - Check systemd service file for correct syntax
   - Reload systemd with `sudo systemctl daemon-reload`

3. **Application Not Reading Variables**
   - Ensure `.env` file exists and has correct permissions
   - Verify `python-dotenv` is installed
   - Check for syntax errors in the `.env` file

## Example: Complete Configuration

### .env file

```
# Network configuration
BIND_IP=0.0.0.0
BIND_PORT=8000

# Application settings
FLASK_APP=app.py
FLASK_ENV=production
SECRET_KEY=generate-a-secure-random-key-here
DEBUG=False

# Logging configuration
LOG_LEVEL=INFO
LOG_FILE=/opt/git-dashboard/logs/git-dashboard.log

# Data settings
DATA_DIR=/opt/git-dashboard/data
METRICS_FILE=/opt/git-dashboard/data/metrics_history.json
HISTORY_DIR=/opt/git-dashboard/data/history
MAX_HISTORY_DAYS=30

# Git repository settings
GIT_REPO_PATH=/home/<USER>/project-tracker.git
GIT_BRANCH=main
```

### Checking Configuration in Python

```python
# At the top of app.py
import os
from dotenv import load_dotenv

# Load environment variables from .env file
dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)

# Print configuration for debugging
if os.environ.get('DEBUG', 'False').lower() in ('true', '1', 't'):
    print(f"Configuration:")
    print(f"BIND_IP: {os.environ.get('BIND_IP', '0.0.0.0')}")
    print(f"BIND_PORT: {os.environ.get('BIND_PORT', '8000')}")
    print(f"DATA_DIR: {os.environ.get('DATA_DIR', 'default_path')}")
```
