from flask import Blueprint, render_template, jsonify
import psutil
import os
from datetime import datetime, timedelta
import json
import logging
import psycopg2

monitoring = Blueprint('monitoring', __name__)

class SystemMonitor:
    def __init__(self, db_path):
        self.db_path = db_path
        self.setup_logging()
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/project-tracker/monitor.log'),
                logging.StreamHandler()
            ]
        )
    
    def get_system_metrics(self):
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_metrics(self):
        metrics = self.get_system_metrics()
        logging.info(f"System Metrics: {json.dumps(metrics)}")
        return metrics

class DatabaseMonitor:
    def __init__(self, db_host, db_name, db_user, db_password, backup_dir='/var/backups/project-tracker'):
        self.db_host = db_host
        self.db_name = db_name
        self.db_user = db_user
        self.db_password = db_password
        self.backup_dir = backup_dir
        os.makedirs(backup_dir, exist_ok=True)
        
    def create_backup(self):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = os.path.join(self.backup_dir, f'tracker_{timestamp}.sql')
        
        try:
            # Create backup command
            command = f"pg_dump -h {self.db_host} -U {self.db_user} {self.db_name} > {backup_path}"
            
            # Perform backup
            os.system(command)
            
            # Keep only last 7 backups
            self._cleanup_old_backups()
            
            logging.info(f"Database backup created: {backup_path}")
            return True
        except Exception as e:
            logging.error(f"Backup failed: {str(e)}")
            return False
    
    def _cleanup_old_backups(self):
        backups = []
        for f in os.listdir(self.backup_dir):
            if f.startswith('tracker_') and f.endswith('.sql'):
                path = os.path.join(self.backup_dir, f)
                backups.append((os.path.getmtime(path), path))
        
        # Sort by modification time (newest first)
        backups.sort(reverse=True)
        
        # Remove all but the last 7 backups
        for _, path in backups[7:]:
            try:
                os.remove(path)
                logging.info(f"Removed old backup: {path}")
            except Exception as e:
                logging.error(f"Failed to remove old backup {path}: {str(e)}")
    
    def get_db_stats(self):
        try:
            # Connect to database
            conn = psycopg2.connect(
                host=self.db_host,
                database=self.db_name,
                user=self.db_user,
                password=self.db_password
            )
            cursor = conn.cursor()
            
            # Get table statistics
            stats = {}
            for table in ['improvements', 'categories', 'subtasks']:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                stats[table] = {'count': count}
            
            # Get database file size
            cursor.execute("SELECT pg_database_size('tracker')")
            stats['db_size_mb'] = cursor.fetchone()[0] / (1024 * 1024)
            
            # Get last backup info
            backups = [f for f in os.listdir(self.backup_dir) if f.startswith('tracker_') and f.endswith('.sql')]
            if backups:
                latest_backup = max(backups)
                backup_path = os.path.join(self.backup_dir, latest_backup)
                stats['last_backup'] = {
                    'timestamp': datetime.fromtimestamp(os.path.getmtime(backup_path)).isoformat(),
                    'size_mb': os.path.getsize(backup_path) / (1024 * 1024)
                }
            
            conn.close()
            return stats
        except Exception as e:
            logging.error(f"Failed to get DB stats: {str(e)}")
            return None

def get_system_stats():
    return {
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_percent': psutil.disk_usage('/').percent,
        'connections': len(psutil.net_connections()),
        'timestamp': datetime.now().isoformat()
    }

def get_app_stats(db_host, db_name, db_user, db_password):
    # Connect to database
    conn = psycopg2.connect(
        host=db_host,
        database=db_name,
        user=db_user,
        password=db_password
    )
    cursor = conn.cursor()
    
    stats = {
        'total_tasks': cursor.execute('SELECT COUNT(*) FROM tasks').fetchone()[0],
        'completed_tasks': cursor.execute(
            'SELECT COUNT(*) FROM tasks WHERE status = "Completed"'
        ).fetchone()[0],
        'categories': cursor.execute('SELECT COUNT(*) FROM categories').fetchone()[0],
        'active_tasks': cursor.execute(
            'SELECT COUNT(*) FROM tasks WHERE status != "Completed"'
        ).fetchone()[0]
    }
    
    conn.close()
    return stats

@monitoring.route('/monitoring/stats')
def stats():
    system_stats = get_system_stats()
    app_stats = get_app_stats(
        current_app.config['DB_HOST'],
        current_app.config['DB_NAME'],
        current_app.config['DB_USER'],
        current_app.config['DB_PASSWORD']
    )
    
    return jsonify({
        'system': system_stats,
        'application': app_stats
    })

@monitoring.route('/monitoring/dashboard')
def dashboard():
    return render_template('monitoring.html')

# Add monitoring data collection
def collect_metrics():
    metrics_file = 'metrics.json'
    retention_days = 7
    
    # Load existing metrics
    if os.path.exists(metrics_file):
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
    else:
        metrics = []
    
    # Add new metrics
    metrics.append({
        **get_system_stats(),
        **get_app_stats(
            current_app.config['DB_HOST'],
            current_app.config['DB_NAME'],
            current_app.config['DB_USER'],
            current_app.config['DB_PASSWORD']
        )
    })
    
    # Remove old metrics
    cutoff = datetime.now() - timedelta(days=retention_days)
    metrics = [m for m in metrics 
              if datetime.fromisoformat(m['timestamp']) > cutoff]
    
    # Save metrics
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f)

@monitoring.route('/monitoring/metrics')
def get_metrics():
    if os.path.exists('metrics.json'):
        with open('metrics.json', 'r') as f:
            return jsonify(json.load(f))
    return jsonify([])

# Health check endpoint
@monitoring.route('/health')
def health_check():
    try:
        # Check database connection
        conn = psycopg2.connect(
            host=current_app.config['DB_HOST'],
            database=current_app.config['DB_NAME'],
            user=current_app.config['DB_USER'],
            password=current_app.config['DB_PASSWORD']
        )
        conn.execute('SELECT 1')
        conn.close()
        
        # Check system resources
        system_stats = get_system_stats()
        health_status = {
            'status': 'healthy',
            'database': 'connected',
            'system': {
                'status': 'healthy',
                'warnings': []
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # Add warnings if resources are high
        if system_stats['cpu_percent'] > 80:
            health_status['system']['warnings'].append('High CPU usage')
        if system_stats['memory_percent'] > 80:
            health_status['system']['warnings'].append('High memory usage')
        if system_stats['disk_percent'] > 80:
            health_status['system']['warnings'].append('High disk usage')
            
        if health_status['system']['warnings']:
            health_status['system']['status'] = 'warning'
            
        return jsonify(health_status)
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
