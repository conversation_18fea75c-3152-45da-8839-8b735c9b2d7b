# Logger.psm1 - Centralized logging system for database service deployment scripts

<#
.SYNOPSIS
    Logger module for database service deployment scripts.
#>

# Only attempt to import UI module if it exists
$uiModulePath = "$PSScriptRoot\UI.psm1"
if (Test-Path $uiModulePath) {
    try {
        Import-Module -Name $uiModulePath -DisableNameChecking -ErrorAction Stop
    } catch {
        Write-Host "Warning: Failed to import UI module: $_" -ForegroundColor Yellow
    }
}

# Global variables - use $global: scope to ensure they're shared across all modules
$global:LogFile = $null
$global:LogLevel = "Info" # Default log level (Debug, Info, Warning, Error)
$global:LogToConsole = $true
$global:LogToFile = $false  # Default to false until explicitly enabled with valid path

# Log levels with corresponding colors
$global:LogLevels = @{
    "Debug"   = @{ Value = 0; Color = "Gray" }
    "Info"    = @{ Value = 1; Color = "White" }
    "Warning" = @{ Value = 2; Color = "Yellow" }
    "Error"   = @{ Value = 3; Color = "Red" }
    "Success" = @{ Value = 4; Color = "Green" }
    "UI"      = @{ Value = 5; Color = "White" } # Special level for UI display
}

function Initialize-Logger {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$false)]
        [string]$LogFilePath,
        [Parameter(Mandatory=$false)]
        [ValidateSet("Debug", "Info", "Warning", "Error", "Success")]
        [string]$Level = "Info",
        [Parameter(Mandatory=$false)]
        [bool]$ToConsole = $true,
        [Parameter(Mandatory=$false)]
        [bool]$ToFile = $true
    )

    try {
        # Set log level
        if ($global:LogLevels.ContainsKey($Level)) {
            $global:LogLevel = $Level
        } else {
            $global:LogLevel = "Info"
            Write-Host "Invalid log level '$Level' specified. Using 'Info' instead." -ForegroundColor Yellow
        }

        # Set console and file logging flags
        $global:LogToConsole = $ToConsole
        $global:LogToFile = $ToFile

        # Set log file path
        if ([string]::IsNullOrEmpty($LogFilePath)) {
            # Always resolve logs directory relative to deployment_scripts root
            $deploymentRoot = Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent
            $logDir = Join-Path -Path $deploymentRoot -ChildPath "logs"
            if (-not (Test-Path -Path $logDir)) {
                try {
                    New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Host "Created log directory: $logDir" -ForegroundColor Green
                } catch {
                    Write-Host "Failed to create log directory: $logDir. Error: $_" -ForegroundColor Red
                    $global:LogToFile = $false
                    return $false
                }
            }
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $global:LogFile = [System.IO.Path]::GetFullPath((Join-Path -Path $logDir -ChildPath "database-service_$timestamp.log"))
        } else {
            $global:LogFile = [System.IO.Path]::GetFullPath($LogFilePath)
            $logDir = Split-Path -Path $global:LogFile -Parent
            if (-not (Test-Path -Path $logDir)) {
                try {
                    New-Item -Path $logDir -ItemType Directory -Force -ErrorAction Stop | Out-Null
                    Write-Host "Created log directory: $logDir" -ForegroundColor Green
                } catch {
                    Write-Host "Failed to create log directory: $logDir. Error: $_" -ForegroundColor Red
                    $global:LogToFile = $false
                    return $false
                }
            }
        }
        # Define a simple log function for initialization
        function Write-InitLog {
            param([string]$Message, [string]$Level = "Info")
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            $formattedMessage = "[$timestamp] [$Level] [Initialization] $Message"
            # Write to console
            if ($global:LogToConsole) {
                $color = if ($Level -eq "Error") { "Red" } elseif ($Level -eq "Warning") { "Yellow" } else { "White" }
                Write-Host $formattedMessage -ForegroundColor $color
            }
            # Write to file
            if ($global:LogToFile -and $null -ne $global:LogFile) {
                $maxAttempts = 5
                $attempt = 0
                $success = $false
                while (-not $success -and $attempt -lt $maxAttempts) {
                    try {
                        Add-Content -Path $global:LogFile -Value $formattedMessage -ErrorAction Stop
                        $success = $true
                    } catch {
                        $attempt++
                        if ($attempt -ge $maxAttempts) {
                            Write-Host "Failed to write to log file after $attempt attempts: $_" -ForegroundColor Red
                            $global:LogToFile = $false
                        } else {
                            Start-Sleep -Milliseconds 200
                        }
                    }
                }
            }
        }
        # Log initialization using the simple function
        Write-InitLog -Message "Logger initialized with level: $($global:LogLevel)" -Level "Debug"
        Write-InitLog -Message "Log file: $($global:LogFile)" -Level "Debug"
        return $true
    } catch {
        Write-Host "Failed to initialize logger: $_" -ForegroundColor Red
        return $false
    }
}

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [string]$Message,
        [Parameter(Mandatory=$false, Position=1)]
        [ValidateSet("Debug", "Info", "Warning", "Error", "Success", "UI")]
        [string]$Level = "Info",
        [Parameter(Mandatory=$false, Position=2)]
        [string]$Component = "General",
        [Parameter(Mandatory=$false)]
        [System.ConsoleColor]$ForegroundColor = [System.ConsoleColor]::White
    )
    try {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        
        # For UI level, don't include timestamp in console output
        if ($Level -eq "UI") {
            $consoleMessage = $Message
        } else {
            $consoleMessage = "[$timestamp] [$Level] [$Component] $Message"
        }
        
        # Always use full format for log file
        $formattedMessage = "[$timestamp] [$Level] [$Component] $Message"
        
        # Write to console
        if ($global:LogToConsole) {
            # If Level is UI, use the provided ForegroundColor
            # Otherwise use the color from LogLevels
            if ($Level -eq "UI") {
                Write-Host $consoleMessage -ForegroundColor $ForegroundColor
            } else {
                $color = $global:LogLevels[$Level].Color
                Write-Host $consoleMessage -ForegroundColor $color
            }
        }
        # Write to file
        if ($global:LogToFile -and $null -ne $global:LogFile) {
            $maxAttempts = 5
            $attempt = 0
            $success = $false
            while (-not $success -and $attempt -lt $maxAttempts) {
                try {
                    Add-Content -Path $global:LogFile -Value $formattedMessage -ErrorAction Stop
                    $success = $true
                } catch {
                    $attempt++
                    if ($attempt -ge $maxAttempts) {
                        Write-Host "Failed to write to log file after $attempt attempts: $_" -ForegroundColor Red
                        $global:LogToFile = $false
                    } else {
                        Start-Sleep -Milliseconds 200
                    }
                }
            }
        }
    } catch {
        Write-Host "Failed to write log: $_" -ForegroundColor Red
    }
}

function Get-LogFilePath {
    [CmdletBinding()]
    [OutputType([string])]
    param()
    if ($null -ne $global:LogFile) {
        return [System.IO.Path]::GetFullPath($global:LogFile)
    } else {
        return $null
    }
}

function Write-InitLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [string]$Message,
        [Parameter(Mandatory=$false, Position=1)]
        [ValidateSet("Debug", "Info", "Warning", "Error", "Success")]
        [string]$Level = "Info"
    )
    Write-Log -Message $Message -Level $Level -Component "General"
}

function Write-DebugLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [string]$Message,
        [Parameter(Mandatory=$false, Position=1)]
        [string]$Component = "General"
    )
    Write-Log -Message $Message -Level "Debug" -Component $Component
}

function Write-ProgressLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [string]$Message,
        [Parameter(Mandatory=$false, Position=1)]
        [string]$Component = "General"
    )
    Write-Log -Message $Message -Level "Info" -Component $Component
}

function Set-LogLevel {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [ValidateSet("Debug", "Info", "Warning", "Error", "Success")]
        [string]$Level
    )
    $global:LogLevel = $Level
}

function Write-SimpleLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [string]$Message,
        [Parameter(Mandatory=$false, Position=1)]
        [ValidateSet("Debug", "Info", "Warning", "Error", "Success", "UI")]
        [string]$Level = "UI",
        [Parameter(Mandatory=$false)]
        [System.ConsoleColor]$Color = [System.ConsoleColor]::White
    )
    # Use Write-Log with UI level to ensure consistent behavior
    # This will automatically handle timestamps correctly
    Write-Log -Message $Message -Level $Level -Component "General" -ForegroundColor $Color
}

Export-ModuleMember -Function Write-Log, Initialize-Logger, Get-LogFilePath, Write-InitLog, Write-DebugLog, Write-ProgressLog, Set-LogLevel, Write-SimpleLog
