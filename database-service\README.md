# Database Service

A C++23 database management service for the Git Dashboard project.

## Overview

The Database Service is a standalone C++23 application that provides database management capabilities for the Git Dashboard project. It handles database connections, schema management, security, and API endpoints for other services to interact with the database.

## Features

- **Connection Management**: Efficient connection pooling for PostgreSQL databases
- **Schema Management**: Automatic schema creation and migration
- **Enhanced Security**:
  - JWT-based authentication with refresh tokens
  - SSL/TLS support for secure communication
  - CORS support for web clients
  - Secure credential storage
- **API**: RESTful API for database operations
- **Database Metrics**: Detailed metrics for database operations
- **Logging**: Comprehensive logging system
- **Reverse Proxy Ready**: Designed for Nginx SSL termination

## Requirements

- C++23 compatible compiler (GCC 14.2+, Clang 16+, or MSVC 19.36+)
- CMake 3.20+
- PostgreSQL 14+
- Boost 1.74+
- OpenSSL 1.1.1+
- libpqxx (PostgreSQL C++ client library)
- n<PERSON><PERSON>_json (JSON for Modern C++)

## Building

```bash
mkdir build
cd build
cmake ..
make
```

## Installation

```bash
sudo make install
```

## Configuration

Edit the configuration file at `/etc/database-service/config.json`:

```json
{
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "database_service",
        "user": "postgres",
        "password": "postgres",
        "ssl": {
            "enabled": false,
            "mode": "verify-full",
            "cert_path": "/etc/letsencrypt/live/example.com/fullchain.pem",
            "key_path": "/etc/letsencrypt/live/example.com/privkey.pem",
            "ca_path": "/etc/letsencrypt/live/example.com/chain.pem",
            "crl_path": "",
            "reject_expired": true
        },
        "pool": {
            "max_connections": 10
        }
    },
    "api": {
        "port": 8080,
        "host": "127.0.0.1",
        "base_path": "/api",
        "cors": {
            "enabled": true,
            "allowed_origins": ["*"],
            "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allowed_headers": ["Content-Type", "Authorization"],
            "allow_credentials": true,
            "max_age": 86400
        },
        "ssl": {
            "enabled": false,
            "cert_path": "/etc/letsencrypt/live/example.com/fullchain.pem",
            "key_path": "/etc/letsencrypt/live/example.com/privkey.pem"
        }
    },
    "security": {
        "enable_authentication": true,
        "jwt_secret": "change-this-to-a-secure-secret-in-production",
        "token_expiration_seconds": 3600,
        "refresh_token_expiration_seconds": 86400,
        "password_hash_algorithm": "bcrypt",
        "password_hash_cost": 12,
        "secure_credential_storage": {
            "enabled": true,
            "encryption_key": "change-this-to-a-secure-encryption-key"
        }
    },
    "logging": {
        "enabled": true,
        "level": "info",
        "file": "/var/log/database-service/database-service.log",
        "max_size_mb": 10,
        "max_files": 5,
        "console": true
    },
    "schema": {
        "directory": "/etc/database-service/schemas",
        "auto_migrate": true,
        "version_table": "schema_version"
    }
}
```

## Running

### Development (HTTP only)
```bash
database-service --config /etc/database-service/config.json
```

### Production (with Nginx SSL termination)
```bash
# 1. Setup reverse proxy (run as root)
sudo ./scripts/setup-reverse-proxy.sh

# 2. Start the database service
systemctl start database-service

# 3. Access via HTTPS
curl https://database-service.chcit.org/api/health
```

## Architecture

### Reverse Proxy SSL Termination

The database service is designed to run behind an Nginx reverse proxy for production deployments:

```
Internet → Nginx (HTTPS/SSL) → Database Service (HTTP)
```

**Benefits:**
- SSL/TLS termination handled by Nginx
- Rate limiting and security headers
- Compression and caching
- Load balancing capabilities
- Centralized certificate management

**Configuration:**
- Database service runs on `127.0.0.1:8080` (HTTP only)
- Nginx handles SSL and forwards to backend
- SSL certificates managed by Let's Encrypt

## API Endpoints

### Authentication
- `GET /api/health`: Health check
- `POST /api/auth/login`: Authenticate user and get tokens
- `POST /api/auth/refresh`: Refresh access token
- `POST /api/auth/logout`: Logout and invalidate tokens
- `GET /api/auth/user`: Get user info

### Database Operations
- `POST /api/query`: Execute a query
- `POST /api/execute`: Execute a non-query statement

### Metrics
- `GET /api/database/metrics`: Get all database metrics
- `GET /api/database/metrics/connection-pool`: Get connection pool metrics
- `GET /api/database/metrics/query-performance`: Get query performance metrics

### Credential Management
- `POST /api/credentials/store`: Store a credential securely
- `GET /api/credentials/get`: Get a stored credential
- `DELETE /api/credentials/remove`: Remove a stored credential

## Documentation

For more detailed documentation, see:

- [Security Features](docs/security_features.md): Detailed information about security features
- [Database Metrics](docs/database_metrics.md): Information about database metrics
- [API Reference](docs/api_reference.md): Complete API reference

## License

This project is licensed under the MIT License - see the LICENSE file for details.
