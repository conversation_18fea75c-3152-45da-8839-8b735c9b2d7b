# Frontend Architecture 🔄

*Last Updated: March 9, 2025*

> This document provides detailed frontend implementation specifics. For a high-level system overview, see the [main architecture document](../architecture/PROJECT-TRACKER-ARCHITECTURE.md).

# Project Tracker Overview
Project Tracker is a comprehensive project management and improvement tracking system that helps teams monitor progress, track improvements, analyze code changes, generate insights and collaborate effectively.

## Overview

The Project Tracker frontend is a React/TypeScript application designed to run on its own dedicated Ubuntu server. This architectural decision provides better scalability, cleaner deployment, and improved resource management.

## Component Architecture

```mermaid
graph TD
    App[App.tsx] --> Router[React Router]
    Router --> ProjectList[ProjectList.tsx]
    Router --> GitOperationMonitor[GitOperationMonitor.tsx]
    Router --> Dashboard[Dashboard.tsx]
    ProjectList --> APIService[api.ts]
    GitOperationMonitor --> APIService
    GitOperationMonitor --> WebSocketService[websocket.ts]
    Dashboard --> APIService
    WebSocketService --> AuthService[auth.ts]
    APIService --> AuthService
```

### Core Components

- **App.tsx**: Main application component
- **ProjectList.tsx**: Displays list of projects with details and selection functionality
- **GitOperationMonitor.tsx**: Visualizes git operations and performance metrics
- **Dashboard.tsx**: Main dashboard interface for project monitoring

### Services

- **api.ts**: Handles REST API communication with backend
- **websocket.ts**: Manages WebSocket connections for real-time updates
- **auth.ts**: Handles authentication and token management

## UI Architecture 🔄

### Component Hierarchy

```mermaid
graph TD
    App --> Header
    App --> MainContent
    App --> Footer
    MainContent --> ProjectsView
    MainContent --> DashboardView
    MainContent --> GitMonitorView
    ProjectsView --> ProjectCard
    ProjectsView --> ProjectFilter
    DashboardView --> MetricsPanel
    DashboardView --> ActivityFeed
    GitMonitorView --> RepositoryList
    GitMonitorView --> OperationMetrics
```

### UI Component Status

| Component | Status | Description |
|-----------|--------|-------------|
| Header | ✅ | Application header with navigation and user info |
| ProjectsView | ✅ | Main project listing and management view |
| ProjectCard | ✅ | Individual project display component |
| ProjectFilter | ✅ | Filtering mechanism for projects list |
| DashboardView | 🔄 | Dashboard for metrics and activity monitoring |
| MetricsPanel | 🔄 | Visual display of key performance metrics |
| ActivityFeed | ✅ | Real-time feed of project activities |
| GitMonitorView | 🔄 | Git operations monitoring interface |
| RepositoryList | ✅ | List of tracked repositories |
| OperationMetrics | 🔄 | Performance metrics for git operations |

## State Management 🔄

### Redux Store Structure

```
store
├── auth/
│   ├── actions.ts
│   ├── reducers.ts
│   └── selectors.ts
├── projects/
│   ├── actions.ts
│   ├── reducers.ts
│   └── selectors.ts
├── git/
│   ├── actions.ts
│   ├── reducers.ts
│   └── selectors.ts
└── index.ts
```

### WebSocket Integration 🔄

The frontend implements a WebSocket client that connects to the backend WebSocket service for real-time updates. Key features include:

- Automatic reconnection with exponential backoff
- Authentication via JWT tokens
- Channel subscription for specific update types
- Message queuing during connection interruptions
- Event-based architecture for handling different message types

## Responsive Design ✅

The UI implements a responsive design approach using:

- CSS Grid and Flexbox for layout
- Media queries for different device sizes
- Mobile-first approach for core components
- Touch-friendly controls for mobile devices
- Adaptive content display based on screen size

## Accessibility 🔄

The application aims to meet WCAG 2.1 AA standards with:

- Semantic HTML structure
- ARIA attributes for complex components
- Keyboard navigation support
- Screen reader compatibility
- Sufficient color contrast
- Focus management for modals and dialogs

## Performance Optimizations 🔄

- Code splitting for route-based components
- Lazy loading for non-critical components
- Memoization of expensive calculations
- Virtualized lists for large data sets
- Image optimization and lazy loading
- Bundle size optimization

---

*This document provides detailed frontend implementation specifics. For system-wide architecture concerns, data flow, and integration points, refer to the [main architecture document](../architecture/PROJECT-TRACKER-ARCHITECTURE.md).*
