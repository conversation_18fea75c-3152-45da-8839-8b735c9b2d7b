# Start Database Service Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force
# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force

function Start-DatabaseService {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Start Database Service                 " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    if ($null -eq $script:Config -or $null -eq $script:Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "UI" -ForegroundColor Red
        if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
            Wait-ForUser
        } else {
            Write-Host "Press Enter to continue..." -ForegroundColor Yellow
            Read-Host | Out-Null
        }
        if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
            Show-MainMenu
        }
        return
    }
    
    Write-Log -Message "Starting database service on ${script:Config.ssh.host}..." -Level "UI" -ForegroundColor Cyan
    
    if (Get-Command -Name Invoke-SSHCommand -ErrorAction SilentlyContinue) {
        $result = Invoke-SSHCommand -Command "sudo systemctl start database-service" -Silent:$false
        if ($result.Success) {
            Write-Log -Message "Database service started successfully." -Level "UI" -ForegroundColor Green
            
            # Check service status
            $statusResult = Invoke-SSHCommand -Command "sudo systemctl status database-service" -Silent:$false
            if ($statusResult.Success) {
                Write-Log -Message "Service Status:" -Level "UI" -ForegroundColor Cyan
                Write-Log -Message $statusResult.Output -Level "UI" -ForegroundColor White
            }
        } else {
            Write-Log -Message "Failed to start database service!" -Level "UI" -ForegroundColor Red
            Write-Log -Message "Error: $($result.Error)" -Level "UI" -ForegroundColor White
        }
    } else {
        Write-Log -Message "Invoke-SSHCommand function not found. Cannot start service." -Level "Error" -Component "DatabaseService"
    }
    
    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }
    
    if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
        Wait-ForUser
    } else {
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null
    }
    
    if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
        Show-MainMenu
    }
}

# Export the function
Export-ModuleMember -Function Start-DatabaseService
