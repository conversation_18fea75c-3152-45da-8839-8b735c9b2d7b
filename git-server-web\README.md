# Git Dashboard Setup Guide

This document explains how to set up the Git Dashboard on your Ubuntu server.

## Prerequisites

- Ubuntu Server (20.04 LTS or newer)
- Root access or sudo privileges
- Git repository at `/home/<USER>/project-tracker.git`

## Quick Setup

1. Copy all files to your server:
```bash
scp -r * user@your-server:/tmp/git-dashboard/
```

2. SSH into your server and run the setup script:
```bash
cd /tmp/git-dashboard
chmod +x setup-git-dashboard.sh
sudo ./setup-git-dashboard.sh
```

## Manual Installation Steps

If you prefer to install manually, follow these steps:

1. Install required packages:
```bash
sudo apt-get update
sudo apt-get install python3.10 python3.10-venv python3-pip nginx git supervisor
```

2. Create installation directory:
```bash
sudo mkdir -p /opt/git-dashboard
```

3. Copy files and set permissions:
```bash
sudo cp -r * /opt/git-dashboard/
sudo chown -R www-data:www-data /opt/git-dashboard
```

4. Set up Python virtual environment:
```bash
cd /opt/git-dashboard
sudo python3.10 -m venv venv
sudo ./venv/bin/pip install -r requirements.txt
```

5. Configure Nginx and systemd:
```bash
sudo ln -s /opt/git-dashboard/nginx/git-dashboard.conf /etc/nginx/sites-enabled/
sudo cp /opt/git-dashboard/systemd/git-dashboard.service /etc/systemd/system/
```

6. Start services:
```bash
sudo systemctl daemon-reload
sudo systemctl enable git-dashboard
sudo systemctl start git-dashboard
sudo systemctl restart nginx
```

## Directory Structure

```
/opt/git-dashboard/
├── app.py              # Flask application
├── collect-metrics.sh  # Metrics collection script
├── css/               # Stylesheet files
├── data/              # Data storage
│   └── history/       # Metrics history
├── js/                # JavaScript files
├── logs/              # Application logs
├── nginx/             # Nginx configuration
└── systemd/           # Systemd service files
```

## Configuration Files

- Nginx configuration: `/etc/nginx/sites-enabled/git-dashboard`
- Systemd service: `/etc/systemd/system/git-dashboard.service`
- Application logs: `/opt/git-dashboard/logs/git-dashboard.log`
- Nginx logs: `/var/log/nginx/git-dashboard.error.log`

## Security Features

- Rate limiting on API endpoints
- Security headers (CSP, XSS Protection, etc.)
- File access restrictions
- Resource limits via systemd
- Proper file permissions
- Firewall configuration (if UFW is installed)

## Monitoring

The dashboard includes:
- Server metrics (CPU, Memory, Disk usage)
- Git repository statistics
- Recent commit history
- System uptime

Metrics are collected every minute and stored in `/opt/git-dashboard/data/history/metrics_history.json`.

## Troubleshooting

1. Check application status:
```bash
sudo systemctl status git-dashboard
```

2. View application logs:
```bash
tail -f /opt/git-dashboard/logs/git-dashboard.log
```

3. View Nginx error logs:
```bash
tail -f /var/log/nginx/git-dashboard.error.log
```

4. Test API endpoint:
```bash
curl http://localhost/api/dashboard
```

## Support

If you encounter any issues:
1. Check the logs mentioned above
2. Verify all services are running
3. Ensure proper permissions are set
4. Confirm network connectivity and firewall settings
