#include "database-service/client/database_client.hpp"
#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>

using json = nlohmann::json;

// Example log entry structure
struct LogEntry {
    std::string timestamp;
    std::string level;
    std::string source;
    std::string message;
    std::string details;
};

// Format timestamp for database
std::string formatTimestamp(const std::chrono::system_clock::time_point& timePoint) {
    auto time = std::chrono::system_clock::to_time_t(timePoint);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// Example logging service that uses the database client
class LoggingService {
public:
    LoggingService(const std::string& dbServiceUrl, const std::string& appName, const std::string& apiKey)
        : dbClient_(dbServiceUrl, appName, apiKey) {
        
        // Initialize database schema if needed
        initializeSchema();
    }
    
    // Log a message
    void log(const std::string& level, const std::string& source, const std::string& message, const std::string& details = "") {
        try {
            // Create log entry
            LogEntry entry{
                formatTimestamp(std::chrono::system_clock::now()),
                level,
                source,
                message,
                details
            };
            
            // Store in database
            storeLogEntry(entry);
        } catch (const std::exception& e) {
            std::cerr << "Error logging message: " << e.what() << std::endl;
        }
    }
    
    // Query logs
    std::vector<LogEntry> queryLogs(const std::string& level = "", const std::string& source = "", int limit = 100) {
        try {
            // Build query
            std::string query = "SELECT timestamp, level, source, message, details FROM logs WHERE 1=1";
            
            if (!level.empty()) {
                query += " AND level = '" + level + "'";
            }
            
            if (!source.empty()) {
                query += " AND source = '" + source + "'";
            }
            
            query += " ORDER BY timestamp DESC LIMIT " + std::to_string(limit);
            
            // Execute query
            json results = dbClient_.query(query);
            
            // Convert to log entries
            std::vector<LogEntry> logs;
            for (const auto& row : results) {
                LogEntry entry{
                    row["timestamp"].get<std::string>(),
                    row["level"].get<std::string>(),
                    row["source"].get<std::string>(),
                    row["message"].get<std::string>(),
                    row.contains("details") ? row["details"].get<std::string>() : ""
                };
                logs.push_back(entry);
            }
            
            return logs;
        } catch (const std::exception& e) {
            std::cerr << "Error querying logs: " << e.what() << std::endl;
            return {};
        }
    }
    
private:
    // Initialize database schema
    void initializeSchema() {
        try {
            // Create logs table if it doesn't exist
            dbClient_.execute(
                "CREATE TABLE IF NOT EXISTS logs ("
                "  id SERIAL PRIMARY KEY,"
                "  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,"
                "  level VARCHAR(20) NOT NULL,"
                "  source VARCHAR(100) NOT NULL,"
                "  message TEXT NOT NULL,"
                "  details TEXT,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");"
            );
            
            // Create indexes
            dbClient_.execute("CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);");
            dbClient_.execute("CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);");
            dbClient_.execute("CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);");
        } catch (const std::exception& e) {
            std::cerr << "Error initializing schema: " << e.what() << std::endl;
        }
    }
    
    // Store log entry in database
    void storeLogEntry(const LogEntry& entry) {
        try {
            // Insert log entry
            dbClient_.execute(
                "INSERT INTO logs (timestamp, level, source, message, details) "
                "VALUES ('" + entry.timestamp + "', '" + entry.level + "', '" + 
                entry.source + "', '" + entry.message + "', '" + entry.details + "');"
            );
        } catch (const std::exception& e) {
            std::cerr << "Error storing log entry: " << e.what() << std::endl;
        }
    }
    
    dbservice::client::DatabaseClient dbClient_;
};

int main() {
    try {
        // Create logging service
        LoggingService loggingService(
            "http://localhost:8080",
            "logging-service",
            "api-key-456"
        );
        
        // Log some messages
        loggingService.log("INFO", "main", "Application started");
        loggingService.log("DEBUG", "database", "Connected to database");
        loggingService.log("WARNING", "auth", "Failed login attempt", "username=test, ip=*************");
        loggingService.log("ERROR", "api", "Request failed", "status=500, error=Internal Server Error");
        
        // Query logs
        std::cout << "All logs:" << std::endl;
        auto logs = loggingService.queryLogs();
        for (const auto& log : logs) {
            std::cout << log.timestamp << " [" << log.level << "] " << log.source << ": " 
                     << log.message;
            
            if (!log.details.empty()) {
                std::cout << " (" << log.details << ")";
            }
            
            std::cout << std::endl;
        }
        
        // Query error logs
        std::cout << "\nError logs:" << std::endl;
        auto errorLogs = loggingService.queryLogs("ERROR");
        for (const auto& log : errorLogs) {
            std::cout << log.timestamp << " [" << log.level << "] " << log.source << ": " 
                     << log.message;
            
            if (!log.details.empty()) {
                std::cout << " (" << log.details << ")";
            }
            
            std::cout << std::endl;
        }
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

