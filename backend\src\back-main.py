from flask import Flask
from services.database_service import DatabaseService
from services.cache_service import CacheService
from services.websocket_service import WebSocketService
from utils.config_loader import load_config
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)
    
    # Load configuration
    config = load_config()
    app.config.update(config)
    
    # Initialize services
    db_service = DatabaseService()
    cache_service = CacheService()
    websocket_service = WebSocketService()
    
    # Register services with app
    app.db = db_service
    app.cache = cache_service
    app.websocket = websocket_service
    
    # Initialize WebSocket connection
    websocket_service.init_app(app)
    
    # Register error handlers
    @app.errorhandler(Exception)
    def handle_error(error):
        logger.error(f"Unhandled error: {str(error)}", exc_info=True)
        return {"error": "Internal server error"}, 500
    
    return app

if __name__ == "__main__":
    app = create_app()
    app.run(host="0.0.0.0", port=8081)  # Aligned with WebSocket port
