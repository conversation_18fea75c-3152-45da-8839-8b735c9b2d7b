#pragma once

#include <system_error>

namespace dbservice::core {

enum class DatabaseErrc {
    Success = 0,
    ConnectionError,
    QueryError,
    TransactionError,
    Timeout,
    InvalidArgument,
    ConstraintViolation,
    DeadlockDetected,
    PermissionDenied,
    ObjectInUse,
    ObjectDoesNotExist,
    InvalidState,
    SerializationFailure,
    ConfigurationError,
    NetworkError,
    ProtocolError,
    InternalError
};

/**
 * @brief Error category for database errors
 */
class DatabaseErrorCategory : public std::error_category {
public:
    [[nodiscard]] const char* name() const noexcept override {
        return "database";
    }

    [[nodiscard]] std::string message(int ev) const override {
        switch (static_cast<DatabaseErrc>(ev)) {
            case DatabaseErrc::Success: return "Success";
            case DatabaseErrc::ConnectionError: return "Connection error";
            case DatabaseErrc::QueryError: return "Query execution error";
            case DatabaseErrc::TransactionError: return "Transaction error";
            case DatabaseErrc::Timeout: return "Operation timed out";
            case DatabaseErrc::InvalidArgument: return "Invalid argument";
            case DatabaseErrc::ConstraintViolation: return "Constraint violation";
            case DatabaseErrc::DeadlockDetected: return "Deadlock detected";
            case DatabaseErrc::PermissionDenied: return "Permission denied";
            case DatabaseErrc::ObjectInUse: return "Object in use";
            case DatabaseErrc::ObjectDoesNotExist: return "Object does not exist";
            case DatabaseErrc::InvalidState: return "Invalid state";
            case DatabaseErrc::SerializationFailure: return "Serialization failure";
            case DatabaseErrc::ConfigurationError: return "Configuration error";
            case DatabaseErrc::NetworkError: return "Network error";
            case DatabaseErrc::ProtocolError: return "Protocol error";
            case DatabaseErrc::InternalError: return "Internal error";
            default: return "Unknown database error";
        }
    }
};

// Global error category instance
inline const DatabaseErrorCategory database_error_category_instance{};

// Make std::error_code from DatabaseErrc
inline std::error_code make_error_code(DatabaseErrc e) {
    return {static_cast<int>(e), database_error_category_instance};
}

// Allow DatabaseErrc to be used with std::error_code
inline const std::error_category& database_error_category() noexcept {
    return database_error_category_instance;
}

} // namespace dbservice::core

// Enable automatic conversion to std::error_code for DatabaseErrc
namespace std {
    template<>
    struct is_error_code_enum<dbservice::core::DatabaseErrc> : true_type {};
} // namespace std
