#include "syslog_parser.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace logging {

SyslogParser::SyslogParser() {
    // Initialize regular expressions for different syslog formats
    
    // Traditional format: May 20 10:15:30 hostname service[pid]: message
    traditionalFormat_ = std::regex(
        R"((\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+([^:]+)(?:\[(\d+)\])?:\s+(.*))"
    );
    
    // RFC 3164 format: <PRI>MMM DD HH:MM:SS hostname service[pid]: message
    rfc3164Format_ = std::regex(
        R"(<(\d+)>(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+([^:]+)(?:\[(\d+)\])?:\s+(.*))"
    );
    
    // RFC 5424 format: <PRI>VERSION TIMESTAMP HOSTNAME APP-NAME PROCID MSGID STRUCTURED-DATA MSG
    rfc5424Format_ = std::regex(
        R"(<(\d+)>(\d+)\s+(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2}))\s+(\S+)\s+(\S+)\s+(\S+)\s+(\S+)\s+(?:(\[.*?\])\s+)?(.*))"
    );
}

std::optional<LogEntry> SyslogParser::parse(const std::string& line, const std::string& source) {
    std::smatch matches;
    
    // Try traditional format
    if (std::regex_search(line, matches, traditionalFormat_)) {
        LogEntry entry;
        
        // Parse timestamp
        std::string timestampStr = matches[1].str();
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%b %d %H:%M:%S");
        
        // Add current year (since syslog doesn't include year)
        auto now = std::chrono::system_clock::now();
        auto nowTm = std::chrono::system_clock::to_time_t(now);
        auto nowTmStruct = *std::localtime(&nowTm);
        tm.tm_year = nowTmStruct.tm_year;
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set hostname
        entry.hostname = matches[2].str();
        
        // Set component
        entry.component = matches[3].str();
        
        // Set unit (process ID)
        if (matches[4].matched) {
            entry.unit = matches[4].str();
        }
        
        // Set message
        entry.message = matches[5].str();
        
        // Set source
        entry.source = source;
        
        // Set raw line
        entry.rawLine = line;
        
        // Determine log type based on content
        if (line.find("error") != std::string::npos || 
            line.find("fail") != std::string::npos || 
            line.find("critical") != std::string::npos) {
            entry.type = LogType::ERROR;
        } else if (line.find("warning") != std::string::npos || 
                   line.find("warn") != std::string::npos) {
            entry.type = LogType::WARNING;
        } else {
            entry.type = LogType::INFO;
        }
        
        // Set category
        entry.category = LogCategory::SYSTEM;
        
        return entry;
    }
    
    // Try RFC 3164 format
    if (std::regex_search(line, matches, rfc3164Format_)) {
        LogEntry entry;
        
        // Parse priority
        int priority = std::stoi(matches[1].str());
        int facility = priority >> 3;
        int severity = priority & 0x7;
        
        // Parse timestamp
        std::string timestampStr = matches[2].str();
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%b %d %H:%M:%S");
        
        // Add current year (since syslog doesn't include year)
        auto now = std::chrono::system_clock::now();
        auto nowTm = std::chrono::system_clock::to_time_t(now);
        auto nowTmStruct = *std::localtime(&nowTm);
        tm.tm_year = nowTmStruct.tm_year;
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set hostname
        entry.hostname = matches[3].str();
        
        // Set component
        entry.component = matches[4].str();
        
        // Set unit (process ID)
        if (matches[5].matched) {
            entry.unit = matches[5].str();
        }
        
        // Set message
        entry.message = matches[6].str();
        
        // Set source
        entry.source = source;
        
        // Set raw line
        entry.rawLine = line;
        
        // Determine log type based on severity
        switch (severity) {
            case 0: // Emergency
            case 1: // Alert
            case 2: // Critical
            case 3: // Error
                entry.type = LogType::ERROR;
                break;
            case 4: // Warning
                entry.type = LogType::WARNING;
                break;
            case 5: // Notice
            case 6: // Informational
                entry.type = LogType::INFO;
                break;
            case 7: // Debug
                entry.type = LogType::DEBUG;
                break;
            default:
                entry.type = LogType::INFO;
                break;
        }
        
        // Set category based on facility
        if (facility == 4 || facility == 10) {
            // auth, authpriv
            entry.category = LogCategory::SECURITY;
        } else {
            entry.category = LogCategory::SYSTEM;
        }
        
        // Add metadata
        entry.metadata["facility"] = std::to_string(facility);
        entry.metadata["severity"] = std::to_string(severity);
        
        return entry;
    }
    
    // Try RFC 5424 format
    if (std::regex_search(line, matches, rfc5424Format_)) {
        LogEntry entry;
        
        // Parse priority
        int priority = std::stoi(matches[1].str());
        int facility = priority >> 3;
        int severity = priority & 0x7;
        
        // Parse timestamp
        std::string timestampStr = matches[3].str();
        // RFC 5424 uses ISO 8601 format
        try {
            std::tm tm = {};
            std::istringstream ss(timestampStr);
            ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%S");
            
            // Convert to time_point
            auto timeT = std::mktime(&tm);
            entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        } catch (...) {
            // Use current time if parsing fails
            entry.timestamp = std::chrono::system_clock::now();
        }
        
        // Set hostname
        entry.hostname = matches[4].str();
        
        // Set component (app-name)
        entry.component = matches[5].str();
        
        // Set unit (process ID)
        if (matches[6].str() != "-") {
            entry.unit = matches[6].str();
        }
        
        // Set message
        entry.message = matches[9].str();
        
        // Set source
        entry.source = source;
        
        // Set raw line
        entry.rawLine = line;
        
        // Determine log type based on severity
        switch (severity) {
            case 0: // Emergency
            case 1: // Alert
            case 2: // Critical
            case 3: // Error
                entry.type = LogType::ERROR;
                break;
            case 4: // Warning
                entry.type = LogType::WARNING;
                break;
            case 5: // Notice
            case 6: // Informational
                entry.type = LogType::INFO;
                break;
            case 7: // Debug
                entry.type = LogType::DEBUG;
                break;
            default:
                entry.type = LogType::INFO;
                break;
        }
        
        // Set category based on facility
        if (facility == 4 || facility == 10) {
            // auth, authpriv
            entry.category = LogCategory::SECURITY;
        } else {
            entry.category = LogCategory::SYSTEM;
        }
        
        // Add metadata
        entry.metadata["facility"] = std::to_string(facility);
        entry.metadata["severity"] = std::to_string(severity);
        entry.metadata["version"] = matches[2].str();
        entry.metadata["msgid"] = matches[7].str();
        
        // Parse structured data if present
        if (matches[8].matched && matches[8].str() != "-") {
            entry.metadata["structured_data"] = matches[8].str();
        }
        
        return entry;
    }
    
    // No match
    return std::nullopt;
}

bool SyslogParser::canParse(const std::string& line) {
    // Check if line matches any of the syslog formats
    return std::regex_search(line, traditionalFormat_) ||
           std::regex_search(line, rfc3164Format_) ||
           std::regex_search(line, rfc5424Format_);
}

} // namespace logging
