-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler <PERSON><PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Performing Test HAVE_STD_EXPECTED
-- Performing Test HAVE_STD_EXPECTED - Success
-- Performing Test HAVE_STD_FORMAT
-- Performing Test HAVE_STD_FORMAT - Success
-- Performing Test HAVE_STD_PRINT
-- Performing Test HAVE_STD_PRINT - Failed
CMake Warning at CMakeLists.txt:82 (message):
  std::print is not available.  Will fall back to iostream.


CMake Warning (dev) at CMakeLists.txt:90 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

This warning is for project developers.  Use -Wno-dev to suppress it.

-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- GTest not found, tests will not be built
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: ON
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- 
-- C++23 Features:
--   std::expected: 1
--   std::format: 1
--   std::print: 
-- ===============================================
-- 
-- Configuring done (3.7s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
[1/15] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
FAILED: CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o -MF CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o.d -o CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o -c /home/<USER>/database-service-build/src/core/connection_manager.cpp
In file included from /home/<USER>/database-service-build/src/core/connection_manager.cpp:5:
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:68:5: error: ‘nlohmann’ does not name a type
   68 |     nlohmann::json getConnectionPoolMetrics() const;
      |     ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:74:5: error: ‘nlohmann’ does not name a type
   74 |     nlohmann::json getQueryPerformanceMetrics() const;
      |     ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:80:5: error: ‘nlohmann’ does not name a type
   80 |     nlohmann::json getTransactionMetrics() const;
      |     ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:86:5: error: ‘nlohmann’ does not name a type
   86 |     nlohmann::json getAuthenticationMetrics() const;
      |     ^~~~~~~~
/home/<USER>/database-service-build/include/database-service/metrics/database_metrics.hpp:92:5: error: ‘nlohmann’ does not name a type
   92 |     nlohmann::json getAllMetrics() const;
      |     ^~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ConnectionManager::ConnectionManager(const std::string&, size_t, const dbservice::core::SSLConfig&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:54:63: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   54 |             if (*expected_connection && (*expected_connection)->isOpen()) {
      |                                                               ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:55:39: error: no matching function for call to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::push_back(std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type&)’
   55 |                 connections_.push_back(*expected_connection);
      |                 ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/vector:66,
                 from /home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:4,
                 from /home/<USER>/database-service-build/src/core/connection_manager.cpp:1:
/usr/include/c++/14/bits/stl_vector.h:1283:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(const value_type&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1283 |       push_back(const value_type& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:35: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘const std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&’ {aka ‘const std::shared_ptr<dbservice::core::Connection>&’}
 1283 |       push_back(const value_type& __x)
      |                 ~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_vector.h:1300:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(value_type&&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1300 |       push_back(value_type&& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1300:30: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&&’ {aka ‘std::shared_ptr<dbservice::core::Connection>&&’}
 1300 |       push_back(value_type&& __x)
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:56:24: error: could not convert ‘((std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>*)(& expected_connection))->std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::operator*()’ from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘bool’
   56 |             } else if (*expected_connection) {
      |                        ^~~~~~~~~~~~~~~~~~~~
      |                        |
      |                        std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/src/core/connection_manager.cpp:57:154: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   57 |                  utils::Logger::warning(std::format("Initial connection created but failed to open (ID: {}). Not adding to pool.", (*expected_connection)->getId()));
      |                                                                                                                                                          ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:59:144: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
   59 |                  utils::Logger::error(std::format("Initial connection creation failed to produce a connection object: {}", expected_connection.error()));
      |                                                                                                                                                ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:62:108: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
   62 |             utils::Logger::error(std::format("Initial connection creation failed: {}", expected_connection.error()));
      |                                                                                                            ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ConnectionManager::ConnectionManager(const std::string&, size_t, bool)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:96:63: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   96 |             if (*expected_connection && (*expected_connection)->isOpen()) {
      |                                                               ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:97:39: error: no matching function for call to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::push_back(std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type&)’
   97 |                 connections_.push_back(*expected_connection);
      |                 ~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(const value_type&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1283 |       push_back(const value_type& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1283:35: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘const std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&’ {aka ‘const std::shared_ptr<dbservice::core::Connection>&’}
 1283 |       push_back(const value_type& __x)
      |                 ~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/stl_vector.h:1300:7: note: candidate: ‘constexpr void std::vector< <template-parameter-1-1>, <template-parameter-1-2> >::push_back(value_type&&) [with _Tp = std::shared_ptr<dbservice::core::Connection>; _Alloc = std::allocator<std::shared_ptr<dbservice::core::Connection> >; value_type = std::shared_ptr<dbservice::core::Connection>]’
 1300 |       push_back(value_type&& __x)
      |       ^~~~~~~~~
/usr/include/c++/14/bits/stl_vector.h:1300:30: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::vector<std::shared_ptr<dbservice::core::Connection> >::value_type&&’ {aka ‘std::shared_ptr<dbservice::core::Connection>&&’}
 1300 |       push_back(value_type&& __x)
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:98:24: error: could not convert ‘((std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>*)(& expected_connection))->std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::operator*()’ from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘bool’
   98 |             } else if (*expected_connection) {
      |                        ^~~~~~~~~~~~~~~~~~~~
      |                        |
      |                        std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/src/core/connection_manager.cpp:99:154: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
   99 |                  utils::Logger::warning(std::format("Initial connection created but failed to open (ID: {}). Not adding to pool.", (*expected_connection)->getId()));
      |                                                                                                                                                          ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:101:144: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  101 |                  utils::Logger::error(std::format("Initial connection creation failed to produce a connection object: {}", expected_connection.error()));
      |                                                                                                                                                ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:104:108: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  104 |             utils::Logger::error(std::format("Initial connection creation failed: {}", expected_connection.error()));
      |                                                                                                            ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::getConnection()’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:143:22: error: ‘class std::vector<std::shared_ptr<dbservice::core::Connection> >’ has no member named ‘pop_front’
  143 |         connections_.pop_front();
      |                      ^~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:41: error: no match for ‘operator&&’ (operand types are ‘std::shared_ptr<dbservice::core::Connection>’ and ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’})
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                 ~~~~~~~~~~~~~~~~~~~~~~~ ^~ ~~~~~~~~~~~~~~~~~~~~~~~~
      |                 |                          |
      |                 |                          std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
      |                 std::shared_ptr<dbservice::core::Connection>
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:41: note: candidate: ‘operator&&(bool, bool)’ (built-in)
  153 |             if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
      |                 ~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:153:41: note:   no known conversion for argument 2 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘bool’
/home/<USER>/database-service-build/src/core/connection_manager.cpp:158:47: error: base operand of ‘->’ has non-pointer type ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’}
  158 |                 if ((*new_connection_expected)->isOpen()) {
      |                                               ^~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:159:38: error: cannot convert ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::shared_ptr<dbservice::core::Connection>’
  159 |                     returnConnection(*new_connection_expected);
      |                                      ^~~~~~~~~~~~~~~~~~~~~~~~
      |                                      |
      |                                      std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type {aka dbservice::core::Connection}
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:83:55: note:   initializing argument 1 of ‘void dbservice::core::ConnectionManager::returnConnection(std::shared_ptr<dbservice::core::Connection>)’
   83 |     void returnConnection(std::shared_ptr<Connection> connection);
      |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:174:111: error: ‘class std::shared_ptr<dbservice::core::Connection>’ has no member named ‘error’
  174 |             std::string errorMsg = std::format("Failed to create new connection: {}", new_connection_expected.error());
      |                                                                                                               ^~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: error: no match for ‘operator=’ (operand types are ‘std::shared_ptr<dbservice::core::Connection>’ and ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’})
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/include/c++/14/memory:80,
                 from /home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:3:
/usr/include/c++/14/bits/shared_ptr.h:417:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<const std::shared_ptr<_Yp>&> std::shared_ptr<_Tp>::operator=(const std::shared_ptr<_Yp>&) [with _Tp = dbservice::core::Connection]’
  417 |         operator=(const shared_ptr<_Yp>& __r) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:417:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘const std::shared_ptr<_Tp>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:428:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<std::auto_ptr<_Up> > std::shared_ptr<_Tp>::operator=(std::auto_ptr<_Up>&&) [with _Tp = dbservice::core::Connection]’
  428 |         operator=(auto_ptr<_Yp>&& __r)
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:428:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘std::auto_ptr<_Up>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:445:9: note: candidate: ‘template<class _Yp> std::shared_ptr<_Tp>::_Assignable<std::shared_ptr<_Yp> > std::shared_ptr<_Tp>::operator=(std::shared_ptr<_Yp>&&) [with _Tp = dbservice::core::Connection]’
  445 |         operator=(shared_ptr<_Yp>&& __r) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:445:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘std::shared_ptr<_Tp>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:453:9: note: candidate: ‘template<class _Yp, class _Del> std::shared_ptr<_Tp>::_Assignable<std::unique_ptr<_Up, _Ep> > std::shared_ptr<_Tp>::operator=(std::unique_ptr<_Up, _Ep>&&) [with _Del = _Yp; _Tp = dbservice::core::Connection]’
  453 |         operator=(unique_ptr<_Yp, _Del>&& __r)
      |         ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:453:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:178:27: note:   ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} is not derived from ‘std::unique_ptr<_Tp, _Dp>’
  178 |         connection_ptr = *new_connection_expected;
      |                           ^~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:413:19: note: candidate: ‘std::shared_ptr<_Tp>& std::shared_ptr<_Tp>::operator=(const std::shared_ptr<_Tp>&) [with _Tp = dbservice::core::Connection]’
  413 |       shared_ptr& operator=(const shared_ptr&) noexcept = default;
      |                   ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:413:29: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘const std::shared_ptr<dbservice::core::Connection>&’
  413 |       shared_ptr& operator=(const shared_ptr&) noexcept = default;
      |                             ^~~~~~~~~~~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:437:7: note: candidate: ‘std::shared_ptr<_Tp>& std::shared_ptr<_Tp>::operator=(std::shared_ptr<_Tp>&&) [with _Tp = dbservice::core::Connection]’
  437 |       operator=(shared_ptr&& __r) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/shared_ptr.h:437:30: note:   no known conversion for argument 1 from ‘std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type’ {aka ‘dbservice::core::Connection’} to ‘std::shared_ptr<dbservice::core::Connection>&&’
  437 |       operator=(shared_ptr&& __r) noexcept
      |                 ~~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: At global scope:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:226:39: error: no declaration matches ‘std::vector<std::vector<std::__cxx11::basic_string<char> > > dbservice::core::ConnectionManager::executeQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char> >&)’
  226 | std::vector<std::vector<std::string>> ConnectionManager::executeQuery(const std::string& query, const std::vector<std::string>& params) {
      |                                       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:91:71: note: candidate is: ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char> > >, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char> >&)’
   91 |     std::expected<std::vector<std::vector<std::string>>, std::string> executeQuery(const std::string& query, const std::vector<std::string>& params = {});
      |                                                                       ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:50:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   50 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:282:6: error: no declaration matches ‘void dbservice::core::ConnectionManager::executeQueryWithCallback(const std::string&, std::function<void(const std::vector<std::__cxx11::basic_string<char> >&)>, const std::vector<std::__cxx11::basic_string<char> >&)’
  282 | void ConnectionManager::executeQueryWithCallback(const std::string& query, std::function<void(const std::vector<std::string>&)> callback, const std::vector<std::string>& params) {
      |      ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:100:38: note: candidate is: ‘std::expected<void, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeQueryWithCallback(const std::string&, std::function<void(std::span<const std::__cxx11::basic_string<char> >)>, const std::vector<std::__cxx11::basic_string<char> >&)’
  100 |     std::expected<void, std::string> executeQueryWithCallback(const std::string& query, std::function<void(std::span<const std::string>)> callback, const std::vector<std::string>& params = {});
      |                                      ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:50:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   50 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:302:5: error: no declaration matches ‘int dbservice::core::ConnectionManager::executeNonQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char> >&)’
  302 | int ConnectionManager::executeNonQuery(const std::string& statement, const std::vector<std::string>& params) {
      |     ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:108:37: note: candidate is: ‘std::expected<int, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::executeNonQuery(const std::string&, const std::vector<std::__cxx11::basic_string<char> >&)’
  108 |     std::expected<int, std::string> executeNonQuery(const std::string& statement, const std::vector<std::string>& params = {});
      |                                     ^~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:50:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   50 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:356:30: error: no declaration matches ‘std::shared_ptr<dbservice::core::Transaction> dbservice::core::ConnectionManager::beginTransaction()’
  356 | std::shared_ptr<Transaction> ConnectionManager::beginTransaction() {
      |                              ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:114:68: note: candidate is: ‘std::expected<std::shared_ptr<dbservice::core::Transaction>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::beginTransaction()’
  114 |     std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();
      |                                                                    ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:50:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   50 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp:400:57: error: no declaration matches ‘std::expected<std::shared_ptr<dbservice::core::Connection>, std::__cxx11::basic_string<char> > dbservice::core::ConnectionManager::createConnection()’
  400 | std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::createConnection() {
      |                                                         ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:161:33: note: candidate is: ‘std::shared_ptr<dbservice::core::Connection> dbservice::core::ConnectionManager::createConnection()’
  161 |     std::shared_ptr<Connection> createConnection();
      |                                 ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/core/connection_manager.hpp:50:7: note: ‘class dbservice::core::ConnectionManager’ defined here
   50 | class ConnectionManager {
      |       ^~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘size_t dbservice::core::ConnectionManager::getActiveConnectionCount() const’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:485:38: error: binding reference of type ‘std::lock_guard<std::mutex>::mutex_type&’ {aka ‘std::mutex&’} to ‘const std::mutex’ discards qualifiers
  485 |     std::lock_guard<std::mutex> lock(mutex_);
      |                                      ^~~~~~
In file included from /usr/include/c++/14/bits/atomic_wait.h:51,
                 from /usr/include/c++/14/bits/atomic_base.h:42,
                 from /usr/include/c++/14/bits/shared_ptr_atomic.h:33,
                 from /usr/include/c++/14/memory:81:
/usr/include/c++/14/bits/std_mutex.h:249:39: note:   initializing argument 1 of ‘std::lock_guard<_Mutex>::lock_guard(mutex_type&) [with _Mutex = std::mutex; mutex_type = std::mutex]’
  249 |       explicit lock_guard(mutex_type& __m) : _M_device(__m)
      |                           ~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘size_t dbservice::core::ConnectionManager::getIdleConnectionCount() const’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:490:38: error: binding reference of type ‘std::lock_guard<std::mutex>::mutex_type&’ {aka ‘std::mutex&’} to ‘const std::mutex’ discards qualifiers
  490 |     std::lock_guard<std::mutex> lock(mutex_);
      |                                      ^~~~~~
/usr/include/c++/14/bits/std_mutex.h:249:39: note:   initializing argument 1 of ‘std::lock_guard<_Mutex>::lock_guard(mutex_type&) [with _Mutex = std::mutex; mutex_type = std::mutex]’
  249 |       explicit lock_guard(mutex_type& __m) : _M_device(__m)
      |                           ~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In member function ‘size_t dbservice::core::ConnectionManager::getWaitingConnectionCount() const’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:495:38: error: binding reference of type ‘std::lock_guard<std::mutex>::mutex_type&’ {aka ‘std::mutex&’} to ‘const std::mutex’ discards qualifiers
  495 |     std::lock_guard<std::mutex> lock(mutex_);
      |                                      ^~~~~~
/usr/include/c++/14/bits/std_mutex.h:249:39: note:   initializing argument 1 of ‘std::lock_guard<_Mutex>::lock_guard(mutex_type&) [with _Mutex = std::mutex; mutex_type = std::mutex]’
  249 |       explicit lock_guard(mutex_type& __m) : _M_device(__m)
      |                           ~~~~~~~~~~~~^~~
/home/<USER>/database-service-build/src/core/connection_manager.cpp: In constructor ‘dbservice::core::ManagedConnection::ManagedConnection(dbservice::core::ConnectionManager&)’:
/home/<USER>/database-service-build/src/core/connection_manager.cpp:534:160: error: ‘using std::__shared_ptr_access<dbservice::core::Connection, __gnu_cxx::_S_atomic, false, false>::element_type = class dbservice::core::Connection’ {aka ‘class dbservice::core::Connection’} has no member named ‘getId’
  534 |         utils::Logger::warning(std::format("ManagedConnection: Acquired connection (ID: {}) but it was not open. Returning to pool.", (*connection_expected_)->getId()));
      |                                                                                                                                                                ^~~~~
[2/15] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
FAILED: CMakeFiles/database-service.dir/src/api/api_server.cpp.o 
/usr/bin/g++-14 -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -MD -MT CMakeFiles/database-service.dir/src/api/api_server.cpp.o -MF CMakeFiles/database-service.dir/src/api/api_server.cpp.o.d -o CMakeFiles/database-service.dir/src/api/api_server.cpp.o -c /home/<USER>/database-service-build/src/api/api_server.cpp
In file included from /home/<USER>/database-service-build/src/api/api_server.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:74:25: error: ‘ApiError’ was not declared in this scope; did you mean ‘perror’?
   74 |     std::expected<void, ApiError> start();
      |                         ^~~~~~~~
      |                         perror
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:74:33: error: template argument 2 is invalid
   74 |     std::expected<void, ApiError> start();
      |                                 ^
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:74:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
   74 |     std::expected<void, ApiError> start();
      |          ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:80:25: error: ‘ApiError’ was not declared in this scope; did you mean ‘perror’?
   80 |     std::expected<void, ApiError> stop();
      |                         ^~~~~~~~
      |                         perror
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:80:33: error: template argument 2 is invalid
   80 |     std::expected<void, ApiError> stop();
      |                                 ^
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:80:10: error: ‘<expression error>’ in namespace ‘std’ does not name a type
   80 |     std::expected<void, ApiError> stop();
      |          ^~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:117:62: error: ‘ParsedRequest’ does not name a type
  117 |     std::expected<Response, std::string> handleRequest(const ParsedRequest& request);
      |                                                              ^~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In destructor ‘dbservice::api::ApiServer::~ApiServer()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:56:5: error: ‘stop’ was not declared in this scope
   56 |     stop();
      |     ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:59:37: error: ‘ApiError’ is not a member of ‘dbservice::api’
   59 | std::expected<void, dbservice::api::ApiError> ApiServer::start() {
      |                                     ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:59:45: error: template argument 2 is invalid
   59 | std::expected<void, dbservice::api::ApiError> ApiServer::start() {
      |                                             ^
/home/<USER>/database-service-build/src/api/api_server.cpp:59:47: error: no declaration matches ‘int dbservice::api::ApiServer::start()’
   59 | std::expected<void, dbservice::api::ApiError> ApiServer::start() {
      |                                               ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:59:47: note: no functions named ‘int dbservice::api::ApiServer::start()’
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:133:37: error: ‘ApiError’ is not a member of ‘dbservice::api’
  133 | std::expected<void, dbservice::api::ApiError> ApiServer::stop() {
      |                                     ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:133:45: error: template argument 2 is invalid
  133 | std::expected<void, dbservice::api::ApiError> ApiServer::stop() {
      |                                             ^
/home/<USER>/database-service-build/src/api/api_server.cpp:133:47: error: no declaration matches ‘int dbservice::api::ApiServer::stop()’
  133 | std::expected<void, dbservice::api::ApiError> ApiServer::stop() {
      |                                               ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:133:47: note: no functions named ‘int dbservice::api::ApiServer::stop()’
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:238:70: error: no declaration matches ‘std::pair<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(const std::string&, const std::string&, const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::string&)’
  238 | std::pair<std::unordered_map<std::string, std::string>, std::string> ApiServer::handleRequest(
      |                                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:117:42: note: candidate is: ‘std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(const int&)’
  117 |     std::expected<Response, std::string> handleRequest(const ParsedRequest& request);
      |                                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:359:6: error: no declaration matches ‘void dbservice::api::ApiServer::applyCorsHeaders(const std::string&, const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&)’
  359 | void ApiServer::applyCorsHeaders(
      |      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:125:10: note: candidate is: ‘void dbservice::api::ApiServer::applyCorsHeaders(std::string_view, std::span<const std::pair<const std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > >, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&)’
  125 |     void applyCorsHeaders(
      |          ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:488:30: error: no matching function for call to ‘dbservice::api::ApiServer::parseRequest(std::string&, std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
  488 |             if (!parseRequest(request, method, path, headers, body)) {
      |                  ~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:157:47: note: candidate: ‘std::expected<dbservice::api::ApiServer::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  157 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:157:47: note:   candidate expects 1 argument, 5 provided
/home/<USER>/database-service-build/src/api/api_server.cpp:490:59: error: conversion from ‘dbservice::api::ApiServer::Response’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
  490 |                 std::string response = createErrorResponse(400, "Bad Request", "Invalid request format");
      |                                        ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:497:65: error: no matching function for call to ‘dbservice::api::ApiServer::handleRequest(std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
  497 |             auto [responseHeaders, responseBody] = handleRequest(method, path, headers, body);
      |                                                    ~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:117:42: note: candidate: ‘std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::handleRequest(const int&)’
  117 |     std::expected<Response, std::string> handleRequest(const ParsedRequest& request);
      |                                          ^~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:117:42: note:   candidate expects 1 argument, 4 provided
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)> [with auto:57 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:58 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:546:5:   required from here
  546 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:544:51: warning: unused parameter ‘headers’ [-Wunused-parameter]
  544 |     optionsHandlers_["/api"] = [this](const auto& headers, const auto& body) {
      |                                       ~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:544:72: warning: unused parameter ‘body’ [-Wunused-parameter]
  544 |     optionsHandlers_["/api"] = [this](const auto& headers, const auto& body) {
      |                                                            ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In member function ‘void dbservice::api::ApiServer::initializeRoutes()’:
/home/<USER>/database-service-build/src/api/api_server.cpp:546:5: error: no match for ‘operator=’ (operand types are ‘std::unordered_map<std::__cxx11::basic_string<char>, std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)> >::mapped_type’ {aka ‘std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>’} and ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’)
  546 |     };
      |     ^
In file included from /usr/include/c++/14/functional:59,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:4:
/usr/include/c++/14/bits/std_function.h:531:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>::_Requires<std::function<_Res(_ArgTypes ...)>::_Callable<_Functor>, std::function<_Res(_ArgTypes ...)>&> std::function<_Res(_ArgTypes ...)>::operator=(_Functor&&) [with _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:9: note:   template argument deduction/substitution failed:
In file included from /usr/include/c++/14/bits/char_traits.h:50,
                 from /usr/include/c++/14/string:42,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:2:
/usr/include/c++/14/type_traits: In substitution of ‘template<bool _Cond, class _Tp> using std::__enable_if_t = typename std::enable_if::type [with bool _Cond = false; _Tp = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>&]’:
/usr/include/c++/14/bits/std_function.h:353:8:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>&; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |               ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:531:2:   required by substitution of ‘template<class _Functor> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Requires<std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<_Functor, typename std::enable_if<(!(bool)std::is_same<typename std::remove_cv<typename std::remove_reference<_Tp>::type>::type, std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)> >::value), std::decay<_Ex> >::type::type, std::__invoke_result<typename std::enable_if<(!(bool)std::is_same<typename std::remove_cv<typename std::remove_reference<_Tp>::type>::type, std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)> >::value), std::decay<_Ex> >::type::type&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >, std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>&> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::operator=(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>]’
  531 |         operator=(_Functor&& __f)
      |         ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:546:5:   required from here
  546 |     };
      |     ^
/usr/include/c++/14/type_traits:138:11: error: no type named ‘type’ in ‘struct std::enable_if<false, std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>&>’
  138 |     using __enable_if_t = typename enable_if<_Cond, _Tp>::type;
      |           ^~~~~~~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note: candidate: ‘template<class _Functor> std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::reference_wrapper<_Functor>) [with _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  541 |         operator=(reference_wrapper<_Functor> __f) noexcept
      |         ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:541:9: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:546:5: note:   ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ is not derived from ‘std::reference_wrapper<_Tp>’
  546 |     };
      |     ^
/usr/include/c++/14/bits/std_function.h:469:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(const std::function<_Res(_ArgTypes ...)>&) [with _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  469 |       operator=(const function& __x)
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:469:33: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ to ‘const std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>&’
  469 |       operator=(const function& __x)
      |                 ~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:487:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::function<_Res(_ArgTypes ...)>&&) [with _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  487 |       operator=(function&& __x) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:487:28: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ to ‘std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>&&’
  487 |       operator=(function&& __x) noexcept
      |                 ~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:501:7: note: candidate: ‘std::function<_Res(_ArgTypes ...)>& std::function<_Res(_ArgTypes ...)>::operator=(std::nullptr_t) [with _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}; std::nullptr_t = std::nullptr_t]’
  501 |       operator=(nullptr_t) noexcept
      |       ^~~~~~~~
/usr/include/c++/14/bits/std_function.h:501:17: note:   no known conversion for argument 1 from ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:57&, const auto:58&)>’ to ‘std::nullptr_t’
  501 |       operator=(nullptr_t) noexcept
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)> [with auto:59 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:60 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:59&, const auto:60&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:557:5:   required from here
  557 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:549:54: warning: unused parameter ‘headers’ [-Wunused-parameter]
  549 |     getHandlers_["/api/health"] = [this](const auto& headers, const auto& body) {
      |                                          ~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:549:75: warning: unused parameter ‘body’ [-Wunused-parameter]
  549 |     getHandlers_["/api/health"] = [this](const auto& headers, const auto& body) {
      |                                                               ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:576:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  576 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:581:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  581 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:581:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:581:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:581:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  581 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:581:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:581:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)> [with auto:61 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:62 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:61&, const auto:62&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:593:5:   required from here
  593 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:560:85: warning: unused parameter ‘body’ [-Wunused-parameter]
  560 |     getHandlers_["/api/database/metrics"] = [this](const auto& headers, const auto& body) {
      |                                                                         ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:611:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  611 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:616:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  616 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:616:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:616:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:616:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  616 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:616:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:616:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)> [with auto:63 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:64 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:63&, const auto:64&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:628:5:   required from here
  628 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:595:101: warning: unused parameter ‘body’ [-Wunused-parameter]
  595 |     getHandlers_["/api/database/metrics/connection-pool"] = [this](const auto& headers, const auto& body) {
      |                                                                                         ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:646:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  646 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:651:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  651 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:651:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:651:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:651:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  651 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:651:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:651:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)> [with auto:65 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:66 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:65&, const auto:66&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:663:5:   required from here
  663 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:630:103: warning: unused parameter ‘body’ [-Wunused-parameter]
  630 |     getHandlers_["/api/database/metrics/query-performance"] = [this](const auto& headers, const auto& body) {
      |                                                                                           ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)> [with auto:67 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:68 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:67&, const auto:68&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:696:5:   required from here
  696 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:679:72: error: conversion from ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError>’ to non-scalar type ‘dbservice::security::TokenPair’ requested
  679 |             security::TokenPair tokens = securityManager_->authenticate(username, password);
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:668:59: warning: unused parameter ‘headers’ [-Wunused-parameter]
  668 |     postHandlers_["/api/auth/login"] = [this](const auto& headers, const auto& body) {
      |                                               ~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)> [with auto:69 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:70 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:69&, const auto:70&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:724:5:   required from here
  724 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:708:78: error: conversion from ‘std::expected<dbservice::security::TokenPair, dbservice::security::SecurityError>’ to non-scalar type ‘dbservice::security::TokenPair’ requested
  708 |             security::TokenPair tokens = securityManager_->refreshAccessToken(refreshToken);
      |                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:699:61: warning: unused parameter ‘headers’ [-Wunused-parameter]
  699 |     postHandlers_["/api/auth/refresh"] = [this](const auto& headers, const auto& body) {
      |                                                 ~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:741:22: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  741 |         if (userInfo.empty()) {
      |                      ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:746:34: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  746 |             {"username", userInfo["username"]},
      |                                  ^
/home/<USER>/database-service-build/src/api/api_server.cpp:746:34: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:746:34: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:747:31: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [6]’)
  747 |             {"email", userInfo["email"]},
      |                               ^
/home/<USER>/database-service-build/src/api/api_server.cpp:747:31: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:747:31: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:748:34: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  748 |             {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
      |                                  ^
/home/<USER>/database-service-build/src/api/api_server.cpp:748:34: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:748:34: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:748:65: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  748 |             {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
      |                                                                 ^
/home/<USER>/database-service-build/src/api/api_server.cpp:748:65: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:748:65: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:749:36: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [11]’)
  749 |             {"created_at", userInfo["created_at"]}
      |                                    ^
/home/<USER>/database-service-build/src/api/api_server.cpp:749:36: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:749:36: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)> [with auto:71 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:72 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:71&, const auto:72&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:753:5:   required from here
  753 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:745:24: error: could not convert ‘{{"username", <expression error>}, {"email", <expression error>}, {"is_admin", <expression error>}, {"created_at", <expression error>}}’ from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::json’ {aka ‘nlohmann::json_abi_v3_11_3::basic_json<>’}
  745 |         nlohmann::json response = {
      |                        ^~~~~~~~
      |                        |
      |                        <brace-enclosed initializer list>
/home/<USER>/database-service-build/src/api/api_server.cpp:727:78: warning: unused parameter ‘body’ [-Wunused-parameter]
  727 |     getHandlers_["/api/auth/user"] = [this](const auto& headers, const auto& body) {
      |                                                                  ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:771:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  771 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:775:44: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  775 |             std::string username = userInfo["username"];
      |                                            ^
/home/<USER>/database-service-build/src/api/api_server.cpp:775:44: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:775:44: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)> [with auto:73 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:74 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:73&, const auto:74&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:790:5:   required from here
  790 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:776:62: error: cannot convert ‘std::expected<void, dbservice::security::SecurityError>’ to ‘bool’ in initialization
  776 |             bool success = securityManager_->invalidateTokens(username);
      |                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~
      |                                                              |
      |                                                              std::expected<void, dbservice::security::SecurityError>
In file included from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:10:
/usr/include/c++/14/expected:1475:7: note: explicit conversion function was not considered
 1475 |       operator bool() const noexcept { return _M_has_value; }
      |       ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:756:81: warning: unused parameter ‘body’ [-Wunused-parameter]
  756 |     postHandlers_["/api/auth/logout"] = [this](const auto& headers, const auto& body) {
      |                                                                     ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:809:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  809 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:814:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  814 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:814:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:814:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:814:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  814 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:814:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:814:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:864:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  864 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:869:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  869 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:869:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:869:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:869:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  869 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:869:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:869:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)> [with auto:77 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:78 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:77&, const auto:78&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:900:5:   required from here
  900 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:848:84: warning: unused parameter ‘body’ [-Wunused-parameter]
  848 |     getHandlers_["/api/credentials/get"] = [this](const auto& headers, const auto& body) {
      |                                                                        ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:918:26: error: ‘class std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ has no member named ‘empty’
  918 |             if (userInfo.empty()) {
      |                          ^~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:923:25: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  923 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                         ^
/home/<USER>/database-service-build/src/api/api_server.cpp:923:25: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:923:25: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp:923:56: error: no match for ‘operator[]’ (operand types are ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ and ‘const char [9]’)
  923 |             if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
      |                                                        ^
/home/<USER>/database-service-build/src/api/api_server.cpp:923:56: note: candidate: ‘operator[](long int, const char*)’ (built-in)
/home/<USER>/database-service-build/src/api/api_server.cpp:923:56: note:   no known conversion for argument 1 from ‘std::expected<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, dbservice::security::SecurityError>’ to ‘long int’
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)> [with auto:79 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:80 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:79&, const auto:80&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:953:5:   required from here
  953 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:902:90: warning: unused parameter ‘body’ [-Wunused-parameter]
  902 |     deleteHandlers_["/api/credentials/remove"] = [this](const auto& headers, const auto& body) {
      |                                                                              ~~~~~~~~~~~~^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/api_server.cpp:976:33: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘size’
  976 |                 {"rows", result.size()},
      |                                 ^~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: error: no matching function for call to ‘begin(std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >&)’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
In file included from /usr/include/c++/14/bits/range_access.h:36,
                 from /usr/include/c++/14/string:53:
/usr/include/c++/14/initializer_list:88:5: note: candidate: ‘template<class _Tp> constexpr const _Tp* std::begin(initializer_list<_Tp>)’
   88 |     begin(initializer_list<_Tp> __ils) noexcept
      |     ^~~~~
/usr/include/c++/14/initializer_list:88:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::initializer_list<_Tp>’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:52:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(_Container&)’
   52 |     begin(_Container& __cont) -> decltype(__cont.begin())
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:52:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(_Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36:   required from here
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:52:50: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘begin’
   52 |     begin(_Container& __cont) -> decltype(__cont.begin())
      |                                           ~~~~~~~^~~~~
/usr/include/c++/14/bits/range_access.h:63:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(const _Container&)’
   63 |     begin(const _Container& __cont) -> decltype(__cont.begin())
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:63:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.begin()) std::begin(const _Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36:   required from here
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:63:56: error: ‘const class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘begin’
   63 |     begin(const _Container& __cont) -> decltype(__cont.begin())
      |                                                 ~~~~~~~^~~~~
/usr/include/c++/14/bits/range_access.h:95:5: note: candidate: ‘template<class _Tp, long unsigned int _Nm> constexpr _Tp* std::begin(_Tp (&)[_Nm])’
   95 |     begin(_Tp (&__arr)[_Nm]) noexcept
      |     ^~~~~
/usr/include/c++/14/bits/range_access.h:95:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   mismatched types ‘_Tp [_Nm]’ and ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
In file included from /usr/include/nlohmann/detail/conversions/from_json.hpp:21,
                 from /usr/include/nlohmann/adl_serializer.hpp:14,
                 from /usr/include/nlohmann/json.hpp:34,
                 from /home/<USER>/database-service-build/include/database-service/api/api_server.hpp:9:
/usr/include/c++/14/valarray:1227:5: note: candidate: ‘template<class _Tp> _Tp* std::begin(valarray<_Tp>&)’
 1227 |     begin(valarray<_Tp>& __va) noexcept
      |     ^~~~~
/usr/include/c++/14/valarray:1227:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::valarray<_Tp>’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1238:5: note: candidate: ‘template<class _Tp> const _Tp* std::begin(const valarray<_Tp>&)’
 1238 |     begin(const valarray<_Tp>& __va) noexcept
      |     ^~~~~
/usr/include/c++/14/valarray:1238:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::valarray<_Tp>’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: error: no matching function for call to ‘end(std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >&)’
/usr/include/c++/14/initializer_list:99:5: note: candidate: ‘template<class _Tp> constexpr const _Tp* std::end(initializer_list<_Tp>)’
   99 |     end(initializer_list<_Tp> __ils) noexcept
      |     ^~~
/usr/include/c++/14/initializer_list:99:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::initializer_list<_Tp>’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:74:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.end()) std::end(_Container&)’
   74 |     end(_Container& __cont) -> decltype(__cont.end())
      |     ^~~
/usr/include/c++/14/bits/range_access.h:74:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.end()) std::end(_Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36:   required from here
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:74:48: error: ‘class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘end’
   74 |     end(_Container& __cont) -> decltype(__cont.end())
      |                                         ~~~~~~~^~~
/usr/include/c++/14/bits/range_access.h:85:5: note: candidate: ‘template<class _Container> constexpr decltype (__cont.end()) std::end(const _Container&)’
   85 |     end(const _Container& __cont) -> decltype(__cont.end())
      |     ^~~
/usr/include/c++/14/bits/range_access.h:85:5: note:   template argument deduction/substitution failed:
/usr/include/c++/14/bits/range_access.h: In substitution of ‘template<class _Container> constexpr decltype (__cont.end()) std::end(const _Container&) [with _Container = std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >]’:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36:   required from here
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/bits/range_access.h:85:54: error: ‘const class std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ has no member named ‘end’
   85 |     end(const _Container& __cont) -> decltype(__cont.end())
      |                                               ~~~~~~~^~~
/usr/include/c++/14/bits/range_access.h:106:5: note: candidate: ‘template<class _Tp, long unsigned int _Nm> constexpr _Tp* std::end(_Tp (&)[_Nm])’
  106 |     end(_Tp (&__arr)[_Nm]) noexcept
      |     ^~~
/usr/include/c++/14/bits/range_access.h:106:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   mismatched types ‘_Tp [_Nm]’ and ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1249:5: note: candidate: ‘template<class _Tp> _Tp* std::end(valarray<_Tp>&)’
 1249 |     end(valarray<_Tp>& __va) noexcept
      |     ^~~
/usr/include/c++/14/valarray:1249:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘std::valarray<_Tp>’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/usr/include/c++/14/valarray:1265:5: note: candidate: ‘template<class _Tp> const _Tp* std::end(const valarray<_Tp>&)’
 1265 |     end(const valarray<_Tp>& __va) noexcept
      |     ^~~
/usr/include/c++/14/valarray:1265:5: note:   template argument deduction/substitution failed:
/home/<USER>/database-service-build/src/api/api_server.cpp:980:36: note:   ‘std::expected<std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >, std::__cxx11::basic_string<char> >’ is not derived from ‘const std::valarray<_Tp>’
  980 |             for (const auto& row : result) {
      |                                    ^~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)> [with auto:81 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:82 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:81&, const auto:82&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:988:5:   required from here
  988 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:975:28: error: could not convert ‘{{"rows", <expression error>}, {"data", nlohmann::json_abi_v3_11_3::basic_json<ObjectType, ArrayType, StringType, BooleanType, NumberIntegerType, NumberUnsignedType, NumberFloatType, AllocatorType, JSONSerializer, BinaryType, CustomBaseClass>::array(initializer_list_t) [with ObjectType = std::map; ArrayType = std::vector; StringType = std::__cxx11::basic_string<char>; BooleanType = bool; NumberIntegerType = long int; NumberUnsignedType = long unsigned int; NumberFloatType = double; AllocatorType = std::allocator; JSONSerializer = nlohmann::json_abi_v3_11_3::adl_serializer; BinaryType = std::vector<unsigned char>; CustomBaseClass = void; initializer_list_t = std::initializer_list<nlohmann::json_abi_v3_11_3::detail::json_ref<nlohmann::json_abi_v3_11_3::basic_json<> > >]()}}’ from ‘<brace-enclosed initializer list>’ to ‘nlohmann::json_abi_v3_11_3::json’ {aka ‘nlohmann::json_abi_v3_11_3::basic_json<>’}
  975 |             nlohmann::json response = {
      |                            ^~~~~~~~
      |                            |
      |                            <brace-enclosed initializer list>
/home/<USER>/database-service-build/src/api/api_server.cpp:956:54: warning: unused parameter ‘headers’ [-Wunused-parameter]
  956 |     postHandlers_["/api/query"] = [this](const auto& headers, const auto& body) {
      |                                          ~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: In instantiation of ‘dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)> [with auto:83 = std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >; auto:84 = std::__cxx11::basic_string<char>]’:
/usr/include/c++/14/type_traits:2640:26:   required by substitution of ‘template<class _Fn, class ... _Args> static std::__result_of_success<decltype (declval<_Fn>()((declval<_Args>)()...)), std::__invoke_other> std::__result_of_other_impl::_S_test(int) [with _Fn = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>&; _Args = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
 2640 |       std::declval<_Fn>()(std::declval<_Args>()...)
      |       ~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/include/c++/14/type_traits:2651:60:   required from ‘struct std::__result_of_impl<false, false, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>’
 2651 |       using type = decltype(_S_test<_Functor, _ArgTypes...>(0));
      |                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~
/usr/include/c++/14/bits/std_function.h:348:9:   recursively required by substitution of ‘template<class _Result, class _Ret> struct std::__is_invocable_impl<_Result, _Ret, false, std::__void_t<typename _CTp::type> > [with _Result = std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&>; _Ret = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >]’
  348 |         struct _Callable
      |                ^~~~~~~~~
/usr/include/c++/14/bits/std_function.h:348:9:   required from ‘struct std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >’
/usr/include/c++/14/bits/std_function.h:353:41:   required by substitution of ‘template<class _Res, class ... _ArgTypes> template<class _Cond, class _Tp> using std::function<_Res(_ArgTypes ...)>::_Requires = std::__enable_if_t<((bool)_Cond::value), _Tp> [with _Cond = std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::_Callable<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>, dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>, std::__invoke_result<dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>&, const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&> >; _Tp = void; _Res = std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >; _ArgTypes = {const std::unordered_map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >&, const std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&}]’
  353 |         using _Requires = __enable_if_t<_Cond::value, _Tp>;
      |                                                ^~~~~
/usr/include/c++/14/bits/std_function.h:434:9:   required by substitution of ‘template<class _Functor, class _Constraints> std::function<std::expected<dbservice::api::ApiServer::Response, std::__cxx11::basic_string<char> >(const std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, const std::__cxx11::basic_string<char>&)>::function(_Functor&&) [with _Functor = dbservice::api::ApiServer::initializeRoutes()::<lambda(const auto:83&, const auto:84&)>; _Constraints = <missing>]’
  434 |                typename _Constraints = _Requires<_Callable<_Functor>>>
      |                ^~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1022:5:   required from here
 1022 |     };
      |     ^
/home/<USER>/database-service-build/src/api/api_server.cpp:1008:61: error: cannot convert ‘std::expected<int, std::__cxx11::basic_string<char> >’ to ‘int’ in initialization
 1008 |             int result = connectionManager_->executeNonQuery(statement, params);
      |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~
      |                                                             |
      |                                                             std::expected<int, std::__cxx11::basic_string<char> >
/home/<USER>/database-service-build/src/api/api_server.cpp:991:56: warning: unused parameter ‘headers’ [-Wunused-parameter]
  991 |     postHandlers_["/api/execute"] = [this](const auto& headers, const auto& body) {
      |                                            ~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp: At global scope:
/home/<USER>/database-service-build/src/api/api_server.cpp:1025:6: error: no declaration matches ‘bool dbservice::api::ApiServer::parseRequest(const std::string&, std::string&, std::string&, std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >&, std::string&)’
 1025 | bool ApiServer::parseRequest(const std::string& request,
      |      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:157:47: note: candidate is: ‘std::expected<dbservice::api::ApiServer::ParsedRequest, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseRequest(const std::string&)’
  157 |     std::expected<ParsedRequest, std::string> parseRequest(const std::string& request);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1078:13: error: no declaration matches ‘std::string dbservice::api::ApiServer::createSuccessResponse(const nlohmann::json_abi_v3_11_3::json&)’
 1078 | std::string ApiServer::createSuccessResponse(const nlohmann::json& data) {
      |             ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:164:14: note: candidate is: ‘dbservice::api::ApiServer::Response dbservice::api::ApiServer::createSuccessResponse(const nlohmann::json_abi_v3_11_3::json&)’
  164 |     Response createSuccessResponse(const nlohmann::json& data);
      |              ^~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1097:70: error: no declaration matches ‘std::pair<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::createSuccessResponseWithHeaders(const nlohmann::json_abi_v3_11_3::json&)’
 1097 | std::pair<std::unordered_map<std::string, std::string>, std::string> ApiServer::createSuccessResponseWithHeaders(const nlohmann::json& data) {
      |                                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:171:14: note: candidate is: ‘dbservice::api::ApiServer::Response dbservice::api::ApiServer::createSuccessResponseWithHeaders(const nlohmann::json_abi_v3_11_3::json&)’
  171 |     Response createSuccessResponseWithHeaders(const nlohmann::json& data);
      |              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1113:70: error: no declaration matches ‘std::pair<std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> >, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::createErrorResponse(int, const std::string&, const std::string&)’
 1113 | std::pair<std::unordered_map<std::string, std::string>, std::string> ApiServer::createErrorResponse(
      |                                                                      ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:180:14: note: candidate is: ‘dbservice::api::ApiServer::Response dbservice::api::ApiServer::createErrorResponse(int, const std::string&, const std::string&)’
  180 |     Response createErrorResponse(
      |              ^~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1135:46: error: no declaration matches ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(const std::string&)’
 1135 | std::unordered_map<std::string, std::string> ApiServer::parseQueryParams(const std::string& queryString) {
      |                                              ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:188:50: note: candidate is: ‘std::unordered_map<std::__cxx11::basic_string<char>, std::__cxx11::basic_string<char> > dbservice::api::ApiServer::parseQueryParams(std::string_view)’
  188 |     std::unordered_map<std::string, std::string> parseQueryParams(std::string_view queryString);
      |                                                  ^~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
/home/<USER>/database-service-build/src/api/api_server.cpp:1162:13: error: no declaration matches ‘std::string dbservice::api::ApiServer::urlDecode(const std::string&)’
 1162 | std::string ApiServer::urlDecode(const std::string& input) {
      |             ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:195:17: note: candidate is: ‘std::string dbservice::api::ApiServer::urlDecode(std::string_view)’
  195 |     std::string urlDecode(std::string_view input);
      |                 ^~~~~~~~~
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:53:7: note: ‘class dbservice::api::ApiServer’ defined here
   53 | class ApiServer {
      |       ^~~~~~~~~
[3/15] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
ninja: build stopped: subcommand failed.
