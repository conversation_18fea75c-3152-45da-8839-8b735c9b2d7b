# Database Service Deployment Guide

This document provides detailed instructions for deploying the Database Service using the deployment scripts.

## Prerequisites

Before deploying the Database Service, ensure you have the following:

1. **PowerShell 5.1 or later** installed on your local machine
2. **SSH client** installed and configured on your local machine
3. **SSH key** for authentication with the remote server
4. **Sudo access** on the remote server
5. **PostgreSQL** installed on the remote server
6. **Required dependencies** installed on the remote server:
   - CMake 3.10 or later
   - GCC 7.0 or later
   - libpq-dev
   - libssl-dev
   - libcurl4-openssl-dev

## Deployment Process

The deployment process consists of the following steps:

1. **Configuration**: Set up the deployment configuration
2. **Build**: Build the project on the remote server
3. **Installation**: Install the built project on the remote server
4. **Service Setup**: Configure the service on the remote server
5. **Database Setup**: Set up the database for the service
6. **Certificate Access**: Configure SSL certificate access

### 1. Configuration

The deployment scripts use JSON configuration files to store deployment settings. Configuration files are stored in the `config` directory with the naming convention `database-service-{environment}.json`.

To create or edit a configuration:

1. Run the deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select option 1 (Manage Deployment Configurations) from the main menu
3. Select option 1 (Create New Configuration) or option 2 (Edit Existing Configuration)
4. Follow the prompts to configure the deployment settings

### 2. Build

The build process compiles the source code on the remote server. To build the project:

1. Run the deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select option 8 (Build Project) from the main menu
3. The script will:
   - Connect to the remote server via SSH
   - Create a build directory on the remote server
   - Copy the source code to the build directory
   - Run CMake to configure the build
   - Run Make to build the project
   - Copy the built files to the installation directory

### 3. Installation

The installation process is handled as part of the build process. The built files are copied to the installation directory specified in the configuration.

### 4. Service Setup

To set up the service on the remote server:

1. Run the deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select option 9 (Setup Service) from the main menu
3. The script will:
   - Create a systemd service file on the remote server
   - Configure the service to start on boot
   - Start the service

### 5. Database Setup

To set up the database for the service:

1. Run the deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select option 10 (Setup Database) from the main menu
3. The script will:
   - Connect to the PostgreSQL server
   - Create the database if it doesn't exist
   - Create the database user if it doesn't exist
   - Apply the database schema

### 6. Certificate Access

To configure SSL certificate access:

1. Run the deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select option 11 (Setup Certificate Access) from the main menu
3. The script will:
   - Configure the service user to access the SSL certificates
   - Set up the necessary permissions

## Troubleshooting

### Common Issues

1. **SSH Connection Failures**
   - Ensure the SSH key is correctly configured in the deployment configuration
   - Verify that the SSH key is accessible on your local machine
   - Check that the SSH server is running on the remote server
   - Verify that the SSH user has sudo access on the remote server

2. **Build Failures**
   - Ensure all required dependencies are installed on the remote server
   - Check the build logs for specific error messages
   - Verify that the source code is correct and can be built

3. **Service Startup Failures**
   - Check the service logs on the remote server
   - Verify that the service configuration is correct
   - Ensure the service user has the necessary permissions

4. **Database Connection Failures**
   - Verify that PostgreSQL is running on the remote server
   - Check the database connection settings in the configuration
   - Ensure the database user has the necessary permissions

### Logs

Logs are stored in the `logs` directory with timestamped filenames. Check the logs for detailed error messages and debugging information.

## Advanced Usage

### Manual Deployment

If you need to deploy the service manually, you can use the individual scripts in the `Modules` directory:

1. **Build-Project.ps1**: Builds the project on the remote server
2. **Setup-Service.ps1**: Sets up the service on the remote server
3. **Setup-Database.ps1**: Sets up the database for the service
4. **Setup-CertificateAccess.ps1**: Configures SSL certificate access

### Custom Configuration

You can create custom configuration files for different environments by creating new JSON files in the `config` directory with the naming convention `database-service-{environment}.json`.

### Backup and Restore

The deployment scripts include functionality for backing up and restoring configurations:

1. Run the deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select option 2 (Manage Configuration Backups) from the main menu
3. Select option 1 (Create Backup) or option 2 (Restore from Backup)
4. Follow the prompts to backup or restore the configuration

## Reference

### Configuration File Structure

```json
{
  "version": {
    "number": 1,
    "created": "2025-04-14 13:30:00",
    "updated": "2025-04-14 13:30:00"
  },
  "project": {
    "name": "database-service",
    "description": "Database Service for Project Tracker",
    "local_source_dir": "D:\\Augment\\project-tracker\\database-service",
    "remote_install_dir": "/opt/database-service",
    "remote_build_dir": "/home/<USER>/database-service-build"
  },
  "ssh": {
    "host": "git.chcit.org",
    "port": 22,
    "username": "btaylor-admin",
    "local_key_path": "%USERPROFILE%\\.ssh\\id_rsa"
  },
  "service": {
    "name": "database-service",
    "description": "Database Service for Project Tracker",
    "user": "dbservice",
    "group": "dbservice"
  },
  "database": {
    "user": "dbservice",
    "password": "password123",
    "host": "localhost",
    "port": 5432,
    "name": "database_service"
  }
}
```

### Command Line Options

The deployment script supports the following command line options:

```powershell
.\deploy-database-service-modular.ps1 [-Environment <String>] [-ConfigFile <String>] [-LogLevel <String>]
```

- **-Environment**: The deployment environment (development, testing, production)
- **-ConfigFile**: The path to a specific configuration file
- **-LogLevel**: The log level (Debug, Info, Warning, Error, Success)

### Environment Variables

The deployment scripts support the following environment variables:

- **DATABASE_SERVICE_CONFIG_DIR**: The directory containing configuration files
- **DATABASE_SERVICE_LOG_DIR**: The directory for log files
- **DATABASE_SERVICE_ENV**: The deployment environment
- **DATABASE_SERVICE_SSH_KEY**: The path to the SSH key file
- **DATABASE_SERVICE_DB_PASSWORD**: The database password

## Support

For support with the deployment scripts, contact the Database Service Deployment Team.
