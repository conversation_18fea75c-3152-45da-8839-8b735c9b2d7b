#include <gtest/gtest.h>
#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include <memory>
#include <thread>
#include <chrono>
#include <string>
#include <nlohmann/json.hpp>

namespace dbservice::api {

class ApiServerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test connection manager
        connectionString_ = "host=localhost port=5432 dbname=test_db user=test_user password=test_pass";
        sslConfig_.enabled = false;
        connectionManager_ = std::make_shared<core::ConnectionManager>(connectionString_, 5, sslConfig_);
        
        // Create security manager
        securityManager_ = std::make_shared<security::SecurityManager>(connectionManager_);
        securityManager_->setJwtSecret("test-jwt-secret");
        
        // Use a test port
        testPort_ = 18080;
    }

    void TearDown() override {
        if (apiServer_) {
            apiServer_->stop();
        }
        
        if (connectionManager_) {
            connectionManager_->shutdown();
        }
    }

    std::string connectionString_;
    core::SSLConfig sslConfig_;
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;
    std::unique_ptr<ApiServer> apiServer_;
    unsigned short testPort_;
};

// API Server Construction Tests
TEST_F(ApiServerTest, ConstructorTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    ASSERT_NE(apiServer_, nullptr);
    EXPECT_EQ(apiServer_->getPort(), testPort_);
    EXPECT_FALSE(apiServer_->isRunning());
}

TEST_F(ApiServerTest, StartStopTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    // Start server
    EXPECT_NO_THROW(apiServer_->start());
    
    // Give server time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Check if running
    EXPECT_TRUE(apiServer_->isRunning());
    
    // Stop server
    EXPECT_NO_THROW(apiServer_->stop());
    EXPECT_FALSE(apiServer_->isRunning());
}

TEST_F(ApiServerTest, MultipleStartTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    // Start server
    apiServer_->start();
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Try to start again (should handle gracefully)
    EXPECT_NO_THROW(apiServer_->start());
    
    // Should still be running
    EXPECT_TRUE(apiServer_->isRunning());
}

TEST_F(ApiServerTest, StopWithoutStartTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    // Stop without starting (should handle gracefully)
    EXPECT_NO_THROW(apiServer_->stop());
    EXPECT_FALSE(apiServer_->isRunning());
}

// CORS Configuration Tests
TEST_F(ApiServerTest, CorsConfigurationTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    CorsConfig corsConfig;
    corsConfig.enabled = true;
    corsConfig.allowedOrigins = {"http://localhost:3000", "https://example.com"};
    corsConfig.allowedMethods = {"GET", "POST", "PUT", "DELETE"};
    corsConfig.allowedHeaders = {"Content-Type", "Authorization"};
    corsConfig.allowCredentials = true;
    corsConfig.maxAge = 3600;
    
    EXPECT_NO_THROW(apiServer_->configureCors(corsConfig));
}

TEST_F(ApiServerTest, DisabledCorsTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    CorsConfig corsConfig;
    corsConfig.enabled = false;
    
    EXPECT_NO_THROW(apiServer_->configureCors(corsConfig));
}

// SSL Configuration Tests
TEST_F(ApiServerTest, SSLConfigurationTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    SSLConfig sslConfig;
    sslConfig.enabled = true;
    sslConfig.certPath = "/path/to/cert.pem";
    sslConfig.keyPath = "/path/to/key.pem";
    
    EXPECT_NO_THROW(apiServer_->configureSSL(sslConfig));
}

TEST_F(ApiServerTest, DisabledSSLTest) {
    apiServer_ = std::make_unique<ApiServer>(testPort_, connectionManager_, securityManager_);
    
    SSLConfig sslConfig;
    sslConfig.enabled = false;
    
    EXPECT_NO_THROW(apiServer_->configureSSL(sslConfig));
}

// Port Tests
TEST_F(ApiServerTest, PortConfigurationTest) {
    unsigned short customPort = 19090;
    apiServer_ = std::make_unique<ApiServer>(customPort, connectionManager_, securityManager_);
    
    EXPECT_EQ(apiServer_->getPort(), customPort);
}

TEST_F(ApiServerTest, StandardPortTest) {
    unsigned short standardPort = 8080;
    apiServer_ = std::make_unique<ApiServer>(standardPort, connectionManager_, securityManager_);
    
    EXPECT_EQ(apiServer_->getPort(), standardPort);
}

// Configuration Structure Tests
TEST(CorsConfigTest, DefaultCorsConfigTest) {
    CorsConfig config;
    
    EXPECT_FALSE(config.enabled);
    EXPECT_TRUE(config.allowedOrigins.empty());
    EXPECT_TRUE(config.allowedMethods.empty());
    EXPECT_TRUE(config.allowedHeaders.empty());
    EXPECT_FALSE(config.allowCredentials);
    EXPECT_EQ(config.maxAge, 86400); // Default 24 hours
}

TEST(CorsConfigTest, CorsConfigWithValuesTest) {
    CorsConfig config;
    config.enabled = true;
    config.allowedOrigins = {"*"};
    config.allowedMethods = {"GET", "POST"};
    config.allowedHeaders = {"Content-Type"};
    config.allowCredentials = true;
    config.maxAge = 3600;
    
    EXPECT_TRUE(config.enabled);
    EXPECT_EQ(config.allowedOrigins.size(), 1);
    EXPECT_EQ(config.allowedMethods.size(), 2);
    EXPECT_EQ(config.allowedHeaders.size(), 1);
    EXPECT_TRUE(config.allowCredentials);
    EXPECT_EQ(config.maxAge, 3600);
}

TEST(SSLConfigTest, DefaultSSLConfigTest) {
    SSLConfig config;
    
    EXPECT_FALSE(config.enabled);
    EXPECT_TRUE(config.certPath.empty());
    EXPECT_TRUE(config.keyPath.empty());
}

TEST(SSLConfigTest, SSLConfigWithValuesTest) {
    SSLConfig config;
    config.enabled = true;
    config.certPath = "/etc/ssl/cert.pem";
    config.keyPath = "/etc/ssl/key.pem";
    
    EXPECT_TRUE(config.enabled);
    EXPECT_EQ(config.certPath, "/etc/ssl/cert.pem");
    EXPECT_EQ(config.keyPath, "/etc/ssl/key.pem");
}

// JSON Response Tests
TEST(JSONResponseTest, ValidJSONTest) {
    nlohmann::json testJson = {
        {"success", true},
        {"message", "Test message"},
        {"data", {
            {"id", 1},
            {"name", "Test"}
        }}
    };
    
    EXPECT_TRUE(testJson["success"]);
    EXPECT_EQ(testJson["message"], "Test message");
    EXPECT_EQ(testJson["data"]["id"], 1);
    EXPECT_EQ(testJson["data"]["name"], "Test");
}

TEST(JSONResponseTest, ErrorResponseTest) {
    nlohmann::json errorJson = {
        {"success", false},
        {"error", {
            {"code", 400},
            {"message", "Bad Request"}
        }}
    };
    
    EXPECT_FALSE(errorJson["success"]);
    EXPECT_EQ(errorJson["error"]["code"], 400);
    EXPECT_EQ(errorJson["error"]["message"], "Bad Request");
}

// HTTP Method Tests
TEST(HTTPMethodTest, SupportedMethodsTest) {
    std::vector<std::string> supportedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
    
    for (const auto& method : supportedMethods) {
        EXPECT_FALSE(method.empty());
        EXPECT_TRUE(method == "GET" || method == "POST" || method == "PUT" || 
                   method == "DELETE" || method == "OPTIONS");
    }
}

// URL Parsing Tests
TEST(URLParsingTest, BasicURLTest) {
    std::string url = "/api/health";
    EXPECT_TRUE(url[0] == '/');
    EXPECT_TRUE(url.find("api") != std::string::npos);
}

TEST(URLParsingTest, URLWithParametersTest) {
    std::string url = "/api/users?id=123&name=test";
    
    size_t queryPos = url.find('?');
    EXPECT_NE(queryPos, std::string::npos);
    
    std::string path = url.substr(0, queryPos);
    std::string query = url.substr(queryPos + 1);
    
    EXPECT_EQ(path, "/api/users");
    EXPECT_TRUE(query.find("id=123") != std::string::npos);
    EXPECT_TRUE(query.find("name=test") != std::string::npos);
}

// Authentication Header Tests
TEST(AuthHeaderTest, BearerTokenTest) {
    std::string authHeader = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
    
    EXPECT_TRUE(authHeader.substr(0, 7) == "Bearer ");

    std::string token = authHeader.substr(7);
    EXPECT_FALSE(token.empty());
    EXPECT_EQ(token, "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9");
}

TEST(AuthHeaderTest, InvalidAuthHeaderTest) {
    std::string invalidHeader1 = "Basic dXNlcjpwYXNz";
    std::string invalidHeader2 = "Bearer";
    std::string invalidHeader3 = "";

    EXPECT_FALSE(invalidHeader1.substr(0, 7) == "Bearer ");
    EXPECT_FALSE(invalidHeader2.length() > 7);
    EXPECT_TRUE(invalidHeader3.empty());
}

// Content Type Tests
TEST(ContentTypeTest, JSONContentTypeTest) {
    std::string jsonContentType = "application/json";
    std::string jsonWithCharset = "application/json; charset=utf-8";
    
    EXPECT_TRUE(jsonContentType.find("application/json") != std::string::npos);
    EXPECT_TRUE(jsonWithCharset.find("application/json") != std::string::npos);
}

TEST(ContentTypeTest, FormContentTypeTest) {
    std::string formContentType = "application/x-www-form-urlencoded";
    
    EXPECT_TRUE(formContentType.find("application/x-www-form-urlencoded") != std::string::npos);
}

} // namespace dbservice::api
