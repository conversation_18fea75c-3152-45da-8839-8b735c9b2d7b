{"tiers": {"memory": {"enabled": true, "max_entries": 1000, "max_age_minutes": 60}, "database": {"enabled": true, "connection_string": "postgresql://git_logger:change_me_please@localhost:5432/git_logging", "structured_only": true, "batch_size": 100, "flush_interval_seconds": 30}, "file": {"enabled": true, "base_directory": "/var/log/git-dashboard/logs", "compression": true, "compression_age_days": 7, "max_file_size_mb": 10, "max_files_per_source": 10}}, "retention": {"system": 30, "security": 90, "web": 15, "database": 15, "mail": 30, "git": 60, "dashboard": 30, "other": 15}, "sources": {"system": {"enabled": true, "path": "/var/log/syslog", "parser": "syslog", "storage_tiers": ["memory", "database", "file"]}, "security": {"enabled": true, "path": "/var/log/auth.log", "parser": "syslog", "storage_tiers": ["memory", "database", "file"]}, "web": {"enabled": true, "path": "/var/log/nginx/error.log", "parser": "nginx", "storage_tiers": ["memory", "database"]}, "git": {"enabled": true, "path": "/var/log/git", "parser": "git", "storage_tiers": ["memory", "database", "file"]}, "dashboard": {"enabled": true, "path": "/var/log/git-dashboard/git-dashboard.log", "parser": "dashboard", "storage_tiers": ["memory", "database"]}}}