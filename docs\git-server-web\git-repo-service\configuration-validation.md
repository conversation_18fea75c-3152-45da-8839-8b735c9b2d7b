# Git Repository Service Configuration Validation

## Overview

The Git Repository Service includes a robust configuration validation system that ensures all configuration parameters are valid and appropriate for the service's operation. This document describes the configuration validation features, how they work, and how to use them effectively.

## Configuration Sources

The Git Repository Service can load configuration from two sources:

1. **Configuration File**: JSON format configuration file
2. **Environment Variables**: System environment variables

When both sources are available, environment variables take precedence over the configuration file.

## Command-Line Usage

The service can be started with an optional configuration file path:

```bash
./git-repo-service /path/to/config.json
```

If no configuration file is specified, the service will look for environment variables.

## Configuration Parameters

### Required Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `GIT_REPO_PATH` | String | `/home/<USER>
| `SERVER_PORT` | Integer | `8080` | Port on which the service listens |
| `LOG_LEVEL` | String | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL) |
| `LOG_FILE` | String | `/var/log/git-repo-service.log` | Path to the log file |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `DEBUG` | Boolean | `false` | Enable debug mode |
| `MAX_THREADS` | Integer | `4` | Maximum number of worker threads |
| `CACHE_SIZE` | Integer | `1000` | Maximum number of cached items |
| `CACHE_TTL` | Integer | `3600` | Cache time-to-live in seconds |

## Validation Features

The configuration validation system performs the following checks:

### Path Validation

- Checks if `GIT_REPO_PATH` exists and is readable
- Creates the directory if it doesn't exist
- Verifies that the service has appropriate permissions

### Port Validation

- Ensures `SERVER_PORT` is within the valid range (1-65535)
- Checks if the port is available (not already in use)
- Falls back to the default port if the specified port is invalid

### Log Level Validation

- Validates `LOG_LEVEL` against the list of valid levels
- Case-insensitive comparison
- Falls back to INFO if an invalid level is specified

### Log File Validation

- Checks if the log directory exists and creates it if necessary
- Verifies that the service has write permissions to the log file
- Tests file creation to ensure the path is valid

## Configuration File Format

The configuration file should be in JSON format:

```json
{
  "GIT_REPO_PATH": "/home/<USER>/repositories",
  "SERVER_PORT": 8080,
  "LOG_LEVEL": "INFO",
  "LOG_FILE": "/var/log/git-repo-service.log",
  "DEBUG": false,
  "MAX_THREADS": 4,
  "CACHE_SIZE": 1000,
  "CACHE_TTL": 3600
}
```

## Environment Variables

The same configuration can be provided through environment variables:

```bash
export GIT_REPO_PATH=/home/<USER>/repositories
export SERVER_PORT=8080
export LOG_LEVEL=INFO
export LOG_FILE=/var/log/git-repo-service.log
export DEBUG=false
export MAX_THREADS=4
export CACHE_SIZE=1000
export CACHE_TTL=3600
```

## Validation Behavior

When the service starts, it performs the following steps:

1. Loads configuration from the specified file (if provided)
2. Loads configuration from environment variables
3. Validates all configuration parameters
4. Reports any validation issues
5. Applies default values for invalid parameters
6. Continues execution with the validated configuration

The service will attempt to continue running even if some configuration parameters are invalid, falling back to default values where possible.

## Debugging Configuration

To help diagnose configuration issues, you can enable debug mode:

```bash
export DEBUG=true
```

With debug mode enabled, the service will output the complete configuration at startup, showing all parameters and their values.

## Example Validation Output

```
Loading configuration from file: /etc/git-repo-service/config.json
Warning: GIT_REPO_PATH does not exist: /home/<USER>/repositories
Created directory: /home/<USER>/repositories
Invalid SERVER_PORT: 99999, must be between 1 and 65535
Setting SERVER_PORT to default: 8080
Invalid LOG_LEVEL: TRACE, must be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL
Setting LOG_LEVEL to default: INFO
Warning: Log directory does not exist: /var/log/git-repo-service
Created directory: /var/log/git-repo-service
Configuration validation found issues, but will attempt to continue
```

## Best Practices

1. **Use a Configuration File**: For production deployments, use a configuration file rather than environment variables for better maintainability.

2. **Set Appropriate Permissions**: Ensure the service has appropriate permissions to read/write to the specified paths.

3. **Validate Before Deployment**: Test your configuration before deploying to production to catch any issues early.

4. **Monitor Validation Output**: Check the service logs for validation warnings and errors during startup.

5. **Use Absolute Paths**: Always use absolute paths in configuration to avoid ambiguity.

## Troubleshooting

### Common Issues

1. **Service fails to start**: Check if the specified paths exist and have appropriate permissions.

2. **Service ignores configuration file**: Ensure the file is in valid JSON format and the service has read permissions.

3. **Service uses default values**: Check the logs for validation errors that might cause fallback to defaults.

4. **Permission denied errors**: Ensure the service has appropriate permissions to access the specified paths.

## Conclusion

The configuration validation system in the Git Repository Service helps prevent runtime errors by validating configuration parameters before they are used. By providing sensible defaults and clear error messages, it makes the service more robust and easier to configure correctly.
