# Common.psm1
# Common functions for the deployment scripts

# Import the centralized Configuration module
Import-Module "$PSScriptRoot\Modules\Configuration.psm1" -DisableNameChecking

# Import the Logger module for consistent logging
Import-Module "$PSScriptRoot\Modules\Logger\Logger.psm1" -Force

#region UI Helpers
# Enable UI Mode for consistent UI display
function Enable-UIMode {
    $script:UIMode = $true
}

# Disable UI Mode when returning to normal operations
function Disable-UIMode {
    $script:UIMode = $false
}

# Wait for user to press a key to continue
function Wait-ForUser {
    Write-Host "Press Enter to continue..." -ForegroundColor Yellow
    Read-Host | Out-Null
}

<#
.SYNOPSIS
    Shows the main menu for the database service deployment.

.DESCRIPTION
    Displays the main menu with options for deployment, configuration, and utilities.
#>
function Show-MainMenu {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    
    # Get configuration
    $config = Get-Configuration
    
    Write-Log -Message "========================================================" -Color Blue
    Write-Log -Message "               Database Service Deployment               " -Color Blue
    Write-Log -Message "========================================================" -Color Blue
    Write-Log -Message " " -Color White
    
    if ($null -ne $config) {
        $env = $script:Environment
        if ($null -ne $env:DeploymentEnvironment) {
            $env = $env:DeploymentEnvironment
        }
        
        Write-Log -Message "Current Configuration:" -Color Cyan
        Write-Log -Message "Project: $($config.project.name)" -Color White
        Write-Log -Message "Environment: $env" -Color White
        Write-Log -Message "Server: $($config.ssh.host)" -Color White
        Write-Log -Message "Local Source Directory: $($config.project.local_source_dir)" -Color White
        Write-Log -Message "Remote Install Directory: $($config.project.remote_install_dir)" -Color White
        Write-Log -Message "Remote Build Directory: $($config.project.remote_build_dir)" -Color White
        
        if ($null -ne $config.version) {
            Write-Log -Message "Configuration Version: v$($config.version.number)" -Color White
            Write-Log -Message "Last Updated: $($config.version.updated)" -Color White
        }
    } else {
        Write-Log -Message "No configuration loaded." -Color Yellow
    }
    
    Write-Log -Message "`nMenu Options:" -Color Yellow
    Write-Log -Message "[1] Set Environment" -Color White
    Write-Log -Message "[2] Edit Configuration" -Color White
    Write-Log -Message "[3] Deploy Database Service" -Color White
    Write-Log -Message "[4] Configuration Backups" -Color White
    Write-Log -Message "[5] Exit" -Color White
    
    $choice = Read-Host "`nSelect an option (1-5)"
    
    # Disable UI Mode after menu display
    Disable-UIMode
    
    switch ($choice) {
        "1" { 
            if (Get-Command Set-Environment -ErrorAction SilentlyContinue) {
                Set-Environment
            } else {
                Write-Log -Message "Set-Environment command not found." -Color Red
                Wait-ForUser
                Show-MainMenu
            }
        }
        "2" { 
            if (Get-Command Show-DeploymentConfigurations -ErrorAction SilentlyContinue) {
                Show-DeploymentConfigurations
            } else {
                Write-Log -Message "Configuration manager not found." -Color Red
                Wait-ForUser
                Show-MainMenu
            }
        }
        "3" { 
            if (Get-Command Deploy-DatabaseService -ErrorAction SilentlyContinue) {
                Deploy-DatabaseService
            } else {
                Write-Log -Message "Deployment function not found." -Color Red
                Wait-ForUser
                Show-MainMenu
            }
        }
        "4" {
            if (Get-Command Manage-ConfigurationBackups -ErrorAction SilentlyContinue) {
                Manage-ConfigurationBackups
            } else {
                Write-Log -Message "Backup manager not found." -Color Red
                Wait-ForUser
                Show-MainMenu
            }
        }
        "5" { 
            Clear-Host 
            Write-Log -Message "Exiting deployment script." -Color Green
            return
        }
        default {
            Write-Log -Message "Invalid option. Please try again." -Color Red
            Start-Sleep -Seconds 1
            Show-MainMenu
        }
    }
}

# Import requirements from a JSON file
function Import-Requirements {
    param(
        [string]$RequirementsFile = "requirements.json",
        [switch]$Silent
    )
    
    try {
        $requirementsPath = Join-Path -Path $PSScriptRoot -ChildPath $RequirementsFile
        
        if (-not (Test-Path -Path $requirementsPath)) {
            if (-not $Silent) {
                Write-Log -Message "Requirements file not found at: $requirementsPath" -Color Red
            }
            return $null
        }
        
        $requirementsJson = Get-Content -Path $requirementsPath -Raw -ErrorAction Stop | ConvertFrom-Json -ErrorAction Stop
        return $requirementsJson
    } catch {
        if (-not $Silent) {
            Write-Log -Message "Error loading requirements: $_" -Color Red
        }
        return $null
    }
}
#endregion

#region Backup
<#
.SYNOPSIS
    Creates a backup of a configuration file.

.DESCRIPTION
    Creates a timestamped backup of a configuration file in the backups directory.

.PARAMETER ConfigFilePath
    The path to the configuration file to back up.

.EXAMPLE
    Backup-ConfigurationFile -ConfigFilePath "config\database-service-development.json"
#>
function Backup-ConfigurationFile {
    param (
        [Parameter(Mandatory=$true)]
        [string]$ConfigFilePath
    )
    
    try {
        # Create backups directory if it doesn't exist
        $backupsDir = Join-Path -Path $PSScriptRoot -ChildPath "config\backups"
        if (-not (Test-Path -Path $backupsDir)) {
            New-Item -Path $backupsDir -ItemType Directory -Force | Out-Null
        }
        
        # Get filename and create backup filename with timestamp
        $fileName = [System.IO.Path]::GetFileName($ConfigFilePath)
        $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
        $backupFileName = "$([System.IO.Path]::GetFileNameWithoutExtension($fileName))-$timestamp$([System.IO.Path]::GetExtension($fileName))"
        $backupFilePath = Join-Path -Path $backupsDir -ChildPath $backupFileName
        
        # Copy file to backup location
        Copy-Item -Path $ConfigFilePath -Destination $backupFilePath -Force
        Write-Log -Message "Created backup: $backupFilePath" -Color Green
        return $backupFilePath
    } catch {
        Write-Log -Message "Error creating backup: $_" -Color Red
        return $null
    }
}
#endregion

#region RemoteCommand
<#
.SYNOPSIS
    Executes a command on a remote server via SSH.

.DESCRIPTION
    Uses the SSH configuration from the global Config variable to execute a command on the remote server.

.PARAMETER Command
    The command to execute on the remote server.

.PARAMETER Silent
    If specified, suppresses logging of command execution.

.EXAMPLE
    $output = Invoke-RemoteCommand -Command "ls -la"
    if ($output) {
        Write-Log -Message "Command output: $output" -Color White
    }

.RETURNS
    String containing the command output, or $null if the command failed.
#>
function Invoke-RemoteCommand {
    [CmdletBinding()]
    [OutputType([string])]
    param (
        [Parameter(Mandatory=$true)]
        [string]$Command,

        [Parameter(Mandatory=$false)]
        [switch]$Silent
    )

    try {
        # Import the SSHManager module if not already imported
        if (-not (Get-Module -Name "SSHManager")) {
            Import-Module -Name "$PSScriptRoot\Modules\SSHManager.psm1" -Force -ErrorAction Stop
        }

        # Get SSH configuration
        $config = Get-Configuration
        if ($null -eq $config -or $null -eq $config.ssh) {
            Write-Log -Message "SSH configuration not found. Please configure SSH settings first." -Color Red
            return $null
        }

        $sshHost = $config.ssh.host
        $sshPort = $config.ssh.port
        $sshUser = $config.ssh.username
        $sshKeyPath = $config.ssh.local_key_path

        # Use the Invoke-SSHCommand function from the SSHManager module
        $result = Invoke-SSHCommand -Command $Command -HostName $sshHost -User $sshUser -Port $sshPort -KeyPath $sshKeyPath -Silent:$Silent -Component "RemoteCommand"

        if ($result.Success) {
            return $result.Output
        } else {
            return $null
        }
    } catch {
        if (-not $Silent) {
            Write-Log -Message "SSH command execution failed: $_" -Color Red
        }
        return $null
    }
}
#endregion

#region Configuration
<#
.SYNOPSIS
    Updates configuration paths to ensure they are correct.

.DESCRIPTION
    Checks and updates configuration paths to ensure they point to the correct locations.
    This is used to fix common configuration issues and ensure consistency.

.EXAMPLE
    Update-ConfigurationPaths

.RETURNS
    Boolean indicating success or failure.
#>
function Update-ConfigurationPaths {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    $config = Get-Configuration
    if ($null -eq $config -or $null -eq $config.project) {
        Write-Log -Message "Configuration not loaded or missing project section." -Color Red
        return $false
    }

    $modified = $false

    # Fix source directory if it's pointing to the wrong location
    if ($config.project.local_source_dir -like "*\config" -or
        $config.project.local_source_dir -like "*\deployment*") {
        $config.project.local_source_dir = "D:\Augment\project-tracker\database-service"
        Write-Log -Message "Updated source directory path." -Color Yellow
        $modified = $true
    }

    # Add deployment_scripts property if it doesn't exist
    if (-not (Get-Member -InputObject $config.project -Name "deployment_scripts" -MemberType Properties)) {
        $config.project | Add-Member -MemberType NoteProperty -Name "deployment_scripts" -Value "D:\Augment\project-tracker\database-service-deployment\deployment_scripts"
        Write-Log -Message "Added deployment scripts path." -Color Yellow
        $modified = $true
    }

    # Update server for database-service to git.chcit.org
    if ($config.project.name -eq "database-service" -and $config.ssh.host -eq "project-tracker.chcit.org") {
        $config.ssh.host = "git.chcit.org"
        Write-Log -Message "Updated server to git.chcit.org for database-service." -Color Yellow
        $modified = $true
    }

    # Save the configuration if it was modified
    if ($modified) {
        Save-Configuration
    }

    return $true
}
#endregion

# Script-level variables
$script:UIMode = $false
$script:LoggingEnabled = $true

# Export functions
Export-ModuleMember -Function Import-Requirements, Wait-ForUser, Show-MainMenu, Backup-ConfigurationFile, Invoke-RemoteCommand, Update-ConfigurationPaths, Enable-UIMode, Disable-UIMode
