
# API Reference

> **Note:** The project has been reorganized. The API implementation is now in the `/src/api` directory of the database-service-traditional project.

The Database Service exposes a RESTful API for database operations. This document provides a detailed reference for all API endpoints.

## Authentication

All API endpoints require authentication using a Bearer token. To obtain a token, use the authentication endpoint.

### Obtain Authentication Token

```
POST /api/auth
```

Request:
```json
{
  "username": "admin",
  "password": "password"
}
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_at": "2023-04-08T12:00:00Z"
}
```

Authentication tokens are valid for a limited time (default: 60 minutes). After expiration, a new token must be obtained.

## Database Operations

### Execute Query

Execute a read-only SQL query and return the results.

```
POST /api/query
```

Request:
```json
{
  "query": "SELECT * FROM users WHERE id = $1",
  "params": {
    "$1": "123"
  },
  "application": "my-app",
  "database": "my_app_db",
  "schema": "public"
}
```

Response:
```json
[
  {
    "id": "123",
    "username": "john",
    "email": "<EMAIL>"
  }
]
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `query` | string | SQL query to execute | Yes |
| `params` | object | Query parameters | No |
| `application` | string | Application name | Yes |
| `database` | string | Database name | Yes |
| `schema` | string | Database schema | No |

### Execute Statement

Execute a SQL statement that modifies data (INSERT, UPDATE, DELETE).

```
POST /api/execute
```

Request:
```json
{
  "query": "INSERT INTO users (username, email) VALUES ($1, $2)",
  "params": {
    "$1": "john",
    "$2": "<EMAIL>"
  },
  "application": "my-app",
  "database": "my_app_db",
  "schema": "public"
}
```

Response:
```json
{
  "affected_rows": 1
}
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `query` | string | SQL statement to execute | Yes |
| `params` | object | Statement parameters | No |
| `application` | string | Application name | Yes |
| `database` | string | Database name | Yes |
| `schema` | string | Database schema | No |

### Execute Transaction

Execute multiple SQL statements in a single transaction.

```
POST /api/transaction
```

Request:
```json
{
  "queries": [
    "INSERT INTO users (username, email) VALUES ('john', '<EMAIL>')",
    "INSERT INTO profiles (user_id, name) VALUES (LASTVAL(), 'John Doe')"
  ],
  "application": "my-app",
  "database": "my_app_db",
  "schema": "public"
}
```

Response:
```json
[
  {
    "affected_rows": 1
  },
  {
    "affected_rows": 1
  }
]
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `queries` | array | SQL statements to execute | Yes |
| `application` | string | Application name | Yes |
| `database` | string | Database name | Yes |
| `schema` | string | Database schema | No |

## Asynchronous Operations

The Database Service supports asynchronous operations for long-running queries. These endpoints return immediately with a task ID, which can be used to check the status of the operation.

### Execute Asynchronous Query

Execute a read-only SQL query asynchronously.

```
POST /api/async/query
```

Request:
```json
{
  "query": "SELECT * FROM large_table WHERE complex_condition = $1",
  "params": {
    "$1": "value"
  },
  "application": "my-app",
  "database": "my_app_db",
  "schema": "public"
}
```

Response:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "running",
  "created_at": "2023-04-08T12:00:00Z"
}
```

### Check Asynchronous Task Status

Check the status of an asynchronous task.

```
GET /api/async/status/{task_id}
```

Response:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "created_at": "2023-04-08T12:00:00Z",
  "completed_at": "2023-04-08T12:01:30Z",
  "result_available": true
}
```

### Get Asynchronous Task Result

Get the result of a completed asynchronous task.

```
GET /api/async/result/{task_id}
```

Response:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "created_at": "2023-04-08T12:00:00Z",
  "completed_at": "2023-04-08T12:01:30Z",
  "result": [
    {
      "id": 1,
      "name": "Example",
      "value": 123
    },
    // ... more results
  ]
}
```

## Database Management

### List Databases

List all databases managed by the Database Service.

```
GET /api/databases
```

Response:
```json
[
  {
    "name": "database_service",
    "description": "Central management database",
    "created_at": "2023-04-01T12:00:00Z"
  },
  {
    "name": "git_repo_db",
    "description": "Git repository database",
    "created_at": "2023-04-01T12:05:00Z"
  },
  {
    "name": "logging_db",
    "description": "Logging database",
    "created_at": "2023-04-01T12:10:00Z"
  }
]
```

### Create Database

Create a new database for an application.

```
POST /api/databases
```

Request:
```json
{
  "name": "new_app_db",
  "application": "new-app",
  "description": "New application database"
}
```

Response:
```json
{
  "name": "new_app_db",
  "application": "new-app",
  "description": "New application database",
  "created_at": "2023-04-08T12:00:00Z"
}
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `name` | string | Database name | Yes |
| `application` | string | Application name | Yes |
| `description` | string | Database description | No |

### List Schemas

List all schemas in a database.

```
GET /api/databases/{database}/schemas
```

Response:
```json
[
  {
    "name": "public",
    "description": "Default schema",
    "created_at": "2023-04-01T12:00:00Z"
  },
  {
    "name": "metadata",
    "description": "Metadata schema",
    "created_at": "2023-04-01T12:05:00Z"
  }
]
```

### Create Schema

Create a new schema in a database.

```
POST /api/databases/{database}/schemas
```

Request:
```json
{
  "name": "new_schema",
  "description": "New schema"
}
```

Response:
```json
{
  "name": "new_schema",
  "description": "New schema",
  "created_at": "2023-04-08T12:00:00Z"
}
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `name` | string | Schema name | Yes |
| `description` | string | Schema description | No |

## Schema Management

### Get Database Schema

Get the database schema for all tables in a database.

```
GET /api/schema?database={database}
```

Response:
```json
[
  {
    "name": "users",
    "schema": "public",
    "columns": [
      {
        "name": "id",
        "type": "integer",
        "nullable": false,
        "default": "nextval('users_id_seq'::regclass)"
      },
      {
        "name": "username",
        "type": "character varying",
        "nullable": false
      },
      {
        "name": "email",
        "type": "character varying",
        "nullable": false
      }
    ]
  }
]
```

### Apply Schema Migration

Apply a schema migration to a database.

```
POST /api/schema/migrate
```

Request:
```json
{
  "application": "my-app",
  "database": "my_app_db",
  "target_version": "1.1.0"
}
```

Response:
```json
{
  "success": true,
  "message": "Schema migrated from 1.0.0 to 1.1.0"
}
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `application` | string | Application name | Yes |
| `database` | string | Database name | Yes |
| `target_version` | string | Target schema version | Yes |

## Status and Monitoring

### Get Database Status

Get the current status of a database.

```
GET /api/status?database={database}
```

Response:
```json
{
  "status": "online",
  "version": "PostgreSQL 17.0",
  "uptime": 3600,
  "connections": {
    "active": 5,
    "idle": 5,
    "max": 10
  }
}
```

### Get Service Health

Get the health status of the Database Service.

```
GET /api/health
```

Response:
```json
{
  "status": "healthy",
  "components": {
    "database_service": "healthy",
    "git_repo_db": "healthy",
    "logging_db": "healthy",
    "api": "healthy"
  },
  "uptime": 3600
}
```

## JSON Operations

### Store JSON Document

Store a JSON document in a table.

```
POST /api/json
```

Request:
```json
{
  "table": "repository_metadata",
  "document": {
    "repository_id": 123,
    "language_stats": {
      "C++": 75,
      "Python": 20,
      "JavaScript": 5
    },
    "tags": ["backend", "api", "core"]
  },
  "application": "git-repo-service",
  "database": "git_repo_db",
  "schema": "metadata"
}
```

Response:
```json
{
  "id": 456,
  "created_at": "2023-04-08T12:00:00Z"
}
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `table` | string | Table name | Yes |
| `document` | object | JSON document to store | Yes |
| `application` | string | Application name | Yes |
| `database` | string | Database name | Yes |
| `schema` | string | Database schema | No |

### Query JSON Documents

Query JSON documents in a table.

```
POST /api/json/query
```

Request:
```json
{
  "table": "repository_metadata",
  "query": {
    "language_stats.C++": {"$gt": 50},
    "tags": {"$contains": "api"}
  },
  "application": "git-repo-service",
  "database": "git_repo_db",
  "schema": "metadata"
}
```

Response:
```json
[
  {
    "id": 456,
    "repository_id": 123,
    "document": {
      "language_stats": {
        "C++": 75,
        "Python": 20,
        "JavaScript": 5
      },
      "tags": ["backend", "api", "core"]
    },
    "created_at": "2023-04-08T12:00:00Z"
  }
]
```

#### Parameters

| Parameter | Type | Description | Required |
| --------- | ---- | ----------- | -------- |
| `table` | string | Table name | Yes |
| `query` | object | JSON query | Yes |
| `application` | string | Application name | Yes |
| `database` | string | Database name | Yes |
| `schema` | string | Database schema | No |

## Error Handling

All API endpoints return standard HTTP status codes to indicate success or failure:

- `200 OK`: The request was successful
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required
- `403 Forbidden`: The authenticated user does not have permission
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An error occurred on the server

Error responses include a JSON object with an error message:

```json
{
  "error": "Invalid query: syntax error at or near \"SELECT\""
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse. Rate limits are applied per IP address and per authenticated user.

Rate limit headers are included in all responses:

- `X-RateLimit-Limit`: The maximum number of requests allowed per hour
- `X-RateLimit-Remaining`: The number of requests remaining in the current hour
- `X-RateLimit-Reset`: The time at which the rate limit will reset (Unix timestamp)

If you exceed the rate limit, you will receive a `429 Too Many Requests` response.

## CORS

The API supports Cross-Origin Resource Sharing (CORS) for browser-based clients. The following headers are included in all responses:

- `Access-Control-Allow-Origin`: The allowed origin (configurable)
- `Access-Control-Allow-Methods`: The allowed HTTP methods
- `Access-Control-Allow-Headers`: The allowed HTTP headers
- `Access-Control-Max-Age`: The maximum age of the CORS preflight request

## Versioning

The API is versioned using the `Accept` header:

```
Accept: application/json; version=1.0
```

If no version is specified, the latest version is used.


