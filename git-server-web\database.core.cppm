export module database.core;

// Standard library imports
import <string>;
import <memory>;
import <vector>;
import <mutex>;
import <condition_variable>;
import <unordered_map>;
import <functional>;
import <optional>;
import <coroutine>;
import <future>;
import <stdexcept>;
import <iostream>;
import <thread>;
import <atomic>;

// Forward declarations for PostgreSQL
export namespace pqxx {
    class connection;
    class result;
    class transaction_base;
    class work;
}

// Task type for coroutines
export namespace dbservice::core {

    // Result type for database operations
    template<typename T>
    struct Result {
        std::optional<T> value;
        std::optional<std::string> error;

        bool success() const { return value.has_value(); }
        bool failure() const { return error.has_value(); }

        // Convenience methods
        T& operator*() { return *value; }
        const T& operator*() const { return *value; }
        T* operator->() { return &(*value); }
        const T* operator->() const { return &(*value); }
    };

    // Specialized result for void returns
    template<>
    struct Result<void> {
        bool success = true;
        std::optional<std::string> error;

        bool succeeded() const { return success; }
        bool failed() const { return error.has_value(); }
    };

    // Task type for coroutines
    template<typename T>
    class Task {
    public:
        struct promise_type {
            Result<T> result;
            std::exception_ptr exception_ptr = nullptr;
            std::coroutine_handle<> continuation = nullptr;
            std::atomic<bool> is_ready{false};

            Task get_return_object() {
                return Task(std::coroutine_handle<promise_type>::from_promise(*this));
            }

            std::suspend_never initial_suspend() { return {}; }
            
            auto final_suspend() noexcept {
                struct FinalAwaiter {
                    bool await_ready() const noexcept { return false; }
                    
                    void await_suspend(std::coroutine_handle<promise_type> h) noexcept {
                        // Mark as ready
                        h.promise().is_ready.store(true, std::memory_order_release);
                        
                        // Resume continuation if it exists
                        if (h.promise().continuation) {
                            h.promise().continuation.resume();
                        }
                    }
                    
                    void await_resume() noexcept {}
                };
                
                return FinalAwaiter{};
            }

            void return_value(Result<T> value) { 
                result = std::move(value); 
            }

            void unhandled_exception() {
                try {
                    std::rethrow_exception(std::current_exception());
                } catch (const std::exception& e) {
                    result.error = e.what();
                } catch (...) {
                    result.error = "Unknown exception";
                }
                exception_ptr = std::current_exception();
            }
        };

        Task() : handle(nullptr) {}
        Task(std::coroutine_handle<promise_type> h) : handle(h) {}
        
        ~Task() {
            if (handle && !handle.done()) {
                handle.destroy();
            }
        }
        
        // Move semantics
        Task(Task&& other) noexcept : handle(other.handle) {
            other.handle = nullptr;
        }
        
        Task& operator=(Task&& other) noexcept {
            if (this != &other) {
                if (handle && !handle.done()) {
                    handle.destroy();
                }
                handle = other.handle;
                other.handle = nullptr;
            }
            return *this;
        }
        
        // No copy
        Task(const Task&) = delete;
        Task& operator=(const Task&) = delete;

        bool await_ready() const { 
            return !handle || handle.done() || 
                   handle.promise().is_ready.load(std::memory_order_acquire); 
        }
        
        void await_suspend(std::coroutine_handle<> awaiting) {
            // Store the awaiting coroutine to resume it later
            handle.promise().continuation = awaiting;
        }
        
        Result<T> await_resume() {
            if (!handle) {
                return Result<T>{std::nullopt, "Task not initialized"};
            }
            
            if (handle.promise().exception_ptr) {
                std::rethrow_exception(handle.promise().exception_ptr);
            }
            
            return handle.promise().result;
        }
        
        // Synchronously wait for the task to complete
        Result<T> get_result() {
            if (!handle) {
                return Result<T>{std::nullopt, "Task not initialized"};
            }
            
            // Wait until the coroutine is done
            while (!handle.done() && 
                   !handle.promise().is_ready.load(std::memory_order_acquire)) {
                std::this_thread::yield();
            }
            
            if (handle.promise().exception_ptr) {
                std::rethrow_exception(handle.promise().exception_ptr);
            }
            
            return handle.promise().result;
        }

    private:
        std::coroutine_handle<promise_type> handle;
    };

    // Specialized Task for void returns
    template<>
    class Task<void> {
    public:
        struct promise_type {
            Result<void> result;
            std::exception_ptr exception_ptr = nullptr;
            std::coroutine_handle<> continuation = nullptr;
            std::atomic<bool> is_ready{false};

            Task get_return_object() {
                return Task(std::coroutine_handle<promise_type>::from_promise(*this));
            }

            std::suspend_never initial_suspend() { return {}; }
            
            auto final_suspend() noexcept {
                struct FinalAwaiter {
                    bool await_ready() const noexcept { return false; }
                    
                    void await_suspend(std::coroutine_handle<promise_type> h) noexcept {
                        // Mark as ready
                        h.promise().is_ready.store(true, std::memory_order_release);
                        
                        // Resume continuation if it exists
                        if (h.promise().continuation) {
                            h.promise().continuation.resume();
                        }
                    }
                    
                    void await_resume() noexcept {}
                };
                
                return FinalAwaiter{};
            }

            void return_void() { 
                result.success = true; 
            }

            void unhandled_exception() {
                try {
                    std::rethrow_exception(std::current_exception());
                } catch (const std::exception& e) {
                    result.error = e.what();
                } catch (...) {
                    result.error = "Unknown exception";
                }
                exception_ptr = std::current_exception();
            }
        };

        Task() : handle(nullptr) {}
        Task(std::coroutine_handle<promise_type> h) : handle(h) {}
        
        ~Task() {
            if (handle && !handle.done()) {
                handle.destroy();
            }
        }
        
        // Move semantics
        Task(Task&& other) noexcept : handle(other.handle) {
            other.handle = nullptr;
        }
        
        Task& operator=(Task&& other) noexcept {
            if (this != &other) {
                if (handle && !handle.done()) {
                    handle.destroy();
                }
                handle = other.handle;
                other.handle = nullptr;
            }
            return *this;
        }
        
        // No copy
        Task(const Task&) = delete;
        Task& operator=(const Task&) = delete;

        bool await_ready() const { 
            return !handle || handle.done() || 
                   handle.promise().is_ready.load(std::memory_order_acquire); 
        }
        
        void await_suspend(std::coroutine_handle<> awaiting) {
            // Store the awaiting coroutine to resume it later
            handle.promise().continuation = awaiting;
        }
        
        Result<void> await_resume() {
            if (!handle) {
                return Result<void>{false, "Task not initialized"};
            }
            
            if (handle.promise().exception_ptr) {
                std::rethrow_exception(handle.promise().exception_ptr);
            }
            
            return handle.promise().result;
        }
        
        // Synchronously wait for the task to complete
        Result<void> get_result() {
            if (!handle) {
                return Result<void>{false, "Task not initialized"};
            }
            
            // Wait until the coroutine is done
            while (!handle.done() && 
                   !handle.promise().is_ready.load(std::memory_order_acquire)) {
                std::this_thread::yield();
            }
            
            if (handle.promise().exception_ptr) {
                std::rethrow_exception(handle.promise().exception_ptr);
            }
            
            return handle.promise().result;
        }

    private:
        std::coroutine_handle<promise_type> handle;
    };

    // Connection Manager
    export class ConnectionManager {
    public:
        /**
         * @brief Constructor
         * @param connectionString PostgreSQL connection string
         * @param poolSize Number of connections to maintain in the pool
         * @param useSSL Whether to use SSL for connections
         */
        ConnectionManager(const std::string& connectionString,
                         size_t poolSize = 10,
                         bool useSSL = true);

        /**
         * @brief Destructor
         */
        ~ConnectionManager();

        /**
         * @brief Get a connection from the pool (synchronous)
         * @return Shared pointer to a connection
         */
        std::shared_ptr<pqxx::connection> getConnection();

        /**
         * @brief Get a connection from the pool (asynchronous)
         * @return Task with a shared pointer to a connection
         */
        Task<std::shared_ptr<pqxx::connection>> getConnectionAsync();

        /**
         * @brief Execute a function with a connection (synchronous)
         * @param func Function to execute with the connection
         * @return Result of the function
         */
        template<typename Func>
        auto executeWithConnection(Func func) -> decltype(func(std::declval<pqxx::connection&>())) {
            auto conn = getConnection();
            try {
                return func(*conn);
            } catch (...) {
                // Connection will be returned to the pool by the shared_ptr deleter
                throw;
            }
        }

        /**
         * @brief Execute a function with a connection (asynchronous)
         * @param func Function to execute with the connection
         * @return Task with the result of the function
         */
        template<typename Func>
        auto executeWithConnectionAsync(Func func)
            -> Task<decltype(func(std::declval<pqxx::connection&>()))> {
            
            // This is a coroutine function
            auto conn = co_await getConnectionAsync();
            
            try {
                auto result = func(*conn);
                co_return Result<decltype(func(std::declval<pqxx::connection&>()))>{
                    std::move(result), std::nullopt
                };
            } catch (const std::exception& e) {
                co_return Result<decltype(func(std::declval<pqxx::connection&>()))>{
                    std::nullopt, e.what()
                };
            } catch (...) {
                co_return Result<decltype(func(std::declval<pqxx::connection&>()))>{
                    std::nullopt, "Unknown error"
                };
            }
        }

        /**
         * @brief Check if the database is available
         * @return True if the database is available
         */
        bool isAvailable();

        /**
         * @brief Check if the database is available (asynchronous)
         * @return Task with true if the database is available
         */
        Task<bool> isAvailableAsync();

        /**
         * @brief Get database version
         * @return PostgreSQL version string
         */
        std::string getDatabaseVersion();

        /**
         * @brief Get database version (asynchronous)
         * @return Task with PostgreSQL version string
         */
        Task<std::string> getDatabaseVersionAsync();

        /**
         * @brief Close all connections
         */
        void shutdown();

    private:
        /**
         * @brief Create a new connection
         * @return Shared pointer to a new connection
         */
        std::shared_ptr<pqxx::connection> createConnection();

        /**
         * @brief Return a connection to the pool
         * @param conn Connection to return
         */
        void returnConnection(pqxx::connection* conn);

        /**
         * @brief Build a secure connection string
         * @return Connection string with SSL parameters if enabled
         */
        std::string buildConnectionString();

        std::string connectionString_;
        size_t poolSize_;
        bool useSSL_;

        std::vector<std::shared_ptr<pqxx::connection>> availableConnections_;
        std::unordered_map<pqxx::connection*, std::shared_ptr<pqxx::connection>> activeConnections_;

        std::mutex mutex_;
        std::condition_variable cv_;
        bool shutdown_{false};

        // SSL configuration
        std::string sslCertPath_;
        std::string sslKeyPath_;
        std::string sslCaPath_;
    };

    // Query Result
    export class QueryResult {
    public:
        QueryResult(pqxx::result&& result);

        // Get number of rows
        size_t size() const;

        // Get row as vector of strings
        std::vector<std::string> getRow(size_t index) const;

        // Get all rows as vector of vectors
        std::vector<std::vector<std::string>> getRows() const;

        // Get column names
        std::vector<std::string> getColumnNames() const;

        // Get value at row, column
        std::string getValue(size_t row, size_t col) const;

        // Get value at row, column name
        std::string getValue(size_t row, const std::string& colName) const;

    private:
        pqxx::result result_;
    };

    // Database Service Interface
    export class DatabaseInterface {
    public:
        virtual ~DatabaseInterface() = default;

        // Synchronous methods
        virtual Result<QueryResult> executeQuery(const std::string& query) = 0;
        virtual Result<void> executeCommand(const std::string& command) = 0;
        virtual Result<void> executeTransaction(const std::vector<std::string>& commands) = 0;

        // Asynchronous methods
        virtual Task<Result<QueryResult>> executeQueryAsync(const std::string& query) = 0;
        virtual Task<Result<void>> executeCommandAsync(const std::string& command) = 0;
        virtual Task<Result<void>> executeTransactionAsync(const std::vector<std::string>& commands) = 0;
    };

} // namespace dbservice::core
