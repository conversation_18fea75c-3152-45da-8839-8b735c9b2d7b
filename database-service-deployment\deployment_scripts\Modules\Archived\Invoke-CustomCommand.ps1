# Run Custom Command Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Run custom command function
function Invoke-CustomCommand {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Run Custom Command                     " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host 
    
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "SSH configuration is not set up. Please configure SSH settings first." -ForegroundColor Red
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    
    Write-Host "This function allows you to run a custom command on the remote server." -ForegroundColor Yellow
    Write-Host "The command will be executed as the SSH user." -ForegroundColor Yellow
    Write-Host "Use 'sudo' to execute commands with elevated privileges." -ForegroundColor Yellow
    Write-Host 
    
    $customCommand = Read-Host "Enter command to execute"
    
    if ([string]::IsNullOrWhiteSpace($customCommand)) {
        Write-Host "Command cannot be empty." -ForegroundColor Red
        Wait-ForUser
        Show-MainMenu
        return
    }
    
    Write-Host 
    Write-Host "You are about to execute the following command:" -ForegroundColor Cyan
    Write-Host $customCommand -ForegroundColor White
    Write-Host 
    
    $confirm = Read-Host "Are you sure you want to execute this command? (y/n)"
    
    if ($confirm -ne "y") {
        Write-Host "Command execution cancelled." -ForegroundColor Yellow
        Show-MainMenu
        return
    }
    
    # Execute the command
    Write-Host "Executing command..." -ForegroundColor Yellow
    $result = Invoke-RemoteCommand -Command $customCommand
    
    Write-Host 
    Write-Host "Command output:" -ForegroundColor Cyan
    Write-Host $result
    
    Write-Host 
    Write-Host "Press any key to return to the main menu..."
    Wait-ForUser
    
    Show-MainMenu
}

# Run the function
Invoke-CustomCommand
