# Logging Service Configuration Validation

## Overview

The Logging Service includes a comprehensive configuration validation system that ensures all configuration parameters are valid and appropriate for the service's operation. This document describes the configuration validation features, how they work, and how to use them effectively.

## Configuration Files

The Logging Service uses two main configuration files:

1. **Main Configuration File**: Contains general settings, log sources, and retention policies
2. **Storage Configuration File**: Contains storage tier-specific settings

By default, these files are located at:
- Main Configuration: `/etc/git-dashboard/logging-config.json`
- Storage Configuration: `/opt/git-dashboard/config/storage-config.json`

## Configuration Parameters

### Main Configuration Parameters

#### Tier Configuration

| Section | Parameter | Type | Default | Description |
|---------|-----------|------|---------|-------------|
| `tiers.memory` | `enabled` | Boolean | `true` | Enable memory storage tier |
| `tiers.memory` | `max_entries` | Integer | `1000` | Maximum number of entries in memory |
| `tiers.memory` | `max_age_minutes` | Integer | `60` | Maximum age of entries in memory |
| `tiers.database` | `enabled` | Boolean | `false` | Enable database storage tier |
| `tiers.database` | `connection_string` | String | `postgresql://username:password@localhost/gitdashboard` | Database connection string |
| `tiers.database` | `structured_only` | Boolean | `true` | Store only structured logs in database |
| `tiers.file` | `enabled` | Boolean | `true` | Enable file storage tier |
| `tiers.file` | `base_directory` | String | `/var/log/git-dashboard/logs` | Base directory for log files |
| `tiers.file` | `compression` | Boolean | `true` | Enable log file compression |
| `tiers.file` | `compression_age_days` | Integer | `7` | Age in days before compressing log files |

#### Retention Configuration

| Section | Parameter | Type | Default | Description |
|---------|-----------|------|---------|-------------|
| `retention` | `system` | Integer | `30` | Retention period in days for system logs |
| `retention` | `security` | Integer | `90` | Retention period in days for security logs |
| `retention` | `application` | Integer | `30` | Retention period in days for application logs |
| `retention` | `database` | Integer | `15` | Retention period in days for database logs |
| `retention` | `custom` | Integer | `30` | Retention period in days for custom logs |

#### Log Sources Configuration

| Section | Parameter | Type | Default | Description |
|---------|-----------|------|---------|-------------|
| `sources.[name]` | `enabled` | Boolean | `true` | Enable this log source |
| `sources.[name]` | `path` | String | Varies | Path to the log file or directory |
| `sources.[name]` | `parser` | String | Varies | Parser to use for this log source |

### Environment Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `CONFIG_PATH` | String | `/etc/git-dashboard/logging-config.json` | Path to the main configuration file |
| `LOG_SOURCES_PATH` | String | `/var/log` | Base path for log sources |
| `SERVER_PORT` | Integer | `8081` | Port on which the service listens |
| `LOG_LEVEL` | String | `info` | Logging level for the service itself |
| `STORAGE_CONFIG_PATH` | String | `/opt/git-dashboard/config/storage-config.json` | Path to the storage configuration file |
| `DB_CONNECTION_STRING` | String | - | Database connection string (overrides configuration file) |

## Validation Features

The configuration validation system performs the following checks:

### Memory Tier Validation

- Validates `max_entries` is between 1 and 1,000,000
- Validates `max_age_minutes` is between 1 and 10,080 (1 week)
- Sets default values if parameters are invalid

### Database Tier Validation

- Validates database connection string format
- Checks for environment variable if connection string is empty
- Validates `structured_only` is a boolean
- Provides clear error messages for invalid connection strings

### File Tier Validation

- Checks if `base_directory` exists and creates it if necessary
- Verifies write permissions to the base directory
- Validates `compression_age_days` is between 1 and 365
- Sets default values if parameters are invalid

### Retention Validation

- Validates retention periods are between 1 and 3,650 days (10 years)
- Sets appropriate default values based on log type
- Provides clear error messages for invalid retention periods

### Log Sources Validation

- Validates each log source has required parameters
- Checks if log source paths exist and are readable
- Validates parser types against available parsers
- Sets appropriate default values based on source name

## Validation Behavior

When the service starts, it performs the following steps:

1. Loads main configuration from the specified file
2. Validates main configuration parameters
3. Loads storage configuration from the specified file
4. Validates storage configuration parameters
5. Reports any validation issues
6. Applies default values for invalid parameters
7. Writes corrected configuration back to the files
8. Continues execution with the validated configuration

The service will attempt to continue running even if some configuration parameters are invalid, falling back to default values where possible.

## Example Configuration File

### Main Configuration

```json
{
  "tiers": {
    "memory": {
      "enabled": true,
      "max_entries": 1000,
      "max_age_minutes": 60
    },
    "database": {
      "enabled": false,
      "connection_string": "postgresql://username:password@localhost/gitdashboard",
      "structured_only": true
    },
    "file": {
      "enabled": true,
      "base_directory": "/var/log/git-dashboard/logs",
      "compression": true,
      "compression_age_days": 7
    }
  },
  "retention": {
    "system": 30,
    "security": 90,
    "application": 30,
    "database": 15,
    "custom": 30
  },
  "sources": {
    "system": {
      "enabled": true,
      "path": "/var/log/syslog",
      "parser": "syslog"
    },
    "auth": {
      "enabled": true,
      "path": "/var/log/auth.log",
      "parser": "syslog"
    },
    "nginx": {
      "enabled": true,
      "path": "/var/log/nginx/error.log",
      "parser": "nginx"
    },
    "git": {
      "enabled": true,
      "path": "/var/log/git",
      "parser": "git"
    },
    "dashboard": {
      "enabled": true,
      "path": "/var/log/git-dashboard.log",
      "parser": "dashboard"
    }
  }
}
```

## Example Validation Output

```
Validating configuration file: /etc/git-dashboard/logging-config.json
Invalid max_entries value: 0, must be between 1 and 1000000
Setting max_entries to default: 1000
Warning: Log directory does not exist: /var/log/git-dashboard/logs
Created directory: /var/log/git-dashboard/logs
Invalid retention period for security logs: 0 days, must be between 1 and 3650
Setting security retention to default: 90 days
Warning: Source path does not exist: /var/log/git
This source may not function correctly until the log file is created
Configuration validation found issues, but will attempt to continue
Updated configuration saved to /etc/git-dashboard/logging-config.json
```

## Best Practices

1. **Use Default Paths**: Use the default configuration file paths unless you have a specific reason to change them.

2. **Set Appropriate Permissions**: Ensure the service has appropriate permissions to read/write to the specified paths.

3. **Configure Database Tier Carefully**: If enabling the database tier, ensure the connection string is correct and the database exists.

4. **Monitor Validation Output**: Check the service logs for validation warnings and errors during startup.

5. **Adjust Retention Periods**: Set appropriate retention periods based on your organization's needs and compliance requirements.

## Troubleshooting

### Common Issues

1. **Service fails to start**: Check if the specified paths exist and have appropriate permissions.

2. **Database tier not working**: Verify the connection string and ensure the database is accessible.

3. **Missing log sources**: Check if the log source paths exist and are readable.

4. **Logs not being stored**: Verify that at least one storage tier is enabled and properly configured.

5. **Configuration not being saved**: Check if the service has write permissions to the configuration files.

## Conclusion

The configuration validation system in the Logging Service helps prevent runtime errors by validating configuration parameters before they are used. By providing sensible defaults, creating necessary directories, and giving clear error messages, it makes the service more robust and easier to configure correctly.
