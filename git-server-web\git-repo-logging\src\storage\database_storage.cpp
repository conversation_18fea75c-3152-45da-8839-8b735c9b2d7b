#include "database_storage.hpp"
#include <sstream>
#include <jsoncpp/json/json.h>
#include <iostream>
#include <pqxx/pqxx>

namespace logging {

DatabaseStorage::DatabaseStorage(const std::string& connectionString, bool structuredOnly)
    : connectionString_(connectionString), structuredOnly_(structuredOnly), connection_(nullptr) {

    // Check if connection string is empty, use environment variable if available
    if (connectionString_.empty()) {
        const char* connStrEnv = std::getenv("DB_CONNECTION_STRING");
        if (connStrEnv) {
            connectionString_ = connStrEnv;
        } else {
            connectionString_ = "postgresql://postgres:postgres@localhost/git_logs";
        }
    }

    std::cout << "Initializing database storage with connection string: " << connectionString_
              << ", structured only: " << (structuredOnly ? "true" : "false") << std::endl;

    // Initialize database connection
    initialize();
}

DatabaseStorage::~DatabaseStorage() {
    // Connection will be closed automatically when destroyed
}

bool DatabaseStorage::initialize() {
    try {
        // Create connection
        connection_ = std::make_unique<pqxx::connection>(connectionString_);

        if (!connection_->is_open()) {
            std::cerr << "Failed to open database connection" << std::endl;
            circuitBreaker_.recordFailure();
            return false;
        }

        // Create tables and indexes
        if (!createTables() || !createIndexes()) {
            circuitBreaker_.recordFailure();
            return false;
        }

        // Record success in circuit breaker
        circuitBreaker_.recordSuccess();
        std::cout << "Database storage initialized successfully" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Database initialization error: " << e.what() << std::endl;
        circuitBreaker_.recordFailure();
        return false;
    }
}

bool DatabaseStorage::isConnected() const {
    return connection_ && connection_->is_open();
}

bool DatabaseStorage::reconnect(int maxAttempts, int delaySeconds) {
    // Check if we're already in the process of reconnecting
    if (reconnecting_.exchange(true)) {
        std::cout << "Reconnection already in progress, skipping" << std::endl;
        return false;
    }

    // Check if we're in cooldown period
    auto now = std::chrono::steady_clock::now();
    if (now - lastReconnectAttempt_ < reconnectCooldown_) {
        std::cout << "Reconnection in cooldown period, skipping" << std::endl;
        reconnecting_ = false;
        return false;
    }

    lastReconnectAttempt_ = now;

    // Check if circuit breaker allows the request
    if (!circuitBreaker_.allowRequest()) {
        std::cerr << "Circuit breaker is open, skipping reconnection attempt" << std::endl;
        reconnecting_ = false;
        return false;
    }

    std::cout << "Attempting database reconnection (max attempts: " << maxAttempts << ")..." << std::endl;

    bool success = false;

    for (int attempt = 1; attempt <= maxAttempts; ++attempt) {
        std::cout << "Reconnection attempt " << attempt << "/" << maxAttempts << "..." << std::endl;

        try {
            // Close existing connection if any
            connection_.reset();

            // Create new connection
            connection_ = std::make_unique<pqxx::connection>(connectionString_);

            if (connection_->is_open()) {
                std::cout << "Database reconnection successful" << std::endl;
                circuitBreaker_.recordSuccess();
                success = true;
                break;
            }
        } catch (const std::exception& e) {
            std::cerr << "Reconnection attempt " << attempt << " failed: " << e.what() << std::endl;
        }

        // Wait before next attempt
        if (attempt < maxAttempts) {
            std::this_thread::sleep_for(std::chrono::seconds(delaySeconds));
        }
    }

    if (!success) {
        std::cerr << "All reconnection attempts failed" << std::endl;
        circuitBreaker_.recordFailure();
    }

    reconnecting_ = false;
    return success;
}

bool DatabaseStorage::createTables() {
    try {
        pqxx::work txn(*connection_);

        // Create logs table
        txn.exec(
            "CREATE TABLE IF NOT EXISTS logs ("
            "  id BIGSERIAL PRIMARY KEY,"
            "  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,"
            "  source VARCHAR(255) NOT NULL,"
            "  log_level VARCHAR(50) NOT NULL,"
            "  message TEXT NOT NULL,"
            "  raw_data TEXT,"
            "  metadata JSONB"
            ");"
        );

        // Create log_stats table for caching statistics
        txn.exec(
            "CREATE TABLE IF NOT EXISTS log_stats ("
            "  id SERIAL PRIMARY KEY,"
            "  stat_name VARCHAR(255) NOT NULL UNIQUE,"
            "  stat_value JSONB NOT NULL,"
            "  updated_at TIMESTAMP WITH TIME ZONE NOT NULL"
            ");"
        );

        txn.commit();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error creating tables: " << e.what() << std::endl;
        return false;
    }
}

bool DatabaseStorage::createIndexes() {
    try {
        pqxx::work txn(*connection_);

        // Create indexes for common queries
        txn.exec("CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);");
        txn.exec("CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);");
        txn.exec("CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(log_level);");
        txn.exec("CREATE INDEX IF NOT EXISTS idx_logs_metadata ON logs USING GIN (metadata);");

        txn.commit();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error creating indexes: " << e.what() << std::endl;
        return false;
    }
}

bool DatabaseStorage::store(const LogEntry& entry) {
    // Check if we can handle this entry
    if (!canHandle(entry)) {
        return false;
    }

    // Check if circuit breaker allows the request
    if (!circuitBreaker_.allowRequest()) {
        std::cerr << "Circuit breaker is open, skipping database operation" << std::endl;
        return false;
    }

    // Check connection and attempt reconnection if needed
    if (!isConnected()) {
        std::cerr << "Database not connected, attempting reconnection..." << std::endl;
        if (!reconnect()) {
            std::cerr << "Failed to reconnect to database" << std::endl;
            return false;
        }
    }

    try {
        std::lock_guard<std::mutex> lock(mutex_);

        pqxx::work txn(*connection_);

        // Convert timestamp to string
        std::string timestamp = std::to_string(
            std::chrono::duration_cast<std::chrono::seconds>(
                entry.timestamp.time_since_epoch()
            ).count()
        );

        // Convert log type to string
        std::string logLevel;
        switch (entry.type) {
            case LogType::INFO: logLevel = "info"; break;
            case LogType::WARNING: logLevel = "warning"; break;
            case LogType::ERROR: logLevel = "error"; break;
            case LogType::SUCCESS: logLevel = "success"; break;
            case LogType::DEBUG: logLevel = "debug"; break;
        }

        // Convert metadata to JSON
        std::string metadataJson = logEntryToJson(entry);

        // Insert into database
        txn.exec_params(
            "INSERT INTO logs (timestamp, source, log_level, message, raw_data, metadata) "
            "VALUES (to_timestamp($1), $2, $3, $4, $5, $6)",
            timestamp, entry.source, logLevel, entry.message,
            entry.rawLine.value_or(""), metadataJson
        );

        txn.commit();

        // Record success in circuit breaker
        circuitBreaker_.recordSuccess();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error storing log entry: " << e.what() << std::endl;

        // Record failure in circuit breaker
        circuitBreaker_.recordFailure();
        return false;
    }
}

bool DatabaseStorage::storeBatch(const std::vector<LogEntry>& entries) {
    if (entries.empty()) {
        return true;
    }

    // Check if circuit breaker allows the request
    if (!circuitBreaker_.allowRequest()) {
        std::cerr << "Circuit breaker is open, skipping database batch operation" << std::endl;
        return false;
    }

    // Check connection and attempt reconnection if needed
    if (!isConnected()) {
        std::cerr << "Database not connected, attempting reconnection..." << std::endl;
        if (!reconnect()) {
            std::cerr << "Failed to reconnect to database" << std::endl;
            return false;
        }
    }

    try {
        std::lock_guard<std::mutex> lock(mutex_);

        pqxx::work txn(*connection_);

        // Start a COPY operation
        pqxx::stream_to stream(
            txn,
            "logs",
            {"timestamp", "source", "log_level", "message", "raw_data", "metadata"}
        );

        // Process each entry
        for (const auto& entry : entries) {
            // Skip entries we can't handle
            if (!canHandle(entry)) {
                continue;
            }

            // Convert timestamp to string
            std::string timestamp = std::to_string(
                std::chrono::duration_cast<std::chrono::seconds>(
                    entry.timestamp.time_since_epoch()
                ).count()
            );

            // Convert log type to string
            std::string logLevel;
            switch (entry.type) {
                case LogType::INFO: logLevel = "info"; break;
                case LogType::WARNING: logLevel = "warning"; break;
                case LogType::ERROR: logLevel = "error"; break;
                case LogType::SUCCESS: logLevel = "success"; break;
                case LogType::DEBUG: logLevel = "debug"; break;
            }

            // Convert metadata to JSON
            std::string metadataJson = logEntryToJson(entry);

            // Add to stream
            stream << std::make_tuple(
                "to_timestamp(" + timestamp + ")",
                entry.source,
                logLevel,
                entry.message,
                entry.rawLine.value_or(""),
                metadataJson
            );
        }

        // Complete the COPY operation
        stream.complete();

        txn.commit();

        // Record success in circuit breaker
        circuitBreaker_.recordSuccess();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error storing log entries in batch: " << e.what() << std::endl;

        // Record failure in circuit breaker
        circuitBreaker_.recordFailure();
        return false;
    }
}

std::vector<LogEntry> DatabaseStorage::query(const LogQueryParams& params) {
    std::vector<LogEntry> results;

    // Check if circuit breaker allows the request
    if (!circuitBreaker_.allowRequest()) {
        std::cerr << "Circuit breaker is open, skipping database query operation" << std::endl;
        return results;
    }

    // Check connection and attempt reconnection if needed
    if (!isConnected()) {
        std::cerr << "Database not connected, attempting reconnection..." << std::endl;
        if (!reconnect()) {
            std::cerr << "Failed to reconnect to database" << std::endl;
            return results;
        }
    }

    try {
        std::lock_guard<std::mutex> lock(mutex_);

        // Build query string
        std::string queryStr = buildQueryString(params);

        pqxx::work txn(*connection_);
        pqxx::result dbResult = txn.exec(queryStr);

        // Convert rows to LogEntry objects
        for (const auto& row : dbResult) {
            results.push_back(rowToLogEntry(row));
        }

        txn.commit();

        // Record success in circuit breaker
        circuitBreaker_.recordSuccess();
    } catch (const std::exception& e) {
        std::cerr << "Error querying logs: " << e.what() << std::endl;

        // Record failure in circuit breaker
        circuitBreaker_.recordFailure();
    }

    return results;
}

StorageStats DatabaseStorage::getStats() {
    StorageStats stats;

    if (!isConnected()) {
        std::cerr << "Database not connected" << std::endl;
        return stats;
    }

    try {
        std::lock_guard<std::mutex> lock(mutex_);

        pqxx::work txn(*connection_);

        // Get total count
        pqxx::result totalResult = txn.exec("SELECT COUNT(*) FROM logs");
        stats.totalEntries = totalResult[0][0].as<size_t>();

        // Get counts by source
        pqxx::result sourceResult = txn.exec("SELECT source, COUNT(*) FROM logs GROUP BY source");
        for (const auto& row : sourceResult) {
            stats.bySource[row[0].as<std::string>()] = row[1].as<size_t>();
        }

        // Get counts by level
        pqxx::result levelResult = txn.exec("SELECT log_level, COUNT(*) FROM logs GROUP BY log_level");
        for (const auto& row : levelResult) {
            std::string level = row[0].as<std::string>();
            size_t count = row[1].as<size_t>();

            if (level == "info") stats.byLevel[LogType::INFO] = count;
            else if (level == "warning") stats.byLevel[LogType::WARNING] = count;
            else if (level == "error") stats.byLevel[LogType::ERROR] = count;
            else if (level == "success") stats.byLevel[LogType::SUCCESS] = count;
            else if (level == "debug") stats.byLevel[LogType::DEBUG] = count;
        }

        // Get oldest and newest timestamps
        if (stats.totalEntries > 0) {
            pqxx::result oldestResult = txn.exec("SELECT timestamp FROM logs ORDER BY timestamp ASC LIMIT 1");
            pqxx::result newestResult = txn.exec("SELECT timestamp FROM logs ORDER BY timestamp DESC LIMIT 1");

            // Convert to time_point
            std::string oldestStr = oldestResult[0][0].as<std::string>();
            std::string newestStr = newestResult[0][0].as<std::string>();

            // Parse timestamps (simplified for now)
            // In a real implementation, you'd need proper timestamp parsing
            stats.oldestEntry = std::chrono::system_clock::now();
            stats.newestEntry = std::chrono::system_clock::now();
        }

        // Get storage size
        pqxx::result sizeResult = txn.exec(
            "SELECT pg_total_relation_size('logs') + pg_total_relation_size('log_stats')"
        );
        stats.storageSize = sizeResult[0][0].as<size_t>();

        txn.commit();
    } catch (const std::exception& e) {
        std::cerr << "Error getting storage stats: " << e.what() << std::endl;
    }

    return stats;
}

bool DatabaseStorage::prune(std::chrono::system_clock::time_point olderThan) {
    if (!isConnected()) {
        std::cerr << "Database not connected" << std::endl;
        return false;
    }

    try {
        std::lock_guard<std::mutex> lock(mutex_);

        // Convert timestamp to string
        std::string timestamp = std::to_string(
            std::chrono::duration_cast<std::chrono::seconds>(
                olderThan.time_since_epoch()
            ).count()
        );

        pqxx::work txn(*connection_);

        // Delete logs older than the specified time
        pqxx::result result = txn.exec_params(
            "DELETE FROM logs WHERE timestamp < to_timestamp($1) RETURNING COUNT(*)",
            timestamp
        );

        size_t deletedCount = result[0][0].as<size_t>();

        txn.commit();

        std::cout << "Pruned " << deletedCount << " logs from database" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error pruning logs: " << e.what() << std::endl;
        return false;
    }
}

bool DatabaseStorage::compact() {
    if (!isConnected()) {
        std::cerr << "Database not connected" << std::endl;
        return false;
    }

    try {
        std::lock_guard<std::mutex> lock(mutex_);

        pqxx::work txn(*connection_);

        // Vacuum the tables to reclaim space
        txn.exec("VACUUM FULL logs");
        txn.exec("VACUUM FULL log_stats");

        txn.commit();

        std::cout << "Database compacted successfully" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error compacting database: " << e.what() << std::endl;
        return false;
    }
}

bool DatabaseStorage::backup(const std::string& backupPath) {
    if (!isConnected()) {
        std::cerr << "Database not connected" << std::endl;
        return false;
    }

    try {
        // Use pg_dump to create a backup
        std::string command = "pg_dump -Fc -f " + backupPath + " " + connectionString_;
        int result = system(command.c_str());

        if (result != 0) {
            std::cerr << "Error executing pg_dump command" << std::endl;
            return false;
        }

        std::cout << "Database backed up to " << backupPath << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error backing up database: " << e.what() << std::endl;
        return false;
    }
}

bool DatabaseStorage::canHandle(const LogEntry& entry) const {
    // If structuredOnly is true, only handle entries with clear structure
    if (structuredOnly_) {
        // Check if entry has required fields
        if (entry.message.empty() || entry.source.empty()) {
            return false;
        }

        // Check if entry has a valid timestamp
        if (entry.timestamp == std::chrono::system_clock::time_point()) {
            return false;
        }

        // Additional checks for structure could be added here
    }

    return true;
}

std::string DatabaseStorage::buildQueryString(const LogQueryParams& params) {
    std::stringstream query;

    query << "SELECT id, timestamp, source, log_level, message, raw_data, metadata FROM logs WHERE 1=1";

    // Add source filter
    if (params.source != "all") {
        query << " AND source = '" << params.source << "'";
    }

    // Add level filter
    if (params.level != "all") {
        query << " AND log_level = '" << params.level << "'";
    }

    // Add time range filters
    if (params.startTime) {
        std::string startTimestamp = std::to_string(
            std::chrono::duration_cast<std::chrono::seconds>(
                params.startTime->time_since_epoch()
            ).count()
        );
        query << " AND timestamp >= to_timestamp(" << startTimestamp << ")";
    }

    if (params.endTime) {
        std::string endTimestamp = std::to_string(
            std::chrono::duration_cast<std::chrono::seconds>(
                params.endTime->time_since_epoch()
            ).count()
        );
        query << " AND timestamp <= to_timestamp(" << endTimestamp << ")";
    }

    // Add text search
    if (!params.searchText.empty()) {
        query << " AND (message ILIKE '%" << params.searchText << "%' OR "
              << "raw_data ILIKE '%" << params.searchText << "%')";
    }

    // Add include sources
    if (!params.includeSources.empty()) {
        query << " AND source IN (";
        bool first = true;
        for (const auto& source : params.includeSources) {
            if (!first) query << ", ";
            query << "'" << source << "'";
            first = false;
        }
        query << ")";
    }

    // Add exclude sources
    if (!params.excludeSources.empty()) {
        query << " AND source NOT IN (";
        bool first = true;
        for (const auto& source : params.excludeSources) {
            if (!first) query << ", ";
            query << "'" << source << "'";
            first = false;
        }
        query << ")";
    }

    // Add order by
    query << " ORDER BY timestamp DESC";

    // Add limit and offset
    if (params.limit > 0) {
        query << " LIMIT " << params.limit;
    }

    if (params.offset > 0) {
        query << " OFFSET " << params.offset;
    }

    return query.str();
}

LogEntry DatabaseStorage::rowToLogEntry(const pqxx::row& row) {
    LogEntry entry;

    // Parse timestamp
    std::string timestampStr = row["timestamp"].as<std::string>();
    // In a real implementation, you'd need proper timestamp parsing
    entry.timestamp = std::chrono::system_clock::now();

    // Set basic fields
    entry.source = row["source"].as<std::string>();

    // Parse log level
    std::string logLevel = row["log_level"].as<std::string>();
    if (logLevel == "info") entry.type = LogType::INFO;
    else if (logLevel == "warning") entry.type = LogType::WARNING;
    else if (logLevel == "error") entry.type = LogType::ERROR;
    else if (logLevel == "success") entry.type = LogType::SUCCESS;
    else if (logLevel == "debug") entry.type = LogType::DEBUG;

    entry.message = row["message"].as<std::string>();
    entry.rawLine = row["raw_data"].as<std::string>();

    // Parse metadata JSON
    std::string metadataJson = row["metadata"].as<std::string>();

    // In a real implementation, you'd parse the JSON and populate the entry
    // For now, we'll just set a placeholder
    entry.metadata["stored_in_db"] = "true";

    return entry;
}

std::string DatabaseStorage::logEntryToJson(const LogEntry& entry) {
    Json::Value root;

    // Add optional fields
    if (entry.component) root["component"] = *entry.component;
    if (entry.unit) root["unit"] = *entry.unit;
    if (entry.hostname) root["hostname"] = *entry.hostname;
    if (entry.commit) root["commit"] = *entry.commit;
    if (entry.author) root["author"] = *entry.author;

    // Add metadata
    for (const auto& [key, value] : entry.metadata) {
        root[key] = value;
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

LogEntry DatabaseStorage::jsonToLogEntry(const std::string& json) {
    LogEntry entry;

    try {
        Json::Value root;
        Json::CharReaderBuilder reader;
        std::string errors;

        std::istringstream jsonStream(json);
        if (Json::parseFromStream(reader, jsonStream, &root, &errors)) {
            // Parse optional fields
            if (root.isMember("component")) entry.component = root["component"].asString();
            if (root.isMember("unit")) entry.unit = root["unit"].asString();
            if (root.isMember("hostname")) entry.hostname = root["hostname"].asString();
            if (root.isMember("commit")) entry.commit = root["commit"].asString();
            if (root.isMember("author")) entry.author = root["author"].asString();

            // Parse metadata
            for (const auto& key : root.getMemberNames()) {
                if (key != "component" && key != "unit" && key != "hostname" &&
                    key != "commit" && key != "author") {
                    entry.metadata[key] = root[key].asString();
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error parsing JSON: " << e.what() << std::endl;
    }

    return entry;
}

} // namespace logging
