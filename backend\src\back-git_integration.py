import subprocess
import re
from typing import Dict, List, Optional
from datetime import datetime
import logging
from templates import CommitTracker

class GitOperationError(Exception):
    pass

class GitOperationManager:
    TIMEOUT = 30  # seconds
    MAX_BUFFER = 1024 * 1024  # 1MB

    @contextmanager
    def git_operation(self, operation_name: str):
        """Context manager for git operations with timeout and resource limits"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self._record_operation_metrics(operation_name, duration)

    def execute_git_command(self, command: List[str], cwd: Optional[str] = None) -> Dict:
        """Execute git command with proper resource management"""
        try:
            with self.git_operation(command[0]):
                result = subprocess.run(
                    command,
                    cwd=cwd or self.repo_path,
                    capture_output=True,
                    text=True,
                    timeout=self.TIMEOUT,
                    bufsize=self.MAX_BUFFER
                )
                
                if result.returncode != 0:
                    raise GitOperationError(
                        f"Git command failed: {result.stderr}"
                    )
                
                return {
                    "output": result.stdout,
                    "status": result.returncode,
                    "error": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error(f"Git operation timed out after {self.TIMEOUT}s")
            raise GitOperationError("Operation timed out")

class GitIntegration:
    def __init__(self, repository_path: str, project_config: Dict):
        self.repo_path = repository_path
        self.commit_tracker = CommitTracker(project_config)
        self.setup_logging()
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('git_integration.log'),
                logging.StreamHandler()
            ]
        )
    
    def get_latest_commits(self, since: Optional[str] = None) -> List[Dict]:
        """Get commits since the specified date or last recorded commit."""
        try:
            cmd = ['git', 'log', '--pretty=format:%H|%an|%at|%s']
            if since:
                cmd.append(f'--since="{since}"')
            
            result = subprocess.run(
                cmd,
                cwd=self.repo_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logging.error(f"Failed to get commits: {result.stderr}")
                return []
            
            commits = []
            for line in result.stdout.split('\n'):
                if not line.strip():
                    continue
                    
                hash, author, timestamp, message = line.split('|')
                commit_info = self.commit_tracker.parse_commit_message(message)
                
                # Get affected files for this commit
                files = self.get_commit_files(hash)
                commit_info['affected_files'].extend(files)
                
                commits.append({
                    'hash': hash,
                    'author': author,
                    'timestamp': datetime.fromtimestamp(int(timestamp)),
                    'message': message,
                    'type': commit_info['type'],
                    'version': commit_info['version'],
                    'affected_files': list(set(commit_info['affected_files']))
                })
            
            return commits
        except Exception as e:
            logging.error(f"Error getting commits: {str(e)}")
            return []
    
    def get_commit_files(self, commit_hash: str) -> List[str]:
        """Get list of files modified in a commit."""
        try:
            result = subprocess.run(
                ['git', 'show', '--name-only', '--pretty=format:', commit_hash],
                cwd=self.repo_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logging.error(f"Failed to get commit files: {result.stderr}")
                return []
            
            files = []
            for file in result.stdout.strip().split('\n'):
                if file.strip():
                    # Check if file matches tracked patterns
                    for pattern in self.commit_tracker.config['tracked_files']:
                        if self._matches_glob(file, pattern):
                            files.append(file)
                            break
            
            return files
        except Exception as e:
            logging.error(f"Error getting commit files: {str(e)}")
            return []
    
    def get_rollback_command(self, commit_hash: str) -> str:
        """Generate rollback command for a commit."""
        return f"git revert {commit_hash}"
    
    def _matches_glob(self, filename: str, pattern: str) -> bool:
        """Check if a filename matches a glob pattern."""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)
    
    def analyze_commit_impact(self, commit_hash: str) -> Dict:
        """Analyze the potential impact of a commit."""
        try:
            # Get the diff stats
            result = subprocess.run(
                ['git', 'show', '--stat', commit_hash],
                cwd=self.repo_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logging.error(f"Failed to analyze commit: {result.stderr}")
                return {}
            
            # Parse the diff stats
            stats = {
                'files_changed': 0,
                'insertions': 0,
                'deletions': 0,
                'impact_level': 'low'
            }
            
            # Extract numbers from the summary line
            summary = result.stdout.split('\n')[-2]
            numbers = re.findall(r'\d+', summary)
            
            if numbers:
                stats['files_changed'] = int(numbers[0])
                if len(numbers) > 1:
                    stats['insertions'] = int(numbers[1])
                if len(numbers) > 2:
                    stats['deletions'] = int(numbers[2])
            
            # Determine impact level
            total_changes = stats['insertions'] + stats['deletions']
            if total_changes > 500 or stats['files_changed'] > 10:
                stats['impact_level'] = 'high'
            elif total_changes > 100 or stats['files_changed'] > 5:
                stats['impact_level'] = 'medium'
            
            return stats
        except Exception as e:
            logging.error(f"Error analyzing commit: {str(e)}")
            return {}
