#!/usr/bin/env python3
"""
Project Tracker Ubuntu Server Auto-Fix Tool
Version: 1.0.0

Automatic error resolution script for Project Tracker running on Ubuntu server.
This script is intended to run on the target Ubuntu server, not on the Windows build server.
Attempts to automatically fix common configuration and deployment issues.

Requirements:
- Ubuntu 24.04 LTS
- Python 3.12+
- Root/sudo access for system modifications
"""
import os
import sys
import subprocess
import shutil
import pwd
import grp
from typing import List, Dict, Any, Tuple
from validate_environment import EnvironmentValidator

class AutoFixer:
    def __init__(self):
        self.validator = EnvironmentValidator()
        self.fixes_applied = []
        self.sudo_available = self._check_sudo()

    def _check_sudo(self) -> bool:
        """Check if script has sudo privileges"""
        try:
            subprocess.run(["sudo", "-n", "true"], check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError:
            return False

    def _run_command(self, command: List[str], use_sudo: bool = False) -> Tuple[bool, str]:
        """Run a shell command and return success status and output"""
        try:
            if use_sudo and self.sudo_available:
                command.insert(0, "sudo")
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            return False, e.stderr

    def fix_python_environment(self) -> None:
        """Fix Python environment issues"""
        self.validator.check_python_environment()
        details = self.validator.results["python"]["details"]

        # Always create a new virtual environment if not in one
        if not details.get("virtualenv"):
            print("Creating new virtual environment...")
            venv_path = os.path.join(os.getcwd(), "venv")
            
            # Remove existing venv if it exists
            if os.path.exists(venv_path):
                print("Removing existing virtual environment...")
                shutil.rmtree(venv_path)
            
            # Create new venv
            success, output = self._run_command(["python3", "-m", "venv", venv_path])
            if success:
                self.fixes_applied.append("Created new virtual environment")
                
                # Create activation script
                activate_script = "activate_venv.sh"
                with open(activate_script, "w") as f:
                    f.write(f"""#!/bin/bash
# Activate virtual environment
source {venv_path}/bin/activate

# Upgrade pip
python3 -m pip install --upgrade pip

# Install requirements
python3 -m pip install -r requirements.txt
""")
                os.chmod(activate_script, 0o755)
                self.fixes_applied.append("Created virtual environment activation script")
                
                print("\nIMPORTANT: To use the virtual environment:")
                print(f"1. Run: source {activate_script}")
                print("2. Wait for pip upgrade and package installation to complete")
                print("3. The prompt should show (venv) to indicate it's active")

        # Upgrade pip if needed
        success, output = self._run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        if success:
            self.fixes_applied.append("Upgraded pip to latest version")

    def fix_system_resources(self) -> None:
        """Fix system resource issues"""
        self.validator.check_system_resources()
        details = self.validator.results["system"]["details"]

        # Check disk space
        if "disk" in details:
            disk_percent = float(details["disk"]["percent"].strip("%"))
            if disk_percent > 90:
                print("Cleaning up system packages...")
                commands = [
                    ["apt-get", "clean"],
                    ["apt-get", "autoremove", "-y"],
                    ["journalctl", "--vacuum-time=7d"]
                ]
                for cmd in commands:
                    success, _ = self._run_command(cmd, use_sudo=True)
                    if success:
                        self.fixes_applied.append(f"Cleaned up disk space using {cmd[0]}")

    def fix_services(self) -> None:
        """Fix service-related issues"""
        self.validator.check_required_services()
        details = self.validator.results["services"]["details"]

        services = ["nginx", "postgresql", "redis-server"]
        for service in services:
            if details.get(service) != "active":
                print(f"Attempting to start {service}...")
                commands = [
                    ["systemctl", "enable", service],
                    ["systemctl", "start", service]
                ]
                for cmd in commands:
                    success, _ = self._run_command(cmd, use_sudo=True)
                    if success:
                        self.fixes_applied.append(f"Started {service} service")

    def fix_network(self) -> None:
        """Fix network-related issues"""
        self.validator.check_network_configuration()
        details = self.validator.results["network"]["details"]

        # Check DNS resolution
        if details.get("dns_resolution") == "error":
            print("Fixing DNS configuration...")
            dns_config = "nameserver 10.0.10.1\nnameserver 1.1.1.1\n"
            try:
                with open("/etc/resolv.conf", "w") as f:
                    f.write(dns_config)
                self.fixes_applied.append("Updated DNS configuration")
            except PermissionError:
                print("Please run with sudo to fix DNS configuration")

        # Check required ports
        required_ports = {80, 443, 5000, 5432, 6379}
        open_ports = set(details.get("open_ports", []))
        closed_ports = required_ports - open_ports

        if closed_ports:
            print(f"Opening required ports: {closed_ports}")
            for port in closed_ports:
                success, _ = self._run_command(["ufw", "allow", str(port)], use_sudo=True)
                if success:
                    self.fixes_applied.append(f"Opened port {port}")

    def fix_dependencies(self) -> None:
        """Fix Python dependency issues"""
        self.validator.check_python_dependencies()
        details = self.validator.results["dependencies"]["details"]

        # Install missing packages
        missing = details.get("missing", [])
        if missing:
            print(f"Installing missing packages: {missing}")
            for package in missing:
                success, _ = self._run_command([sys.executable, "-m", "pip", "install", package])
                if success:
                    self.fixes_applied.append(f"Installed missing package: {package}")

        # Fix version mismatches
        mismatches = details.get("version_mismatch", [])
        if mismatches:
            print(f"Fixing package version mismatches: {mismatches}")
            requirements_file = "requirements.txt"
            if os.path.exists(requirements_file):
                success, _ = self._run_command([sys.executable, "-m", "pip", "install", "-r", requirements_file])
                if success:
                    self.fixes_applied.append("Updated packages to required versions")

    def fix_permissions(self) -> None:
        """Fix common permission issues"""
        directories = {
            "/var/log/project-tracker": ("www-data", "www-data", 0o755),
            "/var/run/project-tracker": ("www-data", "www-data", 0o755),
            "/etc/project-tracker": ("root", "root", 0o755)
        }

        for directory, (user, group, mode) in directories.items():
            if not os.path.exists(directory):
                success, _ = self._run_command(["mkdir", "-p", directory], use_sudo=True)
                if success:
                    self.fixes_applied.append(f"Created directory: {directory}")

            try:
                uid = pwd.getpwnam(user).pw_uid
                gid = grp.getgrnam(group).gr_gid
                
                success, _ = self._run_command(["chown", f"{user}:{group}", directory], use_sudo=True)
                if success:
                    self.fixes_applied.append(f"Fixed ownership of {directory}")
                
                success, _ = self._run_command(["chmod", oct(mode)[2:], directory], use_sudo=True)
                if success:
                    self.fixes_applied.append(f"Fixed permissions of {directory}")
            except (KeyError, subprocess.CalledProcessError) as e:
                print(f"Error fixing permissions for {directory}: {e}")

    def run_all_fixes(self) -> None:
        """Run all available fixes"""
        if not self.sudo_available:
            print("Warning: Some fixes require sudo privileges. Please run with sudo for full functionality.")

        print("Running environment validation...")
        self.validator.run_all_checks()
        self.validator.print_results()

        print("\nApplying fixes...")
        self.fix_python_environment()
        self.fix_system_resources()
        self.fix_services()
        self.fix_network()
        self.fix_dependencies()
        self.fix_permissions()

        print("\nRe-running validation...")
        self.validator.run_all_checks()
        self.validator.print_results()

        if self.fixes_applied:
            print("\nFixes applied:")
            for fix in self.fixes_applied:
                print(f"✓ {fix}")
        else:
            print("\nNo fixes were necessary or some fixes require manual intervention")

def main():
    if os.geteuid() != 0:
        print("Warning: Some fixes require root privileges. Run with sudo for full functionality.")
    
    fixer = AutoFixer()
    fixer.run_all_fixes()
    sys.exit(0 if not fixer.validator.get_exit_code() else 1)

if __name__ == "__main__":
    main()
