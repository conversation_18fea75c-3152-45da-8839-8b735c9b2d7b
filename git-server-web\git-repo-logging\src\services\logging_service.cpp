#include "logging_service.hpp"
#include "../api/api_server.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <regex>
#include <chrono>
#include <thread>
#include <filesystem>
#include <jsoncpp/json/json.h>

namespace fs = std::filesystem;

LoggingService::LoggingService(
    const std::string& logSourcesPath,
    const std::string& logLevel,
    std::shared_ptr<logging::ParserManager> parserManager,
    std::shared_ptr<logging::StorageManager> storageManager
) : logSourcesPath_(logSourcesPath),
    logLevel_(logLevel),
    parserManager_(parserManager),
    storageManager_(storageManager),
    running_(false),
    logDiscoveryService_(std::make_shared<logging::LogDiscoveryService>(logSourcesPath)),
    startTime_(std::chrono::steady_clock::now()) {

    // Initialize log discovery service
    logDiscoveryService_->scanLogSources();

    // Load configuration if available
    const char* configPathEnv = std::getenv("CONFIG_PATH");
    std::string configPath = configPathEnv ? configPathEnv : "/etc/git-dashboard/logging-config.json";
    if (fs::exists(configPath)) {
        logDiscoveryService_->loadConfiguration(configPath);
    }

    // Initialize log source paths
    systemLogPath_ = logSourcesPath_ + "/syslog";
    nginxLogPath_ = logSourcesPath_ + "/nginx/error.log";
    gitLogPath_ = logSourcesPath_ + "/git";
    authLogPath_ = logSourcesPath_ + "/auth.log";
    dashboardLogPath_ = logSourcesPath_ + "/git-dashboard.log";

    // Load existing logs
    loadLogsFromFile();

    // Start log collection
    startLogCollection();
}

LoggingService::~LoggingService() {
    // Stop log collection
    stopLogCollection();

    // Save logs to file
    saveLogsToFile();
}

// API endpoint handlers
std::string LoggingService::getLogs(const std::string& source, const std::string& level, int maxCount, bool getAll) {
    std::lock_guard<std::mutex> lock(logsMutex_);

    // Create JSON response
    Json::Value root;
    root["logs"] = Json::Value(Json::arrayValue);

    // Filter logs by source and level
    std::vector<LogEntry> filteredLogs;
    for (const auto& entry : logs_) {
        // Filter by source
        if (source != "all" && entry.source != source) {
            continue;
        }

        // Filter by level
        if (level != "all") {
            std::string entryLevel = getLogTypeString(entry.type);
            if (entryLevel != level) {
                continue;
            }
        }

        filteredLogs.push_back(entry);
    }

    // Sort logs by timestamp (newest first)
    std::sort(filteredLogs.begin(), filteredLogs.end(), [](const LogEntry& a, const LogEntry& b) {
        return a.timestamp > b.timestamp;
    });

    // Limit to maxCount if not getAll
    if (!getAll && filteredLogs.size() > static_cast<size_t>(maxCount)) {
        filteredLogs.resize(maxCount);
    }

    // Convert to JSON
    for (const auto& entry : filteredLogs) {
        Json::Value logJson;
        logJson["timestamp"] = formatTimestamp(entry.timestamp);
        logJson["type"] = getLogTypeString(entry.type);
        logJson["message"] = entry.message;
        logJson["source"] = entry.source;

        // Add optional fields if present
        if (entry.component) {
            logJson["component"] = *entry.component;
        }

        if (entry.unit) {
            logJson["unit"] = *entry.unit;
        }

        if (entry.hostname) {
            logJson["hostname"] = *entry.hostname;
        }

        if (entry.commit) {
            logJson["commit"] = *entry.commit;
        }

        if (entry.author) {
            logJson["author"] = *entry.author;
        }

        // Add metadata
        for (const auto& [key, value] : entry.metadata) {
            logJson[key] = value;
        }

        root["logs"].append(logJson);
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

std::string LoggingService::getLogSources() {
    // Create JSON response
    Json::Value root;
    root["sources"] = Json::Value(Json::arrayValue);

    // Add available log sources
    root["sources"].append("dashboard");
    root["sources"].append("system");
    root["sources"].append("nginx");
    root["sources"].append("git");
    root["sources"].append("auth");

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

std::string LoggingService::getLogStats() {
    std::lock_guard<std::mutex> lock(logsMutex_);

    // Create JSON response
    Json::Value root;

    // Count logs by source and type
    std::unordered_map<std::string, int> sourceCount;
    std::unordered_map<std::string, int> typeCount;

    for (const auto& entry : logs_) {
        sourceCount[entry.source]++;
        typeCount[getLogTypeString(entry.type)]++;
    }

    // Add source counts
    root["sources"] = Json::Value(Json::objectValue);
    for (const auto& [source, count] : sourceCount) {
        root["sources"][source] = count;
    }

    // Add type counts
    root["types"] = Json::Value(Json::objectValue);
    for (const auto& [type, count] : typeCount) {
        root["types"][type] = count;
    }

    // Add total count
    root["total"] = static_cast<int>(logs_.size());

    // Add oldest and newest log timestamps
    if (!logs_.empty()) {
        auto oldestIt = std::min_element(logs_.begin(), logs_.end(), [](const LogEntry& a, const LogEntry& b) {
            return a.timestamp < b.timestamp;
        });

        auto newestIt = std::max_element(logs_.begin(), logs_.end(), [](const LogEntry& a, const LogEntry& b) {
            return a.timestamp < b.timestamp;
        });

        root["oldest"] = formatTimestamp(oldestIt->timestamp);
        root["newest"] = formatTimestamp(newestIt->timestamp);
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

// WebSocket subscription handlers
void LoggingService::subscribeToLogs(std::shared_ptr<WebSocketSession> session, const std::string& source, const std::string& level) {
    std::lock_guard<std::mutex> lock(subscriptionsMutex_);

    // Add subscription
    subscriptions_[session] = {source, level};

    std::cout << "New WebSocket subscription: source=" << source << ", level=" << level << std::endl;
}

void LoggingService::unsubscribeFromLogs(std::shared_ptr<WebSocketSession> session) {
    std::lock_guard<std::mutex> lock(subscriptionsMutex_);

    // Remove subscription
    subscriptions_.erase(session);

    std::cout << "WebSocket unsubscribed" << std::endl;
}

std::vector<std::shared_ptr<logging::StorageTier>> LoggingService::getStorageTiers() const {
    return storageManager_->getTiers();
}

std::chrono::steady_clock::time_point LoggingService::getStartTime() const {
    return startTime_;
}