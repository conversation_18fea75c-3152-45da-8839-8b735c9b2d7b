# Certificate Access Configuration

This document describes how to configure certificate access for the Database Service.

## Certificate Requirements

The Database Service requires access to SSL certificates for:

1. **Secure Database Connections**: Connecting to PostgreSQL using SSL
2. **API Server**: Serving HTTPS requests (if enabled)
3. **Client Authentication**: Validating client certificates (if enabled)

## Certificate Locations

On the production server (git.chcit.org), certificates are stored in the following locations:

- **Let's Encrypt Certificates**: `/etc/letsencrypt/live/chcit.org/`
  - `cert.pem`: Server certificate
  - `privkey.pem`: Private key
  - `chain.pem`: Certificate chain
  - `fullchain.pem`: Full certificate chain

## Access Configuration

### Group-Based Access

The recommended approach is to add the database service user to the appropriate group:

1. Identify the group that has access to the certificates:
   ```bash
   ls -la /etc/letsencrypt/live/chcit.org/
   ```

2. Add the database service user to this group:
   ```bash
   sudo usermod -a -G ssl-cert btaylor-admin
   ```

3. Verify group membership:
   ```bash
   groups btaylor-admin
   ```

### Setup Script

The `setup-certificate-access.sh` script automates this process:

```bash
#!/bin/bash
# Script to set up certificate access for the Database Service

# Configuration
CERT_DIR="/etc/letsencrypt/live/chcit.org"
DB_USER="btaylor-admin"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root"
  exit 1
fi

# Get certificate group
CERT_GROUP=$(stat -c '%G' $CERT_DIR)
echo "Certificate directory group: $CERT_GROUP"

# Add database user to certificate group
usermod -a -G $CERT_GROUP $DB_USER
echo "Added $DB_USER to $CERT_GROUP group"

# Verify permissions
echo "Verifying permissions..."
ls -la $CERT_DIR

# Verify group membership
echo "Verifying group membership..."
groups $DB_USER

echo "Certificate access setup complete"
```

## Configuration File Settings

The Database Service configuration file needs to reference the certificate files:

```ini
[database]
ssl_enabled = true
ssl_cert_file = /etc/letsencrypt/live/chcit.org/cert.pem
ssl_key_file = /etc/letsencrypt/live/chcit.org/privkey.pem
ssl_ca_file = /etc/letsencrypt/live/chcit.org/chain.pem

[api]
ssl_enabled = true
ssl_cert_file = /etc/letsencrypt/live/chcit.org/fullchain.pem
ssl_key_file = /etc/letsencrypt/live/chcit.org/privkey.pem
```

## Security Considerations

### Principle of Least Privilege

The Database Service should have the minimum permissions necessary to access the certificates:

- Read-only access to certificate files
- No access to other sensitive files
- No ability to modify certificates

### Certificate Renewal

Let's Encrypt certificates are automatically renewed by the Certbot service. The Database Service needs to handle certificate changes:

1. **Reload on Signal**: The service should reload certificates when it receives a SIGHUP signal
2. **Periodic Reload**: The service should periodically check for certificate changes
3. **Graceful Handling**: The service should gracefully handle certificate expiration or renewal

### Monitoring

Monitor certificate expiration and access:

1. **Expiration Alerts**: Set up alerts for certificate expiration
2. **Access Logging**: Log all certificate access attempts
3. **Permission Changes**: Monitor for changes to certificate permissions

## Troubleshooting

### Common Issues

1. **Permission Denied**:
   - Verify group membership: `groups btaylor-admin`
   - Check file permissions: `ls -la /etc/letsencrypt/live/chcit.org/`
   - Verify directory permissions: `ls -la /etc/letsencrypt/live/`

2. **Certificate Not Found**:
   - Verify certificate path: `ls -la /etc/letsencrypt/live/chcit.org/`
   - Check symbolic links: `readlink -f /etc/letsencrypt/live/chcit.org/cert.pem`
   - Verify domain name: `certbot certificates`

3. **Certificate Expired**:
   - Check expiration date: `openssl x509 -enddate -noout -in /etc/letsencrypt/live/chcit.org/cert.pem`
   - Force renewal: `certbot renew --force-renewal`
   - Verify renewal service: `systemctl status certbot.timer`

### Diagnostic Commands

```bash
# Check certificate expiration
openssl x509 -enddate -noout -in /etc/letsencrypt/live/chcit.org/cert.pem

# Verify certificate chain
openssl verify -CAfile /etc/letsencrypt/live/chcit.org/chain.pem /etc/letsencrypt/live/chcit.org/cert.pem

# Test SSL connection
openssl s_client -connect localhost:5432 -CAfile /etc/letsencrypt/live/chcit.org/chain.pem
```

## Related Documentation

- [Installation Guide](./installation.md)
- [Configuration Validation](./configuration-validation.md)
- [Security Model](./architecture-and-design.md#security-model)
