# Security Features

The Database Service includes several security features to protect data and ensure secure access.

## Enhanced Token-based Authentication

The Database Service uses JW<PERSON> (JSON Web Token) for authentication with the following features:

### Access and Refresh Tokens

- **Access Tokens**: Short-lived tokens used for API access
- **Refresh Tokens**: Longer-lived tokens used to obtain new access tokens
- **Token Expiration**: Configurable expiration times for both token types
- **Token Renewal**: Ability to refresh access tokens without re-authentication

### JWT Implementation

- **Token Format**: Standard JWT format with header, payload, and signature
- **Signature Algorithm**: HMAC-SHA256
- **Token Payload**: Contains user information, token type, and expiration time
- **Token Validation**: Validates token signature and expiration

## SSL Support

The Database Service supports SSL/TLS for secure communication:

- **SSL Configuration**: Enable/disable SSL in the configuration file
- **Certificate Paths**: Configure paths to SSL certificate and key files
- **Certificate Verification**: Options for certificate verification mode

## CORS Support

Cross-Origin Resource Sharing (CORS) support for web clients:

- **Allowed Origins**: Configure which origins can access the API
- **Allowed Methods**: Configure which HTTP methods are allowed
- **Allowed Headers**: Configure which headers are allowed
- **Credentials Support**: Configure whether credentials are allowed
- **Preflight Requests**: Proper handling of OPTIONS preflight requests

## Secure Credential Storage

Secure storage for sensitive credentials:

- **Encryption**: AES-256 encryption for stored credentials
- **Key Management**: Secure key management for encryption keys
- **API Access Control**: Admin-only access to credential management APIs

## API Endpoints

### Authentication

```
POST /api/auth/login
```

Authenticates a user and returns access and refresh tokens.

**Request Body**:
```json
{
  "username": "admin",
  "password": "password"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": "admin",
    "token_type": "Bearer"
  }
}
```

### Token Refresh

```
POST /api/auth/refresh
```

Refreshes an access token using a refresh token.

**Request Body**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer"
  }
}
```

### Logout

```
POST /api/auth/logout
```

Invalidates all tokens for the current user.

**Authentication Required**: Yes

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Logged out successfully"
  }
}
```

### User Information

```
GET /api/auth/user
```

Returns information about the current user.

**Authentication Required**: Yes

**Response**:
```json
{
  "success": true,
  "data": {
    "username": "admin",
    "email": "<EMAIL>",
    "is_admin": true,
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### Credential Management

```
POST /api/credentials/store
```

Stores a credential securely.

**Authentication Required**: Yes (Admin only)

**Request Body**:
```json
{
  "key": "api_key",
  "value": "secret_api_key"
}
```

```
GET /api/credentials/get?key=api_key
```

Retrieves a stored credential.

**Authentication Required**: Yes (Admin only)

```
DELETE /api/credentials/remove?key=api_key
```

Removes a stored credential.

**Authentication Required**: Yes (Admin only)

## Configuration

Security features can be configured in the `config.json` file:

```json
{
  "security": {
    "enable_authentication": true,
    "jwt_secret": "change-this-to-a-secure-secret-in-production",
    "token_expiration_seconds": 3600,
    "refresh_token_expiration_seconds": 86400,
    "password_hash_algorithm": "bcrypt",
    "password_hash_cost": 12,
    "secure_credential_storage": {
      "enabled": true,
      "encryption_key": "change-this-to-a-secure-encryption-key"
    }
  },
  "api": {
    "ssl": {
      "enabled": false,
      "cert_path": "/etc/letsencrypt/live/example.com/fullchain.pem",
      "key_path": "/etc/letsencrypt/live/example.com/privkey.pem"
    },
    "cors": {
      "enabled": true,
      "allowed_origins": ["*"],
      "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      "allowed_headers": ["Content-Type", "Authorization"],
      "allow_credentials": true,
      "max_age": 86400
    }
  }
}
```
