/* responsive.css - Media queries and responsive adjustments */

/* Responsive container adjustments */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }
}

@media (max-width: 992px) {
    /* Adjust chart heights for medium screens */
    .chart-container {
        height: 250px;
    }
    
    /* Reduce card padding */
    .card-body {
        padding: 0.75rem;
    }
    
    /* Remove unnecessary padding in nested cards */
    .card .card .card-body {
        padding: 0.5rem;
    }
}

@media (max-width: 768px) {
    /* Mobile layout adjustments */
    .container {
        padding: 10px;
        height: calc(100vh - 20px); /* 20px accounts for top and bottom padding */
        max-width: 98%;
    }
    
    /* Adjust dashboard header for mobile */
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 10px;
    }
    
    .dashboard-title {
        margin-bottom: 10px;
    }
    
    /* Make tabs more compact on mobile */
    .nav-tabs .nav-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
    
    .nav-tabs .nav-link i {
        margin-right: 3px;
    }
    
    /* Adjust metrics on mobile */
    .metric-value {
        font-size: 18px;
    }
    
    .metric-label {
        font-size: 11px;
    }
    
    /* Mobile log display adjustments */
    .component-header {
        flex-wrap: wrap;
        padding: 8px;
    }
    
    .component-header h5 {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .log-count-control select {
        min-width: 70px;
    }
    
    .log-count-control label {
        display: none;
    }
    
    /* Ensure consistent tab spacing on mobile too */
    .nav-tabs {
        margin-bottom: 10px;
    }
    
    .tab-content {
        margin-top: 0;
    }
    
    .tab-pane {
        margin-top: 0;
    }
    
    .tab-pane > .card {
        margin-top: 0;
    }
}

@media (max-width: 576px) {
    /* Small mobile adjustments */
    .container {
        padding: 5px;
        height: calc(100vh - 10px); /* 10px accounts for top and bottom padding */
        max-width: 100%;
    }
    
    /* Simplify dashboard title */
    .dashboard-title h1 {
        font-size: 1.1rem;
    }
    
    /* Further adjust spacing */
    .card-header {
        padding: 0.5rem 0.75rem;
    }
    
    .card-body {
        padding: 0.5rem;
    }
    
    /* Stack items in headers */
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-header > div:last-child {
        margin-top: 5px;
        width: 100%;
    }
    
    /* Switch to vertical layout for metrics */
    .col-6 {
        width: 100%;
    }
    
    /* Minimize badges to save space */
    .badge {
        padding: 0.25em 0.4em;
        font-size: 75%;
    }
    
    /* Adjust log timestamp display */
    .log-timestamp {
        min-width: 100px;
        font-size: 0.7rem;
    }
    
    /* Hide some elements on very small screens */
    .d-sm-none {
        display: none !important;
    }
}
