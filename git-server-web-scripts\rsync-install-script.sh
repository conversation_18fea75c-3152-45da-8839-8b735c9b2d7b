#!/bin/bash
# Installation script for rsync-based certificate sync
# Run this script as root

# Color codes for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting installation of rsync-based certificate sync...${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}This script must be run as root${NC}"
    exit 1
fi

# Check for rsync
if ! command -v rsync >/dev/null 2>&1; then
    echo -e "${YELLOW}Installing rsync...${NC}"
    apt-get update && apt-get install -y rsync
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p /opt/git-dashboard/bin
mkdir -p /opt/git-dashboard/logs
mkdir -p /home/<USER>/letsencrypt_backup

# Set permissions for log directory
chmod 755 /opt/git-dashboard/logs
touch /opt/git-dashboard/logs/cert-sync.log
chmod 666 /opt/git-dashboard/logs/cert-sync.log

# Set ownership for backup directory
chown btaylor-admin:btaylor-admin /home/<USER>/letsencrypt_backup

# Install the updated sync script
echo "Installing updated sync script..."
cat > /opt/git-dashboard/sync-certificates.sh << 'EOF'
#!/bin/bash

# SSL Certificate Sync Script for Git Dashboard
# This script can be run as root directly (via cron) or via the cert_sync_helper
# Uses rsync to transfer certificates from main server to local backup directory

# Configuration
MAIN_SERVER="***********"
GIT_DASHBOARD="***********"
MAIN_SERVER_USER="btaylor-admin"
CERT_DIR="/etc/letsencrypt"
DOMAIN="chcit.org"
LOG_DIR="/home/<USER>/letsencrypt_backup_logs"
SYNC_LOG="$LOG_DIR/cert-sync.log"
# Using ed25519 SSH key
SSH_KEY="/home/<USER>/.ssh/id_ed25519"
# Backup directory for certificates
BACKUP_DIR="/home/<USER>/letsencrypt_backup"

# Function to log messages
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1"
    echo "[$timestamp] $1" >> "$SYNC_LOG" 2>/dev/null || true
}

# Add diagnostic logging
log "Script environment details:"
log "Running as user: $(id -un)"
log "User home: $HOME"
log "SSH_KEY value: $SSH_KEY"
log "SSH_KEY exists: $([ -f "$SSH_KEY" ] && echo 'Yes' || echo 'No')"
log "btaylor-admin user exists: $(id btaylor-admin >/dev/null 2>&1 && echo 'Yes' || echo 'No')"
log "Current working directory: $(pwd)"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR" 2>/dev/null || true
touch "$SYNC_LOG" 2>/dev/null || true

# Make log directory accessible
chmod 755 "$LOG_DIR" 2>/dev/null || true
chmod 666 "$SYNC_LOG" 2>/dev/null || true

log "Starting certificate sync process"

# Create certificate directories if they don't exist
log "Creating certificate directories"
mkdir -p "$CERT_DIR/archive/$DOMAIN" "$CERT_DIR/live/$DOMAIN" 2>/dev/null || true

# Check if rsync is available
if ! command -v rsync >/dev/null 2>&1; then
    log "Error: rsync command not found"
    exit 1
fi

# Create backup directory if it doesn't exist
if ! [ -d "$BACKUP_DIR" ]; then
    log "Creating backup directory $BACKUP_DIR"
    if [ "$(id -un)" = "btaylor-admin" ]; then
        mkdir -p "$BACKUP_DIR" 2>/dev/null || true
    else
        sudo -u btaylor-admin mkdir -p "$BACKUP_DIR" 2>/dev/null || true
    fi
    
    if ! [ -d "$BACKUP_DIR" ]; then
        log "Error: Failed to create backup directory"
        exit 1
    fi
fi

# Ensure backup directory has correct permissions
if [ "$(id -un)" = "btaylor-admin" ]; then
    mkdir -p "$BACKUP_DIR/archive/$DOMAIN" "$BACKUP_DIR/live/$DOMAIN" 2>/dev/null || true
else
    sudo -u btaylor-admin mkdir -p "$BACKUP_DIR/archive/$DOMAIN" "$BACKUP_DIR/live/$DOMAIN" 2>/dev/null || true
fi

# Step 1: Test connection to main server
log "Testing connection to $MAIN_SERVER"

# Use appropriate command based on current user
if [ "$(id -un)" = "btaylor-admin" ]; then
    SSH_TEST=$(ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "${MAIN_SERVER_USER}@${MAIN_SERVER}" "echo Connection successful" 2>&1)
else
    SSH_TEST=$(sudo -u btaylor-admin ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "${MAIN_SERVER_USER}@${MAIN_SERVER}" "echo Connection successful" 2>&1)
fi

# Check if connection was successful
if ! echo "$SSH_TEST" | grep -q "Connection successful"; then
    log "Error: Failed to connect to $MAIN_SERVER. Output: $SSH_TEST"
    exit 1
fi

log "SSH connection successful"

# Step 2: Sync certificates using rsync
log "Syncing certificates from $MAIN_SERVER using rsync"

# Sync archive directory
log "Syncing archive directory"
if [ "$(id -un)" = "btaylor-admin" ]; then
    RSYNC_CMD="rsync -avz --timeout=30 -e 'ssh -i $SSH_KEY' ${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/archive/ $BACKUP_DIR/archive/"
    rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/archive/" "$BACKUP_DIR/archive/"
else
    RSYNC_CMD="sudo -u btaylor-admin rsync -avz --timeout=30 -e 'ssh -i $SSH_KEY' ${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/archive/ $BACKUP_DIR/archive/"
    sudo -u btaylor-admin rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/archive/" "$BACKUP_DIR/archive/"
fi

# Check if rsync was successful
if [ $? -ne 0 ]; then
    log "Error: Failed to sync archive directory"
    exit 1
fi

# Sync live directory
log "Syncing live directory"
if [ "$(id -un)" = "btaylor-admin" ]; then
    rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/live/" "$BACKUP_DIR/live/"
else
    sudo -u btaylor-admin rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/live/" "$BACKUP_DIR/live/"
fi

# Check if rsync was successful
if [ $? -ne 0 ]; then
    log "Error: Failed to sync live directory"
    exit 1
fi

log "Certificate sync from remote server completed successfully"

# Step 3: Copy certificates to their proper locations
log "Copying certificates to proper locations"

# Copy live certificates
if [ -d "$BACKUP_DIR/live/$DOMAIN" ]; then
    cp -a "$BACKUP_DIR/live/$DOMAIN/"* "$CERT_DIR/live/$DOMAIN/" || log "Warning: Failed to copy live certificates"
else
    log "Error: Live certificates directory not found in backup"
    exit 1
fi

# Copy archive certificates
if [ -d "$BACKUP_DIR/archive/$DOMAIN" ]; then
    cp -a "$BACKUP_DIR/archive/$DOMAIN/"* "$CERT_DIR/archive/$DOMAIN/" || log "Warning: Failed to copy archive certificates"
else
    log "Error: Archive certificates directory not found in backup"
    exit 1
fi

# Step 4: Set correct permissions
log "Setting correct permissions"
chown -R root:root "$CERT_DIR" 2>/dev/null || log "Warning: Failed to set ownership on cert directory"
chmod -R 644 "$CERT_DIR" 2>/dev/null || log "Warning: Failed to set permissions on cert directory"
find "$CERT_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || log "Warning: Failed to set directory permissions"
find "$CERT_DIR/archive" -name "privkey*.pem" -exec chmod 640 {} \; 2>/dev/null || true
find "$CERT_DIR/live" -name "privkey*.pem" -exec chmod 640 {} \; 2>/dev/null || true

# Detect Nginx group automatically
NGINX_GROUP=$(grep -i "^user" /etc/nginx/nginx.conf | awk '{print $2}' | sed 's/;$//')
if [ -z "$NGINX_GROUP" ]; then
    # Fallback to common default groups
    if getent group www-data >/dev/null; then
        NGINX_GROUP="www-data"
    elif getent group nginx >/dev/null; then
        NGINX_GROUP="nginx"
    else
        NGINX_GROUP="root"  # Last resort
        log "Warning: Could not determine Nginx group, using root"
    fi
fi

log "Using Nginx group: $NGINX_GROUP"

# Add these lines to ensure Nginx can read the private key
chgrp $NGINX_GROUP "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"
chmod 640 "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to set permissions on privkey.pem"

# Step 5: Verify certificate files exist
log "Verifying certificate files"
MISSING_FILES=0
for file in fullchain.pem privkey.pem chain.pem cert.pem; do
    if [ ! -f "$CERT_DIR/live/$DOMAIN/$file" ]; then
        log "Error: Missing certificate file $file"
        MISSING_FILES=1
    fi
done

if [ $MISSING_FILES -eq 1 ]; then
    log "Certificate sync failed: Missing certificate files"
    exit 1
fi

# Step 6: Reload Nginx to apply new certificates
log "Reloading Nginx"
if ! systemctl reload nginx 2>/dev/null; then
    # Try alternate methods if systemctl fails
    if command -v nginx >/dev/null 2>&1; then
        nginx -s reload 2>/dev/null || log "Warning: Failed to reload Nginx via nginx -s reload"
    elif [ -x /etc/init.d/nginx ]; then
        /etc/init.d/nginx reload 2>/dev/null || log "Warning: Failed to reload Nginx via init script"
    else
        log "Warning: Failed to reload Nginx - please reload manually"
    fi
fi

# Step 7: Test HTTPS connection
log "Testing HTTPS connection"
if command -v curl >/dev/null 2>&1 && curl -sf https://$GIT_DASHBOARD/ > /dev/null 2>&1; then
    log "Certificate sync completed successfully"
else
    log "Warning: HTTPS test failed after sync, but certificates may still be installed correctly"
fi

log "Certificate sync process completed"
exit 0
EOF

# Set permissions on sync script
chmod 755 /opt/git-dashboard/sync-certificates.sh
chown root:root /opt/git-dashboard/sync-certificates.sh

# Install the C++ helper
echo "Installing C++ helper..."
cat > /opt/git-dashboard/bin/cert_sync_helper.cpp << 'EOF'
// cert_sync_helper.cpp
// A setuid helper program to safely run the certificate sync script
// Compile with:
// g++ -std=c++17 -o cert_sync_helper cert_sync_helper.cpp
// Then set permissions:
// sudo chown root:www-data cert_sync_helper
// sudo chmod 4750 cert_sync_helper

#include <iostream>
#include <string>
#include <array>
#include <memory>
#include <stdexcept>
#include <cstring>
#include <cerrno>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <grp.h>
#include <syslog.h>

// Path to the certificate sync script (constant and not modifiable)
constexpr const char* SYNC_SCRIPT = "/opt/git-dashboard/sync-certificates.sh";

// Log a message to syslog and stdout
void log_message(const std::string& message, bool is_error = false) {
    int priority = is_error ? LOG_ERR : LOG_INFO;
    syslog(priority, "%s", message.c_str());
    if (is_error) {
        std::cerr << "ERROR: " << message << std::endl;
    } else {
        std::cout << message << std::endl;
    }
}

// Execute the sync script and capture output
std::string exec_sync_script() {
    std::array<char, 4096> buffer;
    std::string result;
    
    // Use sudo to run as btaylor-admin which has SSH key access
    std::unique_ptr<FILE, decltype(&pclose)> pipe(
        popen("sudo -u btaylor-admin /opt/git-dashboard/sync-certificates.sh", "r"), 
        pclose
    );
    
    if (!pipe) {
        throw std::runtime_error("popen() failed: " + std::string(strerror(errno)));
    }
    
    // Read command output
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    
    return result;
}

// Check if the user is in the www-data group or is www-data user
bool is_www_data_user_or_group() {
    // Get user info
    uid_t uid = getuid(); // Use real user ID, not effective user ID
    struct passwd *pw = getpwuid(uid);
    if (!pw) {
        log_message("Failed to get user info", true);
        return false;
    }
    
    // Check if user is www-data
    if (strcmp(pw->pw_name, "www-data") == 0) {
        return true;
    }
    
    // Get www-data group info
    struct group *gr = getgrnam("www-data");
    if (!gr) {
        log_message("Failed to get www-data group info", true);
        return false;
    }
    
    // Check if user is in www-data group
    for (char **members = gr->gr_mem; *members != nullptr; members++) {
        if (strcmp(*members, pw->pw_name) == 0) {
            return true;
        }
    }
    
    // Also check if user's primary group is www-data
    return pw->pw_gid == gr->gr_gid;
}

int main() {
    try {
        // Set up syslog
        openlog("cert_sync_helper", LOG_PID | LOG_PERROR, LOG_DAEMON);
        
        // Log the user information for debugging
        uid_t real_uid = getuid();
        uid_t effective_uid = geteuid();
        struct passwd *pw_real = getpwuid(real_uid);
        struct passwd *pw_effective = getpwuid(effective_uid);
        
        std::string real_user = pw_real ? pw_real->pw_name : "unknown";
        std::string effective_user = pw_effective ? pw_effective->pw_name : "unknown";
        
        log_message("Real UID: " + std::to_string(real_uid) + " (" + real_user + ")");
        log_message("Effective UID: " + std::to_string(effective_uid) + " (" + effective_user + ")");
        
        // Security check: only allow www-data user or group to run this
        if (!is_www_data_user_or_group()) {
            log_message("Access denied: Only www-data user or group can run this helper", true);
            return 1;
        }
        
        log_message("Certificate sync helper started by user " + real_user);
        
        // Execute the sync script
        std::string output = exec_sync_script();
        
        // Send the output as JSON response
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"success\",\n";
        std::cout << "  \"message\": \"Certificate sync completed\",\n";
        std::cout << "  \"output\": \"" << output << "\"\n";
        std::cout << "}\n";
        
        log_message("Certificate sync completed successfully");
        return 0;
    } 
    catch (const std::exception& e) {
        log_message(std::string("Error: ") + e.what(), true);
        
        // Send error as JSON
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"error\",\n";
        std::cout << "  \"message\": \"" << e.what() << "\"\n";
        std::cout << "}\n";
        
        return 1;
    }
}
EOF

# Compile the helper
echo "Compiling C++ helper..."
cd /opt/git-dashboard/bin
g++ -std=c++17 -o cert_sync_helper cert_sync_helper.cpp

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to compile C++ helper${NC}"
    exit 1
fi

# Set permissions on helper
echo "Setting permissions on helper..."
chown root:www-data /opt/git-dashboard/bin/cert_sync_helper
chmod 4750 /opt/git-dashboard/bin/cert_sync_helper

# Add sudoers entry to allow www-data to run the script as btaylor-admin
echo "Adding sudoers entry..."
cat > /etc/sudoers.d/git-dashboard << 'EOF'
# Allow www-data to run the certificate sync script as btaylor-admin without a password
www-data ALL=(btaylor-admin) NOPASSWD: /opt/git-dashboard/sync-certificates.sh
EOF

# Set proper permissions on sudoers file
chmod 440 /etc/sudoers.d/git-dashboard

# Verify permissions
echo -e "${GREEN}Verifying permissions...${NC}"
echo "Sync script: $(ls -la /opt/git-dashboard/sync-certificates.sh)"
echo "C++ helper: $(ls -la /opt/git-dashboard/bin/cert_sync_helper)"
echo "Backup directory: $(ls -la /home/<USER>/letsencrypt_backup)"
echo "Sudoers file: $(ls -la /etc/sudoers.d/git-dashboard)"

# Restart services if necessary
echo "Checking if services need to be restarted..."
if systemctl is-active --quiet git-dashboard.service; then
    echo "Restarting Git Dashboard service..."
    systemctl restart git-dashboard.service
fi

if systemctl is-active --quiet nginx.service; then
    echo "Reloading Nginx service..."
    systemctl reload nginx.service
fi

echo -e "${GREEN}Installation completed successfully!${NC}"
echo "You can now use the 'Sync Certificate' button on the dashboard."
echo "For manual testing, you can run:"
echo "  As root: /opt/git-dashboard/sync-certificates.sh"
echo "  As btaylor-admin: sudo -u btaylor-admin /opt/git-dashboard/sync-certificates.sh"
echo "  As www-data: sudo -u www-data /opt/git-dashboard/bin/cert_sync_helper"
echo ""
echo "Log file will be at: /opt/git-dashboard/logs/cert-sync.log"
