{"project": {"local_source_dir": "D:\\Augment\\project-tracker\\database-service", "name": "database-service", "remote_install_dir": "/opt/database-service", "description": "database-service for production environment", "remote_build_dir": "/home/<USER>/database-service-build"}, "ssh": {"local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa", "username": "btaylor-admin", "port": 22, "host": "git.chcit.org"}, "service": {"user": "database-service", "name": "database-service", "group": "database-service", "description": "database-service for production environment"}, "database": {"username": "database_service_prod", "name": "database_service_prod", "port": 5432, "password": "", "host": "localhost"}, "version": {"number": 1, "updated": "2025-04-15 22:25:00", "created": "2025-04-15 22:25:00"}}