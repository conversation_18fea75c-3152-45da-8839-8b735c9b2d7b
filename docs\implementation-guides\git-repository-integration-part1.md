# Git Repository Integration Guide - Part 1: Basic Integration and Core Features

This guide provides detailed instructions for integrating the Git Repository Service with other applications in the Project Tracker ecosystem.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Integration Steps](#integration-steps)
4. [Client Library Integration](#client-library-integration)
5. [Repository Management](#repository-management)
6. [Commit History](#commit-history)
7. [Branch and Tag Management](#branch-and-tag-management)

## Overview

The Git Repository Service provides a centralized Git repository management solution for all applications in the Project Tracker ecosystem. This guide explains how to integrate your application with the Git Repository Service using the provided client library.

## Prerequisites

Before integrating with the Git Repository Service, ensure you have:

1. **Git Repository Service Installed**: The Git Repository Service should be installed and running on your server
2. **Database Service Installed**: The Database Service should be installed and configured
3. **C++23 Compiler**: A C++23 compatible compiler (GCC 11+, Clang 14+, MSVC 19.30+)
4. **Client Library**: The Git Repository Service client library
5. **API Key**: An API key for authenticating with the Git Repository Service

## Integration Steps

### Step 1: Include the Client Library

Add the Git Repository Service client library to your project:

```cpp
#include "git-repo-service/client/repository_client.hpp"
```

### Step 2: Create a Repository Client

Create a repository client instance:

```cpp
// Create repository client
git::client::RepositoryClient repoClient(
    "http://localhost:8081",  // Git Repository Service URL
    "your-application",       // Application name
    "your-api-key"            // API key
);
```

### Step 3: Check Service Availability

Check if the Git Repository Service is available:

```cpp
// Check if service is available
if (!repoClient.isAvailable()) {
    std::cerr << "Git Repository Service is not available" << std::endl;
    return 1;
}

// Get service version
std::string version = repoClient.getVersion();
std::cout << "Connected to Git Repository Service version: " << version << std::endl;
```

### Step 4: List Repositories

List available repositories:

```cpp
// List repositories
auto repositories = repoClient.listRepositories();

// Display repositories
for (const auto& repo : repositories) {
    std::cout << "Repository: " << repo["name"].get<std::string>() << std::endl;
    std::cout << "  Owner: " << repo["owner"].get<std::string>() << std::endl;
    std::cout << "  Path: " << repo["path"].get<std::string>() << std::endl;
    std::cout << "  Created: " << repo["created_at"].get<std::string>() << std::endl;
    std::cout << "  Updated: " << repo["updated_at"].get<std::string>() << std::endl;
}
```

### Step 5: Get Repository Details

Get details for a specific repository:

```cpp
// Get repository details
auto repo = repoClient.getRepository("example-repo");

// Display repository details
std::cout << "Repository: " << repo["name"].get<std::string>() << std::endl;
std::cout << "  Owner: " << repo["owner"].get<std::string>() << std::endl;
std::cout << "  Path: " << repo["path"].get<std::string>() << std::endl;
std::cout << "  Description: " << repo["description"].get<std::string>() << std::endl;
std::cout << "  Default Branch: " << repo["default_branch"].get<std::string>() << std::endl;
std::cout << "  Created: " << repo["created_at"].get<std::string>() << std::endl;
std::cout << "  Updated: " << repo["updated_at"].get<std::string>() << std::endl;
```

## Client Library Integration

### Repository Client Class

Create a repository client class for your application:

```cpp
class GitRepositoryManager {
public:
    static GitRepositoryManager& getInstance() {
        static GitRepositoryManager instance;
        return instance;
    }
    
    void initialize(const std::string& appName, const std::string& apiKey) {
        client_ = std::make_unique<git::client::RepositoryClient>(
            "http://localhost:8081",
            appName,
            apiKey
        );
    }
    
    auto listRepositories() {
        return client_->listRepositories();
    }
    
    auto getRepository(const std::string& name) {
        return client_->getRepository(name);
    }
    
    auto createRepository(const std::string& name, const std::string& owner, 
                         const std::string& description = "") {
        return client_->createRepository(name, owner, description);
    }
    
    auto deleteRepository(const std::string& name) {
        return client_->deleteRepository(name);
    }
    
    auto getCommits(const std::string& repository, int limit = 10) {
        return client_->getCommits(repository, limit);
    }
    
    auto getBranches(const std::string& repository) {
        return client_->getBranches(repository);
    }
    
    auto getTags(const std::string& repository) {
        return client_->getTags(repository);
    }
    
private:
    GitRepositoryManager() = default;
    ~GitRepositoryManager() = default;
    
    std::unique_ptr<git::client::RepositoryClient> client_;
};
```

### Error Handling

Implement proper error handling:

```cpp
try {
    // Get repository details
    auto repo = repoClient.getRepository("non-existent-repo");
} catch (const git::client::RepositoryNotFoundError& e) {
    std::cerr << "Repository not found: " << e.what() << std::endl;
} catch (const git::client::AuthenticationError& e) {
    std::cerr << "Authentication error: " << e.what() << std::endl;
} catch (const git::client::ConnectionError& e) {
    std::cerr << "Connection error: " << e.what() << std::endl;
} catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
}
```

### Asynchronous Operations

For long-running operations, use asynchronous methods:

```cpp
// Get commits asynchronously
auto future = repoClient.getCommitsAsync("large-repository", 1000);

// Do other work while the operation is executing
// ...

// Get the results
try {
    auto commits = future.get();
    
    // Process commits
    for (const auto& commit : commits) {
        std::cout << "Commit: " << commit["hash"].get<std::string>() << std::endl;
        std::cout << "  Author: " << commit["author"].get<std::string>() << std::endl;
        std::cout << "  Message: " << commit["message"].get<std::string>() << std::endl;
    }
} catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
}
```

## Repository Management

### Creating a Repository

Create a new Git repository:

```cpp
// Create repository
auto result = repoClient.createRepository(
    "new-repository",
    "btaylor-admin",
    "A new repository created via API"
);

// Display result
std::cout << "Repository created: " << result["name"].get<std::string>() << std::endl;
std::cout << "  ID: " << result["id"].get<int>() << std::endl;
std::cout << "  Path: " << result["path"].get<std::string>() << std::endl;
```

### Updating a Repository

Update an existing repository:

```cpp
// Update repository
auto result = repoClient.updateRepository(
    "example-repo",
    {
        {"description", "Updated repository description"},
        {"default_branch", "main"}
    }
);

// Display result
std::cout << "Repository updated: " << result["name"].get<std::string>() << std::endl;
std::cout << "  Description: " << result["description"].get<std::string>() << std::endl;
std::cout << "  Default Branch: " << result["default_branch"].get<std::string>() << std::endl;
```

### Deleting a Repository

Delete a repository:

```cpp
// Delete repository
bool success = repoClient.deleteRepository("old-repository");

// Display result
if (success) {
    std::cout << "Repository deleted successfully" << std::endl;
} else {
    std::cerr << "Failed to delete repository" << std::endl;
}
```

### Repository Metadata

Store and retrieve repository metadata:

```cpp
// Store repository metadata
repoClient.setMetadata(
    "example-repo",
    {
        {"language", "C++"},
        {"team", "Backend"},
        {"priority", "high"},
        {"tags", {"api", "core", "backend"}}
    }
);

// Get repository metadata
auto metadata = repoClient.getMetadata("example-repo");

// Display metadata
std::cout << "Repository metadata:" << std::endl;
std::cout << "  Language: " << metadata["language"].get<std::string>() << std::endl;
std::cout << "  Team: " << metadata["team"].get<std::string>() << std::endl;
std::cout << "  Priority: " << metadata["priority"].get<std::string>() << std::endl;
std::cout << "  Tags: ";
for (const auto& tag : metadata["tags"]) {
    std::cout << tag.get<std::string>() << " ";
}
std::cout << std::endl;
```

## Commit History

### Getting Commits

Get commit history for a repository:

```cpp
// Get commits
auto commits = repoClient.getCommits("example-repo", 10);

// Display commits
for (const auto& commit : commits) {
    std::cout << "Commit: " << commit["hash"].get<std::string>() << std::endl;
    std::cout << "  Author: " << commit["author"].get<std::string>() << std::endl;
    std::cout << "  Email: " << commit["email"].get<std::string>() << std::endl;
    std::cout << "  Date: " << commit["date"].get<std::string>() << std::endl;
    std::cout << "  Message: " << commit["message"].get<std::string>() << std::endl;
}
```

### Filtering Commits

Filter commits by various criteria:

```cpp
// Filter commits by author
auto authorCommits = repoClient.getCommits(
    "example-repo",
    {
        {"author", "btaylor-admin"},
        {"limit", 10}
    }
);

// Filter commits by date range
auto dateRangeCommits = repoClient.getCommits(
    "example-repo",
    {
        {"since", "2023-04-01T00:00:00Z"},
        {"until", "2023-04-30T23:59:59Z"},
        {"limit", 10}
    }
);

// Filter commits by file
auto fileCommits = repoClient.getCommits(
    "example-repo",
    {
        {"path", "src/main.cpp"},
        {"limit", 10}
    }
);

// Filter commits by message
auto messageCommits = repoClient.getCommits(
    "example-repo",
    {
        {"message", "fix"},
        {"limit", 10}
    }
);
```

### Getting Commit Details

Get detailed information for a specific commit:

```cpp
// Get commit details
auto commit = repoClient.getCommit("example-repo", "abcdef123456");

// Display commit details
std::cout << "Commit: " << commit["hash"].get<std::string>() << std::endl;
std::cout << "  Author: " << commit["author"].get<std::string>() << std::endl;
std::cout << "  Email: " << commit["email"].get<std::string>() << std::endl;
std::cout << "  Date: " << commit["date"].get<std::string>() << std::endl;
std::cout << "  Message: " << commit["message"].get<std::string>() << std::endl;
std::cout << "  Files Changed: " << commit["files_changed"].get<int>() << std::endl;
std::cout << "  Insertions: " << commit["insertions"].get<int>() << std::endl;
std::cout << "  Deletions: " << commit["deletions"].get<int>() << std::endl;

// Display changed files
std::cout << "  Changed Files:" << std::endl;
for (const auto& file : commit["files"]) {
    std::cout << "    " << file["path"].get<std::string>() << " ("
              << file["insertions"].get<int>() << " insertions, "
              << file["deletions"].get<int>() << " deletions)" << std::endl;
}
```

## Branch and Tag Management

### Getting Branches

Get branches for a repository:

```cpp
// Get branches
auto branches = repoClient.getBranches("example-repo");

// Display branches
for (const auto& branch : branches) {
    std::cout << "Branch: " << branch["name"].get<std::string>() << std::endl;
    std::cout << "  Commit: " << branch["commit"].get<std::string>() << std::endl;
    std::cout << "  Protected: " << (branch["protected"].get<bool>() ? "Yes" : "No") << std::endl;
}
```

### Creating a Branch

Create a new branch:

```cpp
// Create branch
auto result = repoClient.createBranch(
    "example-repo",
    "feature/new-feature",
    "main"  // Base branch
);

// Display result
std::cout << "Branch created: " << result["name"].get<std::string>() << std::endl;
std::cout << "  Commit: " << result["commit"].get<std::string>() << std::endl;
```

### Deleting a Branch

Delete a branch:

```cpp
// Delete branch
bool success = repoClient.deleteBranch("example-repo", "feature/old-feature");

// Display result
if (success) {
    std::cout << "Branch deleted successfully" << std::endl;
} else {
    std::cerr << "Failed to delete branch" << std::endl;
}
```

### Getting Tags

Get tags for a repository:

```cpp
// Get tags
auto tags = repoClient.getTags("example-repo");

// Display tags
for (const auto& tag : tags) {
    std::cout << "Tag: " << tag["name"].get<std::string>() << std::endl;
    std::cout << "  Commit: " << tag["commit"].get<std::string>() << std::endl;
    std::cout << "  Message: " << tag["message"].get<std::string>() << std::endl;
}
```

### Creating a Tag

Create a new tag:

```cpp
// Create tag
auto result = repoClient.createTag(
    "example-repo",
    "v1.0.0",
    "main",  // Target branch or commit
    "Version 1.0.0 release"
);

// Display result
std::cout << "Tag created: " << result["name"].get<std::string>() << std::endl;
std::cout << "  Commit: " << result["commit"].get<std::string>() << std::endl;
std::cout << "  Message: " << result["message"].get<std::string>() << std::endl;
```

### Deleting a Tag

Delete a tag:

```cpp
// Delete tag
bool success = repoClient.deleteTag("example-repo", "v0.9.0");

// Display result
if (success) {
    std::cout << "Tag deleted successfully" << std::endl;
} else {
    std::cerr << "Failed to delete tag" << std::endl;
}
```

