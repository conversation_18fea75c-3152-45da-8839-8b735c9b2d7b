/**
 * API Endpoint Check
 * Tests API endpoints for proper functionality
 */

const ApiCheck = {
    // API endpoints to test
    endpoints: [
        '/api/metrics/current',
        '/api/metrics/history?timeWindow=15m',
        '/api/git/repositories',
        '/api/certificates/status',
        '/api/git/repository/project-tracker/commits?days=30', // Changed from example-repo to project-tracker
        '/api/logs?get_all=true'
    ],

    /**
     * Initialize the API Check tool
     */
    init() {
        console.log('Initializing API Check...');

        // Only create the API check card if it doesn't exist already
        if (!document.getElementById('api-check-card')) {
            // Create the API check card
            this.createApiCheckCard();
        }

        // Add event listener for Run API Tests button (always do this to ensure it works)
        const runButton = document.getElementById('run-api-tests');
        if (runButton) {
            // Remove any existing event listeners
            runButton.replaceWith(runButton.cloneNode(true));

            // Get the fresh button and add the event listener
            const freshButton = document.getElementById('run-api-tests');
            if (freshButton) {
                console.log('Adding event listener to Run API Tests button');
                freshButton.addEventListener('click', () => {
                    console.log('Run API Tests button clicked');
                    this.testAllEndpoints();
                });
            }
        }
    },

    /**
     * Create the API Check card in the UI
     */
    createApiCheckCard() {
        const container = document.getElementById('troubleshooting-container');
        if (!container) {
            console.error('Troubleshooting container not found');
            return;
        }

        // Create a column for the API check card
        const column = document.createElement('div');
        column.className = 'col-md-6'; // Removed mb-4 to fix spacing
        column.id = 'api-check-card';

        // Create the card
        column.innerHTML = `
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-exchange-alt me-2"></i>API Endpoint Tests
                </div>
                <div class="card-body overflow-auto">
                    <button id="run-api-tests" class="btn btn-info mb-3">
                        <i class="fas fa-play me-2"></i>Run API Tests
                    </button>

                    <div id="api-test-output" class="mt-3">
                        <div class="alert alert-info">Click the button above to test API endpoints.</div>
                    </div>

                    <div id="api-test-results" class="api-results"></div>
                </div>
            </div>
        `;

        // Add to container
        container.appendChild(column);
    },

    /**
     * Test an endpoint
     * @param {string} endpoint - The endpoint to test
     * @returns {Promise<{endpoint: string, success: boolean, status: string, data: any}>}
     */
    async testEndpoint(endpoint) {
        try {
            const response = await fetch(endpoint);
            const data = await response.json();

            return {
                endpoint,
                success: response.ok,
                status: `${response.status} ${response.statusText}`,
                data
            };
        } catch (error) {
            return {
                endpoint,
                success: false,
                status: 'Error',
                error: error.message
            };
        }
    },

    /**
     * Display test results
     * @param {Array<{endpoint: string, success: boolean, status: string, data: any}>} results - The test results
     * @param {number} successful - The number of successful tests
     * @param {number} failed - The number of failed tests
     */
    displayResults(results, successful, failed) {
        const resultsContainer = document.getElementById('api-test-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }

        const outputContainer = document.getElementById('api-test-output');
        if (outputContainer) {
            outputContainer.innerHTML = `
                <div class="alert alert-info">Testing complete. ${successful} successful, ${failed} failed.</div>
            `;
        }

        if (!results || !Array.isArray(results)) {
            console.error('Invalid results data:', results);
            if (resultsContainer) {
                resultsContainer.innerHTML = '<div class="alert alert-danger">Error: Failed to fetch diagnostic results. Invalid data format.</div>';
            }
            return;
        }

        for (const result of results) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'card mb-3';
            resultDiv.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>${result.endpoint}</div>
                    <span class="badge ${result.success ? 'bg-success' : 'bg-danger'}">
                        ${result.status}
                    </span>
                </div>
                <div class="card-body">
                    ${result.success ?
                        `<pre class="mb-0" style="max-height: 200px; overflow-y: auto;">${JSON.stringify(result.data, null, 2)}</pre>` :
                        `<div class="alert alert-danger">${result.error || 'Unknown error'}</div>`}
                </div>
            `;

            // Add to results container if it exists
            if (resultsContainer) {
                resultsContainer.appendChild(resultDiv);
            }
        }
    },

    /**
     * Test all endpoints
     */
    async testAllEndpoints() {
        console.log('Testing all API endpoints...');

        // Show testing status
        const outputContainer = document.getElementById('api-test-output');
        if (outputContainer) {
            outputContainer.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm text-info me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>Testing endpoints...</span>
                </div>
            `;
        }

        // Clear previous results
        const resultsContainer = document.getElementById('api-test-results');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }

        // Test each endpoint
        const results = [];
        let successful = 0;
        let failed = 0;

        // Test each endpoint
        for (const endpoint of this.endpoints) {
            try {
                const result = await this.testEndpoint(endpoint);
                results.push(result);

                if (result.success) {
                    successful++;
                } else {
                    failed++;
                }
            } catch (error) {
                console.error(`Error testing ${endpoint}:`, error);
                results.push({
                    endpoint,
                    success: false,
                    status: 'Error',
                    error: error.message
                });
                failed++;
            }
        }

        // Display results
        this.displayResults(results, successful, failed);
    },
};

// Run the API check when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('API check script loaded');
    setTimeout(() => {
        // Check if we're on the troubleshooting tab
        const troubleshootingContainer = document.getElementById('troubleshooting-container');
        if (troubleshootingContainer) {
            ApiCheck.init();
        }
    }, 1000); // Wait 1 second for everything to load
});
