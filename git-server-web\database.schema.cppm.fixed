export module database.schema;

// Import the core module
import database.core;

// Standard library imports
import <string>;
import <vector>;
import <unordered_map>;
import <optional>;
import <variant>;

// Schema module
export namespace dbservice::schema {

    // Column type
    export enum class ColumnType {
        Integer,
        BigInteger,
        Real,
        Text,
        Boolean,
        Date,
        Timestamp,
        Json,
        Uuid,
        Binary
    };

    // Column definition
    export struct ColumnDefinition {
        std::string name;
        ColumnType type;
        bool nullable = true;
        bool primaryKey = false;
        bool unique = false;
        std::optional<std::string> defaultValue;
        std::optional<std::string> check;
        std::optional<std::string> references;
    };

    // Table definition
    export struct TableDefinition {
        std::string name;
        std::vector<ColumnDefinition> columns;
        std::vector<std::string> indexes;
        std::vector<std::string> constraints;
    };

    // Schema definition
    export struct SchemaDefinition {
        std::string name;
        std::vector<TableDefinition> tables;
    };

    // Migration
    export struct Migration {
        std::string version;
        std::string description;
        std::vector<std::string> upStatements;
        std::vector<std::string> downStatements;
    };

    // Schema manager
    export class SchemaManager {
    public:
        /**
         * @brief Constructor
         * @param dbService Database service
         */
        SchemaManager(std::shared_ptr<core::DatabaseInterface> dbService);

        /**
         * @brief Create a schema (synchronous)
         * @param schema Schema definition
         * @return Result of the operation
         */
        core::Result<void> createSchema(const SchemaDefinition& schema);

        /**
         * @brief Create a schema (asynchronous)
         * @param schema Schema definition
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> createSchemaAsync(const SchemaDefinition& schema);

        /**
         * @brief Drop a schema (synchronous)
         * @param schemaName Schema name
         * @return Result of the operation
         */
        core::Result<void> dropSchema(const std::string& schemaName);

        /**
         * @brief Drop a schema (asynchronous)
         * @param schemaName Schema name
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> dropSchemaAsync(const std::string& schemaName);

        /**
         * @brief Create a table (synchronous)
         * @param schema Schema name
         * @param table Table definition
         * @return Result of the operation
         */
        core::Result<void> createTable(const std::string& schema, const TableDefinition& table);

        /**
         * @brief Create a table (asynchronous)
         * @param schema Schema name
         * @param table Table definition
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> createTableAsync(
            const std::string& schema, 
            const TableDefinition& table
        );

        /**
         * @brief Drop a table (synchronous)
         * @param schema Schema name
         * @param tableName Table name
         * @return Result of the operation
         */
        core::Result<void> dropTable(const std::string& schema, const std::string& tableName);

        /**
         * @brief Drop a table (asynchronous)
         * @param schema Schema name
         * @param tableName Table name
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> dropTableAsync(
            const std::string& schema, 
            const std::string& tableName
        );

        /**
         * @brief Apply migrations (synchronous)
         * @param migrations List of migrations
         * @return Result of the operation
         */
        core::Result<void> applyMigrations(const std::vector<Migration>& migrations);

        /**
         * @brief Apply migrations (asynchronous)
         * @param migrations List of migrations
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> applyMigrationsAsync(
            const std::vector<Migration>& migrations
        );

        /**
         * @brief Get current schema version (synchronous)
         * @return Current schema version
         */
        core::Result<std::string> getCurrentVersion();

        /**
         * @brief Get current schema version (asynchronous)
         * @return Task with the current schema version
         */
        core::Task<core::Result<std::string>> getCurrentVersionAsync();

    private:
        /**
         * @brief Generate SQL for a column
         * @param column Column definition
         * @return SQL string
         */
        std::string generateColumnSql(const ColumnDefinition& column);

        /**
         * @brief Generate SQL for a table
         * @param table Table definition
         * @return SQL string
         */
        std::string generateTableSql(const TableDefinition& table);

        /**
         * @brief Generate SQL for a schema
         * @param schema Schema definition
         * @return SQL string
         */
        std::string generateSchemaSql(const SchemaDefinition& schema);

        std::shared_ptr<core::DatabaseInterface> dbService_;
    };

} // namespace dbservice::schema
