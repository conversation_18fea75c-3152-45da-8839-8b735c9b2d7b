/**
 * Git Repository List Module
 * Responsible for managing and rendering the repository list
 */
const GitRepositoryList = {
    // Module state
    state: {
        sortBy: 'name',
        sortDirection: 'asc',
        filterText: '',
        container: null,
        empty: false,
        isInitialized: false
    },

    /**
     * Initialize the module
     * @param {Array} repositories - Optional initial repositories data
     * @returns {Object} This module for chaining
     */
    init(repositories = []) {
        console.log('GitRepositoryList.init() - Initializing');

        // Initialize configuration
        this.initConfig();

        // Set up event listeners
        this.setupEventListeners();

        // Render initial list if data provided
        if (repositories.length > 0) {
            console.log('Rendering initial repository list with:', repositories.length, 'items');
            this.renderRepositoryList(repositories);
        }

        console.log('GitRepositoryList.init() - Initialized successfully');
        return this;
    },

    /**
     * Initialize configuration
     */
    initConfig() {
        // Create repository list container
        this.createRepositoryListContainer();

        // Mark as initialized
        this.state.isInitialized = true;
    },

    /**
     * Create the repository list container
     */
    createRepositoryListContainer() {
        console.log('Creating repository list container');

        // Get the repository section element
        const repoSection = document.getElementById('git-repo-section');
        if (!repoSection) {
            console.error('Repository section not found');
            return;
        }

        // Store the container reference
        this.state.container = repoSection;

        // Add loading message
        const loadingEl = document.createElement('div');
        loadingEl.id = 'repository-list-loading';
        loadingEl.className = 'text-center p-5';
        loadingEl.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading repositories...</p>
        `;

        repoSection.appendChild(loadingEl);
        console.log('Repository list container created and loading state added');
    },

    /**
     * Get or create the repository list container
     * @returns {HTMLElement} Container element
     */
    getContainer() {
        // Try to get existing container first
        let container = document.getElementById('git-repo-section');

        if (!container) {
            console.error('Repository section container not found');
            return null;
        }

        // Store reference to container
        this.state.container = container;

        return container;
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Filter input event
        const filterInput = document.getElementById('repo-filter');
        if (filterInput) {
            filterInput.addEventListener('input', this.handleFilterChange.bind(this));
        }

        // Clear filter button
        const clearFilterBtn = document.getElementById('clear-filter');
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', this.handleClearFilter.bind(this));
        }

        // Sort options
        const sortOptions = document.querySelectorAll('.sort-option');
        sortOptions.forEach(option => {
            option.addEventListener('click', (event) => {
                event.preventDefault();
                const sortBy = option.getAttribute('data-sort');
                const sortDirection = option.getAttribute('data-direction');
                this.handleSortChange(sortBy, sortDirection);
            });
        });

        // Set up hide details button
        const hideDetailsBtn = document.getElementById('hide-details-btn');
        if (hideDetailsBtn) {
            hideDetailsBtn.addEventListener('click', this.hideRepositoryDetails.bind(this));
        }
    },

    /**
     * Handle filter text change
     * @param {Event} event - Input event
     */
    handleFilterChange(event) {
        this.state.filterText = event.target.value.trim().toLowerCase();

        // If GitRepositoryManager is available, use its data
        if (window.GitRepositoryManager && GitRepositoryManager.state.repositoriesData) {
            this.renderRepositoryList(GitRepositoryManager.state.repositoriesData);
        }
    },

    /**
     * Handle clear filter button click
     */
    handleClearFilter() {
        const filterInput = document.getElementById('repo-filter');
        if (filterInput) {
            filterInput.value = '';
            this.state.filterText = '';

            // If GitRepositoryManager is available, use its data
            if (window.GitRepositoryManager && GitRepositoryManager.state.repositoriesData) {
                this.renderRepositoryList(GitRepositoryManager.state.repositoriesData);
            }
        }
    },

    /**
     * Handle sort option change
     * @param {string} sortBy - Field to sort by
     * @param {string} sortDirection - Sort direction (asc or desc)
     */
    handleSortChange(sortBy, sortDirection) {
        // Update state
        this.state.sortBy = sortBy;
        this.state.sortDirection = sortDirection;

        // Update active sort option in UI
        const sortOptions = document.querySelectorAll('.sort-option');
        sortOptions.forEach(option => {
            const isActive = option.getAttribute('data-sort') === sortBy &&
                           option.getAttribute('data-direction') === sortDirection;

            if (isActive) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });

        // Update dropdown button text
        const sortDropdown = document.getElementById('sortDropdown');
        if (sortDropdown) {
            const activeOption = document.querySelector('.sort-option.active');
            if (activeOption) {
                sortDropdown.innerHTML = `<i class="fas fa-sort"></i> ${activeOption.textContent}`;
            }
        }

        // If GitRepositoryManager is available, use its data
        if (window.GitRepositoryManager && GitRepositoryManager.state.repositoriesData) {
            this.renderRepositoryList(GitRepositoryManager.state.repositoriesData);
        }
    },

    /**
     * Filter repositories based on filter text
     * @param {Array} repositories - Array of repository objects
     * @returns {Array} Filtered repositories
     */
    filterRepositories(repositories) {
        if (!this.state.filterText) {
            return repositories;
        }

        return repositories.filter(repo => {
            const name = (repo.name || '').toLowerCase();
            const description = (repo.description || '').toLowerCase();
            return name.includes(this.state.filterText) || description.includes(this.state.filterText);
        });
    },

    /**
     * Sort repositories based on sort criteria
     * @param {Array} repositories - Array of repository objects
     * @returns {Array} Sorted repositories
     */
    sortRepositories(repositories) {
        const { sortBy, sortDirection } = this.state;

        return [...repositories].sort((a, b) => {
            let valueA, valueB;

            // Extract values based on sort field
            switch (sortBy) {
                case 'name':
                    valueA = (a.name || '').toLowerCase();
                    valueB = (b.name || '').toLowerCase();
                    break;

                case 'last_commit':
                    valueA = a.last_commit ? new Date(a.last_commit) : new Date(0);
                    valueB = b.last_commit ? new Date(b.last_commit) : new Date(0);
                    break;

                case 'size':
                    valueA = a.size || 0;
                    valueB = b.size || 0;
                    break;

                default:
                    valueA = (a.name || '').toLowerCase();
                    valueB = (b.name || '').toLowerCase();
            }

            // Compare values based on direction
            if (sortDirection === 'asc') {
                return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
            } else {
                return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
            }
        });
    },

    /**
     * Render the repository list
     * @param {Array} repositories - Array of repository objects
     */
    renderRepositoryList(repositories) {
        console.log('GitRepositoryList.renderRepositoryList() - Rendering repository list with', repositories ? repositories.length : 0, 'items');

        // Get container
        const container = this.getContainer();
        if (!container) {
            console.error('Repository list container not found');
            return;
        }

        // Clear existing content
        container.innerHTML = '';

        if (!repositories || !Array.isArray(repositories) || repositories.length === 0) {
            console.log('No repositories to display');
            // Show empty state
            container.innerHTML = `
                <div class="text-center p-5">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5>No Repositories Found</h5>
                    <p class="text-muted">There are no Git repositories available.</p>
                </div>
            `;
            return;
        }

        console.log('Creating table for', repositories.length, 'repository items');

        // Create a table for the repositories
        const table = document.createElement('table');
        table.className = 'table table-hover mb-0';

        // No table header needed
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th style="width: 100%"></th>
            </tr>
        `;
        table.appendChild(thead);

        // Create table body
        const tbody = document.createElement('tbody');

        // Create repository items
        let firstRepo = null;
        repositories.forEach((repo, index) => {
            console.log(`Creating repository item for ${repo.name}`);
            try {
                const item = this.createRepositoryItem(repo);
                tbody.appendChild(item);

                // Store the first repository for auto-selection
                if (index === 0) {
                    firstRepo = repo;
                }
            } catch (error) {
                console.error(`Error creating repository item for ${repo.name}:`, error);
                // Create a simple fallback row
                const fallbackRow = document.createElement('tr');
                fallbackRow.innerHTML = `
                    <td colspan="4" class="text-danger">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error displaying repository: ${repo.name} - ${error.message}
                        </div>
                    </td>
                `;
                tbody.appendChild(fallbackRow);
            }
        });

        table.appendChild(tbody);
        container.appendChild(table);

        console.log('Repository list rendered successfully with', repositories.length, 'items');

        // Update status indicator
        const statusIndicator = document.getElementById('repo-list-status-indicator');
        if (statusIndicator) {
            statusIndicator.classList.remove('loading', 'error');
            statusIndicator.classList.add('success');
        }

        // Auto-select the first repository after a short delay to ensure DOM is ready
        if (firstRepo) {
            console.log('Auto-selecting first repository:', firstRepo.name);
            setTimeout(() => {
                this.selectRepository(firstRepo.name);
            }, 100);
        }
    },

    /**
     * Create a repository item
     * @param {Object} repo - Repository object
     * @returns {HTMLElement} Repository list item element
     */
    createRepositoryItem(repo) {
        if (!repo || !repo.name) {
            console.error('Invalid repository object', repo);
            return document.createElement('tr'); // Return empty row
        }

        console.log('Creating repository item for:', repo.name);

        const item = document.createElement('tr');
        item.className = 'repository-item';
        item.setAttribute('data-repo-name', repo.name);

        // Format last commit date - handle missing data
        const lastCommitText = repo.last_commit_date || repo.last_commit || 'Never';

        // Calculate health - handle missing health object
        let health = 50;
        let healthClass = 'bg-info';
        let healthMessage = 'Unknown';

        if (repo.health && typeof repo.health.score !== 'undefined') {
            health = repo.health.score;
        } else if (typeof repo.health_score !== 'undefined') {
            health = repo.health_score;
        }

        if (health >= 70) {
            healthClass = 'bg-success';
            healthMessage = 'Healthy';
        } else if (health >= 40) {
            healthClass = 'bg-warning';
            healthMessage = 'Needs attention';
        } else if (health >= 10) {
            healthClass = 'bg-danger';
            healthMessage = 'Inactive';
        }

        // Format size
        const sizeText = repo.size_formatted || this.formatBytes(repo.size || 0);

        // Create simplified item content - just repo name
        item.innerHTML = `
            <td>
                <div class="d-flex align-items-center">
                    <div class="health-indicator me-2" title="${healthMessage}">
                        <span class="badge ${healthClass}">
                            <i class="fas fa-circle"></i>
                        </span>
                    </div>
                    <div class="fw-bold">${repo.name}</div>
                </div>
            </td>
        `;

        // No view button anymore, we'll just use the row click

        // Add click event to entire row
        item.addEventListener('click', (event) => {
            this.handleRepositoryClick(event, repo);
        });

        return item;
    },

    /**
     * Format bytes to human-readable string
     * @param {Number} bytes - Bytes to format
     * @param {Number} decimals - Number of decimal places
     * @returns {String} - Formatted string
     */
    formatBytes(bytes, decimals = 2) {
        if (!bytes || bytes === 0) return '0 Bytes';

        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },

    /**
     * Handle click on a repository item
     * @param {Event} event - The click event
     * @param {Object} repo - The repository object
     */
    handleRepositoryClick(event, repo) {
        // Prevent default behavior
        event.preventDefault();

        console.log('Repository clicked:', repo);

        // Update UI to show this repo is selected
        const allItems = document.querySelectorAll('.repository-item');
        allItems.forEach(item => item.classList.remove('active'));
        event.currentTarget.classList.add('active');

        // Show repository details in the side panel instead of opening modal
        this.showRepositoryDetails(repo);
    },

    /**
     * Show repository details in the side panel
     * @param {Object} repo - The repository object
     */
    showRepositoryDetails(repo) {
        // Hide the "no repository selected" message
        const noRepoSelectedEl = document.getElementById('no-repository-selected');
        if (noRepoSelectedEl) {
            noRepoSelectedEl.style.display = 'none';
        }

        // Show the repository details card
        const repoDetailsCard = document.getElementById('repository-details-card');
        if (repoDetailsCard) {
            repoDetailsCard.style.display = 'block';
        }

        // Update the repository details content
        this.updateRepositoryDetails(repo);
    },

    /**
     * Update repository details content
     * @param {Object} repo - The repository object
     */
    updateRepositoryDetails(repo) {
        console.log('Updating repository details for:', repo.name);

        // Set loading state
        const loadingEl = document.getElementById('repository-details-loading');
        const contentEl = document.getElementById('repository-details-content');
        const errorEl = document.getElementById('repository-details-error');

        if (loadingEl) loadingEl.style.display = 'block';
        if (contentEl) contentEl.style.display = 'none';
        if (errorEl) errorEl.style.display = 'none';

        try {
            // Update repository name in header
            const nameEl = document.getElementById('repo-name');
            if (nameEl) {
                nameEl.textContent = repo.name;
            }

            // Update repository description
            const descEl = document.getElementById('repo-description');
            if (descEl) {
                descEl.textContent = repo.description || '';
            }

            // Update repository size
            const sizeEl = document.getElementById('repo-size');
            if (sizeEl) {
                sizeEl.textContent = repo.size_formatted || this.formatBytes(repo.size || 0);
            }

            // Update last commit date
            const lastCommitEl = document.getElementById('repo-last-commit');
            if (lastCommitEl) {
                const commitDate = repo.last_commit_date || repo.last_commit;
                if (commitDate && commitDate !== 'Never') {
                    try {
                        // Parse the ISO date string
                        const date = new Date(commitDate);

                        // Calculate date range (from 30 days ago to current date)
                        const today = new Date();
                        const thirtyDaysAgo = new Date();
                        thirtyDaysAgo.setDate(today.getDate() - 30);

                        // Format as MM/DD/YYYY - MM/DD/YYYY
                        const fromDate = `${(thirtyDaysAgo.getMonth() + 1).toString().padStart(2, '0')}/${thirtyDaysAgo.getDate().toString().padStart(2, '0')}/${thirtyDaysAgo.getFullYear()}`;
                        const toDate = `${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getDate().toString().padStart(2, '0')}/${today.getFullYear()}`;

                        lastCommitEl.textContent = `${fromDate} - ${toDate}`;
                    } catch (e) {
                        console.error('Error formatting date:', e);
                        lastCommitEl.textContent = commitDate; // Fallback to original format
                    }
                } else {
                    lastCommitEl.textContent = 'Never';
                }
            }

            // Update branch count
            const branchCountEl = document.getElementById('repo-branch-count');
            if (branchCountEl) {
                branchCountEl.textContent = `${repo.branch_count || 0} branches`;
            }

            // Update health indicator
            this.updateHealthIndicator(repo);

            // Initialize commit history chart
            if (window.GitCommitHistoryChart) {
                GitCommitHistoryChart.initChart(repo.name);
            }

            // Hide loading, show content
            if (loadingEl) loadingEl.style.display = 'none';
            if (contentEl) contentEl.style.display = 'block';

        } catch (error) {
            console.error('Error updating repository details:', error);
            if (errorEl) {
                errorEl.style.display = 'block';
            }
            if (loadingEl) loadingEl.style.display = 'none';
        }
    },

    /**
     * Update health indicator for a repository
     * @param {Object} repo - The repository object
     */
    updateHealthIndicator(repo) {
        const healthProgressEl = document.getElementById('repo-health-progress');
        const healthMessageEl = document.getElementById('repo-health-message');

        if (!healthProgressEl || !healthMessageEl) {
            return;
        }

        // Calculate health score (0-100)
        let healthScore = 0;
        let healthMessage = 'Inactive repository';

        if (repo.health && typeof repo.health.score !== 'undefined') {
            healthScore = repo.health.score;
            healthMessage = repo.health.message || 'Unknown health status';
        } else if (typeof repo.health_score !== 'undefined') {
            healthScore = repo.health_score;
            healthMessage = repo.health_message || 'Unknown health status';
        } else if (repo.last_commit_date || repo.last_commit) {
            // Simple calculation based on last commit date
            const lastCommitDate = new Date(repo.last_commit_date || repo.last_commit);
            const now = new Date();
            const daysSinceLastCommit = Math.floor((now - lastCommitDate) / (1000 * 60 * 60 * 24));

            if (daysSinceLastCommit <= 7) {
                healthScore = 100;
                healthMessage = 'Very active';
            } else if (daysSinceLastCommit <= 30) {
                healthScore = 75;
                healthMessage = 'Active';
            } else if (daysSinceLastCommit <= 90) {
                healthScore = 50;
                healthMessage = 'Regular updates';
            } else if (daysSinceLastCommit <= 180) {
                healthScore = 25;
                healthMessage = 'Occasional updates';
            } else {
                healthScore = 10;
                healthMessage = 'Inactive';
            }
        }

        // Update progress bar
        healthProgressEl.style.width = `${healthScore}%`;
        healthProgressEl.setAttribute('aria-valuenow', healthScore);

        // Update color based on health score
        healthProgressEl.classList.remove('bg-success', 'bg-warning', 'bg-danger', 'bg-info');
        if (healthScore >= 70) {
            healthProgressEl.classList.add('bg-success');
        } else if (healthScore >= 40) {
            healthProgressEl.classList.add('bg-warning');
        } else if (healthScore >= 10) {
            healthProgressEl.classList.add('bg-danger');
        } else {
            healthProgressEl.classList.add('bg-info');
        }

        // Update health message
        healthMessageEl.textContent = healthMessage;
    },

    /**
     * Hide repository details panel
     */
    hideRepositoryDetails() {
        // Show no repository selected message
        const noRepoSelectedEl = document.getElementById('no-repository-selected');
        if (noRepoSelectedEl) {
            noRepoSelectedEl.style.display = 'block';
        }

        // Hide repository details card
        const repoDetailsCard = document.getElementById('repository-details-card');
        if (repoDetailsCard) {
            repoDetailsCard.style.display = 'none';
        }

        // Clear active selection
        const allItems = document.querySelectorAll('.repository-item');
        allItems.forEach(item => item.classList.remove('active'));
    },

    /**
     * Create empty state display
     * @returns {HTMLElement} Empty state element
     */
    createEmptyState() {
        const emptyState = document.createElement('div');
        emptyState.className = 'empty-state text-center p-5';
        emptyState.innerHTML = `
            <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
            <h5>No repositories found</h5>
            <p class="text-muted">There are no Git repositories to display.</p>
        `;
        return emptyState;
    },
};

// Expose the module globally
window.GitRepositoryList = GitRepositoryList;