-- Project Improvements Tracker Schema

-- Project Templates Table
CREATE TABLE IF NOT EXISTS project_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL,
    description TEXT,
    git_repository_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Predefined Project Templates
INSERT INTO project_templates (name, type, description, git_repository_url) VALUES
    ('Python Flask Web Service', 'web_service', 'Flask-based web application template', 'https://github.com/project-tracker-templates/flask-template.git'),
    ('Python Asyncio Agent', 'agent_system', 'Asyncio-based background agent template', 'https://github.com/project-tracker-templates/asyncio-template.git'),
    ('Python Library', 'library', 'Standard Python library project template', 'https://github.com/project-tracker-templates/library-template.git');

-- Additional Project Templates
INSERT INTO project_templates (name, type, description, git_repository_url) VALUES
    ('Python Asyncio Background Agent', 'agent_system', 'Advanced asynchronous background processing agent', 'https://github.com/project-tracker-templates/asyncio-agent-template.git'),
    ('Python Library with Poetry', 'library', 'Comprehensive Python library template with modern packaging', 'https://github.com/project-tracker-templates/python-library-template.git'),
    ('FastAPI Microservice', 'web_service', 'High-performance async web service with FastAPI', 'https://github.com/project-tracker-templates/fastapi-microservice-template.git'),
    ('Data Science Project', 'library', 'Data analysis and machine learning project template', 'https://github.com/project-tracker-templates/data-science-template.git');

-- Git Repositories Table
CREATE TABLE IF NOT EXISTS git_repositories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    clone_url TEXT NOT NULL,
    default_branch TEXT DEFAULT 'main',
    last_fetch TIMESTAMP,
    webhook_secret TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Git Branches Table
CREATE TABLE IF NOT EXISTS git_branches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    repository_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    last_commit_hash TEXT,
    last_commit_message TEXT,
    last_commit_date TIMESTAMP,
    is_protected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES git_repositories(id) ON DELETE CASCADE
);

-- Tasks Table
CREATE TABLE IF NOT EXISTS tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    task_key TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'open',
    priority TEXT DEFAULT 'medium',
    assignee TEXT,
    created_by TEXT,
    branch_name TEXT,
    related_pr TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE(project_id, task_key)
);

-- Task Dependencies
CREATE TABLE IF NOT EXISTS task_dependencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    depends_on_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- Code Reviews Table
CREATE TABLE IF NOT EXISTS code_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    task_id INTEGER,
    pr_number TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'open',
    author TEXT NOT NULL,
    branch_name TEXT NOT NULL,
    target_branch TEXT DEFAULT 'main',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL
);

-- Review Comments Table
CREATE TABLE IF NOT EXISTS review_comments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    review_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    line_number INTEGER,
    content TEXT NOT NULL,
    author TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (review_id) REFERENCES code_reviews(id) ON DELETE CASCADE
);

-- Workspace Settings Table
CREATE TABLE IF NOT EXISTS workspace_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    ide_type TEXT NOT NULL,
    settings_json TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Projects table (enhanced)
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL,
    template_id INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'initialized',
    repository_url TEXT,
    default_branch TEXT DEFAULT 'main',
    task_prefix TEXT DEFAULT 'TASK',
    pr_prefix TEXT DEFAULT 'PR',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES project_templates(id)
);

-- Categories table with project association
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    UNIQUE(project_id, name),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Improvements table with project association
CREATE TABLE IF NOT EXISTS improvements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    improvement_id TEXT NOT NULL,
    project_id INTEGER NOT NULL,
    task_id INTEGER,
    date TEXT NOT NULL,
    commit_hash TEXT,
    category_id INTEGER,
    affected_files TEXT,
    version TEXT,
    rollback_command TEXT,
    description TEXT,
    potential_impacts TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, improvement_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL
);

-- Subtasks table
CREATE TABLE IF NOT EXISTS subtasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    improvement_id TEXT NOT NULL,
    project_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    completed BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (improvement_id) REFERENCES improvements(improvement_id) ON DELETE CASCADE
);

-- Project Creation Tracking
CREATE TABLE IF NOT EXISTS project_creation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    action TEXT NOT NULL,  -- 'created', 'modified', 'deleted'
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- Project activity log
CREATE TABLE IF NOT EXISTS project_activity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    action_type TEXT NOT NULL, -- 'create', 'update', 'delete'
    entity_type TEXT NOT NULL, -- 'improvement', 'category', 'subtask'
    entity_id TEXT NOT NULL,
    details TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Git Operations Log
CREATE TABLE IF NOT EXISTS git_operations_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    repository_id INTEGER NOT NULL,
    operation_type TEXT NOT NULL,
    status TEXT NOT NULL,
    duration_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES git_repositories(id) ON DELETE CASCADE
);

-- Role Management Tables
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS user_roles (
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES user_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Insert default roles
INSERT OR IGNORE INTO roles (name, description) VALUES
    ('admin', 'Full system access'),
    ('manager', 'Project and team management access'),
    ('developer', 'Development and code access'),
    ('viewer', 'Read-only access');

-- Insert default permissions
INSERT OR IGNORE INTO permissions (name, description) VALUES
    ('manage_users', 'Create and manage user accounts'),
    ('manage_roles', 'Assign and manage roles'),
    ('manage_projects', 'Create and manage projects'),
    ('manage_git', 'Manage Git repositories and branches'),
    ('manage_tasks', 'Create and manage tasks'),
    ('view_projects', 'View project details'),
    ('view_tasks', 'View task details'),
    ('submit_code', 'Submit code and create branches');

-- Assign default role permissions
INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.name = 'admin';  -- Admin gets all permissions

INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.name = 'manager' 
AND p.name IN ('manage_projects', 'manage_tasks', 'view_projects', 'view_tasks');

INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.name = 'developer'
AND p.name IN ('manage_tasks', 'view_projects', 'view_tasks', 'submit_code');

INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE r.name = 'viewer'
AND p.name IN ('view_projects', 'view_tasks');

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_improvements_date ON improvements(date);
CREATE INDEX IF NOT EXISTS idx_improvements_category ON improvements(category_id);
CREATE INDEX IF NOT EXISTS idx_git_branches_repo ON git_branches(repository_id);
CREATE INDEX IF NOT EXISTS idx_tasks_project ON tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_code_reviews_project ON code_reviews(project_id);
CREATE INDEX IF NOT EXISTS idx_git_operations_repo ON git_operations_log(repository_id);
CREATE INDEX IF NOT EXISTS idx_subtasks_improvement ON subtasks(improvement_id);
CREATE INDEX IF NOT EXISTS idx_project_activity_project ON project_activity(project_id);
