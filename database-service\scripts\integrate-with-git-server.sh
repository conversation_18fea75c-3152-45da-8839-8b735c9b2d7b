#!/bin/bash

# Database Service Integration with Existing Git Server
# Integrates database service with existing git.chcit.org Nginx configuration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NGINX_CONF_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
GIT_SERVER_CONF="git.chcit.org"
BACKUP_DIR="/etc/nginx/backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message"
            ;;
    esac
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log "ERROR" "This script must be run as root"
        exit 1
    fi
}

# Check if git server config exists
check_git_server_config() {
    local config_file="$NGINX_CONF_DIR/$GIT_SERVER_CONF"
    
    if [[ ! -f "$config_file" ]]; then
        log "ERROR" "Git server Nginx configuration not found at $config_file"
        log "ERROR" "Please ensure your git.chcit.org Nginx configuration exists"
        exit 1
    fi
    
    log "INFO" "Found git server configuration at $config_file"
}

# Create backup
create_backup() {
    local config_file="$NGINX_CONF_DIR/$GIT_SERVER_CONF"
    local backup_file="$BACKUP_DIR/${GIT_SERVER_CONF}.backup.$(date +%Y%m%d_%H%M%S)"
    
    log "INFO" "Creating backup of existing configuration"
    
    mkdir -p "$BACKUP_DIR"
    cp "$config_file" "$backup_file"
    
    log "INFO" "Backup created at $backup_file"
}

# Add upstream block
add_upstream_block() {
    local config_file="$NGINX_CONF_DIR/$GIT_SERVER_CONF"
    
    log "INFO" "Adding database service upstream block"
    
    # Check if upstream already exists
    if grep -q "upstream database_service_backend" "$config_file"; then
        log "WARN" "Upstream block already exists, skipping"
        return 0
    fi
    
    # Find the http block or add before first server block
    if grep -q "^http {" "$config_file"; then
        # Add after http block opening
        sed -i '/^http {/a\\n# Database Service Backend\nupstream database_service_backend {\n    server 127.0.0.1:8080;\n    keepalive 32;\n}' "$config_file"
    else
        # Add before first server block
        sed -i '/^server {/i # Database Service Backend\nupstream database_service_backend {\n    server 127.0.0.1:8080;\n    keepalive 32;\n}\n' "$config_file"
    fi
    
    log "INFO" "Upstream block added"
}

# Add rate limiting zones
add_rate_limiting() {
    local config_file="$NGINX_CONF_DIR/$GIT_SERVER_CONF"
    
    log "INFO" "Adding rate limiting zones"
    
    # Check if rate limiting already exists
    if grep -q "limit_req_zone.*api_limit" "$config_file"; then
        log "WARN" "Rate limiting zones already exist, skipping"
        return 0
    fi
    
    # Add rate limiting zones
    if grep -q "^http {" "$config_file"; then
        sed -i '/^http {/a\\n# Rate limiting for database API\nlimit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;' "$config_file"
    else
        sed -i '/upstream database_service_backend/i # Rate limiting for database API\nlimit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;\nlimit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;\n' "$config_file"
    fi
    
    log "INFO" "Rate limiting zones added"
}

# Add location blocks
add_location_blocks() {
    local config_file="$NGINX_CONF_DIR/$GIT_SERVER_CONF"
    local location_blocks="$PROJECT_ROOT/nginx/database-service.conf"
    
    log "INFO" "Adding database service location blocks"
    
    # Check if location blocks already exist
    if grep -q "location /database-api/" "$config_file"; then
        log "WARN" "Database API location blocks already exist, skipping"
        return 0
    fi
    
    # Find the HTTPS server block and add location blocks before the closing brace
    # Look for the last location block or before the closing server brace
    local temp_file=$(mktemp)
    
    # Extract just the location blocks from our config file
    sed -n '/# Database API proxy/,/# Database metrics endpoint/p' "$location_blocks" > "$temp_file"
    
    # Add the location blocks to the server block
    # Find the last location block or before server closing brace
    if grep -q "location.*{" "$config_file"; then
        # Add after the last location block
        sed -i '/location.*{/,/^[[:space:]]*}[[:space:]]*$/{ /^[[:space:]]*}[[:space:]]*$/r '"$temp_file"' }' "$config_file"
    else
        # Add before server closing brace
        sed -i '/^[[:space:]]*}[[:space:]]*$/i\\n# Database Service API Endpoints' "$config_file"
        sed -i '/# Database Service API Endpoints/r '"$temp_file" "$config_file"
    fi
    
    rm "$temp_file"
    log "INFO" "Location blocks added"
}

# Test Nginx configuration
test_nginx_config() {
    log "INFO" "Testing Nginx configuration"
    
    if nginx -t; then
        log "INFO" "Nginx configuration test passed"
        return 0
    else
        log "ERROR" "Nginx configuration test failed"
        return 1
    fi
}

# Reload Nginx
reload_nginx() {
    log "INFO" "Reloading Nginx"
    
    if systemctl reload nginx; then
        log "INFO" "Nginx reloaded successfully"
    else
        log "ERROR" "Failed to reload Nginx"
        return 1
    fi
}

# Check if database service is running
check_database_service() {
    local service_port=8080
    
    if netstat -tuln | grep -q ":$service_port "; then
        log "INFO" "Database service appears to be running on port $service_port"
        return 0
    else
        log "WARN" "Database service does not appear to be running on port $service_port"
        log "WARN" "Make sure to start the database service before testing"
        return 1
    fi
}

# Display integration summary
show_summary() {
    log "INFO" "=== Integration Summary ==="
    log "INFO" "Database service integrated with git.chcit.org"
    log "INFO" "API endpoints available at:"
    log "INFO" "  - https://git.chcit.org/database-api/health"
    log "INFO" "  - https://git.chcit.org/database-api/auth/login"
    log "INFO" "  - https://git.chcit.org/database-api/query"
    log "INFO" "  - https://git.chcit.org/database-api/database/metrics"
    log "INFO" ""
    log "INFO" "=== Next Steps ==="
    log "INFO" "1. Start the database service: systemctl start database-service"
    log "INFO" "2. Test the integration: curl https://git.chcit.org/database-api/health"
    log "INFO" "3. Check logs: tail -f /var/log/nginx/access.log"
}

# Restore from backup
restore_backup() {
    local latest_backup=$(ls -t "$BACKUP_DIR"/${GIT_SERVER_CONF}.backup.* 2>/dev/null | head -1)
    
    if [[ -n "$latest_backup" ]]; then
        log "INFO" "Restoring from backup: $latest_backup"
        cp "$latest_backup" "$NGINX_CONF_DIR/$GIT_SERVER_CONF"
        nginx -t && systemctl reload nginx
        log "INFO" "Backup restored successfully"
    else
        log "ERROR" "No backup found to restore"
    fi
}

# Main execution
main() {
    log "INFO" "Starting Database Service Integration with Git Server"
    
    # Handle restore option
    if [[ "${1:-}" == "--restore" ]]; then
        check_root
        restore_backup
        exit 0
    fi
    
    # Checks
    check_root
    check_git_server_config
    
    # Create backup
    create_backup
    
    # Integration steps
    add_upstream_block
    add_rate_limiting
    add_location_blocks
    
    # Test and reload
    if test_nginx_config; then
        reload_nginx
        log "INFO" "Integration completed successfully"
    else
        log "ERROR" "Configuration test failed. Restoring backup..."
        restore_backup
        exit 1
    fi
    
    # Final checks and summary
    check_database_service || log "WARN" "Database service not running"
    show_summary
    
    log "INFO" "Integration complete!"
    log "INFO" "To restore original configuration, run: $0 --restore"
}

# Run main function
main "$@"
