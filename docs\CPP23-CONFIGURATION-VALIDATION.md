# C++23 Applications Configuration Validation

## Overview

This document provides an overview of the configuration validation features implemented across all three C++23 applications in the Project Tracker system:

1. Git Repository Service
2. Logging Service
3. Database Service

These validation features ensure that all configuration parameters are valid and appropriate for each service's operation, preventing runtime errors and making the services more robust and easier to configure correctly.

## Common Validation Features

All three applications share the following validation features:

1. **Schema Validation**: Validates the structure of configuration files
2. **Type Checking**: Ensures values are of the correct type (integer, string, boolean, etc.)
3. **Range Validation**: Validates numeric values are within acceptable ranges
4. **Format Validation**: Checks that strings like connection strings, file paths, and URLs have the correct format
5. **Existence Checks**: Verifies that files and directories exist and are accessible
6. **Permission Checks**: Ensures the application has the necessary permissions to read/write files and directories
7. **Default Values**: Provides sensible defaults when invalid values are detected
8. **Clear Error Messages**: Provides detailed feedback about configuration issues

## Application-Specific Validation

### Git Repository Service

The Git Repository Service validates:

- Repository path existence and permissions
- Server port range and availability
- Log level against valid values
- Log file path and permissions

For detailed information, see the [Git Repository Service Configuration Validation](./git-server-web/git-repo-service/configuration-validation.md) documentation.

### Logging Service

The Logging Service validates:

- Storage tier configurations (memory, database, file)
- Database connection strings
- Log source paths and parsers
- Retention policies
- File permissions and directory structures

For detailed information, see the [Logging Service Configuration Validation](./git-server-web/git-repo-logging/configuration-validation.md) documentation.

### Database Service

The Database Service validates:

- Database connection parameters
- Connection pool settings
- API server configuration
- Security settings (authentication and authorization)
- Logging configuration
- Schema management settings

For detailed information, see the [Database Service Configuration Validation](./database-service/configuration-validation.md) documentation.

## Configuration Sources

All three applications can load configuration from:

1. **Configuration Files**: JSON format configuration files
2. **Environment Variables**: System environment variables

When both sources are available, environment variables take precedence over configuration files.

## Validation Behavior

When each service starts, it performs the following steps:

1. Loads configuration from the specified file (if provided)
2. Loads configuration from environment variables
3. Validates all configuration parameters
4. Reports any validation issues
5. Applies default values for invalid parameters
6. Optionally saves corrected configuration
7. Continues execution with the validated configuration

The services will attempt to continue running even if some configuration parameters are invalid, falling back to default values where possible.

## Best Practices

### Common Best Practices

1. **Use Configuration Files**: For production deployments, use configuration files rather than environment variables for better maintainability.

2. **Secure Sensitive Information**: Store sensitive information like passwords in environment variables rather than configuration files.

3. **Set Appropriate Permissions**: Ensure services have appropriate permissions to read/write to the specified paths.

4. **Validate Before Deployment**: Test your configuration before deploying to production to catch any issues early.

5. **Monitor Validation Output**: Check service logs for validation warnings and errors during startup.

6. **Use Absolute Paths**: Always use absolute paths in configuration to avoid ambiguity.

### Git Repository Service

- Ensure the Git repository path exists and has appropriate permissions
- Use a dedicated user for running the service
- Configure appropriate log levels based on environment (production vs. development)

### Logging Service

- Configure appropriate retention periods based on your organization's needs
- Enable only the storage tiers you need
- Ensure log source paths are correct and accessible
- Use appropriate parsers for each log source

### Database Service

- Use connection pooling for better performance
- Configure appropriate security settings
- Generate strong random secrets for JWT and API keys
- Set up proper log rotation for production environments

## Troubleshooting

### Common Issues

1. **Service fails to start**: Check if the specified paths exist and have appropriate permissions.

2. **Service ignores configuration file**: Ensure the file is in valid JSON format and the service has read permissions.

3. **Service uses default values**: Check the logs for validation errors that might cause fallback to defaults.

4. **Permission denied errors**: Ensure the service has appropriate permissions to access the specified paths.

### Git Repository Service

- Check if Git repositories are accessible
- Verify that the service has appropriate permissions to read Git repositories
- Ensure the server port is available

### Logging Service

- Verify database connection if using the database tier
- Check if log sources exist and are readable
- Ensure storage directories are writable

### Database Service

- Check database connection parameters
- Verify SSL certificate and key files if SSL is enabled
- Ensure JWT secret or API key is properly configured

## Conclusion

The configuration validation systems in all three C++23 applications help prevent runtime errors by validating configuration parameters before they are used. By providing sensible defaults, creating necessary directories, and giving clear error messages, they make the services more robust and easier to configure correctly.

For detailed information about each application's configuration validation, refer to the application-specific documentation:

- [Git Repository Service Configuration Validation](./git-server-web/git-repo-service/configuration-validation.md)
- [Logging Service Configuration Validation](./git-server-web/git-repo-logging/configuration-validation.md)
- [Database Service Configuration Validation](./database-service/configuration-validation.md)
