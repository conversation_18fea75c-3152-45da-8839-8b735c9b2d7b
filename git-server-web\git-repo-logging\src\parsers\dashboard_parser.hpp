#pragma once
#include "parser_interface.hpp"
#include <regex>

namespace logging {

class DashboardParser : public LogParser {
public:
    DashboardParser();
    ~DashboardParser() = default;
    
    // LogParser interface implementation
    std::optional<LogEntry> parse(const std::string& line, const std::string& source) override;
    bool canParse(const std::string& line) override;
    std::string getName() const override { return "dashboard"; }
    std::string getDescription() const override { return "Parser for Git Dashboard log format"; }
    
private:
    // Regular expressions for dashboard log formats
    std::regex standardFormat_;  // Standard dashboard log format
    std::regex jsonFormat_;      // JSON dashboard log format
};

} // namespace logging
