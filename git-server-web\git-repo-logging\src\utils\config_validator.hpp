#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <regex>
#include <jsoncpp/json/json.h>

namespace fs = std::filesystem;

namespace logging {

class ConfigValidator {
public:
    // Validate the main configuration file
    static bool validateMainConfig(const std::string& configPath, Json::Value& config) {
        try {
            // Check if file exists
            if (!fs::exists(configPath)) {
                std::cerr << "Configuration file does not exist: " << configPath << std::endl;
                return false;
            }

            // Open and parse the file
            std::ifstream configFile(configPath);
            if (!configFile.is_open()) {
                std::cerr << "Failed to open config file: " << configPath << std::endl;
                return false;
            }

            Json::CharReaderBuilder reader;
            std::string errors;

            if (!Json::parseFromStream(reader, configFile, &config, &errors)) {
                std::cerr << "Failed to parse config file: " << errors << std::endl;
                return false;
            }

            // Validate required sections
            if (!config.isMember("tiers") || !config["tiers"].isObject()) {
                std::cerr << "Missing or invalid 'tiers' section in config" << std::endl;
                return false;
            }

            if (!config.isMember("retention") || !config["retention"].isObject()) {
                std::cerr << "Missing or invalid 'retention' section in config" << std::endl;
                return false;
            }

            if (!config.isMember("sources") || !config["sources"].isObject()) {
                std::cerr << "Missing or invalid 'sources' section in config" << std::endl;
                return false;
            }

            // Validate tiers configuration
            bool isValid = true;
            isValid &= validateMemoryTier(config);
            isValid &= validateDatabaseTier(config);
            isValid &= validateFileTier(config);

            // Validate retention configuration
            isValid &= validateRetention(config);

            // Validate sources configuration
            isValid &= validateSources(config);

            return isValid;
        } catch (const std::exception& e) {
            std::cerr << "Error validating configuration: " << e.what() << std::endl;
            return false;
        }
    }

    // Validate storage configuration
    static bool validateStorageConfig(const std::string& configPath, Json::Value& config) {
        try {
            // Check if file exists
            if (!fs::exists(configPath)) {
                std::cerr << "Storage configuration file does not exist: " << configPath << std::endl;
                return false;
            }

            // Open and parse the file
            std::ifstream configFile(configPath);
            if (!configFile.is_open()) {
                std::cerr << "Failed to open storage config file: " << configPath << std::endl;
                return false;
            }

            Json::CharReaderBuilder reader;
            std::string errors;

            if (!Json::parseFromStream(reader, configFile, &config, &errors)) {
                std::cerr << "Failed to parse storage config file: " << errors << std::endl;
                return false;
            }

            // Validate required sections
            if (!config.isMember("tiers") || !config["tiers"].isObject()) {
                std::cerr << "Missing or invalid 'tiers' section in storage config" << std::endl;
                return false;
            }

            // Validate tiers configuration
            bool isValid = true;
            isValid &= validateMemoryTier(config);
            isValid &= validateDatabaseTier(config);
            isValid &= validateFileTier(config);

            return isValid;
        } catch (const std::exception& e) {
            std::cerr << "Error validating storage configuration: " << e.what() << std::endl;
            return false;
        }
    }

private:
    // Validate memory tier configuration
    static bool validateMemoryTier(Json::Value& config) {
        if (!config["tiers"].isMember("memory") || !config["tiers"]["memory"].isObject()) {
            std::cerr << "Missing or invalid 'memory' tier configuration" << std::endl;
            return false;
        }

        auto& memoryConfig = config["tiers"]["memory"];
        
        // Check if enabled is a boolean
        if (!memoryConfig.isMember("enabled") || !memoryConfig["enabled"].isBool()) {
            std::cerr << "Missing or invalid 'enabled' field in memory tier config" << std::endl;
            memoryConfig["enabled"] = true;
            std::cerr << "Setting memory tier enabled to default: true" << std::endl;
        }

        // Only validate further if enabled
        if (memoryConfig["enabled"].asBool()) {
            // Validate max_entries
            if (!memoryConfig.isMember("max_entries") || !memoryConfig["max_entries"].isNumeric()) {
                std::cerr << "Missing or invalid 'max_entries' field in memory tier config" << std::endl;
                memoryConfig["max_entries"] = 1000;
                std::cerr << "Setting max_entries to default: 1000" << std::endl;
            } else {
                int maxEntries = memoryConfig["max_entries"].asInt();
                if (maxEntries <= 0 || maxEntries > 1000000) {
                    std::cerr << "Invalid max_entries value: " << maxEntries << ", must be between 1 and 1000000" << std::endl;
                    memoryConfig["max_entries"] = 1000;
                    std::cerr << "Setting max_entries to default: 1000" << std::endl;
                }
            }

            // Validate max_age_minutes
            if (!memoryConfig.isMember("max_age_minutes") || !memoryConfig["max_age_minutes"].isNumeric()) {
                std::cerr << "Missing or invalid 'max_age_minutes' field in memory tier config" << std::endl;
                memoryConfig["max_age_minutes"] = 60;
                std::cerr << "Setting max_age_minutes to default: 60" << std::endl;
            } else {
                int maxAgeMinutes = memoryConfig["max_age_minutes"].asInt();
                if (maxAgeMinutes <= 0 || maxAgeMinutes > 10080) { // Max 1 week
                    std::cerr << "Invalid max_age_minutes value: " << maxAgeMinutes << ", must be between 1 and 10080" << std::endl;
                    memoryConfig["max_age_minutes"] = 60;
                    std::cerr << "Setting max_age_minutes to default: 60" << std::endl;
                }
            }
        }

        return true;
    }

    // Validate database tier configuration
    static bool validateDatabaseTier(Json::Value& config) {
        if (!config["tiers"].isMember("database") || !config["tiers"]["database"].isObject()) {
            std::cerr << "Missing or invalid 'database' tier configuration" << std::endl;
            return false;
        }

        auto& dbConfig = config["tiers"]["database"];
        
        // Check if enabled is a boolean
        if (!dbConfig.isMember("enabled") || !dbConfig["enabled"].isBool()) {
            std::cerr << "Missing or invalid 'enabled' field in database tier config" << std::endl;
            dbConfig["enabled"] = false;
            std::cerr << "Setting database tier enabled to default: false" << std::endl;
        }

        // Only validate further if enabled
        if (dbConfig["enabled"].asBool()) {
            // Validate connection_string
            if (!dbConfig.isMember("connection_string") || !dbConfig["connection_string"].isString()) {
                std::cerr << "Missing or invalid 'connection_string' field in database tier config" << std::endl;
                
                // Check if environment variable is set
                const char* connStrEnv = std::getenv("DB_CONNECTION_STRING");
                if (connStrEnv) {
                    dbConfig["connection_string"] = connStrEnv;
                    std::cerr << "Using database connection string from environment variable" << std::endl;
                } else {
                    dbConfig["connection_string"] = "*****************************************************";
                    std::cerr << "Setting connection_string to default, but this will likely fail to connect" << std::endl;
                    std::cerr << "Please set a valid connection string or disable the database tier" << std::endl;
                }
            } else {
                std::string connStr = dbConfig["connection_string"].asString();
                
                // Validate connection string format
                std::regex connStrRegex("postgresql://[^:]+:[^@]+@[^/]+/[^/]+");
                if (!std::regex_match(connStr, connStrRegex)) {
                    std::cerr << "Invalid database connection string format: " << connStr << std::endl;
                    std::cerr << "Expected format: ************************************************" << std::endl;
                    
                    // Check if environment variable is set
                    const char* connStrEnv = std::getenv("DB_CONNECTION_STRING");
                    if (connStrEnv) {
                        dbConfig["connection_string"] = connStrEnv;
                        std::cerr << "Using database connection string from environment variable" << std::endl;
                    } else {
                        std::cerr << "Please set a valid connection string or disable the database tier" << std::endl;
                    }
                }
            }

            // Validate structured_only
            if (!dbConfig.isMember("structured_only") || !dbConfig["structured_only"].isBool()) {
                std::cerr << "Missing or invalid 'structured_only' field in database tier config" << std::endl;
                dbConfig["structured_only"] = true;
                std::cerr << "Setting structured_only to default: true" << std::endl;
            }
        }

        return true;
    }

    // Validate file tier configuration
    static bool validateFileTier(Json::Value& config) {
        if (!config["tiers"].isMember("file") || !config["tiers"]["file"].isObject()) {
            std::cerr << "Missing or invalid 'file' tier configuration" << std::endl;
            return false;
        }

        auto& fileConfig = config["tiers"]["file"];
        
        // Check if enabled is a boolean
        if (!fileConfig.isMember("enabled") || !fileConfig["enabled"].isBool()) {
            std::cerr << "Missing or invalid 'enabled' field in file tier config" << std::endl;
            fileConfig["enabled"] = true;
            std::cerr << "Setting file tier enabled to default: true" << std::endl;
        }

        // Only validate further if enabled
        if (fileConfig["enabled"].asBool()) {
            // Validate base_directory
            if (!fileConfig.isMember("base_directory") || !fileConfig["base_directory"].isString()) {
                std::cerr << "Missing or invalid 'base_directory' field in file tier config" << std::endl;
                fileConfig["base_directory"] = "/var/log/git-dashboard/logs";
                std::cerr << "Setting base_directory to default: /var/log/git-dashboard/logs" << std::endl;
            } else {
                std::string baseDir = fileConfig["base_directory"].asString();
                
                // Check if directory exists
                if (!fs::exists(baseDir)) {
                    std::cerr << "File tier base directory does not exist: " << baseDir << std::endl;
                    try {
                        fs::create_directories(baseDir);
                        std::cerr << "Created directory: " << baseDir << std::endl;
                    } catch (const std::exception& e) {
                        std::cerr << "Failed to create directory: " << e.what() << std::endl;
                        std::cerr << "File tier may not function correctly" << std::endl;
                    }
                }
                
                // Check if directory is writable
                try {
                    std::string testFile = baseDir + "/test_write.tmp";
                    std::ofstream test(testFile);
                    if (!test.is_open()) {
                        std::cerr << "Warning: File tier base directory is not writable: " << baseDir << std::endl;
                        std::cerr << "File tier may not function correctly" << std::endl;
                    } else {
                        test.close();
                        fs::remove(testFile);
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Warning: File tier base directory is not writable: " << e.what() << std::endl;
                    std::cerr << "File tier may not function correctly" << std::endl;
                }
            }

            // Validate compression
            if (!fileConfig.isMember("compression") || !fileConfig["compression"].isBool()) {
                std::cerr << "Missing or invalid 'compression' field in file tier config" << std::endl;
                fileConfig["compression"] = true;
                std::cerr << "Setting compression to default: true" << std::endl;
            }

            // Validate compression_age_days
            if (!fileConfig.isMember("compression_age_days") || !fileConfig["compression_age_days"].isNumeric()) {
                std::cerr << "Missing or invalid 'compression_age_days' field in file tier config" << std::endl;
                fileConfig["compression_age_days"] = 7;
                std::cerr << "Setting compression_age_days to default: 7" << std::endl;
            } else {
                int compressionAgeDays = fileConfig["compression_age_days"].asInt();
                if (compressionAgeDays <= 0 || compressionAgeDays > 365) {
                    std::cerr << "Invalid compression_age_days value: " << compressionAgeDays << ", must be between 1 and 365" << std::endl;
                    fileConfig["compression_age_days"] = 7;
                    std::cerr << "Setting compression_age_days to default: 7" << std::endl;
                }
            }
        }

        return true;
    }

    // Validate retention configuration
    static bool validateRetention(Json::Value& config) {
        if (!config.isMember("retention") || !config["retention"].isObject()) {
            std::cerr << "Missing or invalid 'retention' section in config" << std::endl;
            return false;
        }

        auto& retention = config["retention"];
        
        // Validate retention periods for different log types
        std::vector<std::string> retentionTypes = {"system", "security", "application", "database", "custom"};
        
        for (const auto& type : retentionTypes) {
            if (!retention.isMember(type) || !retention[type].isNumeric()) {
                std::cerr << "Missing or invalid retention period for " << type << " logs" << std::endl;
                
                // Set default values based on type
                int defaultDays = 30;
                if (type == "security") defaultDays = 90;
                else if (type == "database") defaultDays = 15;
                
                retention[type] = defaultDays;
                std::cerr << "Setting " << type << " retention to default: " << defaultDays << " days" << std::endl;
            } else {
                int days = retention[type].asInt();
                if (days <= 0 || days > 3650) { // Max 10 years
                    std::cerr << "Invalid retention period for " << type << " logs: " << days << " days, must be between 1 and 3650" << std::endl;
                    
                    // Set default values based on type
                    int defaultDays = 30;
                    if (type == "security") defaultDays = 90;
                    else if (type == "database") defaultDays = 15;
                    
                    retention[type] = defaultDays;
                    std::cerr << "Setting " << type << " retention to default: " << defaultDays << " days" << std::endl;
                }
            }
        }

        return true;
    }

    // Validate sources configuration
    static bool validateSources(Json::Value& config) {
        if (!config.isMember("sources") || !config["sources"].isObject()) {
            std::cerr << "Missing or invalid 'sources' section in config" << std::endl;
            return false;
        }

        auto& sources = config["sources"];
        std::vector<std::string> validParsers = {"syslog", "nginx", "git", "dashboard"};
        
        // Validate each source
        for (const auto& sourceName : sources.getMemberNames()) {
            if (!sources[sourceName].isObject()) {
                std::cerr << "Invalid configuration for source: " << sourceName << std::endl;
                continue;
            }
            
            auto& source = sources[sourceName];
            
            // Validate enabled flag
            if (!source.isMember("enabled") || !source["enabled"].isBool()) {
                std::cerr << "Missing or invalid 'enabled' field for source: " << sourceName << std::endl;
                source["enabled"] = true;
                std::cerr << "Setting " << sourceName << " source enabled to default: true" << std::endl;
            }
            
            // Only validate further if enabled
            if (source["enabled"].asBool()) {
                // Validate path
                if (!source.isMember("path") || !source["path"].isString()) {
                    std::cerr << "Missing or invalid 'path' field for source: " << sourceName << std::endl;
                    source["path"] = "/var/log/" + sourceName + ".log";
                    std::cerr << "Setting " << sourceName << " source path to default: " << source["path"].asString() << std::endl;
                } else {
                    std::string path = source["path"].asString();
                    
                    // Check if path exists
                    if (!fs::exists(path)) {
                        std::cerr << "Warning: Source path does not exist: " << path << std::endl;
                        std::cerr << "This source may not function correctly until the log file is created" << std::endl;
                    }
                    
                    // Check if path is readable
                    try {
                        std::ifstream test(path);
                        if (!test.is_open()) {
                            std::cerr << "Warning: Source path is not readable: " << path << std::endl;
                            std::cerr << "This source may not function correctly" << std::endl;
                        }
                    } catch (const std::exception& e) {
                        std::cerr << "Warning: Source path is not readable: " << e.what() << std::endl;
                        std::cerr << "This source may not function correctly" << std::endl;
                    }
                }
                
                // Validate parser
                if (!source.isMember("parser") || !source["parser"].isString()) {
                    std::cerr << "Missing or invalid 'parser' field for source: " << sourceName << std::endl;
                    
                    // Try to guess parser based on source name
                    std::string parser = "syslog"; // Default
                    if (sourceName == "nginx") parser = "nginx";
                    else if (sourceName == "git") parser = "git";
                    else if (sourceName == "dashboard") parser = "dashboard";
                    
                    source["parser"] = parser;
                    std::cerr << "Setting " << sourceName << " source parser to default: " << parser << std::endl;
                } else {
                    std::string parser = source["parser"].asString();
                    
                    // Check if parser is valid
                    if (std::find(validParsers.begin(), validParsers.end(), parser) == validParsers.end()) {
                        std::cerr << "Invalid parser for source " << sourceName << ": " << parser << std::endl;
                        std::cerr << "Valid parsers are: syslog, nginx, git, dashboard" << std::endl;
                        
                        // Try to guess parser based on source name
                        std::string newParser = "syslog"; // Default
                        if (sourceName == "nginx") newParser = "nginx";
                        else if (sourceName == "git") newParser = "git";
                        else if (sourceName == "dashboard") newParser = "dashboard";
                        
                        source["parser"] = newParser;
                        std::cerr << "Setting " << sourceName << " source parser to: " << newParser << std::endl;
                    }
                }
            }
        }

        return true;
    }
};

} // namespace logging
