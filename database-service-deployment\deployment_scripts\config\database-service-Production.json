﻿{
    "database":  {
                     "username":  "database_service_prod",
                     "port":  5432,
                     "name":  "database_service_prod",
                     "password":  "",
                     "host":  "localhost"
                 },
    "project":  {
                    "remote_build_dir":  "/home/<USER>/database-service-build",
                    "description":  "database-service for production environment",
                    "name":  "database-service",
                    "remote_install_dir":  "/opt/database-service",
                    "local_source_dir":  "D:\\Augment\\project-tracker\\database-service"
                },
    "ssh":  {
                "username":  "btaylor-admin",
                "port":  22,
                "local_key_path":  "C:\\Users\\<USER>\\.ssh\\id_rsa",
                "host":  "git.chcit.org"
            },
    "service":  {
                    "port":  null,
                    "group":  "database-service",
                    "description":  "database-service for production environment",
                    "user":  "database-service",
                    "name":  "database-service"
                },
    "version":  {
                    "number":  1,
                    "updated":  "2025-04-15 22:25:00",
                    "created":  "2025-04-15 22:25:00"
                }
}
