#!/bin/bash

# Database Service Integration Test Script
# Tests the database service integration with git.chcit.org

set -euo pipefail

# Configuration
BASE_URL="https://git.chcit.org/database-api"
LOCAL_URL="http://localhost:8080/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "PASS")
            echo -e "${GREEN}[PASS]${NC} ${timestamp} - $message"
            ((TESTS_PASSED++))
            ;;
        "FAIL")
            echo -e "${RED}[FAIL]${NC} ${timestamp} - $message"
            ((TESTS_FAILED++))
            ;;
    esac
}

# Test function
test_endpoint() {
    local url=$1
    local expected_status=${2:-200}
    local description=$3
    
    log "INFO" "Testing: $description"
    log "INFO" "URL: $url"
    
    local response
    local status_code
    
    # Make request and capture response
    if response=$(curl -s -w "\n%{http_code}" "$url" 2>/dev/null); then
        status_code=$(echo "$response" | tail -n1)
        response_body=$(echo "$response" | head -n -1)
        
        if [[ "$status_code" == "$expected_status" ]]; then
            log "PASS" "$description (Status: $status_code)"
            if [[ -n "$response_body" ]]; then
                echo "Response: $response_body"
            fi
        else
            log "FAIL" "$description (Expected: $expected_status, Got: $status_code)"
            if [[ -n "$response_body" ]]; then
                echo "Response: $response_body"
            fi
        fi
    else
        log "FAIL" "$description (Connection failed)"
    fi
    
    echo ""
}

# Test local service
test_local_service() {
    log "INFO" "=== Testing Local Database Service ==="
    
    # Check if service is running
    if netstat -tuln | grep -q ":8080 "; then
        log "INFO" "Database service is running on port 8080"
        
        test_endpoint "$LOCAL_URL/health" 200 "Local health check"
        
        # Test CORS preflight
        if command -v curl &> /dev/null; then
            log "INFO" "Testing CORS preflight request"
            local cors_response
            cors_response=$(curl -s -X OPTIONS \
                -H "Origin: https://git.chcit.org" \
                -H "Access-Control-Request-Method: GET" \
                -H "Access-Control-Request-Headers: Content-Type" \
                -w "%{http_code}" \
                "$LOCAL_URL/health" 2>/dev/null || echo "000")
            
            if [[ "$cors_response" == *"200"* ]] || [[ "$cors_response" == *"204"* ]]; then
                log "PASS" "CORS preflight request"
            else
                log "FAIL" "CORS preflight request (Status: $cors_response)"
            fi
        fi
    else
        log "WARN" "Database service is not running on port 8080"
        log "WARN" "Start the service with: ./database-service --config config-dev.json"
    fi
    
    echo ""
}

# Test production integration
test_production_integration() {
    log "INFO" "=== Testing Production Integration ==="
    
    # Test health endpoint
    test_endpoint "$BASE_URL/health" 200 "Production health check via Nginx"
    
    # Test authentication endpoint (should return 401 or method not allowed)
    test_endpoint "$BASE_URL/auth/login" 405 "Authentication endpoint (OPTIONS/GET not allowed)"
    
    # Test metrics endpoint (should be restricted)
    test_endpoint "$BASE_URL/database/metrics" 403 "Metrics endpoint (access restricted)"
    
    # Test non-existent endpoint
    test_endpoint "$BASE_URL/nonexistent" 404 "Non-existent endpoint"
}

# Test SSL certificate
test_ssl_certificate() {
    log "INFO" "=== Testing SSL Certificate ==="
    
    local cert_info
    if cert_info=$(echo | openssl s_client -servername git.chcit.org -connect git.chcit.org:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null); then
        log "PASS" "SSL certificate is valid"
        echo "$cert_info"
        
        # Check certificate expiration
        local not_after
        not_after=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
        local expiry_date
        expiry_date=$(date -d "$not_after" +%s 2>/dev/null || echo "0")
        local current_date
        current_date=$(date +%s)
        local days_until_expiry=$(( (expiry_date - current_date) / 86400 ))
        
        if [[ $days_until_expiry -gt 30 ]]; then
            log "PASS" "Certificate expires in $days_until_expiry days"
        elif [[ $days_until_expiry -gt 0 ]]; then
            log "WARN" "Certificate expires in $days_until_expiry days (renewal recommended)"
        else
            log "FAIL" "Certificate has expired"
        fi
    else
        log "FAIL" "Unable to retrieve SSL certificate information"
    fi
    
    echo ""
}

# Test Nginx configuration
test_nginx_config() {
    log "INFO" "=== Testing Nginx Configuration ==="
    
    if command -v nginx &> /dev/null; then
        if nginx -t &>/dev/null; then
            log "PASS" "Nginx configuration is valid"
        else
            log "FAIL" "Nginx configuration has errors"
            nginx -t
        fi
    else
        log "WARN" "Nginx not found or not accessible"
    fi
    
    echo ""
}

# Test database connection (if possible)
test_database_connection() {
    log "INFO" "=== Testing Database Connection ==="
    
    # This is a basic test - in production you'd want more comprehensive testing
    if command -v psql &> /dev/null; then
        local db_test
        if db_test=$(psql -h localhost -U postgres -d postgres -c "SELECT 1;" 2>/dev/null); then
            log "PASS" "PostgreSQL is accessible"
        else
            log "WARN" "PostgreSQL connection test failed (may be expected if not configured)"
        fi
    else
        log "WARN" "psql not available for database testing"
    fi
    
    echo ""
}

# Performance test
performance_test() {
    log "INFO" "=== Performance Test ==="
    
    if command -v curl &> /dev/null; then
        log "INFO" "Testing response time for health endpoint"
        
        local total_time=0
        local requests=5
        
        for i in $(seq 1 $requests); do
            local response_time
            response_time=$(curl -s -w "%{time_total}" -o /dev/null "$BASE_URL/health" 2>/dev/null || echo "0")
            total_time=$(echo "$total_time + $response_time" | bc -l 2>/dev/null || echo "$total_time")
            log "INFO" "Request $i: ${response_time}s"
        done
        
        if command -v bc &> /dev/null; then
            local avg_time
            avg_time=$(echo "scale=3; $total_time / $requests" | bc -l)
            log "INFO" "Average response time: ${avg_time}s"
            
            if (( $(echo "$avg_time < 1.0" | bc -l) )); then
                log "PASS" "Response time is acceptable (< 1s)"
            else
                log "WARN" "Response time is slow (> 1s)"
            fi
        fi
    fi
    
    echo ""
}

# Show summary
show_summary() {
    log "INFO" "=== Test Summary ==="
    log "INFO" "Tests passed: $TESTS_PASSED"
    log "INFO" "Tests failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log "PASS" "All tests passed! Database service integration is working correctly."
    else
        log "WARN" "Some tests failed. Please review the output above."
    fi
    
    echo ""
    log "INFO" "=== Integration Status ==="
    log "INFO" "Local service: http://localhost:8080/api/health"
    log "INFO" "Production service: https://git.chcit.org/database-api/health"
    log "INFO" "Git dashboard: https://git.chcit.org/"
}

# Main execution
main() {
    log "INFO" "Starting Database Service Integration Tests"
    echo ""
    
    # Run tests
    test_local_service
    test_production_integration
    test_ssl_certificate
    test_nginx_config
    test_database_connection
    performance_test
    
    # Show summary
    show_summary
}

# Handle command line arguments
case "${1:-}" in
    "--local-only")
        log "INFO" "Running local tests only"
        test_local_service
        ;;
    "--production-only")
        log "INFO" "Running production tests only"
        test_production_integration
        test_ssl_certificate
        ;;
    "--help")
        echo "Usage: $0 [--local-only|--production-only|--help]"
        echo ""
        echo "Options:"
        echo "  --local-only      Test only local database service"
        echo "  --production-only Test only production integration"
        echo "  --help           Show this help message"
        exit 0
        ;;
    *)
        main
        ;;
esac
