# Build Project Module

<#
.SYNOPSIS
    Provides functionality for building the database service project.

.DESCRIPTION
    This module handles the build process for the database service project,
    including source code synchronization, remote build, and deployment.

.NOTES
    File Name      : Build-Project.ps1
    Author         : Database Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
    }
} catch {
    Write-Host "Error loading required modules: $_" -ForegroundColor Red
    return
}

<#
.SYNOPSIS
    Builds the database service project on the remote server.

.DESCRIPTION
    Synchronizes the source code to the remote server, builds the project,
    and optionally installs it. Handles all aspects of the build process.

.EXAMPLE
    Build-Project
#>
function Build-Project {
    [CmdletBinding()]
    param()

    Clear-Host

    # Add debug output
    Write-Host "Starting Build-Project function..." -ForegroundColor Yellow

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Build Project                          " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after menu display
    Disable-UIMode

    # Add more debug output
    Write-Host "Loading configuration..." -ForegroundColor Yellow

    # Set default environment if not already set
    if (-not (Get-Variable -Name Environment -Scope Script -ErrorAction SilentlyContinue)) {
        $script:Environment = "development"
    }

    # Check if configuration is loaded
    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "Configuration not loaded, attempting to load..." -Level "Info" -Component "Build"

        # Try to load configuration if not already loaded
        $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"

        if (Test-Path -Path $configDir) {
            # Import the ConfigValidator module if not already imported
            if (-not (Get-Module -Name "ConfigValidator")) {
                try {
                    Import-Module -Name "$PSScriptRoot\ConfigValidator.psm1" -Force -ErrorAction Stop
                    Write-Log -Message "Loaded ConfigValidator module" -Level "Debug" -Component "Build"
                } catch {
                    Write-Log -Message "Failed to load ConfigValidator module: $_" -Level "Warning" -Component "Build"
                }
            }

            # Look for environment-specific configuration files
            $configFile = "D:\Augment\project-tracker\database-service-deployment\deployment_scripts\config\database-service-development.json"

            if (Test-Path -Path $configFile) {
                # Load the environment-specific configuration
                Write-Log -Message "Loading configuration from $configFile" -Level "Info" -Component "Build"

                try {
                    # Directly load the configuration file
                    $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                    $script:Config = $configContent

                    if ($null -ne $script:Config -and $null -ne $script:Config.project) {
                        Write-Log -Message "Successfully loaded configuration from file" -Level "Success" -Component "Build"
                    } else {
                        Write-Log -Message "Failed to load configuration from file" -Level "Error" -Component "Build"
                        Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
                        Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
                        Wait-ForUser
                        return $false
                    }
                } catch {
                    Write-Log -Message "Error loading configuration: $_" -Level "Error" -Component "Build"
                    Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
                    Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
                    Wait-ForUser
                    return $false
                }
            } else {
                # Look for any configuration file if environment-specific one doesn't exist
                $configFiles = Get-ChildItem -Path $configDir -Filter "*.json" | Select-Object -ExpandProperty FullName

                if ($configFiles.Count -gt 0) {
                    # Use the first configuration file found
                    $configFile = $configFiles[0]
                    Write-Log -Message "Environment-specific configuration not found. Loading from $configFile" -Level "Warning" -Component "Build"

                    try {
                        # Directly load the configuration file
                        $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                        $script:Config = $configContent

                        if ($null -ne $script:Config -and $null -ne $script:Config.project) {
                            Write-Log -Message "Successfully loaded configuration from file" -Level "Info" -Component "Build"
                        } else {
                            Write-Log -Message "Failed to load configuration from file" -Level "Error" -Component "Build"
                            Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
                            Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
                            Wait-ForUser
                            return $false
                        }
                    } catch {
                        Write-Log -Message "Error loading configuration: $_" -Level "Error" -Component "Build"
                        Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
                        Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
                        Wait-ForUser
                        return $false
                    }
                } else {
                    Write-Log -Message "No configuration files found in $configDir" -Level "Error" -Component "Build"
                    Write-Log -Message "Please set environment first." -Level "Warning" -Component "Build"
                    Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
                    Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
                    Wait-ForUser
                    return $false
                }
            }
        } else {
            Write-Log -Message "Configuration directory not found: $configDir" -Level "Error" -Component "Build"
            Write-Log -Message "Please set environment first." -Level "Warning" -Component "Build"
            Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
            Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
            Wait-ForUser
            return $false
        }
    }

    # Double-check that configuration is loaded
    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "Configuration not loaded. Cannot continue." -Level "Error" -Component "Build"
        Write-Log -Message "Please run this script from the main menu to ensure proper configuration." -Level "Error" -Component "Build"
        Wait-ForUser
        return $false
    }

    # Display current configuration
    Enable-UIMode
    Write-Log -Message "Current Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "Project: $($script:Config.project.name)" -Level "UI" -ForegroundColor White

    # Display environment with color coding
    $envColor = "White"
    if ($script:Environment -eq "production") {
        $envColor = "Red"
    } elseif ($script:Environment -eq "development") {
        $envColor = "Green"
    } elseif ($script:Environment -eq "testing" -or $script:Environment -eq "test") {
        $envColor = "Yellow"
    }

    Write-Log -Message "Environment: " -Level "UI" -ForegroundColor White -NoNewline
    Write-Log -Message "$script:Environment" -Level "UI" -ForegroundColor $envColor
    Write-Log -Message "Server: $($script:Config.ssh.host)" -Level "UI" -ForegroundColor White

    # Display paths with proper property names
    if ($script:Config.project.PSObject.Properties.Name -contains "remote_install_dir") {
        Write-Log -Message "Install Directory: $($script:Config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
    } elseif ($script:Config.project.PSObject.Properties.Name -contains "install_dir") {
        Write-Log -Message "Install Directory: $($script:Config.project.install_dir)" -Level "UI" -ForegroundColor White
    }

    if ($script:Config.project.PSObject.Properties.Name -contains "local_source_dir") {
        Write-Log -Message "Source Directory: $($script:Config.project.local_source_dir)" -Level "UI" -ForegroundColor White
    } elseif ($script:Config.project.PSObject.Properties.Name -contains "source_dir") {
        Write-Log -Message "Source Directory: $($script:Config.project.source_dir)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message " " -Level "UI"
    Disable-UIMode

    # Validate required configuration using ConfigValidator
    $configValidatorLoaded = $false

    if (-not (Get-Module -Name "ConfigValidator")) {
        try {
            # Try to import from the current directory
            if (Test-Path -Path "$PSScriptRoot\ConfigValidator.psm1") {
                Import-Module -Name "$PSScriptRoot\ConfigValidator.psm1" -Force -ErrorAction Stop
                $configValidatorLoaded = $true
                Write-Log -Message "Loaded ConfigValidator module from current directory" -Level "Debug" -Component "Build"
            }
            # Try to import from the parent directory
            elseif (Test-Path -Path "$PSScriptRoot\..\Modules\ConfigValidator.psm1") {
                Import-Module -Name "$PSScriptRoot\..\Modules\ConfigValidator.psm1" -Force -ErrorAction Stop
                $configValidatorLoaded = $true
                Write-Log -Message "Loaded ConfigValidator module from parent directory" -Level "Debug" -Component "Build"
            }
            else {
                Write-Log -Message "ConfigValidator module not found" -Level "Warning" -Component "Build"
            }
        } catch {
            Write-Log -Message "Failed to load ConfigValidator module: $_" -Level "Warning" -Component "Build"
        }
    } else {
        $configValidatorLoaded = $true
    }

    if ($configValidatorLoaded -and (Get-Command -Name Test-Configuration -ErrorAction SilentlyContinue)) {
        try {
            $validationResult = Test-Configuration -Config $script:Config -Detailed

            if (-not $validationResult.IsValid) {
                Write-Log -Message "Configuration validation failed" -Level "Error" -Component "Build"

                foreach ($section in $validationResult.MissingProperties.Keys) {
                    foreach ($property in $validationResult.MissingProperties[$section]) {
                        Write-Log -Message "Missing required property: $section.$property" -Level "Error" -Component "Build"
                    }
                }

                Write-Log -Message "Please fix the configuration before continuing" -Level "Error" -Component "Build"
                Wait-ForUser
                return $false
            }
        } catch {
            Write-Log -Message "Error during configuration validation: $_" -Level "Error" -Component "Build"
            Write-Log -Message "Falling back to basic validation" -Level "Warning" -Component "Build"
            $configValidatorLoaded = $false
        }
    }

    # Fallback validation if ConfigValidator couldn't be used
    if (-not $configValidatorLoaded) {
        $sourceDir = $null
        $installDir = $null

        if ($script:Config.project.PSObject.Properties.Name -contains "local_source_dir") {
            $sourceDir = $script:Config.project.local_source_dir
        } elseif ($script:Config.project.PSObject.Properties.Name -contains "source_dir") {
            $sourceDir = $script:Config.project.source_dir
        }

        if ($script:Config.project.PSObject.Properties.Name -contains "remote_install_dir") {
            $installDir = $script:Config.project.remote_install_dir
        } elseif ($script:Config.project.PSObject.Properties.Name -contains "install_dir") {
            $installDir = $script:Config.project.install_dir
        }

        if ([string]::IsNullOrWhiteSpace($sourceDir) -or [string]::IsNullOrWhiteSpace($installDir)) {
            Write-Log -Message "Source directory or installation directory not configured." -Level "Error" -Component "Build"
            Wait-ForUser
            return $false
        }
    }

    # Get source directory with proper property name
    $sourceDir = $null
    if ($script:Config.project.PSObject.Properties.Name -contains "local_source_dir") {
        $sourceDir = $script:Config.project.local_source_dir
    } elseif ($script:Config.project.PSObject.Properties.Name -contains "source_dir") {
        $sourceDir = $script:Config.project.source_dir
    }

    if (-not (Test-Path -Path $sourceDir)) {
        Write-Log -Message "Source directory not found: $sourceDir" -Level "Error" -Component "Build"
        Wait-ForUser
        return $false
    }

    # Get installation directory with proper property name
    $installDir = $null
    if ($script:Config.project.PSObject.Properties.Name -contains "remote_install_dir") {
        $installDir = $script:Config.project.remote_install_dir
    } elseif ($script:Config.project.PSObject.Properties.Name -contains "install_dir") {
        $installDir = $script:Config.project.install_dir
    }

    # Import the SSHManager module if not already imported
    if (-not (Get-Module -Name "SSHManager")) {
        try {
            Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
            Write-Log -Message "Loaded SSHManager module" -Level "Debug" -Component "Build"
        } catch {
            Write-Log -Message "Failed to load SSHManager module: $_" -Level "Warning" -Component "Build"
        }
    }

    # Check SSH connection
    Write-Log -Message "Checking SSH connection..." -Level "Info" -Component "Build"

    if (Get-Module -Name "SSHManager") {
        # Use the Test-SSHConnection function from SSHManager module
        $connected = Test-SSHConnection

        if (-not $connected) {
            Write-Log -Message "Failed to connect to the server. Please check your SSH settings." -Level "Error" -Component "Build"
            Wait-ForUser
            return $false
        }
    } else {
        # Fallback to Invoke-RemoteCommand if SSHManager module couldn't be loaded
        $testCmd = "echo 'SSH connection successful'"
        $testResult = Invoke-RemoteCommand -Command $testCmd

        if ($null -eq $testResult -or $testResult -notmatch "SSH connection successful") {
            Write-Log -Message "Failed to connect to the server. Please check your SSH settings." -Level "Error" -Component "Build"
            Wait-ForUser
            return $false
        }
    }

    # Get SSH connection details
    $sshHost = $script:Config.ssh.host
    $sshPort = $script:Config.ssh.port
    $sshUser = $script:Config.ssh.username
    $sshKeyPath = $null

    if ($script:Config.ssh.PSObject.Properties.Name -contains "local_key_path") {
        $sshKeyPath = $script:Config.ssh.local_key_path
    } elseif ($script:Config.ssh.PSObject.Properties.Name -contains "key_path") {
        $sshKeyPath = $script:Config.ssh.key_path
    }

    # Define remote build directory with proper property name
    $remoteBuildDir = $null
    if ($script:Config.project.PSObject.Properties.Name -contains "remote_build_dir") {
        $remoteBuildDir = $script:Config.project.remote_build_dir
    } elseif ($script:Config.project.PSObject.Properties.Name -contains "build_dir") {
        $remoteBuildDir = $script:Config.project.build_dir
    }

    if ([string]::IsNullOrEmpty($remoteBuildDir)) {
        # Fallback to a temporary directory if not specified in config
        $remoteBuildDir = "/tmp/build_$($script:Config.project.name)_$([guid]::NewGuid().ToString().Substring(0, 8))"
        Write-Log -Message "Build directory not specified in configuration. Using temporary directory: $remoteBuildDir" -Level "Warning" -Component "Build"
    } else {
        Write-Log -Message "Using build directory from configuration: $remoteBuildDir" -Level "Info" -Component "Build"
    }


    # Upload source files directly to remote server
    Write-Log -Message "Uploading source files to remote server..." -Level "Info" -Component "Build"

    # Get all files from source directory
    $sourceFiles = Get-ChildItem -Path $sourceDir -Recurse -File

    # Create a list of files to upload
    $filesToUpload = @()
    foreach ($file in $sourceFiles) {
        $relativePath = $file.FullName.Substring($sourceDir.Length).TrimStart('\')
        $remoteFilePath = "{0}/{1}" -f $remoteBuildDir, $relativePath.Replace('\', '/')
        $filesToUpload += @{
            LocalPath = $file.FullName
            RemotePath = $remoteFilePath
        }
    }

    # Upload files in batches
    $batchSize = 10
    $totalFiles = $filesToUpload.Count
    $filesUploaded = 0

    for ($i = 0; $i -lt $totalFiles; $i += $batchSize) {
        $batch = $filesToUpload | Select-Object -Skip $i -First $batchSize

        foreach ($file in $batch) {
            # Upload each file individually
            $scpArgs = @(
                "-i", $sshKeyPath,
                "-P", $sshPort,
                $file.LocalPath,
                "{0}@{1}:{2}" -f $sshUser, $sshHost, $file.RemotePath
            ) -join " "

            $uploadProcess = Start-Process -FilePath "scp" -ArgumentList $scpArgs -NoNewWindow -PassThru -Wait

            if ($uploadProcess.ExitCode -ne 0) {
                Write-Log -Message "Failed to upload file: $($file.LocalPath)" -Level "Warning" -Component "Build"
            } else {
                $filesUploaded++
            }
        }

        # Update progress
        $percentComplete = [math]::Round(($filesUploaded / $totalFiles) * 100)
        Write-Log -Message "Uploaded $filesUploaded of $totalFiles files ($percentComplete%)" -Level "Info" -Component "Build"
    }

    if ($filesUploaded -lt $totalFiles) {
        Write-Log -Message "Warning: Some files failed to upload. Continuing with build process." -Level "Warning" -Component "Build"
    } else {
        Write-Log -Message "All files uploaded successfully." -Level "Success" -Component "Build"
    }

    # Build the project on the remote server
    Write-Log -Message "Building project on remote server..." -Level "Info" -Component "Build"

    # Create the build command with proper variable substitution
    $buildCmd = "cd '{0}' && " -f $remoteBuildDir +
               "mkdir -p build && " +
               "cd build && " +
               "cmake .. -DCMAKE_BUILD_TYPE=Release && " +
               "make -j4 && " +
               "echo 'Build completed successfully'"

    # Execute the build script
    $buildResult = Invoke-RemoteCommand -Command $buildCmd

    if ($buildResult -notmatch "Build completed successfully") {
        Write-Log -Message "Build failed." -Level "Error" -Component "Build"
        Write-Log -Message $buildResult -Level "Error" -Component "Build"
        Wait-ForUser
        return
    }

    Write-Log -Message "Build completed successfully!" -Level "Success" -Component "Build"

    # Copy files to installation directory
    Write-Log -Message "Copying files to installation directory..." -Level "Info" -Component "Build"

    # Create the copy command with proper variable substitution
    $copyCmd = "sudo chown -R '{0}' '{1}' && " -f $sshUser, $installDir +
              "if [ -d '{0}/build/bin' ]; then mkdir -p '{1}/bin' && cp -r '{0}/build/bin/'* '{1}/bin/'; fi && " -f $remoteBuildDir, $installDir +
              "if [ -d '{0}/config' ]; then mkdir -p '{1}/config' && cp -r '{0}/config/'* '{1}/config/'; fi && " -f $remoteBuildDir, $installDir +
              "if [ -d '{0}/schemas' ]; then mkdir -p '{1}/schemas' && cp -r '{0}/schemas/'* '{1}/schemas/'; fi && " -f $remoteBuildDir, $installDir +
              "if [ -d '{0}/src' ]; then mkdir -p '{1}/src' && cp -r '{0}/src/'* '{1}/src/'; fi && " -f $remoteBuildDir, $installDir +
              "sudo chown -R '{0}' '{1}' && " -f $script:Config.service.user, $installDir +
              "sudo chmod -R 755 '{0}' && " -f $installDir +
              "sudo rm -rf '{0}' && " -f $remoteBuildDir +
              "echo 'Files copied successfully to {0} with proper permissions'" -f $installDir

    # Execute the copy script
    $copyResult = Invoke-RemoteCommand -Command $copyCmd

    if ($copyResult -match "Files copied successfully") {
        Write-Log -Message "Files copied successfully to installation directory!" -Level "Success" -Component "Build"
    } else {
        Write-Log -Message "Warning: There may have been issues copying files to the installation directory." -Level "Warning" -Component "Build"
        Write-Log -Message $copyResult -Level "Verbose" -Component "Build"
    }

    Write-Log -Message "`nBuild process completed!" -Level "Success" -Component "Build"
    Write-Log -Message "The database service has been built and installed on the server." -Level "Success" -Component "Build"
    Write-Log -Message "Note: SSL certificate access can be configured using the 'Setup Certificate Access' menu option." -Level "Info" -Component "Build"
    Wait-ForUser
}
