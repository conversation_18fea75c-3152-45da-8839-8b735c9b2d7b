# Database Service Nginx Configuration
# Integration with existing git.chcit.org server

# STEP 1: Add this upstream block to your nginx.conf in the http context
# (outside of any server blocks)

upstream database_service_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

# STEP 2: Add these rate limiting zones to your http block (if not already present)
# limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;

# STEP 3: Add these location blocks INSIDE your existing git.chcit.org HTTPS server block

    # Database API proxy - accessible at https://git.chcit.org/database-api/
    location /database-api/ {
        # Rate limiting
        limit_req zone=api_limit burst=20 nodelay;

        # Strip /database-api prefix and forward to backend as /api/
        rewrite ^/database-api/(.*)$ /api/$1 break;

        # Proxy settings
        proxy_pass http://database_service_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # Cache control for API responses
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }
    # Database authentication endpoints with stricter rate limiting
    location /database-api/auth/ {
        # Stricter rate limiting for auth endpoints
        limit_req zone=auth_limit burst=10 nodelay;

        # Strip prefix and forward
        rewrite ^/database-api/(.*)$ /api/$1 break;

        # Proxy settings
        proxy_pass http://database_service_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # No caching for auth endpoints
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    # Database health check endpoint
    location /database-api/health {
        # Allow health checks without rate limiting
        rewrite ^/database-api/(.*)$ /api/$1 break;

        proxy_pass http://database_service_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Quick timeouts for health checks
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;

        # Cache health check responses briefly
        add_header Cache-Control "public, max-age=30" always;
    }

    # Database metrics endpoint (admin only)
    location /database-api/database/metrics {
        # Allow from localhost and private networks only
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;

        # Rate limiting
        limit_req zone=api_limit burst=5 nodelay;

        # Strip prefix and forward
        rewrite ^/database-api/(.*)$ /api/$1 break;

        proxy_pass http://database_service_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_Set_header X-Forwarded-Proto $scheme;

        # No caching for metrics
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    }
