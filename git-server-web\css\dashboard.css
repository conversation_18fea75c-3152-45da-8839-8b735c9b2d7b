/* dashboard.css - Dashboard framework styles */

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
}

.dashboard-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    margin: 0;
}

.dashboard-title i {
    color: #0d6efd;
}

.dashboard-title h1 {
    font-size: 1.5rem;
    margin: 0;
}

.refresh-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.refresh-btn, #refresh-logs-btn {
    background-color: #007bff;
    color: white;
    border: 1px solid #0069d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
    min-width: 120px; /* Increased width to accommodate 'Refreshing...' text */
    justify-content: center; /* Center content horizontally */
}

#refresh-logs-btn {
    min-width: auto; /* No need for minimum width for the logs refresh button */
}

.refresh-btn:hover, #refresh-logs-btn:hover {
    background-color: #0069d9;
}

.refresh-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.refresh-btn i {
    display: inline-block;
    width: 14px; /* Set a fixed width for the icon */
    text-align: center;
}

.last-updated-info {
    font-size: 0.75rem;
    text-align: center;
}

/* Tab Styling - IMPORTANT FIX */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 15px !important; /* Force consistent margin */
    padding-bottom: 0;
    flex-shrink: 0;
    position: relative; /* Establish positioning context */
    z-index: 10; /* Ensure tabs are above content */
}

.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    display: flex;
    align-items: center;
}

.nav-tabs .nav-link i {
    margin-right: 8px;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .nav-link:hover:not(.active) {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #f8f9fa;
}

/* CRITICAL FIX: Reset tab content styles to ensure tab content is aligned consistently */
.tab-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0 !important;
    overflow: hidden;
    height: auto !important;
    width: 100%;
    padding: 0 !important;
    border: none;
    border-radius: 0 0 0.25rem 0.25rem;
    margin-bottom: 0 !important;
    margin-top: 0 !important;
    background-color: transparent;
}

/* CRITICAL FIX: Make all tab panes use consistent layout and positioning */
.tab-pane {
    position: relative !important;
    top: 0 !important;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: auto;
    min-height: 0;
    width: 100%;
    padding: 0 !important;
    margin: 0 !important;
}

/* Fix for all tab consistency */
#overview, #repositories, #metrics, #certificates, #logs, #debug {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    position: relative !important;
    top: 0 !important;
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* CRITICAL FIX: Ensure all cards within tab panes use flex layout and consistent positioning */
.tab-pane > .card {
    position: relative !important;
    top: 0 !important;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    max-width: 100%;
    margin: 0 !important;
    border-radius: 8px;
    overflow: hidden;
}

#overview .card, #repositories .card, #metrics .card, #certificates .card, #logs .card, #debug .card {
    position: relative !important;
    top: 0 !important;
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    height: 100%;
}

/* Card headers with fixed height */
.card-header {
    flex-shrink: 0;
    font-weight: bold;
    background-color: #f8f9fa;
}

/* Ensure all card bodies can scroll properly */
.card-body {
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    min-height: 0;
}

/* Make sure all tab content has the same width */
.tab-content > .tab-pane > .card {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
}

/* Component Header Styles */
.component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.component-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.component-header h5 i {
    margin-right: 8px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Special case for logs tab with no padding */
#logs .card-body {
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* Scrollable log content */
#dashboard-log-content {
    flex: 1;
    height: auto;
    min-height: 0;
    padding: 0.5rem;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.85rem;
    background-color: #fff;
}

/* Remove any absolute positioning or fixed heights */
#logs .card-header {
    position: static;
    width: auto;
}

/* Reset any legacy height properties */
#logs .tab-pane,
#logs .card,
#logs .card-body {
    min-height: 0;
}

/* Nested cards within tab panes */
.tab-pane .card .card {
    max-height: none;
    margin-bottom: 15px;
}

/* Overview tab nested cards layout */
#overview .card-body {
    overflow-y: auto;
}

#overview .row {
    margin-right: 0;
    margin-left: 0;
}

/* Repository tab layout */
#repositories #git-summary-container {
    margin-bottom: 15px;
}

/* Set container to viewport height minus padding */
.container {
    height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding-bottom: 20px;
}

/* CRITICAL FIX: Force all tabs to have the same vertical positioning */
.tab-pane.fade,
.tab-pane.fade.active,
.tab-pane.fade.show,
.tab-pane.fade.active.show {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    transform: none !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.tab-pane.fade:not(.show):not(.active) {
    display: none !important;
}

/* Make troubleshooting container use flex row layout */
#troubleshooting-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 15px;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
}

/* Set fixed width for the diagnostic cards to ensure they're side by side */
#git-debug-card, #api-check-card {
    flex: 1 1 calc(50% - 15px);
    min-width: 300px;
    max-width: calc(50% - 15px);
    margin: 0;
}

/* Ensure cards fill height appropriately */
#git-debug-card .card, #api-check-card .card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Fix scrolling in card bodies */
#git-debug-card .card-body, #api-check-card .card-body {
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 320px);
}

/* Remove any row margins that might affect layout */
#troubleshooting-container.row {
    margin-left: 0;
    margin-right: 0;
}

/* For mobile devices */
@media (max-width: 768px) {
    .container {
        height: calc(100vh - 20px);
        padding-bottom: 10px;
    }
    
    .dashboard-header {
        margin-bottom: 10px;
    }
    
    .nav-tabs {
        margin-bottom: 10px !important;
    }
    
    #dashboard-log-content {
        max-height: none;
    }
    
    /* Make troubleshooting cards stack on mobile */
    #git-debug-card, #api-check-card {
        flex: 1 1 100%;
        max-width: 100%;
        margin-bottom: 15px;
    }
}
