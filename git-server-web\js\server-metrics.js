/**
 * Server Metrics Module
 * Handles fetching and displaying current server metrics (CPU, memory, disk, uptime)
 */
const ServerMetricsManager = {
    // Configuration
    config: {
        endpoints: {
            current: '/api/metrics/current'
        },
        refreshInterval: 60000, // Default: 1 minute
        refreshOptions: [
            { label: '30 seconds', value: 30000 },
            { label: '1 minute', value: 60000 },
            { label: '5 minutes', value: 300000 },
            { label: '15 minutes', value: 900000 },
            { label: '30 minutes', value: 1800000 }
        ]
    },
    
    // Application state
    state: {
        metricsData: null,
        lastUpdated: null,
        status: 'loading',
        refreshTimer: null,
        countdownTimer: null,
        nextRefreshTime: null
    },
    
    /**
     * Initialize the Server Metrics module
     */
    init() {
        console.log('Initializing Server Metrics Manager...');
        
        // Log initialization if logger is available
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Server Metrics Manager initialized', 'metrics');
        }
        
        // Load saved refresh interval from localStorage if available
        this.loadUserPreferences();
        
        // Add refresh controls to header
        this.addRefreshControls();
        
        // Initialize performance gauges if available
        if (window.PerformanceGauges && window.PerformanceGauges.init) {
            window.PerformanceGauges.init();
        }
        
        // Fetch initial data
        this.fetchMetrics();
        
        // Set up auto-refresh
        this.startRefreshTimer();
        
        // Listen for dashboard refresh button clicks
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.fetchMetrics();
            });
        }
        
        return this;
    },
    
    /**
     * Load user preferences from localStorage
     */
    loadUserPreferences() {
        try {
            const savedInterval = localStorage.getItem('serverMetricsRefreshInterval');
            if (savedInterval) {
                const interval = parseInt(savedInterval, 10);
                if (!isNaN(interval) && interval > 0) {
                    this.config.refreshInterval = interval;
                    console.log(`Loaded saved refresh interval: ${interval}ms`);
                }
            }
        } catch (error) {
            console.warn('Failed to load preferences from localStorage:', error);
        }
    },
    
    /**
     * Save user preferences to localStorage
     */
    saveUserPreferences() {
        try {
            localStorage.setItem('serverMetricsRefreshInterval', this.config.refreshInterval.toString());
        } catch (error) {
            console.warn('Failed to save preferences to localStorage:', error);
        }
    },
    
    /**
     * Add refresh controls to the Server Performance card header
     */
    addRefreshControls() {
        // Find the server metrics card header - updated selector to find the correct header
        const headerDiv = document.querySelector('#server-performance-container').closest('.card').querySelector('.card-header');
        
        if (!headerDiv) {
            console.warn('Could not find server metrics card header');
            return;
        }
        
        // Make sure the status indicator is in the header
        let statusIndicatorSpan = headerDiv.querySelector('#server-status-indicator');
        if (!statusIndicatorSpan) {
            // Create it if it doesn't exist
            const headerText = headerDiv.querySelector('div');
            if (headerText) {
                statusIndicatorSpan = document.createElement('span');
                statusIndicatorSpan.id = 'server-status-indicator';
                statusIndicatorSpan.className = 'status-indicator loading';
                headerText.prepend(statusIndicatorSpan);
            }
        }
        
        // Check if controls already exist to avoid duplicates
        if (headerDiv.querySelector('.refresh-controls')) {
            console.log('Refresh controls already exist');
            return;
        }
        
        // Create refresh controls container
        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'refresh-controls ms-auto';
        
        // Create refresh interval dropdown
        const select = document.createElement('select');
        select.className = 'form-select form-select-sm';
        select.style.width = 'auto';
        select.style.display = 'inline-block';
        
        // Add options
        this.config.refreshOptions.forEach(option => {
            const optionEl = document.createElement('option');
            optionEl.value = option.value;
            optionEl.textContent = `Refresh: ${option.label}`;
            optionEl.selected = this.config.refreshInterval === option.value;
            select.appendChild(optionEl);
        });
        
        // Add change event listener
        select.addEventListener('change', () => {
            const newInterval = parseInt(select.value, 10);
            this.config.refreshInterval = newInterval;
            this.saveUserPreferences();
            this.startRefreshTimer(); // Restart timer with new interval
            
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', `Server metrics refresh interval changed to ${newInterval/1000} seconds`, 'metrics');
            }
        });
        
        // Add countdown indicator
        const countdownSpan = document.createElement('span');
        countdownSpan.className = 'refresh-countdown ms-2 text-muted small';
        countdownSpan.id = 'server-metrics-countdown';
        countdownSpan.textContent = '';
        
        // Add to controls div
        controlsDiv.appendChild(select);
        controlsDiv.appendChild(countdownSpan);
        
        // Insert controls into header
        headerDiv.appendChild(controlsDiv);
        
        // Add flex class to header for proper alignment
        headerDiv.classList.add('d-flex', 'justify-content-between', 'align-items-center');
        
        // Keep only the text "Server Performance" without any other elements
        const headerTextEl = headerDiv.querySelector('div');
        if (headerTextEl) {
            // Simplify header text, keeping only the status indicator and text
            const originalText = headerTextEl.textContent.trim();
            headerTextEl.innerHTML = '';
            headerTextEl.appendChild(statusIndicatorSpan);
            headerTextEl.appendChild(document.createTextNode(' Server Performance'));
        }
    },
    
    /**
     * Start the refresh timer
     */
    startRefreshTimer() {
        // Clear any existing timer
        if (this.state.refreshTimer) {
            clearInterval(this.state.refreshTimer);
            clearInterval(this.state.countdownTimer);
        }
        
        // Calculate next refresh time
        this.state.nextRefreshTime = Date.now() + this.config.refreshInterval;
        
        // Start a new timer
        this.state.refreshTimer = setInterval(() => {
            this.fetchMetrics();
        }, this.config.refreshInterval);
        
        // Start countdown display timer
        this.updateCountdown();
        this.state.countdownTimer = setInterval(() => {
            this.updateCountdown();
        }, 1000); // Update every second
        
        console.log(`Server metrics refresh timer started: ${this.config.refreshInterval}ms`);
    },
    
    /**
     * Update the countdown display
     */
    updateCountdown() {
        const countdownEl = document.getElementById('server-metrics-countdown');
        if (!countdownEl || !this.state.nextRefreshTime) return;
        
        const remainingMs = this.state.nextRefreshTime - Date.now();
        if (remainingMs <= 0) {
            countdownEl.textContent = 'Refreshing...';
            return;
        }
        
        const seconds = Math.ceil(remainingMs / 1000);
        countdownEl.textContent = `Next: ${seconds}s`;
    },
    
    /**
     * Update module status
     * @param {string} status - New status (loading, success, error)
     */
    updateStatus(status) {
        this.state.status = status;
        
        // Update UI indicators if they exist
        const statusIndicator = document.getElementById('server-status-indicator');
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator ' + status;
        }
    },
    
    /**
     * Fetch current server metrics
     * @returns {Promise<Object>} Metrics data
     */
    async fetchMetrics() {
        this.updateStatus('loading');
        
        try {
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', 'Fetching current server metrics', 'metrics');
            }
            
            // Reset next refresh time
            this.state.nextRefreshTime = Date.now() + this.config.refreshInterval;
            
            const response = await fetch(this.config.endpoints.current);
            if (!response.ok) {
                const errorMsg = `Failed to fetch current metrics: ${response.status}`;
                if (window.DashboardLogger) {
                    window.DashboardLogger.addLog('error', errorMsg, 'metrics');
                }
                throw new Error(errorMsg);
            }
            
            const data = await response.json();
            console.log('Current server metrics:', data);
            this.state.metricsData = data;
            
            this.state.lastUpdated = new Date();
            this.updateStatus('success');
            this.updateMetricsDisplay();
            
            // Update global last updated time
            const lastUpdatedEl = document.getElementById('last-updated');
            if (lastUpdatedEl && this.state.lastUpdated) {
                lastUpdatedEl.textContent = this.state.lastUpdated.toLocaleString();
            }
            
            return data;
        } catch (error) {
            console.error('Error fetching server metrics:', error);
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Error fetching server metrics: ${error.message}`, 'metrics');
            }
            this.updateStatus('error');
            throw error;
        }
    },
    
    /**
     * Update the server metrics display
     */
    updateMetricsDisplay() {
        if (!this.state.metricsData) return;
        
        try {
            const data = this.state.metricsData;
            
            // Extract metrics based on data structure
            let cpuUsage = 0;
            let memoryUsage = 0;
            let diskUsage = 0;
            let uptime = 'Unknown';
            
            // Handle both data formats (flat or nested)
            if (data.system) {
                // Nested format
                cpuUsage = data.system.cpu_usage || 0;
                memoryUsage = data.system.mem_usage || 0;
                diskUsage = data.system.disk_usage || 0;
                uptime = data.system.uptime || 'Unknown';
            } else {
                // Flat format
                cpuUsage = data.cpu_usage || 0;
                memoryUsage = data.mem_usage || 0;
                diskUsage = data.disk_usage || 0;
                uptime = data.uptime || 'Unknown';
            }
            
            // Convert strings to numbers if needed
            cpuUsage = typeof cpuUsage === 'string' ? parseFloat(cpuUsage) : cpuUsage;
            memoryUsage = typeof memoryUsage === 'string' ? parseFloat(memoryUsage) : memoryUsage;
            diskUsage = typeof diskUsage === 'string' ? parseFloat(diskUsage) : diskUsage;
            
            // Keep the legacy display for backward compatibility
            const updateElement = (id, value) => {
                const element = document.getElementById(id);
                if (element) {
                    if (typeof value === 'number') {
                        element.textContent = `${value}%`;
                    } else {
                        element.textContent = value;
                    }
                }
            };
            
            // Update original UI elements (kept for backward compatibility)
            updateElement('cpu-usage', cpuUsage);
            updateElement('memory-usage', memoryUsage);
            updateElement('disk-usage', diskUsage);
            updateElement('uptime', uptime);
            
            // Update gauges if available
            if (window.PerformanceGauges && window.PerformanceGauges.updateGauges) {
                window.PerformanceGauges.updateGauges({
                    cpu: cpuUsage,
                    memory: memoryUsage,
                    disk: diskUsage,
                    uptime: uptime
                });
            }
            
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', 'Updated server metrics display', 'metrics');
            }
        } catch (error) {
            console.error('Error updating server metrics display:', error);
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Error updating metrics display: ${error.message}`, 'metrics');
            }
        }
    }
};

// Expose the module globally
window.ServerMetricsManager = ServerMetricsManager;
