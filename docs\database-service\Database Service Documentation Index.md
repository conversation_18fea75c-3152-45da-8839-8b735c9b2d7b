# Database Service Documentation Index

This document serves as the central index for all Database Service documentation.

## Documentation Contents

1. [**Architecture and Design**](./architecture-and-design.md)
   - Layered architecture
   - Component-based structure
   - Multi-threaded architecture for asynchronous operations
   - Multi-database architecture
   - Security model
   - Error handling and resilience

2. [**Directory Structure**](./directory-structure.md)
   - Project organization
   - Source code directory
   - Supporting files directory
   - Documentation directory
   - Production server structure

3. [**Installation Guide**](./installation.md)
   - Prerequisites
   - Building from source
   - Deployment options
   - Configuration

4. [**API Reference**](./api-reference.md)
   - Endpoints
   - Request/response formats
   - Authentication
   - Error handling

5. [**Security Features**](./security-features.md)
   - Enhanced token-based authentication
   - SSL/TLS support
   - CORS support
   - Secure credential storage

6. [**Database Metrics**](./database-metrics.md)
   - Connection pool metrics
   - Query performance metrics
   - Transaction metrics
   - Authentication metrics
   - API endpoints for metrics

7. [**Client Library**](./client.md)
   - Client library usage
   - Integration examples
   - Error handling
   - Advanced features

8. [**Certificate Access**](./certificate-access.md)
   - Certificate requirements
   - Access configuration
   - Security considerations
   - Troubleshooting

9. [**Configuration Validation**](./configuration-validation.md)
   - Configuration options
   - Validation rules
   - Environment variables
   - Default values

10. [**User Interface**](./ui-documentation.md)
    - Features and functionality (authentication, dashboard, metrics, credentials, settings, database management, legal pages, dev bypass banner)
    - Architecture and components
    - API integration (including /database-api/databases)
    - Deployment and customization
    - Responsive layout and accessibility

11. [**Integration Guide**](./integration-guide.md)
    - Complete integration between Database Service C++23 application and React/TypeScript UI
    - Development and production environment setup
    - API integration and authentication flow
    - Nginx reverse proxy configuration
    - Deployment procedures and configuration management

12. [**SSL Termination Strategy**](./ssl-termination-strategy.md)
    - Detailed rationale for reverse proxy SSL termination approach
    - Performance benchmarks and operational benefits
    - Implementation details and configuration changes
    - Security considerations and best practices
    - Monitoring and maintenance procedures

13. [**Advanced Integration**](./integration-advanced.md)
    - Security integration (JWT tokens, CORS, SSL/TLS)
    - Monitoring and metrics collection
    - Performance optimization techniques
    - Component optimization and bundle management

14. [**Troubleshooting and Maintenance**](./troubleshooting-maintenance.md)
    - Common integration issues and solutions
    - Performance troubleshooting procedures
    - Regular maintenance tasks and automation
    - Backup and disaster recovery procedures
    - Monitoring setup and alerting

14. [**Deployment Checklist**](./deployment-checklist.md)
    - Pre-deployment infrastructure requirements
    - Step-by-step deployment procedures
    - Post-deployment verification tasks
    - Security hardening checklist
    - Monitoring and backup configuration

15. [**Future Enhancements**](./future-enhancements.md)
    - Planned features and improvements
    - Implementation roadmap
    - Development priorities

## Source Code Documentation

- [**DATABASE-SERVICE.md**](../database-service/DATABASE-SERVICE.md): Main source code documentation
- [**DATABASE-SCRIPTS.md**](../database-service-scripts/DATABASE-SCRIPTS.md): Supporting files documentation

## Production Server

- **Hostname**: git.chcit.org
- **IP Address**: ***********
- **SSH User**: btaylor-admin
- **SSH Port**: 22
