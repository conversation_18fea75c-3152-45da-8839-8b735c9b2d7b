# Client Library

> **Note:** The project has been reorganized. The client library implementation is now in the `/src/client` directory of the database-service-traditional project.

The Database Service includes a C++ client library for easy integration with other applications. This document provides a detailed reference for the client library.

## Overview

The client library provides a simple interface for interacting with the Database Service API. It handles authentication, request formatting, and response parsing, allowing you to focus on your application logic.

## Installation

The client library is included with the Database Service. To use it in your application, include the header files and link against the library:

```cpp
#include "database-service/client/database_client.hpp"
```

## Basic Usage

### Creating a Client

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>

int main() {
    try {
        // Create database client
        dbservice::client::DatabaseClient client(
            "http://localhost:8080",  // Service URL
            "git-repo-service",       // Application name
            "api-key-123",            // API key
            "git_repo_db"             // Database name
        );

        // Use the client...

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
```

### Checking Database Availability

```cpp
// Check if database is available
if (!client.isAvailable()) {
    std::cerr << "Database is not available" << std::endl;
    return 1;
}

// Get database version
std::string version = client.getVersion();
std::cout << "Connected to database version: " << version << std::endl;
```

### Executing Queries

```cpp
// Execute a query
nlohmann::json results = client.query(
    "SELECT * FROM repositories WHERE owner = $1",
    {{"$1", "btaylor-admin"}},
    "public"  // Schema name (optional)
);

// Display results
for (const auto& row : results) {
    std::cout << "ID: " << row["id"].get<int>() << ", "
             << "Name: " << row["name"].get<std::string>() << ", "
             << "Owner: " << row["owner"].get<std::string>() << std::endl;
}
```

### Executing Statements

```cpp
// Execute a statement
int rowsAffected = client.execute(
    "INSERT INTO repositories (name, owner, path) VALUES ($1, $2, $3)",
    {{"$1", "new-repo"}, {"$2", "btaylor-admin"}, {"$3", "/path/to/repo"}},
    "public"  // Schema name (optional)
);

std::cout << "Inserted " << rowsAffected << " row(s)" << std::endl;
```

### Executing Transactions

```cpp
// Execute a transaction
nlohmann::json results = client.transaction({
    "INSERT INTO repositories (name, owner, path) VALUES ('new-repo', 'btaylor-admin', '/path/to/repo')",
    "INSERT INTO repository_metadata (repository_id, metadata) VALUES (LASTVAL(), '{\\"language\\": \\"C++\\"}');"
}, "public");  // Schema name (optional)

std::cout << "Transaction results: " << results.dump(2) << std::endl;
```

### Getting Schema Information

```cpp
// Get database schema
nlohmann::json schema = client.getSchema();

// Display schema
for (const auto& table : schema) {
    std::cout << "Table: " << table["name"].get<std::string>() << " ("
             << table["schema"].get<std::string>() << ")" << std::endl;

    std::cout << "  Columns:" << std::endl;
    for (const auto& column : table["columns"]) {
        std::cout << "    " << column["name"].get<std::string>() << " ("
                 << column["type"].get<std::string>() << ")"
                 << (column["nullable"].get<bool>() ? " NULL" : " NOT NULL");

        if (column.contains("default")) {
            std::cout << " DEFAULT " << column["default"].get<std::string>();
        }

        std::cout << std::endl;
    }
}
```

### Working with JSON Data

```cpp
// Store JSON document
nlohmann::json document = {
    {"repository_id", 123},
    {"language_stats", {
        {"C++", 75},
        {"Python", 20},
        {"JavaScript", 5}
    }},
    {"tags", {"backend", "api", "core"}}
};

nlohmann::json result = client.storeJson(
    "repository_metadata",  // Table name
    document,               // JSON document
    "metadata"              // Schema name (optional)
);

std::cout << "Document stored with ID: " << result["id"].get<int>() << std::endl;

// Query JSON documents
nlohmann::json query = {
    {"language_stats.C++", {{"$gt", 50}}},
    {"tags", {{"$contains", "api"}}}
};

nlohmann::json queryResults = client.queryJson(
    "repository_metadata",  // Table name
    query,                  // JSON query
    "metadata"              // Schema name (optional)
);

for (const auto& doc : queryResults) {
    std::cout << "Document ID: " << doc["id"].get<int>() << std::endl;
    std::cout << "Repository ID: " << doc["repository_id"].get<int>() << std::endl;
    std::cout << "Languages: " << doc["document"]["language_stats"].dump() << std::endl;
    std::cout << "Tags: " << doc["document"]["tags"].dump() << std::endl;
}
```

## Advanced Usage

### Custom Error Handling

```cpp
try {
    // Execute a query
    nlohmann::json results = client.query(
        "SELECT * FROM non_existent_table"
    );
} catch (const dbservice::client::DatabaseError& e) {
    std::cerr << "Database error: " << e.what() << std::endl;
    std::cerr << "Error code: " << e.getCode() << std::endl;
} catch (const dbservice::client::AuthenticationError& e) {
    std::cerr << "Authentication error: " << e.what() << std::endl;
} catch (const dbservice::client::ConnectionError& e) {
    std::cerr << "Connection error: " << e.what() << std::endl;
} catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
}
```

### Custom HTTP Headers

```cpp
// Set custom HTTP headers
client.setHeader("X-Custom-Header", "custom-value");

// Execute a query with custom headers
nlohmann::json results = client.query(
    "SELECT * FROM repositories",
    {},
    "public",
    {"X-Another-Header", "another-value"}
);
```

### Connection Pooling

```cpp
// Create a connection pool
dbservice::client::ConnectionPool pool(
    "http://localhost:8080",  // Service URL
    "git-repo-service",       // Application name
    "api-key-123",            // API key
    "git_repo_db",            // Database name
    10                        // Pool size
);

// Get a client from the pool
auto client = pool.getClient();

// Use the client
nlohmann::json results = client->query("SELECT * FROM repositories");

// Client is automatically returned to the pool when it goes out of scope
```

### Asynchronous Queries

```cpp
// Execute a query asynchronously with a callback
client.queryAsync(
    "SELECT * FROM repositories WHERE owner = $1",
    {{"$1", "btaylor-admin"}},
    [](const auto& result) {
        if (result) {
            // Process results
            for (const auto& row : result.value()) {
                std::cout << "ID: " << row["id"].get<int>() << ", "
                         << "Name: " << row["name"].get<std::string>() << std::endl;
            }
        } else {
            std::cerr << "Error: " << result.error() << std::endl;
        }
    },
    "public"  // Schema name (optional)
);

// Do other work while the query is executing
// ...

// Using std::future with a promise
std::promise<nlohmann::json> resultPromise;
std::future<nlohmann::json> resultFuture = resultPromise.get_future();

client.queryAsync(
    "SELECT * FROM repositories WHERE owner = $1",
    {{"$1", "btaylor-admin"}},
    [&resultPromise](auto result) {
        if (result) {
            resultPromise.set_value(result.value());
        } else {
            resultPromise.set_exception(std::make_exception_ptr(
                std::runtime_error(result.error())
            ));
        }
    },
    "public"  // Schema name (optional)
);

// Get the results
try {
    nlohmann::json results = resultFuture.get();

    // Process results
    for (const auto& row : results) {
        std::cout << "ID: " << row["id"].get<int>() << ", "
                 << "Name: " << row["name"].get<std::string>() << std::endl;
    }
} catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
}
```

## Integration with Git Repository Service

The client library can be used to integrate the Database Service with the Git Repository Service:

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>
#include <string>
#include <vector>

// Example repository structure
struct Repository {
    int id;
    std::string name;
    std::string owner;
    std::string path;
    std::string description;
    std::chrono::system_clock::time_point created_at;
    std::chrono::system_clock::time_point updated_at;
};

// Example repository service that uses the database client
class GitRepositoryService {
public:
    GitRepositoryService(const std::string& dbServiceUrl, const std::string& apiKey)
        : dbClient_(dbServiceUrl, "git-repo-service", apiKey, "git_repo_db") {

        // Initialize database schema if needed
        initializeSchema();
    }

    // Get all repositories
    std::vector<Repository> getAllRepositories() {
        try {
            // Query repositories
            nlohmann::json results = dbClient_.query(
                "SELECT * FROM repositories ORDER BY name"
            );

            // Convert to repository objects
            std::vector<Repository> repositories;
            for (const auto& row : results) {
                Repository repo{
                    row["id"].get<int>(),
                    row["name"].get<std::string>(),
                    row["owner"].get<std::string>(),
                    row["path"].get<std::string>(),
                    row.contains("description") ? row["description"].get<std::string>() : "",
                    parseTimestamp(row["created_at"].get<std::string>()),
                    parseTimestamp(row["updated_at"].get<std::string>())
                };
                repositories.push_back(repo);
            }

            return repositories;
        } catch (const std::exception& e) {
            std::cerr << "Error getting repositories: " << e.what() << std::endl;
            return {};
        }
    }

    // Get repository by ID
    std::optional<Repository> getRepositoryById(int id) {
        try {
            // Query repository
            nlohmann::json results = dbClient_.query(
                "SELECT * FROM repositories WHERE id = $1",
                {{"$1", std::to_string(id)}}
            );

            // Check if repository exists
            if (results.empty()) {
                return std::nullopt;
            }

            // Convert to repository object
            const auto& row = results[0];
            Repository repo{
                row["id"].get<int>(),
                row["name"].get<std::string>(),
                row["owner"].get<std::string>(),
                row["path"].get<std::string>(),
                row.contains("description") ? row["description"].get<std::string>() : "",
                parseTimestamp(row["created_at"].get<std::string>()),
                parseTimestamp(row["updated_at"].get<std::string>())
            };

            return repo;
        } catch (const std::exception& e) {
            std::cerr << "Error getting repository: " << e.what() << std::endl;
            return std::nullopt;
        }
    }

    // Create repository
    bool createRepository(const Repository& repo) {
        try {
            // Insert repository
            int rowsAffected = dbClient_.execute(
                "INSERT INTO repositories (name, owner, path, description) "
                "VALUES ($1, $2, $3, $4)",
                {
                    {"$1", repo.name},
                    {"$2", repo.owner},
                    {"$3", repo.path},
                    {"$4", repo.description}
                }
            );

            return rowsAffected > 0;
        } catch (const std::exception& e) {
            std::cerr << "Error creating repository: " << e.what() << std::endl;
            return false;
        }
    }

    // Store repository metadata as JSON
    bool storeRepositoryMetadata(int repositoryId, const nlohmann::json& metadata) {
        try {
            // Store metadata
            nlohmann::json document = {
                {"repository_id", repositoryId},
                {"metadata", metadata}
            };

            nlohmann::json result = dbClient_.storeJson(
                "repository_metadata",
                document,
                "metadata"
            );

            return result.contains("id");
        } catch (const std::exception& e) {
            std::cerr << "Error storing repository metadata: " << e.what() << std::endl;
            return false;
        }
    }

private:
    // Initialize database schema
    void initializeSchema() {
        try {
            // Create repositories table if it doesn't exist
            dbClient_.execute(
                "CREATE TABLE IF NOT EXISTS repositories ("
                "  id SERIAL PRIMARY KEY,"
                "  name VARCHAR(100) NOT NULL,"
                "  owner VARCHAR(100) NOT NULL,"
                "  path VARCHAR(255) NOT NULL,"
                "  description TEXT,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),"
                "  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");"
            );

            // Create repository_metadata table if it doesn't exist
            dbClient_.execute(
                "CREATE TABLE IF NOT EXISTS repository_metadata ("
                "  id SERIAL PRIMARY KEY,"
                "  repository_id INTEGER NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,"
                "  metadata JSONB NOT NULL,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");",
                {},
                "metadata"  // Use metadata schema
            );

            // Create indexes
            dbClient_.execute(
                "CREATE INDEX IF NOT EXISTS idx_repositories_name ON repositories(name);"
            );
            dbClient_.execute(
                "CREATE INDEX IF NOT EXISTS idx_repositories_owner ON repositories(owner);"
            );
            dbClient_.execute(
                "CREATE INDEX IF NOT EXISTS idx_repository_metadata_repository_id "
                "ON metadata.repository_metadata(repository_id);",
                {},
                "metadata"  // Use metadata schema
            );
        } catch (const std::exception& e) {
            std::cerr << "Error initializing schema: " << e.what() << std::endl;
        }
    }

    // Parse timestamp string to time_point
    std::chrono::system_clock::time_point parseTimestamp(const std::string& timestamp) {
        // Implementation of timestamp parsing
        // ...
        return std::chrono::system_clock::now();  // Placeholder
    }

    dbservice::client::DatabaseClient dbClient_;
};
```

## Integration with Logging Service

The client library can be used to integrate the Database Service with the Logging Service:

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>

// Example log entry structure
struct LogEntry {
    std::string timestamp;
    std::string level;
    std::string source;
    std::string message;
    std::string details;
};

// Format timestamp for database
std::string formatTimestamp(const std::chrono::system_clock::time_point& timePoint) {
    auto time = std::chrono::system_clock::to_time_t(timePoint);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// Example logging service that uses the database client
class LoggingService {
public:
    LoggingService(const std::string& dbServiceUrl, const std::string& apiKey)
        : dbClient_(dbServiceUrl, "logging-service", apiKey, "logging_db") {

        // Initialize database schema if needed
        initializeSchema();
    }

    // Log a message
    void log(const std::string& level, const std::string& source, const std::string& message, const std::string& details = "") {
        try {
            // Create log entry
            LogEntry entry{
                formatTimestamp(std::chrono::system_clock::now()),
                level,
                source,
                message,
                details
            };

            // Store in database
            storeLogEntry(entry);
        } catch (const std::exception& e) {
            std::cerr << "Error logging message: " << e.what() << std::endl;
        }
    }

    // Query logs
    std::vector<LogEntry> queryLogs(const std::string& level = "", const std::string& source = "", int limit = 100) {
        try {
            // Build query
            std::string query = "SELECT timestamp, level, source, message, details FROM logs WHERE 1=1";

            std::unordered_map<std::string, std::string> params;

            if (!level.empty()) {
                query += " AND level = $1";
                params["$1"] = level;
            }

            if (!source.empty()) {
                query += " AND source = $" + std::to_string(params.size() + 1);
                params["$" + std::to_string(params.size())] = source;
            }

            query += " ORDER BY timestamp DESC LIMIT " + std::to_string(limit);

            // Execute query
            nlohmann::json results = dbClient_.query(query, params);

            // Convert to log entries
            std::vector<LogEntry> logs;
            for (const auto& row : results) {
                LogEntry entry{
                    row["timestamp"].get<std::string>(),
                    row["level"].get<std::string>(),
                    row["source"].get<std::string>(),
                    row["message"].get<std::string>(),
                    row.contains("details") ? row["details"].get<std::string>() : ""
                };
                logs.push_back(entry);
            }

            return logs;
        } catch (const std::exception& e) {
            std::cerr << "Error querying logs: " << e.what() << std::endl;
            return {};
        }
    }

    // Archive old logs
    bool archiveLogs(const std::string& olderThan) {
        try {
            // Move logs to archive schema
            int rowsAffected = dbClient_.execute(
                "INSERT INTO archive.logs "
                "SELECT * FROM logs WHERE timestamp < $1",
                {{"$1", olderThan}}
            );

            // Delete archived logs from main table
            if (rowsAffected > 0) {
                dbClient_.execute(
                    "DELETE FROM logs WHERE timestamp < $1",
                    {{"$1", olderThan}}
                );
            }

            return true;
        } catch (const std::exception& e) {
            std::cerr << "Error archiving logs: " << e.what() << std::endl;
            return false;
        }
    }

private:
    // Initialize database schema
    void initializeSchema() {
        try {
            // Create logs table in public schema
            dbClient_.execute(
                "CREATE TABLE IF NOT EXISTS logs ("
                "  id SERIAL PRIMARY KEY,"
                "  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,"
                "  level VARCHAR(20) NOT NULL,"
                "  source VARCHAR(100) NOT NULL,"
                "  message TEXT NOT NULL,"
                "  details TEXT,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");"
            );

            // Create archive schema if it doesn't exist
            dbClient_.execute("CREATE SCHEMA IF NOT EXISTS archive;");

            // Create archived logs table in archive schema
            dbClient_.execute(
                "CREATE TABLE IF NOT EXISTS archive.logs ("
                "  id SERIAL PRIMARY KEY,"
                "  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,"
                "  level VARCHAR(20) NOT NULL,"
                "  source VARCHAR(100) NOT NULL,"
                "  message TEXT NOT NULL,"
                "  details TEXT,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");",
                {},
                "archive"
            );

            // Create stats schema if it doesn't exist
            dbClient_.execute("CREATE SCHEMA IF NOT EXISTS stats;");

            // Create log statistics table in stats schema
            dbClient_.execute(
                "CREATE TABLE IF NOT EXISTS stats.log_statistics ("
                "  id SERIAL PRIMARY KEY,"
                "  date DATE NOT NULL,"
                "  level VARCHAR(20) NOT NULL,"
                "  source VARCHAR(100) NOT NULL,"
                "  count INTEGER NOT NULL,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");",
                {},
                "stats"
            );

            // Create indexes
            dbClient_.execute("CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);");
            dbClient_.execute("CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);");
            dbClient_.execute("CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);");

            dbClient_.execute(
                "CREATE INDEX IF NOT EXISTS idx_archive_logs_timestamp ON archive.logs(timestamp);",
                {},
                "archive"
            );

            dbClient_.execute(
                "CREATE INDEX IF NOT EXISTS idx_stats_log_statistics_date ON stats.log_statistics(date);",
                {},
                "stats"
            );
        } catch (const std::exception& e) {
            std::cerr << "Error initializing schema: " << e.what() << std::endl;
        }
    }

    // Store log entry in database
    void storeLogEntry(const LogEntry& entry) {
        try {
            // Insert log entry
            dbClient_.execute(
                "INSERT INTO logs (timestamp, level, source, message, details) "
                "VALUES ($1, $2, $3, $4, $5)",
                {
                    {"$1", entry.timestamp},
                    {"$2", entry.level},
                    {"$3", entry.source},
                    {"$4", entry.message},
                    {"$5", entry.details}
                }
            );

            // Update statistics (in a separate transaction)
            updateStatistics(entry);
        } catch (const std::exception& e) {
            std::cerr << "Error storing log entry: " << e.what() << std::endl;
        }
    }

    // Update log statistics
    void updateStatistics(const LogEntry& entry) {
        try {
            // Extract date from timestamp
            std::string date = entry.timestamp.substr(0, 10);

            // Update statistics
            dbClient_.execute(
                "INSERT INTO stats.log_statistics (date, level, source, count) "
                "VALUES ($1, $2, $3, 1) "
                "ON CONFLICT (date, level, source) DO UPDATE "
                "SET count = stats.log_statistics.count + 1",
                {
                    {"$1", date},
                    {"$2", entry.level},
                    {"$3", entry.source}
                },
                "stats"
            );
        } catch (const std::exception& e) {
            std::cerr << "Error updating statistics: " << e.what() << std::endl;
        }
    }

    dbservice::client::DatabaseClient dbClient_;
};
```

## API Reference

### DatabaseClient

```cpp
/**
 * @class DatabaseClient
 * @brief Client for interacting with the database service
 */
class DatabaseClient {
public:
    /**
     * @brief Constructor
     * @param serviceUrl URL of the database service
     * @param applicationName Name of the application
     * @param apiKey API key for authentication
     * @param databaseName Name of the database
     */
    DatabaseClient(const std::string& serviceUrl,
                  const std::string& applicationName,
                  const std::string& apiKey,
                  const std::string& databaseName = "");

    /**
     * @brief Destructor
     */
    ~DatabaseClient();

    /**
     * @brief Execute a query
     * @param query SQL query to execute
     * @param params Query parameters
     * @param schema Schema name (optional)
     * @param headers Additional HTTP headers (optional)
     * @return Query results as JSON
     */
    nlohmann::json query(const std::string& query,
                        const std::unordered_map<std::string, std::string>& params = {},
                        const std::string& schema = "",
                        const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Execute a non-query statement
     * @param statement SQL statement to execute
     * @param params Statement parameters
     * @param schema Schema name (optional)
     * @param headers Additional HTTP headers (optional)
     * @return Number of affected rows
     */
    int execute(const std::string& statement,
               const std::unordered_map<std::string, std::string>& params = {},
               const std::string& schema = "",
               const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Execute a transaction
     * @param statements SQL statements to execute
     * @param schema Schema name (optional)
     * @param headers Additional HTTP headers (optional)
     * @return Transaction results
     */
    nlohmann::json transaction(const std::vector<std::string>& statements,
                              const std::string& schema = "",
                              const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Get database schema
     * @param headers Additional HTTP headers (optional)
     * @return Database schema as JSON
     */
    nlohmann::json getSchema(const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Check database status
     * @param headers Additional HTTP headers (optional)
     * @return True if database is available
     */
    bool isAvailable(const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Get database version
     * @param headers Additional HTTP headers (optional)
     * @return Database version
     */
    std::string getVersion(const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Store JSON document
     * @param table Table name
     * @param document JSON document
     * @param schema Schema name (optional)
     * @param headers Additional HTTP headers (optional)
     * @return Result as JSON
     */
    nlohmann::json storeJson(const std::string& table,
                            const nlohmann::json& document,
                            const std::string& schema = "",
                            const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Query JSON documents
     * @param table Table name
     * @param query JSON query
     * @param schema Schema name (optional)
     * @param headers Additional HTTP headers (optional)
     * @return Query results as JSON
     */
    nlohmann::json queryJson(const std::string& table,
                            const nlohmann::json& query,
                            const std::string& schema = "",
                            const std::unordered_map<std::string, std::string>& headers = {});

    /**
     * @brief Set custom HTTP header
     * @param name Header name
     * @param value Header value
     */
    void setHeader(const std::string& name, const std::string& value);

    /**
     * @brief Execute a query asynchronously
     * @param query SQL query to execute
     * @param params Query parameters
     * @param schema Schema name (optional)
     * @param headers Additional HTTP headers (optional)
     * @return Future with query results
     */
    std::future<nlohmann::json> queryAsync(const std::string& query,
                                         const std::unordered_map<std::string, std::string>& params = {},
                                         const std::string& schema = "",
                                         const std::unordered_map<std::string, std::string>& headers = {});
};
```

### ConnectionPool

```cpp
/**
 * @class ConnectionPool
 * @brief Pool of database clients
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param serviceUrl URL of the database service
     * @param applicationName Name of the application
     * @param apiKey API key for authentication
     * @param databaseName Name of the database
     * @param poolSize Number of clients to maintain in the pool
     */
    ConnectionPool(const std::string& serviceUrl,
                  const std::string& applicationName,
                  const std::string& apiKey,
                  const std::string& databaseName = "",
                  size_t poolSize = 10);

    /**
     * @brief Destructor
     */
    ~ConnectionPool();

    /**
     * @brief Get a client from the pool
     * @return Shared pointer to a client
     */
    std::shared_ptr<DatabaseClient> getClient();
};
```

### Exceptions

```cpp
/**
 * @class DatabaseError
 * @brief Base class for database errors
 */
class DatabaseError : public std::runtime_error {
public:
    /**
     * @brief Constructor
     * @param message Error message
     * @param code Error code
     */
    DatabaseError(const std::string& message, int code);

    /**
     * @brief Get error code
     * @return Error code
     */
    int getCode() const;
};

/**
 * @class AuthenticationError
 * @brief Authentication error
 */
class AuthenticationError : public DatabaseError {
public:
    /**
     * @brief Constructor
     * @param message Error message
     */
    AuthenticationError(const std::string& message);
};

/**
 * @class ConnectionError
 * @brief Connection error
 */
class ConnectionError : public DatabaseError {
public:
    /**
     * @brief Constructor
     * @param message Error message
     */
    ConnectionError(const std::string& message);
};
```


