# Git Dashboard Web Server Setup Guide

## Overview

This document provides instructions for setting up the Git Dashboard web interface that complements the Git server. The dashboard provides real-time visualization of server metrics, repository statistics, and Git activity through an intuitive web interface.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Setup](#quick-setup)
3. [Manual Installation](#manual-installation)
4. [SSH Key Configuration](#ssh-key-configuration)
5. [SSL Certificate Configuration](#ssl-certificate-configuration)
6. [Network Configuration](#network-configuration)
7. [Security Features](#security-features)
8. [User Permissions](#user-permissions)
9. [Monitoring](#monitoring)
10. [Troubleshooting](#troubleshooting)
11. [Maintenance Tasks](#maintenance-tasks)

## Prerequisites

- Ubuntu Server (20.04 LTS or newer)
- Root access (required)
- Git repository at `/home/<USER>/project-tracker.git`
- Domain name pointing to your server (for SSL setup)
- A non-root user with sudo privileges for secure SSH operations

## Network Configuration

The dashboard is configured with the following network settings:

1. **IP Binding**:
   - Default: `0.0.0.0` (all interfaces)
   - Can be modified to bind to specific IP in `setup-git-dashboard.sh`
   - Nginx listens on port 80 (HTTP) and 443 (HTTPS)
   - Flask backend runs on port 8000 (internal)

2. **Firewall Rules**:
   - Port 80 (HTTP) open
   - Port 443 (HTTPS) open for SSL
   - Internal port 8000 should NOT be exposed externally
   - UFW enabled by default

3. **Proxy Configuration**:
   - Nginx proxies API requests to Flask backend
   - Rate limiting enabled (10 req/sec with burst)
   - All external traffic handled by Nginx
   - CORS headers configured for security

To modify these settings, edit the following variables in `setup-git-dashboard.sh`:
```bash
BIND_IP="0.0.0.0"    # Change to specific IP if needed
BIND_PORT="80"       # External HTTP port
FLASK_PORT="8000"    # Internal Flask port
```

## Quick Setup

1. Copy all files to your server:
```bash
scp -r * root@your-server:/tmp/git-dashboard/
```

2. SSH into your server as root and run:
```bash
cd /tmp/git-dashboard
chmod +x setup-git-dashboard.sh
./setup-git-dashboard.sh
```

The setup script will automatically:
- Install required packages
- Configure network bindings and firewall
- Set up Python environment
- Configure Nginx with security features
- Create systemd service
- Set up metrics collection

## Manual Installation Steps

If you prefer to install manually, follow these steps:

1. Install required packages:
```bash
apt-get update
apt-get install -y python3.10 python3.10-venv python3-pip nginx git supervisor ufw certbot python3-certbot-nginx
```

2. Create installation directory:
```bash
mkdir -p /opt/git-dashboard
mkdir -p /opt/git-dashboard/data/history
mkdir -p /opt/git-dashboard/logs
```

3. Copy files and set permissions:
```bash
cp -r * /opt/git-dashboard/
chown -R root:root /opt/git-dashboard
chmod -R 755 /opt/git-dashboard
chmod 644 /opt/git-dashboard/*.html /opt/git-dashboard/js/*.js /opt/git-dashboard/css/*.css
chmod 755 /opt/git-dashboard/app.py /opt/git-dashboard/collect-metrics.sh
```

4. Set up Python virtual environment:
```bash
cd /opt/git-dashboard
python3.10 -m venv venv
./venv/bin/pip install -r requirements.txt
```

5. Configure Nginx and systemd:
```bash
ln -s /opt/git-dashboard/nginx/git-dashboard.conf /etc/nginx/sites-enabled/
cp /opt/git-dashboard/systemd/git-dashboard.service /etc/systemd/system/
```

6. Configure firewall:
```bash
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable
```

7. Start services:
```bash
systemctl daemon-reload
systemctl enable git-dashboard
systemctl start git-dashboard
systemctl restart nginx
```

## SSH Key Configuration

To synchronize SSL certificates between the main Git server and dashboard server, you need to set up SSH key authentication:

1. Create SSH key pair for the root user on the dashboard server:
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
```

2. Add the public key to authorized_keys on the Git server:
```bash
# On the dashboard server
cat ~/.ssh/id_ed25519.pub | ssh user@git-server "mkdir -p ~/.ssh && chmod 700 ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys"
```

3. Test the connection:
```bash
ssh -i ~/.ssh/id_ed25519 user@git-server "echo Connection successful"
```

4. Create a specific SSH config for the Git server:
```bash
cat > ~/.ssh/config << EOF
Host git-server
    HostName your-git-server-ip
    User git
    IdentityFile ~/.ssh/id_ed25519
    IdentitiesOnly yes
EOF
chmod 600 ~/.ssh/config
```

## SSL Certificate Configuration

For secure HTTPS connections, set up SSL certificates using Let's Encrypt:

1. Obtain certificates:
```bash
certbot --nginx -d your-dashboard-domain.com
```

2. Create SSL certificate sync script for Git server synchronization:
```bash
cat > /usr/local/bin/sync-ssl-certs.sh << EOF
#!/bin/bash

# Sync SSL certificates to Git server
timestamp=$(date '+%Y-%m-%d_%H-%M-%S')
cd /etc/letsencrypt/
tar -czf /tmp/letsencrypt-backup-$timestamp.tar.gz live archive renewal
scp /tmp/letsencrypt-backup-$timestamp.tar.gz git-server:/tmp/
ssh git-server "sudo mkdir -p /etc/letsencrypt && sudo tar -xzf /tmp/letsencrypt-backup-$timestamp.tar.gz -C /etc/letsencrypt/ && sudo systemctl reload nginx"
rm /tmp/letsencrypt-backup-$timestamp.tar.gz
EOF
```

3. Make the script executable and set up a hook for certificate renewal:
```bash
chmod +x /usr/local/bin/sync-ssl-certs.sh
mkdir -p /etc/letsencrypt/renewal-hooks/post
ln -s /usr/local/bin/sync-ssl-certs.sh /etc/letsencrypt/renewal-hooks/post/sync-ssl-certs.sh
```

## User Permissions

Proper user permissions are critical for security and functionality:

1. **Dashboard Server**:
   - All dashboard files should be owned by `root:root`
   - Use permission 755 for directories and executable scripts
   - Use permission 644 for configuration files
   - The data directory needs to be writable by the service user

2. **Git Server**:
   - The `git` user should have restricted permissions
   - SSH access should be limited to specific users
   - Create a dedicated user (e.g., `git-sync`) for SSL certificate synchronization
   - Add the sync user to the `sudo` group for certificate operations

3. **Required User Setup on Git Server**:
   ```bash
   # Create sync user on Git server
   sudo adduser git-sync
   sudo usermod -aG sudo git-sync
   
   # Configure sudoers for limited commands
   echo "git-sync ALL=(ALL) NOPASSWD: /bin/systemctl reload nginx, /bin/tar, /bin/mkdir -p /etc/letsencrypt" | sudo tee /etc/sudoers.d/git-sync
   ```

## Security Features

The dashboard includes several security measures:

1. **Network Security**:
   - Configurable IP binding
   - UFW firewall enabled
   - Internal services bound to localhost
   - Rate limiting on API endpoints
   - HTTPS with strong cipher configuration

2. **Access Control**:
   - Running as root for full system access
   - Proper file permissions (755 for directories, 644 for files)
   - Protected system directories
   - Least privilege principle for service accounts

3. **Application Security**:
   - Security headers (CSP, XSS Protection)
   - File access restrictions
   - Resource limits via systemd
   - Request validation
   - Regular security updates

## Monitoring

The dashboard provides comprehensive monitoring:

1. **Server Metrics**:
   - CPU usage
   - Memory usage
   - Disk usage
   - System load
   - Uptime

2. **Git Statistics**:
   - Total repositories
   - Active repositories
   - Total commits
   - Active branches
   - Recent commit history

3. **Data Collection**:
   - Metrics collected every minute
   - History retained for 24 hours
   - Configurable retention period
   - Efficient JSON storage format

## Troubleshooting

1. Check service status:
```bash
systemctl status git-dashboard
systemctl status nginx
```

2. View logs:
```bash
tail -f /opt/git-dashboard/logs/git-dashboard.log
tail -f /var/log/nginx/git-dashboard.error.log
```

3. Test network binding:
```bash
# Check listening ports
netstat -tulpn | grep -E ':(80|8000)'
# Test API endpoint
curl http://localhost/api/metrics
```

4. Common Issues:

   a. Service won't start:
   ```bash
   # Check Python environment
   /opt/git-dashboard/venv/bin/python -V
   # Check systemd logs
   journalctl -u git-dashboard -n 50
   ```

   b. Network issues:
   ```bash
   # Check UFW status
   ufw status
   # Test Nginx configuration
   nginx -t
   ```

   c. Permission issues:
   ```bash
   # Fix permissions
   chown -R root:root /opt/git-dashboard
   chmod -R 755 /opt/git-dashboard
   ```

   d. Dashboard stuck on "Loading dashboard data...":
   ```bash
   # Check if API is responding
   curl http://localhost:8000/api/metrics
   ```
   - Check browser console for JavaScript errors
     - Verify API responses with the debug.html page
     - Ensure the metrics collection script is running properly
     - Check file permissions on data directory
     - Verify JSON format in metrics_history.json

5. **Git Data Not Showing**:
   - Check collect-metrics.sh script execution:
   ```bash
   # Run manually to check for errors
   /opt/git-dashboard/collect-metrics.sh
   ```
   - Verify Git repository paths in app.py
   - Ensure Git repositories have proper permissions
   - Check the format of collected data in metrics_history.json

6. **SSL Certificate Issues**:
   - Check certificate status:
   ```bash
   certbot certificates
   ```
   - Test SSL configuration:
   ```bash
   curl -svo /dev/null https://your-domain.com
   ```
   - Verify certificate sync between servers:
   ```bash
   ls -la /etc/letsencrypt/live/
   ```

## Maintenance Tasks

Regular maintenance helps ensure the dashboard continues to run smoothly:

1. **Backup Configuration**:
   ```bash
   tar -czf git-dashboard-backup.tar.gz -C /opt git-dashboard
   ```

2. **Update Dependencies**:
   ```bash
   cd /opt/git-dashboard
   ./venv/bin/pip install --upgrade -r requirements.txt
   ```

3. **Log Rotation**:
   - Ensure log rotation is configured:
   ```bash
   cat > /etc/logrotate.d/git-dashboard << EOF
   /opt/git-dashboard/logs/*.log {
       daily
       missingok
       rotate 14
       compress
       delaycompress
       notifempty
       create 644 root root
   }
   EOF
   ```

4. **Certificate Renewal Testing**:
   ```bash
   certbot renew --dry-run
   ```

5. **Security Updates**:
   ```bash
   apt update && apt upgrade -y
   ```

For additional support:
1. Check all logs mentioned above
2. Verify network configuration
3. Ensure all services are running
4. Check firewall rules
