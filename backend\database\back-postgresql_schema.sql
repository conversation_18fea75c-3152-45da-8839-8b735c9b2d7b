-- Project Tracker PostgreSQL Schema
-- Updated for PostgreSQL compatibility

-- Project Templates Table
CREATE TABLE IF NOT EXISTS project_templates (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL,
    description TEXT,
    git_repository_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Predefined Project Templates
INSERT INTO project_templates (name, type, description, git_repository_url) VALUES
    ('Python Flask Web Service', 'web_service', 'Flask-based web application template', 'https://github.com/project-tracker-templates/flask-template.git'),
    ('Python Asyncio Agent', 'agent_system', 'Asyncio-based background agent template', 'https://github.com/project-tracker-templates/asyncio-template.git'),
    ('Python Library', 'library', 'Standard Python library project template', 'https://github.com/project-tracker-templates/library-template.git'),
    ('Python Asyncio Background Agent', 'agent_system', 'Advanced asynchronous background processing agent', 'https://github.com/project-tracker-templates/asyncio-agent-template.git'),
    ('Python Library with Poetry', 'library', 'Comprehensive Python library template with modern packaging', 'https://github.com/project-tracker-templates/python-library-template.git'),
    ('FastAPI Microservice', 'web_service', 'High-performance async web service with FastAPI', 'https://github.com/project-tracker-templates/fastapi-microservice-template.git'),
    ('Data Science Project', 'library', 'Data analysis and machine learning project template', 'https://github.com/project-tracker-templates/data-science-template.git')
ON CONFLICT (name) DO NOTHING;

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL,
    template_id INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'initialized',
    repository_url TEXT,
    default_branch TEXT DEFAULT 'main',
    task_prefix TEXT DEFAULT 'TASK',
    pr_prefix TEXT DEFAULT 'PR',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES project_templates(id)
);

-- Git Repositories Table
CREATE TABLE IF NOT EXISTS git_repositories (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    clone_url TEXT NOT NULL,
    default_branch TEXT DEFAULT 'main',
    last_fetch TIMESTAMP,
    webhook_secret TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Git Branches Table
CREATE TABLE IF NOT EXISTS git_branches (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    last_commit_hash TEXT,
    last_commit_message TEXT,
    last_commit_date TIMESTAMP,
    is_protected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES git_repositories(id) ON DELETE CASCADE
);

-- Tasks Table
CREATE TABLE IF NOT EXISTS tasks (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    task_key TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'open',
    priority TEXT DEFAULT 'medium',
    assignee TEXT,
    created_by TEXT,
    branch_name TEXT,
    related_pr TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE(project_id, task_key)
);

-- Task Dependencies
CREATE TABLE IF NOT EXISTS task_dependencies (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    depends_on_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- Code Reviews Table
CREATE TABLE IF NOT EXISTS code_reviews (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    task_id INTEGER,
    pr_number TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'open',
    author TEXT NOT NULL,
    branch_name TEXT NOT NULL,
    target_branch TEXT DEFAULT 'main',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL
);

-- Create necessary indexes
CREATE INDEX IF NOT EXISTS idx_improvements_date ON improvements(date);
CREATE INDEX IF NOT EXISTS idx_improvements_category ON improvements(category_id);
CREATE INDEX IF NOT EXISTS idx_git_branches_repo ON git_branches(repository_id);
CREATE INDEX IF NOT EXISTS idx_tasks_project ON tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_code_reviews_project ON code_reviews(project_id);
CREATE INDEX IF NOT EXISTS idx_git_operations_repo ON git_operations_log(repository_id);
CREATE INDEX IF NOT EXISTS idx_subtasks_improvement ON subtasks(improvement_id);
CREATE INDEX IF NOT EXISTS idx_project_activity_project ON project_activity(project_id);
