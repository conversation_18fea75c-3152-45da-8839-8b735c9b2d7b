import React from 'react';

interface Project {
  id: string;
  name: string;
  description: string;
  lastActivity: string;
}

interface ProjectListProps {
  projects: Project[];
  onSelectProject: (id: string) => void;
}

const ProjectList: React.FC<ProjectListProps> = ({ projects, onSelectProject }) => {
  return (
    <div className="project-list">
      <h2>Your Projects</h2>
      <div className="project-grid">
        {projects.length === 0 ? (
          <p>No projects found. Create a new project to get started.</p>
        ) : (
          projects.map((project) => (
            <div 
              key={project.id} 
              className="project-card" 
              onClick={() => onSelectProject(project.id)}
            >
              <h3>{project.name}</h3>
              <p>{project.description}</p>
              <div className="project-meta">
                <span>Last activity: {project.lastActivity}</span>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ProjectList;
