#!/bin/bash

# SSL Certificate Sync Script for Git Dashboard
# This script can be run as root directly (via cron) or via the cert_sync_helper
# Uses rsync to transfer certificates from main server to local backup directory

# Configuration
MAIN_SERVER="***********"
GIT_DASHBOARD="***********"
MAIN_SERVER_USER="btaylor-admin"
CERT_DIR="/etc/letsencrypt"
DOMAIN="chcit.org"
LOG_DIR="/opt/git-dashboard/logs"
SYNC_LOG="$LOG_DIR/cert-sync.log"
# Using ed25519 SSH key
SSH_KEY="/home/<USER>/.ssh/id_ed25519"
# Backup directory for certificates
BACKUP_DIR="/home/<USER>/letsencrypt_backup"
# Group for certificate access
CERT_GROUP="cert-access"

# Function to log messages
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1"
    echo "[$timestamp] $1" >> "$SYNC_LOG" 2>/dev/null || true
}

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR" 2>/dev/null || true
touch "$SYNC_LOG" 2>/dev/null || true

# Make log directory accessible
chmod 755 "$LOG_DIR" 2>/dev/null || true
chmod 666 "$SYNC_LOG" 2>/dev/null || true

log "Starting certificate sync process"

# Add diagnostic logging
log "Running as user: $(id -un) (UID=$(id -u))"
log "User home: $HOME"
log "EUID (effective UID): $EUID"
log "SSH_KEY value: $SSH_KEY"
log "SSH_KEY exists: $([ -f "$SSH_KEY" ] && echo 'Yes' || echo 'No')"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    log "Warning: This script is not running as root, some operations may fail"
fi

# Create certificate directories if they don't exist
log "Creating certificate directories"
if [ "$EUID" -eq 0 ]; then
    mkdir -p "$CERT_DIR/archive/$DOMAIN" "$CERT_DIR/live/$DOMAIN" 2>/dev/null || true
    # Make sure permissions are set correctly
    chmod 755 "$CERT_DIR/archive/$DOMAIN" "$CERT_DIR/live/$DOMAIN" 2>/dev/null || true
else
    sudo mkdir -p "$CERT_DIR/archive/$DOMAIN" "$CERT_DIR/live/$DOMAIN" 2>/dev/null || true
    # Make sure permissions are set correctly
    sudo chmod 755 "$CERT_DIR/archive/$DOMAIN" "$CERT_DIR/live/$DOMAIN" 2>/dev/null || true
fi

# Check if rsync is available
if ! command -v rsync >/dev/null 2>&1; then
    log "Error: rsync command not found"
    exit 1
fi

# Create backup directory if it doesn't exist
if ! [ -d "$BACKUP_DIR" ]; then
    log "Creating backup directory $BACKUP_DIR"
    if [ "$(id -un)" = "btaylor-admin" ]; then
        mkdir -p "$BACKUP_DIR" 2>/dev/null || true
    else
        sudo -u btaylor-admin mkdir -p "$BACKUP_DIR" 2>/dev/null || true
    fi
    
    if ! [ -d "$BACKUP_DIR" ]; then
        log "Error: Failed to create backup directory"
        exit 1
    fi
fi

# Ensure backup directory has correct permissions
if [ "$(id -un)" = "btaylor-admin" ]; then
    mkdir -p "$BACKUP_DIR/archive/$DOMAIN" "$BACKUP_DIR/live/$DOMAIN" 2>/dev/null || true
else
    sudo -u btaylor-admin mkdir -p "$BACKUP_DIR/archive/$DOMAIN" "$BACKUP_DIR/live/$DOMAIN" 2>/dev/null || true
fi

# Step 1: Test connection to main server
log "Testing connection to $MAIN_SERVER"

# Use appropriate command based on current user
if [ "$(id -un)" = "btaylor-admin" ]; then
    SSH_TEST=$(ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "${MAIN_SERVER_USER}@${MAIN_SERVER}" "echo Connection successful" 2>&1)
else
    SSH_TEST=$(sudo -u btaylor-admin ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "${MAIN_SERVER_USER}@${MAIN_SERVER}" "echo Connection successful" 2>&1)
fi

# Check if connection was successful
if ! echo "$SSH_TEST" | grep -q "Connection successful"; then
    log "Error: Failed to connect to $MAIN_SERVER. Output: $SSH_TEST"
    exit 1
fi

log "SSH connection successful"

# Step 2: Sync certificates using rsync
log "Syncing certificates from $MAIN_SERVER using rsync"

# Sync archive directory
log "Syncing archive directory"
if [ "$(id -un)" = "btaylor-admin" ]; then
    rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/archive/" "$BACKUP_DIR/archive/"
else
    sudo -u btaylor-admin rsync -avz --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/archive/" "$BACKUP_DIR/archive/"
fi

# Check if rsync was successful
if [ $? -ne 0 ]; then
    log "Error: Failed to sync archive directory"
    exit 1
fi

# Sync live directory - we need the files, not the symlinks
log "Syncing live directory"
if [ "$(id -un)" = "btaylor-admin" ]; then
    rsync -avzL --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/live/" "$BACKUP_DIR/live/"
else
    sudo -u btaylor-admin rsync -avzL --timeout=30 -e "ssh -i $SSH_KEY" "${MAIN_SERVER_USER}@${MAIN_SERVER}:/etc/letsencrypt/live/" "$BACKUP_DIR/live/"
fi

# Check if rsync was successful
if [ $? -ne 0 ]; then
    log "Error: Failed to sync live directory"
    exit 1
fi

log "Certificate sync from remote server completed successfully"

# Step 3: Copy archive certificates directly
log "Copying archive certificates to their proper locations"

if [ -d "$BACKUP_DIR/archive/$DOMAIN" ]; then
    if [ "$EUID" -eq 0 ]; then
        # If running as root, copy directly
        cp -a "$BACKUP_DIR/archive/$DOMAIN/"* "$CERT_DIR/archive/$DOMAIN/" || {
            log "Warning: Failed to copy archive certificates"
        }
    else
        # If not root, use sudo
        sudo cp -a "$BACKUP_DIR/archive/$DOMAIN/"* "$CERT_DIR/archive/$DOMAIN/" || {
            log "Warning: Failed to copy archive certificates"
        }
    fi
else
    log "Error: Archive certificates directory not found in backup"
    exit 1
fi

# Step 4: Create proper symlinks in live directory
log "Creating symbolic links in live directory"

# Get the latest version number from archive
LATEST_CERT=$(find "$BACKUP_DIR/archive/$DOMAIN" -name "cert*.pem" | sort -V | tail -1)
if [ -z "$LATEST_CERT" ]; then
    log "Error: No certificate files found in backup archive"
    exit 1
fi

VERSION=$(echo "$LATEST_CERT" | grep -o '[0-9]*\.pem' | cut -d. -f1)
if [ -z "$VERSION" ]; then
    VERSION="1"  # Default to 1 if no version found
    log "No version number found, using default: 1"
else
    log "Found certificate version: $VERSION"
fi

# Create symlinks for each certificate file
for FILE in cert chain fullchain privkey; do
    log "Creating symlink for $FILE.pem (version $VERSION)"
    TARGET="../../archive/$DOMAIN/$FILE$VERSION.pem"
    
    if [ "$EUID" -eq 0 ]; then
        # If running as root, create symlink directly
        ln -sf "$TARGET" "$CERT_DIR/live/$DOMAIN/$FILE.pem" || {
            log "Warning: Failed to create symlink for $FILE.pem"
        }
    else
        # If not root, use sudo
        sudo ln -sf "$TARGET" "$CERT_DIR/live/$DOMAIN/$FILE.pem" || {
            log "Warning: Failed to create symlink for $FILE.pem"
        }
    fi
done

# Step 5: Set correct permissions
log "Setting correct permissions"
if [ "$EUID" -eq 0 ]; then
    # If running as root, set permissions directly
    chown -R root:root "$CERT_DIR" 2>/dev/null || log "Warning: Failed to set ownership on cert directory"
    chmod -R 644 "$CERT_DIR" 2>/dev/null || log "Warning: Failed to set permissions on cert directory"
    find "$CERT_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || log "Warning: Failed to set directory permissions"
    find "$CERT_DIR/archive" -name "privkey*.pem" -exec chmod 640 {} \; 2>/dev/null || true
    find "$CERT_DIR/live" -name "privkey*.pem" -exec chmod 640 {} \; 2>/dev/null || true
else
    # If not root, use sudo
    sudo chown -R root:root "$CERT_DIR" 2>/dev/null || log "Warning: Failed to set ownership on cert directory"
    sudo chmod -R 644 "$CERT_DIR" 2>/dev/null || log "Warning: Failed to set permissions on cert directory"
    sudo find "$CERT_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || log "Warning: Failed to set directory permissions"
    sudo find "$CERT_DIR/archive" -name "privkey*.pem" -exec chmod 640 {} \; 2>/dev/null || true
    sudo find "$CERT_DIR/live" -name "privkey*.pem" -exec chmod 640 {} \; 2>/dev/null || true
fi

# Create cert-access group if it doesn't exist
if [ "$EUID" -eq 0 ]; then
    getent group $CERT_GROUP > /dev/null || groupadd $CERT_GROUP
    log "Using certificate access group: $CERT_GROUP"
else
    sudo getent group $CERT_GROUP > /dev/null || sudo groupadd $CERT_GROUP
    log "Using certificate access group: $CERT_GROUP"
fi

# Set group on private key files for both www-data and database-service access
if [ "$EUID" -eq 0 ]; then
    # If running as root, set group directly
    chgrp $CERT_GROUP "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"
    find "$CERT_DIR/archive" -name "privkey*.pem" -exec chgrp $CERT_GROUP {} \; 2>/dev/null || log "Warning: Failed to change group on archive privkey files"
    chmod 640 "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to set permissions on privkey.pem"
else
    # If not root, use sudo
    sudo chgrp $CERT_GROUP "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"
    sudo find "$CERT_DIR/archive" -name "privkey*.pem" -exec chgrp $CERT_GROUP {} \; 2>/dev/null || log "Warning: Failed to change group on archive privkey files"
    sudo chmod 640 "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to set permissions on privkey.pem"
fi

# Step 6: Verify certificate files exist
log "Verifying certificate files"
MISSING_FILES=0
for file in fullchain.pem privkey.pem chain.pem cert.pem; do
    if [ ! -f "$CERT_DIR/live/$DOMAIN/$file" ]; then
        log "Error: Missing certificate file $file"
        MISSING_FILES=1
    else
        log "Certificate file $file exists"
    fi
done

if [ $MISSING_FILES -eq 1 ]; then
    log "Certificate sync failed: Missing certificate files"
    exit 1
fi

# Step 7: Reload Nginx to apply new certificates
log "Reloading Nginx"
if [ "$EUID" -eq 0 ]; then
    # If running as root, reload directly
    systemctl reload nginx 2>/dev/null || {
        # Try alternate methods if systemctl fails
        if command -v nginx >/dev/null 2>&1; then
            nginx -s reload 2>/dev/null || log "Warning: Failed to reload Nginx via nginx -s reload"
        elif [ -x /etc/init.d/nginx ]; then
            /etc/init.d/nginx reload 2>/dev/null || log "Warning: Failed to reload Nginx via init script"
        else
            log "Warning: Failed to reload Nginx - please reload manually"
        fi
    }
else
    # If not root, use sudo
    sudo systemctl reload nginx 2>/dev/null || {
        # Try alternate methods if systemctl fails
        if command -v nginx >/dev/null 2>&1; then
            sudo nginx -s reload 2>/dev/null || log "Warning: Failed to reload Nginx via nginx -s reload"
        elif [ -x /etc/init.d/nginx ]; then
            sudo /etc/init.d/nginx reload 2>/dev/null || log "Warning: Failed to reload Nginx via init script"
        else
            log "Warning: Failed to reload Nginx - please reload manually"
        fi
    }
fi

# Step 8: Test HTTPS connection
log "Testing HTTPS connection"
if curl -sf https://$GIT_DASHBOARD/ > /dev/null 2>&1; then
    log "Certificate sync completed successfully"
else
    log "Warning: HTTPS test failed after sync, but certificates may still be installed correctly"
fi

log "Certificate sync process completed"
exit 0
