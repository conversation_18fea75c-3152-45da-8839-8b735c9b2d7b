#!/bin/bash
# Script to set up certificate access for the Database Service

# Log function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "Starting setup of certificate access for Database Service"

# Create the cert-access group if it doesn't exist
if ! getent group cert-access > /dev/null; then
    log "Creating cert-access group"
    groupadd cert-access
else
    log "cert-access group already exists"
fi

# Create the database-service user if it doesn't exist
if ! id database-service &>/dev/null; then
    log "Creating database-service user"
    useradd -r -s /bin/false database-service
else
    log "database-service user already exists"
fi

# Add database-service to the cert-access group
log "Adding database-service user to cert-access group"
usermod -a -G cert-access database-service

# Change the group ownership of the private key
log "Changing group ownership of private key files"
chgrp cert-access /etc/letsencrypt/live/chcit.org/privkey.pem
find /etc/letsencrypt/archive/chcit.org -name "privkey*.pem" -exec chgrp cert-access {} \;

# Ensure permissions are correct
log "Setting correct permissions on private key files"
chmod 640 /etc/letsencrypt/live/chcit.org/privkey.pem
find /etc/letsencrypt/archive/chcit.org -name "privkey*.pem" -exec chmod 640 {} \;

# Modify the certificate sync script to maintain group ownership
SYNC_SCRIPT="/opt/git-dashboard/sync-certificates.sh"
if [ -f "$SYNC_SCRIPT" ]; then
    log "Modifying certificate sync script"
    
    # Check if the script already has our modifications
    if ! grep -q "cert-access" "$SYNC_SCRIPT"; then
        # Add our modifications before the "Certificate sync process completed" line
        sed -i '/log "Certificate sync process completed"/i \
# Set correct group for database service access\
if [ "$EUID" -eq 0 ]; then\
    # If running as root, set group directly\
    chgrp cert-access "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"\
    find "$CERT_DIR/archive" -name "privkey*.pem" -exec chgrp cert-access {} \\; 2>/dev/null || log "Warning: Failed to change group on archive privkey files"\
else\
    # If not root, use sudo\
    sudo chgrp cert-access "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"\
    sudo find "$CERT_DIR/archive" -name "privkey*.pem" -exec chgrp cert-access {} \\; 2>/dev/null || log "Warning: Failed to change group on archive privkey files"\
fi\
log "Set certificate permissions for database service access"' "$SYNC_SCRIPT"
        
        log "Certificate sync script modified successfully"
    else
        log "Certificate sync script already contains our modifications"
    fi
else
    log "Warning: Certificate sync script not found at $SYNC_SCRIPT"
fi

# Create systemd service file
log "Creating systemd service file"
cat > /etc/systemd/system/database-service.service << "EOF"
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=/opt/git-dashboard/bin/database-service --config /opt/git-dashboard/config/database-service.json
Restart=on-failure
WorkingDirectory=/opt/git-dashboard
Environment="LD_LIBRARY_PATH=/opt/git-dashboard/lib"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF

# Create config directory if it doesn't exist
mkdir -p /opt/git-dashboard/config

# Create database service configuration file
log "Creating database service configuration file"
cat > /opt/git-dashboard/config/database-service.json << "EOF"
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "gitdashboard",
    "user": "database_service_user",
    "password": "change_this_password",
    "ssl": {
      "enabled": true,
      "cert_path": "/etc/letsencrypt/live/chcit.org/fullchain.pem",
      "key_path": "/etc/letsencrypt/live/chcit.org/privkey.pem",
      "ca_path": "/etc/letsencrypt/live/chcit.org/chain.pem"
    },
    "pool": {
      "min_connections": 5,
      "max_connections": 20,
      "connection_timeout": 30
    }
  },
  "api": {
    "host": "127.0.0.1",
    "port": 8082,
    "threads": 4,
    "timeout": 30
  },
  "logging": {
    "level": "info",
    "file": "/var/log/database-service.log",
    "rotation": {
      "enabled": true,
      "max_size": 10,
      "max_files": 5
    }
  }
}
EOF

# Create log directory and file with appropriate permissions
log "Setting up log file"
mkdir -p /var/log
touch /var/log/database-service.log
chown database-service:database-service /var/log/database-service.log
chmod 644 /var/log/database-service.log

# Reload systemd to recognize the new service
log "Reloading systemd"
systemctl daemon-reload

log "Setup completed successfully"
log "IMPORTANT: Please update the database password in /opt/git-dashboard/config/database-service.json"
log "To start the service, run: systemctl enable --now database-service"
