# Configuration.psm1
# Central configuration management for all deployment modules

# Script-level variables
$script:ConfigDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
$script:Config = $null
$script:Environment = "development"

# Only initialize once, not every time the module is imported
$script:Initialized = $false

# Helper function to write messages only when -Verbose is used
function Write-ConfigMessage {
    [CmdletBinding()]
    param([string]$Message, [string]$Level = "Info")
    
    if ($VerbosePreference -eq 'Continue') {
        $color = switch($Level) {
            "Info" { "Cyan" }
            "Warning" { "Yellow" }
            "Error" { "Red" }
            "Success" { "Green" }
            default { "White" }
        }
        Write-Host $Message -ForegroundColor $color
    }
}

<#
.SYNOPSIS
    Gets the current configuration.

.DESCRIPTION
    Returns the currently loaded configuration. If no configuration is loaded,
    it attempts to load the default configuration for the current environment.

.EXAMPLE
    $config = Get-Configuration
    Write-Host "Project name: $($config.project.name)"
#>
function Get-Configuration {
    [CmdletBinding()]
    param()
    
    # Initialize if needed
    Initialize-ConfigurationModule
    
    # If configuration isn't loaded yet, try to load it
    if ($null -eq $script:Config) {
        # Determine environment
        $env = $script:Environment
        if ($null -ne $env:DeploymentEnvironment) {
            $env = $env:DeploymentEnvironment
        }
        
        # Try to load from file
        $configPath = Join-Path -Path $script:ConfigDir -ChildPath "database-service-$($env.ToLower()).json"
        if (Test-Path $configPath) {
            Import-Configuration -ConfigFile $configPath | Out-Null
        }
    }
    
    return $script:Config
}

<#
.SYNOPSIS
    Sets the current environment.

.DESCRIPTION
    Sets the current environment and optionally reloads the configuration for that environment.

.PARAMETER Environment
    The environment to set (e.g., "development", "staging", "production").

.PARAMETER ReloadConfig
    If specified, reloads the configuration for the new environment.

.EXAMPLE
    Set-ConfigurationEnvironment -Environment "staging" -ReloadConfig
#>
function Set-ConfigurationEnvironment {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$Environment,
        
        [Parameter(Mandatory=$false)]
        [switch]$ReloadConfig
    )
    
    $script:Environment = $Environment
    
    # Set it in process scope as well
    $env:DeploymentEnvironment = $Environment
    
    if ($ReloadConfig) {
        $configPath = Join-Path -Path $script:ConfigDir -ChildPath "database-service-$($Environment.ToLower()).json"
        if (Test-Path $configPath) {
            Import-Configuration -ConfigFile $configPath | Out-Null
        }
    }
}

<#
.SYNOPSIS
    Imports configuration from a file.

.DESCRIPTION
    Loads configuration from a JSON file and makes it available to all modules.

.PARAMETER ConfigFile
    The path to the configuration file to load.

.EXAMPLE
    Import-Configuration -ConfigFile "D:\config\database-service-development.json"
#>
function Import-Configuration {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$ConfigFile
    )

    try {
        Write-ConfigMessage "Loading configuration from: $ConfigFile" -Level "Info"
        if (Test-Path -Path $ConfigFile) {
            $config = Get-Content -Path $ConfigFile -Raw | ConvertFrom-Json
            
            # Set in script scope
            $script:Config = $config
            
            # Extract environment from filename
            if ($ConfigFile -match '(?:database-service-|config-)([^.]+)(?:\.json)') {
                $env = $matches[1]
                $script:Environment = $env
                $env:DeploymentEnvironment = $env
                Write-ConfigMessage "Environment set to: $env" -Level "Success"
            }
            
            Write-ConfigMessage "Configuration loaded successfully" -Level "Success"
            return $true
        } else {
            Write-ConfigMessage "Configuration file not found: $ConfigFile" -Level "Warning"
            return $false
        }
    } catch {
        Write-ConfigMessage "Error loading configuration: $_" -Level "Error"
        return $false
    }
}

<#
.SYNOPSIS
    Saves the current configuration to a file.

.DESCRIPTION
    Saves the current configuration to a file for the current environment.

.PARAMETER NoVersionIncrement
    If specified, does not increment the version number.

.EXAMPLE
    Save-Configuration
#>
function Save-Configuration {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$false)]
        [switch]$NoVersionIncrement
    )
    
    if ($null -eq $script:Config) {
        Write-ConfigMessage "No configuration loaded to save." -Level "Warning"
        return $false
    }
    
    try {
        # Update version information if exists and increment requested
        if (-not $NoVersionIncrement -and $null -ne $script:Config.version) {
            $script:Config.version.updated = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            $script:Config.version.number += 1
        }
        
        # Save to file
        $configPath = Join-Path -Path $script:ConfigDir -ChildPath "database-service-$($script:Environment.ToLower()).json"
        $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
        Write-ConfigMessage "Configuration saved to: $configPath" -Level "Success"
        return $true
    }
    catch {
        Write-ConfigMessage "Error saving configuration: $_" -Level "Error"
        return $false
    }
}

<#
.SYNOPSIS
    Initializes the configuration module.

.DESCRIPTION
    Internal function that ensures the module is only initialized once.
#>
function Initialize-ConfigurationModule {
    [CmdletBinding()]
    param()
    
    # Only initialize once
    if (-not $script:Initialized) {
        Write-ConfigMessage "Initializing Configuration Module..." -Level "Info"
        
        # Look for default configuration file
        $defaultEnv = $script:Environment
        if ($null -ne $env:DeploymentEnvironment) {
            $defaultEnv = $env:DeploymentEnvironment
        }
        $configPath = Join-Path -Path $script:ConfigDir -ChildPath "database-service-$($defaultEnv.ToLower()).json"
        if (Test-Path $configPath) {
            Import-Configuration -ConfigFile $configPath | Out-Null
        }
        
        $script:Initialized = $true
    }
}

# Export module members - only export functions, not variables
Export-ModuleMember -Function Get-Configuration, Set-ConfigurationEnvironment, Import-Configuration, Save-Configuration
