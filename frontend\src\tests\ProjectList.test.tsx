import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ProjectList } from '../components/front-ProjectList';
import { api } from '../services/front-api';

// Mock the API service
jest.mock('../services/front-api');

describe('ProjectList Component', () => {
    const mockProjects = [
        {
            id: 1,
            name: 'Test Project',
            description: 'Test Description',
            improvements_count: 5,
            categories_count: 2
        }
    ];

    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    it('renders project list correctly', async () => {
        // Mock the API call
        (api.getProjects as jest.Mock).mockResolvedValue(mockProjects);

        render(<ProjectList />);

        // Wait for projects to load
        await waitFor(() => {
            expect(screen.getByText('Test Project')).toBeInTheDocument();
        });

        // Check if project details are displayed
        expect(screen.getByText('Test Description')).toBeInTheDocument();
        expect(screen.getByText('5 improvements')).toBeInTheDocument();
        expect(screen.getByText('2 categories')).toBeInTheDocument();
    });

    it('handles project creation', async () => {
        const newProject = {
            id: 2,
            name: 'New Project',
            description: 'New Description',
            improvements_count: 0,
            categories_count: 0
        };

        // Mock API calls
        (api.getProjects as jest.Mock).mockResolvedValue(mockProjects);
        (api.createProject as jest.Mock).mockResolvedValue(newProject);

        render(<ProjectList />);

        // Click create project button
        fireEvent.click(screen.getByText('Create Project'));

        // Fill in form
        fireEvent.change(screen.getByLabelText('Project Name'), {
            target: { value: 'New Project' }
        });
        fireEvent.change(screen.getByLabelText('Description'), {
            target: { value: 'New Description' }
        });

        // Submit form
        fireEvent.click(screen.getByText('Save'));

        // Verify API was called
        await waitFor(() => {
            expect(api.createProject).toHaveBeenCalledWith({
                name: 'New Project',
                description: 'New Description'
            });
        });
    });

    it('handles errors gracefully', async () => {
        // Mock API error
        (api.getProjects as jest.Mock).mockRejectedValue(new Error('Failed to load'));

        render(<ProjectList />);

        // Check if error message is displayed
        await waitFor(() => {
            expect(screen.getByText('Error: Failed to load')).toBeInTheDocument();
        });
    });

    it('filters projects correctly', async () => {
        const manyProjects = [
            ...mockProjects,
            {
                id: 2,
                name: 'Another Project',
                description: 'Another Description',
                improvements_count: 3,
                categories_count: 1
            }
        ];

        // Mock API call
        (api.getProjects as jest.Mock).mockResolvedValue(manyProjects);

        render(<ProjectList />);

        // Wait for projects to load
        await waitFor(() => {
            expect(screen.getByText('Test Project')).toBeInTheDocument();
        });

        // Type in search box
        fireEvent.change(screen.getByPlaceholderText('Search projects...'), {
            target: { value: 'Another' }
        });

        // Check filtered results
        expect(screen.queryByText('Test Project')).not.toBeInTheDocument();
        expect(screen.getByText('Another Project')).toBeInTheDocument();
    });
});
