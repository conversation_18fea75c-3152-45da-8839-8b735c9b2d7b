export module database.service;

// Import other modules
import database.core;
import database.schema;
import database.security;
import database.api;

// Standard library imports
import <string>;
import <memory>;
import <vector>;
import <unordered_map>;
import <thread>;
import <atomic>;
import <chrono>;
import <iostream>;

// Database service module
export namespace dbservice {

    // Database service configuration
    export struct DatabaseServiceConfig {
        // Database configuration
        std::string connectionString;
        size_t connectionPoolSize = 10;
        bool useSSL = true;

        // API configuration
        unsigned short apiPort = 8080;
        std::string apiHost = "127.0.0.1";
        std::string apiBasePath = "/api";

        // Security configuration
        bool enableAuthentication = true;
        std::string jwtSecret = "default-secret-change-me";

        // Logging configuration
        bool enableLogging = true;
        std::string logLevel = "info";
        std::string logFile = "database-service.log";
    };

    // Database service
    export class DatabaseService : public core::DatabaseInterface {
    public:
        /**
         * @brief Constructor
         * @param config Service configuration
         */
        DatabaseService(const DatabaseServiceConfig& config);

        /**
         * @brief Destructor
         */
        ~DatabaseService();

        /**
         * @brief Start the service (synchronous)
         * @return Result of the operation
         */
        core::Result<void> start();

        /**
         * @brief Start the service (asynchronous)
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> startAsync();

        /**
         * @brief Stop the service (synchronous)
         * @return Result of the operation
         */
        core::Result<void> stop();

        /**
         * @brief Stop the service (asynchronous)
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> stopAsync();

        /**
         * @brief Check if the service is running
         * @return True if the service is running
         */
        bool isRunning() const;

        /**
         * @brief Get the API server
         * @return API server
         */
        std::shared_ptr<api::ApiServer> getApiServer();

        /**
         * @brief Get the schema manager
         * @return Schema manager
         */
        std::shared_ptr<schema::SchemaManager> getSchemaManager();

        /**
         * @brief Get the security manager
         * @return Security manager
         */
        std::shared_ptr<security::SecurityManager> getSecurityManager();

        // DatabaseInterface implementation

        /**
         * @brief Execute a query (synchronous)
         * @param query SQL query
         * @return Query result
         */
        core::Result<core::QueryResult> executeQuery(const std::string& query) override;

        /**
         * @brief Execute a command (synchronous)
         * @param command SQL command
         * @return Result of the operation
         */
        core::Result<void> executeCommand(const std::string& command) override;

        /**
         * @brief Execute a transaction (synchronous)
         * @param commands SQL commands
         * @return Result of the operation
         */
        core::Result<void> executeTransaction(const std::vector<std::string>& commands) override;

        /**
         * @brief Execute a query (asynchronous)
         * @param query SQL query
         * @return Task with query result
         */
        core::Task<core::Result<core::QueryResult>> executeQueryAsync(const std::string& query) override;

        /**
         * @brief Execute a command (asynchronous)
         * @param command SQL command
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> executeCommandAsync(const std::string& command) override;

        /**
         * @brief Execute a transaction (asynchronous)
         * @param commands SQL commands
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> executeTransactionAsync(const std::vector<std::string>& commands) override;

    private:
        /**
         * @brief Initialize the database
         * @return Result of the operation
         */
        core::Result<void> initializeDatabase();

        /**
         * @brief Initialize the database (asynchronous)
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> initializeDatabaseAsync();

        /**
         * @brief Log a message
         * @param level Log level
         * @param message Message to log
         */
        void log(const std::string& level, const std::string& message);

        DatabaseServiceConfig config_;
        std::shared_ptr<core::ConnectionManager> connectionManager_;
        std::shared_ptr<schema::SchemaManager> schemaManager_;
        std::shared_ptr<security::SecurityManager> securityManager_;
        std::shared_ptr<api::ApiServer> apiServer_;

        std::atomic<bool> running_{false};
    };

} // namespace dbservice
