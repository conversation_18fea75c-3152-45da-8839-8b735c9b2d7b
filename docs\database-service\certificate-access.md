# Database Service Certificate Access

This document explains how the Database Service accesses SSL certificates for secure connections.

## Overview

The Database Service needs access to SSL certificates to establish secure connections with clients. These certificates are stored in `/etc/letsencrypt/live/chcit.org/` and are also used by Nginx for the Git Dashboard.

## Security Model

The security model for certificate access is as follows:

1. **Nginx Access Pattern**:
   - Nginx master process runs as root and reads the certificates during startup
   - Worker processes run as www-data but inherit file descriptors from the master process
   - This allows worker processes to use certificates without direct file access

2. **Database Service Access Pattern**:
   - Database Service runs entirely as the database-service user
   - The database-service user is added to the cert-access group
   - The private key's group is set to cert-access with read permissions (640)
   - This allows the Database Service to read the certificates directly

## Implementation

> **Note:** The project has been reorganized. The source code is now in the `database-service-traditional` directory, while supporting files and scripts (including the certificate access setup script) are in the `database-service-scripts` directory.

The certificate access setup is integrated into the database deployment script (deploy-database-service.bat) as part of the requirements installation (option 2) and is included in the full deployment process (option 9). The comprehensive server readiness check (option 3) verifies that certificate access is properly configured.

The implementation involves the following steps:

1. Create a cert-access group
2. Create a database-service user and add it to the cert-access group
3. Change the group ownership of the private key to cert-access
4. Modify the certificate sync script to maintain these permissions
5. Create a systemd service file for the Database Service
6. Configure the Database Service to use the certificates

## Setup Script

A setup script (`setup-database-cert-access.sh`) is provided to automate these changes. The script:

- Creates the cert-access group
- Creates the database-service user
- Adds the database-service user to the cert-access group
- Changes the group ownership of the private key
- Modifies the certificate sync script
- Creates a systemd service file
- Creates a basic configuration file

## Manual Setup

If you prefer to make these changes manually, follow these steps:

1. **Create the cert-access group**:
   ```bash
   sudo groupadd cert-access
   ```

2. **Create the database-service user and add to the group**:
   ```bash
   sudo useradd -r -s /bin/false database-service
   sudo usermod -a -G cert-access database-service
   ```

3. **Change the group ownership of the private key**:
   ```bash
   sudo chgrp cert-access /etc/letsencrypt/live/chcit.org/privkey.pem
   sudo find /etc/letsencrypt/archive/chcit.org -name "privkey*.pem" -exec chgrp cert-access {} \;
   ```

4. **Ensure permissions are correct**:
   ```bash
   sudo chmod 640 /etc/letsencrypt/live/chcit.org/privkey.pem
   sudo find /etc/letsencrypt/archive/chcit.org -name "privkey*.pem" -exec chmod 640 {} \;
   ```

5. **Modify the certificate sync script**:
   Add the following code to `/opt/git-dashboard/sync-certificates.sh` before the "Certificate sync process completed" line:
   ```bash
   # Set correct group for database service access
   if [ "$EUID" -eq 0 ]; then
       # If running as root, set group directly
       chgrp cert-access "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"
       find "$CERT_DIR/archive" -name "privkey*.pem" -exec chgrp cert-access {} \; 2>/dev/null || log "Warning: Failed to change group on archive privkey files"
   else
       # If not root, use sudo
       sudo chgrp cert-access "$CERT_DIR/live/$DOMAIN/privkey.pem" 2>/dev/null || log "Warning: Failed to change group on privkey.pem"
       sudo find "$CERT_DIR/archive" -name "privkey*.pem" -exec chgrp cert-access {} \; 2>/dev/null || log "Warning: Failed to change group on archive privkey files"
   fi
   log "Set certificate permissions for database service access"
   ```

6. **Create a systemd service file**:
   ```bash
   sudo nano /etc/systemd/system/database-service.service
   ```

   Add the following content:
   ```ini
   [Unit]
   Description=Database Service
   After=network.target postgresql.service
   Wants=postgresql.service

   [Service]
   Type=simple
   User=database-service
   Group=database-service
   ExecStart=/opt/git-dashboard/bin/database-service --config /opt/git-dashboard/config/database-service.json
   Restart=on-failure
   WorkingDirectory=/opt/git-dashboard
   Environment="LD_LIBRARY_PATH=/opt/git-dashboard/lib"

   # Security enhancements
   PrivateTmp=true
   ProtectSystem=full
   ProtectHome=true
   NoNewPrivileges=true

   [Install]
   WantedBy=multi-user.target
   ```

7. **Create a configuration file**:
   ```bash
   sudo mkdir -p /opt/git-dashboard/config
   sudo nano /opt/git-dashboard/config/database-service.json
   ```

   Add the following content (adjust as needed):
   ```json
   {
     "database": {
       "host": "localhost",
       "port": 5432,
       "name": "gitdashboard",
       "user": "database_service_user",
       "password": "change_this_password",
       "ssl": {
         "enabled": true,
         "cert_path": "/etc/letsencrypt/live/chcit.org/fullchain.pem",
         "key_path": "/etc/letsencrypt/live/chcit.org/privkey.pem",
         "ca_path": "/etc/letsencrypt/live/chcit.org/chain.pem"
       },
       "pool": {
         "min_connections": 5,
         "max_connections": 20,
         "connection_timeout": 30
       }
     },
     "api": {
       "host": "127.0.0.1",
       "port": 8082,
       "threads": 4,
       "timeout": 30
     },
     "logging": {
       "level": "info",
       "file": "/var/log/database-service.log",
       "rotation": {
         "enabled": true,
         "max_size": 10,
         "max_files": 5
       }
     }
   }
   ```

8. **Set up logging**:
   ```bash
   sudo touch /var/log/database-service.log
   sudo chown database-service:database-service /var/log/database-service.log
   sudo chmod 644 /var/log/database-service.log
   ```

9. **Reload systemd and start the service**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable --now database-service
   ```

## Verification

To verify that the setup is correct:

1. **Check group membership**:
   ```bash
   groups database-service
   ```
   Should include `cert-access`

2. **Check certificate permissions**:
   ```bash
   ls -la /etc/letsencrypt/live/chcit.org/privkey.pem
   ls -la /etc/letsencrypt/archive/chcit.org/privkey*.pem
   ```
   Should show group as `cert-access` with permissions `640`

3. **Test certificate access**:
   ```bash
   sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null && echo "Access OK" || echo "Access Failed"
   ```
   Should output "Access OK"

4. **Check service status**:
   ```bash
   sudo systemctl status database-service
   ```
   Should show the service as active

## Troubleshooting

If you encounter issues:

1. **Certificate Access Issues**:
   - Verify group ownership: `ls -la /etc/letsencrypt/live/chcit.org/privkey.pem`
   - Verify group membership: `groups database-service`
   - Check for SELinux/AppArmor issues: `sudo aa-status` or `sudo sestatus`

2. **Service Start Issues**:
   - Check logs: `sudo journalctl -u database-service`
   - Verify binary exists: `ls -la /opt/git-dashboard/bin/database-service`
   - Check configuration: `cat /opt/git-dashboard/config/database-service.json`

3. **Database Connection Issues**:
   - Verify PostgreSQL is running: `sudo systemctl status postgresql`
   - Check database user exists: `sudo -u postgres psql -c "\du"`
   - Verify database exists: `sudo -u postgres psql -c "\l"`

## Maintenance

The certificate sync script has been modified to maintain the correct group ownership when certificates are renewed. No additional maintenance is required for certificate access.

If you need to add more services that require certificate access, simply add them to the `cert-access` group:

```bash
sudo usermod -a -G cert-access <service-user>
```


