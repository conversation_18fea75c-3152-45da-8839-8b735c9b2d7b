# Architecture and Diagrams

This directory contains architecture diagrams and screenshots for the Project Tracker ecosystem.

## Available Diagrams

1. [**Architecture Diagrams**](./architecture-diagram.md)
   - System architecture diagram
   - Component interaction diagram
   - Database architecture diagram
   - Multi-database architecture diagram
   - Deployment architecture diagram
   - Service architecture diagrams

## Screenshots

The `screenshots` directory contains screenshots of the Project Tracker UI:

- Dashboard overview
- Repository list and details
- Commit history
- Logs view
- Certificate management
- Settings
- Customization options

## Purpose

These diagrams and screenshots provide visual representations of the Project Tracker architecture and UI. They are intended to help developers and system administrators understand the system structure and user interface.

## How to Use These Diagrams

1. Start with the [Architecture Diagrams](./architecture-diagram.md) for an overview of the system architecture
2. Refer to the specific diagrams for detailed information about components and their interactions
3. Use the screenshots as a reference for the UI design and functionality

## Related Documentation

- [Project Tracker Documentation Index](../DOCUMENTATION-INDEX.md)
- [Implementation Guides](../implementation-guides/README.md)
- [Database Service Documentation](../database-service/overview.md)
- [Git Repository Service Documentation](../git-server-web/git-repo-service/README.md)
- [Logging Service Documentation](../git-server-web/git-repo-logging/README.md)

