# Git Repository Migration Script
# This script migrates repositories from Windows to the Ubuntu Git server

# Configuration - modify these variables
$UbuntuServerIP = "***********"  # Updated to your Ubuntu server IP
$GitUser = "git"
$AdminUser = "btaylor-admin"  # Admin username on the Ubuntu server
$LocalRepoBasePath = "D:\Codeium\CHCIT"  # Base directory containing repositories
$RepositoriesToMigrate = @(
    "project-tracker"  # Add more repository names as needed
)
$RemoveAfterMigration = $false  # Set to $true to remove local repos after migration

# Colors for output
function Write-ColorOutput {
    param (
        [System.ConsoleColor]$ForegroundColor
    )
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-GreenOutput { Write-ColorOutput Green $args }
function Write-YellowOutput { Write-ColorOutput Yellow $args }
function Write-BlueOutput { Write-ColorOutput Cyan $args }

Write-BlueOutput "=== Starting Git Repository Migration ==="

# Check if SSH is available
if (-not (Get-Command "ssh" -ErrorAction SilentlyContinue)) {
    Write-Host "Error: SSH client not found. Please install OpenSSH client." -ForegroundColor Red
    exit 1
}

# Function to check if the remote server is reachable
function Test-ServerConnection {
    Write-YellowOutput "Testing connection to $UbuntuServerIP..."
    $testResult = Test-Connection -ComputerName $UbuntuServerIP -Count 1 -Quiet
    
    if (-not $testResult) {
        Write-Host "Error: Cannot reach the Ubuntu server at $UbuntuServerIP" -ForegroundColor Red
        exit 1
    }
    
    Write-GreenOutput "Connection to server successful!"
}

# Function to ensure SSH key is set up
function Ensure-SSHKey {
    # Check if SSH key exists
    $sshKeyPath = "$env:USERPROFILE\.ssh\id_rsa"
    $sshPubKeyPath = "$sshKeyPath.pub"
    
    if (-not (Test-Path $sshPubKeyPath)) {
        Write-YellowOutput "\nNo SSH key found. Generating a new SSH key..."
        ssh-keygen -t rsa -b 4096 -f $sshKeyPath -N '""'
        
        if (-not (Test-Path $sshPubKeyPath)) {
            Write-Host "Error: Failed to generate SSH key." -ForegroundColor Red
            return $false
        }
        
        Write-GreenOutput "SSH key generated successfully!"
    }
    
    # Copy SSH key to the server
    Write-YellowOutput "\nCopying SSH key to the server..."
    
    # First try with the admin user
    $adminSshCommand = "{0}@{1}" -f $AdminUser, $UbuntuServerIP
    
    # Use scp to copy the key to the server
    $tempKeyFile = "$env:TEMP\temp_ssh_key.pub"
    Copy-Item $sshPubKeyPath $tempKeyFile
    
    Write-YellowOutput "Uploading SSH key to server..."
    $scpResult = $false
    
    try {
        # Try to copy using the admin user first
        $output = ssh $adminSshCommand "mkdir -p ~/temp_keys" 2>&1
        $output = scp $tempKeyFile "$adminSshCommand:~/temp_keys/$(whoami).pub" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            # Now use the admin user to add the key to git user
            $output = ssh $adminSshCommand "sudo -u git /home/<USER>/add-user-key.sh $(whoami) ~/temp_keys/$(whoami).pub && rm -f ~/temp_keys/$(whoami).pub" 2>&1
            $scpResult = ($LASTEXITCODE -eq 0)
        }
    }
    catch {
        Write-Host "Warning: Could not automatically add SSH key to server." -ForegroundColor Yellow
        Write-Host "You may need to manually add your SSH key to the server." -ForegroundColor Yellow
    }
    finally {
        Remove-Item $tempKeyFile -ErrorAction SilentlyContinue
    }
    
    if (-not $scpResult) {
        Write-YellowOutput "\nTo manually add your SSH key to the server, run this command on the Ubuntu server:"
        Write-YellowOutput "  sudo -u git /home/<USER>/add-user-key.sh $(whoami) /path/to/your/id_rsa.pub"
        
        Write-YellowOutput "\nOr copy this public key and add it to /home/<USER>/.ssh/authorized_keys on the server:"
        Get-Content $sshPubKeyPath | Write-YellowOutput
        
        Write-YellowOutput "\nPress Enter to continue once you've added your SSH key to the server..."
        Read-Host | Out-Null
    }
    else {
        Write-GreenOutput "SSH key added to server successfully!"
    }
    
    # Test SSH connection to git user
    Write-YellowOutput "Testing SSH connection to git user..."
    $gitSshCommand = "{0}@{1}" -f $GitUser, $UbuntuServerIP
    $output = ssh -o "BatchMode=yes" $gitSshCommand "echo 'SSH connection successful'" 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error: SSH connection to git user failed. Please check your SSH key setup." -ForegroundColor Red
        Write-Host "Error details: $output" -ForegroundColor Red
        return $false
    }
    
    Write-GreenOutput "SSH connection to git user successful!"
    return $true
}

# Function to create a repository on the remote server
function New-RemoteRepository {
    param (
        [string]$RepoName
    )
    
    Write-YellowOutput "Creating repository '$RepoName' on remote server..."
    
    $sshCommand = "{0}@{1}" -f $GitUser, $UbuntuServerIP
    $output = ssh $sshCommand "./create-repo.sh $RepoName" 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error creating repository on remote server: $output" -ForegroundColor Red
        return $false
    }
    
    Write-GreenOutput "Repository created successfully on remote server!"
    return $true
}

# Function to migrate a repository
function Start-RepositoryMigration {
    param (
        [string]$RepoName
    )
    
    $localRepoPath = Join-Path -Path $LocalRepoBasePath -ChildPath $RepoName
    
    # Check if the local repository exists
    if (-not (Test-Path -Path $localRepoPath)) {
        Write-Host "Error: Local repository not found at $localRepoPath" -ForegroundColor Red
        return $false
    }
    
    # Check if it's a git repository
    if (-not (Test-Path -Path (Join-Path -Path $localRepoPath -ChildPath ".git"))) {
        Write-Host "Error: $localRepoPath is not a git repository" -ForegroundColor Red
        return $false
    }
    
    # Create the repository on the remote server
    if (-not (New-RemoteRepository -RepoName $RepoName)) {
        return $false
    }
    
    # Push all branches and tags to the remote server
    Write-YellowOutput "Pushing repository data to remote server..."
    
    # Navigate to the repository directory
    Push-Location -Path $localRepoPath
    
    try {
        # Check if remote already exists
        $remotes = git remote
        if ($remotes -match "migration") {
            Write-YellowOutput "Remote 'migration' already exists, removing it first..."
            git remote remove migration
        }
        
        # Add the remote - using string formatting to avoid variable interpolation issues
        $remoteUrl = "{0}@{1}:{2}.git" -f $GitUser, $UbuntuServerIP, $RepoName
        git remote add migration $remoteUrl
        
        # Push all branches
        git push migration --all
        
        # Push all tags
        git push migration --tags
        
        Write-GreenOutput "Repository '$RepoName' successfully migrated!"
        
        # Update the origin remote to point to the new server
        Write-YellowOutput "Updating remote 'origin' to point to the new server..."
        
        # Check if origin exists before trying to remove it
        if ($remotes -match "origin") {
            git remote remove origin
        }
        
        git remote rename migration origin
        
        Write-GreenOutput "Remote 'origin' updated to point to the new server!"
        
        # Remove the local repository if specified
        if ($RemoveAfterMigration) {
            Write-YellowOutput "Removing local repository..."
            Pop-Location
            Remove-Item -Path $localRepoPath -Recurse -Force
            Write-GreenOutput "Local repository removed!"
        }
        
        return $true
    }
    catch {
        Write-Host "Error during migration: $_" -ForegroundColor Red
        return $false
    }
    finally {
        # Return to the original directory
        Pop-Location
    }
}

# Main script execution

# Check connection to the server
Test-ServerConnection

# Ensure SSH key is set up
if (-not (Ensure-SSHKey)) {
    Write-Host "Error: SSH key setup failed. Migration cannot continue." -ForegroundColor Red
    exit 1
}

# Migrate each repository
$successCount = 0
$failCount = 0

foreach ($repo in $RepositoriesToMigrate) {
    Write-BlueOutput "\nMigrating repository: $repo"
    
    if (Start-RepositoryMigration -RepoName $repo) {
        $successCount++
    }
    else {
        $failCount++
    }
}

# Summary
Write-BlueOutput "\n=== Migration Summary ==="
Write-GreenOutput "Successfully migrated: $successCount repositories"

if ($failCount -gt 0) {
    Write-Host "Failed to migrate: $failCount repositories" -ForegroundColor Red
}

$sshCommand = "{0}@{1}" -f $GitUser, $UbuntuServerIP
Write-YellowOutput "\nTo clone repositories from the new server:"
Write-YellowOutput ("  git clone {0}:<repository-name>.git" -f $sshCommand)

Write-YellowOutput "\nGit web interface:"
Write-YellowOutput ("  http://{0}/gitweb" -f $UbuntuServerIP)

Write-BlueOutput "\n=== Migration Complete ==="
