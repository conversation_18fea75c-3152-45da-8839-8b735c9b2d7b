# Database Service Installation Guide

This document provides instructions for installing and configuring the Database Service.

## Prerequisites

Before installing the Database Service, ensure that the following prerequisites are met:

### Server Requirements

- **Operating System**: Ubuntu 24.04 or later
- **CPU**: 2+ cores recommended
- **Memory**: 4GB+ RAM recommended
- **Disk Space**: 10GB+ free space

### Software Dependencies

- **PostgreSQL**: Version 17 or later
- **GCC**: Version 14 or later with C++23 support
- **CMake**: Version 3.20 or later
- **Boost**: Version 1.80 or later
- **libpqxx**: PostgreSQL C++ client library
- **OpenSSL**: Development libraries

### User Accounts

- **btaylor-admin**: Administrative user with sudo privileges
- **postgres**: PostgreSQL database user

## Installation Options

There are two ways to install the Database Service:

1. **Automated Deployment**: Using the deployment script
2. **Manual Installation**: Building and installing from source

## Automated Deployment

The automated deployment script handles all aspects of installation:

1. Open a command prompt on the build server
2. Navigate to the deployment scripts directory:
   ```
   cd D:\Augment\project-tracker\database-service-scripts\scripts
   ```
3. Run the deployment script:
   ```
   deploy-database-service.bat
   ```
4. Follow the on-screen prompts to complete the installation

## Manual Installation

### Building from Source

1. Connect to the server via SSH:
   ```
   ssh <EMAIL>
   ```

2. Create a build directory:
   ```bash
   mkdir -p ~/database-service-build
   cd ~/database-service-build
   ```

3. Clone the source code (if using Git):
   ```bash
   git clone <repository-url> .
   ```
   
   Or copy the source code from the build server:
   ```bash
   # This would be done from the build server
   scp -r D:\Augment\project-tracker\database-service\* <EMAIL>:~/database-service-build/
   ```

4. Create a build directory:
   ```bash
   mkdir build
   cd build
   ```

5. Configure the build:
   ```bash
   CC=gcc-14 CXX=g++-14 cmake ..
   ```

6. Build the application:
   ```bash
   make -j$(nproc)
   ```

### Installation

1. Create the installation directory:
   ```bash
   sudo mkdir -p /opt/database-service/bin
   sudo mkdir -p /opt/database-service/config
   sudo mkdir -p /opt/database-service/schemas
   sudo mkdir -p /opt/database-service/migrations
   sudo mkdir -p /opt/database-service/logs
   ```

2. Copy the executable:
   ```bash
   sudo cp ~/database-service-build/build/bin/database-service /opt/database-service/bin/
   ```

3. Copy configuration files:
   ```bash
   sudo cp ~/database-service-build/config/config.ini /opt/database-service/config/
   ```

4. Copy schema files:
   ```bash
   sudo cp -r ~/database-service-build/schemas/* /opt/database-service/schemas/
   ```

5. Copy migration files:
   ```bash
   sudo cp -r ~/database-service-build/migrations/* /opt/database-service/migrations/
   ```

6. Set permissions:
   ```bash
   sudo chown -R btaylor-admin:btaylor-admin /opt/database-service
   sudo chmod -R 755 /opt/database-service
   sudo chmod -R 644 /opt/database-service/config/*
   sudo chmod 755 /opt/database-service/bin/database-service
   ```

## Certificate Access Setup

The Database Service requires access to SSL certificates for secure database connections:

1. Run the certificate access setup script:
   ```bash
   sudo /opt/database-service/bin/setup-certificate-access.sh
   ```

   This script:
   - Adds the database service user to the appropriate groups
   - Sets up the necessary permissions for certificate access

## Service Setup

1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/database-service.service
   ```

2. Add the following content:
   ```
   [Unit]
   Description=Database Service
   After=network.target postgresql.service
   
   [Service]
   Type=simple
   User=btaylor-admin
   ExecStart=/opt/database-service/bin/database-service --config /opt/database-service/config/config.ini
   Restart=on-failure
   RestartSec=5
   
   [Install]
   WantedBy=multi-user.target
   ```

3. Reload systemd:
   ```bash
   sudo systemctl daemon-reload
   ```

4. Enable the service to start at boot:
   ```bash
   sudo systemctl enable database-service
   ```

5. Start the service:
   ```bash
   sudo systemctl start database-service
   ```

## Verification

1. Check the service status:
   ```bash
   sudo systemctl status database-service
   ```

2. Check the logs:
   ```bash
   sudo journalctl -u database-service
   ```

3. Test the API:
   ```bash
   curl http://localhost:8080/api/health
   ```

## Troubleshooting

### Common Issues

1. **Service fails to start**:
   - Check the logs: `sudo journalctl -u database-service`
   - Verify permissions: `ls -la /opt/database-service/bin/database-service`
   - Check configuration: `cat /opt/database-service/config/config.ini`

2. **Database connection issues**:
   - Verify PostgreSQL is running: `sudo systemctl status postgresql`
   - Check database credentials in config file
   - Verify certificate access: `ls -la /etc/letsencrypt/live/chcit.org/`

3. **Permission issues**:
   - Verify user permissions: `id btaylor-admin`
   - Check file permissions: `ls -la /opt/database-service/`
   - Verify certificate access: `groups btaylor-admin`

## Additional Resources

- [Database Service Documentation Index](./Database%20Service%20Documentation%20Index.md)
- [Configuration Validation](./configuration-validation.md)
- [Certificate Access](./certificate-access.md)
