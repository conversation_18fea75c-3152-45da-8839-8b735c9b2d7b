#pragma once

// Build configuration options
#cmakedefine DATABASE_SERVICE_SSL_ENABLED
#cmakedefine DATABASE_SERVICE_JWT_ENABLED
#cmakedefine DATABASE_SERVICE_METRICS_ENABLED

// Version information
#define DATABASE_SERVICE_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define DATABASE_SERVICE_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define DATABASE_SERVICE_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define DATABASE_SERVICE_VERSION "@PROJECT_VERSION@"

// Compiler and platform detection
#if defined(_MSC_VER)
    #define DATABASE_SERVICE_COMPILER_MSVC
#elif defined(__clang__)
    #define DATABASE_SERVICE_COMPILER_CLANG
#elif defined(__GNUC__)
    #define DATABASE_SERVICE_COMPILER_GCC
#endif

#if defined(_WIN32) || defined(_WIN64)
    #define DATABASE_SERVICE_PLATFORM_WINDOWS
#elif defined(__APPLE__)
    #define DATABASE_SERVICE_PLATFORM_APPLE
#elif defined(__linux__)
    #define DATABASE_SERVICE_PLATFORM_LINUX
#elif defined(__unix__)
    #define DATABASE_SERVICE_PLATFORM_UNIX
#endif
