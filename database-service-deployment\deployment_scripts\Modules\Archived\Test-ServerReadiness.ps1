# Test Server Readiness Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force
# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Test server readiness function
function Test-ServerReadiness {
    Clear-Host

    # Enable UI Mode for header display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Test Server Readiness                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after header display
    Disable-UIMode

    # Check if configuration is loaded
    if ($null -eq $script:Config -or $null -eq $script:Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "ServerReadiness"
        Wait-ForUser
        Show-MainMenu
        return
    }

    Write-Log -Message "Testing server readiness for $($($script:Config.ssh.host))..." -Level "Info" -Component "ServerReadiness"
    Write-Log -Message " " -Level "Info" -Component "ServerReadiness"

    # First, test SSH connection
    Write-Log -Message "[1/6] Testing SSH connection..." -Level "Info" -Component "ServerReadiness"
    $testCmd = "echo 'SSH connection successful'"

    # Use direct SSH command with improved options
    $sshKeyPath = $null
    if ($script:Config.ssh.PSObject.Properties.Name -contains "local_key_path") {
        $sshKeyPath = $script:Config.ssh.local_key_path
    } elseif ($script:Config.ssh.PSObject.Properties.Name -contains "key_path") {
        $sshKeyPath = $script:Config.ssh.key_path
    }

    $sshHost = $script:Config.ssh.host
    $sshPort = $script:Config.ssh.port
    $sshUser = $script:Config.ssh.username

    # Add options to bypass host checking
    $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$testCmd`""
    $result = Invoke-Expression $sshCommand 2>&1

    if ($null -ne $result -and $result -match "SSH connection successful") {
        Write-Log -Message "  SSH connection successful!" -Level "Success" -Component "ServerReadiness"
    } else {
        Write-Log -Message "  SSH connection failed." -Level "Error" -Component "ServerReadiness"
        Write-Log -Message "  Please check your SSH configuration." -Level "Error" -Component "ServerReadiness"
        Wait-ForUser
        Show-MainMenu
        return
    }

    # Check if the installation directory exists
    Write-Log -Message "[2/6] Checking installation directories..." -Level "Info" -Component "ServerReadiness"

    # Get installation directory from configuration
    $installDir = $null
    if ($script:Config.project.PSObject.Properties.Name -contains "remote_install_dir") {
        $installDir = $script:Config.project.remote_install_dir
    } elseif ($script:Config.project.PSObject.Properties.Name -contains "install_dir") {
        $installDir = $script:Config.project.install_dir
    }

    # Get directories to check
    $directories = @(
        @{ Name = "Installation directory"; Path = $installDir },
        @{ Name = "Binary directory"; Path = "$installDir/bin" },
        @{ Name = "Configuration directory"; Path = "$installDir/config" },
        @{ Name = "Library directory"; Path = "$installDir/lib" },
        @{ Name = "Log directory"; Path = "$installDir/logs" },
        @{ Name = "SQL directory"; Path = "$installDir/sql" },
        @{ Name = "Systemd directory"; Path = "/etc/systemd/system" }
    )

    $missingDirectories = @()

    foreach ($dir in $directories) {
        $checkDirCmd = "test -d '$($dir.Path)' && echo 'EXISTS' || echo 'NOT_EXISTS'"

        # Use direct SSH command
        $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkDirCmd`""
        $dirExists = Invoke-Expression $sshCommand 2>&1

        if ($dirExists -match "EXISTS") {
            Write-Log -Message "  $($dir.Name) exists: $($dir.Path)" -Level "Success" -Component "ServerReadiness"
        } else {
            Write-Log -Message "  $($dir.Name) does not exist: $($dir.Path)" -Level "Warning" -Component "ServerReadiness"
            $missingDirectories += $dir.Path
        }
    }

    if ($missingDirectories.Count -gt 0) {
        Write-Log -Message "  Some directories will be created during installation." -Level "Info" -Component "ServerReadiness"
    }

    # Check if required packages are installed
    Write-Log -Message "[3/6] Checking required packages..." -Level "Info" -Component "ServerReadiness"

    $requiredPackages = @(
        "postgresql",
        "g++-14",
        "cmake",
        "build-essential",
        "libboost-all-dev",
        "libpq-dev",
        "libpqxx-dev",
        "libssl-dev",
        "nlohmann-json3-dev"
    )

    $missingPackages = @()

    foreach ($package in $requiredPackages) {
        # Different packages need different check commands
        switch -Wildcard ($package) {
            "libboost*" {
                $checkPackageCmd = "dpkg -l | grep libboost | wc -l"
            }
            "nlohmann-json*" {
                $checkPackageCmd = "dpkg -l | grep nlohmann-json | wc -l"
            }
            "g++-*" {
                $checkPackageCmd = "dpkg -l | grep g\+\+-14 | wc -l"
            }
            default {
                $checkPackageCmd = "dpkg -l | grep -w $package | wc -l"
            }
        }

        # Use direct SSH command
        $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkPackageCmd`""
        $packageExists = Invoke-Expression $sshCommand 2>&1

        if ($packageExists -gt 0) {
            Write-Log -Message "  Package $package is installed." -Level "Success" -Component "ServerReadiness"
        } else {
            Write-Log -Message "  Package $package is not installed." -Level "Error" -Component "ServerReadiness"
            $missingPackages += $package
        }
    }

    # Check PostgreSQL status
    Write-Log -Message "[4/6] Checking PostgreSQL status..." -Level "Info" -Component "ServerReadiness"
    $checkPgCmd = "systemctl is-active postgresql"

    # Use direct SSH command
    $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkPgCmd`""
    $pgStatus = Invoke-Expression $sshCommand 2>&1

    if ($pgStatus -eq "active") {
        Write-Log -Message "  PostgreSQL is running." -Level "Success" -Component "ServerReadiness"
    } else {
        Write-Log -Message "  PostgreSQL is not running." -Level "Error" -Component "ServerReadiness"
        Write-Log -Message "  Please start PostgreSQL service." -Level "Warning" -Component "ServerReadiness"
    }

    # Check if the service user exists
    Write-Log -Message "[5/6] Checking service user..." -Level "Info" -Component "ServerReadiness"

    # Get service user from configuration
    $serviceUser = $null
    if ($script:Config.service.PSObject.Properties.Name -contains "user") {
        $serviceUser = $script:Config.service.user
    } elseif ($script:Config.service.PSObject.Properties.Name -contains "username") {
        $serviceUser = $script:Config.service.username
    }

    if ([string]::IsNullOrEmpty($serviceUser)) {
        Write-Log -Message "  Service user not configured in configuration." -Level "Warning" -Component "ServerReadiness"
        $serviceUser = "dbservice" # Default fallback
        Write-Log -Message "  Using default service user: $serviceUser" -Level "Info" -Component "ServerReadiness"
    }

    $checkUserCmd = "id -u $serviceUser 2>/dev/null || echo 'NOT_EXISTS'"

    # Use direct SSH command
    $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkUserCmd`""
    $userExists = Invoke-Expression $sshCommand 2>&1

    if ($userExists -ne "NOT_EXISTS") {
        Write-Log -Message "  Service user $serviceUser exists." -Level "Success" -Component "ServerReadiness"
    } else {
        Write-Log -Message "  Service user $serviceUser does not exist." -Level "Warning" -Component "ServerReadiness"
        Write-Log -Message "  User will be created during installation." -Level "Info" -Component "ServerReadiness"
    }

    Write-Log -Message "" -Level "Info" -Component "ServerReadiness"

    # Check SSL certificates (Step 6)
    Write-Log -Message "[6/6] Checking SSL certificates..." -Level "Info" -Component "ServerReadiness"

    # Check in Let's Encrypt live directory
    $checkLiveCertCmd = "test -d '/etc/letsencrypt/live/chcit.org' && echo 'FOUND' || echo 'NOT_FOUND'"

    # Use direct SSH command for live directory
    $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkLiveCertCmd`""
    $liveCertExists = Invoke-Expression $sshCommand 2>&1

    # Check in Let's Encrypt archive directory
    $checkArchiveCertCmd = "test -d '/etc/letsencrypt/archive/chcit.org' && echo 'FOUND' || echo 'NOT_FOUND'"

    # Use direct SSH command for archive directory
    $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkArchiveCertCmd`""
    $archiveCertExists = Invoke-Expression $sshCommand 2>&1

    if ($liveCertExists -eq "FOUND" -or $archiveCertExists -eq "FOUND") {
        if ($liveCertExists -eq "FOUND") {
            Write-Log -Message "  SSL certificates found in /etc/letsencrypt/live/chcit.org" -Level "Success" -Component "ServerReadiness"
        }

        if ($archiveCertExists -eq "FOUND") {
            Write-Log -Message "  SSL certificates found in /etc/letsencrypt/archive/chcit.org" -Level "Success" -Component "ServerReadiness"
        }

        # Check certificate access for database service user
        # Service user should already be set from previous check
        if ([string]::IsNullOrEmpty($serviceUser)) {
            if ($script:Config.service.PSObject.Properties.Name -contains "user") {
                $serviceUser = $script:Config.service.user
            } elseif ($script:Config.service.PSObject.Properties.Name -contains "username") {
                $serviceUser = $script:Config.service.username
            } else {
                $serviceUser = "dbservice" # Default fallback
            }
        }

        $checkAccessCmd = "sudo -u $serviceUser ls -la /etc/letsencrypt/archive/chcit.org 2>&1 | grep -v 'Permission denied' | wc -l"
        $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkAccessCmd`""
        $hasAccess = Invoke-Expression $sshCommand 2>&1

        if ($hasAccess -gt 0) {
            Write-Log -Message "  Database service user has access to certificates." -Level "Success" -Component "ServerReadiness"
        } else {
            Write-Log -Message "  Database service user does not have access to certificates." -Level "Warning" -Component "ServerReadiness"
            Write-Log -Message "  Run the certificate access setup script to grant access." -Level "Info" -Component "ServerReadiness"
        }

        # Check if PostgreSQL is configured to use SSL
        $checkPgSslCmd = "grep ssl /etc/postgresql/*/main/postgresql.conf | grep -v '#' | wc -l"
        $sshCommand = "ssh -i `"$sshKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$checkPgSslCmd`""
        $pgSslEnabled = Invoke-Expression $sshCommand 2>&1

        if ($pgSslEnabled -gt 0) {
            Write-Log -Message "  PostgreSQL SSL is enabled." -Level "Success" -Component "ServerReadiness"
        } else {
            Write-Log -Message "  PostgreSQL SSL is not enabled." -Level "Warning" -Component "ServerReadiness"
            Write-Log -Message "  SSL should be configured for PostgreSQL." -Level "Info" -Component "ServerReadiness"
        }
    } else {
        Write-Log -Message "  SSL certificates not found in Let's Encrypt directories." -Level "Warning" -Component "ServerReadiness"
        Write-Log -Message "  SSL certificates should be configured for secure connections." -Level "Info" -Component "ServerReadiness"
    }

    # Now display the completion message ONCE at the end
    Write-Log -Message "" -Level "Info" -Component "ServerReadiness"
    Write-Log -Message "Server readiness check completed." -Level "Info" -Component "ServerReadiness"
    Write-Log -Message "" -Level "Info" -Component "ServerReadiness"

    if ($missingPackages.Count -gt 0) {
        Write-Log -Message "Missing packages:" -Level "Error" -Component "ServerReadiness"
        foreach ($package in $missingPackages) {
            Write-Log -Message "  - $package" -Level "Error" -Component "ServerReadiness"
        }

        Write-Log -Message "" -Level "Info" -Component "ServerReadiness"
        $installDeps = Read-Host "Would you like to install missing dependencies? (y/n)"

        if ($installDeps -eq "y") {
            & "$PSScriptRoot\Install-Dependencies.ps1"
            return
        }
    } else {
        Write-Log -Message "All required packages are installed." -Level "Success" -Component "ServerReadiness"
    }

    # Enable UI Mode for final message
    Enable-UIMode

    Write-Log -Message "" -Level "UI"
    Write-Log -Message "Press Enter to return to the main menu..." -Level "UI" -ForegroundColor Cyan

    # Disable UI Mode before waiting for user input
    Disable-UIMode

    Wait-ForUser

    Show-MainMenu
}

<#
.SYNOPSIS
    Determines if the script is being run directly or imported as a module.

.DESCRIPTION
    This script checks if it's being run directly or imported as a module.
    If run directly, it executes the Test-ServerReadiness function.
    If imported as a module, it exports the Test-ServerReadiness function.
#>

# Get information about the current execution context
$scriptName = $MyInvocation.MyCommand.Name
$callerName = (Get-PSCallStack)[1].Command

# If this script is being run directly (not dot-sourced or imported)
if ($callerName -eq "<ScriptBlock>" -or $callerName -eq $scriptName) {
    # Execute the function
    Test-ServerReadiness
} else {
    # Export the function for module import
    Export-ModuleMember -Function Test-ServerReadiness
}
