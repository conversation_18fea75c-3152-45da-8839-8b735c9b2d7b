/**
 * Git Commit History Chart Module
 * Handles the visualization of repository commit history
 */
const GitCommitHistoryChart = {
    // Module configuration
    config: {
        chartId: 'commit-history-chart',
        containerId: 'commit-history-container',
        loadingId: 'commit-chart-loading',
        errorId: 'commit-chart-error',
        messageId: 'commit-chart-message',
        days: 30,
        colors: {
            background: 'rgba(54, 162, 235, 0.5)',
            border: 'rgba(54, 162, 235, 1)'
        }
    },
    
    // Module state
    state: {
        chart: null
    },
    
    /**
     * Initialize the chart module
     * @returns {Object} This module for chaining
     */
    init() {
        console.log('GitCommitHistoryChart.init() - Initialization complete');
        return this;
    },
    
    /**
     * Clean up chart instance
     */
    cleanupChart() {
        if (this.state.chart) {
            this.state.chart.destroy();
            this.state.chart = null;
            console.log('Chart instance cleaned up');
        }
    },
    
    /**
     * Initialize chart for a repository
     * @param {string} repoName - Repository name
     */
    initChart(repoName) {
        console.log(`init<PERSON>hart called for ${repoName}`);
        this.fetchAndRenderChart(repoName);
    },
    
    /**
     * Fetch data and render chart
     * @param {string} repoName - Repository name
     */
    async fetchAndRenderChart(repoName) {
        console.log(`fetchAndRenderChart called for ${repoName}`);
        
        try {
            // Get elements
            const container = document.getElementById(this.config.containerId);
            const canvas = document.getElementById(this.config.chartId);
            const loading = document.getElementById(this.config.loadingId);
            const error = document.getElementById(this.config.errorId);
            const message = document.getElementById(this.config.messageId);
            
            if (!container || !canvas) {
                console.error('Chart container elements not found');
                return;
            }
            
            console.log('Found chart elements:', { container, canvas, loading, error });
            
            // Reset UI state
            if (loading) loading.style.display = 'flex';
            if (error) error.style.display = 'none';
            if (canvas) canvas.style.display = 'none';
            if (message) message.style.display = 'none';
            
            // Clean up existing chart
            this.cleanupChart();
            
            // Format repo name (strip .git extension)
            const cleanName = repoName.trim().replace(/\.git$/, '');
            
            // Fetch data from API
            const apiUrl = `/api/git/repository/${encodeURIComponent(cleanName)}/commits?days=${this.config.days}`;
            console.log(`Fetching commit history from ${apiUrl}`);
            
            const response = await fetch(apiUrl);
            
            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('API response:', data);
            
            // Check for API error
            if (data.error) {
                throw new Error(data.error);
            }
            
            // Check for commits array
            if (!data.commits || !Array.isArray(data.commits)) {
                throw new Error('Invalid response format: missing commits array');
            }
            
            // Format data for chart
            const chartData = this.formatCommitData(data.commits);
            
            // Handle empty data
            if (chartData.labels.length === 0 || chartData.datasets[0].data.every(val => val === 0)) {
                console.log('No commit data found');
                if (loading) loading.style.display = 'none';
                if (message) {
                    message.textContent = 'No commits found in the selected time period';
                    message.style.display = 'block';
                }
                return;
            }
            
            // Update UI
            if (loading) loading.style.display = 'none';
            if (canvas) canvas.style.display = 'block';
            
            // Create chart
            this.createChart(canvas, chartData);
            
        } catch (error) {
            console.error('Error fetching/rendering commit history:', error);
            const errorEl = document.getElementById(this.config.errorId);
            const loadingEl = document.getElementById(this.config.loadingId);
            
            if (loadingEl) loadingEl.style.display = 'none';
            if (errorEl) {
                errorEl.textContent = `Failed to load commit history: ${error.message}`;
                errorEl.style.display = 'flex';
            }
        }
    },
    
    /**
     * Format commit data for chart
     * @param {Array} commits - Array of commit objects from API
     * @returns {Object} Formatted data for Chart.js
     */
    formatCommitData(commits) {
        // Group commits by date
        const commitsByDate = {};
        const dateFormat = new Intl.DateTimeFormat('en-US', { 
            month: 'short', 
            day: 'numeric' 
        });
        
        // Create date range for past N days
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - this.config.days);
        
        // Initialize all dates with 0 counts
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            const dateKey = d.toISOString().split('T')[0];
            const displayDate = dateFormat.format(d);
            commitsByDate[dateKey] = {
                count: 0,
                displayDate: displayDate
            };
        }
        
        // Count commits by date
        for (const commit of commits) {
            if (commit.date) {
                const dateKey = commit.date.split('T')[0];
                if (commitsByDate[dateKey]) {
                    commitsByDate[dateKey].count++;
                }
            }
        }
        
        // Convert to arrays
        const labels = [];
        const counts = [];
        
        // Sort dates chronologically
        const sortedDates = Object.keys(commitsByDate).sort();
        for (const dateKey of sortedDates) {
            labels.push(commitsByDate[dateKey].displayDate);
            counts.push(commitsByDate[dateKey].count);
        }
        
        return {
            labels: labels,
            datasets: [{
                label: 'Commits',
                data: counts,
                backgroundColor: this.config.colors.background,
                borderColor: this.config.colors.border,
                borderWidth: 1
            }]
        };
    },
    
    /**
     * Create the chart with the provided data
     * @param {HTMLCanvasElement} canvas - Canvas element
     * @param {Object} chartData - Data for the chart
     */
    createChart(canvas, chartData) {
        console.log('createChart called with canvas and data:', canvas, chartData);
        
        if (!canvas) {
            console.error('Cannot create chart: canvas element not provided');
            return;
        }
        
        if (!chartData || !chartData.labels || !chartData.datasets) {
            console.error('Cannot create chart: invalid chart data', chartData);
            return;
        }
        
        try {
            // Destroy any existing chart instance
            if (this.state.chart) {
                this.state.chart.destroy();
                this.state.chart = null;
            }
            
            // Make sure canvas is visible before creating chart
            canvas.style.display = 'block';
            
            // Ensure canvas has proper dimensions
            const parentWidth = canvas.parentElement.clientWidth;
            canvas.width = parentWidth;
            canvas.height = Math.min(parentWidth * 0.6, 400); // Maintain aspect ratio with max height
            
            // Configuration for chart
            const config = {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `Commit History (Last ${this.config.days} Days)`
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const count = context.raw;
                                    return `${count} commit${count !== 1 ? 's' : ''}`;
                                }
                            }
                        }
                    }
                }
            };
            
            // Create chart with a small delay to ensure DOM is ready
            setTimeout(() => {
                try {
                    this.state.chart = new Chart(canvas, config);
                    console.log('Chart created successfully');
                } catch (err) {
                    console.error('Error creating chart in timeout:', err);
                }
            }, 100);
        } catch (error) {
            console.error('Error creating chart:', error);
            throw new Error(`Chart creation failed: ${error.message}`);
        }
    }
};

// Expose the module globally
window.GitCommitHistoryChart = GitCommitHistoryChart;
