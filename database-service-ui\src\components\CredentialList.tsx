import React, { useState, useEffect } from 'react';
import { credentialsAPI } from '../services/api';
import './CredentialList.css';

interface Credential {
  key: string;
  value: string;
}

interface CredentialListProps {
  refreshTrigger?: number;
}

const CredentialList: React.FC<CredentialListProps> = ({ refreshTrigger }) => {
  const [credentials, setCredentials] = useState<string[]>([]);
  const [selectedCredential, setSelectedCredential] = useState<Credential | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);

  // This is a mock function since the API doesn't provide a way to list all credentials
  // In a real implementation, you would need an API endpoint to list all credentials
  const fetchCredentialKeys = async () => {
    // Mock implementation - in a real app, this would call an API endpoint
    // For now, we'll use localStorage to simulate storing credential keys
    const storedKeys = localStorage.getItem('credential_keys');
    return storedKeys ? JSON.parse(storedKeys) : [];
  };

  // Mock function to save credential keys to localStorage
  const saveCredentialKey = (key: string) => {
    const storedKeys = localStorage.getItem('credential_keys');
    const keys = storedKeys ? JSON.parse(storedKeys) : [];
    if (!keys.includes(key)) {
      keys.push(key);
      localStorage.setItem('credential_keys', JSON.stringify(keys));
    }
  };

  // Mock function to remove credential key from localStorage
  const removeCredentialKey = (key: string) => {
    const storedKeys = localStorage.getItem('credential_keys');
    if (storedKeys) {
      const keys = JSON.parse(storedKeys);
      const updatedKeys = keys.filter((k: string) => k !== key);
      localStorage.setItem('credential_keys', JSON.stringify(updatedKeys));
    }
  };

  useEffect(() => {
    const loadCredentials = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const keys = await fetchCredentialKeys();
        setCredentials(keys);
      } catch (err: any) {
        setError(err.message || 'Failed to load credentials');
      } finally {
        setLoading(false);
      }
    };
    
    loadCredentials();
  }, [refreshTrigger]);

  const handleViewCredential = async (key: string) => {
    try {
      const response = await credentialsAPI.getCredential(key);
      
      if (response.data.success) {
        setSelectedCredential(response.data.data);
      } else {
        setError(response.data.error?.message || 'Failed to retrieve credential');
      }
    } catch (err: any) {
      setError(err.response?.data?.error?.message || err.message || 'An error occurred');
    }
  };

  const handleDeleteCredential = async (key: string) => {
    if (!window.confirm(`Are you sure you want to delete the credential "${key}"?`)) {
      return;
    }
    
    setDeleteLoading(key);
    setError(null);
    
    try {
      const response = await credentialsAPI.removeCredential(key);
      
      if (response.data.success) {
        // Remove from the list
        setCredentials(credentials.filter(k => k !== key));
        
        // Remove from localStorage
        removeCredentialKey(key);
        
        // Clear selected credential if it was the one deleted
        if (selectedCredential?.key === key) {
          setSelectedCredential(null);
        }
      } else {
        setError(response.data.error?.message || 'Failed to delete credential');
      }
    } catch (err: any) {
      setError(err.response?.data?.error?.message || err.message || 'An error occurred');
    } finally {
      setDeleteLoading(null);
    }
  };

  return (
    <div className="credential-list card">
      <h2>Stored Credentials</h2>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      {loading ? (
        <div className="credential-loading">Loading credentials...</div>
      ) : credentials.length === 0 ? (
        <div className="credential-empty">No credentials stored</div>
      ) : (
        <div className="credential-table-container">
          <table className="credential-table">
            <thead>
              <tr>
                <th>Key</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {credentials.map(key => (
                <tr key={key}>
                  <td>{key}</td>
                  <td className="credential-actions">
                    <button 
                      className="btn btn-sm btn-primary"
                      onClick={() => handleViewCredential(key)}
                    >
                      View
                    </button>
                    <button 
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDeleteCredential(key)}
                      disabled={deleteLoading === key}
                    >
                      {deleteLoading === key ? 'Deleting...' : 'Delete'}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {selectedCredential && (
        <div className="credential-details">
          <h3>Credential Details</h3>
          <div className="credential-value">
            <strong>Key:</strong> {selectedCredential.key}
          </div>
          <div className="credential-value">
            <strong>Value:</strong> {selectedCredential.value}
          </div>
          <button 
            className="btn btn-secondary"
            onClick={() => setSelectedCredential(null)}
          >
            Close
          </button>
        </div>
      )}
    </div>
  );
};

export default CredentialList;
