#pragma once
#include "../models/log_entry.hpp"
#include <string>
#include <optional>

namespace logging {

class LogParser {
public:
    virtual ~LogParser() = default;
    
    // Parse a log line into a LogEntry
    virtual std::optional<LogEntry> parse(const std::string& line, const std::string& source) = 0;
    
    // Check if this parser can handle the given log line
    virtual bool canParse(const std::string& line) = 0;
    
    // Get parser name
    virtual std::string getName() const = 0;
    
    // Get parser description
    virtual std::string getDescription() const = 0;
};

} // namespace logging
