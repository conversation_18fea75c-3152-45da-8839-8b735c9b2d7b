# Deployment script for Git Server Dashboard

# Configuration
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"

# Create backup folder name with timestamp
$BackupFolder = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupPath = "/opt/git-dashboard-backups/$BackupFolder"

# Create backup
Write-Host "Creating backup..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo mkdir -p ${BackupPath} && sudo cp -r ${RemotePath}/* ${BackupPath}/"

# Deploy app.py
Write-Host "Deploying app.py..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/app.py" "${RemoteUser}@${RemoteHost}:/tmp/app.py"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/app.py ${RemotePath}/app.py && sudo chown www-data:www-data ${RemotePath}/app.py"

# Deploy index.html
Write-Host "Deploying index.html..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/index.html" "${RemoteUser}@${RemoteHost}:/tmp/index.html"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/index.html ${RemotePath}/index.html && sudo chown www-data:www-data ${RemotePath}/index.html"

# Deploy refactored Git Repository JavaScript modules
Write-Host "Deploying Git Repository JavaScript modules..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo mkdir -p ${RemotePath}/js"

# Deploy git-utils.js
Write-Host "  - Deploying js/git-utils.js..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/js/git-utils.js" "${RemoteUser}@${RemoteHost}:/tmp/git-utils.js"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/git-utils.js ${RemotePath}/js/git-utils.js && sudo chown www-data:www-data ${RemotePath}/js/git-utils.js"

# Deploy git-commit-history-chart.js
Write-Host "  - Deploying js/git-commit-history-chart.js..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/js/git-commit-history-chart.js" "${RemoteUser}@${RemoteHost}:/tmp/git-commit-history-chart.js"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/git-commit-history-chart.js ${RemotePath}/js/git-commit-history-chart.js && sudo chown www-data:www-data ${RemotePath}/js/git-commit-history-chart.js"

# Deploy git-size-comparison-chart.js
Write-Host "  - Deploying js/git-size-comparison-chart.js..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/js/git-size-comparison-chart.js" "${RemoteUser}@${RemoteHost}:/tmp/git-size-comparison-chart.js"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/git-size-comparison-chart.js ${RemotePath}/js/git-size-comparison-chart.js && sudo chown www-data:www-data ${RemotePath}/js/git-size-comparison-chart.js"

# Deploy git-repository-list.js
Write-Host "  - Deploying js/git-repository-list.js..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/js/git-repository-list.js" "${RemoteUser}@${RemoteHost}:/tmp/git-repository-list.js"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/git-repository-list.js ${RemotePath}/js/git-repository-list.js && sudo chown www-data:www-data ${RemotePath}/js/git-repository-list.js"

# Deploy git-repository-modal.js
Write-Host "  - Deploying js/git-repository-modal.js..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/js/git-repository-modal.js" "${RemoteUser}@${RemoteHost}:/tmp/git-repository-modal.js"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/git-repository-modal.js ${RemotePath}/js/git-repository-modal.js && sudo chown www-data:www-data ${RemotePath}/js/git-repository-modal.js"

# Deploy git-repository-manager.js
Write-Host "  - Deploying js/git-repository-manager.js..." -ForegroundColor Yellow
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/js/git-repository-manager.js" "${RemoteUser}@${RemoteHost}:/tmp/git-repository-manager.js"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/git-repository-manager.js ${RemotePath}/js/git-repository-manager.js && sudo chown www-data:www-data ${RemotePath}/js/git-repository-manager.js"

# Store original git-repositories.js as backup on server
Write-Host "  - Backing up original js/git-repositories.js on server..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo cp ${RemotePath}/js/git-repositories.js ${RemotePath}/js/git-repositories.js.bak"

# Deploy css/repositories.css
Write-Host "Deploying css/repositories.css..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo mkdir -p ${RemotePath}/css"
scp "d:/Codeium/CHCIT/project-tracker/git-server-web/css/repositories.css" "${RemoteUser}@${RemoteHost}:/tmp/repositories.css"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/repositories.css ${RemotePath}/css/repositories.css && sudo chown www-data:www-data ${RemotePath}/css/repositories.css"

# Restart the Flask application
Write-Host "Restarting Flask application..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo systemctl restart git-dashboard.service"

Write-Host "Deployment completed!" -ForegroundColor Green
Write-Host "You can now access the dashboard at http://git.chcit.org/" -ForegroundColor Cyan
