#pragma once
#include <string>
#include <memory>
#include <vector>
#include <unordered_map>

namespace dbservice {

namespace core {
    class ConnectionManager;
}

namespace schema {

/**
 * @class SchemaManager
 * @brief Manages database schemas
 */
class SchemaManager {
public:
    /**
     * @brief Constructor
     * @param connectionManager Connection manager
     * @param schemaDirectory Directory containing schema files
     */
    SchemaManager(std::shared_ptr<core::ConnectionManager> connectionManager, 
                 const std::string& schemaDirectory);

    /**
     * @brief Destructor
     */
    ~SchemaManager();

    /**
     * @brief Initialize the schema manager
     * @return True if initialization was successful
     */
    bool initialize();

    /**
     * @brief Create a schema
     * @param schemaName Schema name
     * @return True if schema was created successfully
     */
    bool createSchema(const std::string& schemaName);

    /**
     * @brief Drop a schema
     * @param schemaName Schema name
     * @return True if schema was dropped successfully
     */
    bool dropSchema(const std::string& schemaName);

    /**
     * @brief Check if a schema exists
     * @param schemaName Schema name
     * @return True if schema exists
     */
    bool schemaExists(const std::string& schemaName);

    /**
     * @brief Get all schemas
     * @return List of schema names
     */
    std::vector<std::string> getSchemas();

    /**
     * @brief Apply a migration
     * @param schemaName Schema name
     * @param migrationName Migration name
     * @return True if migration was applied successfully
     */
    bool applyMigration(const std::string& schemaName, const std::string& migrationName);

    /**
     * @brief Get applied migrations
     * @param schemaName Schema name
     * @return List of applied migration names
     */
    std::vector<std::string> getAppliedMigrations(const std::string& schemaName);

private:
    /**
     * @brief Create the migrations table
     * @return True if table was created successfully
     */
    bool createMigrationsTable();

    /**
     * @brief Load migration files
     * @return True if migration files were loaded successfully
     */
    bool loadMigrationFiles();

    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::string schemaDirectory_;
    std::unordered_map<std::string, std::string> migrationFiles_;
    bool initialized_;
};

} // namespace schema
} // namespace dbservice
