.settings-page {
  padding: 2rem 0;
}

.page-title {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.settings-description {
  margin-bottom: 2rem;
}

.settings-description h2 {
  margin-top: 0;
  color: var(--primary-color);
}

.settings-section {
  margin-bottom: 2rem;
}

.settings-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.settings-subsection {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.settings-subsection h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-label input {
  margin-right: 0.5rem;
}

.settings-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}
