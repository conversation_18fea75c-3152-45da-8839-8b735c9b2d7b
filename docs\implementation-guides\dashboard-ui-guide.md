# Git Dashboard UI Guide

This guide provides a comprehensive overview of the Git Dashboard user interface, including screenshots and usage instructions.

## Table of Contents

1. [Overview](#overview)
2. [Dashboard Layout](#dashboard-layout)
3. [Repository List](#repository-list)
4. [Repository Details](#repository-details)
5. [Commit History](#commit-history)
6. [Logs View](#logs-view)
7. [Certificate Management](#certificate-management)
8. [Settings](#settings)
9. [Customization](#customization)

## Overview

The Git Dashboard provides a web-based interface for managing Git repositories, viewing commit history, and monitoring system logs. It integrates with several C++23 applications for enhanced functionality.

![Dashboard Overview](../diagrams/screenshots/dashboard-overview.png)
*Screenshot: Dashboard Overview (placeholder)*

## Dashboard Layout

The Git Dashboard follows a responsive layout with the following components:

1. **Header**: Contains the dashboard title, user information, and navigation menu
2. **Sidebar**: Contains the repository list and navigation links
3. **Main Content**: Contains the selected view (repository details, commit history, logs, etc.)
4. **Footer**: Contains version information and links

![Dashboard Layout](../diagrams/screenshots/dashboard-layout.png)
*Screenshot: Dashboard Layout (placeholder)*

### Navigation Menu

The navigation menu provides access to the following views:

- **Overview**: Dashboard overview with summary information
- **Repositories**: Repository list and details
- **Logs**: System logs and log analysis
- **Certificates**: SSL certificate management
- **Settings**: Dashboard settings

## Repository List

The repository list displays all available Git repositories with the following information:

- Repository name
- Owner
- Last updated date
- Default branch

![Repository List](../diagrams/screenshots/repository-list.png)
*Screenshot: Repository List (placeholder)*

### Repository List Features

- **Search**: Filter repositories by name, owner, or description
- **Sort**: Sort repositories by name, owner, or last updated date
- **Pagination**: Navigate through multiple pages of repositories
- **Create**: Create a new repository
- **Delete**: Delete an existing repository

## Repository Details

The repository details view displays detailed information about a selected repository:

- Repository name and description
- Owner and creation date
- Default branch
- Number of commits, branches, and tags
- Repository statistics

![Repository Details](../diagrams/screenshots/repository-details.png)
*Screenshot: Repository Details (placeholder)*

### Repository Details Tabs

The repository details view includes the following tabs:

- **Overview**: Repository summary and statistics
- **Files**: Repository file browser
- **Commits**: Commit history
- **Branches**: Branch list and management
- **Tags**: Tag list and management
- **Settings**: Repository settings

### Repository Statistics

The repository statistics section displays the following information:

- Commit activity over time
- Code frequency (additions and deletions)
- Language breakdown
- Contributor statistics

![Repository Statistics](../diagrams/screenshots/repository-statistics.png)
*Screenshot: Repository Statistics (placeholder)*

## Commit History

The commit history view displays the commit history for a selected repository:

- Commit hash
- Author and date
- Commit message
- Changed files

![Commit History](../diagrams/screenshots/commit-history.png)
*Screenshot: Commit History (placeholder)*

### Commit History Features

- **Filter**: Filter commits by author, date range, or message
- **Search**: Search for specific commits
- **Pagination**: Navigate through multiple pages of commits
- **Details**: View detailed information for a specific commit
- **Diff**: View changes introduced by a commit

### Commit Details

The commit details view displays detailed information about a selected commit:

- Commit hash and parent(s)
- Author and committer
- Date and time
- Commit message
- Changed files with additions and deletions
- Diff view

![Commit Details](../diagrams/screenshots/commit-details.png)
*Screenshot: Commit Details (placeholder)*

## Logs View

The logs view displays system logs from various sources:

- Git Dashboard logs
- Git Repository Service logs
- Logging Service logs
- Database Service logs
- System logs

![Logs View](../diagrams/screenshots/logs-view.png)
*Screenshot: Logs View (placeholder)*

### Logs View Features

- **Filter**: Filter logs by level, source, or date range
- **Search**: Search for specific log entries
- **Pagination**: Navigate through multiple pages of logs
- **Export**: Export logs to a file
- **Settings**: Configure log display settings

### Log Analysis

The log analysis view provides insights into log patterns and trends:

- Log volume over time
- Error frequency
- Log sources distribution
- Common error patterns

![Log Analysis](../diagrams/screenshots/log-analysis.png)
*Screenshot: Log Analysis (placeholder)*

## Certificate Management

The certificate management view displays SSL certificates and provides certificate synchronization functionality:

- Certificate list
- Certificate details
- Certificate synchronization
- Certificate status

![Certificate Management](../diagrams/screenshots/certificate-management.png)
*Screenshot: Certificate Management (placeholder)*

### Certificate Details

The certificate details view displays detailed information about a selected certificate:

- Common name
- Issuer
- Valid from/to dates
- Subject alternative names
- Fingerprint

![Certificate Details](../diagrams/screenshots/certificate-details.png)
*Screenshot: Certificate Details (placeholder)*

### Certificate Synchronization

The certificate synchronization feature allows synchronizing certificates between servers:

- Source server
- Destination server
- Certificate selection
- Synchronization status

![Certificate Synchronization](../diagrams/screenshots/certificate-synchronization.png)
*Screenshot: Certificate Synchronization (placeholder)*

## Settings

The settings view provides configuration options for the Git Dashboard:

- General settings
- Repository settings
- Log settings
- Certificate settings
- User settings

![Settings](../diagrams/screenshots/settings.png)
*Screenshot: Settings (placeholder)*

### General Settings

The general settings section includes:

- Dashboard theme
- Default view
- Date format
- Time zone
- Language

### Repository Settings

The repository settings section includes:

- Default repository path
- Default owner
- Default branch
- Repository display options

### Log Settings

The log settings section includes:

- Default log level
- Log sources
- Log display options
- Log retention

### Certificate Settings

The certificate settings section includes:

- Certificate path
- Certificate synchronization options
- Certificate display options

### User Settings

The user settings section includes:

- User information
- Password change
- API key management
- Notification preferences

## Customization

The Git Dashboard can be customized in several ways:

### Theme Customization

The dashboard supports multiple themes:

- Light theme
- Dark theme
- Custom themes

![Theme Customization](../diagrams/screenshots/theme-customization.png)
*Screenshot: Theme Customization (placeholder)*

### Layout Customization

The dashboard layout can be customized:

- Sidebar position (left or right)
- Sidebar width
- Content layout (one, two, or three columns)
- Card sizes and spacing

![Layout Customization](../diagrams/screenshots/layout-customization.png)
*Screenshot: Layout Customization (placeholder)*

### Widget Customization

The dashboard widgets can be customized:

- Widget selection
- Widget position
- Widget size
- Widget refresh interval

![Widget Customization](../diagrams/screenshots/widget-customization.png)
*Screenshot: Widget Customization (placeholder)*

