# Database Service API Reference

This document provides a comprehensive reference for the Database Service API.

## API Overview

The Database Service exposes a RESTful API for database operations. The API is designed to be:

- **Secure**: All endpoints require authentication
- **Consistent**: Follows REST principles with consistent patterns
- **Versioned**: API versioning to support backward compatibility
- **Documented**: Comprehensive documentation and examples

## Base URL

```
https://git.chcit.org/api/database/v1
```

## Authentication

All API requests require authentication using one of the following methods:

### API Key

Include the API key in the `X-API-Key` header:

```
X-API-Key: your-api-key
```

### JWT Token

Include a JWT token in the `Authorization` header:

```
Authorization: Bearer your-jwt-token
```

## Common Headers

| Header | Description |
|--------|-------------|
| `Content-Type` | `application/json` for request bodies |
| `Accept` | `application/json` for response bodies |
| `X-Request-ID` | Optional client-provided request ID for tracing |

## Response Format

All API responses follow a consistent format:

```json
{
  "status": "success",
  "data": { ... },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z",
    "request_id": "abc123"
  }
}
```

For errors:

```json
{
  "status": "error",
  "error": {
    "code": "invalid_request",
    "message": "Invalid request parameters",
    "details": { ... }
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z",
    "request_id": "abc123"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `invalid_request` | Invalid request parameters |
| `unauthorized` | Authentication failed |
| `forbidden` | Permission denied |
| `not_found` | Resource not found |
| `conflict` | Resource conflict |
| `internal_error` | Internal server error |

## Endpoints

### Health Check

```
GET /health
```

Returns the health status of the service.

**Response:**

```json
{
  "status": "success",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "database_connection": "connected",
    "uptime": 3600
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

### Database Management

#### List Databases

```
GET /databases
```

Returns a list of all databases.

**Response:**

```json
{
  "status": "success",
  "data": {
    "databases": [
      {
        "name": "git_dashboard",
        "owner": "postgres",
        "size": "10 MB",
        "created_at": "2023-06-01T10:00:00Z"
      },
      {
        "name": "logging",
        "owner": "postgres",
        "size": "5 MB",
        "created_at": "2023-06-02T11:00:00Z"
      }
    ]
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Create Database

```
POST /databases
```

Creates a new database.

**Request:**

```json
{
  "name": "new_database",
  "owner": "postgres",
  "encoding": "UTF8",
  "template": "template0"
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "name": "new_database",
    "owner": "postgres",
    "encoding": "UTF8",
    "created_at": "2023-06-15T12:34:56Z"
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Get Database

```
GET /databases/{database_name}
```

Returns information about a specific database.

**Response:**

```json
{
  "status": "success",
  "data": {
    "name": "git_dashboard",
    "owner": "postgres",
    "encoding": "UTF8",
    "size": "10 MB",
    "created_at": "2023-06-01T10:00:00Z",
    "schemas": [
      "public",
      "git_repos",
      "users"
    ]
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Drop Database

```
DELETE /databases/{database_name}
```

Drops a database.

**Response:**

```json
{
  "status": "success",
  "data": {
    "name": "git_dashboard",
    "dropped_at": "2023-06-15T12:34:56Z"
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

### Schema Management

#### List Schemas

```
GET /databases/{database_name}/schemas
```

Returns a list of all schemas in a database.

**Response:**

```json
{
  "status": "success",
  "data": {
    "schemas": [
      {
        "name": "public",
        "owner": "postgres",
        "tables": 5
      },
      {
        "name": "git_repos",
        "owner": "postgres",
        "tables": 3
      }
    ]
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Create Schema

```
POST /databases/{database_name}/schemas
```

Creates a new schema in a database.

**Request:**

```json
{
  "name": "new_schema",
  "owner": "postgres"
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "name": "new_schema",
    "owner": "postgres",
    "created_at": "2023-06-15T12:34:56Z"
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Get Schema

```
GET /databases/{database_name}/schemas/{schema_name}
```

Returns information about a specific schema.

**Response:**

```json
{
  "status": "success",
  "data": {
    "name": "git_repos",
    "owner": "postgres",
    "created_at": "2023-06-01T10:00:00Z",
    "tables": [
      "repositories",
      "commits",
      "branches"
    ]
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Drop Schema

```
DELETE /databases/{database_name}/schemas/{schema_name}
```

Drops a schema.

**Response:**

```json
{
  "status": "success",
  "data": {
    "name": "git_repos",
    "dropped_at": "2023-06-15T12:34:56Z"
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

### Schema Migration

#### Get Current Version

```
GET /databases/{database_name}/schemas/{schema_name}/version
```

Returns the current schema version.

**Response:**

```json
{
  "status": "success",
  "data": {
    "version": "1.0.0",
    "description": "Initial schema",
    "applied_at": "2023-06-01T10:00:00Z"
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### List Available Migrations

```
GET /databases/{database_name}/schemas/{schema_name}/migrations
```

Returns a list of available migrations.

**Response:**

```json
{
  "status": "success",
  "data": {
    "current_version": "1.0.0",
    "available_migrations": [
      {
        "version": "1.1.0",
        "description": "Add user roles",
        "file": "git_dashboard_1.0.0_to_1.1.0.sql"
      },
      {
        "version": "1.2.0",
        "description": "Add repository tags",
        "file": "git_dashboard_1.1.0_to_1.2.0.sql"
      }
    ]
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Apply Migration

```
POST /databases/{database_name}/schemas/{schema_name}/migrate
```

Applies a schema migration.

**Request:**

```json
{
  "target_version": "1.1.0"
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "previous_version": "1.0.0",
    "current_version": "1.1.0",
    "description": "Add user roles",
    "applied_at": "2023-06-15T12:34:56Z"
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

### Query Execution

#### Execute Query

```
POST /databases/{database_name}/query
```

Executes a SQL query.

**Request:**

```json
{
  "query": "SELECT * FROM git_repos.repositories LIMIT 10",
  "parameters": []
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "columns": ["id", "name", "url", "created_at"],
    "rows": [
      [1, "project-tracker", "**************:example/project-tracker.git", "2023-06-01T10:00:00Z"],
      [2, "database-service", "**************:example/database-service.git", "2023-06-02T11:00:00Z"]
    ],
    "row_count": 2,
    "execution_time_ms": 5
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

#### Execute Parameterized Query

```
POST /databases/{database_name}/query
```

Executes a parameterized SQL query.

**Request:**

```json
{
  "query": "SELECT * FROM git_repos.repositories WHERE name = $1",
  "parameters": ["project-tracker"]
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "columns": ["id", "name", "url", "created_at"],
    "rows": [
      [1, "project-tracker", "**************:example/project-tracker.git", "2023-06-01T10:00:00Z"]
    ],
    "row_count": 1,
    "execution_time_ms": 3
  },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z"
  }
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default Limit**: 100 requests per minute
- **Burst Limit**: 200 requests per minute for short bursts

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1623760496
```

## Pagination

List endpoints support pagination using the following query parameters:

- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 20, max: 100)

Pagination metadata is included in the response:

```json
{
  "status": "success",
  "data": { ... },
  "meta": {
    "timestamp": "2023-06-15T12:34:56Z",
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total_items": 45,
      "total_pages": 3
    }
  }
}
```

## Client Libraries

The Database Service provides client libraries for easy integration:

- **C++**: Native C++23 client library
- **Python**: Python client library
- **JavaScript**: Node.js client library

See the [Client Library Documentation](./client.md) for details.

## Examples

### Creating a Database and Schema

```bash
# Create a database
curl -X POST https://git.chcit.org/api/database/v1/databases \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"name": "new_database", "owner": "postgres"}'

# Create a schema
curl -X POST https://git.chcit.org/api/database/v1/databases/new_database/schemas \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"name": "new_schema", "owner": "postgres"}'
```

### Applying a Schema Migration

```bash
# Apply migration
curl -X POST https://git.chcit.org/api/database/v1/databases/git_dashboard/schemas/git_repos/migrate \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"target_version": "1.1.0"}'
```

### Executing a Query

```bash
# Execute query
curl -X POST https://git.chcit.org/api/database/v1/databases/git_dashboard/query \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"query": "SELECT * FROM git_repos.repositories LIMIT 10", "parameters": []}'
```
