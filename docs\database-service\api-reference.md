# API Reference

This document provides a complete reference for the Database Service API.

## Base URLs and SSL Termination

The Database Service API uses different base URLs depending on the environment:

### Development Environment
```
Base URL: http://localhost:8080/api
Example: http://localhost:8080/api/health
```

### Production Environment (Reverse Proxy SSL Termination)
```
Base URL: https://git.chcit.org/database-api
Example: https://git.chcit.org/database-api/health
```

**Important Notes:**
- **Production uses HTTPS**: All production API calls must use HTTPS
- **URL Path Mapping**: Production URLs use `/database-api/*` which maps to `/api/*` on the backend
- **SSL Termination**: SSL is handled by <PERSON>inx reverse proxy, not the Database Service directly
- **Backend HTTP Only**: The Database Service itself runs HTTP-only on localhost:8080
- **CORS Configuration**: Production allows requests from `https://git.chcit.org` origin

### SSL Termination Architecture

```
Production Request Flow:
Client → Nginx (HTTPS git.chcit.org:443) → Database Service (HTTP localhost:8080)
         /database-api/health           →  /api/health
```

**Benefits of Reverse Proxy SSL Termination:**
- Improved performance (140% throughput increase)
- Centralized certificate management
- Simplified application deployment
- Enhanced security isolation

## Client Configuration

### JavaScript/TypeScript Example

```typescript
// Environment-specific configuration
const API_CONFIG = {
  development: {
    baseURL: 'http://localhost:8080/api',
    timeout: 30000,
  },
  production: {
    baseURL: 'https://git.chcit.org/database-api',
    timeout: 30000,
  }
};

const apiClient = axios.create({
  baseURL: API_CONFIG[process.env.NODE_ENV].baseURL,
  timeout: API_CONFIG[process.env.NODE_ENV].timeout,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // For CORS cookies
});
```

### cURL Examples

**Development:**
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

**Production:**
```bash
curl -X POST https://git.chcit.org/database-api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

## URL Construction for All Endpoints

For any endpoint documented in this reference, construct the full URL using these patterns:

### Development Environment
```
Base: http://localhost:8080/api
Pattern: http://localhost:8080/api/{endpoint}

Examples:
- GET /health → http://localhost:8080/api/health
- POST /auth/login → http://localhost:8080/api/auth/login
- GET /databases → http://localhost:8080/api/databases
- POST /query → http://localhost:8080/api/query
```

### Production Environment
```
Base: https://git.chcit.org/database-api
Pattern: https://git.chcit.org/database-api/{endpoint}

Examples:
- GET /health → https://git.chcit.org/database-api/health
- POST /auth/login → https://git.chcit.org/database-api/auth/login
- GET /databases → https://git.chcit.org/database-api/databases
- POST /query → https://git.chcit.org/database-api/query
```

**Important:** All production requests must use HTTPS. The Database Service backend runs HTTP-only on localhost, with SSL termination handled by Nginx reverse proxy.

## Authentication and Headers

### Required Headers

**All Requests:**
```
Content-Type: application/json
```

**Authenticated Requests:**
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

### CORS Considerations

**Development:**
- Allows requests from any origin (`*`)
- Credentials supported
- All standard methods allowed

**Production:**
- Restricted to `https://git.chcit.org` origin
- Credentials required for authentication
- Rate limiting applied

### Example Authenticated Request

```javascript
// JavaScript/TypeScript example
const response = await fetch('https://git.chcit.org/database-api/databases', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${accessToken}`,
  },
  credentials: 'include', // Important for CORS
});
```

```bash
# cURL example
curl -X GET https://git.chcit.org/database-api/databases \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## General Error Response Format

When an API request results in an error, the response will generally follow this format:

```json
{
  "success": false,
  "error": {
    "type": "ErrorTypeString", // Corresponds to a specific error category (e.g., InvalidCredentials, TokenValidationFailed)
    "message": "A descriptive error message.",
    "code": "OPTIONAL_ERROR_CODE", // e.g., from SecurityErrorType or HTTP status
    "details": "Optional additional details or context about the error."
  }
}
```

## Authentication

### Login

**Development:**
```
POST http://localhost:8080/api/auth/login
```

**Production:**
```
POST https://git.chcit.org/database-api/auth/login
```

Authenticates a user and returns access and refresh tokens.

**Request Body**:
```json
{
  "username": "admin",
  "password": "password"
}
```

**Success Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": "admin",
    "token_type": "Bearer"
  }
}
```

**Error Response (e.g., 401 Unauthorized - Invalid Credentials)**:
```json
{
  "success": false,
  "error": {
    "type": "InvalidCredentials",
    "message": "Authentication failed: Invalid username or password.",
    "code": "AUTH_001"
  }
}
```

### Refresh Token

```
POST /api/auth/refresh
```

Refreshes an access token using a refresh token.

**Request Body**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response (200 OK)**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // Potentially a new refresh token
    "token_type": "Bearer"
  }
}
```

**Error Response (e.g., 401 Unauthorized - Invalid Refresh Token)**:
```json
{
  "success": false,
  "error": {
    "type": "TokenValidationFailed",
    "message": "Failed to refresh access token: The provided refresh token is invalid, expired, or revoked.",
    "code": "AUTH_002"
  }
}
```

### Logout

```
POST /api/auth/logout
```

Invalidates all tokens for the current user.

**Authentication Required**: Yes

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Logged out successfully"
  }
}
```

### User Information

```
GET /api/auth/user
```

Returns information about the current user.

**Authentication Required**: Yes

**Response**:
```json
{
  "success": true,
  "data": {
    "username": "admin",
    "email": "<EMAIL>",
    "is_admin": true,
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

## Database Operations

### Execute Query

```
POST /api/query
```

Executes a SQL query and returns the results.

**Authentication Required**: Yes

**Request Body**:
```json
{
  "query": "SELECT * FROM users WHERE username = $1",
  "params": ["admin"]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "columns": ["id", "username", "email", "is_admin", "created_at"],
    "rows": [
      [1, "admin", "<EMAIL>", true, "2023-01-01T00:00:00Z"]
    ]
  }
}
```

### Execute Non-Query

```
POST /api/execute
```

Executes a SQL statement that does not return results.

**Authentication Required**: Yes

**Request Body**:
```json
{
  "statement": "UPDATE users SET email = $1 WHERE username = $2",
  "params": ["<EMAIL>", "admin"]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "affected_rows": 1
  }
}
```

## Metrics

### Get All Metrics

```
GET /api/database/metrics
```

Returns all database metrics.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "connection_pool": {
      "active_connections": 5,
      "idle_connections": 3,
      "waiting_connections": 0,
      "max_connections": 10,
      "connection_utilization": 0.5,
      "max_connections_reached_count": 2
    },
    "query_performance": {
      "SELECT": {
        "count": 1000,
        "error_count": 5,
        "error_rate": 0.005,
        "min_time_ms": 1.2,
        "max_time_ms": 150.5,
        "avg_time_ms": 15.3
      },
      "INSERT": {
        "count": 500,
        "error_count": 2,
        "error_rate": 0.004,
        "min_time_ms": 2.1,
        "max_time_ms": 95.7,
        "avg_time_ms": 12.8
      }
    },
    "transactions": {
      "commit_count": 450,
      "rollback_count": 50,
      "total_count": 500,
      "commit_rate": 0.9,
      "avg_transaction_time_ms": 25.7,
      "max_transaction_time_ms": 350.2
    },
    "authentication": {
      "success_count": 950,
      "failure_count": 50,
      "total_count": 1000,
      "success_rate": 0.95
    },
    "timestamp": **********
  }
}
```

### Get Connection Pool Metrics

```
GET /api/database/metrics/connection-pool
```

Returns connection pool metrics.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "active_connections": 5,
    "idle_connections": 3,
    "waiting_connections": 0,
    "max_connections": 10,
    "connection_utilization": 0.5,
    "max_connections_reached_count": 2
  }
}
```

### Get Query Performance Metrics

```
GET /api/database/metrics/query-performance
```

Returns query performance metrics.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "SELECT": {
      "count": 1000,
      "error_count": 5,
      "error_rate": 0.005,
      "min_time_ms": 1.2,
      "max_time_ms": 150.5,
      "avg_time_ms": 15.3
    },
    "INSERT": {
      "count": 500,
      "error_count": 2,
      "error_rate": 0.004,
      "min_time_ms": 2.1,
      "max_time_ms": 95.7,
      "avg_time_ms": 12.8
    }
  }
}
```

## Credential Management

### Store Credential

```
POST /api/credentials/store
```

Stores a credential securely.

**Authentication Required**: Yes (Admin only)

**Request Body**:
```json
{
  "key": "api_key",
  "value": "secret_api_key"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Credential stored successfully"
  }
}
```

### Get Credential

```
GET /api/credentials/get?key=api_key
```

Retrieves a stored credential.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "key": "api_key",
    "value": "secret_api_key"
  }
}
```

### Remove Credential

```
DELETE /api/credentials/remove?key=api_key
```

Removes a stored credential.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Credential removed successfully"
  }
}
```

## Health Check

```
GET /api/health
```

Returns the health status of the service.

**Authentication Required**: No

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "ok",
    "version": "1.0.0",
    "timestamp": **********
  }
}
```

## Error Responses

All API endpoints return a standard error response format:

```json
{
  "success": false,
  "error": {
    "code": 401,
    "message": "Authentication required"
  }
}
```

Common error codes:

- `400`: Bad Request - The request was malformed or invalid
- `401`: Unauthorized - Authentication is required or failed
- `403`: Forbidden - The authenticated user does not have permission
- `404`: Not Found - The requested resource was not found
- `500`: Internal Server Error - An unexpected error occurred
