# API Reference

This document provides a complete reference for the Database Service API.

## Authentication

### Login

```
POST /api/auth/login
```

Authenticates a user and returns access and refresh tokens.

**Request Body**:
```json
{
  "username": "admin",
  "password": "password"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": "admin",
    "token_type": "Bearer"
  }
}
```

### Refresh Token

```
POST /api/auth/refresh
```

Refreshes an access token using a refresh token.

**Request Body**:
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer"
  }
}
```

### Logout

```
POST /api/auth/logout
```

Invalidates all tokens for the current user.

**Authentication Required**: Yes

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Logged out successfully"
  }
}
```

### User Information

```
GET /api/auth/user
```

Returns information about the current user.

**Authentication Required**: Yes

**Response**:
```json
{
  "success": true,
  "data": {
    "username": "admin",
    "email": "<EMAIL>",
    "is_admin": true,
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

## Database Operations

### Execute Query

```
POST /api/query
```

Executes a SQL query and returns the results.

**Authentication Required**: Yes

**Request Body**:
```json
{
  "query": "SELECT * FROM users WHERE username = $1",
  "params": ["admin"]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "columns": ["id", "username", "email", "is_admin", "created_at"],
    "rows": [
      [1, "admin", "<EMAIL>", true, "2023-01-01T00:00:00Z"]
    ]
  }
}
```

### Execute Non-Query

```
POST /api/execute
```

Executes a SQL statement that does not return results.

**Authentication Required**: Yes

**Request Body**:
```json
{
  "statement": "UPDATE users SET email = $1 WHERE username = $2",
  "params": ["<EMAIL>", "admin"]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "affected_rows": 1
  }
}
```

## Metrics

### Get All Metrics

```
GET /api/database/metrics
```

Returns all database metrics.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "connection_pool": {
      "active_connections": 5,
      "idle_connections": 3,
      "waiting_connections": 0,
      "max_connections": 10,
      "connection_utilization": 0.5,
      "max_connections_reached_count": 2
    },
    "query_performance": {
      "SELECT": {
        "count": 1000,
        "error_count": 5,
        "error_rate": 0.005,
        "min_time_ms": 1.2,
        "max_time_ms": 150.5,
        "avg_time_ms": 15.3
      },
      "INSERT": {
        "count": 500,
        "error_count": 2,
        "error_rate": 0.004,
        "min_time_ms": 2.1,
        "max_time_ms": 95.7,
        "avg_time_ms": 12.8
      }
    },
    "transactions": {
      "commit_count": 450,
      "rollback_count": 50,
      "total_count": 500,
      "commit_rate": 0.9,
      "avg_transaction_time_ms": 25.7,
      "max_transaction_time_ms": 350.2
    },
    "authentication": {
      "success_count": 950,
      "failure_count": 50,
      "total_count": 1000,
      "success_rate": 0.95
    },
    "timestamp": **********
  }
}
```

### Get Connection Pool Metrics

```
GET /api/database/metrics/connection-pool
```

Returns connection pool metrics.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "active_connections": 5,
    "idle_connections": 3,
    "waiting_connections": 0,
    "max_connections": 10,
    "connection_utilization": 0.5,
    "max_connections_reached_count": 2
  }
}
```

### Get Query Performance Metrics

```
GET /api/database/metrics/query-performance
```

Returns query performance metrics.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "SELECT": {
      "count": 1000,
      "error_count": 5,
      "error_rate": 0.005,
      "min_time_ms": 1.2,
      "max_time_ms": 150.5,
      "avg_time_ms": 15.3
    },
    "INSERT": {
      "count": 500,
      "error_count": 2,
      "error_rate": 0.004,
      "min_time_ms": 2.1,
      "max_time_ms": 95.7,
      "avg_time_ms": 12.8
    }
  }
}
```

## Credential Management

### Store Credential

```
POST /api/credentials/store
```

Stores a credential securely.

**Authentication Required**: Yes (Admin only)

**Request Body**:
```json
{
  "key": "api_key",
  "value": "secret_api_key"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Credential stored successfully"
  }
}
```

### Get Credential

```
GET /api/credentials/get?key=api_key
```

Retrieves a stored credential.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "key": "api_key",
    "value": "secret_api_key"
  }
}
```

### Remove Credential

```
DELETE /api/credentials/remove?key=api_key
```

Removes a stored credential.

**Authentication Required**: Yes (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Credential removed successfully"
  }
}
```

## Health Check

```
GET /api/health
```

Returns the health status of the service.

**Authentication Required**: No

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "ok",
    "version": "1.0.0",
    "timestamp": **********
  }
}
```

## Error Responses

All API endpoints return a standard error response format:

```json
{
  "success": false,
  "error": {
    "code": 401,
    "message": "Authentication required"
  }
}
```

Common error codes:

- `400`: Bad Request - The request was malformed or invalid
- `401`: Unauthorized - Authentication is required or failed
- `403`: Forbidden - The authenticated user does not have permission
- `404`: Not Found - The requested resource was not found
- `500`: Internal Server Error - An unexpected error occurred
