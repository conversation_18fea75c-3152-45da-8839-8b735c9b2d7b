# Configuration Validation

This document describes the configuration options for the Database Service and how they are validated.

## Configuration File

The Database Service uses a configuration file in INI format. The default location is `/opt/database-service/config/database-service.json`.

## Configuration Sections

The configuration file is divided into sections:

- **[general]**: General service settings
- **[database]**: Database connection settings
- **[api]**: API server settings
- **[security]**: Security settings
- **[logging]**: Logging settings
- **[schema]**: Schema management settings

## Configuration Options

### General Settings

| Option | Description | Default | Validation |
|--------|-------------|---------|------------|
| `service_name` | Name of the service | `database-service` | Non-empty string |
| `instance_id` | Unique instance identifier | Generated UUID | Valid UUID |
| `data_dir` | Data directory | `/opt/database-service/data` | Existing directory with write permissions |
| `temp_dir` | Temporary directory | `/tmp` | Existing directory with write permissions |
| `threads` | Number of worker threads | Number of CPU cores | Integer between 1 and 64 |

### Database Connection Settings

| Option | Description | Default | Validation |
|--------|-------------|---------|------------|
| `host` | Database host | `localhost` | Valid hostname or IP address |
| `port` | Database port | `5432` | Integer between 1 and 65535 |
| `user` | Database user | `postgres` | Non-empty string |
| `password` | Database password | None | String |
| `connection_timeout` | Connection timeout in seconds | `30` | Integer between 1 and 300 |
| `idle_timeout` | Idle connection timeout in seconds | `600` | Integer between 60 and 3600 |
| `max_connections` | Maximum number of connections | `10` | Integer between 1 and 100 |
| `ssl_enabled` | Enable SSL | `true` | Boolean |
| `ssl_mode` | SSL mode | `verify-full` | One of: `disable`, `allow`, `prefer`, `require`, `verify-ca`, `verify-full` |
| `ssl_cert_file` | SSL certificate file | `/etc/letsencrypt/live/chcit.org/cert.pem` | Existing file with read permissions |
| `ssl_key_file` | SSL key file | `/etc/letsencrypt/live/chcit.org/privkey.pem` | Existing file with read permissions |
| `ssl_ca_file` | SSL CA file | `/etc/letsencrypt/live/chcit.org/chain.pem` | Existing file with read permissions |

### API Server Settings

| Option | Description | Default | Validation |
|--------|-------------|---------|------------|
| `enabled` | Enable API server | `true` | Boolean |
| `host` | API server host | `127.0.0.1` | Valid hostname or IP address |
| `port` | API server port | `8080` | Integer between 1 and 65535 |
| `workers` | Number of worker threads | `4` | Integer between 1 and 32 |
| `request_timeout` | Request timeout in seconds | `30` | Integer between 1 and 300 |
| `max_request_size` | Maximum request size in bytes | `1048576` (1MB) | Integer between 1024 and 104857600 (100MB) |
| `ssl_enabled` | Enable SSL | `true` | Boolean |
| `ssl_cert_file` | SSL certificate file | `/etc/letsencrypt/live/chcit.org/fullchain.pem` | Existing file with read permissions |
| `ssl_key_file` | SSL key file | `/etc/letsencrypt/live/chcit.org/privkey.pem` | Existing file with read permissions |

### Security Settings

| Option | Description | Default | Validation |
|--------|-------------|---------|------------|
| `api_keys` | Comma-separated list of API keys | None | Non-empty string |
| `jwt_secret` | JWT secret key | None | String with minimum length of 32 characters |
| `jwt_issuer` | JWT issuer | `database-service` | Non-empty string |
| `jwt_audience` | JWT audience | `database-clients` | Non-empty string |
| `jwt_expiration` | JWT expiration in seconds | `3600` (1 hour) | Integer between 60 and 86400 (24 hours) |
| `rate_limit_enabled` | Enable rate limiting | `true` | Boolean |
| `rate_limit_requests` | Rate limit requests per minute | `100` | Integer between 1 and 10000 |
| `rate_limit_burst` | Rate limit burst | `200` | Integer between 1 and 20000 |

### Logging Settings

| Option | Description | Default | Validation |
|--------|-------------|---------|------------|
| `level` | Log level | `info` | One of: `trace`, `debug`, `info`, `warning`, `error`, `critical` |
| `file` | Log file | `/opt/database-service/logs/database-service.log` | Valid file path with write permissions |
| `max_size` | Maximum log file size in bytes | `10485760` (10MB) | Integer between 1048576 (1MB) and 1073741824 (1GB) |
| `max_files` | Maximum number of log files | `10` | Integer between 1 and 100 |
| `console` | Log to console | `false` | Boolean |
| `syslog` | Log to syslog | `true` | Boolean |
| `syslog_facility` | Syslog facility | `local0` | Valid syslog facility |

### Schema Settings

| Option | Description | Default | Validation |
|--------|-------------|---------|------------|
| `schema_dir` | Schema directory | `/opt/database-service/schemas` | Existing directory with read permissions |
| `migration_dir` | Migration directory | `/opt/database-service/migrations` | Existing directory with read permissions |
| `auto_migrate` | Automatically apply migrations | `false` | Boolean |
| `version_table` | Schema version table | `schema_version` | Valid table name |

## Environment Variables

Configuration options can also be set using environment variables. Environment variables take precedence over configuration file values.

Environment variable names are derived from the configuration option names by:
1. Converting to uppercase
2. Replacing dots with underscores
3. Adding the `DBSERVICE_` prefix

For example:
- `database.host` becomes `DBSERVICE_DATABASE_HOST`
- `api.port` becomes `DBSERVICE_API_PORT`
- `logging.level` becomes `DBSERVICE_LOGGING_LEVEL`

## Configuration Validation

The Database Service validates the configuration at startup:

1. **File Existence**: Checks if the configuration file exists
2. **File Format**: Validates the INI format
3. **Required Options**: Checks for required options
4. **Option Types**: Validates option types (string, integer, boolean, etc.)
5. **Option Values**: Validates option values against constraints
6. **File Permissions**: Checks file and directory permissions
7. **Consistency**: Validates consistency between related options

If validation fails, the service logs detailed error messages and exits.

## Configuration Examples

### Minimal Configuration

```ini
[database]
host = localhost
port = 5432
user = postgres
password = secret

[security]
api_keys = api-key-1,api-key-2
```

### Complete Configuration

```ini
[general]
service_name = database-service
instance_id = 550e8400-e29b-41d4-a716-446655440000
data_dir = /opt/database-service/data
temp_dir = /tmp
threads = 8

[database]
host = localhost
port = 5432
user = postgres
password = secret
connection_timeout = 30
idle_timeout = 600
max_connections = 10
ssl_enabled = true
ssl_mode = verify-full
ssl_cert_file = /etc/letsencrypt/live/chcit.org/cert.pem
ssl_key_file = /etc/letsencrypt/live/chcit.org/privkey.pem
ssl_ca_file = /etc/letsencrypt/live/chcit.org/chain.pem

[api]
enabled = true
host = 127.0.0.1
port = 8080
workers = 4
request_timeout = 30
max_request_size = 1048576
ssl_enabled = true
ssl_cert_file = /etc/letsencrypt/live/chcit.org/fullchain.pem
ssl_key_file = /etc/letsencrypt/live/chcit.org/privkey.pem

[security]
api_keys = api-key-1,api-key-2
jwt_secret = very-long-and-secure-jwt-secret-key-at-least-32-chars
jwt_issuer = database-service
jwt_audience = database-clients
jwt_expiration = 3600
rate_limit_enabled = true
rate_limit_requests = 100
rate_limit_burst = 200

[logging]
level = info
file = /opt/database-service/logs/database-service.log
max_size = 10485760
max_files = 10
console = false
syslog = true
syslog_facility = local0

[schema]
schema_dir = /opt/database-service/schemas
migration_dir = /opt/database-service/migrations
auto_migrate = false
version_table = schema_version
```

## Configuration for Different Environments

### Development Environment

```ini
[general]
service_name = database-service-dev

[database]
host = localhost
port = 5432
user = postgres
password = dev-password
ssl_enabled = false

[api]
host = 127.0.0.1
port = 8080
ssl_enabled = false

[security]
api_keys = dev-api-key
rate_limit_enabled = false

[logging]
level = debug
console = true
syslog = false

[schema]
auto_migrate = true
```

### Production Environment

```ini
[general]
service_name = database-service-prod

[database]
host = postgres.internal
port = 5432
user = postgres
password = prod-password
ssl_enabled = true
ssl_mode = verify-full
ssl_cert_file = /etc/letsencrypt/live/chcit.org/cert.pem
ssl_key_file = /etc/letsencrypt/live/chcit.org/privkey.pem
ssl_ca_file = /etc/letsencrypt/live/chcit.org/chain.pem

[api]
host = 127.0.0.1
port = 8080
ssl_enabled = true
ssl_cert_file = /etc/letsencrypt/live/chcit.org/fullchain.pem
ssl_key_file = /etc/letsencrypt/live/chcit.org/privkey.pem

[security]
api_keys = prod-api-key-1,prod-api-key-2
rate_limit_enabled = true
rate_limit_requests = 100
rate_limit_burst = 200

[logging]
level = info
console = false
syslog = true

[schema]
auto_migrate = false
```

## Configuration Validation Code

The Database Service uses a configuration validation module to validate the configuration:

```cpp
import dbservice.config;
import std.core;

int main(int argc, char** argv) {
    // Parse command line arguments
    auto args = dbservice::config::parseCommandLine(argc, argv);
    
    // Load configuration
    auto config = dbservice::config::ConfigManager::load(args.configFile);
    
    // Validate configuration
    auto validationResult = config->validate();
    
    if (!validationResult.isValid) {
        std::cerr << "Configuration validation failed:" << std::endl;
        for (const auto& error : validationResult.errors) {
            std::cerr << "- " << error << std::endl;
        }
        return 1;
    }
    
    // Configuration is valid, start the service
    // ...
    
    return 0;
}
```

## Related Documentation

- [Installation Guide](./installation.md)
- [Certificate Access](./certificate-access.md)
- [Architecture and Design](./architecture-and-design.md)
