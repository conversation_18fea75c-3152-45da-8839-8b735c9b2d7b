#pragma once
#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <chrono>

namespace dbservice {

namespace core {
    class ConnectionManager;
}

namespace security {

/**
 * @struct TokenPair
 * @brief Access and refresh token pair
 */
struct TokenPair {
    std::string accessToken;
    std::string refreshToken;
};

/**
 * @class SecurityManager
 * @brief Manages security for the database service
 */
class SecurityManager {
public:
    /**
     * @brief Constructor
     * @param connectionManager Connection manager
     */
    SecurityManager(std::shared_ptr<core::ConnectionManager> connectionManager);

    /**
     * @brief Destructor
     */
    ~SecurityManager();

    /**
     * @brief Initialize the security manager
     * @return True if initialization was successful
     */
    bool initialize();

    /**
     * @brief Authenticate a user
     * @param username Username
     * @param password Password
     * @return Authentication token pair if successful, empty tokens otherwise
     */
    TokenPair authenticate(const std::string& username, const std::string& password);

    /**
     * @brief Validate an authentication token
     * @param token Authentication token
     * @return True if token is valid
     */
    bool validateToken(const std::string& token);

    /**
     * @brief Refresh an access token using a refresh token
     * @param refreshToken Refresh token
     * @return New token pair if successful, empty tokens otherwise
     */
    TokenPair refreshAccessToken(const std::string& refreshToken);

    /**
     * @brief Invalidate all tokens for a user
     * @param username Username
     * @return True if tokens were invalidated successfully
     */
    bool invalidateTokens(const std::string& username);

    /**
     * @brief Get user information from a token
     * @param token Authentication token
     * @return User information
     */
    std::unordered_map<std::string, std::string> getUserInfo(const std::string& token);

    /**
     * @brief Check if a user has a permission
     * @param username Username
     * @param permission Permission to check
     * @return True if user has permission
     */
    bool hasPermission(const std::string& username, const std::string& permission);

    /**
     * @brief Grant a permission to a user
     * @param username Username
     * @param permission Permission to grant
     * @return True if permission was granted successfully
     */
    bool grantPermission(const std::string& username, const std::string& permission);

    /**
     * @brief Revoke a permission from a user
     * @param username Username
     * @param permission Permission to revoke
     * @return True if permission was revoked successfully
     */
    bool revokePermission(const std::string& username, const std::string& permission);

    /**
     * @brief Create a user
     * @param username Username
     * @param password Password
     * @param isAdmin Whether the user is an admin
     * @return True if user was created successfully
     */
    bool createUser(const std::string& username, const std::string& password, bool isAdmin = false);

    /**
     * @brief Delete a user
     * @param username Username
     * @return True if user was deleted successfully
     */
    bool deleteUser(const std::string& username);

    /**
     * @brief Set the JWT secret
     * @param secret JWT secret
     */
    void setJwtSecret(const std::string& secret);

    /**
     * @brief Set token expiration times
     * @param accessTokenExpirationSeconds Access token expiration time in seconds
     * @param refreshTokenExpirationSeconds Refresh token expiration time in seconds
     */
    void setTokenExpirationTimes(int accessTokenExpirationSeconds, int refreshTokenExpirationSeconds);

private:
    /**
     * @brief Create the users table
     * @return True if table was created successfully
     */
    bool createUsersTable();

    /**
     * @brief Create the permissions table
     * @return True if table was created successfully
     */
    bool createPermissionsTable();

    /**
     * @brief Create the refresh tokens table
     * @return True if table was created successfully
     */
    bool createRefreshTokensTable();

    /**
     * @brief Hash a password
     * @param password Password to hash
     * @return Hashed password
     */
    std::string hashPassword(const std::string& password);

    /**
     * @brief Verify a password
     * @param password Password to verify
     * @param hash Hash to verify against
     * @return True if password matches hash
     */
    bool verifyPassword(const std::string& password, const std::string& hash);

    /**
     * @brief Generate a token pair
     * @param username Username
     * @return Token pair
     */
    TokenPair generateTokenPair(const std::string& username);

    /**
     * @brief Store a refresh token
     * @param username Username
     * @param refreshToken Refresh token
     * @param expiresAt Expiration time
     * @return True if token was stored successfully
     */
    bool storeRefreshToken(const std::string& username, const std::string& refreshToken,
                          const std::chrono::system_clock::time_point& expiresAt);

    /**
     * @brief Validate a refresh token
     * @param refreshToken Refresh token
     * @param username Output parameter for username
     * @return True if token is valid
     */
    bool validateRefreshToken(const std::string& refreshToken, std::string& username);

    /**
     * @brief Delete expired refresh tokens
     */
    void cleanupExpiredRefreshTokens();

    std::shared_ptr<core::ConnectionManager> connectionManager_;
    bool initialized_;
    std::mutex mutex_;
    std::string jwtSecret_;
    int accessTokenExpirationSeconds_;
    int refreshTokenExpirationSeconds_;
};

} // namespace security
} // namespace dbservice
