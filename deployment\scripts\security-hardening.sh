#!/bin/bash

# Exit on any error
set -e

# Default values
CERT_DIR="/etc/project-tracker/certs"
SSL_CONF="/etc/nginx/conf.d/ssl.conf"
DHPARAM_FILE="/etc/nginx/dhparam.pem"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}Please run as root${NC}"
    exit 1
fi

# Function to log messages
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --email)
            EMAIL="$2"
            shift 2
            ;;
        *)
            echo "Unknown parameter: $1"
            exit 1
            ;;
    esac
done

# Verify required parameters
if [ -z "$DOMAIN" ] || [ -z "$EMAIL" ]; then
    echo "Usage: $0 --domain yourdomain.com --email <EMAIL>"
    exit 1
fi

# Install required packages
log "Installing security packages..."
apt-get update
apt-get install -y \
    certbot \
    python3-certbot-nginx \
    libpam-google-authenticator \
    rkhunter \
    auditd \
    lynis

# Configure SSL/TLS
log "Configuring SSL/TLS..."
mkdir -p $CERT_DIR

# Generate strong DH parameters
log "Generating DH parameters (this may take a while)..."
openssl dhparam -out $DHPARAM_FILE 2048

# Configure SSL settings
cat > $SSL_CONF << EOF
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_dhparam $DHPARAM_FILE;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:50m;
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;
add_header Strict-Transport-Security "max-age=63072000" always;
EOF

# Obtain SSL certificate
log "Obtaining SSL certificate..."
certbot --nginx \
    --non-interactive \
    --agree-tos \
    --email $EMAIL \
    --domains $DOMAIN \
    --redirect

# Configure security headers
log "Configuring security headers..."
cat > /etc/nginx/conf.d/security-headers.conf << EOF
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;
EOF

# Configure ModSecurity WAF
log "Installing and configuring ModSecurity..."
apt-get install -y \
    libmodsecurity3 \
    libapache2-mod-security2 \
    modsecurity-crs

# Enable OWASP ModSecurity Core Rule Set
ln -s /usr/share/modsecurity-crs/rules/* /etc/nginx/modsec/

# Configure audit logging
log "Configuring audit logging..."
cat > /etc/audit/rules.d/project-tracker.rules << EOF
# Log file changes
-w /etc/project-tracker/ -p wa -k project_tracker_config
-w /var/log/project-tracker/ -p wa -k project_tracker_logs

# Log authentication events
-w /var/log/auth.log -p wa -k auth_log
-w /var/log/syslog -p wa -k syslog

# Log user and group modifications
-w /etc/group -p wa -k group_modification
-w /etc/passwd -p wa -k passwd_modification
-w /etc/shadow -p wa -k shadow_modification

# Monitor specific commands
-w /usr/bin/curl -p x -k curl_execution
-w /usr/bin/wget -p x -k wget_execution
-w /usr/bin/ssh -p x -k ssh_execution
EOF

# Reload audit rules
auditctl -R /etc/audit/rules.d/project-tracker.rules

# Configure fail2ban for additional services
log "Enhancing fail2ban configuration..."
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2
EOF

# Configure SSH hardening
log "Hardening SSH configuration..."
cat > /etc/ssh/sshd_config.d/hardening.conf << EOF
Protocol 2
PermitRootLogin no
PasswordAuthentication no
PermitEmptyPasswords no
X11Forwarding no
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
AllowUsers www-data
EOF

# Configure system hardening
log "Configuring system hardening..."
cat >> /etc/sysctl.conf << EOF
# Kernel hardening
kernel.randomize_va_space = 2
kernel.kptr_restrict = 2
kernel.sysrq = 0
kernel.core_uses_pid = 1
kernel.dmesg_restrict = 1

# Network security
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.icmp_echo_ignore_all = 1
EOF

sysctl -p

# Set up automated security scans
log "Setting up automated security scans..."
cat > /usr/local/bin/security-scan << EOF
#!/bin/bash
DATE=\$(date +%Y-%m-%d)
REPORT_DIR="/var/log/project-tracker/security-scans/\$DATE"

mkdir -p \$REPORT_DIR

# Run rkhunter
rkhunter --check --skip-keypress --report-warnings-only > \$REPORT_DIR/rkhunter.log

# Run Lynis
lynis audit system > \$REPORT_DIR/lynis.log

# Check SSL certificate
certbot certificates > \$REPORT_DIR/ssl-status.log

# Check fail2ban status
fail2ban-client status > \$REPORT_DIR/fail2ban-status.log

# Cleanup old reports (keep last 30 days)
find /var/log/project-tracker/security-scans/ -type d -mtime +30 -exec rm -rf {} +
EOF

chmod +x /usr/local/bin/security-scan

# Add security scan to cron
echo "0 3 * * * root /usr/local/bin/security-scan" > /etc/cron.d/security-scan

# Restart services
log "Restarting services..."
systemctl restart ssh fail2ban nginx auditd

log "Security hardening complete!"
log "Regular security scans will run daily at 3 AM"
log "Security reports are stored in /var/log/project-tracker/security-scans/"
