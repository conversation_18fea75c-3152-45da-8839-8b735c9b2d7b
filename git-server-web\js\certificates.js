// Certificate Management Module
const CertificateManager = {
    /**
     * Fetch certificate status from API
     * @returns {Promise<Object>} Certificate status data
     */
    async getCertificateStatus() {
        try {
            // Show loading state
            const loadingEl = document.getElementById('cert-status-loading');
            if (loadingEl) {
                loadingEl.style.display = 'flex';
            }

            const response = await fetch('/api/certificates/status');
            if (!response.ok) throw new Error('Failed to fetch certificate status');
            return await response.json();
        } catch (error) {
            console.error('Error fetching certificate status:', error);
            // Use the global logger if available
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Failed to fetch certificate status: ${error.message}`, 'certificate');
            }
            throw error;
        } finally {
            // Hide loading state
            const loadingEl = document.getElementById('cert-status-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }
        }
    },

    /**
     * Sync certificates from main server
     * @returns {Promise<void>}
     */
    async syncCertificate() {
        try {
            // Get the button and clear any existing status message
            const button = document.getElementById('sync-certificate-btn');
            this.clearStatusMessage();

            if (!button) {
                console.error('Sync button not found');
                return;
            }

            // Disable button and show loading state
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Syncing...';

            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', 'Starting certificate sync process...', 'certificate');
            }

            // Add timestamp to prevent caching
            const timestamp = new Date().getTime();
            const response = await fetch(`/api/certificates/sync?t=${timestamp}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                // Add credentials to ensure cookies are sent
                credentials: 'same-origin'
            });

            // Parse response
            let result;
            try {
                result = await response.json();
            } catch (e) {
                // If response isn't valid JSON, create an error object
                result = {
                    status: 'error',
                    message: 'Invalid response from server'
                };
            }

            if (!response.ok) {
                throw new Error(result.message || `Sync failed with status: ${response.status}`);
            }

            // Update the current time as the new "Last Sync" time
            const currentTime = new Date().toLocaleString();

            // Update entire certificate UI to refresh all details
            await this.updateCertificateUI();

            // Show success message inline
            this.showStatusMessage('success', 'Certificates synced successfully.');

            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('success', 'Certificate sync completed successfully', 'certificate');
            }

        } catch (error) {
            console.error('Certificate sync error:', error);
            // Show error message inline
            this.showStatusMessage('danger', `Error: ${error.message}`);

            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Certificate sync failed: ${error.message}`, 'certificate');
            }
        } finally {
            // Re-enable button
            const button = document.getElementById('sync-certificate-btn');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync-alt me-1"></i> Manual Sync';
            }
        }
    },

    /**
     * Show status message below the refresh button
     * @param {string} type - Message type (success, danger)
     * @param {string} message - Message to display
     */
    showStatusMessage(type, message) {
        // Clear any existing status message
        this.clearStatusMessage();

        // Find the status message container
        const container = document.getElementById('status-message-container');
        if (!container) return;

        // Create status message element
        const statusMessage = document.createElement('div');
        statusMessage.id = 'cert-sync-status-message';
        statusMessage.className = `text-${type}`;
        statusMessage.innerHTML = message;

        // Add message to container
        container.appendChild(statusMessage);

        // Set timeout to remove the message after 5 seconds
        setTimeout(() => {
            this.clearStatusMessage();
        }, 5000);
    },

    /**
     * Clear status message
     */
    clearStatusMessage() {
        const statusMessage = document.getElementById('cert-sync-status-message');
        if (statusMessage) {
            statusMessage.remove();
        }
    },

    /**
     * Format certificate details for display
     * @param {Object} status - Certificate status data
     * @returns {string} Formatted HTML for certificate details
     */
	formatCertificateDetails(status) {
    if (!status) return '<div class="alert alert-warning">No certificate information available</div>';

    // Format dates for display
    const startDate = status.start_date ? new Date(status.start_date) : null;
    const expiryDate = status.expiry_date ? new Date(status.expiry_date) : null;

    const formattedStartDate = startDate ? startDate.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }) : 'Unknown';

    const formattedExpiryDate = expiryDate ? expiryDate.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }) : 'Unknown';

    // Create a more comprehensive details view
    let detailsHtml = `
        <div class="card h-100 position-relative" ${status.cert_info ? 'data-has-cert-info="true"' : ''}>
            ${status.cert_info ? `
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Comprehensive Certificate Details</h6>
                <div class="d-flex align-items-center">
                    <span class="text-muted me-2 small">Raw Certificate Data</span>
                    <button class="btn btn-link position-relative p-0 raw-cert-btn" id="view-raw-cert-btn" style="z-index: 10;">
                        <i class="fas fa-ellipsis-v text-muted"></i>
                    </button>
                </div>
            </div>
            <div class="modal fade" id="raw-cert-modal" tabindex="-1" aria-labelledby="rawCertModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="rawCertModalLabel">Raw Certificate Data</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <pre class="bg-light p-3 rounded small" style="white-space: pre-wrap; word-break: break-all;">${status.cert_info || 'No raw certificate data available'}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
            ` : `
            <div class="card-header bg-light">
                <h6 class="mb-0">Comprehensive Certificate Details</h6>
            </div>
            `}
            <div class="card-body p-0 flex-grow-1">
                <div class="row g-0 h-100">
                    <div class="col-md-4 border-end d-flex flex-column">
                        <div class="p-3 flex-grow-1">
                            <h6 class="text-muted border-bottom pb-2 mb-3">Certificate Lifecycle</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="text-muted fw-bold">Valid From</td>
                                    <td>${formattedStartDate}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Valid Until</td>
                                    <td>${formattedExpiryDate}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Days Remaining</td>
                                    <td>
                                        <span class="badge ${
                                            status.days_until_expiry <= 7 ? 'bg-danger' :
                                            status.days_until_expiry <= 30 ? 'bg-warning' : 'bg-success'
                                        }">
                                            ${status.days_until_expiry} days
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Days Since Issued</td>
                                    <td>
                                        ${Math.floor((new Date() - startDate) / (1000 * 60 * 60 * 24))} days
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4 border-end d-flex flex-column">
                        <div class="p-3 flex-grow-1">
                            <h6 class="text-muted border-bottom pb-2 mb-3">Certificate Details</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="text-muted fw-bold">Algorithm</td>
                                    <td>${status.algorithm || 'Unknown'}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Self-Signed</td>
                                    <td>${status.is_self_signed ?
                                        '<span class="badge bg-warning">Yes</span>' :
                                        '<span class="badge bg-success">No</span>'}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Last Synced</td>
                                    <td>${status.last_sync || 'Unknown'}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Signature Type</td>
                                    <td>${status.signature_type || 'Unknown'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4 d-flex flex-column">
                        <div class="p-3 flex-grow-1">
                            <h6 class="text-muted border-bottom pb-2 mb-3">Certificate Entities</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="text-muted fw-bold">Issuer</td>
                                    <td class="text-break small">${status.issuer || 'Unknown'}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Subject</td>
                                    <td class="text-break small">${status.subject || 'Unknown'}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Domains</td>
                                    <td>
                                        ${status.domains && status.domains.length > 0 ?
                                            status.domains.map(domain =>
                                                `<span class="badge bg-primary me-1">${domain}</span>`
                                            ).join('') :
                                            '<span class="badge bg-secondary">No domains</span>'}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted fw-bold">Key Usage</td>
                                    <td class="text-break small">${status.key_usage || 'Unknown'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    return detailsHtml;
},

    /**
     * Update certificate UI with status information and details directly in the tab
     * @returns {Promise<void>}
     */
    async updateCertificateUI() {
        console.log('Updating certificate UI');
        const certificateContainer = document.getElementById('certStatus');
        if (!certificateContainer) {
            console.error('Certificate container not found (certStatus)');
            return;
        }

        // Add full-height class to the container
        certificateContainer.className = 'w-100 h-100';

        // Show loading state
        certificateContainer.innerHTML = `
            <div class="card h-100 shadow-sm w-100">
                <div class="card-body d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        `;

        try {
            const status = await this.getCertificateStatus();

            // Determine status color and icon based on days until expiry
            let statusColor = 'success';
            let statusIcon = 'check-circle';
            let statusText = 'Valid';

            if (status.days_until_expiry <= 0) {
                statusColor = 'danger';
                statusIcon = 'exclamation-circle';
                statusText = 'Expired';
            } else if (status.days_until_expiry <= 7) {
                statusColor = 'danger';
                statusIcon = 'exclamation-circle';
                statusText = 'Critical';
            } else if (status.days_until_expiry <= 30) {
                statusColor = 'warning';
                statusIcon = 'exclamation-triangle';
                statusText = 'Expiring Soon';
            }

            // Format expiry date
            const expiryDate = new Date(status.expiry_date);
            const formattedExpiryDate = expiryDate.toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // Get certificate details HTML
            const detailsHtml = this.formatCertificateDetails(status);

            // Create integrated UI with summary and details
            const statusHtml = `
                <div class="certificate-container">
                    <!-- Summary Card -->
                    <div class="card mb-2 border-${statusColor} shadow-sm w-100">
                        <div class="card-body py-2">
                            <!-- Header with title and status -->
                            <div class="cert-header">
                                <div class="d-flex align-items-center">
                                    <div class="bg-${statusColor} p-2 rounded-circle text-white me-3">
                                        <i class="fas fa-${statusIcon}"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">SSL Certificate</h5>
                                        <span class="badge bg-${statusColor}">${statusText}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Centered expiry info -->
                            <div class="cert-expiry text-center my-2">
                                <p class="card-text mb-1 smaller-text">
                                    <strong>Expires:</strong> ${formattedExpiryDate} (${status.days_until_expiry} days)
                                </p>
                                <p class="card-text mb-0 smaller-text">
									<strong>Last Sync:</strong> ${status.last_sync || 'Unknown'}
                                </p>
                            </div>

                            <!-- Right-aligned button -->
                            <div class="cert-actions text-end">
                                <div>
                                    <button class="btn btn-primary btn-sm" id="sync-certificate-btn">
                                        <i class="fas fa-sync-alt me-1"></i> Manual Sync
                                    </button>
                                </div>
                                <div class="mt-1" id="status-message-container">
                                    <!-- Status message will be added here dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Certificate Details Section -->
                    <div class="certificate-details">
                        ${detailsHtml}
                    </div>
                </div>
            `;

            // Update the container
            certificateContainer.innerHTML = statusHtml;

            // Add event listener for sync button
            const syncButton = document.getElementById('sync-certificate-btn');
            if (syncButton) {
                syncButton.addEventListener('click', () => this.syncCertificate());
            }

            // Add event listener for raw certificate data button
            const rawCertBtn = document.getElementById('view-raw-cert-btn');
            if (rawCertBtn && status.cert_info) {
                rawCertBtn.addEventListener('click', () => {
                    // Check if modal already exists
                    let modal = bootstrap.Modal.getInstance(document.getElementById('raw-cert-modal'));

                    // If not, create a new modal
                    if (!modal) {
                        modal = new bootstrap.Modal(document.getElementById('raw-cert-modal'));
                    }

                    // Show the modal
                    modal.show();
                });
            }

            // Log success
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', 'Certificate status updated', 'certificate');
            }

        } catch (error) {
            console.error('Error updating certificate UI:', error);

            // Show error state with both Retry and Manual Sync buttons
            certificateContainer.innerHTML = `
                <div class="card h-100 border-danger shadow-sm w-100">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-danger p-3 rounded-circle text-white me-3">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-0">SSL Certificate</h5>
                                <span class="badge bg-danger">Error</span>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <p class="card-text text-danger">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Failed to load certificate status: ${error.message}
                            </p>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-1"></i>
                                If certificates are missing, you can use the Manual Sync button to restore them.
                            </div>
                        </div>
                        <div class="mt-auto d-flex justify-content-between">
                            <button class="btn btn-primary btn-sm" id="retry-certificate-btn">
                                <i class="fas fa-sync-alt me-1"></i> Retry
                            </button>
                            <button class="btn btn-success btn-sm" id="sync-certificate-btn">
                                <i class="fas fa-sync-alt me-1"></i> Manual Sync
                            </button>
                            <div id="status-message-container" class="ms-2">
                                <!-- Status message will be added here dynamically -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add event listener for retry button
            const retryButton = document.getElementById('retry-certificate-btn');
            if (retryButton) {
                retryButton.addEventListener('click', () => this.updateCertificateUI());
            }

            // Add event listener for sync button
            const syncButton = document.getElementById('sync-certificate-btn');
            if (syncButton) {
                syncButton.addEventListener('click', () => this.syncCertificate());
            }

            // Log error
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Failed to update certificate status: ${error.message}`, 'certificate');
            }
        }
    },

    /**
     * Create a simplified certificate overview for the dashboard
     * @returns {Promise<void>}
     */
    async updateCertificateOverview() {
        console.log('Updating certificate overview');
        const overviewContainer = document.getElementById('cert-overview');
        if (!overviewContainer) {
            console.error('Certificate overview container not found');
            return;
        }

        try {
            // Fetch certificate status
            const status = await this.getCertificateStatus();

            if (!status || status.status === 'error') {
                overviewContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${status?.message || 'Failed to load certificate information'}
                        </div>
                    </div>
                    <div class="col-12 mt-2 text-center">
                        <button class="btn btn-sm btn-success" id="overview-sync-cert-btn">
                            <i class="fas fa-sync-alt me-1"></i> Sync Certificates
                        </button>
                    </div>
                `;

                // Add event listener for sync button in overview
                const overviewSyncButton = document.getElementById('overview-sync-cert-btn');
                if (overviewSyncButton) {
                    overviewSyncButton.addEventListener('click', () => this.syncCertificate());
                }
                return;
            }

            // Format dates for display
            const expiryDate = status.expiry_date ? new Date(status.expiry_date) : null;
            const formattedExpiryDate = expiryDate ? expiryDate.toLocaleDateString() : 'Unknown';

            // Determine certificate status class
            let statusClass = 'bg-success';
            let statusIcon = 'check-circle';
            let statusText = 'Valid';

            if (status.days_until_expiry < 30) {
                statusClass = 'bg-warning';
                statusIcon = 'exclamation-circle';
                statusText = 'Expiring Soon';
            }

            if (status.days_until_expiry < 7) {
                statusClass = 'bg-danger';
                statusIcon = 'exclamation-triangle';
                statusText = 'Critical';
            }

            // Create overview content
            overviewContainer.innerHTML = `
                <div class="col-12 mb-3 text-center">
                    <span class="badge ${statusClass} p-2">
                        <i class="fas fa-${statusIcon} me-1"></i> ${statusText}
                    </span>
                </div>
                <div class="col-6 mb-3">
                    <div class="metric-value">${status.days_until_expiry}</div>
                    <div class="metric-label">Days Until Expiry</div>
                </div>
                <div class="col-6 mb-3">
                    <div class="metric-value">${status.is_self_signed ? 'Yes' : 'No'}</div>
                    <div class="metric-label">Self-Signed</div>
                </div>
                <div class="col-12 mb-2">
                    <div class="small text-muted">Expires: ${formattedExpiryDate}</div>
                </div>
                <div class="col-12">
                    <div class="small text-muted text-truncate" title="${status.subject || 'Unknown'}">
                        ${status.subject ? status.subject.substring(0, 30) + '...' : 'Unknown'}
                    </div>
                </div>
            `;

            // Update status indicator
            const statusIndicator = document.getElementById('cert-overview-status-indicator');
            if (statusIndicator) {
                statusIndicator.classList.remove('loading');
                statusIndicator.classList.add('success');
            }

        } catch (error) {
            console.error('Error updating certificate overview:', error);
            overviewContainer.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger mb-0">
                        <i class="fas fa-times-circle me-2"></i>
                        Failed to load certificate information: ${error.message}
                    </div>
                </div>
                <div class="col-12 mt-2 text-center">
                    <button class="btn btn-sm btn-success" id="overview-sync-cert-btn">
                        <i class="fas fa-sync-alt me-1"></i> Sync Certificates
                    </button>
                </div>
            `;

            // Add event listener for sync button in overview
            const overviewSyncButton = document.getElementById('overview-sync-cert-btn');
            if (overviewSyncButton) {
                overviewSyncButton.addEventListener('click', () => this.syncCertificate());
            }

            // Update status indicator
            const statusIndicator = document.getElementById('cert-overview-status-indicator');
            if (statusIndicator) {
                statusIndicator.classList.remove('loading');
                statusIndicator.classList.add('error');
            }
        }
    },

    /**
     * Initialize the certificate module
     */
    init() {
        console.log('Initializing certificate module');

        // Update certificate UI
        this.updateCertificateUI();

        // Update certificate overview in the dashboard
        this.updateCertificateOverview();

        // Log initialization
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Certificate module initialized', 'certificate');
        }

        // Add to the main dashboard refresh
        if (window.Dashboard && window.Dashboard.refreshDashboard) {
            const originalRefresh = window.Dashboard.refreshDashboard;
            window.Dashboard.refreshDashboard = async function() {
                await originalRefresh.call(window.Dashboard);
                await CertificateManager.updateCertificateUI();
                await CertificateManager.updateCertificateOverview();
            };
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    CertificateManager.init();
});