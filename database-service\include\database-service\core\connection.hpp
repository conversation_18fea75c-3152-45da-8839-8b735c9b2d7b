#pragma once
#include <string>
#include <vector>
#include <memory>
#include <functional>

namespace dbservice::core {

/**
 * @class Connection
 * @brief Database connection
 */
class Connection : public std::enable_shared_from_this<Connection> {
public:
    /**
     * @brief Constructor
     * @param connectionString Database connection string
     * @param useSSL Whether to use SSL
     */
    Connection(const std::string& connectionString, bool useSSL);

    /**
     * @brief Destructor
     */
    ~Connection();

    /**
     * @brief Open the connection
     * @return True if connection was opened successfully
     */
    bool open();

    /**
     * @brief Close the connection
     */
    void close();

    /**
     * @brief Check if the connection is open
     * @return True if the connection is open
     */
    bool isOpen() const;

    /**
     * @brief Execute a query
     * @param query Query to execute
     * @param params Query parameters
     * @return Query result
     */
    std::vector<std::vector<std::string>> executeQuery(const std::string& query, const std::vector<std::string>& params = {});

    /**
     * @brief Execute a query with a callback
     * @param query Query to execute
     * @param callback Callback to execute for each row
     * @param params Query parameters
     */
    void executeQueryWithCallback(const std::string& query, std::function<void(const std::vector<std::string>&)> callback, const std::vector<std::string>& params = {});

    /**
     * @brief Execute a non-query statement
     * @param statement Statement to execute
     * @param params Statement parameters
     * @return Number of rows affected
     */
    int executeNonQuery(const std::string& statement, const std::vector<std::string>& params = {});

    /**
     * @brief Begin a transaction
     * @return Transaction object
     */
    std::shared_ptr<class Transaction> beginTransaction();

private:
    /**
     * @brief Prepare a statement
     * @param statement Statement to prepare
     * @param params Statement parameters
     * @return Prepared statement
     */
    void* prepareStatement(const std::string& statement, const std::vector<std::string>& params);

    std::string connectionString_;
    bool useSSL_;
    void* connection_; // PGconn*
    bool isOpen_;
};

} // namespace dbservice::core
