#pragma once
#include <string>
#include <iostream>
#include <fstream>
#include <mutex>
#include <chrono>
#include <ctime>
#include <iomanip>

enum class LogLevel {
    DEBUG,
    INFO,
    WARNING,
    ERROR,
    CRITICAL
};

class Logger {
public:
    static Logger& getInstance() {
        static Logger instance;
        return instance;
    }
    
    void setLogFile(const std::string& logFile) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (logStream_.is_open()) {
            logStream_.close();
        }
        logStream_.open(logFile, std::ios::app);
    }
    
    void setLogLevel(LogLevel level) {
        std::lock_guard<std::mutex> lock(mutex_);
        logLevel_ = level;
    }
    
    void debug(const std::string& message) {
        log(LogLevel::DEBUG, message);
    }
    
    void info(const std::string& message) {
        log(LogLevel::INFO, message);
    }
    
    void warning(const std::string& message) {
        log(LogLevel::WARNING, message);
    }
    
    void error(const std::string& message) {
        log(LogLevel::ERROR, message);
    }
    
    void critical(const std::string& message) {
        log(LogLevel::CRITICAL, message);
    }
    
private:
    Logger() : logLevel_(LogLevel::INFO) {}
    ~Logger() {
        if (logStream_.is_open()) {
            logStream_.close();
        }
    }
    
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    void log(LogLevel level, const std::string& message) {
        if (level < logLevel_) {
            return;
        }
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Get current time
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        auto now_tm = std::localtime(&now_time_t);
        
        std::ostringstream timestamp;
        timestamp << std::put_time(now_tm, "%Y-%m-%d %H:%M:%S");
        
        // Format log message
        std::string levelStr;
        switch (level) {
            case LogLevel::DEBUG: levelStr = "DEBUG"; break;
            case LogLevel::INFO: levelStr = "INFO"; break;
            case LogLevel::WARNING: levelStr = "WARNING"; break;
            case LogLevel::ERROR: levelStr = "ERROR"; break;
            case LogLevel::CRITICAL: levelStr = "CRITICAL"; break;
        }
        
        std::string logMessage = timestamp.str() + " [" + levelStr + "] " + message;
        
        // Write to console
        std::cout << logMessage << std::endl;
        
        // Write to file if available
        if (logStream_.is_open()) {
            logStream_ << logMessage << std::endl;
            logStream_.flush();
        }
    }
    
    std::mutex mutex_;
    std::ofstream logStream_;
    LogLevel logLevel_;
};

#define LOG_DEBUG(message) Logger::getInstance().debug(message)
#define LOG_INFO(message) Logger::getInstance().info(message)
#define LOG_WARNING(message) Logger::getInstance().warning(message)
#define LOG_ERROR(message) Logger::getInstance().error(message)
#define LOG_CRITICAL(message) Logger::getInstance().critical(message)
