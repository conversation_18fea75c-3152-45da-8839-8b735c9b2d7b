Flask==2.3.3
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5
Flask-RESTful==0.3.10
Flask-Cors==4.0.0
psycopg2-binary==2.9.9
redis==5.0.1
celery==5.3.4
gunicorn==21.2.0
SQLAlchemy==2.0.21
alembic==1.12.0
requests==2.31.0
python-dotenv==1.0.0
PyJWT==2.8.0
bcrypt==4.0.1
marshmallow==3.20.1
pytest==7.4.2
pytest-cov==4.1.0
black==23.9.1
flake8==6.1.0
mypy==1.5.1
isort==5.12.0
python-dateutil==2.8.2
pytz==2023.3.post1
APScheduler==3.10.4
prometheus-client==0.17.1
sentry-sdk==1.31.0
structlog==23.1.0
tenacity==8.2.3
websockets==12.0  # Using websockets instead of aiohttp for Python 3.12
asyncio==3.4.3
uvicorn==0.23.2
psutil==5.9.8
Flask==2.3.3
SQLAlchemy==2.0.21
passlib[bcrypt]
PyJWT==2.8.0
cryptography==40.0.1
python-dotenv==1.0.0
