{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"dashboardName": {"type": "string", "defaultValue": "ProjectTrackerCDNDashboard", "metadata": {"description": "Name of the Azure dashboard"}}}, "variables": {"dashboard": {"properties": {"lenses": {"0": {"order": 0, "parts": {"0": {"position": {"x": 0, "y": 0, "colSpan": 6, "rowSpan": 4}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "/subscriptions/${subscription().subscriptionId}/resourceGroups/project-tracker/providers/Microsoft.Cdn/profiles/project-tracker-cdn"}, "name": "cdn_response_time", "aggregationType": "Average", "namespace": "microsoft.cdn/profiles", "metricVisualization": {"displayName": "Response Time by Location"}}], "title": "CDN Response Time", "visualization": "linechart"}}}], "type": "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart"}}, "1": {"position": {"x": 6, "y": 0, "colSpan": 6, "rowSpan": 4}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "/subscriptions/${subscription().subscriptionId}/resourceGroups/project-tracker/providers/Microsoft.Cdn/profiles/project-tracker-cdn"}, "name": "cdn_cache_hit_rate", "aggregationType": "Average", "namespace": "microsoft.cdn/profiles", "metricVisualization": {"displayName": "<PERSON><PERSON> Hit Rate"}}], "title": "<PERSON><PERSON>", "visualization": "linechart"}}}], "type": "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart"}}, "2": {"position": {"x": 0, "y": 4, "colSpan": 6, "rowSpan": 4}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "/subscriptions/${subscription().subscriptionId}/resourceGroups/project-tracker/providers/Microsoft.Cdn/profiles/project-tracker-cdn"}, "name": "cdn_error", "aggregationType": "Count", "namespace": "microsoft.cdn/profiles", "metricVisualization": {"displayName": "Error Count"}}], "title": "CDN Errors", "visualization": "barchart"}}}], "type": "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart"}}, "3": {"position": {"x": 6, "y": 4, "colSpan": 6, "rowSpan": 4}, "metadata": {"inputs": [{"name": "options", "value": {"chart": {"metrics": [{"resourceMetadata": {"id": "/subscriptions/${subscription().subscriptionId}/resourceGroups/project-tracker/providers/Microsoft.Cdn/profiles/project-tracker-cdn"}, "name": "cdn_ssl_expiry_days", "aggregationType": "Minimum", "namespace": "microsoft.cdn/profiles", "metricVisualization": {"displayName": "SSL Certificate Expiry"}}], "title": "SSL Certificate Status", "visualization": "timechart"}}}], "type": "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart"}}}}}}}}, "resources": [{"type": "Microsoft.Portal/dashboards", "apiVersion": "2019-01-01-preview", "name": "[parameters('dashboardName')]", "location": "[resourceGroup().location]", "tags": {"hidden-title": "Project Tracker CDN Dashboard"}, "properties": "[variables('dashboard').properties]"}], "outputs": {"dashboardId": {"type": "string", "value": "[resourceId('Microsoft.Portal/dashboards', parameters('dashboardName'))]"}}}