from typing import TypedDict, List, Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

class ProjectType(str, Enum):
    WEB_SERVICE = 'web_service'
    AGENT_SYSTEM = 'agent_system'
    LIBRARY = 'library'

class ProjectStatus(str, Enum):
    INITIALIZED = 'initialized'
    ACTIVE = 'active'
    WARNING = 'warning'
    ERROR = 'error'

class ActionType(str, Enum):
    CREATE = 'create'
    UPDATE = 'update'
    DELETE = 'delete'

class EntityType(str, Enum):
    IMPROVEMENT = 'improvement'
    CATEGORY = 'category'
    SUBTASK = 'subtask'

class ProjectTemplate(BaseModel):
    id: int
    name: str
    type: ProjectType
    description: Optional[str]
    git_repository_url: Optional[str]
    created_at: datetime

    class Config:
        orm_mode = True

class Project(BaseModel):
    id: int
    name: str
    type: ProjectType
    template_id: Optional[int]
    path: str
    status: ProjectStatus
    repository_url: Optional[str]
    created_at: datetime
    last_modified: datetime
    improvements_count: Optional[int]
    categories_count: Optional[int]

    class Config:
        orm_mode = True

class Category(BaseModel):
    id: int
    project_id: int
    name: str
    improvements_count: Optional[int]

    class Config:
        orm_mode = True

class Subtask(BaseModel):
    id: int
    improvement_id: str
    project_id: int
    description: str
    completed: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class SubtaskCreate(BaseModel):
    description: str
    completed: Optional[bool] = False

class Improvement(BaseModel):
    id: int
    improvement_id: str
    project_id: int
    date: str
    commit_hash: Optional[str]
    category_id: Optional[int]
    category_name: Optional[str]
    affected_files: List[str]
    version: Optional[str]
    rollback_command: Optional[str]
    description: str
    potential_impacts: Optional[str]
    created_at: datetime
    updated_at: datetime
    subtasks: List[Subtask]

    @validator('affected_files', pre=True)
    def split_affected_files(cls, v):
        if isinstance(v, str):
            return v.split(',') if v else []
        return v

    class Config:
        orm_mode = True

class ImprovementCreate(BaseModel):
    improvement_id: str
    category: str
    date: str
    commit_hash: Optional[str]
    affected_files: List[str]
    version: Optional[str]
    rollback_command: Optional[str]
    description: str
    potential_impacts: Optional[str]
    subtasks: List[SubtaskCreate]

class ProjectActivity(BaseModel):
    id: int
    project_id: int
    action_type: ActionType
    entity_type: EntityType
    entity_id: str
    details: Optional[str]
    timestamp: datetime

    class Config:
        orm_mode = True

class ProjectStats(BaseModel):
    total_improvements: int
    active_categories: int
    recent_activity: int
    completion_rate: float

    class Config:
        orm_mode = True

def validate_project_template(data: dict) -> ProjectTemplate:
    return ProjectTemplate(**data)

def validate_project(data: dict) -> Project:
    return Project(**data)

def validate_category(data: dict) -> Category:
    return Category(**data)

def validate_improvement(data: dict) -> Improvement:
    return Improvement(**data)

def validate_improvement_create(data: dict) -> ImprovementCreate:
    return ImprovementCreate(**data)

def validate_project_activity(data: dict) -> ProjectActivity:
    return ProjectActivity(**data)

def validate_project_stats(data: dict) -> ProjectStats:
    return ProjectStats(**data)
