[Unit]
Description=Project Tracker WebSocket Service
After=network.target redis.service
Wants=redis.service

[Service]
User=project-tracker
Group=project-tracker
WorkingDirectory=/opt/project-tracker/backend
ExecStart=/opt/project-tracker/venv/bin/python -m app.websocket
Restart=on-failure
RestartSec=5
Environment=PATH=/opt/project-tracker/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/opt/project-tracker/backend
Environment=FLASK_ENV=production
Environment=REDIS_HOST=localhost
Environment=REDIS_PORT=6379

# Security
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=full

[Install]
WantedBy=multi-user.target
