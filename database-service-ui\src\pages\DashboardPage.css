.dashboard-page {
  padding: 2rem 0;
}

.page-title {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.welcome-card {
  margin-bottom: 2rem;
}

.welcome-card h2 {
  margin-top: 0;
  color: var(--primary-color);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.metrics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metrics-summary-card {
  padding: 1.5rem;
}

.metrics-summary-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.metric-value {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.metric-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.2;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.metric-progress {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.metric-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.metric-details {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.dashboard-actions h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

@media (max-width: 768px) {
  .metrics-summary {
    grid-template-columns: 1fr;
  }
}
