# Comprehensive Git Server Setup Guide

## Overview

This document provides a comprehensive guide for setting up a centralized Git server on Ubuntu 24.04.2 LTS, migrating repositories from Windows, and configuring SSH key authentication for the git user. It includes detailed steps for troubleshooting SSH key issues, which were a significant challenge during the setup process.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Initial Server Setup](#initial-server-setup)
3. [Git Server Installation](#git-server-installation)
4. [SSH Key Configuration](#ssh-key-configuration)
   - [Troubleshooting SSH Key Issues](#troubleshooting-ssh-key-issues)
   - [Working Solution for SSH Key Setup](#working-solution-for-ssh-key-setup)
5. [Repository Migration](#repository-migration)
6. [Windsurf Integration](#windsurf-integration)
7. [Client Machine Configuration](#client-machine-configuration)
   - [Minimal Git Client Installation](#minimal-git-client-installation)
   - [Auto-Commit Functionality](#auto-commit-functionality)
8. [Removing Git from Windows Build1 Server](#removing-git-from-windows-build1-server)
9. [Maintenance and Backup](#maintenance-and-backup)
10. [Conclusion](#conclusion)
11. [Appendix: Essential Scripts](#appendix-essential-scripts)

## Prerequisites

- Ubuntu 24.04.2 LTS server (IP: ***********)
- Administrative access to both Windows and Ubuntu servers
- SSH client on Windows machine
- Git installed on Windows machine (for migration)

## Initial Server Setup

### 1. Update the System

```bash
sudo apt update
sudo apt upgrade -y
```

### 2. Create Administrative User

```bash
sudo adduser btaylor-admin
sudo usermod -aG sudo btaylor-admin
```

### 3. Configure SSH Access for Administrative User

```bash
# On Windows machine, generate SSH key if not already available
ssh-keygen -t rsa -b 4096 -C "btaylor-admin@build1"

# Copy public key to server
ssh-copy-id btaylor-admin@***********
```

## Git Server Installation

### 1. Install Git and Required Packages

```bash
sudo apt install -y git git-core gitweb apache2 highlight
```

### 2. Create Git User

```bash
sudo adduser --system --shell /bin/bash --gecos 'Git Version Control' --group --disabled-password --home /home/<USER>
```

### 3. Set Up Git Repository Directory

```bash
sudo mkdir -p /home/<USER>
sudo chown git:git /home/<USER>
sudo chmod 755 /home/<USER>
```

### 4. Initialize Bare Repository

```bash
sudo -u git mkdir -p /home/<USER>/project-tracker.git
sudo -u git git init --bare /home/<USER>/project-tracker.git
```

## SSH Key Configuration

This section covers the SSH key setup for the git user, which was the most challenging part of the process. After multiple attempts, we found a working solution that ensures seamless SSH access to the git user without password prompts.

### Troubleshooting SSH Key Issues

We encountered several issues when setting up SSH key authentication for the git user:

1. **Host Key Verification Failed**: When trying to SSH to the git user on localhost, we received a "Host key verification failed" error.

2. **Permission Denied (publickey,password)**: After adding localhost to known_hosts, we still couldn't connect to the git user using SSH keys.

3. **Private Key Access Issues**: When trying to use the git user's private key from the btaylor-admin account, we received "Permission denied" errors.

### Working Solution for SSH Key Setup

The following steps provide a reliable solution for setting up SSH key authentication for the git user:

#### 1. Log in to the Server as Administrative User

```bash
ssh btaylor-admin@***********
```

#### 2. Set a Password for the Git User (Temporary)

```bash
sudo -i
passwd git
# Enter a simple password you'll remember
exit
```

#### 3. Log in as Git User

```bash
ssh git@localhost
# Enter the password you just set
```

#### 4. Set Up SSH Keys Directly as the Git User

```bash
# Check the current SSH directory and authorized_keys file
ls -la ~/.ssh/
cat ~/.ssh/authorized_keys

# Create a new SSH key pair for the git user
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""

# Add the public key to authorized_keys
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys

# Make sure permissions are correct
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys

# Test SSH connection locally
ssh -o StrictHostKeyChecking=no localhost 'echo CONNECTION_OK'
```

#### 5. Add Windows Public Key to Git User's Authorized Keys

While still logged in as the git user, add your Windows machine's public key to the authorized_keys file:

```bash
nano ~/.ssh/authorized_keys
# Add your Windows public key on a new line
```

#### 6. Test SSH Connection from Windows

```powershell
# From your Windows machine
ssh -o BatchMode=yes git@*********** 'echo CONNECTION_OK'
```

If successful, you should see "CONNECTION_OK" as the output.

## Repository Migration

### 1. Update Git Remote URL

```powershell
# On Windows machine
cd D:\Codeium\CHCIT\project-tracker
git remote set-url origin ssh://git@***********/home/<USER>/project-tracker.git
```

### 2. Verify Remote URL

```powershell
git remote -v
```

### 3. Push Repository to Git Server

```powershell
git push -u origin master
```

## Windsurf Integration

### 1. Create Windsurf Git Configuration File

Create a file named `windsurf-git-config.json` with the following content:

```json
{
  "gitServer": {
    "repoBasePath": "/home/<USER>",
    "host": "***********",
    "user": "git",
    "defaultRepo": "project-tracker.git",
    "sshKeyPath": "~/.ssh/id_rsa"
  },
  "repositories": [
    {
      "name": "project-tracker",
      "description": "Project Tracker Repository",
      "path": "/home/<USER>/project-tracker.git"
    }
  ]
}
```

### 2. Test Windsurf Git Integration

Run the `test-windsurf-git-integration.ps1` script to verify that Windsurf can interact with the Git server:

```powershell
.\scripts\test-windsurf-git-integration.ps1
```

This script will:
- Verify SSH connection to the Git server
- Create a test file
- Commit and push the file to the remote repository
- Verify the commit on the server

## Client Machine Configuration

### Minimal Git Client Installation

After migrating to a centralized Git server, Windsurf still requires a minimal Git client installation on client machines to display source control features. This is a key finding from our implementation process.

```powershell
# Download Git installer
Invoke-WebRequest -Uri https://github.com/git-for-windows/git/releases/download/v2.44.0.windows.1/Git-2.44.0-64-bit.exe -OutFile $env:TEMP\Git-2.44.0-64-bit.exe

# Install Git with minimal components
Start-Process -FilePath "$env:TEMP\Git-2.44.0-64-bit.exe" -ArgumentList "/VERYSILENT","/NORESTART","/NOCANCEL","/SP-","/CLOSEAPPLICATIONS","/RESTARTAPPLICATIONS","/COMPONENTS=gitlfs,assoc,assoc_sh" -Wait

# Verify Git installation
git --version
```

After installation, you'll need to accept the SSH host key for the Git server:

```powershell
# Accept SSH host key
ssh -o StrictHostKeyChecking=accept-new git@*********** "echo CONNECTION_OK"
```

### Auto-Commit Functionality

The project includes two scripts for real-time auto-commit functionality that can be used with the centralized Git server:

1. `real-time-commit.ps1` - Monitors file changes in the repository and automatically commits them after a brief period of inactivity (2 seconds)
2. `start-real-time-commit.ps1` - Launcher script that starts the real-time commit monitor in a separate PowerShell window

To use the auto-commit functionality:

```powershell
# Start the auto-commit script
.\scripts\start-real-time-commit.ps1
```

Without this script running, changes will be tracked in the source control view but won't be automatically committed.

## Removing Git from Windows Build1 Server

Once you have confirmed that the Git server is working correctly, you can safely remove Git from the Windows Build1 server using the `remove-git-from-build1.ps1` script:

```powershell
.\scripts\remove-git-from-build1.ps1
```

This script will:
- Create a backup of your Git configuration and repositories
- Verify Git server connection before proceeding
- Check for running Git processes
- Uninstall Git from the Windows machine
- Verify Git removal

## Maintenance and Backup

### 1. Regular Backups

```bash
# On Git server
sudo -u git tar -czf /tmp/git-backup-$(date +%Y%m%d).tar.gz /home/<USER>
sudo mv /tmp/git-backup-*.tar.gz /backup/
```

### 2. Monitoring Repository Size

```bash
du -sh /home/<USER>/*
```

### 3. Adding New Repositories

```bash
# On Git server
sudo -u git mkdir -p /home/<USER>/new-repo.git
sudo -u git git init --bare /home/<USER>/new-repo.git
```

## Conclusion

This guide provides a comprehensive approach to setting up a centralized Git server on Ubuntu 24.04.2 LTS, with special emphasis on resolving SSH key authentication issues for the git user. By following these steps, you can establish a robust Git server infrastructure that supports your development workflow and integrates with tools like Windsurf.

The most critical lessons learned during this process were:

1. The importance of setting up SSH keys directly as the git user, rather than trying to configure them from an administrative account. This approach ensures proper permissions and ownership of SSH keys, which is essential for seamless authentication.

2. Windsurf requires a minimal Git client installation on client machines to display source control features, even when using a centralized Git server. This was an unexpected finding that required additional configuration.

3. The auto-commit functionality needs to be explicitly started using the provided scripts if you want changes to be automatically committed without manual intervention.

With this setup, you now have a centralized Git server that provides version control for multiple projects and users, enhancing collaboration and code management across your organization. The Git integration features in your project schema, including repository tracking, branch management, and metrics collection, are all supported by this infrastructure.

## Appendix: Essential Scripts

The following essential scripts have been consolidated in the `git-server-scripts` directory:

### 1. test-windsurf-git-integration.ps1

This script tests the integration between Windsurf and the Git server, verifying SSH connection and Git operations.

```powershell
# Usage
.\git-server-scripts\test-windsurf-git-integration.ps1
```

### 2. real-time-commit.ps1 and start-real-time-commit.ps1

These scripts provide automatic commit functionality, monitoring file changes and committing them after a brief period of inactivity.

```powershell
# Usage
.\git-server-scripts\start-real-time-commit.ps1
```

### 3. setup-git-server.sh

This script sets up the Git server on Ubuntu, including Git, SSH, Apache, and GitWeb installation and configuration.

```bash
# Usage on Ubuntu server
chmod +x setup-git-server.sh
sudo ./setup-git-server.sh
```

### 4. migrate-to-git-server.ps1

This PowerShell script migrates repositories from Windows to the Ubuntu Git server.

```powershell
# Usage
.\git-server-scripts\migrate-to-git-server.ps1
```

### 5. direct-ssh-fix.sh

This script resolves SSH key authentication issues for the git user on the Ubuntu server.

```bash
# Usage on Ubuntu server
chmod +x direct-ssh-fix.sh
sudo ./direct-ssh-fix.sh
```

### direct-ssh-fix.sh

This script provides a direct fix for SSH key issues on the server. It should be run as root on the Ubuntu server.

```bash
#!/bin/bash

# Direct SSH Fix Script for Git User
# Run this script directly on the server as root

# Colors for output
RED="\033[0;31m"
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}=== Direct SSH Fix for Git User ===${NC}"

# Check if running as root
if [ $(id -u) -ne 0 ]; then
    echo -e "${RED}This script must be run as root${NC}"
    exit 1
fi

# Check git user existence
echo -e "${YELLOW}Checking if git user exists...${NC}"
if id "git" &>/dev/null; then
    echo -e "${GREEN}Git user exists.${NC}"
    GIT_HOME=$(getent passwd git | cut -d: -f6)
    echo -e "${BLUE}Git user home directory: ${GIT_HOME}${NC}"
else
    echo -e "${YELLOW}Git user does not exist. Creating git user...${NC}"
    adduser --system --shell /bin/bash --gecos 'Git Version Control' --group --disabled-password --home /home/<USER>
    GIT_HOME="/home/<USER>"
    echo -e "${GREEN}Git user created with home directory: ${GIT_HOME}${NC}"
fi

# Create .ssh directory if it doesn't exist
echo -e "${YELLOW}Setting up .ssh directory for git user...${NC}"
mkdir -p "${GIT_HOME}/.ssh"
chmod 700 "${GIT_HOME}/.ssh"
echo -e "${GREEN}.ssh directory set up.${NC}"

# Get the public key from btaylor-admin user
echo -e "${YELLOW}Getting public key from btaylor-admin user...${NC}"
ADMIN_HOME=$(getent passwd btaylor-admin | cut -d: -f6)

if [ -f "${ADMIN_HOME}/.ssh/id_rsa.pub" ]; then
    echo -e "${GREEN}Found public key in btaylor-admin's .ssh directory.${NC}"
    cp "${ADMIN_HOME}/.ssh/id_rsa.pub" /tmp/admin_key.pub
    PUBLIC_KEY=$(cat /tmp/admin_key.pub)
    rm /tmp/admin_key.pub
elif [ -f "${ADMIN_HOME}/.ssh/authorized_keys" ]; then
    echo -e "${YELLOW}Using first key from btaylor-admin's authorized_keys file.${NC}"
    PUBLIC_KEY=$(head -n 1 "${ADMIN_HOME}/.ssh/authorized_keys")
else
    echo -e "${RED}No SSH keys found for btaylor-admin user!${NC}"
    echo -e "${YELLOW}Please manually add your public key to the script.${NC}"
    exit 1
fi

# Add SSH key to authorized_keys
echo -e "${YELLOW}Adding SSH key to git user's authorized_keys...${NC}"
echo "${PUBLIC_KEY}" > "${GIT_HOME}/.ssh/authorized_keys"
chmod 600 "${GIT_HOME}/.ssh/authorized_keys"
chown -R git:git "${GIT_HOME}/.ssh"
echo -e "${GREEN}SSH key added to authorized_keys.${NC}"

# Verify SSH configuration
echo -e "${YELLOW}Verifying SSH configuration...${NC}"
if [ -f "/etc/ssh/sshd_config" ]; then
    # Check if public key authentication is enabled
    if grep -q "^PubkeyAuthentication yes" "/etc/ssh/sshd_config"; then
        echo -e "${GREEN}Public key authentication is already enabled.${NC}"
    else
        echo -e "${YELLOW}Enabling public key authentication in SSH config...${NC}"
        sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
        systemctl restart sshd
        echo -e "${GREEN}Public key authentication enabled and SSH service restarted.${NC}"
    fi
else
    echo -e "${RED}SSH config file not found!${NC}"
    exit 1
fi

# Set up git repository if it doesn't exist
echo -e "${YELLOW}Setting up git repository...${NC}"
REPO_PATH="${GIT_HOME}/project-tracker.git"

if [ ! -d "$REPO_PATH" ]; then
    echo -e "${YELLOW}Creating git repository...${NC}"
    mkdir -p "$REPO_PATH"
    cd "$REPO_PATH"
    git init --bare
    echo -e "${GREEN}Git repository created.${NC}"
else
    echo -e "${GREEN}Git repository already exists.${NC}"
fi

# Set proper permissions for the repository
echo -e "${YELLOW}Setting proper permissions for the repository...${NC}"
chown -R git:git "$REPO_PATH"
chmod -R 775 "$REPO_PATH"
echo -e "${GREEN}Repository permissions set.${NC}"

# Final verification
echo -e "${YELLOW}Performing final verification...${NC}"
if [ -f "${GIT_HOME}/.ssh/authorized_keys" ]; then
    KEYS_COUNT=$(grep -c '^ssh-' "${GIT_HOME}/.ssh/authorized_keys")
    echo -e "${GREEN}Git user has ${KEYS_COUNT} SSH keys in authorized_keys file.${NC}"
    
    # Check permissions
    SSH_DIR_PERMS=$(stat -c "%a" "${GIT_HOME}/.ssh")
    AUTH_KEYS_PERMS=$(stat -c "%a" "${GIT_HOME}/.ssh/authorized_keys")
    
    echo -e "${BLUE}.ssh directory permissions: ${SSH_DIR_PERMS} (should be 700)${NC}"
    echo -e "${BLUE}authorized_keys permissions: ${AUTH_KEYS_PERMS} (should be 600)${NC}"
    
    if [ "$SSH_DIR_PERMS" != "700" ] || [ "$AUTH_KEYS_PERMS" != "600" ]; then
        echo -e "${YELLOW}Warning: Permissions are not set correctly. Fixing...${NC}"
        chmod 700 "${GIT_HOME}/.ssh"
        chmod 600 "${GIT_HOME}/.ssh/authorized_keys"
        chown -R git:git "${GIT_HOME}/.ssh"
        echo -e "${GREEN}Permissions fixed.${NC}"
    else
        echo -e "${GREEN}All permissions are set correctly.${NC}"
    fi
else
    echo -e "${RED}No authorized_keys file found for git user after setup!${NC}"
    exit 1
fi

# Set proper permissions for git home directory
echo -e "${YELLOW}Setting proper permissions for git home directory...${NC}"
chmod 755 "${GIT_HOME}"
chown git:git "${GIT_HOME}"
echo -e "${GREEN}Git home directory permissions set.${NC}"

# Add btaylor-admin to git group
echo -e "${YELLOW}Adding btaylor-admin to git group...${NC}"
usermod -a -G git btaylor-admin
echo -e "${GREEN}btaylor-admin added to git group.${NC}"

# Test SSH connection locally
echo -e "${YELLOW}Testing SSH connection locally...${NC}"
su - btaylor-admin -c "ssh -o BatchMode=yes -o StrictHostKeyChecking=no git@localhost 'echo CONNECTION_TEST'"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Local SSH connection successful!${NC}"
else
    echo -e "${RED}Local SSH connection failed. Please check the setup.${NC}"
fi

echo -e "${GREEN}SSH key setup completed successfully!${NC}"
echo -e "${YELLOW}You can now use the git user for SSH connections.${NC}"
echo -e "${YELLOW}Test from your client machine with: ssh -o BatchMode=yes git@SERVER_IP 'echo CONNECTION_OK'${NC}"
```

### test-windsurf-git-integration.ps1

This PowerShell script tests the integration between Windsurf and the Git server.

```powershell
# Test Windsurf Git Integration
# This script tests the integration between Windsurf and the Git server

# Configuration
$configFile = "$PSScriptRoot\..\windsurf-git-config.json"
$testCommitMessage = "Test commit from Windsurf integration test script"
$testFileName = "windsurf-git-integration-test.md"

# Function to display colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Check if config file exists
if (-not (Test-Path $configFile)) {
    Write-ColorOutput Red "Error: Config file not found at $configFile"
    exit 1
}

# Read configuration
Write-ColorOutput Green "Reading Git configuration from $configFile"
$config = Get-Content -Path $configFile | ConvertFrom-Json

# Display configuration
Write-ColorOutput Cyan "Git Server Configuration:"
Write-Output "Host: $($config.gitServer.host)"
Write-Output "User: $($config.gitServer.user)"
Write-Output "Repo Base Path: $($config.gitServer.repoBasePath)"
Write-Output "Default Repo: $($config.gitServer.defaultRepo)"

# Test SSH connection to Git server
Write-ColorOutput Green "\nTesting SSH connection to Git server..."
$sshTestResult = ssh -o BatchMode=yes "$($config.gitServer.user)@$($config.gitServer.host)" "echo CONNECTION_OK"

if ($sshTestResult -eq "CONNECTION_OK") {
    Write-ColorOutput Green "SSH connection successful!"
} else {
    Write-ColorOutput Red "SSH connection failed!"
    exit 1
}

# Test Git operations
Write-ColorOutput Green "\nTesting Git operations..."

# Get current directory
$currentDir = Get-Location

# Navigate to project root
Set-Location "$PSScriptRoot\.."

# Create a test file
Write-ColorOutput Cyan "Creating test file..."
$testContent = @"
# Windsurf Git Integration Test

This file was created by the Windsurf Git integration test script on $(Get-Date).

## Test Details

- Server: $($config.gitServer.host)
- User: $($config.gitServer.user)
- Repository: $($config.gitServer.defaultRepo)

If you can see this file in the repository, the integration test was successful!
"@

$testContent | Out-File -FilePath $testFileName -Encoding utf8

# Add the file to Git
Write-ColorOutput Cyan "Adding test file to Git..."
git add $testFileName

# Commit the file
Write-ColorOutput Cyan "Committing test file..."
git commit -m "$testCommitMessage"

# Push to remote
Write-ColorOutput Cyan "Pushing to remote repository..."
$pushResult = git push origin master

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput Green "Push successful! Windsurf Git integration is working correctly."
} else {
    Write-ColorOutput Red "Push failed with exit code $LASTEXITCODE"
    Write-Output $pushResult
    exit 1
}

# Verify on server
Write-ColorOutput Green "\nVerifying commit on server..."
$verifyResult = ssh "$($config.gitServer.user)@$($config.gitServer.host)" "cd $($config.repositories[0].path) && git log -1 --pretty=format:'%s'"

if ($verifyResult -eq $testCommitMessage) {
    Write-ColorOutput Green "Verification successful! Found commit: $verifyResult"
} else {
    Write-ColorOutput Yellow "Verification result does not match expected commit message."
    Write-Output "Expected: $testCommitMessage"
    Write-Output "Actual: $verifyResult"
}

# Return to original directory
Set-Location $currentDir

Write-ColorOutput Green "\nWindsurf Git integration test completed successfully!"
Write-ColorOutput Cyan "You can now safely remove Git from the local Build1 server."
```

### remove-git-from-build1.ps1

This PowerShell script safely removes Git from the Windows Build1 server after migration to the centralized Git server.

```powershell
# Remove Git from Build1 Server
# This script safely removes Git from the Windows Build1 server after migration to the centralized Git server

# Configuration
$backupDir = "$env:USERPROFILE\Git-Backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$gitInstallPath = "C:\Program Files\Git"
$gitConfigBackupPath = "$backupDir\git-config-backup"

# Function to display colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Create backup directory
Write-ColorOutput Green "Creating backup directory at $backupDir"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
New-Item -ItemType Directory -Path $gitConfigBackupPath -Force | Out-Null

# Verify Git server connection before proceeding
Write-ColorOutput Green "Verifying Git server connection..."
try {
    $sshTestResult = ssh -o BatchMode=yes git@*********** "echo CONNECTION_OK"
    if ($sshTestResult -ne "CONNECTION_OK") {
        Write-ColorOutput Red "Error: Cannot connect to Git server. Aborting removal."
        exit 1
    }
    Write-ColorOutput Green "Git server connection verified."
}
catch {
    Write-ColorOutput Red "Error: Cannot connect to Git server. Aborting removal."
    Write-ColorOutput Red $_.Exception.Message
    exit 1
}

# Backup global Git configuration
Write-ColorOutput Green "Backing up global Git configuration..."
if (Test-Path "$env:USERPROFILE\.gitconfig") {
    Copy-Item "$env:USERPROFILE\.gitconfig" -Destination "$gitConfigBackupPath\.gitconfig" -Force
    Write-ColorOutput Cyan "Global .gitconfig backed up."
}

# Backup SSH keys
Write-ColorOutput Green "Backing up SSH keys..."
if (Test-Path "$env:USERPROFILE\.ssh") {
    Copy-Item "$env:USERPROFILE\.ssh" -Destination "$gitConfigBackupPath\.ssh" -Recurse -Force
    Write-ColorOutput Cyan "SSH keys backed up."
}

# Get list of installed Git repositories
Write-ColorOutput Green "Finding Git repositories..."
$repos = @()
Get-ChildItem -Path "D:\Codeium" -Recurse -Directory -Force -ErrorAction SilentlyContinue | 
    Where-Object { $_.Name -eq ".git" -and $_.FullName -notlike "*\node_modules\*" } | 
    ForEach-Object { 
        $repoPath = $_.Parent.FullName
        $repoName = Split-Path $repoPath -Leaf
        $remoteUrl = (git -C $repoPath remote get-url origin) 2>$null
        if ($remoteUrl) {
            $repos += [PSCustomObject]@{
                Name = $repoName
                Path = $repoPath
                RemoteUrl = $remoteUrl
            }
            Write-ColorOutput Cyan "Found repository: $repoName at $repoPath"
        }
    }

# Backup repository information
Write-ColorOutput Green "Backing up repository information..."
$repos | ConvertTo-Json | Out-File "$backupDir\repositories.json" -Encoding utf8

# Check for Git processes
Write-ColorOutput Green "Checking for running Git processes..."
$gitProcesses = Get-Process | Where-Object { $_.Name -like "*git*" }
if ($gitProcesses) {
    Write-ColorOutput Yellow "Warning: The following Git processes are running:"
    $gitProcesses | Format-Table -Property Id, Name, Path -AutoSize
    
    $confirmation = Read-Host "Do you want to stop these processes? (y/n)"
    if ($confirmation -eq 'y') {
        $gitProcesses | ForEach-Object { 
            try {
                Stop-Process -Id $_.Id -Force
                Write-ColorOutput Green "Stopped process: $($_.Name) (ID: $($_.Id))"
            } catch {
                Write-ColorOutput Red "Failed to stop process: $($_.Name) (ID: $($_.Id))"
            }
        }
    } else {
        Write-ColorOutput Yellow "Proceeding without stopping Git processes. This may cause issues during uninstallation."
    }
}

# Uninstall Git
Write-ColorOutput Green "Uninstalling Git..."
$confirmation = Read-Host "Are you sure you want to uninstall Git? This action cannot be undone. (y/n)"
if ($confirmation -ne 'y') {
    Write-ColorOutput Yellow "Git uninstallation cancelled."
    exit 0
}

# Find Git uninstaller
$uninstaller = $null
if (Test-Path "$gitInstallPath\unins000.exe") {
    $uninstaller = "$gitInstallPath\unins000.exe"
} elseif (Test-Path "$env:ProgramData\Package Cache\*\Git*\uninstall.exe") {
    $uninstaller = (Get-ChildItem "$env:ProgramData\Package Cache\*\Git*\uninstall.exe" | Select-Object -First 1).FullName
}

if ($uninstaller) {
    Write-ColorOutput Green "Found Git uninstaller at $uninstaller"
    Write-ColorOutput Yellow "Starting Git uninstallation. Please follow the prompts in the uninstaller window."
    Start-Process -FilePath $uninstaller -ArgumentList "/SILENT" -Wait
    Write-ColorOutput Green "Git uninstallation completed."
} else {
    Write-ColorOutput Red "Error: Git uninstaller not found. Please uninstall Git manually from Control Panel."
}

# Verify Git is removed
Write-ColorOutput Green "Verifying Git removal..."
if (Test-Path $gitInstallPath) {
    Write-ColorOutput Yellow "Warning: Git installation directory still exists at $gitInstallPath"
} else {
    Write-ColorOutput Green "Git installation directory removed successfully."
}

if (Get-Command git -ErrorAction SilentlyContinue) {
    Write-ColorOutput Yellow "Warning: Git command is still available in PATH. You may need to restart your computer or update your PATH environment variable."
} else {
    Write-ColorOutput Green "Git command removed from PATH successfully."
}

# Summary
Write-ColorOutput Green "\nGit removal process completed!"
Write-ColorOutput Cyan "Summary:"
Write-ColorOutput Cyan "- Backup created at: $backupDir"
Write-ColorOutput Cyan "- Repositories backed up: $($repos.Count)"
Write-ColorOutput Cyan "- Git installation removed: $(if (!(Test-Path $gitInstallPath)) { 'Yes' } else { 'No (manual cleanup may be required)' })"

Write-ColorOutput Green "\nNext steps:"
Write-ColorOutput Cyan "1. Restart your computer to ensure all Git components are completely removed."
Write-ColorOutput Cyan "2. Update any scripts or applications that relied on local Git to use the centralized Git server."
Write-ColorOutput Cyan "3. Refer to the Git server documentation for instructions on how to work with the centralized Git server."
