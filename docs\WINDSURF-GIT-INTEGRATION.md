# Windsurf Git Server Integration

## Overview

This document describes how to integrate Windsurf with the centralized Git server running on Ubuntu (***********).

## Configuration

### Git Server Details

- **Server**: ***********
- **User**: git
- **Repository Path**: /home/<USER>/repositories/project-tracker.git

### Repository URL Format

```
ssh://git@***********/home/<USER>/repositories/repository-name.git
```

### Authentication

Authentication is handled through SSH keys. Your public key has been added to the git user's authorized_keys file on the server.

## Windsurf Integration

1. **Update Windsurf Configuration**:
   - Use the windsurf-git-config.json file for Git server configuration
   - The configuration file is located at: $windsurfConfigPath

2. **Clone Repositories**:
   ```bash
   git clone ssh://git@***********/home/<USER>/repositories/project-tracker.git
   ```

3. **Push Changes**:
   ```bash
   git push origin master
   ```

## Repository Management

### Create a New Repository

```bash
# SSH to the server
ssh git@***********

# Create a new bare repository
sudo mkdir -p /home/<USER>/repositories/new-repo.git
cd /home/<USER>/repositories/new-repo.git
sudo git init --bare
sudo chown -R git:git .
sudo chmod -R 775 .
```

### Add a New User

```bash
# SSH to the server
ssh git@***********

# Add a new user
sudo adduser newuser

# Add the user to the git group
sudo usermod -a -G git newuser
```

## Troubleshooting

### SSH Key Issues

If you encounter authentication issues, verify your SSH key is properly set up:

```bash
# Test SSH connection
ssh -T git@***********
```

### Permission Issues

If you encounter permission issues, run these commands on the server:

```bash
ssh git@***********
sudo usermod -a -G git git
sudo chmod 775 /home/<USER>
sudo chgrp git /home/<USER>
sudo find /home/<USER>/ -type d -exec chmod 775 {} \;
sudo find /home/<USER>
sudo chown -R git:git /home/<USER>
```

## Removing Git from Windows Build1 Server

After confirming that Windsurf is properly integrated with the Git server, you can safely remove Git from the Windows Build1 server:

1. Back up any important repositories
2. Uninstall Git from the Windows server
3. Update any scripts or configurations that reference local Git installations

## Support

For assistance with Git server integration, contact the system administrator.
