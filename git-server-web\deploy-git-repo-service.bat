@echo off
setlocal enabledelayedexpansion

:: Git Repository Service Deployment Script
:: This script helps deploy the C++23 Git Repository service to the Ubuntu Git server

:: Configuration Variables
set "GIT_SERVER=git.chcit.org"
set "SSH_USER=btaylor-admin"
set "SOURCE_DIR=D:\Codeium\CHCIT\project-tracker\git-server-web\git-repo-service"
set "REMOTE_DIR=/opt/git-dashboard/git-repo-service"
set "REMOTE_BIN_DIR=/opt/git-dashboard/bin"
set "REMOTE_SYSTEMD_DIR=/opt/git-dashboard/systemd"
set "REPO_PATH=/home/<USER>/repositories"

:: Create local log directory if it doesn't exist
set "LOCAL_LOG_DIR=%~dp0logs"
if not exist "%LOCAL_LOG_DIR%" mkdir "%LOCAL_LOG_DIR%"

:MENU
cls
echo =============================================
echo Git Repository Service Deployment Tool
echo =============================================
echo.
echo Choose an option:
echo 1. Check SSH Connection
echo 2. Install Dependencies on Git Server
echo 3. Deploy Source Code
echo 4. Build Application
echo 5. Install Service
echo 6. Start Service
echo 7. Check Service Status
echo 8. Full Deployment (Steps 1-7)
echo 9. Check Installed Versions
echo 0. Exit
echo.

CHOICE /C:1234567890 /N /M "Enter your choice (0-9): "

IF ERRORLEVEL 10 goto EXIT
IF ERRORLEVEL 9 goto CHECK_INSTALLED_VERSIONS
IF ERRORLEVEL 8 goto FULL_DEPLOY
IF ERRORLEVEL 7 goto CHECK_STATUS
IF ERRORLEVEL 6 goto START_SERVICE
IF ERRORLEVEL 5 goto INSTALL_SERVICE
IF ERRORLEVEL 4 goto BUILD_APP
IF ERRORLEVEL 3 goto DEPLOY_CODE
IF ERRORLEVEL 2 goto INSTALL_DEPS
IF ERRORLEVEL 1 goto CHECK_SSH

echo Invalid choice. Please try again.
timeout /t 2 >nul
goto MENU

:CHECK_SSH
echo.
echo Checking SSH connection to %GIT_SERVER%...
ssh %SSH_USER%@%GIT_SERVER% "echo Connection successful"
if %ERRORLEVEL% neq 0 (
    echo Failed to connect to %GIT_SERVER%. Please check your SSH configuration.
    pause
    goto MENU
)
echo SSH connection successful.
pause
goto MENU

:INSTALL_DEPS
echo.
echo Checking for dependencies on Git server...

:: Define required packages
set "PACKAGES=build-essential cmake libgit2-dev libssl-dev libjsoncpp-dev libboost-all-dev libcurl4-openssl-dev"
set "MISSING_PACKAGES="

:: Check each package
for %%p in (%PACKAGES%) do (
    ssh %SSH_USER%@%GIT_SERVER% "dpkg -s %%p &>/dev/null && echo 'installed' || echo 'missing'" > temp.txt
    set /p RESULT=<temp.txt
    echo Preserving temp.txt for reference
    
    if "!RESULT!"=="installed" (
        echo [OK] %%p
    ) else (
        echo [MISSING] %%p
        set "MISSING_PACKAGES=!MISSING_PACKAGES! %%p"
    )
)

:: Special check for JsonCpp header
echo Checking for JsonCpp header file...
ssh %SSH_USER%@%GIT_SERVER% "find /usr -name json.h | grep -q jsoncpp && echo 'found' || echo 'missing'" > temp.txt
set /p JSON_HEADER=<temp.txt
echo Preserving temp.txt for reference

if "!JSON_HEADER!"=="found" (
    echo [OK] JsonCpp header
    :: Get the actual path for future reference
    ssh %SSH_USER%@%GIT_SERVER% "find /usr -name json.h | grep jsoncpp | head -1" > temp.txt
    set /p JSON_HEADER_PATH=<temp.txt
    echo Preserving temp.txt for reference
    echo       Path: !JSON_HEADER_PATH!
) else (
    echo [MISSING] JsonCpp header
    if not "!MISSING_PACKAGES:libjsoncpp-dev=!"=="!MISSING_PACKAGES!" (
        echo JsonCpp package is missing. Will be installed.
    ) else (
        echo JsonCpp package is installed but header is missing. Will reinstall.
        set "MISSING_PACKAGES=!MISSING_PACKAGES! libjsoncpp-dev"
    )
)

echo.
if "%MISSING_PACKAGES%"=="" (
    echo All required dependencies are installed.
) else (
    echo Missing dependencies:
    for %%p in (%MISSING_PACKAGES%) do (
        echo   - %%p
    )
    
    echo.
    set /p install_deps="Install missing dependencies? (y/n): "
    if /i "%install_deps%"=="y" (
        echo Installing dependencies on Git server...
        ssh %SSH_USER%@%GIT_SERVER% "sudo apt update && sudo apt install -y%MISSING_PACKAGES%"
        if %ERRORLEVEL% neq 0 (
            echo Failed to install dependencies.
            pause
            goto MENU
        )
        echo Dependencies installed successfully.
    ) else (
        echo Skipping dependency installation.
        if not "%MISSING_PACKAGES%"=="" (
            echo Warning: Missing dependencies may cause build failures.
        )
    )
)

pause
goto MENU

:DEPLOY_CODE
echo.
echo Deploying source code to Git server...

:: Create a temporary directory in the user's home directory
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p ~/temp_git_repo_service"

:: Copy files using SCP to the temporary directory
scp -r "%SOURCE_DIR%\*" %SSH_USER%@%GIT_SERVER%:~/temp_git_repo_service/

:: Use sudo to create the target directory and copy files
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_DIR% && sudo cp -r ~/temp_git_repo_service/* %REMOTE_DIR%/ && sudo chown -R www-data:www-data %REMOTE_DIR% && echo Preserving temp_git_repo_service directory"

if %ERRORLEVEL% neq 0 (
    echo Failed to copy files to Git server.
    pause
    goto MENU
)
echo Source code deployed successfully.

REM Install systemd service file
echo Installing systemd service file...
scp "%~dp0systemd\git-repo-service.service" %SSH_USER%@%GIT_SERVER%:/tmp/git-repo-service.service
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_SYSTEMD_DIR% && sudo cp /tmp/git-repo-service.service %REMOTE_SYSTEMD_DIR%/ && sudo mv /tmp/git-repo-service.service /etc/systemd/system/ && sudo chown root:root /etc/systemd/system/git-repo-service.service && sudo chmod 644 /etc/systemd/system/git-repo-service.service && sudo systemctl daemon-reload"
echo Systemd service file installed.

pause
goto MENU

:BUILD_APP
echo.
echo Building application on Git server...

:: Create timestamp for log file
for /f "tokens=2 delims=:=" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,8%-%dt:~8,6%"
set "LOG_FILE=build-log-%TIMESTAMP%.txt"
set "REMOTE_LOG_PATH=/tmp/%LOG_FILE%"
set "LOCAL_LOG_DIR=%~dp0logs"

:: Ensure local log directory exists
if not exist "%LOCAL_LOG_DIR%" mkdir "%LOCAL_LOG_DIR%"

:: Run build with detailed logging
echo Running CMake with detailed logging...

:: First verify JsonCpp installation
ssh %SSH_USER%@%GIT_SERVER% "find /usr -name json.h | grep -q jsoncpp || (echo 'ERROR: JsonCpp header file is missing. Please run the dependency installation first.'; exit 1)"

if %ERRORLEVEL% neq 0 (
    echo [91mJsonCpp header files are missing. Please run the dependency installation first.[0m
    pause
    goto MENU
)

:: Get the JsonCpp header path for logging
ssh %SSH_USER%@%GIT_SERVER% "find /usr -name json.h | grep jsoncpp | head -1" > temp.txt
set /p JSON_HEADER_PATH=<temp.txt
echo Preserving temp.txt for reference
echo Using JsonCpp header at: %JSON_HEADER_PATH%

ssh %SSH_USER%@%GIT_SERVER% "cd %REMOTE_DIR% && sudo -u www-data mkdir -p build && cd build && sudo -u www-data cmake -DCMAKE_VERBOSE_MAKEFILE=ON .. > %REMOTE_LOG_PATH% 2>&1 && sudo -u www-data make -j$(nproc) VERBOSE=1 >> %REMOTE_LOG_PATH% 2>&1 && echo 'BUILD_SUCCESS' >> %REMOTE_LOG_PATH% || echo 'BUILD_FAILED' >> %REMOTE_LOG_PATH%"

:: Copy log file back to local machine
echo Copying build log to local machine...
scp %SSH_USER%@%GIT_SERVER%:%REMOTE_LOG_PATH% "%LOCAL_LOG_DIR%\%LOG_FILE%"

:: Check if build was successful by looking for success marker in the log file
findstr /C:"BUILD_SUCCESS" "%LOCAL_LOG_DIR%\%LOG_FILE%" >nul
if %ERRORLEVEL% neq 0 (
    echo [91mFailed to build application. Check log file: %LOCAL_LOG_DIR%\%LOG_FILE%[0m
    pause
    goto MENU
)

:: Display log file location
echo [92mApplication built successfully.[0m
echo Build log saved to: %LOCAL_LOG_DIR%\%LOG_FILE%

REM Copy executable to bin directory
echo Copying executable to bin directory...
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_BIN_DIR% && sudo cp /opt/git-dashboard/git-repo-service/build/git-repo-service %REMOTE_BIN_DIR%/ && sudo chown git:git %REMOTE_BIN_DIR%/git-repo-service && sudo chmod 755 %REMOTE_BIN_DIR%/git-repo-service"
echo Executable copied to bin directory.

pause
goto MENU

:INSTALL_SERVICE
cls
echo =============================================
echo Install Git Repository Service
echo =============================================
echo.

echo Installing service on Git server...

:: Check if service is already installed
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-enabled git-repo-service 2>/dev/null || echo 'not-installed'" > %TEMP%\service_status.txt
set /p SERVICE_STATUS=<%TEMP%\service_status.txt
echo Preserving service_status.txt for reference

if "%SERVICE_STATUS%"=="not-installed" (
    echo Service is not installed
) else (
    echo Service is already installed
)

set /p install_service="Install/update service? (y/n): "
if /i "%install_service%"=="y" (
    :: Copy the systemd service file
    echo Installing systemd service file...
    scp "%~dp0systemd\git-repo-service.service" %SSH_USER%@%GIT_SERVER%:/tmp/git-repo-service.service
    ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_SYSTEMD_DIR% && sudo cp /tmp/git-repo-service.service %REMOTE_SYSTEMD_DIR%/ && sudo mv /tmp/git-repo-service.service /etc/systemd/system/ && sudo chown root:root /etc/systemd/system/git-repo-service.service && sudo chmod 644 /etc/systemd/system/git-repo-service.service && sudo systemctl daemon-reload"
    
    :: Make sure the executable is in the bin directory
    ssh %SSH_USER%@%GIT_SERVER% "if [ ! -f %REMOTE_BIN_DIR%/git-repo-service ]; then echo 'Executable not found in bin directory'; exit 1; fi"
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Executable not found in bin directory. Please build the application first.
        pause
        goto MENU
    )
    
    :: Add www-data to git group if not already added
    ssh %SSH_USER%@%GIT_SERVER% "sudo usermod -a -G git www-data"
    
    :: Enable and start the service
    ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable git-repo-service && sudo systemctl restart git-repo-service"
    
    echo Service installed successfully.
) else (
    echo Service installation skipped.
)

pause
goto MENU

:START_SERVICE
echo.
echo Checking service status...
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active --quiet git-repo-service && echo 'Service is running' || echo 'Service is not running'"

set /p start_service="Start/restart service? (y/n): "
if /i "%start_service%"=="y" (
    echo Starting service on Git server...
    ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable git-repo-service && sudo systemctl restart git-repo-service"
    if %ERRORLEVEL% neq 0 (
        echo Failed to start service.
        pause
        goto MENU
    )
    echo Service started successfully.
) else (
    echo Skipping service start.
)
pause
goto MENU

:CHECK_STATUS
echo.
echo Checking service status...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status git-repo-service"
pause
goto MENU

:CHECK_INSTALLED_VERSIONS
echo.
echo Checking compiler versions on Git server...

:: Create timestamp for log file
for /f "tokens=2 delims=:=" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,8%-%dt:~8,6%"
set "LOG_FILE=versions-log-%TIMESTAMP%.txt"
set "LOCAL_LOG_DIR=%~dp0logs"

:: Ensure local log directory exists
if not exist "%LOCAL_LOG_DIR%" mkdir "%LOCAL_LOG_DIR%"

:: First test a simple SSH command to verify basic connectivity
echo Testing basic SSH connectivity...
ssh %SSH_USER%@%GIT_SERVER% "echo 'SSH connection successful'"
if %ERRORLEVEL% NEQ 0 (
    echo SSH connection failed. Please check your SSH configuration.
    goto :VERSION_CHECK_FAILED
)

:: Run each version check command separately for better reliability
echo Running compiler version checks...

:: Create directory and initialize log file
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p ~/version_check"
ssh %SSH_USER%@%GIT_SERVER% "echo '===== System Information =====' > ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "echo 'Date: ' `date` >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "echo 'Hostname: ' `hostname` >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "echo '' >> ~/version_check/%LOG_FILE%"

:: Check GCC version
ssh %SSH_USER%@%GIT_SERVER% "echo '===== Compiler Versions =====' >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "echo 'GCC Version:' >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "gcc --version >> ~/version_check/%LOG_FILE% 2>&1"
ssh %SSH_USER%@%GIT_SERVER% "echo '' >> ~/version_check/%LOG_FILE%"

:: Check G++ version
ssh %SSH_USER%@%GIT_SERVER% "echo 'G++ Version:' >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "g++ --version >> ~/version_check/%LOG_FILE% 2>&1"
ssh %SSH_USER%@%GIT_SERVER% "echo '' >> ~/version_check/%LOG_FILE%"

:: Check CMake version
ssh %SSH_USER%@%GIT_SERVER% "echo '===== CMake Version =====' >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "echo 'CMake Version:' >> ~/version_check/%LOG_FILE%"
ssh %SSH_USER%@%GIT_SERVER% "cmake --version >> ~/version_check/%LOG_FILE% 2>&1"
ssh %SSH_USER%@%GIT_SERVER% "echo 'Version check completed' >> ~/version_check/%LOG_FILE%"

:: Copy the log file back to the local machine
echo Copying version information to local machine...
scp %SSH_USER%@%GIT_SERVER%:~/version_check/%LOG_FILE% "%LOCAL_LOG_DIR%\%LOG_FILE%"

:: Check if the log file exists locally
if exist "%LOCAL_LOG_DIR%\%LOG_FILE%" (
    echo Saved to: %LOCAL_LOG_DIR%\%LOG_FILE%
    echo.
    type "%LOCAL_LOG_DIR%\%LOG_FILE%"
) else (
    :VERSION_CHECK_FAILED
    echo Log file was not created or could not be copied.
    echo Creating a simple local log file with basic information...
    
    echo ===== Basic System Information ===== > "%LOCAL_LOG_DIR%\%LOG_FILE%"
    echo Date: %DATE% %TIME% >> "%LOCAL_LOG_DIR%\%LOG_FILE%"
    echo Note: Remote version check failed >> "%LOCAL_LOG_DIR%\%LOG_FILE%"
    echo Please check SSH connection and permissions >> "%LOCAL_LOG_DIR%\%LOG_FILE%"
    
    echo Created basic log file: %LOCAL_LOG_DIR%\%LOG_FILE%
    type "%LOCAL_LOG_DIR%\%LOG_FILE%"
)

pause
goto MENU

:FULL_DEPLOY
echo.
echo Running full deployment...

call :CHECK_SSH
if %ERRORLEVEL% neq 0 goto MENU

call :INSTALL_DEPS
call :DEPLOY_CODE
call :BUILD_APP
call :INSTALL_SERVICE
call :START_SERVICE
call :CHECK_STATUS

echo Full deployment completed.
pause
goto MENU

:EXIT
echo.
echo Exiting deployment tool...
endlocal
exit /b 0


