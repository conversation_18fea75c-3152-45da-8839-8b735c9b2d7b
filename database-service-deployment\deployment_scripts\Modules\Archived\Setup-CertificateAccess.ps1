# Setup Certificate Access Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force
# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Function to set up certificate access
function Initialize-CertificateAccess {
    Clear-Host
    Write-Log -Message "========== Setup Certificate Access ==========" -Level "Info" -Component "Certificate"
    Write-Log -Message "               Setup Certificate Access                " -Level "Info" -Component "Certificate"
    Write-Log -Message "========== Setup Certificate Access ==========" -Level "Info" -Component "Certificate"
    Write-Log -Message "" -Level "Info" -Component "Certificate"
    
    # Load configuration if not already loaded
    if ($null -eq $script:Config -or $null -eq $script:Config.ssh) {
        # Look for configuration files
        $configDir = "$PSScriptRoot\..\config"
        
        if (Test-Path -Path $configDir) {
            $configFiles = Get-ChildItem -Path $configDir -Filter "*.json" | Select-Object -ExpandProperty FullName
            
            if ($configFiles.Count -gt 0) {
                # Use the first configuration file found
                $configFile = $configFiles[0]
                Write-Log -Message "Loading configuration from $configFile" -Level "Info" -Component "Certificate"
                
                # Load the configuration file directly
                try {
                    if (Test-Path -Path $configFile) {
                        $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                        
                        # Convert from JSON to PowerShell hashtable
                        $script:Config = @{}
                        $configContent.PSObject.Properties | ForEach-Object {
                            $name = $_.Name
                            $value = $_.Value
                            
                            if ($value -is [System.Management.Automation.PSCustomObject]) {
                                $valueHash = @{}
                                $value.PSObject.Properties | ForEach-Object {
                                    $valueHash[$_.Name] = $_.Value
                                }
                                $script:Config[$name] = $valueHash
                            } else {
                                $script:Config[$name] = $value
                            }
                        }
                        
                        # Make sure the configuration is available in the Common module
                        & {
                            # Get the Common module
                            $commonModule = Get-Module -Name "Common"
                            if ($null -ne $commonModule) {
                                # Export the Config variable to the Common module
                                $commonModule.SessionState.PSVariable.Set("Config", $script:Config)
                            }
                        }
                        
                        $result = $true
                    } else {
                        Write-Log -Message "Configuration file not found: $configFile" -Level "Error" -Component "Certificate"
                        $result = $false
                    }
                } catch {
                    Write-Log -Message "Error loading configuration: $_" -Level "Error" -Component "Certificate"
                    $result = $false
                }
                
                if (-not $result) {
                    Write-Log -Message "Failed to load configuration. Please configure SSH settings first." -Level "Error" -Component "Certificate"
                    Wait-ForUser
                    return
                }
            } else {
                Write-Log -Message "No configuration files found in $configDir" -Level "Error" -Component "Certificate"
                Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Certificate"
                Wait-ForUser
                return
            }
        } else {
            Write-Log -Message "Configuration directory not found: $configDir" -Level "Error" -Component "Certificate"
            Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Certificate"
            Wait-ForUser
            return
        }
    }
    
    Write-Log -Message "Setting up SSL certificate access..." -Level "Info" -Component "Certificate"
    
    # Check if the certificate setup script exists in the deployment directory
    $scriptPath = "D:\Augment\project-tracker\database-service-deployment\setup-certificate-access.sh"
    
    if (-not (Test-Path -Path $scriptPath)) {
        Write-Log -Message "Certificate setup script not found at: $scriptPath" -Level "Error" -Component "Certificate"
        Write-Log -Message "Please make sure the script exists before proceeding." -Level "Error" -Component "Certificate"
        Wait-ForUser
        Show-MainMenu
        return
    }
    
    # Upload the script to the server
    $remotePath = "/home/<USER>/setup-certificate-access.sh"
    
    try {
        # Build SCP command with flexible key handling
        $scpArgs = @()
        $sshKeyPath = $script:Config.ssh.key_path
        
        # Only add the key path if it's specified and exists
        if (-not [string]::IsNullOrWhiteSpace($sshKeyPath) -and (Test-Path $sshKeyPath)) {
            $scpArgs += @("-i", "$sshKeyPath") 
        }
        
        # Add port if not default
        if ($script:Config.ssh.port -ne 22) {
            $scpArgs += @("-P", "$($script:Config.ssh.port)")
        }
        
        # Add common options
        $scpArgs += @(
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null"
        )
        
        # Add source and destination
        $scpArgs += @(
            "$scriptPath",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host):$remotePath"
        )
        
        # Execute SCP command
        $process = Start-Process -FilePath "scp" -ArgumentList $scpArgs -NoNewWindow -PassThru -Wait
        
        if ($process.ExitCode -eq 0) {
            Write-Log -Message "Certificate setup script uploaded successfully." -Level "Info" -Component "Certificate"
            
            # Make the script executable and run it with sudo
            $chmodCmd = "chmod +x $remotePath"
            $result = Invoke-RemoteCommand -Command $chmodCmd -Silent
            
            $runCmd = "sudo $remotePath"
            $result = Invoke-RemoteCommand -Command $runCmd
            
            # Filter out the SSH warning message
            $filteredResult = $result -replace "Warning: Permanently added '[^']+' \([^)]+\) to the list of known hosts\.\s*", ""
            
            Write-Log -Message $filteredResult -Level "Info" -Component "Certificate"
            Write-Log -Message "SSL certificate access configured successfully." -Level "Info" -Component "Certificate"
        } else {
            Write-Log -Message "Failed to upload certificate setup script." -Level "Error" -Component "Certificate"
            Wait-ForUser
            Show-MainMenu
            return
        }
    } catch {
        Write-Log -Message "Error configuring certificate access: $_" -Level "Error" -Component "Certificate"
        Wait-ForUser
        Show-MainMenu
        return
    }
    
    Write-Log -Message "" -Level "Info" -Component "Certificate"
    Write-Log -Message "Press Enter to return to the main menu..." -Level "Info" -Component "Certificate"
    Wait-ForUser
    Show-MainMenu
}

# Run the function
Initialize-CertificateAccess
