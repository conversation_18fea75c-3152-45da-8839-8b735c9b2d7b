#!/bin/bash

# Exit on any error
set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script as root"
    exit 1
fi

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

# Configuration variables
INSTALL_DIR="/opt/git-dashboard"
NGINX_CONF="/etc/nginx/sites-available/git-dashboard"
DOMAIN="git.chcit.org"
BIND_IP="0.0.0.0"  # Default to all interfaces
BIND_PORT="8000"  # Internal Flask port

# Function to log messages
log() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Function to log success
success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Function to log errors
error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Check if Nginx is installed
if ! command -v nginx &> /dev/null; then
    error "Nginx is not installed. Please install it first."
    exit 1
fi

# Create required directories
log "Creating required directories..."
mkdir -p ${INSTALL_DIR}/static
mkdir -p ${INSTALL_DIR}/data/history
mkdir -p ${INSTALL_DIR}/logs
mkdir -p /var/log/nginx

# Create Nginx configuration with enhanced security
log "Creating Nginx configuration with enhanced security..."
cat > ${NGINX_CONF} << EOL
# Define rate limiting zones
limit_req_zone \$binary_remote_addr zone=api_limit:10m rate=5r/s;

server {
    # Redirect HTTP to HTTPS
    listen 80;
    server_name ${DOMAIN};
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ${DOMAIN};

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/chcit.org/chain.pem;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;

    # HSTS (ngx_http_headers_module is required) (63072000 seconds = 2 years)
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; img-src 'self' data:;" always;

    # Logging configuration
    access_log /var/log/nginx/git-dashboard.access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/git-dashboard.error.log warn;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;

    # JavaScript and CSS files
    location ~* \.(js|css)\$ {
        expires 1d;
        add_header Cache-Control "public, no-transform";
        access_log off;
        gzip_static on;
    }

    # Frontend Routes
    location / {
        try_files \$uri \$uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, no-transform";
        root ${INSTALL_DIR};
        index index.html;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # API Routes (Flask backend)
    location /api {
        proxy_pass http://127.0.0.1:${BIND_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Rate limiting
        limit_req zone=api_limit burst=20 nodelay;
        limit_req_status 429;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Metrics history endpoint with caching
    location /api/metrics/history {
        proxy_pass http://127.0.0.1:${BIND_PORT};
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        proxy_cache_valid 200 5m;
        add_header X-Cache-Status \$upstream_cache_status;
        
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Static Files
    location /static/ {
        alias ${INSTALL_DIR}/static/;
        expires 1d;
        add_header Cache-Control "public, no-transform";
        access_log off;

        # Compression
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        gzip_min_length 1000;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root ${INSTALL_DIR}/static;
    }
}
EOL

# Create symbolic link for Nginx
log "Creating symbolic link for Nginx configuration..."
ln -sf ${NGINX_CONF} /etc/nginx/sites-enabled/git-dashboard

# Remove default Nginx site
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    log "Removing default Nginx site..."
    rm -f /etc/nginx/sites-enabled/default
fi

# Create systemd service for Flask app with enhanced security and resource limits
log "Creating systemd service for Flask app with security enhancements..."
cat << EOL | tee /etc/systemd/system/git-dashboard.service
[Unit]
Description=Git Dashboard Flask Application
After=network.target nginx.service
Requires=nginx.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=${INSTALL_DIR}
Environment="FLASK_APP=app.py"
Environment="FLASK_ENV=production"
Environment="PYTHONUNBUFFERED=1"
Environment="BIND_IP=${BIND_IP}"
Environment="BIND_PORT=${BIND_PORT}"
ExecStart=${INSTALL_DIR}/venv/bin/gunicorn \
    --workers 3 \
    --bind ${BIND_IP}:${BIND_PORT} \
    --access-logfile ${INSTALL_DIR}/logs/gunicorn.access.log \
    --error-logfile ${INSTALL_DIR}/logs/gunicorn.error.log \
    --capture-output \
    --log-level info \
    app:app
Restart=always
RestartSec=3

# Security enhancements
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
RestrictNamespaces=yes
RestrictRealtime=yes

# Resource limits
LimitNOFILE=65535
CPUQuota=50%
MemoryMax=256M

[Install]
WantedBy=multi-user.target
EOL

# Set up cron job for metrics collection
log "Setting up cron job for metrics collection..."
cat > /etc/cron.d/git-dashboard-metrics << EOL
# Collect metrics every minute
* * * * * root ${INSTALL_DIR}/collect-metrics.sh >> ${INSTALL_DIR}/logs/metrics.log 2>&1
EOL
chmod 644 /etc/cron.d/git-dashboard-metrics

# Set proper permissions
log "Setting proper permissions..."
chown -R root:root ${INSTALL_DIR}
chmod -R 755 ${INSTALL_DIR}
find ${INSTALL_DIR} -type f -exec chmod 644 {} \;
chmod +x ${INSTALL_DIR}/collect-metrics.sh

# Create log files if they don't exist
touch /var/log/nginx/git-dashboard.access.log /var/log/nginx/git-dashboard.error.log
chmod 644 /var/log/nginx/git-dashboard.access.log /var/log/nginx/git-dashboard.error.log

# Configure firewall
log "Configuring firewall..."
if command -v ufw &> /dev/null; then
    ufw allow ssh
    ufw allow http
    ufw allow https
    
    # Only enable if not already enabled to avoid prompting
    if ! ufw status | grep -q "Status: active"; then
        echo "y" | ufw enable
    fi
else
    log "UFW not installed. Skipping firewall configuration."
fi

# Test Nginx configuration
log "Testing Nginx configuration..."
if nginx -t; then
    success "Nginx configuration test passed."
    
    # Reload Nginx to apply new configuration
    log "Reloading Nginx..."
    systemctl reload nginx
    
    # Start and enable the Git Dashboard service
    log "Starting Git Dashboard service..."
    systemctl daemon-reload
    systemctl enable git-dashboard.service
    systemctl restart git-dashboard.service
    
    success "Git Dashboard with Nginx setup complete!"
    success "Access the dashboard at: https://${DOMAIN}"
    
    # Verify services are running
    if systemctl is-active --quiet nginx && systemctl is-active --quiet git-dashboard; then
        success "All services are running correctly."
    else
        error "One or more services failed to start. Please check the logs."
        systemctl status nginx.service
        systemctl status git-dashboard.service
    fi
else
    error "Nginx configuration test failed. Please check your configuration."
    exit 1
fi
