export module database.api;

// Import the core module
import database.core;

// Standard library imports
import <string>;
import <memory>;
import <vector>;
import <unordered_map>;
import <functional>;
import <atomic>;
import <thread>;

// Forward declarations for Boost
export namespace boost {
    namespace asio {
        class io_context;
        namespace ip {
            class tcp;
        }
    }
    namespace beast {
        namespace http {
            template<class Body, class Fields = fields>
            class message;

            struct string_body;
        }
    }
}

// API module
export namespace dbservice::api {

    // API configuration
    export struct ApiConfig {
        unsigned short port = 8080;
        std::string host = "127.0.0.1";
        std::string basePath = "/api";
        bool enableCors = true;
        bool enableAuthentication = true;
        std::string jwtSecret;
    };

    // HTTP request handler type
    using HttpHandler = std::function<void(
        boost::beast::http::message<boost::beast::http::string_body>&,
        boost::beast::http::message<boost::beast::http::string_body>&
    )>;

    // API server
    export class ApiServer {
    public:
        /**
         * @brief Constructor
         * @param port Port to listen on
         * @param connectionManager Database connection manager
         * @param securityManager Security manager
         */
        ApiServer(unsigned short port,
                 std::shared_ptr<core::ConnectionManager> connectionManager,
                 std::shared_ptr<security::SecurityManager> securityManager);

        /**
         * @brief Destructor
         */
        ~ApiServer();

        /**
         * @brief Start the API server (synchronous)
         */
        void start();

        /**
         * @brief Start the API server (asynchronous)
         * @return Task that completes when the server has started
         */
        core::Task<core::Result<void>> startAsync();

        /**
         * @brief Stop the API server (synchronous)
         */
        void stop();

        /**
         * @brief Stop the API server (asynchronous)
         * @return Task that completes when the server has stopped
         */
        core::Task<core::Result<void>> stopAsync();

        /**
         * @brief Check if the server is running
         * @return True if the server is running
         */
        bool isRunning() const;

    private:
        /**
         * @brief Set up API endpoints
         */
        void setupEndpoints();

        /**
         * @brief Accept incoming connections
         */
        void doAccept();

        /**
         * @brief Handle HTTP request
         * @param socket TCP socket
         */
        void handleRequest(boost::asio::ip::tcp::socket socket);

        /**
         * @brief Parse query parameters
         * @param query Query string
         * @return Map of query parameters
         */
        std::unordered_map<std::string, std::string> parseQueryParams(const std::string& query);

        // API endpoint handlers
        void handleQuery(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        void handleExecute(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        void handleTransaction(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        void handleSchema(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        void handleStatus(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        // Asynchronous endpoint handlers
        core::Task<void> handleQueryAsync(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        core::Task<void> handleExecuteAsync(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        core::Task<void> handleTransactionAsync(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        core::Task<void> handleSchemaAsync(
            boost::beast::http::message<boost::beast::http::string_body>& req,
            boost::beast::http::message<boost::beast::http::string_body>& res
        );

        unsigned short port_;
        std::shared_ptr<core::ConnectionManager> connectionManager_;
        std::shared_ptr<security::SecurityManager> securityManager_;

        boost::asio::io_context ioc_;
        boost::asio::ip::tcp::acceptor acceptor_;
        std::thread acceptorThread_;

        std::unordered_map<std::string, HttpHandler> handlers_;
        std::atomic<bool> running_{false};
    };

} // namespace dbservice::api
