#include "api/api_server.hpp"
#include "services/logging_service.hpp"
#include "services/log_discovery_service.hpp"
#include "storage/storage_manager.hpp"
#include "storage/memory_storage.hpp"
#include "storage/database_storage.hpp"
#include "storage/file_storage.hpp"
#include "parsers/parser_manager.hpp"
#include "parsers/syslog_parser.hpp"
#include "parsers/nginx_parser.hpp"
#include "parsers/git_parser.hpp"
#include "parsers/dashboard_parser.hpp"
#include "utils/config_validator.hpp"
#include <iostream>
#include <csignal>
#include <memory>
#include <thread>
#include <atomic>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <jsoncpp/json/json.h>

std::unique_ptr<ApiServer> server;
std::atomic<bool> running{true};

void signalHandler(int signal) {
    std::cout << "Received signal " << signal << ", shutting down..." << std::endl;
    running = false;
    if (server) {
        server->stop();
    }
}

int main() {
    try {
        // Register signal handlers
        std::signal(SIGINT, signalHandler);
        std::signal(SIGTERM, signalHandler);

        std::cout << "Git Repository Logging Agent starting..." << std::endl;

        // Get config path from environment variable or use default
        const char* configPathEnv = std::getenv("CONFIG_PATH");
        std::string configPath = configPathEnv ? configPathEnv : "/etc/git-dashboard/logging-config.json";

        // Get log sources path from environment variable or use default
        const char* logPathEnv = std::getenv("LOG_SOURCES_PATH");
        std::string logPath = logPathEnv ? logPathEnv : "/var/log";

        // Get server port from environment variable or use default
        const char* portEnv = std::getenv("SERVER_PORT");
        unsigned short port = portEnv ? static_cast<unsigned short>(std::stoi(portEnv)) : 8081;

        // Get log level from environment variable or use default
        const char* logLevelEnv = std::getenv("LOG_LEVEL");
        std::string logLevel = logLevelEnv ? logLevelEnv : "info";

        std::cout << "Using config path: " << configPath << std::endl;
        std::cout << "Using log sources path: " << logPath << std::endl;
        std::cout << "Server port: " << port << std::endl;
        std::cout << "Log level: " << logLevel << std::endl;

        // Load and validate configuration
        Json::Value config;

        // Create default configuration if it doesn't exist
        if (!std::filesystem::exists(configPath)) {
            std::cout << "Config file not found, creating default configuration..." << std::endl;

            // Create parent directory if it doesn't exist
            std::filesystem::create_directories(std::filesystem::path(configPath).parent_path());

            // Create default configuration
            Json::Value config;

            // Memory tier configuration
            config["tiers"]["memory"]["enabled"] = true;
            config["tiers"]["memory"]["max_entries"] = 1000;
            config["tiers"]["memory"]["max_age_minutes"] = 60;

            // Database tier configuration
            config["tiers"]["database"]["enabled"] = false;
            config["tiers"]["database"]["connection_string"] = "postgresql://username:password@localhost/gitdashboard";
            config["tiers"]["database"]["structured_only"] = true;

            // File tier configuration
            config["tiers"]["file"]["enabled"] = true;
            config["tiers"]["file"]["base_directory"] = "/var/log/git-dashboard/logs";
            config["tiers"]["file"]["compression"] = true;
            config["tiers"]["file"]["compression_age_days"] = 7;

            // Retention configuration
            config["retention"]["system"] = 30;
            config["retention"]["security"] = 90;
            config["retention"]["application"] = 30;
            config["retention"]["database"] = 15;
            config["retention"]["custom"] = 30;

            // Log sources configuration
            config["sources"]["system"]["enabled"] = true;
            config["sources"]["system"]["path"] = "/var/log/syslog";
            config["sources"]["system"]["parser"] = "syslog";

            config["sources"]["auth"]["enabled"] = true;
            config["sources"]["auth"]["path"] = "/var/log/auth.log";
            config["sources"]["auth"]["parser"] = "syslog";

            config["sources"]["nginx"]["enabled"] = true;
            config["sources"]["nginx"]["path"] = "/var/log/nginx/error.log";
            config["sources"]["nginx"]["parser"] = "nginx";

            config["sources"]["git"]["enabled"] = true;
            config["sources"]["git"]["path"] = "/var/log/git";
            config["sources"]["git"]["parser"] = "git";

            config["sources"]["dashboard"]["enabled"] = true;
            config["sources"]["dashboard"]["path"] = "/var/log/git-dashboard.log";
            config["sources"]["dashboard"]["parser"] = "dashboard";

            // Write configuration to file
            std::ofstream configFile(configPath);
            if (configFile) {
                Json::StreamWriterBuilder writer;
                configFile << Json::writeString(writer, config);
                std::cout << "Default configuration created at " << configPath << std::endl;
            } else {
                std::cerr << "Failed to create default configuration file" << std::endl;
            }
        } else {
            // Validate existing configuration
            std::cout << "Validating configuration file: " << configPath << std::endl;
            if (!logging::ConfigValidator::validateMainConfig(configPath, config)) {
                std::cerr << "Configuration validation found issues, but will attempt to continue" << std::endl;

                // Write corrected configuration back to file
                std::ofstream configFile(configPath);
                if (configFile) {
                    Json::StreamWriterBuilder writer;
                    configFile << Json::writeString(writer, config);
                    std::cout << "Updated configuration saved to " << configPath << std::endl;
                } else {
                    std::cerr << "Failed to save updated configuration file" << std::endl;
                }
            } else {
                std::cout << "Configuration validation successful" << std::endl;
            }
        }

        // Initialize parser manager
        auto parserManager = std::make_shared<logging::ParserManager>();

        // Register parsers
        parserManager->registerParser(std::make_shared<logging::SyslogParser>());
        parserManager->registerParser(std::make_shared<logging::NginxParser>());
        parserManager->registerParser(std::make_shared<logging::GitParser>());
        parserManager->registerParser(std::make_shared<logging::DashboardParser>());

        // Initialize storage manager
        auto storageManager = std::make_shared<logging::StorageManager>();

        // Get storage configuration path
        const char* storageConfigPathEnv = std::getenv("STORAGE_CONFIG_PATH");
        std::string storageConfigPath = storageConfigPathEnv ? storageConfigPathEnv : "/opt/git-dashboard/config/storage-config.json";

        // Check if storage config file exists
        if (!fs::exists(storageConfigPath)) {
            std::cerr << "Storage configuration file not found: " << storageConfigPath << std::endl;
            std::cerr << "Using default configuration" << std::endl;
            storageConfigPath = configPath; // Fall back to default config
        } else {
            // Validate storage configuration
            Json::Value storageConfig;
            std::cout << "Validating storage configuration file: " << storageConfigPath << std::endl;
            if (!logging::ConfigValidator::validateStorageConfig(storageConfigPath, storageConfig)) {
                std::cerr << "Storage configuration validation found issues, but will attempt to continue" << std::endl;

                // Write corrected configuration back to file
                std::ofstream storageConfigFile(storageConfigPath);
                if (storageConfigFile) {
                    Json::StreamWriterBuilder writer;
                    storageConfigFile << Json::writeString(writer, storageConfig);
                    std::cout << "Updated storage configuration saved to " << storageConfigPath << std::endl;
                } else {
                    std::cerr << "Failed to save updated storage configuration file" << std::endl;
                }
            } else {
                std::cout << "Storage configuration validation successful" << std::endl;
            }
        }

        // Initialize storage manager with configuration
        if (!storageManager->initialize(storageConfigPath)) {
            std::cerr << "Failed to initialize storage manager" << std::endl;
            return 1;
        }

        // Create logging service
        auto loggingService = std::make_shared<LoggingService>(
            logPath,
            logLevel,
            parserManager,
            storageManager
        );

        // Create and start API server
        server = std::make_unique<ApiServer>(port, loggingService);

        std::cout << "Starting Git Repository Logging Agent on port " << port << "..." << std::endl;
        server->start();

        std::cout << "Server started, running until terminated..." << std::endl;

        // Keep the main thread alive until signaled to stop
        while (running) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        std::cout << "Git Repository Logging Agent shutting down..." << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
