#pragma once
#include <string>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <expected>
#include <source_location>
#include <span>

namespace dbservice {

// Forward declarations
namespace api {
    class ApiServer;
}

namespace core {
    class ConnectionManager;
}

namespace schema {
    class SchemaManager;
}

namespace security {
    class SecurityManager;
}

/**
 * @class DatabaseService
 * @brief Main service class for the database service
 */
class DatabaseService {
public:
    /**
     * @brief Constructor
     */
    DatabaseService();

    /**
     * @brief Destructor
     */
    ~DatabaseService();

    /**
     * @brief Initialize the service
     * @return Result of initialization, containing error message if failed
     */
    std::expected<void, std::string> initialize();

    /**
     * @brief Load configuration from file
     * @param configFile Path to configuration file
     * @return Result of loading configuration, containing error message if failed
     */
    std::expected<void, std::string> loadConfig(const std::string& configFile);

    /**
     * @brief Start the service
     * @return Result of starting the service, containing error message if failed
     */
    std::expected<void, std::string> start();

    /**
     * @brief Stop the service
     */
    void stop();

    /**
     * @brief Check if the service is running
     * @return True if the service is running
     */
    bool isRunning() const;

    /**
     * @brief Wait for the service to stop
     */
    void wait();

    /**
     * @brief Get the API server
     * @return API server
     */
    std::shared_ptr<api::ApiServer> getApiServer() const;

    /**
     * @brief Get the connection manager
     * @return Connection manager
     */
    std::shared_ptr<core::ConnectionManager> getConnectionManager() const;

    /**
     * @brief Get the schema manager
     * @return Schema manager
     */
    std::shared_ptr<schema::SchemaManager> getSchemaManager() const;

    /**
     * @brief Get the security manager
     * @return Security manager
     */
    std::shared_ptr<security::SecurityManager> getSecurityManager() const;

private:
    /**
     * @brief Initialize the database
     * @return True if initialization was successful
     */
    bool initializeDatabase();

    /**
     * @brief Initialize the API server
     * @return True if initialization was successful
     */
    bool initializeApiServer();

    /**
     * @brief Log a message
     * @param level Log level
     * @param message Message to log
     */
    void log(const std::string& level, const std::string& message);

    // Configuration
    std::string connectionString_;
    size_t connectionPoolSize_;
    bool useSSL_;
    unsigned short apiPort_;
    std::string apiHost_;
    std::string apiBasePath_;
    bool enableAuthentication_;
    std::string jwtSecret_;
    bool enableLogging_;
    std::string logLevel_;
    std::string logFile_;

    // Components
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<schema::SchemaManager> schemaManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;
    std::shared_ptr<api::ApiServer> apiServer_;

    // State
    bool initialized_;
    bool running_;
    std::mutex mutex_;
    std::condition_variable cv_;
    std::mutex waitMutex_;
    std::condition_variable waitCondition_;
};

} // namespace dbservice
