#pragma once
#include <string>
#include <unordered_map>
#include <mutex>

namespace dbservice::utils {

/**
 * @class ConfigManager
 * @brief Manages configuration for the database service
 */
class ConfigManager {
public:
    /**
     * @brief Get the singleton instance
     * @return Singleton instance
     */
    static ConfigManager& getInstance();

    /**
     * @brief Load configuration from a file
     * @param filename File to load
     * @return True if configuration was loaded successfully
     */
    bool loadFromFile(const std::string& filename);

    /**
     * @brief Get a string value
     * @param key Key to get
     * @param defaultValue Default value if key is not found
     * @return Value
     */
    std::string getString(const std::string& key, const std::string& defaultValue = "") const;

    /**
     * @brief Get an integer value
     * @param key Key to get
     * @param defaultValue Default value if key is not found
     * @return Value
     */
    int getInt(const std::string& key, int defaultValue = 0) const;

    /**
     * @brief Get a boolean value
     * @param key Key to get
     * @param defaultValue Default value if key is not found
     * @return Value
     */
    bool getBool(const std::string& key, bool defaultValue = false) const;

    /**
     * @brief Get a double value
     * @param key Key to get
     * @param defaultValue Default value if key is not found
     * @return Value
     */
    double getDouble(const std::string& key, double defaultValue = 0.0) const;

    /**
     * @brief Set a string value
     * @param key Key to set
     * @param value Value to set
     */
    void setString(const std::string& key, const std::string& value);

    /**
     * @brief Set an integer value
     * @param key Key to set
     * @param value Value to set
     */
    void setInt(const std::string& key, int value);

    /**
     * @brief Set a boolean value
     * @param key Key to set
     * @param value Value to set
     */
    void setBool(const std::string& key, bool value);

    /**
     * @brief Set a double value
     * @param key Key to set
     * @param value Value to set
     */
    void setDouble(const std::string& key, double value);

private:
    /**
     * @brief Constructor
     */
    ConfigManager();

    /**
     * @brief Destructor
     */
    ~ConfigManager();

    /**
     * @brief Copy constructor (deleted)
     */
    ConfigManager(const ConfigManager&) = delete;

    /**
     * @brief Assignment operator (deleted)
     */
    ConfigManager& operator=(const ConfigManager&) = delete;

    std::unordered_map<std::string, std::string> config_;
    mutable std::mutex mutex_;
};

} // namespace dbservice::utils
