#pragma once
#include "../models/log_entry.hpp"
#include <string>
#include <vector>
#include <optional>
#include <chrono>
#include <unordered_map>

namespace logging {

// Query parameters for log retrieval
struct LogQueryParams {
    std::string source = "all";
    std::string level = "all";
    std::optional<std::chrono::system_clock::time_point> startTime;
    std::optional<std::chrono::system_clock::time_point> endTime;
    std::string searchText;
    int limit = 0;
    int offset = 0;
    bool includeMetadata = true;
    std::vector<std::string> includeSources;
    std::vector<std::string> excludeSources;
};

// Storage statistics
struct StorageStats {
    size_t totalEntries = 0;
    std::unordered_map<std::string, size_t> bySource;
    std::unordered_map<LogType, size_t> byLevel;
    std::optional<std::chrono::system_clock::time_point> oldestEntry;
    std::optional<std::chrono::system_clock::time_point> newestEntry;
    size_t storageSize = 0;   // In bytes
};

// Storage tier interface
class StorageTier {
public:
    virtual ~StorageTier() = default;
    
    // Store a log entry
    virtual bool store(const LogEntry& entry) = 0;
    
    // Store multiple log entries in batch
    virtual bool storeBatch(const std::vector<LogEntry>& entries) = 0;
    
    // Query logs with filtering
    virtual std::vector<LogEntry> query(const LogQueryParams& params) = 0;
    
    // Get storage statistics
    virtual StorageStats getStats() = 0;
    
    // Maintenance operations
    virtual bool prune(std::chrono::system_clock::time_point olderThan) = 0;
    virtual bool compact() = 0;
    virtual bool backup(const std::string& backupPath) = 0;
    
    // Check if this tier can handle this log entry
    virtual bool canHandle(const LogEntry& entry) const = 0;
    
    // Get tier name
    virtual std::string getName() const = 0;
    
    // Get tier priority (lower number = higher priority)
    virtual int getPriority() const = 0;
};

} // namespace logging
