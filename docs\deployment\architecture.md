# Deployment Architecture

## Multi-Server Deployment Overview

The Project Tracker uses a two-server deployment architecture to separate frontend and backend responsibilities:

```mermaid
graph TB
    BuildServer[Windows Server 2022<br>Build Server] --> |Deploy Frontend| FrontendServer[Ubuntu 24.04.2 LTS<br>Frontend Server]
    BuildServer --> |Deploy Backend| BackendServer[Ubuntu 24.04.2 LTS<br>Backend Server]
    FrontendServer --> |API Requests| BackendServer
    FrontendServer <--> |WebSocket| BackendServer
```

## Build Server (Windows Server 2022)

The build server is responsible for:

1. **Preparing Deployment Packages**
   - Building frontend React/TypeScript application
   - Packaging backend Python application
   - Creating separate deployment packages for each server

2. **Running Validation Tests**
   - Verifying package integrity
   - Validating configuration files
   - Running automated tests

3. **Deploying to Target Servers**
   - Using secure SSH connections
   - Performing deployment steps
   - Validating successful deployment

## Target Servers

### Frontend Server (Ubuntu 24.04.2 LTS)

Environment requirements:
- Node.js 16+
- Nginx with Brotli compression
- 2GB RAM minimum
- 20GB storage

Server responsibilities:
- Serving compiled React application
- Handling client-side routing
- Managing WebSocket client connections
- Providing frontend monitoring

### Backend Server (Ubuntu 24.04.2 LTS)

Environment requirements:
- Python 3.10+
- PostgreSQL 14+
- Redis 5.0.1+
- Nginx
- 4GB RAM minimum
- 50GB storage

Server responsibilities:
- Running Flask application
- Managing database connections
- Handling WebSocket server operations
- Processing business logic

## Deployment Process

1. **Build Phase**
   - Frontend: `npm run build`
   - Backend: Package Python application

2. **Validation Phase**
   - Run `validate-build.ps1` to verify package integrity
   - Check configuration files

3. **Deployment Phase**
   - Use `prepare-deployment.ps1` to create deployment packages
   - Use `auto-deploy.ps1` to deploy to target servers

4. **Verification Phase**
   - Verify successful deployment
   - Run health checks
   - Validate services are running
