# Start Real-Time Auto-Commit <PERSON>
# This script launches the real-time auto-commit monitor in a separate window

# Get the directory of this script
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Path to the real-time commit script
$realTimeCommitScript = Join-Path -Path $scriptDir -ChildPath "real-time-commit.ps1"

# Launch the real-time commit script in a new PowerShell window
Start-Process powershell.exe -ArgumentList "-NoExit", "-File", "`"$realTimeCommitScript`""

Write-Host "Started real-time auto-commit monitoring in a separate window." -ForegroundColor Green
Write-Host "The monitor will automatically commit and push changes after a brief period of inactivity." -ForegroundColor Cyan
