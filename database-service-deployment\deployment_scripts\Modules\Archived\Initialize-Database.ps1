# Initialize Database Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Initialize database function
function Initialize-Database {
    Clear-Host
    
    # Enable UI Mode for menu display
    Enable-UIMode
    
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Initialize Database                    " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    
    # Disable UI Mode after menu display
    Disable-UIMode
    
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Database"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    
    # Check if PostgreSQL is installed and running
    $checkPgCmd = "systemctl is-active postgresql"
    $pgStatus = Invoke-RemoteCommand -Command $checkPgCmd -Silent
    
    if ($pgStatus -ne "active") {
        Write-Log -Message "PostgreSQL is not running." -Level "Error" -Component "Database"
        Write-Log -Message "Please install and start PostgreSQL first." -Level "Warning" -Component "Database"
        Wait-ForUser
        & "$PSScriptRoot\Install-Dependencies.ps1"
        return
    }
    
    # Get database configuration
    $dbName = $Config.database.name
    $dbUser = $Config.database.user
    $dbPassword = $Config.database.password
    
    if ([string]::IsNullOrWhiteSpace($dbPassword)) {
        Write-Log -Message "Database password is not set." -Level "Error" -Component "Database"
        $password = Read-Host "Enter database password" -AsSecureString
        $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))
        
        if ([string]::IsNullOrWhiteSpace($plainPassword)) {
            Write-Log -Message "Password cannot be empty." -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        }
        
        $Config.database.password = $plainPassword
        Save-Configuration
        $dbPassword = $plainPassword
    }
    
    # Check if the database user exists
    $checkUserCmd = @"
sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$dbUser'" | wc -l
"@
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    
    if ($userExists -eq "0") {
        Write-Log -Message "Creating database user $dbUser..." -Level "Info" -Component "Database"
        $createUserCmd = @"
sudo -u postgres psql -c "CREATE USER $dbUser WITH PASSWORD '$dbPassword'" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $createUserCmd
        
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create database user: $result" -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        } else {
            Write-Log -Message "Database user $dbUser created successfully!" -Level "Success" -Component "Database"
        }
    } else {
        Write-Log -Message "Database user $dbUser already exists." -Level "Info" -Component "Database"
        
        # Update the user's password
        $updatePasswordCmd = @"
sudo -u postgres psql -c "ALTER USER $dbUser WITH PASSWORD '$dbPassword'" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $updatePasswordCmd
        
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to update user password: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Updated password for user $dbUser." -Level "Success" -Component "Database"
        }
    }
    
    # Check if the database exists
    $checkDbCmd = @"
sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='$dbName'" | wc -l
"@
    $dbExists = Invoke-RemoteCommand -Command $checkDbCmd -Silent
    
    if ($dbExists -eq "0") {
        Write-Log -Message "Creating database $dbName..." -Level "Info" -Component "Database"
        $createDbCmd = @"
sudo -u postgres psql -c "CREATE DATABASE $dbName OWNER $dbUser" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $createDbCmd
        
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create database: $result" -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        } else {
            Write-Log -Message "Database $dbName created successfully!" -Level "Success" -Component "Database"
        }
    } else {
        Write-Log -Message "Database $dbName already exists." -Level "Info" -Component "Database"
        
        # Check if we need to change the owner
        $checkOwnerCmd = @"
sudo -u postgres psql -tAc "SELECT pg_catalog.pg_get_userbyid(d.datdba) FROM pg_catalog.pg_database d WHERE d.datname = '$dbName'" | tr -d ' '
"@
        $currentOwner = Invoke-RemoteCommand -Command $checkOwnerCmd -Silent
        
        if ($currentOwner -ne $dbUser) {
            Write-Log -Message "Changing database owner to $dbUser..." -Level "Info" -Component "Database"
            $changeOwnerCmd = @"
sudo -u postgres psql -c "ALTER DATABASE $dbName OWNER TO $dbUser" 2>&1
"@
            $result = Invoke-RemoteCommand -Command $changeOwnerCmd
            
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to change database owner: $result" -Level "Error" -Component "Database"
            } else {
                Write-Log -Message "Changed database owner to $dbUser." -Level "Success" -Component "Database"
            }
        }
    }
    
    # Setup specialized database configurations based on database type
    Initialize-SpecializedDatabase -DatabaseName $dbName
    
    # Check if we need to initialize the database schema
    $installDir = $Config.project.install_dir
    $checkSchemaCmd = "test -f $installDir/schema.sql && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $schemaExists = Invoke-RemoteCommand -Command $checkSchemaCmd -Silent
    
    if ($schemaExists -eq "EXISTS") {
        Write-Log -Message "Found database schema file." -Level "Info" -Component "Database"
        $initSchema = Read-Host "Do you want to initialize the database schema? (y/n)"
        
        if ($initSchema -eq "y") {
            Write-Log -Message "Initializing database schema..." -Level "Info" -Component "Database"
            $initSchemaCmd = "PGPASSWORD='$dbPassword' psql -h localhost -U $dbUser -d $dbName -f $installDir/schema.sql 2>&1"
            $result = Invoke-RemoteCommand -Command $initSchemaCmd
            
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to initialize database schema: $result" -Level "Error" -Component "Database"
            } else {
                Write-Log -Message "Database schema initialized successfully!" -Level "Success" -Component "Database"
            }
        }
    } else {
        Write-Log -Message "No schema.sql file found in $installDir." -Level "Warning" -Component "Database"
        Write-Log -Message "You may need to manually initialize the database schema." -Level "Warning" -Component "Database"
    }
    
    # Check if we need to load initial data
    $checkDataCmd = "test -f $installDir/data.sql && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $dataExists = Invoke-RemoteCommand -Command $checkDataCmd -Silent
    
    if ($dataExists -eq "EXISTS") {
        Write-Log -Message "Found initial data file." -Level "Info" -Component "Database"
        $loadData = Read-Host "Do you want to load initial data? (y/n)"
        
        if ($loadData -eq "y") {
            Write-Log -Message "Loading initial data..." -Level "Info" -Component "Database"
            $loadDataCmd = "PGPASSWORD='$dbPassword' psql -h localhost -U $dbUser -d $dbName -f $installDir/data.sql 2>&1"
            $result = Invoke-RemoteCommand -Command $loadDataCmd
            
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to load initial data: $result" -Level "Error" -Component "Database"
            } else {
                Write-Log -Message "Initial data loaded successfully!" -Level "Success" -Component "Database"
            }
        }
    } else {
        Write-Log -Message "No data.sql file found in $installDir." -Level "Warning" -Component "Database"
    }
    
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Database initialization completed!" -Level "Success" -Component "Database"
    
    Show-MainMenu
}

# Function to set up specialized database configurations based on database type
function Initialize-SpecializedDatabase {
    param (
        [Parameter(Mandatory = $true)]
        [string]$DatabaseName
    )
    
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Setting up specialized database configuration..." -Level "Info" -Component "Database"
    
    # Create temporary directory on server for schema files
    $tempDirCmd = "mkdir -p /tmp/database-service-schema"
    Invoke-RemoteCommand -Command $tempDirCmd -Silent
    
    # Handle central management database
    if ($DatabaseName -eq "database_service") {
        Write-Log -Message "Setting up central management database..." -Level "Info" -Component "Database"
        
        # Check if schema_version table exists
        $checkSchemaVersionCmd = @"
sudo -u postgres psql -d $DatabaseName -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'schema_version')" | grep -q t && echo "EXISTS" || echo "NOT_EXISTS"
"@
        $schemaVersionExists = Invoke-RemoteCommand -Command $checkSchemaVersionCmd -Silent
        
        if ($schemaVersionExists -eq "EXISTS") {
            Write-Log -Message "Schema version table already exists." -Level "Info" -Component "Database"
        } else {
            Write-Log -Message "Creating schema version table..." -Level "Info" -Component "Database"
            $createSchemaVersionCmd = @"
sudo -u postgres psql -d $DatabaseName -c "CREATE TABLE schema_version (id SERIAL PRIMARY KEY, application_name VARCHAR(100) NOT NULL, database_name VARCHAR(100) NOT NULL, version VARCHAR(50) NOT NULL, description TEXT, applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), UNIQUE(application_name, database_name, version));"
"@
            $result = Invoke-RemoteCommand -Command $createSchemaVersionCmd
            
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to create schema version table: $result" -Level "Error" -Component "Database"
            } else {
                Write-Log -Message "Schema version table created successfully!" -Level "Success" -Component "Database"
            }
        }
        
        # Check if applications table exists
        $checkAppsTableCmd = @"
sudo -u postgres psql -d $DatabaseName -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'applications')" | grep -q t && echo "EXISTS" || echo "NOT_EXISTS"
"@
        $appsTableExists = Invoke-RemoteCommand -Command $checkAppsTableCmd -Silent
        
        if ($appsTableExists -eq "EXISTS") {
            Write-Log -Message "Applications table already exists." -Level "Info" -Component "Database"
        } else {
            Write-Log -Message "Creating applications table..." -Level "Info" -Component "Database"
            $createAppsTableCmd = @"
sudo -u postgres psql -d $DatabaseName -c "CREATE TABLE applications (id SERIAL PRIMARY KEY, name VARCHAR(100) NOT NULL UNIQUE, display_name VARCHAR(200) NOT NULL, description TEXT, database_name VARCHAR(100), created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(), updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW());"
"@
            $result = Invoke-RemoteCommand -Command $createAppsTableCmd
            
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to create applications table: $result" -Level "Error" -Component "Database"
            } else {
                Write-Log -Message "Applications table created successfully!" -Level "Success" -Component "Database"
                
                # Register database service application
                $registerAppCmd = @"
sudo -u postgres psql -d $DatabaseName -c "INSERT INTO applications (name, display_name, description, database_name) VALUES ('database-service', 'Database Service', 'Central database management service', 'database_service') ON CONFLICT (name) DO NOTHING;"
"@
                Invoke-RemoteCommand -Command $registerAppCmd -Silent
                
                # Register git repo service application
                $registerGitAppCmd = @"
sudo -u postgres psql -d $DatabaseName -c "INSERT INTO applications (name, display_name, description, database_name) VALUES ('git-repo-service', 'Git Repository Service', 'Git repository management service', 'git_repo_db') ON CONFLICT (name) DO NOTHING;"
"@
                Invoke-RemoteCommand -Command $registerGitAppCmd -Silent
                
                # Register logging service application
                $registerLoggingAppCmd = @"
sudo -u postgres psql -d $DatabaseName -c "INSERT INTO applications (name, display_name, description, database_name) VALUES ('logging-service', 'Logging Service', 'Centralized logging service', 'logging_db') ON CONFLICT (name) DO NOTHING;"
"@
                Invoke-RemoteCommand -Command $registerLoggingAppCmd -Silent
            }
        }
    } 
    # Handle git repository database
    elseif ($DatabaseName -eq "git_repo_db") {
        Write-Log -Message "Setting up git repository database..." -Level "Info" -Component "Database"
        
        # Create metadata schema
        $createMetadataSchemaCmd = @"
sudo -u postgres psql -d $DatabaseName -c "CREATE SCHEMA IF NOT EXISTS metadata;"
"@
        $result = Invoke-RemoteCommand -Command $createMetadataSchemaCmd
        
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create metadata schema: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Metadata schema created successfully!" -Level "Success" -Component "Database"
        }
        
        # Upload git repository schema file
        $deploymentFilesDir = "D:\Augment\project-tracker\database-service-deployment\deployment_files"
        $schemaFilePath = Join-Path -Path $deploymentFilesDir -ChildPath "schemas\git_repo_schema.sql"
        
        if (Test-Path -Path $schemaFilePath) {
            Write-Log -Message "Uploading git repository schema file..." -Level "Info" -Component "Database"
            
            $remotePath = "/tmp/database-service-schema/git_repo_schema.sql"
            $scpArgs = "-i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$schemaFilePath`" $($Config.ssh.username)@$($Config.ssh.host):$remotePath"
            
            $process = Start-Process -FilePath "scp" -ArgumentList $scpArgs -NoNewWindow -PassThru -Wait
            
            if ($process.ExitCode -eq 0) {
                Write-Log -Message "Schema file uploaded successfully!" -Level "Success" -Component "Database"
                
                # Apply schema
                Write-Log -Message "Applying git repository schema..." -Level "Info" -Component "Database"
                $applySchemaCmd = @"
sudo -u postgres psql -d $DatabaseName -f /tmp/database-service-schema/git_repo_schema.sql
"@
                $result = Invoke-RemoteCommand -Command $applySchemaCmd
                
                if ($result -match "ERROR") {
                    Write-Log -Message "Failed to apply git repository schema: $result" -Level "Error" -Component "Database"
                } else {
                    Write-Log -Message "Git repository schema applied successfully!" -Level "Success" -Component "Database"
                    
                    # Record schema version in central database
                    $recordVersionCmd = @"
sudo -u postgres psql -d database_service -c "INSERT INTO schema_version (application_name, database_name, version, description) VALUES ('git-repo-service', 'git_repo_db', '1.0.0', 'Initial schema') ON CONFLICT (application_name, database_name, version) DO NOTHING;"
"@
                    Invoke-RemoteCommand -Command $recordVersionCmd -Silent
                }
            } else {
                Write-Log -Message "Failed to upload schema file. Exit code: $($process.ExitCode)" -Level "Error" -Component "Database"
            }
        } else {
            Write-Log -Message "Git repository schema file not found: $schemaFilePath" -Level "Error" -Component "Database"
        }
    } 
    # Handle logging database
    elseif ($DatabaseName -eq "logging_db") {
        Write-Log -Message "Setting up logging database..." -Level "Info" -Component "Database"
        
        # Create schemas
        $createSchemasCmd = @"
sudo -u postgres psql -d $DatabaseName -c "CREATE SCHEMA IF NOT EXISTS archive; CREATE SCHEMA IF NOT EXISTS stats;"
"@
        $result = Invoke-RemoteCommand -Command $createSchemasCmd
        
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create schemas: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Schemas created successfully!" -Level "Success" -Component "Database"
        }
        
        # Upload logging schema file
        $deploymentFilesDir = "D:\Augment\project-tracker\database-service-deployment\deployment_files"
        $schemaFilePath = Join-Path -Path $deploymentFilesDir -ChildPath "schemas\logging_schema.sql"
        
        if (Test-Path -Path $schemaFilePath) {
            Write-Log -Message "Uploading logging schema file..." -Level "Info" -Component "Database"
            
            $remotePath = "/tmp/database-service-schema/logging_schema.sql"
            $scpArgs = "-i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$schemaFilePath`" $($Config.ssh.username)@$($Config.ssh.host):$remotePath"
            
            $process = Start-Process -FilePath "scp" -ArgumentList $scpArgs -NoNewWindow -PassThru -Wait
            
            if ($process.ExitCode -eq 0) {
                Write-Log -Message "Schema file uploaded successfully!" -Level "Success" -Component "Database"
                
                # Apply schema
                Write-Log -Message "Applying logging schema..." -Level "Info" -Component "Database"
                $applySchemaCmd = @"
sudo -u postgres psql -d $DatabaseName -f /tmp/database-service-schema/logging_schema.sql
"@
                $result = Invoke-RemoteCommand -Command $applySchemaCmd
                
                if ($result -match "ERROR") {
                    Write-Log -Message "Failed to apply logging schema: $result" -Level "Error" -Component "Database"
                } else {
                    Write-Log -Message "Logging schema applied successfully!" -Level "Success" -Component "Database"
                    
                    # Record schema version in central database
                    $recordVersionCmd = @"
sudo -u postgres psql -d database_service -c "INSERT INTO schema_version (application_name, database_name, version, description) VALUES ('logging-service', 'logging_db', '1.0.0', 'Initial schema') ON CONFLICT (application_name, database_name, version) DO NOTHING;"
"@
                    Invoke-RemoteCommand -Command $recordVersionCmd -Silent
                }
            } else {
                Write-Log -Message "Failed to upload schema file. Exit code: $($process.ExitCode)" -Level "Error" -Component "Database"
            }
        } else {
            Write-Log -Message "Logging schema file not found: $schemaFilePath" -Level "Error" -Component "Database"
        }
    } 
    # Handle custom database
    else {
        Write-Log -Message "Setting up custom database $DatabaseName..." -Level "Info" -Component "Database"
        
        # Create public schema
        $createPublicSchemaCmd = @"
sudo -u postgres psql -d $DatabaseName -c "CREATE SCHEMA IF NOT EXISTS public;"
"@
        $result = Invoke-RemoteCommand -Command $createPublicSchemaCmd
        
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create public schema: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Public schema created successfully!" -Level "Success" -Component "Database"
        }
    }
}

# Run the function
Initialize-Database
