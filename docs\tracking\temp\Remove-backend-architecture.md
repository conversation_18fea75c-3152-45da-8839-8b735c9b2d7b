# Backend Architecture 🔄

*Last Updated: March 9, 2025*

> This document provides detailed backend implementation specifics. For a high-level system overview, see the [main architecture document](../architecture/PROJECT-TRACKER-ARCHITECTURE.md).

# Project Tracker Overview
Project Tracker is a comprehensive project management and improvement tracking system that helps teams monitor progress, track improvements, analyze code changes, generate insights and collaborate effectively.

## Overview

The Project Tracker backend is a Flask/Python application running on a dedicated Ubuntu server. This separation from the frontend provides better resource allocation, improved security, and more flexible scaling options.

## Component Architecture

```mermaid
graph TD
    Flask[Flask Application] --> Routes[API Routes]
    Routes --> AuthController[Auth Controller]
    Routes --> ProjectController[Project Controller]
    Routes --> GitController[Git Operations Controller]
    AuthController --> AuthService[Auth Service]
    ProjectController --> ProjectService[Project Service]
    GitController --> GitService[Git Operations Service]
    AuthService --> Database[(PostgreSQL Database)]
    ProjectService --> Database
    GitService --> Database
    AuthService --> Cache[(<PERSON>is <PERSON>ache)]
    ProjectService --> Cache
    GitService --> Cache
    WebSocketServer[WebSocket Server] --> AuthService
    WebSocketServer --> ProjectService
    WebSocketServer --> GitService
    WebSocketServer --> Cache
```

### Core Components

#### Controllers

- **AuthController**: Handles authentication and authorization requests
- **ProjectController**: Manages project-related operations
- **GitController**: Processes git operation requests and monitoring

#### Services

- **AuthService**: Implements authentication logic and user management
- **ProjectService**: Handles project data operations and business logic
- **GitService**: Manages git repository operations and metrics collection

## Data Models 🔄

### Core Schema

```mermaid
erDiagram
    User ||--o{ Project : manages
    Project ||--o{ Improvement : contains
    Project ||--o{ Category : has
    Improvement }o--|| Category : belongs_to
    Improvement ||--o{ Subtask : contains
    Project ||--o{ Activity : logs
    User ||--o{ Activity : performs
    Project ||--o{ GitRepository : tracks
    GitRepository ||--o{ Branch : contains
    GitRepository ||--o{ OperationMetric : records
    GitRepository ||--o{ Webhook : configures
```

### Key Models

#### User

```python
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
```

#### Project

```python
class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    repository_url = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, onupdate=datetime.utcnow)
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))
```

#### GitRepository

```python
class GitRepository(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    name = db.Column(db.String(100), nullable=False)
    local_path = db.Column(db.String(255))
    remote_url = db.Column(db.String(255))
    last_fetch = db.Column(db.DateTime)
    status = db.Column(db.String(50))
```

## API Definitions 🔄

### Authentication APIs

| Endpoint | Method | Description | Status |
|----------|--------|-------------|--------|
| `/api/auth/login` | POST | User login and token generation | ✅ |
| `/api/auth/register` | POST | New user registration | ✅ |
| `/api/auth/refresh` | POST | Refresh authentication token | ✅ |
| `/api/auth/logout` | POST | Invalidate current token | ✅ |
| `/api/auth/verify` | GET | Verify email address | ✅ |

### Project Management APIs

| Endpoint | Method | Description | Status |
|----------|--------|-------------|--------|
| `/api/projects` | GET | List all projects | ✅ |
| `/api/projects` | POST | Create new project | ✅ |
| `/api/projects/{id}` | GET | Get project details | ✅ |
| `/api/projects/{id}` | PUT | Update project | ✅ |
| `/api/projects/{id}` | DELETE | Delete project | ✅ |
| `/api/projects/{id}/improvements` | GET | List project improvements | ✅ |
| `/api/projects/{id}/activities` | GET | Get project activity log | ✅ |

### Git Operation APIs

| Endpoint | Method | Description | Status |
|----------|--------|-------------|--------|
| `/api/git/repositories` | GET | List all repositories | ✅ |
| `/api/git/repositories` | POST | Add new repository | ✅ |
| `/api/git/repositories/{id}` | GET | Get repository details | ✅ |
| `/api/git/repositories/{id}/metrics` | GET | Get repository metrics | 🔄 |
| `/api/git/repositories/{id}/branches` | GET | List repository branches | ✅ |
| `/api/git/repositories/{id}/webhooks` | GET | List repository webhooks | 🔄 |
| `/api/git/operations/performance` | GET | Get operation performance metrics | 🔄 |

## Service Architecture 🔄

### Authentication Service

The authentication service handles user registration, login, and token management:

- JWT-based authentication with role and permission embedding
- Password hashing using bcrypt
- Token refresh mechanism
- Role-based access control
- Session management

### Project Service

The project service manages project data and operations:

- CRUD operations for projects
- Improvement tracking
- Category management
- Activity logging
- Data validation

### Git Operations Service

The git operations service handles repository management and metrics collection:

- Local and remote repository tracking
- Branch management
- Operation performance monitoring
- Repository metrics collection
- Webhook management

## Database Architecture ✅

### PostgreSQL Configuration

- Connection pooling (1-20 connections)
- Transaction isolation levels
- Indexing strategy for performance
- Backup and recovery procedures
- Query optimization

### Redis Cache Implementation

- Cache invalidation strategy
- TTL configuration for different data types
- Pub/sub for real-time notifications
- Connection pooling
- Persistence configuration

## WebSocket Service 🔄

The WebSocket service provides real-time updates to connected clients:

- Authentication via JWT tokens
- Channel-based subscriptions
- Message queuing for offline clients
- Broadcast and direct messaging
- Connection management and monitoring

## Security Implementation 🔄

- TLS/SSL for all API endpoints
- Input validation and sanitization
- SQL injection prevention via ORM
- CSRF protection
- Rate limiting (🔄 in progress)
- IP filtering (🔄 in progress)
- Security audit logging (🔄 in progress)

---

*This document provides detailed backend implementation specifics. For system-wide architecture concerns, data flow, and integration points, refer to the [main architecture document](../architecture/PROJECT-TRACKER-ARCHITECTURE.md).*
