# Set Environment Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Import Manage-DeploymentConfigurations module
Import-Module -Name "$PSScriptRoot\Manage-DeploymentConfigurations.ps1" -Force

# Set environment function
function Set-Environment {
    Clear-Host
    
    # Enable UI Mode for menu display
    Enable-UIMode
    
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Set Environment                      " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    
    # Display current configuration if available
    Write-Log -Message "Current Configuration:" -Level "UI" -ForegroundColor Cyan
    if ($null -ne $Config -and $null -ne $Config.project -and $null -ne $DeploymentEnvironment) {
        Write-Log -Message "Project: $($Config.project.name)" -Level "UI" -ForegroundColor White
        
        # Color code environment
        $envColor = "White"
        if ($DeploymentEnvironment -eq "production") {
            $envColor = "Red"
        } elseif ($DeploymentEnvironment -eq "development") {
            $envColor = "Green"
        } elseif ($DeploymentEnvironment -eq "testing" -or $DeploymentEnvironment -eq "test") {
            $envColor = "Yellow"
        }
        
        Write-Log -Message "Environment: $DeploymentEnvironment" -Level "UI" -ForegroundColor $envColor
        
        Write-Log -Message "Server: $($Config.ssh.host)" -Level "UI" -ForegroundColor White
        Write-Log -Message "Install Directory: $($Config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
        Write-Log -Message "Source Directory: $($Config.project.local_source_dir)" -Level "UI" -ForegroundColor White
        Write-Log -Message "Build Directory: $($Config.project.remote_build_dir)" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "No configuration currently loaded." -Level "UI" -ForegroundColor Yellow
    }
    Write-Log -Message " " -Level "UI"
    
    # Check if config directory exists
    if (-not (Test-Path -Path $ConfigDir)) {
        Write-Log -Message "No configuration directory found. Creating one now..." -Level "UI" -ForegroundColor Yellow
        try {
            New-Item -Path $ConfigDir -ItemType Directory | Out-Null
        } catch {
            Write-Log -Message "Failed to create configuration directory: $_" -Level "Error" -Component "Environment"
            
            # Disable UI Mode before returning
            Disable-UIMode
            return
        }
    }
    
    # Get available config files
    $configFiles = Get-ChildItem -Path $ConfigDir -Filter "*.json" | Select-Object -ExpandProperty Name
    
    if ($configFiles.Count -eq 0) {
        Write-Log -Message "No configuration files found. Please create a new configuration." -Level "UI" -ForegroundColor Yellow
        Write-Log -Message " " -Level "UI"
        
        # Display environment selection menu
        Write-Log -Message "Select environment type:" -Level "UI" -ForegroundColor Cyan
        Write-Log -Message "[1] Development" -Level "UI" -ForegroundColor White
        Write-Log -Message "[2] Testing" -Level "UI" -ForegroundColor White
        Write-Log -Message "[3] Production" -Level "UI" -ForegroundColor White
        Write-Log -Message "[4] Custom" -Level "UI" -ForegroundColor White
        Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
        Write-Log -Message "[0] Back to Main Menu" -Level "UI" -ForegroundColor White
        
        # Disable UI Mode before reading input
        Disable-UIMode
        
        $envChoice = Read-Host "`nSelect an option (0-4)"
        
        switch ($envChoice) {
            "1" { $environment = "development" }
            "2" { $environment = "testing" }
            "3" { $environment = "production" }
            "4" { 
                $environment = Read-Host "Enter custom environment name"
                if ([string]::IsNullOrWhiteSpace($environment)) {
                    Write-Log -Message "Environment name cannot be empty." -Level "Error" -Component "Environment"
                    Wait-ForUser
                    Set-Environment
                    return
                }
            }
            "0" { 
                Show-MainMenu
                return
            }
            default {
                Write-Log -Message "Invalid option. Using development as default." -Level "Warning" -Component "Environment"
                $environment = "development"
            }
        }
        
        $result = New-DefaultConfig -Environment $environment
        if ($result) {
            $script:DeploymentEnvironment = $environment
            Write-Log -Message "Configuration created successfully." -Level "Success" -Component "Environment"
        } else {
            Write-Log -Message "Failed to create configuration." -Level "Error" -Component "Environment"
        }
        
        Wait-ForUser
        Show-MainMenu
        return
    }
    
    # Display available environments
    Write-Log -Message "Menu Options:" -Level "UI" -ForegroundColor Cyan
    
    $menu = @{}
    $index = 1
    
    # Add option to create a new configuration
    Write-Log -Message "[$index] Create New Configuration" -Level "UI" -ForegroundColor White
    $menu[$index] = "new"
    $index++
    
    # Section for saved configurations
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Available Configurations:" -Level "UI" -ForegroundColor Cyan
    
    foreach ($file in $configFiles) {
        # Extract both project name and environment from filename (project-environment.json)
        $fileName = [System.IO.Path]::GetFileNameWithoutExtension($file)
        $parts = $fileName.Split('-')
        
        if ($parts.Count -gt 1) {
            # Last part is the environment, everything before is the project name
            $envName = $parts[-1]
            $projectName = $parts[0..($parts.Count-2)] -join '-'
        } else {
            $projectName = $parts[0]
            $envName = "default"
        }
        
        $menu[$index] = $file
        
        # Try to read the configuration file
        try {
            $configFile = Join-Path -Path $ConfigDir -ChildPath $file
            $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
            
            # Fix "True" project name issue if present
            if ($configContent.project.name -eq $true) { 
                # If the project name is "True", fix it now but don't output a message
                $configContent.project.name = $projectName
                $configContent | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
            }
            
            # Create a concise display format with version information
            $configDetails = ""
            
            # Add server information if available
            if ($configContent.ssh -and $configContent.ssh.host) {
                $configDetails += "$($configContent.ssh.host) | "
            }
            
            # Add build directory information if available (just the name, not the path)
            if ($configContent.project -and $configContent.project.remote_build_dir) {
                $buildDirShort = ($configContent.project.remote_build_dir -split '/')[-1]
                $configDetails += "$buildDirShort | "
            }
            
            # Add version information if available (prioritize this)
            if ($configContent.version -and $configContent.version.number) {
                $configDetails += "v$($configContent.version.number)"
            } else {
                # For older configs without version info, just show 'Legacy'
                $configDetails += "Legacy"
            }
            
            # Add environment type with color coding
            $envColor = "White"
            if ($envName -eq "production") {
                $envColor = "Red"
            } elseif ($envName -eq "development") {
                $envColor = "Green"
            } elseif ($envName -eq "testing" -or $envName -eq "test") {
                $envColor = "Yellow"
            }
            
            Write-Log -Message "[$index] $projectName" -Level "UI" -ForegroundColor Cyan -NoNewline
            Write-Log -Message " ($envName)" -Level "UI" -ForegroundColor $envColor -NoNewline
            Write-Log -Message ": $configDetails" -Level "UI" -ForegroundColor Cyan
        } catch {
            Write-Log -Message "[$index] $file" -Level "UI" -ForegroundColor White
            Write-Log -Message "    Warning: Could not read configuration file properly" -Level "Warning" -Component "Environment"
            Write-Log -Message "    Error: $_" -Level "Error" -Component "Environment"
        }
        
        $index++
    }
    
    # Add option to back to main menu (at the end)
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
    Write-Log -Message "[0] Return to Main Menu" -Level "UI" -ForegroundColor White
    $menu[0] = "back"
    
    Write-Log -Message " " -Level "UI"
    
    # Disable UI Mode before reading input
    Disable-UIMode
    
    $choice = Read-Host "Select an option"
    
    # Make sure we treat the choice as a string for consistent comparison
    $choice = $choice.ToString()
    
    [int]$choiceNum = 0
    $validInt = [int]::TryParse($choice, [ref]$choiceNum)
    
    if ($validInt -and $menu.ContainsKey($choiceNum)) {
        $selected = $menu[$choiceNum]
        
        if ($selected -eq "back") {
            Show-MainMenu
            return
        } 
        elseif ($selected -eq "new") {
            # Create new configuration
            Write-Log -Message "`nCreate new configuration" -Level "UI" -ForegroundColor Cyan
            Write-Log -Message "Select environment type:" -Level "UI" -ForegroundColor Cyan
            Write-Log -Message "[1] Development" -Level "UI" -ForegroundColor White
            Write-Log -Message "[2] Testing" -Level "UI" -ForegroundColor White
            Write-Log -Message "[3] Production" -Level "UI" -ForegroundColor White
            Write-Log -Message "[4] Custom" -Level "UI" -ForegroundColor White
            Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
            Write-Log -Message "[0] Back to Main Menu" -Level "UI" -ForegroundColor White
            
            $envChoice = Read-Host "`nSelect an option (0-4)"
            
            switch ($envChoice) {
                "1" { $environment = "development" }
                "2" { $environment = "testing" }
                "3" { $environment = "production" }
                "4" { 
                    $environment = Read-Host "Enter custom environment name"
                    if ([string]::IsNullOrWhiteSpace($environment)) {
                        Write-Log -Message "Environment name cannot be empty." -Level "Error" -Component "Environment"
                        Wait-ForUser
                        Set-Environment
                        return
                    }
                }
                "0" { 
                    Show-MainMenu
                    return
                }
                default {
                    Write-Log -Message "Invalid option. Using development as default." -Level "Warning" -Component "Environment"
                    $environment = "development"
                }
            }
            
            $result = New-DefaultConfig -Environment $environment
            if ($result) {
                $script:DeploymentEnvironment = $environment
                Write-Log -Message "Configuration created successfully." -Level "Success" -Component "Environment"
            } else {
                Write-Log -Message "Failed to create configuration." -Level "Error" -Component "Environment"
            }
            
            Wait-ForUser
            Show-MainMenu
            return
        }
        else {
            # Load an existing configuration
            $configFile = Join-Path -Path $ConfigDir -ChildPath $selected
            
            try {
                # Load the configuration file directly instead of using Import-Configuration
                if (Test-Path -Path $configFile) {
                    $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                    
                    # Convert from JSON to PowerShell hashtable
                    $script:Config = @{}
                    $configContent.PSObject.Properties | ForEach-Object {
                        $name = $_.Name
                        $value = $_.Value
                        
                        if ($value -is [System.Management.Automation.PSCustomObject]) {
                            $valueHash = @{}
                            $value.PSObject.Properties | ForEach-Object {
                                $valueHash[$_.Name] = $_.Value
                            }
                            $script:Config[$name] = $valueHash
                        } else {
                            $script:Config[$name] = $value
                        }
                    }
                    
                    # Extract environment from filename
                    $fileNameWithoutExt = [System.IO.Path]::GetFileNameWithoutExtension($selected)
                    $parts = $fileNameWithoutExt.Split('-')
                    if ($parts.Count -gt 1) {
                        $script:Environment = $parts[-1]
                        $script:DeploymentEnvironment = $parts[-1]
                    } else {
                        $script:Environment = "default"
                        $script:DeploymentEnvironment = "default"
                    }
                    
                    Write-Log -Message "`nLoaded configuration: $($selected)" -Level "UI" -ForegroundColor Green
                    
                    # Fix source directory path if it's relative
                    if ($Config.project.local_source_dir -match "\\\.\.\\\.\.\\\.\." -or $Config.project.local_source_dir -match "/\.\./\.\./\.\.") {
                        $Config.project.local_source_dir = "D:\Augment\project-tracker\$($Config.project.name)"
                        Save-Configuration
                        Write-Log -Message "Updated source directory path." -Level "UI" -ForegroundColor Green
                    }
                    
                    # Pause to show the message
                    Wait-ForUser
                    Show-MainMenu
                    return
                } else {
                    Write-Log -Message "Configuration file not found: $configFile" -Level "Error" -Component "Environment"
                }
            } catch {
                Write-Log -Message "Error loading configuration: $_" -Level "Error" -Component "Environment"
                Wait-ForUser
            }
        }
    } else {
        Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Environment"
        Start-Sleep -Seconds 1
        Set-Environment
    }
}

# Create a default configuration
function New-DefaultConfig {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Environment,
        [Parameter(Mandatory=$false)]
        [string]$ProjectName = ""
    )
    
    Write-Log -Message "Creating new configuration for environment: $Environment" -Level "UI" -ForegroundColor Yellow
    
    # If project name is not provided, ask for it
    if ([string]::IsNullOrWhiteSpace($ProjectName)) {
        # Display project type menu
        Write-Log -Message "`nSelect project type:" -Level "UI" -ForegroundColor Cyan
        Write-Log -Message "[1] Database Service" -Level "UI" -ForegroundColor White
        Write-Log -Message "[2] Git Dashboard" -Level "UI" -ForegroundColor White
        Write-Log -Message "[3] Custom Project" -Level "UI" -ForegroundColor White
        
        $projectChoice = Read-Host "Select project type (1-3)"
        
        switch ($projectChoice) {
            "1" { $ProjectName = "database-service" }
            "2" { $ProjectName = "git-dashboard" }
            "3" { 
                $ProjectName = Read-Host "Enter custom project name"
                if ([string]::IsNullOrWhiteSpace($ProjectName)) {
                    Write-Log -Message "Project name cannot be empty." -Level "Error" -Component "Environment"
                    return $false
                }
            }
            default {
                Write-Log -Message "Invalid option. Using database-service as default." -Level "Warning" -Component "Environment"
                $ProjectName = "database-service"
            }
        }
    }
    
    # Ensure we have both project name and environment
    if ([string]::IsNullOrWhiteSpace($ProjectName) -or [string]::IsNullOrWhiteSpace($Environment)) {
        Write-Log -Message "Cannot save configuration: missing project name or environment." -Level "Error" -Component "Environment"
        return $false
    }
    
    # Load requirements from JSON
    # Note: Currently not using requirements but keeping the function call for future extensibility
    $unusedRequirements = Import-Requirements
    
    # Set default values
    $projectDesc = "$ProjectName for $Environment environment"
    $installDir = "/opt/$ProjectName"
    $serviceUser = $ProjectName
    $serviceGroup = $ProjectName
    $dbName = $ProjectName.Replace("-", "_")
    $dbUser = $ProjectName.Replace("-", "_")
    $dbPassword = "" # Will be prompted during database initialization
    
    # Set default source directory based on project name
    $defaultSourceDir = "D:\Augment\project-tracker\database-service"
    $defaultDeploymentDir = "D:\Augment\project-tracker\database-service-scripts\scripts\deployment"
    
    # Ask for source directory with default suggestion
    Write-Log -Message "`nSource Directory Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "Default source directory: $defaultSourceDir" -Level "UI" -ForegroundColor White
    $useDefault = Read-Host "Use default source directory? (y/n)"
    
    if ($useDefault -eq "y" -or $useDefault -eq "") {
        $sourceDir = $defaultSourceDir
    } else {
        $sourceDir = Read-Host "Enter the full path to the source directory"
        # Validate that the directory exists
        if (-not (Test-Path -Path $sourceDir -PathType Container)) {
            Write-Log -Message "Warning: The specified directory does not exist." -Level "Warning" -Component "Environment"
            $createDir = Read-Host "Create this directory? (y/n)"
            if ($createDir -eq "y") {
                try {
                    New-Item -Path $sourceDir -ItemType Directory -Force | Out-Null
                    Write-Log -Message "Directory created successfully." -Level "UI" -ForegroundColor Green
                } catch {
                    Write-Log -Message "Failed to create directory: $_" -Level "Error" -Component "Environment"
                    Write-Log -Message "Using default directory instead." -Level "UI" -ForegroundColor Yellow
                    $sourceDir = $defaultSourceDir
                }
            } else {
                Write-Log -Message "Using default directory instead." -Level "UI" -ForegroundColor Yellow
                $sourceDir = $defaultSourceDir
            }
        }
    }
    
    # Create configuration object
    $script:Config = @{
        version = @{
            number = 1
            created = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
            updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        }
        project = @{
            name = $ProjectName
            description = $projectDesc
            remote_install_dir = $installDir
            local_source_dir = $sourceDir
            remote_build_dir = "$sourceDir-build"
        }
        ssh = @{
            host = "git.chcit.org"
            port = 22
            username = "btaylor-admin"
            local_key_path = Find-SSHKey
        }
        service = @{
            name = $ProjectName
            description = $projectDesc
            user = $serviceUser
            group = $serviceGroup
        }
        database = @{
            name = $dbName
            user = $dbUser
            password = $dbPassword
            host = "localhost"
            port = 5432
        }
    }
    
    # Set the deployment environment
    $script:Environment = $Environment
    
    # Create configuration file
    $configFileName = "$ProjectName-$Environment.json"
    $configFilePath = Join-Path -Path $ConfigDir -ChildPath $configFileName
    
    # Check if file already exists and create backup if needed
    if (Test-Path -Path $configFilePath) {
        $backupResult = Backup-ConfigurationFile -ConfigFilePath $configFilePath
        if ($backupResult) {
            Write-Log -Message "Backup of existing configuration created in the 'backups' directory" -Level "UI" -ForegroundColor Cyan
        }
    }
    
    # Check if file already exists and load version info if it does
    $existingVersion = $null
    if (Test-Path -Path $configFilePath) {
        try {
            $existingConfig = Get-Content -Path $configFilePath -Raw | ConvertFrom-Json
            if ($existingConfig.PSObject.Properties.Name -contains "version") {
                $existingVersion = @{
                    number = [int]$existingConfig.version.number + 1  # Increment version
                    created = $existingConfig.version.created       # Keep original creation date
                    updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss") # Update modification date
                }
            }
        } catch {
            Write-Log -Message "Warning: Could not read existing configuration file. Creating new version." -Level "Warning" -Component "Environment"
        }
    }
    
    # If we found existing version info, use it instead of the default
    if ($existingVersion) {
        $script:Config.version = $existingVersion
        Write-Log -Message "Updating existing configuration to version $($existingVersion.number)" -Level "UI" -ForegroundColor Cyan
    }
    
    try {
        # Ensure the config directory exists
        if (-not (Test-Path -Path $ConfigDir)) {
            New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
        }
        
        # Save configuration to file
        $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFilePath -Encoding UTF8
        Write-Log -Message "Configuration saved to: $configFilePath" -Level "UI" -ForegroundColor Green
        return $true
    } catch {
        Write-Log -Message "Failed to save configuration: $_" -Level "Error" -Component "Environment"
        return $false
    }
}

# Helper function to find SSH key
function Find-SSHKey {
    # Common locations for SSH keys
    $possiblePaths = @(
        (Join-Path -Path $HOME -ChildPath ".ssh\id_rsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_dsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_ecdsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_ed25519")
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path -Path $path) {
            return $path
        }
    }
    
    return $null
}

# Helper function to backup configuration file
function Backup-ConfigurationFile {
    param (
        [Parameter(Mandatory=$true)]
        [string]$ConfigFilePath
    )
    
    # Create backups directory if it doesn't exist
    $backupsDir = Join-Path -Path $ConfigDir -ChildPath "backups"
    if (-not (Test-Path -Path $backupsDir)) {
        New-Item -Path $backupsDir -ItemType Directory -Force | Out-Null
    }
    
    # Get the file name without extension
    $fileName = [System.IO.Path]::GetFileNameWithoutExtension($ConfigFilePath)
    $fileExtension = [System.IO.Path]::GetExtension($ConfigFilePath)
    
    # Create a backup file name with a timestamp
    $backupFileName = "$fileName-$(Get-Date -Format "yyyy-MM-dd_HH-mm-ss")$fileExtension"
    $backupFilePath = Join-Path -Path $backupsDir -ChildPath $backupFileName
    
    try {
        # Copy the original file to the backup location
        Copy-Item -Path $ConfigFilePath -Destination $backupFilePath -Force
        return $true
    } catch {
        Write-Log -Message "Failed to create backup of configuration file: $_" -Level "Error" -Component "Environment"
        return $false
    }
}

# Run the function
Set-Environment
