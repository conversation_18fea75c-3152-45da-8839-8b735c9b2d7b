#!/bin/bash
#
# Project Tracker SSL Setup Script
# Version: 2.0.0
# Last Updated: 2025-03-04
# Author: CHCIT DevOps Team
#
# This script sets up SSL certificates for private network domains using
# Let's Encrypt with Cloudflare DNS validation.

# Exit on any error
set -e

# Source common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# Run root check
check_root

# Configuration
CLOUDFLARE_CONFIG_DIR="/etc/letsencrypt/cloudflare"
TOKEN_FILE="$CLOUDFLARE_CONFIG_DIR/cf-token.secret"
CRED_FILE="$CLOUDFLARE_CONFIG_DIR/credentials.ini"

# Token management function
manage_cloudflare_token() {
    log "INFO" "Checking Cloudflare token"
    track_progress "Token Check" "Verifying Cloudflare API token"
    
    # Check if token file exists
    if [ ! -f "$TOKEN_FILE" ]; then
        log "ERROR" "Cloudflare API Token not found. Please follow these steps:"
        log "INFO" "1. Go to Cloudflare Dashboard > My Profile > API Tokens"
        log "INFO" "2. Create a Custom Token with these permissions:"
        log "INFO" "   - Zone: chcit.org"
        log "INFO" "   - Permissions: Zone:Read, DNS:Edit"
        log "INFO" "3. Copy the token and run this command:"
        log "INFO" "   sudo bash $0 set-token YOUR_CLOUDFLARE_TOKEN"
        exit 1
    fi

    # Token setting mode
    if [ "$1" == "set-token" ] && [ -n "$2" ]; then
        log "INFO" "Setting up Cloudflare token"
        track_progress "Token Setup" "Storing Cloudflare API token"
        
        # Create secure directory if not exists
        mkdir -p "$CLOUDFLARE_CONFIG_DIR"
        
        # Write token with strict permissions
        echo "$2" > "$TOKEN_FILE"
        chmod 600 "$TOKEN_FILE"
        chown root:root "$TOKEN_FILE"
        
        # Create credentials.ini
        cat > "$CRED_FILE" << EOF
# Cloudflare API credentials for DNS validation
dns_cloudflare_api_token = $2
EOF
        chmod 600 "$CRED_FILE"
        
        log "INFO" "Cloudflare API token stored securely"
        track_progress "Token Setup" "Token stored successfully"
        exit 0
    fi
}

# Check command line arguments
if [ "$1" == "set-token" ]; then
    if [ -z "$2" ]; then
        log "ERROR" "Please provide the Cloudflare API token"
        exit 1
    fi
    manage_cloudflare_token "$1" "$2"
    exit 0
fi

if [ -z "$1" ]; then
    log "ERROR" "Please provide domain: ./ssl-setup.sh project-tracker.chcit.org"
    exit 1
fi

# Extract domain parts
DOMAIN=$1
BASE_DOMAIN=$(echo "$DOMAIN" | cut -d. -f2-)  # Extract base domain (e.g., chcit.org)

# Validate domain format
if ! echo "$DOMAIN" | grep -qE '^[a-zA-Z0-9][a-zA-Z0-9-]*\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$'; then
    log "ERROR" "Invalid domain format. Example: project-tracker.chcit.org"
    exit 1
fi

log "INFO" "Setting up SSL for domain: $DOMAIN"
log "INFO" "Will request wildcard certificate for: *.$BASE_DOMAIN"

# Install required packages
log "INFO" "Installing SSL dependencies"
track_progress "Package Setup" "Installing required packages"

apt-get update
apt-get install -y \
    certbot \
    python3-certbot-dns-cloudflare

# Ensure Cloudflare token is set
manage_cloudflare_token

# Read token from secure file
CF_TOKEN=$(cat "$TOKEN_FILE")
if [ -z "$CF_TOKEN" ]; then
    log "ERROR" "Cloudflare API token is empty"
    exit 1
fi

# Test Cloudflare API token
log "INFO" "Testing Cloudflare API token"
track_progress "API Test" "Verifying Cloudflare access"

# Install curl if needed
if ! command -v curl &> /dev/null; then
    apt-get install -y curl
fi

# Test token against Cloudflare API
ZONE_TEST=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones" \
     -H "Authorization: Bearer $CF_TOKEN" \
     -H "Content-Type: application/json")

if ! echo "$ZONE_TEST" | grep -q "success\":true"; then
    log "ERROR" "Cloudflare API token test failed. Please check your token permissions"
    log "ERROR" "Token must have Zone:Read and DNS:Edit permissions"
    exit 1
fi

log "INFO" "Cloudflare API token verified successfully"

# Obtain certificate using DNS validation
log "INFO" "Requesting SSL certificate for base domain and wildcard"
track_progress "Certificate" "Obtaining SSL certificate"

certbot certonly \
    --dns-cloudflare \
    --dns-cloudflare-credentials "$CRED_FILE" \
    --dns-cloudflare-propagation-seconds 60 \
    -d "$BASE_DOMAIN" \
    -d "*.$BASE_DOMAIN" \
    --non-interactive \
    --agree-tos \
    --email <EMAIL> \
    --preferred-challenges dns-01 \
    --server https://acme-v02.api.letsencrypt.org/directory \
    --key-type rsa \
    --rsa-key-size 2048 \
    --verbose

# Configure auto-renewal
log "INFO" "Configuring certificate auto-renewal"
track_progress "Auto Renewal" "Setting up renewal process"

RENEWAL_HOOKS_DIR="/etc/letsencrypt/renewal-hooks"
mkdir -p "$RENEWAL_HOOKS_DIR"/{pre,post,deploy}

# Create post-renewal Nginx reload hook
cat > "$RENEWAL_HOOKS_DIR/deploy/01-reload-nginx.sh" << 'EOF'
#!/bin/bash
systemctl reload nginx
EOF
chmod +x "$RENEWAL_HOOKS_DIR/deploy/01-reload-nginx.sh"

# Update Nginx configuration
log "INFO" "Updating Nginx SSL configuration"
track_progress "Nginx Config" "Configuring web server SSL"

NGINX_CONF="/etc/nginx/sites-available/project-tracker"
if [ -f "$NGINX_CONF" ]; then
    # Backup existing config
    cp "$NGINX_CONF" "$NGINX_CONF.bak"
    
    # Update certificate paths
    sed -i "s|ssl_certificate .*|ssl_certificate /etc/letsencrypt/live/*.$BASE_DOMAIN/fullchain.pem;|" "$NGINX_CONF"
    sed -i "s|ssl_certificate_key .*|ssl_certificate_key /etc/letsencrypt/live/*.$BASE_DOMAIN/privkey.pem;|" "$NGINX_CONF"
    
    # Add OCSP stapling
    if ! grep -q "ssl_stapling " "$NGINX_CONF"; then
        cat >> "$NGINX_CONF" << EOF

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate /etc/letsencrypt/live/*.$BASE_DOMAIN/chain.pem;
resolver 1.1.1.1 1.0.0.1 valid=300s;
resolver_timeout 5s;
EOF
    fi
    
    # Test and reload Nginx
    nginx -t && systemctl reload nginx
else
    log "WARN" "Nginx configuration file not found at $NGINX_CONF"
fi

log "INFO" "SSL setup completed for *.$BASE_DOMAIN"
track_progress "SSL Setup" "Configuration completed successfully"

# Security reminder
log "INFO" "Important security notes:"
log "INFO" "1. Certificate will auto-renew using Cloudflare DNS validation"
log "INFO" "2. OCSP stapling is enabled for better security"
log "INFO" "3. Certificate and key permissions are restricted to root"
log "INFO" "4. Verify SSL setup: curl -v https://$DOMAIN"
