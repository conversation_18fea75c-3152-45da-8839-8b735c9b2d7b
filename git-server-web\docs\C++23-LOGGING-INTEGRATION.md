# C++23 Logging Agent Integration Guide

## Overview

This document describes the integration of the C++23 Logging Agent with the Git Server Dashboard. The C++23 Logging Agent provides enhanced logging capabilities, including dynamic log discovery, real-time log streaming, and advanced filtering.

## Architecture

The integration follows a tiered architecture:

1. **Frontend**: JavaScript-based UI in the Git Server Dashboard
2. **Backend Proxy**: Python Flask application that proxies requests to the C++ service
3. **C++ Logging Service**: High-performance logging agent written in C++23

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Web Browser    │────▶│  Flask Backend  │────▶│  C++ Logging    │
│  (JavaScript)   │◀────│  (Python)       │◀────│  Service        │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  Log Files      │
                                               │  (/var/log)     │
                                               └─────────────────┘
```

## Components

### 1. Frontend Components

- **logging.js**: Core JavaScript module for log handling
- **log-settings-modal.js**: UI for log configuration
- **logging.css**: Styling for log display and settings modal
- **index.html**: Main dashboard HTML with log tab

### 2. Backend Components

- **app.py**: Python Flask application with proxy endpoints
- **nginx/git-dashboard.conf**: Nginx configuration for proxying requests

### 3. C++ Logging Service

- **git-repo-logging**: C++23 logging agent
- **systemd/git-repo-logging.service**: Systemd service for the logging agent
- **sql/schema.sql**: Database schema for PostgreSQL
- **sql/setup_database.sh**: Script to set up the PostgreSQL database
- **config/storage-config.json**: Configuration for storage tiers

## API Endpoints

### Python Backend Proxy Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/logs` | GET | Get log entries with filtering |
| `/api/logs` | POST | Save new log entries |
| `/api/logs/available` | GET | Get discovered log files |
| `/api/logs/scan` | POST | Scan for new log files |
| `/api/logs/configure` | POST | Update log configuration |

### C++ Logging Service Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/logs` | GET | Get log entries with filtering |
| `/api/logs` | POST | Save new log entries |
| `/api/logs/sources` | GET | Get available log sources |
| `/api/logs/stats` | GET | Get log statistics |
| `/api/logs/available` | GET | Get discovered log files |
| `/api/logs/scan` | POST | Scan for new log files |
| `/api/logs/configure` | POST | Update log configuration |
| `/api/logs/stream` | WebSocket | Stream log updates in real-time |

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LOGGING_SERVICE_URL` | URL of the C++ logging service | `http://localhost:8081` |
| `USE_CPP_LOGGING` | Whether to use the C++ logging service | `true` |
| `STORAGE_CONFIG_PATH` | Path to the storage configuration file | `/opt/git-dashboard/config/storage-config.json` |
| `DB_CONNECTION_STRING` | PostgreSQL connection string | `postgresql://git_logger:change_me_please@localhost:5432/git_logging` |

### Storage Configuration

The C++ logging agent uses a tiered storage approach with three tiers:

1. **Memory Tier**: In-memory storage for recent logs
2. **Database Tier**: PostgreSQL database for structured storage and querying
3. **File Tier**: File-based storage for long-term retention

The storage configuration is defined in `/opt/git-dashboard/config/storage-config.json`:

```json
{
  "tiers": {
    "memory": {
      "enabled": true,
      "max_entries": 1000,
      "max_age_minutes": 60
    },
    "database": {
      "enabled": true,
      "connection_string": "postgresql://git_logger:change_me_please@localhost:5432/git_logging",
      "structured_only": true
    },
    "file": {
      "enabled": true,
      "base_directory": "/var/log/git-dashboard/logs",
      "compression": true,
      "compression_age_days": 7
    }
  }
}
```

### Nginx Configuration

The Nginx configuration has been updated to proxy requests to the C++ logging service:

```nginx
# C++ Logging Service API Routes
location /api/logs {
    proxy_pass http://127.0.0.1:8081;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # WebSocket support for real-time log streaming
    proxy_read_timeout 3600s;
    proxy_send_timeout 3600s;

    # Rate limiting
    limit_req zone=api_limit burst=20 nodelay;
    limit_req_status 429;
}
```

## User Interface

### Log Settings Modal

The Log Settings modal provides a user-friendly interface for configuring log sources:

1. **Categories**: Logs are organized by category (system, security, web, etc.)
2. **Log Files**: Each category shows the available log files
3. **Settings**: Each log file can be enabled or disabled
4. **Scan**: The "Scan for Logs" button triggers a new scan for log files

### Log Display

The main log display has been enhanced with:

1. **Level Filtering**: Filter logs by level (all, info, warning, error, success)
2. **Source Selection**: Select the log source to display
3. **Count Selection**: Choose how many logs to display
4. **Settings Button**: Open the Log Settings modal

## Installation and Setup

1. **Deploy the C++ Logging Service**:
   ```bash
   # Copy the service files
   sudo cp -r git-repo-logging /opt/git-dashboard/

   # Install the systemd service
   sudo cp systemd/git-repo-logging.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable git-repo-logging
   sudo systemctl start git-repo-logging
   ```

2. **Set Up PostgreSQL Database**:
   ```bash
   # Install PostgreSQL if not already installed
   sudo apt update
   sudo apt install -y postgresql postgresql-contrib

   # Run the database setup script
   cd /opt/git-dashboard/share/git-repo-logging/sql
   sudo -u postgres ./setup_database.sh --db-name git_logging --db-user git_logger --db-password your_secure_password

   # Update the connection string in the environment
   sudo sed -i 's|DB_CONNECTION_STRING=.*|DB_CONNECTION_STRING="postgresql://git_logger:your_secure_password@localhost:5432/git_logging"|g' /etc/systemd/system/git-repo-logging.service
   sudo systemctl daemon-reload
   ```

   **Database Management Options**:
   ```bash
   # List all PostgreSQL databases
   sudo -u postgres ./setup_database.sh --list-databases

   # Check schema version for a specific database
   sudo -u postgres ./setup_database.sh --check-version git_logging

   # Delete a database (with confirmation)
   sudo -u postgres ./setup_database.sh --delete-database old_database

   # Force schema update even if version mismatch
   sudo -u postgres ./setup_database.sh --db-name git_logging --force-update

   # Skip schema version check
   sudo -u postgres ./setup_database.sh --db-name git_logging --skip-version-check
   ```

   **Advanced Database Management**:

   The deployment script also provides a comprehensive set of database management tools:

   - **List PostgreSQL Databases**: View all databases on the server
   - **Check Database Schema Version**: Verify the schema version of a specific database
   - **Delete Database**: Safely remove a database with confirmation checks
   - **List Schemas in Database**: View all schemas in a specific database
   - **Delete Schema from Database**: Safely remove a schema with confirmation checks

   These tools help prevent accidental changes to existing databases and provide options for managing database resources effectively.

3. **Configure Storage Tiers**:
   ```bash
   # Create log directory
   sudo mkdir -p /var/log/git-dashboard/logs
   sudo chown -R www-data:www-data /var/log/git-dashboard

   # Update storage configuration
   sudo cp /opt/git-dashboard/share/git-repo-logging/config/storage-config.json /opt/git-dashboard/config/
   sudo sed -i 's|"connection_string": ".*"|"connection_string": "postgresql://git_logger:your_secure_password@localhost:5432/git_logging"|g' /opt/git-dashboard/config/storage-config.json
   ```

4. **Update Nginx Configuration**:
   ```bash
   sudo cp nginx/git-dashboard.conf /etc/nginx/sites-available/
   sudo systemctl restart nginx
   ```

5. **Restart the Git Dashboard Service**:
   ```bash
   sudo systemctl restart git-dashboard
   ```

## Troubleshooting

### Common Issues

1. **C++ Logging Service Not Starting**:
   - Check the service status: `sudo systemctl status git-repo-logging`
   - Check the logs: `sudo journalctl -u git-repo-logging`
   - Ensure the binary has execute permissions: `sudo chmod +x /opt/git-dashboard/bin/git-repo-logging`

2. **Database Connection Issues**:
   - Check if PostgreSQL is running: `sudo systemctl status postgresql`
   - Verify the database exists: `sudo -u postgres psql -c "\l" | grep git_logging`
   - Check the connection string in the environment: `grep DB_CONNECTION_STRING /etc/systemd/system/git-repo-logging.service`
   - Test the connection: `sudo -u www-data psql "postgresql://git_logger:password@localhost:5432/git_logging" -c "SELECT 1;"`

3. **Storage Configuration Issues**:
   - Check if the storage configuration file exists: `ls -la /opt/git-dashboard/config/storage-config.json`
   - Verify the file permissions: `sudo chmod 644 /opt/git-dashboard/config/storage-config.json`
   - Check the log directory: `ls -la /var/log/git-dashboard/logs`
   - Ensure proper permissions: `sudo chown -R www-data:www-data /var/log/git-dashboard`

4. **API Endpoints Not Working**:
   - Check if the C++ service is running: `sudo systemctl status git-repo-logging`
   - Check if Nginx is properly configured: `sudo nginx -t`
   - Check the Nginx error logs: `sudo tail -f /var/log/nginx/error.log`

5. **Log Settings Modal Not Appearing**:
   - Check the browser console for JavaScript errors
   - Ensure the log-settings-modal.js file is properly loaded
   - Check if the button is properly added to the logs tab header

## Future Enhancements

1. **Real-time Log Streaming**: Implement WebSocket support for real-time log updates
2. **Advanced Filtering**: Add more advanced filtering options
3. **Log Analysis**: Add log analysis features
4. **Custom Parsers**: Allow users to define custom log parsers
5. **Export/Import**: Add functionality to export and import log configurations
