#include "database-service/security/security_manager.hpp"
#include "database-service/security/jwt.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/config_manager.hpp"
#include <format> // C++20 feature
#include <random>
#include <chrono>
#include <algorithm>
#include <openssl/sha.h>
#include <openssl/hmac.h>
#include <openssl/evp.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <sstream>
#include <iomanip>

namespace dbservice::security {

SecurityManager::SecurityManager(std::shared_ptr<core::ConnectionManager> connectionManager)
    : connectionManager_(connectionManager),
      initialized_(false),
      jwtSecret_("change-this-to-a-secure-secret-in-production"),
      accessTokenExpirationSeconds_(3600),
      refreshTokenExpirationSeconds_(86400) {

    // Load configuration
    auto& configManager = utils::ConfigManager::getInstance();
    jwtSecret_ = configManager.getString("security.jwt_secret", jwtSecret_);
    accessTokenExpirationSeconds_ = configManager.getInt("security.token_expiration_seconds", accessTokenExpirationSeconds_);
    refreshTokenExpirationSeconds_ = configManager.getInt("security.refresh_token_expiration_seconds", refreshTokenExpirationSeconds_);
}

SecurityManager::~SecurityManager() {
}

bool SecurityManager::initialize() {
    if (initialized_) {
        return true;
    }

    try {
        utils::Logger::info("Initializing security manager...");

        // Create users table
        if (!createUsersTable()) {
            utils::Logger::error("Failed to create users table");
            return false;
        }

        // Create permissions table
        if (!createPermissionsTable()) {
            utils::Logger::error("Failed to create permissions table");
            return false;
        }

        // Create refresh tokens table
        if (!createRefreshTokensTable()) {
            utils::Logger::error("Failed to create refresh tokens table");
            return false;
        }

        // Clean up expired refresh tokens
        cleanupExpiredRefreshTokens();

        initialized_ = true;
        utils::Logger::info("Security manager initialized successfully");
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during security manager initialization: {}", e.what()));
        return false;
    }
}

TokenPair SecurityManager::authenticate(const std::string& username, const std::string& password) {
    TokenPair emptyTokens;

    if (!initialized_ && !initialize()) {
        return emptyTokens;
    }

    try {
        // Get user
        std::string query = "SELECT password_hash FROM users WHERE username = $1 AND active = true";
        auto result = connectionManager_->executeQuery(query, {username});

        if (result.empty() || result[0].empty()) {
            utils::Logger::warning(std::format("Authentication failed: User {} not found or inactive", username));
            return emptyTokens;
        }

        std::string storedHash = result[0][0];

        // Verify password
        if (!verifyPassword(password, storedHash)) {
            utils::Logger::warning(std::format("Authentication failed: Invalid password for user {}", username));

            // Record failed authentication
            auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
            dbMetrics.recordAuthenticationMetric(false);

            return emptyTokens;
        }

        // Update last login time
        std::string updateQuery = "UPDATE users SET last_login = NOW() WHERE username = $1";
        connectionManager_->executeNonQuery(updateQuery, {username});

        // Generate token pair
        TokenPair tokens = generateTokenPair(username);

        // Record successful authentication
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordAuthenticationMetric(true);

        utils::Logger::info(std::format("User {} authenticated successfully", username));
        return tokens;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during authentication: {}", e.what()));
        return emptyTokens;
    }
}

bool SecurityManager::validateToken(const std::string& token) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Verify JWT token
        std::unordered_map<std::string, std::string> payload;
        if (!JWT::verify(token, jwtSecret_, payload)) {
            utils::Logger::warning("Invalid or expired JWT token");
            return false;
        }

        // Check if token is for access (not refresh)
        auto tokenTypeIt = payload.find("type");
        if (tokenTypeIt == payload.end() || tokenTypeIt->second != "access") {
            utils::Logger::warning("Token is not an access token");
            return false;
        }

        // Check if user exists and is active
        auto usernameIt = payload.find("sub");
        if (usernameIt == payload.end()) {
            utils::Logger::warning("Token does not contain a subject (username)");
            return false;
        }

        std::string username = usernameIt->second;
        std::string query = "SELECT 1 FROM users WHERE username = $1 AND active = true";
        auto result = connectionManager_->executeQuery(query, {username});

        if (result.empty()) {
            utils::Logger::warning(std::format("User {} not found or inactive", username));
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during token validation: {}", e.what()));
        return false;
    }
}

std::unordered_map<std::string, std::string> SecurityManager::getUserInfo(const std::string& token) {
    std::unordered_map<std::string, std::string> userInfo;

    if (!initialized_ && !initialize()) {
        return userInfo;
    }

    try {
        // Verify JWT token
        std::unordered_map<std::string, std::string> payload;
        if (!JWT::verify(token, jwtSecret_, payload)) {
            utils::Logger::warning("Invalid or expired JWT token");
            return userInfo;
        }

        // Get username from token
        auto usernameIt = payload.find("sub");
        if (usernameIt == payload.end()) {
            utils::Logger::warning("Token does not contain a subject (username)");
            return userInfo;
        }

        std::string username = usernameIt->second;

        // Get user info
        std::string query = "SELECT username, email, is_admin, created_at FROM users WHERE username = $1";
        auto result = connectionManager_->executeQuery(query, {username});

        if (result.empty() || result[0].size() < 4) {
            utils::Logger::warning(std::format("User info not found for {}", username));
            return userInfo;
        }

        userInfo["username"] = result[0][0];
        userInfo["email"] = result[0][1];
        userInfo["is_admin"] = result[0][2];
        userInfo["created_at"] = result[0][3];

        return userInfo;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during user info retrieval: {}", e.what()));
        return userInfo;
    }
}

bool SecurityManager::hasPermission(const std::string& username, const std::string& permission) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Check if user is admin
        std::string adminQuery = "SELECT is_admin FROM users WHERE username = $1";
        auto adminResult = connectionManager_->executeQuery(adminQuery, {username});

        if (!adminResult.empty() && !adminResult[0].empty() && adminResult[0][0] == "t") {
            return true; // Admins have all permissions
        }

        // Check specific permission
        std::string query = "SELECT 1 FROM permissions WHERE username = $1 AND permission = $2";
        auto result = connectionManager_->executeQuery(query, {username, permission});

        return !result.empty();
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during permission check: {}", e.what()));
        return false;
    }
}

bool SecurityManager::grantPermission(const std::string& username, const std::string& permission) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Check if user exists
        std::string userQuery = "SELECT 1 FROM users WHERE username = $1";
        auto userResult = connectionManager_->executeQuery(userQuery, {username});

        if (userResult.empty()) {
            utils::Logger::warning(std::format("Cannot grant permission: User {} not found", username));
            return false;
        }

        // Check if permission already granted
        if (hasPermission(username, permission)) {
            utils::Logger::info(std::format("Permission {} already granted to {}", permission, username));
            return true;
        }

        // Grant permission
        std::string query = "INSERT INTO permissions (username, permission, granted_at) VALUES ($1, $2, NOW())";
        int result = connectionManager_->executeNonQuery(query, {username, permission});

        if (result < 0) {
            utils::Logger::error(std::format("Failed to grant permission {} to {}", permission, username));
            return false;
        }

        utils::Logger::info(std::format("Permission {} granted to {} successfully", permission, username));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during permission granting: {}", e.what()));
        return false;
    }
}

bool SecurityManager::revokePermission(const std::string& username, const std::string& permission) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Check if permission is granted
        if (!hasPermission(username, permission)) {
            utils::Logger::info(std::format("Permission {} not granted to {}", permission, username));
            return true;
        }

        // Revoke permission
        std::string query = "DELETE FROM permissions WHERE username = $1 AND permission = $2";
        int result = connectionManager_->executeNonQuery(query, {username, permission});

        if (result < 0) {
            utils::Logger::error(std::format("Failed to revoke permission {} from {}", permission, username));
            return false;
        }

        utils::Logger::info(std::format("Permission {} revoked from {} successfully", permission, username));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during permission revocation: {}", e.what()));
        return false;
    }
}

bool SecurityManager::createUser(const std::string& username, const std::string& password, bool isAdmin) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Check if user already exists
        std::string checkQuery = "SELECT 1 FROM users WHERE username = $1";
        auto checkResult = connectionManager_->executeQuery(checkQuery, {username});

        if (!checkResult.empty()) {
            utils::Logger::warning(std::format("User {} already exists", username));
            return false;
        }

        // Hash password
        std::string passwordHash = hashPassword(password);

        // Create user
        std::string query = "INSERT INTO users (username, password_hash, is_admin, active, created_at) VALUES ($1, $2, $3, true, NOW())";
        int result = connectionManager_->executeNonQuery(query, {username, passwordHash, isAdmin ? "true" : "false"});

        if (result < 0) {
            utils::Logger::error(std::format("Failed to create user {}", username));
            return false;
        }

        utils::Logger::info(std::format("User {} created successfully", username));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during user creation: {}", e.what()));
        return false;
    }
}

bool SecurityManager::deleteUser(const std::string& username) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Begin transaction
        auto transaction = connectionManager_->beginTransaction();
        if (!transaction) {
            utils::Logger::error("Failed to begin transaction for user deletion");
            return false;
        }

        try {
            // Delete permissions
            std::string permissionsQuery = "DELETE FROM permissions WHERE username = $1";
            int permissionsResult = transaction->getConnection()->executeNonQuery(permissionsQuery, {username});

            if (permissionsResult < 0) {
                utils::Logger::error(std::format("Failed to delete permissions for user {}", username));
                transaction->rollback();
                return false;
            }

            // Delete refresh tokens
            std::string tokensQuery = "DELETE FROM refresh_tokens WHERE username = $1";
            int tokensResult = transaction->getConnection()->executeNonQuery(tokensQuery, {username});

            if (tokensResult < 0) {
                utils::Logger::error(std::format("Failed to delete refresh tokens for user {}", username));
                transaction->rollback();
                return false;
            }

            // Delete user
            std::string userQuery = "DELETE FROM users WHERE username = $1";
            int userResult = transaction->getConnection()->executeNonQuery(userQuery, {username});

            if (userResult < 0) {
                utils::Logger::error(std::format("Failed to delete user {}", username));
                transaction->rollback();
                return false;
            }

            // Commit transaction
            if (!transaction->commit()) {
                utils::Logger::error(std::format("Failed to commit user deletion for {}", username));
                return false;
            }

            utils::Logger::info(std::format("User {} deleted successfully", username));
            return true;
        } catch (const std::exception& e) {
            utils::Logger::error(std::format("Exception during user deletion transaction: {}", e.what()));
            transaction->rollback();
            return false;
        }
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during user deletion: {}", e.what()));
        return false;
    }
}

bool SecurityManager::createUsersTable() {
    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                is_admin BOOLEAN NOT NULL DEFAULT false,
                active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMP NOT NULL,
                last_login TIMESTAMP
            )
        )";

        int result = connectionManager_->executeNonQuery(query, {});

        if (result < 0) {
            utils::Logger::error("Failed to create users table");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during users table creation: {}", e.what()));
        return false;
    }
}

bool SecurityManager::createPermissionsTable() {
    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS permissions (
                id SERIAL PRIMARY KEY,
                username VARCHAR(255) NOT NULL,
                permission VARCHAR(255) NOT NULL,
                granted_at TIMESTAMP NOT NULL,
                UNIQUE (username, permission),
                FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
            )
        )";

        int result = connectionManager_->executeNonQuery(query, {});

        if (result < 0) {
            utils::Logger::error("Failed to create permissions table");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during permissions table creation: {}", e.what()));
        return false;
    }
}

bool SecurityManager::createRefreshTokensTable() {
    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS refresh_tokens (
                id SERIAL PRIMARY KEY,
                token VARCHAR(255) NOT NULL UNIQUE,
                username VARCHAR(255) NOT NULL,
                created_at TIMESTAMP NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                is_revoked BOOLEAN NOT NULL DEFAULT false,
                FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
            )
        )";

        int result = connectionManager_->executeNonQuery(query, {});

        if (result < 0) {
            utils::Logger::error("Failed to create refresh tokens table");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during refresh tokens table creation: {}", e.what()));
        return false;
    }
}

std::string SecurityManager::hashPassword(const std::string& password) {
    // Generate salt
    std::string salt = generateSalt(16);

    // Hash password with salt
    std::string saltedPassword = password + salt;
    std::string hash = sha256(saltedPassword);

    // Format: algorithm$salt$hash
    return "sha256$" + salt + "$" + hash;
}

bool SecurityManager::verifyPassword(const std::string& password, const std::string& hash) {
    // Parse hash
    std::vector<std::string> parts;
    std::string delimiter = "$";
    size_t pos = 0;
    std::string hashCopy = hash;

    while ((pos = hashCopy.find(delimiter)) != std::string::npos) {
        parts.push_back(hashCopy.substr(0, pos));
        hashCopy.erase(0, pos + delimiter.length());
    }
    parts.push_back(hashCopy);

    if (parts.size() != 3) {
        utils::Logger::error("Invalid hash format");
        return false;
    }

    std::string algorithm = parts[0];
    std::string salt = parts[1];
    std::string storedHash = parts[2];

    // Hash password with salt
    std::string saltedPassword = password + salt;
    std::string computedHash;

    if (algorithm == "sha256") {
        computedHash = sha256(saltedPassword);
    } else {
        utils::Logger::error(std::format("Unsupported hash algorithm: {}", algorithm));
        return false;
    }

    return computedHash == storedHash;
}

TokenPair SecurityManager::generateTokenPair(const std::string& username) {
    TokenPair tokens;

    try {
        // Get user info for token payload
        std::string query = "SELECT is_admin FROM users WHERE username = $1";
        auto result = connectionManager_->executeQuery(query, {username});

        bool isAdmin = false;
        if (!result.empty() && !result[0].empty()) {
            isAdmin = (result[0][0] == "t" || result[0][0] == "true");
        }

        // Create access token payload
        std::unordered_map<std::string, std::string> accessPayload;
        accessPayload["sub"] = username;
        accessPayload["type"] = "access";
        accessPayload["admin"] = isAdmin ? "true" : "false";

        // Create refresh token payload
        std::unordered_map<std::string, std::string> refreshPayload;
        refreshPayload["sub"] = username;
        refreshPayload["type"] = "refresh";
        refreshPayload["jti"] = generateSalt(16); // Unique token ID

        // Generate tokens
        tokens.accessToken = JWT::create(accessPayload, jwtSecret_, accessTokenExpirationSeconds_);
        tokens.refreshToken = JWT::create(refreshPayload, jwtSecret_, refreshTokenExpirationSeconds_);

        // Store refresh token in database
        auto expiresAt = std::chrono::system_clock::now() + std::chrono::seconds(refreshTokenExpirationSeconds_);
        storeRefreshToken(username, tokens.refreshToken, expiresAt);

        return tokens;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during token pair generation: {}", e.what()));
        return TokenPair();
    }
}

TokenPair SecurityManager::refreshAccessToken(const std::string& refreshToken) {
    TokenPair emptyTokens;

    if (!initialized_ && !initialize()) {
        return emptyTokens;
    }

    try {
        // Verify refresh token
        std::string username;
        if (!validateRefreshToken(refreshToken, username)) {
            utils::Logger::warning("Invalid refresh token");
            return emptyTokens;
        }

        // Generate new token pair
        TokenPair tokens = generateTokenPair(username);

        // Invalidate old refresh token
        std::string query = "UPDATE refresh_tokens SET is_revoked = true WHERE token = $1";
        connectionManager_->executeNonQuery(query, {refreshToken});

        utils::Logger::info(std::format("Access token refreshed for user {}", username));
        return tokens;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during token refresh: {}", e.what()));
        return emptyTokens;
    }
}

bool SecurityManager::invalidateTokens(const std::string& username) {
    if (!initialized_ && !initialize()) {
        return false;
    }

    try {
        // Invalidate all refresh tokens for the user
        std::string query = "UPDATE refresh_tokens SET is_revoked = true WHERE username = $1";
        int result = connectionManager_->executeNonQuery(query, {username});

        if (result < 0) {
            utils::Logger::error(std::format("Failed to invalidate tokens for user {}", username));
            return false;
        }

        utils::Logger::info(std::format("Tokens invalidated for user {}", username));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during token invalidation: {}", e.what()));
        return false;
    }
}

bool SecurityManager::storeRefreshToken(const std::string& username, const std::string& refreshToken,
                                      const std::chrono::system_clock::time_point& expiresAt) {
    try {
        // Convert expiration time to string
        auto expiresTime = std::chrono::system_clock::to_time_t(expiresAt);
        std::tm expiresTimeTm = *std::localtime(&expiresTime);
        char expiresBuffer[80];
        std::strftime(expiresBuffer, sizeof(expiresBuffer), "%Y-%m-%d %H:%M:%S", &expiresTimeTm);

        // Store token in database
        std::string query = R"(
            INSERT INTO refresh_tokens (token, username, created_at, expires_at, is_revoked)
            VALUES ($1, $2, NOW(), $3, false)
        )";

        int result = connectionManager_->executeNonQuery(query, {refreshToken, username, expiresBuffer});

        if (result < 0) {
            utils::Logger::error("Failed to store refresh token");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during refresh token storage: {}", e.what()));
        return false;
    }
}

bool SecurityManager::validateRefreshToken(const std::string& refreshToken, std::string& username) {
    try {
        // Verify JWT token
        std::unordered_map<std::string, std::string> payload;
        if (!JWT::verify(refreshToken, jwtSecret_, payload)) {
            utils::Logger::warning("Invalid or expired JWT refresh token");
            return false;
        }

        // Check if token is for refresh (not access)
        auto tokenTypeIt = payload.find("type");
        if (tokenTypeIt == payload.end() || tokenTypeIt->second != "refresh") {
            utils::Logger::warning("Token is not a refresh token");
            return false;
        }

        // Get username from token
        auto usernameIt = payload.find("sub");
        if (usernameIt == payload.end()) {
            utils::Logger::warning("Token does not contain a subject (username)");
            return false;
        }

        username = usernameIt->second;

        // Check if token is in database and not revoked
        std::string query = R"(
            SELECT 1 FROM refresh_tokens
            WHERE token = $1 AND username = $2 AND is_revoked = false AND expires_at > NOW()
        )";

        auto result = connectionManager_->executeQuery(query, {refreshToken, username});

        if (result.empty()) {
            utils::Logger::warning("Refresh token not found in database or revoked");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during refresh token validation: {}", e.what()));
        return false;
    }
}

void SecurityManager::cleanupExpiredRefreshTokens() {
    try {
        std::string query = "DELETE FROM refresh_tokens WHERE expires_at < NOW()";
        int result = connectionManager_->executeNonQuery(query, {});

        if (result > 0) {
            utils::Logger::info(std::format("Cleaned up {} expired refresh tokens", result));
        }
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during refresh token cleanup: {}", e.what()));
    }
}

void SecurityManager::setJwtSecret(const std::string& secret) {
    jwtSecret_ = secret;
}

void SecurityManager::setTokenExpirationTimes(int accessTokenExpirationSeconds, int refreshTokenExpirationSeconds) {
    accessTokenExpirationSeconds_ = accessTokenExpirationSeconds;
    refreshTokenExpirationSeconds_ = refreshTokenExpirationSeconds;
}

std::string SecurityManager::generateSalt(size_t length) {
    static const std::string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<> distribution(0, chars.size() - 1);

    std::string salt;
    salt.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        salt += chars[distribution(generator)];
    }

    return salt;
}

std::string SecurityManager::sha256(const std::string& input) {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, input.c_str(), input.size());
    SHA256_Final(hash, &sha256);

    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }

    return ss.str();
}

} // namespace dbservice::security
