-- Enhanced Revision Tracking Migration

-- Add more granular tracking columns to improvements table
ALTER TABLE improvements ADD COLUMN IF NOT EXISTS:
    -- Git Integration
    branch_name TEXT,
    merge_commit_hash TEXT,
    pr_number TEXT,
    
    -- Granular Change Tracking
    lines_added INTEGER DEFAULT 0,
    lines_deleted INTEGER DEFAULT 0,
    files_modified INTEGER DEFAULT 0,
    
    -- Rollback and Dependency Tracking
    depends_on_improvement_id TEXT,
    rollback_complexity INTEGER, -- 1-5 scale of complexity
    auto_rollback_script TEXT,
    manual_rollback_steps TEXT,
    
    -- Impact and Risk Assessment
    system_impact_score REAL DEFAULT 0.0, -- 0.0 to 10.0
    downtime_risk REAL DEFAULT 0.0,
    performance_impact REAL DEFAULT 0.0,
    
    -- Approval and Review
    reviewed_by TEXT,
    approved_by TEXT,
    approval_timestamp TIMESTAMP;

-- Create a new table for detailed file-level changes
CREATE TABLE IF NOT EXISTS improvement_file_changes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    improvement_id TEXT NOT NULL,
    project_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    change_type TEXT NOT NULL, -- 'added', 'modified', 'deleted'
    lines_added INTEGER DEFAULT 0,
    lines_deleted INTEGER DEFAULT 0,
    diff_content TEXT,
    
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (improvement_id) REFERENCES improvements(improvement_id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_improvement_file_changes_improvement 
    ON improvement_file_changes(improvement_id);

-- Trigger to automatically calculate file modification statistics
CREATE TRIGGER IF NOT EXISTS update_improvement_stats 
AFTER INSERT ON improvement_file_changes
BEGIN
    UPDATE improvements 
    SET 
        lines_added = lines_added + NEW.lines_added,
        lines_deleted = lines_deleted + NEW.lines_deleted,
        files_modified = files_modified + 1
    WHERE improvement_id = NEW.improvement_id;
END;

-- Enhanced Rollback Tracking View
CREATE VIEW IF NOT EXISTS rollback_tracking AS
SELECT 
    i.improvement_id,
    i.description,
    i.commit_hash,
    i.rollback_command,
    i.auto_rollback_script,
    i.manual_rollback_steps,
    i.rollback_complexity,
    i.system_impact_score,
    COUNT(fc.id) as files_changed,
    SUM(fc.lines_added) as total_lines_added,
    SUM(fc.lines_deleted) as total_lines_deleted
FROM 
    improvements i
LEFT JOIN 
    improvement_file_changes fc ON i.improvement_id = fc.improvement_id
GROUP BY 
    i.improvement_id;

-- Stored Procedure for Generating Rollback Script
CREATE PROCEDURE IF NOT EXISTS generate_rollback_script(
    IN p_improvement_id TEXT
)
BEGIN
    -- Placeholder for complex rollback script generation logic
    -- This would typically involve:
    -- 1. Retrieving the original file states
    -- 2. Generating Git-like revert commands
    -- 3. Creating a comprehensive rollback script
END;

-- Comments explaining the enhanced tracking
PRAGMA user_version = 2; -- Version of the database schema
