#pragma once
#include <string>
#include <unordered_map>
#include <mutex>
#include <memory>
#include <chrono>
#include <atomic>

namespace dbservice::metrics {

/**
 * @class DatabaseMetrics
 * @brief Collects and provides database-specific metrics
 * 
 * This class collects metrics specific to database operations such as
 * connection pool statistics, query performance, and transaction rates.
 * It provides API endpoints to expose these metrics to the UI Dashboard.
 */
class DatabaseMetrics {
public:
    /**
     * @brief Get the singleton instance
     * @return Reference to the singleton instance
     */
    static DatabaseMetrics& getInstance();
    
    /**
     * @brief Initialize the database metrics
     * @return True if initialization was successful
     */
    bool initialize();
    
    /**
     * @brief Record a connection pool metric
     * @param activeConnections Number of active connections
     * @param idleConnections Number of idle connections
     * @param waitingConnections Number of waiting connections
     * @param maxConnections Maximum number of connections
     */
    void recordConnectionPoolMetrics(int activeConnections, int idleConnections, 
                                   int waitingConnections, int maxConnections);
    
    /**
     * @brief Record a query execution metric
     * @param queryType Type of query (SELECT, INSERT, UPDATE, DELETE)
     * @param executionTimeMs Execution time in milliseconds
     * @param success Whether the query was successful
     */
    void recordQueryMetric(const std::string& queryType, double executionTimeMs, bool success);
    
    /**
     * @brief Record a transaction metric
     * @param committed Whether the transaction was committed or rolled back
     * @param durationMs Duration of the transaction in milliseconds
     */
    void recordTransactionMetric(bool committed, double durationMs);
    
    /**
     * @brief Record an authentication metric
     * @param success Whether the authentication was successful
     */
    void recordAuthenticationMetric(bool success);
    
    /**
     * @brief Get connection pool metrics
     * @return JSON object with connection pool metrics
     */
    nlohmann::json getConnectionPoolMetrics() const;
    
    /**
     * @brief Get query performance metrics
     * @return JSON object with query performance metrics
     */
    nlohmann::json getQueryPerformanceMetrics() const;
    
    /**
     * @brief Get transaction metrics
     * @return JSON object with transaction metrics
     */
    nlohmann::json getTransactionMetrics() const;
    
    /**
     * @brief Get authentication metrics
     * @return JSON object with authentication metrics
     */
    nlohmann::json getAuthenticationMetrics() const;
    
    /**
     * @brief Get all database metrics
     * @return JSON object with all database metrics
     */
    nlohmann::json getAllMetrics() const;
    
private:
    /**
     * @brief Constructor
     */
    DatabaseMetrics();
    
    /**
     * @brief Destructor
     */
    ~DatabaseMetrics();
    
    // Connection pool metrics
    std::atomic<int> activeConnections_;
    std::atomic<int> idleConnections_;
    std::atomic<int> waitingConnections_;
    std::atomic<int> maxConnections_;
    std::atomic<int> connectionWaitTimeMs_;
    std::atomic<int> maxConnectionsReached_;
    
    // Query performance metrics
    struct QueryMetrics {
        std::atomic<int> count;
        std::atomic<double> totalTimeMs;
        std::atomic<double> minTimeMs;
        std::atomic<double> maxTimeMs;
        std::atomic<int> errorCount;
    };
    
    mutable std::mutex queryMetricsMutex_;
    std::unordered_map<std::string, QueryMetrics> queryMetrics_;
    
    // Transaction metrics
    std::atomic<int> commitCount_;
    std::atomic<int> rollbackCount_;
    std::atomic<double> totalTransactionTimeMs_;
    std::atomic<double> maxTransactionTimeMs_;
    
    // Authentication metrics
    std::atomic<int> authSuccessCount_;
    std::atomic<int> authFailureCount_;
    
    bool initialized_;
};

} // namespace dbservice::metrics
