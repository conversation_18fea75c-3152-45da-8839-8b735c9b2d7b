# Project Tracker Nginx Modules Configuration
# Version: 1.0.0
# Last Updated: 2025-03-05

# Load required modules
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;
load_module modules/ngx_http_image_filter_module.so;
load_module modules/ngx_http_headers_more_filter_module.so;

# Brotli Compression Settings
brotli on;
brotli_comp_level 6;
brotli_min_length 20;
brotli_types 
    text/plain 
    text/css 
    text/javascript 
    application/javascript 
    application/x-javascript 
    application/json 
    application/xml 
    application/xml+rss 
    text/xml 
    image/svg+xml 
    application/x-font-ttf 
    application/x-font-opentype 
    application/vnd.ms-fontobject 
    image/x-icon;

# Image Filter Settings
image_filter_buffer 10M;
image_filter_interlace on;
image_filter_jpeg_quality 85;
image_filter_webp_quality 80;

# Headers More Module Settings
more_set_headers "Server: Project Tracker";
more_set_headers "X-Content-Type-Options: nosniff";
more_set_headers "X-XSS-Protection: 1; mode=block";
more_set_headers "X-Frame-Options: DENY";

# WebSocket Module Settings
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Performance Settings
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    multi_accept on;
    worker_connections 65535;
    use epoll;
}

# HTTP Settings
http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Buffer Size Settings
    client_body_buffer_size 16k;
    client_max_body_size 50m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 8k;

    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Cache Settings
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # MIME Types
    include mime.types;
    default_type application/octet-stream;

    # Logging Settings
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    # SSL Settings
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Monitoring Settings
    vhost_traffic_status_zone;
    vhost_traffic_status_filter_by_set_key $status status::*;
}
