#pragma once
#include "storage_types.hpp"
#include "memory_storage.hpp"
#include "database_storage.hpp"
#include "file_storage.hpp"
#include <memory>
#include <vector>
#include <mutex>

namespace logging {

class StorageManager {
public:
    StorageManager();
    ~StorageManager() = default;

    // Initialize with configuration
    bool initialize(const std::string& configPath);

    // Store a log entry in the appropriate tier
    bool store(const LogEntry& entry);

    // Store multiple log entries
    bool storeBatch(const std::vector<LogEntry>& entries);

    // Query logs across all tiers
    std::vector<LogEntry> query(const LogQueryParams& params);

    // Get combined statistics from all tiers
    StorageStats getStats();

    // Maintenance operations
    bool prune(std::chrono::system_clock::time_point olderThan);
    bool compact();
    bool backup(const std::string& backupPath);

    // Add a storage tier
    void addTier(std::shared_ptr<StorageTier> tier);

    // Get all tiers
    std::vector<std::shared_ptr<StorageTier>> getTiers() const;

private:
    // Find the appropriate tier for storing a log entry
    std::shared_ptr<StorageTier> findTierForEntry(const LogEntry& entry) const;

    // Find a database tier that is connected
    std::shared_ptr<DatabaseStorage> findConnectedDatabaseTier() const;

    // Convert unstructured log to JSON format
    std::string convertToJson(const LogEntry& entry) const;

    // Storage tiers, ordered by priority
    std::vector<std::shared_ptr<StorageTier>> tiers_;

    // Mutex for thread safety
    mutable std::mutex mutex_;
};

} // namespace logging
