@echo off
REM Database Service Deployment Script
REM This script deploys the Database Service to the Git server

setlocal enabledelayedexpansion

REM Configuration
set GIT_SERVER=git.chcit.org
set SSH_USER=btaylor-admin
set INSTALL_DIR=/opt/database-service
set CONFIG_DIR=/etc/database-service
set LOG_DIR=/var/log/database-service
set BUILD_DIR=/home/<USER>/database-service-build
set SOURCE_DIR=D:\Augment\project-tracker\database-service

:start
echo.
echo Database Service Deployment Script
echo =====================================
echo.

REM Check if SSH key exists
if not exist "%USERPROFILE%\.ssh\id_rsa" (
    echo Error: SSH key not found at %USERPROFILE%\.ssh\id_rsa
    echo Please generate an SSH key pair and add the public key to the server.
    exit /b 1
)

:menu
echo.
echo Deployment Options:
echo 1. Check SSH Connection
echo 2. Install Dependencies and Setup Certificate Access
echo 3. Comprehensive Server Readiness Check
echo 4. Deploy Source Code
echo 5. Build Application
echo 6. Configure Service
echo 7. Install Service
echo 8. Start Service
echo 9. Full Deployment (Steps 1-8)
echo 10. Check Service Status
echo 11. View Logs
echo 12. Uninstall Service
echo 13. Database Management
echo 14. Backup Configuration
echo 15. Restore Configuration
echo 0. Exit
echo.
set /p choice=Enter your choice (0-15):

if "%choice%"=="0" goto :eof
if "%choice%"=="1" goto check_ssh
if "%choice%"=="2" goto install_dependencies
if "%choice%"=="3" goto server_readiness
if "%choice%"=="4" goto deploy_source
if "%choice%"=="5" goto build_app
if "%choice%"=="6" goto configure_service
if "%choice%"=="7" goto install_service
if "%choice%"=="8" goto start_service
if "%choice%"=="9" goto full_deployment
if "%choice%"=="10" goto check_status
if "%choice%"=="11" goto view_logs
if "%choice%"=="12" goto uninstall_service
if "%choice%"=="13" goto database_management
if "%choice%"=="14" goto backup_config
if "%choice%"=="15" goto restore_config

echo Invalid choice. Please try again.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:check_ssh
echo.
echo Checking SSH connection to %GIT_SERVER%...
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    echo Failed to connect to %GIT_SERVER%. Please check your SSH configuration.
) else (
    echo SSH connection successful.
)
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:install_dependencies
echo.
echo Installing dependencies and setting up certificate access...

REM Copy the certificate access setup script to the server
echo Copying certificate access setup script...
scp "%SOURCE_DIR%\scripts\setup-certificate-access.sh" %SSH_USER%@%GIT_SERVER%:/home/<USER>/

REM Install required packages
echo Installing required packages...
ssh %SSH_USER%@%GIT_SERVER% "sudo apt-get update && sudo apt-get install -y build-essential cmake libpq-dev libssl-dev libcurl4-openssl-dev libjsoncpp-dev nlohmann-json3-dev"

REM Set up certificate access
echo Setting up certificate access...
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /home/<USER>/setup-certificate-access.sh && /home/<USER>/setup-certificate-access.sh"

echo Dependencies installed and certificate access set up successfully.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:server_readiness
echo.
echo Performing comprehensive server readiness check...

REM Check SSH connection
echo Checking SSH connection...
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    echo SSH connection failed. Please check your SSH configuration.
    echo.
    echo Press any key to return to the main menu...
    pause > nul
    goto start
)

REM Check required packages
echo Checking required packages...
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -E 'build-essential|cmake|libpq-dev|libssl-dev|libcurl4-openssl-dev|libjsoncpp-dev|nlohmann-json3-dev'"

REM Check PostgreSQL
echo Checking PostgreSQL...
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active postgresql"

REM Check certificate access
echo Checking certificate access...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null 2>&1 && echo 'Certificate access OK' || echo 'Certificate access FAILED'"

REM Check disk space
echo Checking disk space...
ssh %SSH_USER%@%GIT_SERVER% "df -h | grep -E '/$|/home'"

REM Check memory
echo Checking memory...
ssh %SSH_USER%@%GIT_SERVER% "free -h"

REM Check CPU
echo Checking CPU...
ssh %SSH_USER%@%GIT_SERVER% "lscpu | grep -E 'Model name|CPU\(s\)'"

echo Server readiness check completed.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:deploy_source
echo.
echo Deploying source code to %GIT_SERVER%...

REM Create build directory
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %BUILD_DIR%"

REM Copy source files
echo Copying source files...
scp -r "%SOURCE_DIR%\src" "%SOURCE_DIR%\include" "%SOURCE_DIR%\CMakeLists.txt" "%SOURCE_DIR%\scripts" %SSH_USER%@%GIT_SERVER%:%BUILD_DIR%/

echo Source code deployed successfully.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:build_app
echo.
echo Building application on %GIT_SERVER%...

REM Create build directory and build the application
ssh %SSH_USER%@%GIT_SERVER% "cd %BUILD_DIR% && mkdir -p build && cd build && cmake .. -DCMAKE_BUILD_TYPE=Release && make -j$(nproc); exit $?"
set BUILD_RESULT=%ERRORLEVEL%

if %BUILD_RESULT% neq 0 (
    echo.
    echo Build failed with errors. Please check the output above for details.
) else (
    echo.
    echo Application built successfully.
)
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:configure_service
echo.
echo Configuring service on %GIT_SERVER%...

REM Create configuration directory
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %CONFIG_DIR% %LOG_DIR%"

REM Copy configuration files
echo Copying configuration files...
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/*.json %CONFIG_DIR%/"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR% %LOG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %LOG_DIR%"

echo Service configured successfully.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:install_service
echo.
echo Installing service on %GIT_SERVER%...

REM Check if build was successful
ssh %SSH_USER%@%GIT_SERVER% "[ -f %BUILD_DIR%/build/bin/database-service ]"
if %ERRORLEVEL% neq 0 (
    echo Error: The database-service binary was not found. Please build the application first.
    echo.
    echo Press any key to return to the main menu...
    pause > nul
    goto start
)

REM Create installation directory
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %INSTALL_DIR%/bin %INSTALL_DIR%/lib"

REM Copy binaries and libraries
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/bin/* %INSTALL_DIR%/bin/"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/lib/* %INSTALL_DIR%/lib/ 2>/dev/null || echo No libraries to copy"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R root:database-service %INSTALL_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR% %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR%/bin/*"

REM Install systemd service
echo Creating systemd service...
ssh %SSH_USER%@%GIT_SERVER% "cat > /home/<USER>/database-service.service << 'EOF'
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=%INSTALL_DIR%/bin/database-service --config %CONFIG_DIR%/config.json
Restart=on-failure
WorkingDirectory=%INSTALL_DIR%
Environment=\"LD_LIBRARY_PATH=%INSTALL_DIR%/lib\"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF"

ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/database-service.service /etc/systemd/system/"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

echo Service installed successfully.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:start_service
echo.
echo Starting service on %GIT_SERVER%...

REM Check if service file exists
ssh %SSH_USER%@%GIT_SERVER% "[ -f /etc/systemd/system/database-service.service ]"
if %ERRORLEVEL% neq 0 (
    echo Error: Service file not found. Please install the service first.
    echo.
    echo Press any key to return to the main menu...
    pause > nul
    goto start
)

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable database-service && sudo systemctl start database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"

echo Service started successfully.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:full_deployment
echo.
echo Performing full deployment...

REM Check SSH connection
echo Step 1: Checking SSH connection...
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    echo SSH connection failed. Please check your SSH configuration.
    echo.
    echo Press any key to return to the main menu...
    pause > nul
    goto start
)
echo SSH connection successful.

REM Install dependencies and set up certificate access
echo Step 2: Installing dependencies and setting up certificate access...
scp "%SOURCE_DIR%\scripts\setup-certificate-access.sh" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo apt-get update && sudo apt-get install -y build-essential cmake libpq-dev libssl-dev libcurl4-openssl-dev libjsoncpp-dev nlohmann-json3-dev"
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /home/<USER>/setup-certificate-access.sh && /home/<USER>/setup-certificate-access.sh"
echo Dependencies installed and certificate access set up successfully.

REM Perform server readiness check
echo Step 3: Performing server readiness check...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null 2>&1 && echo 'Certificate access OK' || echo 'Certificate access FAILED'"
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active postgresql"
echo Server readiness check completed.

REM Deploy source code
echo Step 4: Deploying source code...
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %BUILD_DIR%"
scp -r "%SOURCE_DIR%\src" "%SOURCE_DIR%\include" "%SOURCE_DIR%\CMakeLists.txt" "%SOURCE_DIR%\scripts" %SSH_USER%@%GIT_SERVER%:%BUILD_DIR%/
echo Source code deployed successfully.

REM Build application
echo Step 5: Building application...
ssh %SSH_USER%@%GIT_SERVER% "cd %BUILD_DIR% && mkdir -p build && cd build && cmake .. -DCMAKE_BUILD_TYPE=Release && make -j$(nproc); exit $?"
set BUILD_RESULT=%ERRORLEVEL%

if %BUILD_RESULT% neq 0 (
    echo.
    echo Build failed with errors. Full deployment cannot continue.
    echo.
    echo Press any key to return to the main menu...
    pause > nul
    goto start
)
echo Application built successfully.

REM Configure service
echo Step 6: Configuring service...
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %CONFIG_DIR% %LOG_DIR%"
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/*.json %CONFIG_DIR%/"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR% %LOG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %LOG_DIR%"

echo Service configured successfully.

REM Install service
echo Step 7: Installing service...
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/bin/* %INSTALL_DIR%/bin/ 2>/dev/null || echo Warning: No binaries found to copy"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/lib/* %INSTALL_DIR%/lib/ 2>/dev/null || echo No libraries to copy"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R root:database-service %INSTALL_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR% %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo find %INSTALL_DIR%/bin -type f -exec sudo chmod 750 {} \; 2>/dev/null || echo No binaries to set permissions on"
ssh %SSH_USER%@%GIT_SERVER% "cat > /home/<USER>/database-service.service << 'EOF'
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=%INSTALL_DIR%/bin/database-service --config %CONFIG_DIR%/config.json
Restart=on-failure
WorkingDirectory=%INSTALL_DIR%
Environment=\"LD_LIBRARY_PATH=%INSTALL_DIR%/lib\"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/database-service.service /etc/systemd/system/"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

echo Service installed successfully.

REM Start service
echo Step 8: Starting service...
ssh %SSH_USER%@%GIT_SERVER% "if [ -f %INSTALL_DIR%/bin/database-service ]; then sudo systemctl enable database-service && sudo systemctl start database-service && sudo systemctl status database-service; else echo 'Error: database-service binary not found. Service not started.'; fi"

echo Full deployment completed.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:check_status
echo.
echo Checking service status on %GIT_SERVER%...

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"

echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:view_logs
echo.
echo Viewing logs on %GIT_SERVER%...

ssh %SSH_USER%@%GIT_SERVER% "sudo journalctl -u database-service -n 50"

echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:uninstall_service
echo.
echo Uninstalling service from %GIT_SERVER%...

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl stop database-service && sudo systemctl disable database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo rm /etc/systemd/system/database-service.service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

echo Do you want to remove the installation directory and configuration? (y/n)
set /p remove_files=

if /i "%remove_files%"=="y" (
    ssh %SSH_USER%@%GIT_SERVER% "sudo rm -rf %INSTALL_DIR% %CONFIG_DIR%"
    echo Service and files removed successfully.
) else (
    echo Service uninstalled, but files preserved.
)

echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:database_management
echo.
echo Database Management Options:
echo 1. Check PostgreSQL Status
echo 2. List Databases
echo 3. Create Database
echo 4. Drop Database
echo 5. List Users
echo 6. Create User
echo 7. Drop User
echo 8. Back to Main Menu
echo.
set /p db_choice=Enter your choice (1-8):

if "%db_choice%"=="1" (
    echo.
    echo Checking PostgreSQL status...
    ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status postgresql"
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="2" (
    echo.
    echo Listing databases...
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\l'"
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="3" (
    echo.
    set /p db_name=Enter database name:
    echo Creating database %db_name%...
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'CREATE DATABASE %db_name%;'"
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="4" (
    echo.
    set /p db_name=Enter database name to drop:
    echo WARNING: This will permanently delete the database %db_name% and all its data!
    set /p confirm=Are you sure? (y/n): 
    if /i "%confirm%"=="y" (
        echo Dropping database %db_name%...
        ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP DATABASE %db_name%;'"
    )
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="5" (
    echo.
    echo Listing users...
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\du'"
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="6" (
    echo.
    set /p user_name=Enter user name:
    set /p user_pass=Enter password:
    echo Creating user %user_name%...
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c \"CREATE USER %user_name% WITH PASSWORD '%user_pass%';\""
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="7" (
    echo.
    set /p user_name=Enter user name to drop:
    echo WARNING: This will permanently delete the user %user_name%!
    set /p confirm=Are you sure? (y/n):
    if /i "%confirm%"=="y" (
        echo Dropping user %user_name%...
        ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP USER %user_name%;'"
    )
    echo.
    echo Press any key to return to the database management menu...
    pause > nul
    goto database_management
)

if "%db_choice%"=="8" goto start

echo Invalid choice. Please try again.
echo.
echo Press any key to return to the database management menu...
pause > nul
goto database_management

:backup_config
echo.
echo Backing up configuration from %GIT_SERVER%...

set timestamp=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set backup_dir=backups\%timestamp%

mkdir "%SOURCE_DIR%\%backup_dir%" 2>nul

ssh %SSH_USER%@%GIT_SERVER% "sudo tar -czf /home/<USER>/database-config-backup.tar.gz %CONFIG_DIR%"
scp %SSH_USER%@%GIT_SERVER%:/home/<USER>/database-config-backup.tar.gz "%SOURCE_DIR%\%backup_dir%\"
ssh %SSH_USER%@%GIT_SERVER% "rm /home/<USER>/database-config-backup.tar.gz"

echo Configuration backed up to %SOURCE_DIR%\%backup_dir%\database-config-backup.tar.gz
echo.
echo Press any key to return to the main menu...
pause > nul
goto start

:restore_config
echo.
echo Available backups:
dir /b /ad "%SOURCE_DIR%\backups"

echo.
set /p backup_timestamp=Enter backup timestamp to restore:

if not exist "%SOURCE_DIR%\backups\%backup_timestamp%\database-config-backup.tar.gz" (
    echo Backup not found.
    echo.
    echo Press any key to return to the main menu...
    pause > nul
    goto start
)

echo Restoring configuration to %GIT_SERVER%...

scp "%SOURCE_DIR%\backups\%backup_timestamp%\database-config-backup.tar.gz" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo tar -xzf /home/<USER>/database-config-backup.tar.gz -C /"
ssh %SSH_USER%@%GIT_SERVER% "rm /home/<USER>/database-config-backup.tar.gz"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"

echo Configuration restored successfully.
echo.
echo Press any key to return to the main menu...
pause > nul
goto start
