[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "project-tracker"
version = "0.1.0"
description = "Project Management and Tracking System"
readme = "README.md"
requires-python = ">=3.10"

dependencies = [
    "flask==2.3.2",
    "flask-sqlalchemy==3.0.3",
    "flask-marshmallow==0.15.0",
    "flask-jwt-extended==4.5.0",
    "python-dotenv==1.0.0",
    "psycopg2-binary==2.9.6",
    "sqlalchemy==2.0.15"
]

[project.optional-dependencies]
dev = [
    "pytest==7.3.1",
    "pytest-cov==4.0.0",
    "black==23.3.0",
    "mypy==1.3.0",
    "flake8==6.0.0",
    "isort==5.12.0"
]

[project.scripts]
validate-config = "python:scripts.validate-configuration.main"

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = "--cov=src --cov-report=term-missing"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
ignore_missing_imports = true
check_untyped_defs = true

[tool.flake8]
max-line-length = 88
extend-ignore = "E203"
