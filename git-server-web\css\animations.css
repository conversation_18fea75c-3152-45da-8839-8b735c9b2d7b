/* animations.css - Animation definitions */

/* Button Spin Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes fade {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* Animation classes */
.spinning,
.refresh-btn.refreshing i {
    animation: spin 1s linear infinite;
}

.pulsing {
    animation: pulse 1s ease-in-out infinite;
}

.bouncing {
    animation: bounce 1s ease infinite;
}

.shaking {
    animation: shake 0.5s ease infinite;
}

.fading {
    animation: fade 1.5s ease infinite;
}
