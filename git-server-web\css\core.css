/* core.css - Core layout and base styles */

/* General Layout */
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden; /* Prevent main browser window scrolling */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    height: calc(100vh - 40px); /* 40px accounts for top and bottom padding */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Card Styling */
.card {
    margin-bottom: 0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    box-sizing: border-box; /* Add this to ensure padding doesn't affect width */
}

.card-header {
    font-weight: bold;
    background-color: #f8f9fa;
}

/* Nested cards styling */
.card .card {
    box-shadow: none;
    border: 1px solid rgba(0,0,0,.125);
    box-sizing: border-box; /* Add this to ensure padding doesn't affect width */
    margin-bottom: 15px;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 6px;
}
.status-indicator.loading { background-color: #ffc107; }
.status-indicator.success { background-color: #28a745; }
.status-indicator.error { background-color: #dc3545; }

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}
.toast {
    padding: 15px 20px;
    border-radius: 4px;
    margin-bottom: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.toast-info { background-color: #cce5ff; color: #004085; }
.toast-success { background-color: #d4edda; color: #155724; }
.toast-error { background-color: #f8d7da; color: #721c24; }
.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 0 5px;
}

/* Tab Content Styling */
.tab-content {
    height: calc(100vh - 190px); /* Account for header and tabs */
    overflow: hidden;
    display: flex;
}

.tab-pane {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.tab-pane > .card {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
}

.tab-pane > .card > .card-body {
    flex: 1;
    overflow: auto;
}

/* Consistent card height for troubleshooting cards */
#troubleshooting-container {
    height: 100%;
    overflow: visible;
}

#troubleshooting-container .card {
    height: 100%;
}

#troubleshooting-container .card-body {
    height: calc(100% - 50px); /* Account for card header */
    overflow-y: auto;
}

.nav-tabs {
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
    padding-top: 10px;
    margin-bottom: 0 !important; /* Override Bootstrap */
}
