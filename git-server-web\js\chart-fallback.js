/**
 * Chart Fallback Script
 * This script provides a fallback mechanism for the GitCommitHistoryChart
 * if the main implementation fails to load or initialize properly.
 */

(function() {
    console.log('[ChartFallback] Initializing chart fallback mechanism');
    
    // Check if GitCommitHistoryChart exists
    if (typeof window.GitCommitHistoryChart === 'undefined') {
        console.warn('[ChartFallback] GitCommitHistoryChart not found, loading fallback');
        
        // Create a minimal implementation
        window.GitCommitHistoryChart = {
            config: {
                chartId: 'commit-history-chart',
                containerId: 'commit-history-container',
                loadingId: 'commit-chart-loading',
                errorId: 'commit-chart-error',
                messageId: 'commit-chart-message',
                days: 30,
                colors: {
                    background: 'rgba(54, 162, 235, 0.5)',
                    border: 'rgba(54, 162, 235, 1)'
                }
            },
            
            state: {
                chart: null
            },
            
            init() {
                console.log('[ChartFallback] GitCommitHistoryChart.init() - Fallback initialization');
                return this;
            },
            
            cleanupChart() {
                if (this.state.chart) {
                    this.state.chart.destroy();
                    this.state.chart = null;
                    console.log('[ChartFallback] Chart instance cleaned up');
                }
            },
            
            initChart(repoName) {
                console.log(`[ChartFallback] initChart called for ${repoName}`);
                this.fetchAndRenderChart(repoName);
            },
            
            async fetchAndRenderChart(repoName) {
                console.log(`[ChartFallback] fetchAndRenderChart called for ${repoName}`);
                
                // Get elements
                const container = document.getElementById(this.config.containerId);
                const canvas = document.getElementById(this.config.chartId);
                const loading = document.getElementById(this.config.loadingId);
                const error = document.getElementById(this.config.errorId);
                const message = document.getElementById(this.config.messageId);
                
                try {
                    // Show loading state
                    if (loading) loading.style.display = 'flex';
                    if (error) error.style.display = 'none';
                    if (message) message.style.display = 'none';
                    
                    // Clean up existing chart
                    this.cleanupChart();
                    
                    // Format repo name (strip .git extension)
                    const cleanName = repoName.trim().replace(/\.git$/, '');
                    
                    // Fetch data from API
                    const apiUrl = `/api/git/repository/${encodeURIComponent(cleanName)}/commits?days=${this.config.days}`;
                    console.log(`[ChartFallback] Fetching commit history from ${apiUrl}`);
                    
                    const response = await fetch(apiUrl);
                    
                    if (!response.ok) {
                        throw new Error(`API returned ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    console.log('[ChartFallback] API response:', data);
                    
                    // Check for API error
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    // Check for commits array
                    if (!data.commits || !Array.isArray(data.commits)) {
                        throw new Error('Invalid response format: missing commits array');
                    }
                    
                    // Format data for chart
                    const chartData = this.formatCommitData(data.commits);
                    
                    // Handle empty data
                    if (chartData.labels.length === 0 || chartData.datasets[0].data.every(val => val === 0)) {
                        console.log('[ChartFallback] No commit data found');
                        if (loading) loading.style.display = 'none';
                        if (message) {
                            message.textContent = 'No commits found in the selected time period';
                            message.style.display = 'block';
                        }
                        return;
                    }
                    
                    // Create or update chart container
                    if (!container.querySelector('canvas')) {
                        const newCanvas = document.createElement('canvas');
                        newCanvas.id = this.config.chartId;
                        container.appendChild(newCanvas);
                    }
                    
                    // Update UI
                    if (loading) loading.style.display = 'none';
                    
                    // Create chart
                    this.createChart(document.getElementById(this.config.chartId), chartData);
                    
                } catch (error) {
                    console.error('[ChartFallback] Error fetching/rendering commit history:', error);
                    const errorEl = document.getElementById(this.config.errorId);
                    const loadingEl = document.getElementById(this.config.loadingId);
                    
                    if (loadingEl) loadingEl.style.display = 'none';
                    if (errorEl) {
                        errorEl.textContent = `Failed to load commit history: ${error.message}`;
                        errorEl.style.display = 'flex';
                    }
                }
            },
            
            formatCommitData(commits) {
                // Group commits by date
                const commitsByDate = {};
                const today = new Date();
                
                // Initialize all dates in the last N days with 0 commits
                for (let i = 0; i < this.config.days; i++) {
                    const date = new Date(today);
                    date.setDate(date.getDate() - i);
                    const dateStr = date.toISOString().split('T')[0];
                    commitsByDate[dateStr] = 0;
                }
                
                // Count commits for each date
                commits.forEach(commit => {
                    const date = new Date(commit.date);
                    const dateStr = date.toISOString().split('T')[0];
                    
                    if (commitsByDate[dateStr] !== undefined) {
                        commitsByDate[dateStr]++;
                    }
                });
                
                // Convert to arrays for Chart.js
                const labels = [];
                const data = [];
                
                Object.keys(commitsByDate)
                    .sort()
                    .forEach(date => {
                        labels.push(date);
                        data.push(commitsByDate[date]);
                    });
                
                return {
                    labels: labels,
                    datasets: [{
                        label: 'Commits',
                        data: data,
                        backgroundColor: this.config.colors.background,
                        borderColor: this.config.colors.border,
                        borderWidth: 1
                    }]
                };
            },
            
            createChart(canvas, chartData) {
                console.log('[ChartFallback] createChart called with canvas and data:', canvas, chartData);
                
                if (!canvas) {
                    console.error('[ChartFallback] Cannot create chart: canvas element not provided');
                    return;
                }
                
                if (!chartData || !chartData.labels || !chartData.datasets) {
                    console.error('[ChartFallback] Cannot create chart: invalid chart data', chartData);
                    return;
                }
                
                try {
                    // Destroy any existing chart instance
                    if (this.state.chart) {
                        this.state.chart.destroy();
                        this.state.chart = null;
                    }
                    
                    // Configuration for chart
                    const config = {
                        type: 'bar',
                        data: chartData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0,
                                        stepSize: 1
                                    }
                                }
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: `Commit History (Last ${this.config.days} Days)`
                                },
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        title: function(context) {
                                            return context[0].label;
                                        },
                                        label: function(context) {
                                            const count = context.raw;
                                            return `${count} commit${count !== 1 ? 's' : ''}`;
                                        }
                                    }
                                }
                            }
                        }
                    };
                    
                    // Create chart
                    this.state.chart = new Chart(canvas, config);
                    console.log('[ChartFallback] Chart created successfully');
                } catch (error) {
                    console.error('[ChartFallback] Error creating chart:', error);
                    throw new Error(`Chart creation failed: ${error.message}`);
                }
            }
        };
        
        console.log('[ChartFallback] Fallback GitCommitHistoryChart created');
    } else {
        console.log('[ChartFallback] GitCommitHistoryChart already exists, no fallback needed');
    }
})();
