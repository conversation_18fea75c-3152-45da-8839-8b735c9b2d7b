# <PERSON><PERSON><PERSON> and <PERSON><PERSON> Configuration Guide for Git Dashboard

## Overview

This document provides detailed instructions for setting up and configuring <PERSON><PERSON><PERSON> and <PERSON><PERSON> for the Git Dashboard backend. Flask serves as the API backend, while <PERSON><PERSON> is used as a production-ready WSGI HTTP server.

## Flask Installation and Setup

### Creating a Python Virtual Environment

```bash
# Install required packages
sudo apt update
sudo apt install -y python3.10 python3.10-venv python3-pip

# Create application directory
sudo mkdir -p /opt/git-dashboard
sudo mkdir -p /opt/git-dashboard/data/history
sudo mkdir -p /opt/git-dashboard/logs

# Create a virtual environment
cd /opt/git-dashboard
sudo python3.10 -m venv venv

# Activate the virtual environment
source venv/bin/activate

# Install required Python packages
pip install flask==3.0.0 gunicorn==21.2.0 psutil==5.9.5 GitPython==3.1.40
pip install Werkzeug==3.0.1 pytest==7.4.0

# Create requirements.txt
pip freeze > requirements.txt
```

### Flask Application Structure

The Flask application should follow this structure:

```
/opt/git-dashboard/
├── app.py               # Main Flask application
├── collect-metrics.sh   # Script to collect metrics
├── data/                # Data directory
│   ├── history/         # Historical metrics
│   └── metrics_history.json  # Current metrics
├── logs/                # Log directory
└── static/              # Static files (served by Nginx)
    ├── css/             # CSS files
    ├── js/              # JavaScript files
    └── index.html       # Main dashboard HTML
```

### Example Flask Application (app.py)

```python
#!/usr/bin/env python3

from datetime import datetime, timedelta
from flask import Flask, jsonify, request, render_template
import psutil
import os
import json
import socket
import logging
import git
from logging.handlers import RotatingFileHandler

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'git-dashboard.log')

logger = logging.getLogger('git-dashboard')
logger.setLevel(logging.INFO)
handler = RotatingFileHandler(log_file, maxBytes=10485760, backupCount=10)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# Initialize Flask application
app = Flask(__name__)

# Configuration
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
METRICS_FILE = os.path.join(DATA_DIR, 'metrics_history.json')
HISTORY_DIR = os.path.join(DATA_DIR, 'history')

def get_system_metrics():
    """Collect current system metrics"""
    try:
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        load_avg = os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
        
        # Get hostname
        hostname = socket.gethostname()
        
        # Format data
        data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'cpu': {
                'percent': round(cpu_percent, 2)
            },
            'memory': {
                'total': round(memory.total / (1024**3), 2),  # GB
                'used': round(memory.used / (1024**3), 2),     # GB
                'percent': round(memory.percent, 2)
            },
            'disk': {
                'total': round(disk.total / (1024**3), 2),     # GB
                'used': round(disk.used / (1024**3), 2),       # GB
                'percent': round(disk.percent, 2)
            },
            'load': {
                '1min': round(load_avg[0], 2),
                '5min': round(load_avg[1], 2),
                '15min': round(load_avg[2], 2)
            },
            'host': hostname
        }
        
        return data
    except Exception as e:
        logger.error(f"Error collecting system metrics: {e}")
        return {}

def get_git_metrics():
    """Get metrics from Git repositories"""
    try:
        # Load metrics from file
        if os.path.exists(METRICS_FILE):
            with open(METRICS_FILE, 'r') as f:
                data = json.load(f)
                # Check if git metrics exist
                if 'git' in data:
                    return data['git']
        
        # Fallback - return empty metrics
        return {
            'total_repos': 0,
            'total_commits': 0,
            'total_branches': 0,
            'active_repos': 0,
            'recent_commits': []
        }
    except Exception as e:
        logger.error(f"Error reading git metrics: {e}")
        return {}

@app.route('/')
def home():
    """Serve static dashboard"""
    return app.send_static_file('index.html')

@app.route('/api/metrics/current')
def metrics():
    """API endpoint for current metrics"""
    try:
        system_metrics = get_system_metrics()
        git_metrics = get_git_metrics()
        
        response = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'system': system_metrics,
            'git': git_metrics
        }
        
        logger.info("API metrics request successful")
        return jsonify(response)
    except Exception as e:
        logger.error(f"API metrics error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/metrics/history')
def metrics_history():
    """API endpoint for historical metrics"""
    try:
        # Load metrics history from files
        hours = request.args.get('hours', default=24, type=int)
        limit = min(hours, 168)  # Max 7 days (168 hours)
        
        now = datetime.now()
        cutoff = now - timedelta(hours=limit)
        
        # Load current metrics file
        current_metrics = []
        if os.path.exists(METRICS_FILE):
            with open(METRICS_FILE, 'r') as f:
                data = json.load(f)
                if 'timestamp' in data:
                    current_metrics = [data]
        
        # Load historical metrics
        history = []
        if os.path.exists(HISTORY_DIR):
            files = os.listdir(HISTORY_DIR)
            for filename in sorted(files, reverse=True):
                if filename.endswith('.json'):
                    file_path = os.path.join(HISTORY_DIR, filename)
                    with open(file_path, 'r') as f:
                        try:
                            data = json.load(f)
                            # Check if data has timestamp
                            if 'timestamp' in data:
                                # Convert timestamp to datetime
                                ts = datetime.strptime(data['timestamp'], '%Y-%m-%d %H:%M:%S')
                                if ts >= cutoff:
                                    history.append(data)
                        except json.JSONDecodeError:
                            logger.error(f"Could not parse JSON from {file_path}")
        
        # Combine and sort metrics
        all_metrics = current_metrics + history
        all_metrics.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Limit to requested timeframe
        filtered_metrics = [m for m in all_metrics if datetime.strptime(m['timestamp'], '%Y-%m-%d %H:%M:%S') >= cutoff]
        
        logger.info(f"API history request successful: {len(filtered_metrics)} records")
        return jsonify({
            'metrics': filtered_metrics,
            'count': len(filtered_metrics),
            'period_hours': limit
        })
    except Exception as e:
        logger.error(f"API history error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Simple status endpoint for health checks"""
    return jsonify({'status': 'ok', 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')})

if __name__ == '__main__':
    # In development only
    app.run(host='0.0.0.0', port=8000, debug=True)
```

## Gunicorn Configuration and Setup

### Installing Gunicorn

Gunicorn should already be installed in the virtual environment, but ensure it's in your requirements.txt:

```
gunicorn==21.2.0
```

### Creating a Gunicorn Configuration File

Create a Gunicorn configuration file for more control:

```bash
sudo nano /opt/git-dashboard/gunicorn_config.py
```

Add the following content:

```python
# Gunicorn configuration file

# Binding
bind = "0.0.0.0:8000"

# Worker processes
workers = 4  # Generally (2 x NUM_CORES) + 1
worker_class = "sync"
threads = 2

# Timeout
timeout = 30
keepalive = 2

# Server mechanics
backlog = 2048
max_requests = 1000
max_requests_jitter = 50

# Logging
logfile = "/opt/git-dashboard/logs/gunicorn.log"
loglevel = "info"
accesslog = "/opt/git-dashboard/logs/access.log"
access_log_format = '"%({X-Real-IP}i)s" %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# Process naming
proc_name = "git-dashboard"

# Security
user = "root"
group = "root"
umask = 0o22

# SSL (if not using Nginx for SSL termination)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
```

### Systemd Service Setup for Gunicorn

Create a systemd service file to manage Gunicorn:

```bash
sudo nano /etc/systemd/system/git-dashboard.service
```

Add the following content:

```ini
[Unit]
Description=Git Dashboard Gunicorn Service
After=network.target

[Service]
User=root
Group=root
WorkingDirectory=/opt/git-dashboard
Environment="PATH=/opt/git-dashboard/venv/bin"
ExecStart=/opt/git-dashboard/venv/bin/gunicorn --config /opt/git-dashboard/gunicorn_config.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true

# Resource limits
CPUQuota=50%
MemoryLimit=256M
LimitNOFILE=65535

# Security
NoNewPrivileges=true

# Restart on failure
Restart=always
RestartSec=5
StartLimitInterval=60
StartLimitBurst=3

[Install]
WantedBy=multi-user.target
```

### Starting and Enabling the Gunicorn Service

```bash
# Reload systemd to recognize the new service
sudo systemctl daemon-reload

# Enable the service to start at boot
sudo systemctl enable git-dashboard

# Start the service
sudo systemctl start git-dashboard

# Check the status
sudo systemctl status git-dashboard
```

## Metrics Collection Script

Create the metrics collection script:

```bash
sudo nano /opt/git-dashboard/collect-metrics.sh
```

Add the following content:

```bash
#!/bin/bash

# Git Dashboard Metrics Collection Script

# Configuration
DASHBOARD_DIR="/opt/git-dashboard"
DATA_DIR="$DASHBOARD_DIR/data"
METRICS_FILE="$DATA_DIR/metrics_history.json"
HISTORY_DIR="$DATA_DIR/history"
MAX_HISTORY_FILES=168  # Keep 7 days of hourly files
GIT_REPOS_DIR="/home/<USER>"  # Directory containing Git repositories

# Ensure directories exist
mkdir -p "$DATA_DIR"
mkdir -p "$HISTORY_DIR"

# Get timestamp
timestamp=$(date '+%Y-%m-%d %H:%M:%S')
date_for_file=$(date '+%Y%m%d%H%M%S')

# System metrics
cpu_percent=$(top -bn1 | grep "Cpu(s)" | awk '{print $2+$4}')
mem_total=$(free -m | awk '/Mem:/ {print $2}')
mem_used=$(free -m | awk '/Mem:/ {print $3}')
mem_percent=$(free | grep Mem | awk '{print $3/$2 * 100.0}')
disk_total=$(df -h / | awk 'NR==2 {print $2}' | sed 's/G//')
disk_used=$(df -h / | awk 'NR==2 {print $3}' | sed 's/G//')
disk_percent=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
load_1min=$(cat /proc/loadavg | awk '{print $1}')
load_5min=$(cat /proc/loadavg | awk '{print $2}')
load_15min=$(cat /proc/loadavg | awk '{print $3}')
hostname=$(hostname)
uptime_seconds=$(cat /proc/uptime | awk '{print $1}')

# Git metrics
total_repos=0
total_commits=0
total_branches=0
active_repos=0
recent_commits=0

# Initialize recent commits array
recent_commits_array="[]"

# Check if Git repos directory exists
if [ -d "$GIT_REPOS_DIR" ]; then
    # Count repositories
    total_repos=$(find "$GIT_REPOS_DIR" -name "*.git" | wc -l)
    
    # Process each repository
    for repo_path in $(find "$GIT_REPOS_DIR" -name "*.git"); do
        if [ -d "$repo_path" ]; then
            # Get repository name
            repo_name=$(basename "$repo_path" .git)
            
            # Count commits in the repository
            cd "$repo_path"
            repo_commits=$(git rev-list --all --count 2>/dev/null)
            if [ "$?" -eq 0 ]; then
                total_commits=$((total_commits + repo_commits))
                
                # Count branches
                repo_branches=$(git branch -a | wc -l)
                total_branches=$((total_branches + repo_branches))
                
                # Check if repo has commits in last 7 days
                recent_repo_commits=$(git log --since="7 days ago" --pretty=format:"%H" | wc -l)
                if [ "$recent_repo_commits" -gt 0 ]; then
                    active_repos=$((active_repos + 1))
                    recent_commits=$((recent_commits + recent_repo_commits))
                    
                    # Get 5 most recent commits for this repo
                    repo_recent_commits=$(git log -5 --pretty=format:'{"hash":"%h","date":"%ad","author":"%an","message":"%s","repo":"'$repo_name'"}' --date=short | sed 's/\"/\\\"/g')
                    
                    # Add to recent commits array if we have any
                    if [ -n "$repo_recent_commits" ]; then
                        if [ "$recent_commits_array" = "[]" ]; then
                            recent_commits_array="[$repo_recent_commits]"
                        else
                            # Remove the trailing ]
                            recent_commits_array=${recent_commits_array%]}
                            # Add a comma and the new commits
                            recent_commits_array="$recent_commits_array,$repo_recent_commits]"
                        fi
                    fi
                fi
            fi
        fi
    done
fi

# Create metrics JSON
metrics="{\
  \"timestamp\": \"$timestamp\",\
  \"system\": {\
    \"cpu\": {\
      \"percent\": $cpu_percent\
    },\
    \"memory\": {\
      \"total\": $mem_total,\
      \"used\": $mem_used,\
      \"percent\": $mem_percent\
    },\
    \"disk\": {\
      \"total\": $disk_total,\
      \"used\": $disk_used,\
      \"percent\": $disk_percent\
    },\
    \"load\": {\
      \"1min\": $load_1min,\
      \"5min\": $load_5min,\
      \"15min\": $load_15min\
    },\
    \"host\": \"$hostname\",\
    \"uptime\": $uptime_seconds\
  },\
  \"git\": {\
    \"total_repos\": $total_repos,\
    \"total_commits\": $total_commits,\
    \"total_branches\": $total_branches,\
    \"active_repos\": $active_repos,\
    \"recent_commits\": $recent_commits_array\
  }\
}"

# Write metrics to current file
echo "$metrics" > "$METRICS_FILE"

# Archive metrics to history directory (hourly)
current_hour=$(date '+%H')
current_minute=$(date '+%M')
if [ "$current_minute" -eq 0 ]; then
    # It's on the hour, archive a copy
    cp "$METRICS_FILE" "$HISTORY_DIR/metrics_$date_for_file.json"
    
    # Cleanup old history files (keep only MAX_HISTORY_FILES)
    ls -1t "$HISTORY_DIR"/*.json | tail -n +$((MAX_HISTORY_FILES+1)) | xargs -r rm
fi

# Log collection time
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Metrics collected successfully" >> "$DASHBOARD_DIR/logs/metrics.log"
```

Make the script executable:

```bash
sudo chmod +x /opt/git-dashboard/collect-metrics.sh
```

## Cron Job Configuration

Set up a cron job to run the metrics collection script every minute:

```bash
sudo crontab -e
```

Add the following line:

```cron
* * * * * /opt/git-dashboard/collect-metrics.sh
```

## IP Binding Configuration

### Binding Flask/Gunicorn to Specific IP

To bind Flask/Gunicorn to a specific IP address, modify the following files:

1. **For development mode in app.py**:
```python
if __name__ == '__main__':
    app.run(host='*************', port=8000, debug=True)
```

2. **For production with Gunicorn config**:
```python
# In gunicorn_config.py
bind = "*************:8000"
```

3. **Or when starting Gunicorn from systemd**:
```ini
# In git-dashboard.service
ExecStart=/opt/git-dashboard/venv/bin/gunicorn --bind *************:8000 --config /opt/git-dashboard/gunicorn_config.py app:app
```

## Log Rotation Configuration

Set up log rotation for application logs:

```bash
sudo nano /etc/logrotate.d/git-dashboard
```

Add the following content:

```
/opt/git-dashboard/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload git-dashboard >/dev/null 2>&1 || true
    endscript
}
```

## Testing the Flask Application

```bash
# Test with Flask development server
cd /opt/git-dashboard
source venv/bin/activate
FLASK_APP=app.py flask run --host=0.0.0.0 --port=8000

# Test with Gunicorn directly
cd /opt/git-dashboard
source venv/bin/activate
gunicorn --config gunicorn_config.py app:app

# Test API endpoints
curl http://localhost:8000/api/status
curl http://localhost:8000/api/metrics/current
curl http://localhost:8000/api/metrics/history?hours=24
```

## Monitoring Flask and Gunicorn

```bash
# Check service status
sudo systemctl status git-dashboard

# View service logs
sudo journalctl -u git-dashboard

# View application logs
sudo tail -f /opt/git-dashboard/logs/gunicorn.log
sudo tail -f /opt/git-dashboard/logs/access.log

# Monitor running processes
ps aux | grep gunicorn
```

## Troubleshooting Flask and Gunicorn

1. **Application not starting**:
```bash
# Check for syntax errors in app.py
cd /opt/git-dashboard
source venv/bin/activate
python -m py_compile app.py

# Check permissions
sudo chown -R root:root /opt/git-dashboard
sudo chmod -R 755 /opt/git-dashboard
sudo chmod 644 /opt/git-dashboard/logs/*.log
```

2. **API returning errors**:
```bash
# Check application logs
tail -f /opt/git-dashboard/logs/git-dashboard.log

# Verify data directory permissions
sudo chmod -R 755 /opt/git-dashboard/data
```

3. **Metrics not updating**:
```bash
# Check cron service
sudo systemctl status cron

# Check if script is running
ps aux | grep collect-metrics

# Run script manually to check for errors
sudo /opt/git-dashboard/collect-metrics.sh

# Check metrics file existence and content
cat /opt/git-dashboard/data/metrics_history.json
```

4. **Performance issues**:
```bash
# Check resource usage
top -u root

# Monitor Gunicorn workers
ps -ef | grep gunicorn

# Check for memory leaks
sudo cat /proc/$(pgrep -f "gunicorn: master")/status | grep VmSize
```
