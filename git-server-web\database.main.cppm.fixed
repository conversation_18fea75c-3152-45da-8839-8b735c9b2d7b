export module database.main;

// Import other modules
import database.service;

// Standard library imports
import <string>;
import <iostream>;
import <fstream>;
import <filesystem>;
import <thread>;
import <chrono>;
import <csignal>;
import <atomic>;

// Main module
export namespace dbservice::main {

    // Signal handler
    std::atomic<bool> running{true};

    void signalHandler(int signal) {
        std::cout << "Received signal " << signal << std::endl;
        running = false;
    }

    // Load configuration from file
    DatabaseServiceConfig loadConfig(const std::string& configFile) {
        DatabaseServiceConfig config;

        // Default configuration
        config.connectionString = "host=localhost port=5432 dbname=database_service user=postgres password=postgres";
        config.connectionPoolSize = 10;
        config.useSSL = true;

        config.apiPort = 8080;
        config.apiHost = "127.0.0.1";
        config.apiBasePath = "/api";
        
        config.enableAuthentication = true;
        config.jwtSecret = "default-secret-change-me";

        config.enableLogging = true;
        config.logLevel = "info";
        config.logFile = "database-service.log";

        // Load configuration from file if it exists
        if (std::filesystem::exists(configFile)) {
            std::ifstream file(configFile);
            if (file.is_open()) {
                std::string line;
                while (std::getline(file, line)) {
                    // Skip comments and empty lines
                    if (line.empty() || line[0] == '#') {
                        continue;
                    }

                    // Parse key-value pairs
                    auto pos = line.find('=');
                    if (pos != std::string::npos) {
                        std::string key = line.substr(0, pos);
                        std::string value = line.substr(pos + 1);

                        // Trim whitespace
                        key.erase(0, key.find_first_not_of(" \t"));
                        key.erase(key.find_last_not_of(" \t") + 1);
                        value.erase(0, value.find_first_not_of(" \t"));
                        value.erase(value.find_last_not_of(" \t") + 1);

                        // Set configuration values
                        if (key == "connection_string") {
                            config.connectionString = value;
                        } else if (key == "connection_pool_size") {
                            config.connectionPoolSize = std::stoi(value);
                        } else if (key == "use_ssl") {
                            config.useSSL = (value == "true" || value == "1");
                        } else if (key == "api_port") {
                            config.apiPort = std::stoi(value);
                        } else if (key == "api_host") {
                            config.apiHost = value;
                        } else if (key == "api_base_path") {
                            config.apiBasePath = value;
                        } else if (key == "enable_authentication") {
                            config.enableAuthentication = (value == "true" || value == "1");
                        } else if (key == "jwt_secret") {
                            config.jwtSecret = value;
                        } else if (key == "enable_logging") {
                            config.enableLogging = (value == "true" || value == "1");
                        } else if (key == "log_level") {
                            config.logLevel = value;
                        } else if (key == "log_file") {
                            config.logFile = value;
                        }
                    }
                }

                file.close();
            }
        }

        return config;
    }

    // Main function
    export int main(int argc, char* argv[]) {
        // Set up signal handlers
        std::signal(SIGINT, signalHandler);
        std::signal(SIGTERM, signalHandler);
        
        // Parse command line arguments
        std::string configFile = "config.ini";

        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "--config" && i + 1 < argc) {
                configFile = argv[++i];
            } else if (arg == "--help") {
                std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
                std::cout << "Options:" << std::endl;
                std::cout << "  --config <file>  Configuration file (default: config.ini)" << std::endl;
                std::cout << "  --help           Show this help message" << std::endl;
                return 0;
            }
        }

        // Load configuration
        auto config = loadConfig(configFile);

        // Create database service
        auto dbService = std::make_shared<DatabaseService>(config);

        // Start the service
        auto result = dbService->start();
        if (result.failure()) {
            std::cerr << "Failed to start database service: " << *result.error << std::endl;
            return 1;
        }

        std::cout << "Database service started on " << config.apiHost << ":" << config.apiPort << std::endl;

        // Wait for signal to stop
        while (running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // Stop the service
        std::cout << "Stopping database service..." << std::endl;
        result = dbService->stop();
        if (result.failure()) {
            std::cerr << "Failed to stop database service: " << *result.error << std::endl;
            return 1;
        }

        std::cout << "Database service stopped" << std::endl;

        return 0;
    }

} // namespace dbservice::main
