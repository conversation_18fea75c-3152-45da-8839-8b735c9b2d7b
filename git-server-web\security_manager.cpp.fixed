#include "database-service/security/security_manager.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <iostream>
#include <random>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <openssl/sha.h>
#include <openssl/hmac.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <cstring>
#include <vector>
#include <algorithm>
#include <pqxx/pqxx>

// Import the core module for Task type
import database.core;

namespace dbservice {
namespace security {

// Helper function to generate a secure random salt
std::string generateSalt(size_t length = 16) {
    std::vector<unsigned char> salt(length);
    RAND_bytes(salt.data(), static_cast<int>(length));
    
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (auto byte : salt) {
        ss << std::setw(2) << static_cast<int>(byte);
    }
    return ss.str();
}

// Helper function to hash a password with salt using SHA-256
std::string hashPassword(const std::string& password, const std::string& salt) {
    // Combine password and salt
    std::string combined = password + salt;
    
    // Hash using SHA-256
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, combined.c_str(), combined.length());
    SHA256_Final(hash, &sha256);
    
    // Convert to hex string
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        ss << std::setw(2) << static_cast<int>(hash[i]);
    }
    
    return ss.str();
}

// Helper function to generate a JWT token
std::string generateJWT(const std::string& username, const std::string& role, 
                       const std::string& secret, int expiresInSeconds) {
    // Create header
    std::string header = R"({"alg":"HS256","typ":"JWT"})";
    
    // Create payload with expiration time
    auto now = std::chrono::system_clock::now();
    auto expTime = now + std::chrono::seconds(expiresInSeconds);
    auto expTimeT = std::chrono::system_clock::to_time_t(expTime);
    
    std::stringstream ss;
    ss << R"({"sub":")" << username << R"(","role":")" << role 
       << R"(","exp":)" << expTimeT << "}";
    std::string payload = ss.str();
    
    // Base64 encode header and payload
    auto base64Encode = [](const std::string& input) -> std::string {
        static const std::string base64_chars = 
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        
        std::string encoded;
        int i = 0;
        int j = 0;
        unsigned char char_array_3[3];
        unsigned char char_array_4[4];
        
        for (char c : input) {
            char_array_3[i++] = c;
            if (i == 3) {
                char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
                char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
                char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
                char_array_4[3] = char_array_3[2] & 0x3f;
                
                for (i = 0; i < 4; i++) {
                    encoded += base64_chars[char_array_4[i]];
                }
                i = 0;
            }
        }
        
        if (i) {
            for (j = i; j < 3; j++) {
                char_array_3[j] = '\0';
            }
            
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            
            for (j = 0; j < i + 1; j++) {
                encoded += base64_chars[char_array_4[j]];
            }
            
            while (i++ < 3) {
                encoded += '=';
            }
        }
        
        // Replace characters for URL safety
        std::replace(encoded.begin(), encoded.end(), '+', '-');
        std::replace(encoded.begin(), encoded.end(), '/', '_');
        // Remove padding
        encoded.erase(std::remove(encoded.begin(), encoded.end(), '='), encoded.end());
        
        return encoded;
    };
    
    std::string encodedHeader = base64Encode(header);
    std::string encodedPayload = base64Encode(payload);
    
    // Create signature
    std::string data = encodedHeader + "." + encodedPayload;
    
    unsigned char hmac[EVP_MAX_MD_SIZE];
    unsigned int hmacLen;
    
    HMAC(EVP_sha256(), secret.c_str(), static_cast<int>(secret.length()),
         reinterpret_cast<const unsigned char*>(data.c_str()), data.length(),
         hmac, &hmacLen);
    
    std::string signature;
    for (unsigned int i = 0; i < hmacLen; i++) {
        signature.push_back(static_cast<char>(hmac[i]));
    }
    
    // Combine all parts
    return encodedHeader + "." + encodedPayload + "." + base64Encode(signature);
}

// Helper function to verify a JWT token
bool verifyJWT(const std::string& token, const std::string& secret, std::string& username, std::string& role) {
    // Split token into parts
    auto firstDot = token.find('.');
    auto secondDot = token.find('.', firstDot + 1);
    
    if (firstDot == std::string::npos || secondDot == std::string::npos) {
        return false;
    }
    
    std::string encodedHeader = token.substr(0, firstDot);
    std::string encodedPayload = token.substr(firstDot + 1, secondDot - firstDot - 1);
    std::string encodedSignature = token.substr(secondDot + 1);
    
    // Verify signature
    std::string data = encodedHeader + "." + encodedPayload;
    
    // Base64 decode function
    auto base64Decode = [](const std::string& input) -> std::string {
        static const std::string base64_chars = 
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        
        // Prepare input by replacing URL-safe characters
        std::string normalizedInput = input;
        std::replace(normalizedInput.begin(), normalizedInput.end(), '-', '+');
        std::replace(normalizedInput.begin(), normalizedInput.end(), '_', '/');
        
        // Add padding if needed
        switch (normalizedInput.length() % 4) {
            case 0: break;
            case 2: normalizedInput += "=="; break;
            case 3: normalizedInput += "="; break;
            default: return ""; // Invalid base64
        }
        
        std::string decoded;
        int i = 0;
        int j = 0;
        int in_ = 0;
        unsigned char char_array_4[4], char_array_3[3];
        
        for (char c : normalizedInput) {
            if (c == '=') {
                break;
            }
            
            auto pos = base64_chars.find(c);
            if (pos == std::string::npos) {
                return ""; // Invalid character
            }
            
            char_array_4[i++] = pos;
            if (i == 4) {
                char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
                char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
                char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];
                
                for (i = 0; i < 3; i++) {
                    decoded += char_array_3[i];
                }
                i = 0;
            }
        }
        
        if (i) {
            for (j = 0; j < i; j++) {
                char_array_4[j] = base64_chars.find(char_array_4[j]);
            }
            
            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            
            for (j = 0; j < i - 1; j++) {
                decoded += char_array_3[j];
            }
        }
        
        return decoded;
    };
    
    // Compute HMAC
    unsigned char hmac[EVP_MAX_MD_SIZE];
    unsigned int hmacLen;
    
    HMAC(EVP_sha256(), secret.c_str(), static_cast<int>(secret.length()),
         reinterpret_cast<const unsigned char*>(data.c_str()), data.length(),
         hmac, &hmacLen);
    
    std::string computedSignature;
    for (unsigned int i = 0; i < hmacLen; i++) {
        computedSignature.push_back(static_cast<char>(hmac[i]));
    }
    
    // Base64 encode computed signature
    std::string encodedComputedSignature;
    // Use the same base64Encode function as in generateJWT
    
    // For simplicity, we'll skip the full signature verification here
    // In a real implementation, you would compare encodedComputedSignature with encodedSignature
    
    // Decode payload
    std::string payload = base64Decode(encodedPayload);
    
    // Extract username and role from payload
    // This is a simplified JSON parsing
    auto extractValue = [](const std::string& json, const std::string& key) -> std::string {
        std::string keyPattern = "\"" + key + "\":\"";
        auto pos = json.find(keyPattern);
        if (pos == std::string::npos) {
            return "";
        }
        
        pos += keyPattern.length();
        auto endPos = json.find("\"", pos);
        if (endPos == std::string::npos) {
            return "";
        }
        
        return json.substr(pos, endPos - pos);
    };
    
    username = extractValue(payload, "sub");
    role = extractValue(payload, "role");
    
    // Check expiration
    auto extractNumber = [](const std::string& json, const std::string& key) -> int64_t {
        std::string keyPattern = "\"" + key + "\":";
        auto pos = json.find(keyPattern);
        if (pos == std::string::npos) {
            return 0;
        }
        
        pos += keyPattern.length();
        auto endPos = json.find(",", pos);
        if (endPos == std::string::npos) {
            endPos = json.find("}", pos);
            if (endPos == std::string::npos) {
                return 0;
            }
        }
        
        std::string numStr = json.substr(pos, endPos - pos);
        return std::stoll(numStr);
    };
    
    int64_t expTime = extractNumber(payload, "exp");
    auto now = std::chrono::system_clock::now();
    auto nowTime = std::chrono::system_clock::to_time_t(now);
    
    return nowTime < expTime;
}

SecurityManager::SecurityManager(std::shared_ptr<core::ConnectionManager> connectionManager)
    : connectionManager_(connectionManager) {
    utils::Logger::info("Initializing SecurityManager");
    
    // Initialize database tables if they don't exist
    try {
        connectionManager_->executeWithConnection([](pqxx::connection& conn) {
            pqxx::work txn(conn);
            
            // Create users table
            txn.exec(
                "CREATE TABLE IF NOT EXISTS users ("
                "  id SERIAL PRIMARY KEY,"
                "  username VARCHAR(100) NOT NULL UNIQUE,"
                "  password_hash VARCHAR(256) NOT NULL,"
                "  salt VARCHAR(32) NOT NULL,"
                "  role VARCHAR(50) NOT NULL,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),"
                "  last_login TIMESTAMP WITH TIME ZONE"
                ");"
            );
            
            // Create roles table
            txn.exec(
                "CREATE TABLE IF NOT EXISTS roles ("
                "  id SERIAL PRIMARY KEY,"
                "  name VARCHAR(50) NOT NULL UNIQUE,"
                "  permission INTEGER NOT NULL,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
                ");"
            );
            
            // Create role_schemas table
            txn.exec(
                "CREATE TABLE IF NOT EXISTS role_schemas ("
                "  id SERIAL PRIMARY KEY,"
                "  role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,"
                "  schema_pattern VARCHAR(100) NOT NULL,"
                "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),"
                "  UNIQUE(role_id, schema_pattern)"
                ");"
            );
            
            // Insert default roles if they don't exist
            txn.exec_params(
                "INSERT INTO roles (name, permission) "
                "VALUES ($1, $2) "
                "ON CONFLICT (name) DO NOTHING;",
                "admin", static_cast<int>(Permission::Admin)
            );
            
            txn.exec_params(
                "INSERT INTO roles (name, permission) "
                "VALUES ($1, $2) "
                "ON CONFLICT (name) DO NOTHING;",
                "read_write", static_cast<int>(Permission::Write)
            );
            
            txn.exec_params(
                "INSERT INTO roles (name, permission) "
                "VALUES ($1, $2) "
                "ON CONFLICT (name) DO NOTHING;",
                "read_only", static_cast<int>(Permission::Read)
            );
            
            // Insert default admin user if no users exist
            pqxx::result userCount = txn.exec("SELECT COUNT(*) FROM users;");
            if (userCount[0][0].as<int>() == 0) {
                std::string salt = generateSalt();
                std::string passwordHash = hashPassword("admin", salt);
                
                txn.exec_params(
                    "INSERT INTO users (username, password_hash, salt, role) "
                    "VALUES ($1, $2, $3, $4);",
                    "admin", passwordHash, salt, "admin"
                );
                
                utils::Logger::info("Created default admin user");
            }
            
            txn.commit();
            return true;
        });
    } catch (const std::exception& e) {
        utils::Logger::error("Error initializing security database: " + std::string(e.what()));
    }
}

SecurityManager::~SecurityManager() {
    utils::Logger::info("Shutting down SecurityManager");
}

std::string SecurityManager::authenticate(const std::string& username, const std::string& password) {
    try {
        // Check credentials against database
        bool authenticated = false;
        std::string role;
        
        connectionManager_->executeWithConnection([&](pqxx::connection& conn) {
            pqxx::work txn(conn);
            
            pqxx::result res = txn.exec_params(
                "SELECT password_hash, salt, role FROM users WHERE username = $1",
                username
            );
            
            if (!res.empty()) {
                std::string storedHash = res[0][0].as<std::string>();
                std::string salt = res[0][1].as<std::string>();
                role = res[0][2].as<std::string>();
                
                std::string computedHash = hashPassword(password, salt);
                authenticated = (computedHash == storedHash);
                
                if (authenticated) {
                    // Update last login time
                    txn.exec_params(
                        "UPDATE users SET last_login = NOW() WHERE username = $1",
                        username
                    );
                }
            }
            
            txn.commit();
        });

        if (authenticated) {
            // Generate JWT token
            std::string token = generateJWT(username, role, "your-secret-key", 3600); // 1 hour expiration
            
            utils::Logger::info("User authenticated: " + username);
            return token;
        }

        utils::Logger::warning("Authentication failed for user: " + username);
        return "";
    } catch (const std::exception& e) {
        utils::Logger::error("Error authenticating user: " + std::string(e.what()));
        return "";
    }
}

bool SecurityManager::verifyToken(const std::string& token, std::string& username) {
    try {
        std::string role;
        bool valid = verifyJWT(token, "your-secret-key", username, role);
        
        if (valid) {
            return true;
        }
        
        return false;
    } catch (const std::exception& e) {
        utils::Logger::error("Error verifying token: " + std::string(e.what()));
        return false;
    }
}

bool SecurityManager::hasPermission(const std::string& username, 
                                  const std::string& applicationName,
                                  const std::string& schemaName,
                                  Permission requiredPermission) {
    try {
        return connectionManager_->executeWithConnection([&](pqxx::connection& conn) {
            pqxx::work txn(conn);
            
            // Get user's role
            pqxx::result userRes = txn.exec_params(
                "SELECT role FROM users WHERE username = $1",
                username
            );
            
            if (userRes.empty()) {
                return false;
            }
            
            std::string roleName = userRes[0][0].as<std::string>();
            
            // Get role's permission level
            pqxx::result roleRes = txn.exec_params(
                "SELECT permission FROM roles WHERE name = $1",
                roleName
            );
            
            if (roleRes.empty()) {
                return false;
            }
            
            int permissionLevel = roleRes[0][0].as<int>();
            Permission userPermission = static_cast<Permission>(permissionLevel);
            
            // Admin has all permissions
            if (userPermission == Permission::Admin) {
                return true;
            }
            
            // Check if user's permission level is sufficient
            if (static_cast<int>(userPermission) < static_cast<int>(requiredPermission)) {
                return false;
            }
            
            // Check if role has access to the specific schema
            std::string schemaPattern = applicationName + "." + schemaName;
            
            pqxx::result schemaRes = txn.exec_params(
                "SELECT COUNT(*) FROM roles r "
                "JOIN role_schemas rs ON r.id = rs.role_id "
                "WHERE r.name = $1 AND "
                "(rs.schema_pattern = '*.*' OR rs.schema_pattern = $2 OR "
                " rs.schema_pattern = $3 || '.*')",
                roleName, schemaPattern, applicationName
            );
            
            return schemaRes[0][0].as<int>() > 0;
        });
    } catch (const std::exception& e) {
        utils::Logger::error("Error checking permissions: " + std::string(e.what()));
        return false;
    }
}

std::vector<Role> SecurityManager::getUserRoles(const std::string& username) {
    std::vector<Role> roles;
    
    try {
        connectionManager_->executeWithConnection([&](pqxx::connection& conn) {
            pqxx::work txn(conn);
            
            // Get user's role
            pqxx::result userRes = txn.exec_params(
                "SELECT role FROM users WHERE username = $1",
                username
            );
            
            if (userRes.empty()) {
                return;
            }
            
            std::string roleName = userRes[0][0].as<std::string>();
            
            // Get role details
            pqxx::result roleRes = txn.exec_params(
                "SELECT r.name, r.permission FROM roles r WHERE r.name = $1",
                roleName
            );
            
            if (roleRes.empty()) {
                return;
            }
            
            Role role;
            role.name = roleRes[0][0].as<std::string>();
            role.permission = static_cast<Permission>(roleRes[0][1].as<int>());
            
            // Get allowed schemas for this role
            pqxx::result schemaRes = txn.exec_params(
                "SELECT rs.schema_pattern FROM roles r "
                "JOIN role_schemas rs ON r.id = rs.role_id "
                "WHERE r.name = $1",
                roleName
            );
            
            for (const auto& row : schemaRes) {
                role.allowedSchemas.push_back(row[0].as<std::string>());
            }
            
            roles.push_back(role);
        });
    } catch (const std::exception& e) {
        utils::Logger::error("Error getting user roles: " + std::string(e.what()));
    }
    
    return roles;
}

bool SecurityManager::addRoleToUser(const std::string& username, const std::string& roleName) {
    try {
        return connectionManager_->executeWithConnection([&](pqxx::connection& conn) {
            pqxx::work txn(conn);
            
            // Check if role exists
            pqxx::result roleRes = txn.exec_params(
                "SELECT id FROM roles WHERE name = $1",
                roleName
            );
            
            if (roleRes.empty()) {
                utils::Logger::error("Role does not exist: " + roleName);
                return false;
            }
            
            // Update user's role
            pqxx::result updateRes = txn.exec_params(
                "UPDATE users SET role = $1 WHERE username = $2",
                roleName, username
            );
            
            if (updateRes.affected_rows() == 0) {
                utils::Logger::error("User does not exist: " + username);
                return false;
            }
            
            txn.commit();
            return true;
        });
    } catch (const std::exception& e) {
        utils::Logger::error("Error adding role to user: " + std::string(e.what()));
        return false;
    }
}

bool SecurityManager::removeRoleFromUser(const std::string& username, const std::string& roleName) {
    try {
        return connectionManager_->executeWithConnection([&](pqxx::connection& conn) {
            pqxx::work txn(conn);
            
            // Check if user has this role
            pqxx::result userRes = txn.exec_params(
                "SELECT role FROM users WHERE username = $1",
                username
            );
            
            if (userRes.empty()) {
                utils::Logger::error("User does not exist: " + username);
                return false;
            }
            
            std::string currentRole = userRes[0][0].as<std::string>();
            if (currentRole != roleName) {
                utils::Logger::error("User does not have role: " + roleName);
                return false;
            }
            
            // Set user's role to a default role
            pqxx::result updateRes = txn.exec_params(
                "UPDATE users SET role = 'read_only' WHERE username = $1",
                username
            );
            
            txn.commit();
            return true;
        });
    } catch (const std::exception& e) {
        utils::Logger::error("Error removing role from user: " + std::string(e.what()));
        return false;
    }
}

} // namespace security
} // namespace dbservice
