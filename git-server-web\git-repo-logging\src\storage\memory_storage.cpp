#include "memory_storage.hpp"
#include <algorithm>
#include <fstream>
#include <jsoncpp/json/json.h>
#include <iostream>

namespace logging {

MemoryStorage::MemoryStorage(size_t maxEntries, std::chrono::minutes maxAge)
    : maxEntries_(maxEntries), maxAge_(maxAge) {
    std::cout << "Initializing memory storage with max entries: " << maxEntries 
              << ", max age: " << maxAge.count() << " minutes" << std::endl;
}

bool MemoryStorage::store(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Add entry to the front (newest first)
    entries_.push_front(entry);
    
    // Update statistics
    sourceCount_[entry.source]++;
    levelCount_[entry.type]++;
    
    // Prune old entries if needed
    pruneOldEntries();
    
    return true;
}

bool MemoryStorage::storeBatch(const std::vector<LogEntry>& entries) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Add all entries
    for (const auto& entry : entries) {
        entries_.push_front(entry);
        sourceCount_[entry.source]++;
        levelCount_[entry.type]++;
    }
    
    // Prune old entries if needed
    pruneOldEntries();
    
    return true;
}

std::vector<LogEntry> MemoryStorage::query(const LogQueryParams& params) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<LogEntry> results;
    
    // Filter entries based on query parameters
    for (const auto& entry : entries_) {
        if (matchesQuery(entry, params)) {
            results.push_back(entry);
            
            // Check limit
            if (params.limit > 0 && results.size() >= static_cast<size_t>(params.limit)) {
                break;
            }
        }
    }
    
    return results;
}

StorageStats MemoryStorage::getStats() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    StorageStats stats;
    stats.totalEntries = entries_.size();
    
    // Copy source counts
    stats.bySource = sourceCount_;
    
    // Copy level counts
    stats.byLevel = levelCount_;
    
    // Set oldest and newest entry timestamps
    if (!entries_.empty()) {
        stats.newestEntry = entries_.front().timestamp;
        stats.oldestEntry = entries_.back().timestamp;
    }
    
    // Estimate storage size (rough approximation)
    stats.storageSize = entries_.size() * sizeof(LogEntry);
    
    return stats;
}

bool MemoryStorage::prune(std::chrono::system_clock::time_point olderThan) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t initialSize = entries_.size();
    
    // Remove entries older than the specified time
    entries_.erase(
        std::remove_if(entries_.begin(), entries_.end(),
            [olderThan](const LogEntry& entry) {
                return entry.timestamp < olderThan;
            }),
        entries_.end());
    
    // Recalculate statistics
    sourceCount_.clear();
    levelCount_.clear();
    
    for (const auto& entry : entries_) {
        sourceCount_[entry.source]++;
        levelCount_[entry.type]++;
    }
    
    return entries_.size() < initialSize;
}

bool MemoryStorage::compact() {
    // Memory storage doesn't need compaction
    return true;
}

bool MemoryStorage::backup(const std::string& backupPath) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        // Create JSON array of entries
        Json::Value root(Json::arrayValue);
        
        for (const auto& entry : entries_) {
            Json::Value entryJson;
            
            // Convert entry to JSON
            entryJson["timestamp"] = std::chrono::system_clock::to_time_t(entry.timestamp);
            entryJson["type"] = static_cast<int>(entry.type);
            entryJson["message"] = entry.message;
            entryJson["source"] = entry.source;
            
            // Add optional fields
            if (entry.component) entryJson["component"] = *entry.component;
            if (entry.unit) entryJson["unit"] = *entry.unit;
            if (entry.hostname) entryJson["hostname"] = *entry.hostname;
            if (entry.commit) entryJson["commit"] = *entry.commit;
            if (entry.author) entryJson["author"] = *entry.author;
            
            // Add metadata
            Json::Value metadataJson(Json::objectValue);
            for (const auto& [key, value] : entry.metadata) {
                metadataJson[key] = value;
            }
            entryJson["metadata"] = metadataJson;
            
            root.append(entryJson);
        }
        
        // Write to file
        std::ofstream file(backupPath);
        if (!file) {
            return false;
        }
        
        Json::StreamWriterBuilder writer;
        file << Json::writeString(writer, root);
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error backing up memory storage: " << e.what() << std::endl;
        return false;
    }
}

bool MemoryStorage::canHandle(const LogEntry& entry) const {
    // Memory storage can handle any log entry
    return true;
}

void MemoryStorage::pruneOldEntries() {
    auto now = std::chrono::system_clock::now();
    auto cutoff = now - maxAge_;
    
    // Remove entries older than max age
    while (!entries_.empty() && entries_.back().timestamp < cutoff) {
        const auto& entry = entries_.back();
        sourceCount_[entry.source]--;
        levelCount_[entry.type]--;
        entries_.pop_back();
    }
    
    // Remove excess entries
    while (entries_.size() > maxEntries_) {
        const auto& entry = entries_.back();
        sourceCount_[entry.source]--;
        levelCount_[entry.type]--;
        entries_.pop_back();
    }
}

bool MemoryStorage::matchesQuery(const LogEntry& entry, const LogQueryParams& params) const {
    // Check source filter
    if (params.source != "all" && entry.source != params.source) {
        return false;
    }
    
    // Check level filter
    if (params.level != "all") {
        std::string entryLevel;
        switch (entry.type) {
            case LogType::INFO: entryLevel = "info"; break;
            case LogType::WARNING: entryLevel = "warning"; break;
            case LogType::ERROR: entryLevel = "error"; break;
            case LogType::SUCCESS: entryLevel = "success"; break;
            case LogType::DEBUG: entryLevel = "debug"; break;
        }
        
        if (entryLevel != params.level) {
            return false;
        }
    }
    
    // Check time range
    if (params.startTime && entry.timestamp < *params.startTime) {
        return false;
    }
    
    if (params.endTime && entry.timestamp > *params.endTime) {
        return false;
    }
    
    // Check search text
    if (!params.searchText.empty()) {
        if (entry.message.find(params.searchText) == std::string::npos) {
            return false;
        }
    }
    
    // Check include sources
    if (!params.includeSources.empty()) {
        bool found = false;
        for (const auto& source : params.includeSources) {
            if (entry.source == source) {
                found = true;
                break;
            }
        }
        
        if (!found) {
            return false;
        }
    }
    
    // Check exclude sources
    for (const auto& source : params.excludeSources) {
        if (entry.source == source) {
            return false;
        }
    }
    
    return true;
}

} // namespace logging
