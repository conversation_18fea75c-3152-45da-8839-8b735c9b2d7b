# Logging Service Documentation

This directory contains comprehensive documentation for the Logging Service, a C++23 application for centralized logging and log management.

## Overview

The Logging Service is a high-performance C++23 application that provides:

- Centralized logging for all components
- Log storage and retrieval
- Log filtering and searching
- Log statistics and trends
- Log rotation and archiving

## Key Features

- **High Performance**: Optimized C++23 implementation for fast log processing
- **Multi-Tier Storage**: In-memory, file-based, and database storage
- **Database Integration**: Uses the Database Service for data storage
- **RESTful API**: HTTP API for log operations
- **Security**: SSL/TLS encryption and authentication
- **Log Rotation**: Automatic log rotation and archiving
- **Statistics**: Log statistics and trend analysis

## Architecture

The Logging Service follows a multi-tier architecture:

- **Tier 1**: In-memory storage for recent logs
- **Tier 2**: File-based storage for medium-term logs
- **Tier 3**: Database storage for long-term logs and statistics

## API Reference

The Logging Service exposes a RESTful API for log operations:

- **GET /api/logs**: Get logs with filtering options
- **POST /api/logs**: Add a new log entry
- **GET /api/logs/stats**: Get log statistics
- **GET /api/logs/sources**: Get available log sources
- **GET /api/logs/levels**: Get available log levels
- **POST /api/logs/archive**: Archive logs
- **GET /api/logs/archive**: Get archived logs
- **GET /api/logs/settings**: Get logging settings
- **PUT /api/logs/settings**: Update logging settings

## Database Schema

The Logging Service uses the `logging_db` database with the following schemas:

- **public**: Current logs
  - `logs`: Log entries
  - `log_levels`: Log level definitions
  - `log_sources`: Log source definitions

- **archive**: Archived logs
  - `logs`: Archived log entries
  - `archive_info`: Archive metadata

- **stats**: Log statistics
  - `log_statistics`: Log statistics by date, level, and source
  - `error_trends`: Error trend analysis
  - `performance_metrics`: Performance metrics

## Integration with Other Components

The Logging Service integrates with:

- **Database Service**: For data storage and retrieval
- **Git Repository Service**: For logging repository operations
- **Git Dashboard**: For web interface integration

## Deployment

The Logging Service is deployed to `/opt/git-dashboard/git-repo-logging/` with:

- **Binary**: `/opt/git-dashboard/git-repo-logging/bin/git-repo-logging`
- **Configuration**: `/opt/git-dashboard/git-repo-logging/config/config.json`
- **Systemd Service**: `/etc/systemd/system/git-repo-logging.service`
- **Log Files**: `/opt/git-dashboard/git-repo-logging/logs/git-repo-logging.log`

## See Also

- [Database Service Documentation](../../database-service/overview.md)
- [Git Repository Service Documentation](../git-repo-service/README.md)
- [C++23 Applications Integration Guide](../../CPP23-APPLICATIONS-INTEGRATION.md)
- [Project Tracker Documentation Index](../../DOCUMENTATION-INDEX.md)

