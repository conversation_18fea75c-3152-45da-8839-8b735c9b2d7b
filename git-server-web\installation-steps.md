# Git Dashboard Installation & Configuration Steps

Here are the complete steps to properly set up and fix the Git Dashboard:

## 1. Update File Locations

Ensure all files are in their correct locations:

```bash
# Flask Application
sudo chown -R www-data:www-data /opt/git-dashboard

# Systemd Service
sudo cp git-dashboard.service /etc/systemd/system/
sudo chown root:root /etc/systemd/system/git-dashboard.service
sudo chmod 644 /etc/systemd/system/git-dashboard.service

# Nginx Configuration
sudo cp git-dashboard.conf /etc/nginx/sites-available/
sudo chown root:root /etc/nginx/sites-available/git-dashboard.conf
sudo chmod 644 /etc/nginx/sites-available/git-dashboard.conf
sudo ln -sf /etc/nginx/sites-available/git-dashboard.conf /etc/nginx/sites-enabled/

# Certificate Script
sudo cp sync-certificates.sh /opt/git-dashboard/
sudo chmod 755 /opt/git-dashboard/sync-certificates.sh
sudo chown root:root /opt/git-dashboard/sync-certificates.sh

# Sudoers Configuration
sudo cp sudoers.d/git-dashboard /etc/sudoers.d/
sudo chown root:root /etc/sudoers.d/git-dashboard
sudo chmod 440 /etc/sudoers.d/git-dashboard
```

## 2. Update JavaScript Files

```bash
sudo cp dashboard.js /opt/git-dashboard/js/
sudo cp metrics-history.js /opt/git-dashboard/js/
sudo cp certificates.js /opt/git-dashboard/js/
sudo chown www-data:www-data /opt/git-dashboard/js/*.js
```

## 3. Update HTML and Flask App

```bash
sudo cp index.html /opt/git-dashboard/
sudo cp app.py /opt/git-dashboard/
sudo chown www-data:www-data /opt/git-dashboard/index.html /opt/git-dashboard/app.py
```

## 4. Create Required Directories

```bash
sudo mkdir -p /opt/git-dashboard/data/history
sudo mkdir -p /opt/git-dashboard/logs
sudo chown -R www-data:www-data /opt/git-dashboard/data /opt/git-dashboard/logs
```

## 5. Restart Services

```bash
sudo systemctl daemon-reload
sudo systemctl restart git-dashboard.service
sudo systemctl reload nginx
```

## 6. Verify Everything is Working

```bash
# Check systemd service status
sudo systemctl status git-dashboard.service

# Check Nginx configuration
sudo nginx -t

# Check logs
sudo journalctl -u git-dashboard -n 50
sudo tail -f /var/log/nginx/git-dashboard.error.log
sudo tail -f /opt/git-dashboard/logs/git-dashboard.log
```

## 7. Test Dashboard

Access the dashboard in your browser at `https://git.chcit.org` and verify:

1. Server metrics are displayed
2. History charts are loading
3. Git repository information is showing
4. Certificate information is displayed and the sync button works

## 8. Setup Metrics Collection Cron Job

```bash
sudo crontab -e
```

Add the following line to run the metrics collection script every minute:

```
* * * * * /opt/git-dashboard/collect-metrics.sh > /dev/null 2>&1
```

## Troubleshooting

If you encounter issues:

1. **Flask App Not Starting**: Check Python virtual environment and dependencies
   ```bash
   cd /opt/git-dashboard
   source venv/bin/activate
   pip install -r requirements.txt  # Assuming there's a requirements file
   ```

2. **API Endpoints Not Working**: Check browser console for specific errors

3. **Certificate Sync Issues**: Verify SSH key permissions and connectivity
   ```bash
   sudo -u btaylor-admin ssh -i /home/<USER>/.ssh/id_ed25519 btaylor-admin@***********
   ```

4. **Permission Problems**: Verify ownership and permissions
   ```bash
   ls -la /opt/git-dashboard
   ls -la /etc/letsencrypt/live/chcit.org/
   ```
