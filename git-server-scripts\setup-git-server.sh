#!/bin/bash
# Git Server Setup Script for Ubuntu 24.04.2
# This script sets up a complete Git server with SSH access and web interface

# Exit on any error
set -e

# Configuration variables - modify these as needed
GIT_USER="git"
GIT_GROUP="git"
GIT_HOME="/home/<USER>"
REPO_PATH="$GIT_HOME/repositories"
WEB_PATH="/var/www/html/gitweb"
SERVER_NAME="git.example.com"

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}=== Starting Git Server Setup ===${NC}"

# Update system
echo -e "${YELLOW}Updating system packages...${NC}"
sudo apt update && sudo apt upgrade -y

# Install required packages
echo -e "${YELLOW}Installing required packages...${NC}"
sudo apt install -y git openssh-server apache2 gitweb highlight git-daemon-sysvinit

# Create git user if it doesn't exist
echo -e "${YELLOW}Setting up git user...${NC}"
if ! id "$GIT_USER" &>/dev/null; then
    sudo adduser --system --shell /bin/bash --gecos 'Git Version Control' --group --disabled-password --home "$GIT_HOME" "$GIT_USER"
fi

# Create repositories directory
echo -e "${YELLOW}Creating repositories directory...${NC}"
sudo mkdir -p "$REPO_PATH"
sudo chown -R "$GIT_USER:$GIT_GROUP" "$GIT_HOME"
sudo chmod -R 775 "$REPO_PATH"

# Setup SSH for git user
echo -e "${YELLOW}Setting up SSH for git user...${NC}"
sudo mkdir -p "$GIT_HOME/.ssh"
sudo touch "$GIT_HOME/.ssh/authorized_keys"
sudo chown -R "$GIT_USER:$GIT_GROUP" "$GIT_HOME/.ssh"
sudo chmod 700 "$GIT_HOME/.ssh"
sudo chmod 600 "$GIT_HOME/.ssh/authorized_keys"

# Configure git-daemon for anonymous git access
echo -e "${YELLOW}Configuring git-daemon...${NC}"
cat << EOF | sudo tee /etc/systemd/system/git-daemon.service
[Unit]
Description=Git Daemon
After=network.target

[Service]
User=$GIT_USER
ExecStart=/usr/lib/git-core/git-daemon --reuseaddr --base-path=$REPO_PATH $REPO_PATH
Restart=always
RestartSec=500ms
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=git-daemon

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable git-daemon
sudo systemctl start git-daemon

# Configure gitweb
echo -e "${YELLOW}Configuring GitWeb...${NC}"
cat << EOF | sudo tee /etc/gitweb.conf
# path to git projects
\$projectroot = '$REPO_PATH';

# directory to use for temp files
\$git_temp = "/tmp";

# target of the home link on top of all pages
\$home_link = "$SERVER_NAME";

# html text to include at home page
\$home_text = "Git Repositories";

# file with project list; by default, simply scan the projectroot dir.
\$projects_list = \$projectroot;

# stylesheet to use
\$stylesheet = "/gitweb/static/gitweb.css";

# logo to use
\$logo = "/gitweb/static/git-logo.png";

# the favicon
\$favicon = "/gitweb/static/git-favicon.png";

# enable syntax highlighting
\$feature{'highlight'}{'default'} = [1];

# Enable blame, pickaxe, snapshot, search and grep
\$feature{'blame'}{'default'} = [1];
\$feature{'pickaxe'}{'default'} = [1];
\$feature{'snapshot'}{'default'} = [1];
\$feature{'search'}{'default'} = [1];
\$feature{'grep'}{'default'} = [1];
\$feature{'show-sizes'}{'default'} = [1];
EOF

# Configure Apache for gitweb
echo -e "${YELLOW}Configuring Apache for GitWeb...${NC}"
cat << EOF | sudo tee /etc/apache2/conf-available/gitweb.conf
Alias /gitweb /usr/share/gitweb

<Directory /usr/share/gitweb>
    Options +FollowSymLinks +ExecCGI
    AddHandler cgi-script .cgi
    DirectoryIndex gitweb.cgi
    Require all granted
</Directory>
EOF

sudo a2enconf gitweb
sudo systemctl restart apache2

# Create a post-receive hook template
echo -e "${YELLOW}Creating post-receive hook template...${NC}"
sudo mkdir -p "$GIT_HOME/hooks-template"
cat << 'EOF' | sudo tee "$GIT_HOME/hooks-template/post-receive"
#!/bin/bash

# This hook is called after a commit is received
# You can customize this for notifications, deployments, etc.

echo "Repository updated"
EOF

sudo chmod +x "$GIT_HOME/hooks-template/post-receive"
sudo chown -R "$GIT_USER:$GIT_GROUP" "$GIT_HOME/hooks-template"

# Create a script to add new repositories
echo -e "${YELLOW}Creating repository management scripts...${NC}"
cat << 'EOF' | sudo tee "$GIT_HOME/create-repo.sh"
#!/bin/bash

if [ $# -ne 1 ]; then
    echo "Usage: $0 <repository-name>"
    exit 1
fi

REPO_NAME=$1
REPO_PATH="/home/<USER>/repositories/$REPO_NAME.git"

if [ -d "$REPO_PATH" ]; then
    echo "Repository $REPO_NAME already exists!"
    exit 1
fi

mkdir -p "$REPO_PATH"
cd "$REPO_PATH"
git init --bare

# Copy hooks if the hooks template directory exists
if [ -d "/home/<USER>/hooks-template" ]; then
    cp -r /home/<USER>/hooks-template/* "$REPO_PATH/hooks/"
fi

chown -R git:git "$REPO_PATH"
chmod -R 775 "$REPO_PATH"

# Enable git-daemon export for anonymous access
touch "$REPO_PATH/git-daemon-export-ok"

echo "Repository $REPO_NAME created successfully"
echo "Clone URL: git@$(hostname):$REPO_NAME.git"
echo "Anonymous URL: git://$(hostname)/$REPO_NAME.git"
EOF

sudo chmod +x "$GIT_HOME/create-repo.sh"
sudo chown "$GIT_USER:$GIT_GROUP" "$GIT_HOME/create-repo.sh"

# Create a script to add SSH keys
cat << 'EOF' | sudo tee "$GIT_HOME/add-user-key.sh"
#!/bin/bash

if [ $# -ne 2 ]; then
    echo "Usage: $0 <username> <public-key-file>"
    exit 1
fi

USERNAME=$1
KEY_FILE=$2

if [ ! -f "$KEY_FILE" ]; then
    echo "Public key file not found: $KEY_FILE"
    exit 1
fi

echo "# $USERNAME - $(date)" >> /home/<USER>/.ssh/authorized_keys
cat "$KEY_FILE" >> /home/<USER>/.ssh/authorized_keys
echo "Added key for $USERNAME"
EOF

sudo chmod +x "$GIT_HOME/add-user-key.sh"
sudo chown "$GIT_USER:$GIT_GROUP" "$GIT_HOME/add-user-key.sh"

# Configure firewall
echo -e "${YELLOW}Configuring firewall...${NC}"
sudo apt install -y ufw
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow 9418/tcp  # Git protocol port
sudo ufw --force enable

# Final message
echo -e "${GREEN}Git server setup complete!${NC}"
echo -e "${YELLOW}Server IP: $(hostname -I | awk '{print $1}')${NC}"
echo -e "${YELLOW}To create a new repository:${NC}"
echo -e "  sudo -u $GIT_USER $GIT_HOME/create-repo.sh <repository-name>"
echo -e "${YELLOW}To add a user's SSH key:${NC}"
echo -e "  sudo -u $GIT_USER $GIT_HOME/add-user-key.sh <username> <path-to-public-key>"
echo -e "${YELLOW}Web interface:${NC}"
echo -e "  http://$(hostname -I | awk '{print $1}')/gitweb"
