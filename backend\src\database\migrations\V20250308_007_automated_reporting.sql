-- Automated Reporting Features Migration
-- Version: V20250308_007
-- Description: Adds comprehensive automated reporting capabilities

-- Report Templates Table
CREATE TABLE IF NOT EXISTS report_templates (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- 'project_status', 'git_metrics', 'team_performance', etc.
    template_config JSONB NOT NULL, -- Report structure and formatting
    created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Scheduled Reports Table
CREATE TABLE IF NOT EXISTS scheduled_reports (
    id SERIAL PRIMARY KEY,
    template_id INTEGER REFERENCES report_templates(id) ON DELETE CASCADE,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    schedule_type VARCHAR(50) NOT NULL, -- 'daily', 'weekly', 'monthly', 'custom'
    schedule_config JSONB NOT NULL, -- Cron expression or custom schedule
    recipients JSONB NOT NULL, -- List of email addresses or user IDs
    delivery_method VARCHAR(50)[], -- Array of 'email', 'slack', 'dashboard', etc.
    is_active BOOLEAN DEFAULT true,
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Generated Reports Table
CREATE TABLE IF NOT EXISTS generated_reports (
    id SERIAL PRIMARY KEY,
    scheduled_report_id INTEGER REFERENCES scheduled_reports(id) ON DELETE SET NULL,
    template_id INTEGER REFERENCES report_templates(id) ON DELETE SET NULL,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    report_type VARCHAR(50) NOT NULL,
    report_data JSONB NOT NULL, -- The actual report content
    metrics JSONB, -- Performance metrics, data points, etc.
    file_path VARCHAR(512), -- Path to generated file if applicable
    generated_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    generation_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'success', 'failed'
    error_details TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Report Sections Table
CREATE TABLE IF NOT EXISTS report_sections (
    id SERIAL PRIMARY KEY,
    template_id INTEGER REFERENCES report_templates(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    section_type VARCHAR(50) NOT NULL, -- 'text', 'chart', 'table', 'metrics'
    config JSONB NOT NULL, -- Section-specific configuration
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Report Delivery History
CREATE TABLE IF NOT EXISTS report_delivery_history (
    id SERIAL PRIMARY KEY,
    report_id INTEGER REFERENCES generated_reports(id) ON DELETE CASCADE,
    delivery_method VARCHAR(50) NOT NULL, -- 'email', 'slack', etc.
    recipient VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL, -- 'success', 'failed'
    error_details TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Report Access Log
CREATE TABLE IF NOT EXISTS report_access_log (
    id SERIAL PRIMARY KEY,
    report_id INTEGER REFERENCES generated_reports(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    access_type VARCHAR(50) NOT NULL, -- 'view', 'download', 'share'
    ip_address VARCHAR(45),
    user_agent TEXT,
    accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add report-related settings to projects
ALTER TABLE project_settings
ADD COLUMN IF NOT EXISTS report_settings JSONB DEFAULT '{
    "default_schedule": "weekly",
    "auto_generate_status_report": true,
    "include_git_metrics": true,
    "include_team_metrics": true,
    "default_recipients": [],
    "notification_on_generation": true
}'::jsonb;

-- Indexes
CREATE INDEX idx_report_templates_type ON report_templates(type);
CREATE INDEX idx_scheduled_reports_next_run ON scheduled_reports(next_run_at) WHERE is_active = true;
CREATE INDEX idx_generated_reports_project ON generated_reports(project_id, created_at);
CREATE INDEX idx_report_sections_template ON report_sections(template_id, order_index);
CREATE INDEX idx_report_delivery_history_status ON report_delivery_history(status, delivered_at);
CREATE INDEX idx_report_access_log_report ON report_access_log(report_id, accessed_at);

-- Update triggers
CREATE TRIGGER update_report_templates_updated_at
    BEFORE UPDATE ON report_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_scheduled_reports_updated_at
    BEFORE UPDATE ON scheduled_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_report_sections_updated_at
    BEFORE UPDATE ON report_sections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();
