/**
 * Git Operation Tracking Module
 * 
 * This module provides functionality to track and measure performance of Git operations
 * in the Project Tracker application.
 */

import { trackMetric } from '../services/front-performance-service';

/**
 * Git operation types that can be tracked
 */
export enum GitOperationType {
  CLONE = 'clone',
  PULL = 'pull',
  PUSH = 'push',
  COMMIT = 'commit',
  MERGE = 'merge',
  CHECKOUT = 'checkout',
  FETCH = 'fetch'
}

/**
 * Interface for Git operation metadata
 */
export interface GitOperationMetadata {
  repository?: string;
  branch?: string;
  commitCount?: number;
  fileCount?: number;
  userId: string;
  projectId?: string;
  additionalInfo?: Record<string, any>;
}

/**
 * Interface for completed Git operation data
 */
export interface GitOperationResult {
  status: 'success' | 'error' | 'warning';
  errorMessage?: string;
  duration: number;
  additionalData?: Record<string, any>;
}

/**
 * Tracks a Git operation and its performance
 * 
 * @param operation - The type of Git operation being performed
 * @param metadata - Additional metadata about the operation
 * @returns An object with a complete method to call when the operation finishes
 */
export function trackGitOperation(operation: GitOperationType, metadata: GitOperationMetadata) {
  const startTime = performance.now();
  
  return {
    /**
     * Call this method when the Git operation completes
     * 
     * @param status - The result status of the operation
     * @param additionalData - Any additional data to record
     */
    complete: (result: GitOperationResult) => {
      if (!result.duration) {
        const endTime = performance.now();
        result.duration = endTime - startTime;
      }
      
      trackMetric({
        category: 'git',
        operation,
        duration: result.duration,
        status: result.status,
        metadata: {
          ...metadata,
          ...(result.additionalData || {})
        },
        timestamp: new Date().toISOString()
      });

      // Return the duration for immediate use if needed
      return result.duration;
    }
  };
}

/**
 * Utility function to track Git clone operations
 * 
 * @param repository - Repository URL or identifier
 * @param metadata - Additional metadata
 */
export function trackGitClone(repository: string, metadata: Omit<GitOperationMetadata, 'repository'>) {
  return trackGitOperation(GitOperationType.CLONE, {
    ...metadata,
    repository
  });
}

/**
 * Utility function to track Git pull operations
 * 
 * @param repository - Repository identifier
 * @param branch - Branch name
 * @param metadata - Additional metadata
 */
export function trackGitPull(repository: string, branch: string, metadata: Omit<GitOperationMetadata, 'repository' | 'branch'>) {
  return trackGitOperation(GitOperationType.PULL, {
    ...metadata,
    repository,
    branch
  });
}

/**
 * Utility function to track Git push operations
 * 
 * @param repository - Repository identifier
 * @param branch - Branch name
 * @param commitCount - Number of commits being pushed
 * @param metadata - Additional metadata
 */
export function trackGitPush(repository: string, branch: string, commitCount: number, metadata: Omit<GitOperationMetadata, 'repository' | 'branch' | 'commitCount'>) {
  return trackGitOperation(GitOperationType.PUSH, {
    ...metadata,
    repository,
    branch,
    commitCount
  });
}
