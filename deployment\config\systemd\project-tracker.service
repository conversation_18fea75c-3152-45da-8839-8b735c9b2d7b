[Unit]
Description=Project Tracker Application Service
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
User=project-tracker
Group=project-tracker
WorkingDirectory=/opt/project-tracker/backend
ExecStart=/opt/project-tracker/venv/bin/python -m gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
Restart=on-failure
RestartSec=5
Environment=PATH=/opt/project-tracker/venv/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/opt/project-tracker/backend
Environment=FLASK_ENV=production
Environment=POSTGRES_HOST=localhost
Environment=POSTGRES_DB=project_tracker
Environment=POSTGRES_USER=project_tracker
Environment=REDIS_HOST=localhost
Environment=REDIS_PORT=6379

# Security
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=full

[Install]
WantedBy=multi-user.target
