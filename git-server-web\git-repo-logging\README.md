# Git Repository Logging Agent

A C++23 service that provides advanced logging capabilities for the Git Dashboard, replacing the JavaScript logging functionality.

## Features

- High-performance log collection and processing
- Real-time log streaming via WebSockets
- Advanced log filtering and search
- Support for multiple log sources (system, nginx, git, auth)
- Structured logging with strong typing
- Efficient storage and retrieval of historical logs

## API Endpoints

- `/api/logs` - Get logs with filtering options
- `/api/logs/sources` - Get available log sources
- `/api/logs/stream` - WebSocket endpoint for real-time log streaming
- `/api/logs/stats` - Get log statistics

## Requirements

- C++23 compatible compiler (GCC 12+ or Clang 15+)
- CMake 3.16+
- Boost libraries (system, filesystem, asio)
- WebSocket++ for WebSocket support
- jsoncpp for JSON handling
- spdlog for internal logging
- fmt library for string formatting

## Building

```bash
mkdir build
cd build
cmake ..
make
```

## Deployment

Use the provided deployment script:

```bash
.\deploy-git-repo-logging.bat
```

This will:
1. Build the application
2. Copy it to the server
3. Install dependencies
4. Set up the systemd service
5. Configure permissions

## Configuration

The service can be configured through environment variables:

- `LOG_SOURCES_PATH` - Path to log sources (default: `/var/log`)
- `SERVER_PORT` - HTTP server port (default: 8081)
- `LOG_LEVEL` - Logging level (default: INFO)
- `LOG_FILE` - Log file path (default: `/var/log/git-repo-logging.log`)
- `MAX_LOG_SIZE` - Maximum size of log storage (default: 100MB)
- `LOG_RETENTION_DAYS` - Number of days to retain logs (default: 30)

## Integration with Git Dashboard

This service is designed to integrate with the existing Git Dashboard. The integration will be implemented in phase 2, but the service provides compatible API endpoints that can be used as drop-in replacements for the current JavaScript implementation.
