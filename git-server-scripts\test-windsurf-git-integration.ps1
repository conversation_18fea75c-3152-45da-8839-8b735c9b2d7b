# Test Windsurf Git Integration
# This script tests the integration between Windsurf and the Git server

# Configuration
$configFile = "$PSScriptRoot\..\windsurf-git-config.json"
$testCommitMessage = "Test commit from Windsurf integration test script"
$testFileName = "windsurf-git-integration-test.md"

# Function to display colored output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Check if config file exists
if (-not (Test-Path $configFile)) {
    Write-ColorOutput Red "Error: Config file not found at $configFile"
    exit 1
}

# Read configuration
Write-ColorOutput Green "Reading Git configuration from $configFile"
$config = Get-Content -Path $configFile | ConvertFrom-Json

# Display configuration
Write-ColorOutput Cyan "Git Server Configuration:"
Write-Output "Host: $($config.gitServer.host)"
Write-Output "User: $($config.gitServer.user)"
Write-Output "Repo Base Path: $($config.gitServer.repoBasePath)"
Write-Output "Default Repo: $($config.gitServer.defaultRepo)"

# Test SSH connection to Git server
Write-ColorOutput Green "\nTesting SSH connection to Git server..."
$sshTestResult = ssh -o BatchMode=yes "$($config.gitServer.user)@$($config.gitServer.host)" "echo CONNECTION_OK"

if ($sshTestResult -eq "CONNECTION_OK") {
    Write-ColorOutput Green "SSH connection successful!"
} else {
    Write-ColorOutput Red "SSH connection failed!"
    exit 1
}

# Test Git operations
Write-ColorOutput Green "\nTesting Git operations..."

# Get current directory
$currentDir = Get-Location

# Navigate to project root
Set-Location "$PSScriptRoot\.."

# Create a test file
Write-ColorOutput Cyan "Creating test file..."
$testContent = @"
# Windsurf Git Integration Test

This file was created by the Windsurf Git integration test script on $(Get-Date).

## Test Details

- Server: $($config.gitServer.host)
- User: $($config.gitServer.user)
- Repository: $($config.gitServer.defaultRepo)

If you can see this file in the repository, the integration test was successful!
"@

$testContent | Out-File -FilePath $testFileName -Encoding utf8

# Add the file to Git
Write-ColorOutput Cyan "Adding test file to Git..."
git add $testFileName

# Commit the file
Write-ColorOutput Cyan "Committing test file..."
git commit -m "$testCommitMessage"

# Push to remote
Write-ColorOutput Cyan "Pushing to remote repository..."
$pushResult = git push origin master

if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput Green "Push successful! Windsurf Git integration is working correctly."
} else {
    Write-ColorOutput Red "Push failed with exit code $LASTEXITCODE"
    Write-Output $pushResult
    exit 1
}

# Verify on server
Write-ColorOutput Green "\nVerifying commit on server..."
$verifyResult = ssh "$($config.gitServer.user)@$($config.gitServer.host)" "cd $($config.repositories[0].path) && git log -1 --pretty=format:'%s'"

if ($verifyResult -eq $testCommitMessage) {
    Write-ColorOutput Green "Verification successful! Found commit: $verifyResult"
} else {
    Write-ColorOutput Yellow "Verification result does not match expected commit message."
    Write-Output "Expected: $testCommitMessage"
    Write-Output "Actual: $verifyResult"
}

# Return to original directory
Set-Location $currentDir

Write-ColorOutput Green "\nWindsurf Git integration test completed successfully!"
Write-ColorOutput Cyan "You can now safely remove Git from the local Build1 server."
