/**
 * Git Repository Utilities
 * Common utilities and helper functions for Git repository components
 */
const GitUtils = {
    /**
     * Format a date string
     * @param {string} dateString - ISO date string
     * @param {string} format - Output format ('full' or 'date')
     * @returns {string} Formatted date
     */
    formatDate(dateString, format = 'full') {
        if (!dateString) return 'Never';
        
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return 'Invalid date';
            }
            
            if (format === 'date') {
                return date.toLocaleDateString();
            } else {
                return date.toLocaleString();
            }
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'Error';
        }
    },
    
    /**
     * Calculate relative time from now
     * @param {string} dateString - ISO date string
     * @returns {string} Relative time (e.g., '2 days ago')
     */
    timeAgo(dateString) {
        if (!dateString) return 'Never';
        
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return 'Invalid date';
            }
            
            const now = new Date();
            const seconds = Math.floor((now - date) / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            const months = Math.floor(days / 30);
            const years = Math.floor(months / 12);
            
            if (years > 0) return `${years} year${years > 1 ? 's' : ''} ago`;
            if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
            if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
            if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
            if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
            return `${seconds} second${seconds !== 1 ? 's' : ''} ago`;
        } catch (error) {
            console.error('Error calculating time ago:', error);
            return 'Error';
        }
    },
    
    /**
     * Parse size string (e.g., "1.2 MB") into bytes
     * @param {string} sizeString - Size string to parse
     * @returns {number} Size in bytes
     */
    parseSize(sizeString) {
        if (!sizeString || typeof sizeString !== 'string') {
            return 0;
        }
        
        try {
            // Parse size values like "1.2 MB", "512 KB", etc.
            const units = {
                'B': 1,
                'KB': 1024,
                'MB': 1024 * 1024,
                'GB': 1024 * 1024 * 1024,
                'TB': 1024 * 1024 * 1024 * 1024
            };
            
            const regex = /(\d+(\.\d+)?)\s*(B|KB|MB|GB|TB)/i;
            const match = sizeString.match(regex);
            
            if (match) {
                const size = parseFloat(match[1]);
                const unit = match[3].toUpperCase();
                return size * units[unit];
            }
            
            // Try to parse a simple number
            const simpleNumber = parseFloat(sizeString);
            if (!isNaN(simpleNumber)) {
                return simpleNumber;
            }
            
            return 0;
        } catch (error) {
            console.error('Error parsing size string:', error);
            return 0;
        }
    },
    
    /**
     * Format bytes to human-readable string
     * @param {number} bytes - Size in bytes
     * @param {number} decimals - Number of decimal places
     * @returns {string} Formatted size string
     */
    formatBytes(bytes, decimals = 2) {
        if (bytes === 0 || bytes === null || bytes === undefined) return 'Unknown';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },
    
    /**
     * Parse repository size string to number in MB
     * @param {string} sizeStr - Size string (e.g., "1.5 MB", "800 KB")
     * @returns {number} Size in MB
     */
    parseSizeToMB(sizeStr) {
        if (!sizeStr || typeof sizeStr !== 'string') {
            console.error('Invalid size string:', sizeStr);
            return 0;
        }
        
        try {
            // Match the format "X.Y [K|M|G|T]B"
            const matches = sizeStr.match(/(\d+(\.\d+)?)\s*([KMGT]?)B/);
            if (!matches) {
                return 0;
            }
            
            const value = parseFloat(matches[1]);
            const unit = matches[3];
            
            // Convert to MB based on unit
            switch (unit) {
                case 'K': // KB to MB
                    return value / 1024;
                case 'M': // Already in MB
                    return value;
                case 'G': // GB to MB
                    return value * 1024;
                case 'T': // TB to MB
                    return value * 1024 * 1024;
                default: // Bytes to MB
                    return value / (1024 * 1024);
            }
        } catch (error) {
            console.error('Error parsing size:', error);
            return 0;
        }
    },
    
    /**
     * Format bytes to human-readable string
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    formatBytesOld(bytes) {
        if (bytes === 0) return '0 B';
        
        const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        
        return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + units[i];
    },
    
    /**
     * Format date string to user-friendly format
     * @param {string} dateStr - ISO date string
     * @returns {string} Formatted date string
     */
    formatDateOld(dateStr) {
        if (!dateStr) return 'Never';
        
        try {
            const date = new Date(dateStr);
            if (isNaN(date)) return 'Invalid date';
            
            // Check if the date is today
            const today = new Date();
            const isToday = date.getDate() === today.getDate() &&
                           date.getMonth() === today.getMonth() &&
                           date.getFullYear() === today.getFullYear();
            
            if (isToday) {
                return `Today at ${date.toLocaleTimeString()}`;
            }
            
            // Check if the date is yesterday
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const isYesterday = date.getDate() === yesterday.getDate() &&
                              date.getMonth() === yesterday.getMonth() &&
                              date.getFullYear() === yesterday.getFullYear();
            
            if (isYesterday) {
                return `Yesterday at ${date.toLocaleTimeString()}`;
            }
            
            // Format date for older dates
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        } catch (error) {
            console.error('Error formatting date:', error);
            return dateStr; // Return original if parsing fails
        }
    },
    
    /**
     * Calculate repository health metrics based on activity and other factors
     * @param {Object} repo - Repository object
     * @returns {Object} Health metrics object with percentage, status, and message
     */
    calculateRepositoryHealth(repo) {
        if (!repo) return { percentage: 0, status: 'unknown', message: 'Unknown repository' };
        
        // Default values
        let percentage = 0;
        let status = 'unknown';
        let message = 'Unable to determine repository health';
        
        try {
            // Check if we have last commit date
            if (repo.last_commit) {
                const lastCommitDate = new Date(repo.last_commit);
                const now = new Date();
                const daysDifference = Math.floor((now - lastCommitDate) / (1000 * 60 * 60 * 24));
                
                // Calculate health based on activity (days since last commit)
                if (daysDifference <= 7) { // Within a week
                    percentage = 100;
                    status = 'good';
                    message = 'Active repository with recent commits';
                } else if (daysDifference <= 30) { // Within a month
                    percentage = 75;
                    status = 'good';
                    message = 'Regular activity in the last month';
                } else if (daysDifference <= 90) { // Within 3 months
                    percentage = 50;
                    status = 'fair';
                    message = 'Some inactivity, last commit was ' + daysDifference + ' days ago';
                } else if (daysDifference <= 180) { // Within 6 months
                    percentage = 25;
                    status = 'poor';
                    message = 'Low activity, last commit was ' + daysDifference + ' days ago';
                } else { // More than 6 months
                    percentage = 10;
                    status = 'poor';
                    message = 'Inactive repository, last commit was ' + daysDifference + ' days ago';
                }
            } else {
                // No commit information
                percentage = 0;
                status = 'unknown';
                message = 'No commit information available';
            }
            
            return { percentage, status, message };
        } catch (error) {
            console.error('Error calculating repository health:', error);
            return { percentage: 0, status: 'unknown', message: 'Error calculating health' };
        }
    },
    
    /**
     * Clean up a Chart.js instance safely
     * @param {Object} chart - Chart.js instance
     * @returns {boolean} Success status
     */
    cleanupChartInstance(chart) {
        if (chart && typeof chart.destroy === 'function') {
            try {
                chart.destroy();
                return true;
            } catch (error) {
                console.error('Error destroying chart:', error);
                return false;
            }
        }
        return false;
    },
    
    /**
     * Convert any date format to YYYY-MM-DD
     * @param {Date|string} date - Date to convert
     * @returns {string} Formatted date string
     */
    formatYYYYMMDD(date) {
        try {
            const d = new Date(date);
            if (isNaN(d)) return '';
            
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            
            return `${year}-${month}-${day}`;
        } catch (error) {
            console.error('Error formatting date to YYYY-MM-DD:', error);
            return '';
        }
    }
};

// Expose the utilities globally
window.GitUtils = GitUtils;

