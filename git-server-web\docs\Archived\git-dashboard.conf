# Git Dashboard Nginx Configuration

# Define rate limiting zone locally if not defined elsewhere
limit_req_zone $binary_remote_addr zone=dashboard_api_limit:10m rate=10r/s;

server {
    # Redirect HTTP to HTTPS
    listen 80;
    server_name git.chcit.org;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name git.chcit.org;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/chcit.org/chain.pem;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305;

    # HSTS (ngx_http_headers_module is required) (63072000 seconds = 2 years)
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    # Updated CSP to include Bootstrap scripts
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; style-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com;" always;

    # Logging configuration
    access_log /var/log/nginx/git-dashboard.access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/git-dashboard.error.log warn;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;

    # Root Directory
    root /opt/git-dashboard;
    index index.html;

    # JavaScript and CSS files - NO CACHING during dashboard development
    # For production, enable caching by uncommenting the expires line and commenting out the no-cache headers
    location ~* \.(js|css)$ {
        expires -1;
        #expires 7d; # Uncomment for production
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        #add_header Cache-Control "public, max-age=604800"; # Uncomment for production
        access_log off;
        gzip_static on;
    }

    # Frontend Routes
    location / {
        try_files $uri $uri/ /index.html;
        expires -1;
        #expires 1h; # Uncomment for production
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        #add_header Cache-Control "public, max-age=3600"; # Uncomment for production
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # API Routes (Flask backend)
    location /api {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Rate limiting - using locally defined zone (or external if available)
        limit_req zone=dashboard_api_limit burst=20 nodelay;
        limit_req_status 429;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Metrics history endpoint with no caching during development
    location /api/metrics/history {
        proxy_pass http://127.0.0.1:8000;
        # Disable caching for development
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires 0;
        proxy_no_cache 1;
        proxy_cache_bypass 1;
        
        # For production, enable caching by uncommenting these lines
        # proxy_cache dashboard_cache;
        # proxy_cache_valid 200 5m;
        # proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        # add_header X-Cache-Status $upstream_cache_status;
    }
    
    # Certificate endpoints
    location /api/certificates {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Higher timeout for certificate operations which might take longer
        proxy_connect_timeout 180s;
        proxy_send_timeout 180s;
        proxy_read_timeout 180s;
    }
}
