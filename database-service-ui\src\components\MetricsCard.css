.metrics-card {
  margin-bottom: 1.5rem;
}

.metrics-card-title {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.metrics-loading {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.metrics-error {
  margin-bottom: 0;
}

.metrics-table {
  width: 100%;
  border-collapse: collapse;
}

.metrics-table tr {
  border-bottom: 1px solid var(--border-color);
}

.metrics-table tr:last-child {
  border-bottom: none;
}

.metrics-table td {
  padding: 0.75rem 0;
}

.metric-name {
  font-weight: 500;
  color: var(--text-primary);
  text-transform: capitalize;
  width: 40%;
}

.metric-value {
  text-align: right;
  font-family: monospace;
}

.metric-value pre {
  text-align: left;
  margin: 0;
  white-space: pre-wrap;
  font-size: 0.85rem;
}
