# Database Service and UI Integration Guide

This document provides comprehensive guidance for integrating the Database Service C++23 application with its React/TypeScript UI, including deployment, configuration, and operational procedures.

## Table of Contents

1. [Integration Overview](#integration-overview)
2. [Architecture Integration](#architecture-integration)
3. [Development Environment Setup](#development-environment-setup)
4. [Production Deployment](#production-deployment)
5. [API Integration](#api-integration)
6. [Security Integration](#security-integration)
7. [Monitoring and Metrics](#monitoring-and-metrics)
8. [Troubleshooting](#troubleshooting)
9. [Maintenance Procedures](#maintenance-procedures)

## Integration Overview

The Database Service integration consists of three main components working together:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React UI      │    │   Nginx Proxy    │    │ Database Service│
│  (TypeScript)   │◄──►│  (SSL/Reverse)   │◄──►│   (C++23)       │
│                 │    │                  │    │                 │
│ Port: 3000/80   │    │ Port: 443        │    │ Port: 8080      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   Database      │
                       │                 │
                       │ Port: 5432      │
                       └─────────────────┘
```

### Key Integration Points

- **API Communication**: REST API over HTTPS with JWT authentication
- **SSL Termination**: <PERSON>inx handles SSL, forwards HTTP to backend
- **URL Routing**: `/database-api/*` routes to database service
- **Authentication**: Shared JWT tokens between UI and service
- **Configuration**: Environment-specific configurations for each component

## Architecture Integration

### SSL Termination Strategy

**Architectural Decision: Reverse Proxy SSL Termination**

The Database Service is designed to use **reverse proxy SSL termination** with Nginx rather than implementing SSL directly in the C++23 application. This is the recommended production approach for the following reasons:

#### **Why Reverse Proxy SSL Termination?**

1. **Performance Benefits:**
   - Nginx handles SSL/TLS encryption/decryption more efficiently than application-level SSL
   - Reduces CPU overhead on the Database Service application
   - Enables SSL session reuse and connection pooling

2. **Operational Advantages:**
   - Centralized certificate management through existing git.chcit.org infrastructure
   - Leverages existing Let's Encrypt certificate automation
   - Simplified certificate renewal process
   - Unified SSL configuration across all services

3. **Security Benefits:**
   - SSL termination at the edge provides better security isolation
   - Nginx security headers applied consistently
   - Rate limiting and DDoS protection at the proxy level
   - Simplified firewall rules (only Nginx needs external access)

4. **Maintenance Simplicity:**
   - Database Service runs as HTTP-only, reducing complexity
   - No need for certificate access in the application
   - Easier debugging and monitoring of HTTP traffic
   - Simplified deployment and configuration management

#### **Configuration Changes Made:**

**Database Service Configuration:**
```json
{
  "api": {
    "port": 8080,
    "host": "127.0.0.1",  // Bind to localhost only
    "ssl": {
      "enabled": false,   // SSL disabled - handled by Nginx
      "note": "SSL termination handled by Nginx reverse proxy"
    }
  }
}
```

**Nginx Integration:**
- Added upstream backend configuration
- Configured SSL termination with existing certificates
- Implemented URL rewriting (`/database-api/*` → `/api/*`)
- Added security headers and rate limiting

### Network Architecture

**Development Environment:**
```
Client Browser → React Dev Server (localhost:3000) → Database Service (localhost:8080)
                                                      (HTTP - No SSL needed)
```

**Production Environment:**
```
Client Browser → Nginx (git.chcit.org:443) → Database Service (localhost:8080)
    (HTTPS)           ↓ (SSL Termination)        (HTTP - Internal only)
               React Build (Static Files)
```

### Component Responsibilities

| Component | Responsibility | Technology |
|-----------|---------------|------------|
| **React UI** | User interface, client-side logic | React 18, TypeScript, Chart.js |
| **Nginx** | SSL termination, static file serving, reverse proxy | Nginx 1.18+ |
| **Database Service** | Business logic, database operations, API endpoints | C++23, PostgreSQL, JWT |
| **PostgreSQL** | Data persistence, transaction management | PostgreSQL 14+ |

### Data Flow

1. **User Authentication**:
   ```
   UI → POST /database-api/auth/login → Database Service → PostgreSQL
   UI ← JWT Tokens ← Database Service ← User Validation
   ```

2. **Database Operations**:
   ```
   UI → GET /database-api/databases → Database Service → PostgreSQL
   UI ← Database List ← Database Service ← Query Results
   ```

3. **Metrics Collection**:
   ```
   UI → GET /database-api/database/metrics → Database Service → Internal Metrics
   UI ← Metrics Data ← Database Service ← Aggregated Data
   ```

## Development Environment Setup

### Prerequisites

**Backend (Database Service):**
- GCC 14.2+ or Clang 16+ (C++23 support)
- CMake 3.20+
- PostgreSQL 14+ development libraries
- OpenSSL development libraries
- Boost libraries
- nlohmann/json library

**Frontend (React UI):**
- Node.js 18+
- npm 8+
- Modern web browser

### Backend Setup

1. **Clone and Build Database Service:**
   ```bash
   cd database-service
   mkdir build && cd build
   cmake -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON ..
   make -j$(nproc)
   ```

2. **Configure Development Environment:**
   ```bash
   # Copy development configuration
   cp ../config/config-dev.json ./config.json
   
   # Edit configuration for local development
   nano config.json
   ```

3. **Start Database Service:**
   ```bash
   ./database-service --config config.json
   ```

### Frontend Setup

1. **Install Dependencies:**
   ```bash
   cd database-service-ui
   npm install
   ```

2. **Configure Environment:**
   ```bash
   # Create .env.development
   cat > .env.development << EOF
   REACT_APP_API_URL=http://localhost:8080/api
   REACT_APP_DATABASE_API_URL=http://localhost:8080/database-api
   REACT_APP_ENVIRONMENT=development
   EOF
   ```

3. **Start Development Server:**
   ```bash
   npm start
   ```

### Development Workflow

1. **Backend Changes:**
   - Modify C++ source code
   - Rebuild: `make -j$(nproc)`
   - Restart service
   - Test API endpoints

2. **Frontend Changes:**
   - Modify React components
   - Hot reload automatically updates
   - Test UI functionality

3. **Integration Testing:**
   ```bash
   # Test API connectivity
   curl http://localhost:8080/api/health
   
   # Test UI connectivity
   curl http://localhost:3000
   
   # Run integration tests
   cd database-service
   ./scripts/test-integration.sh --local-only
   ```

## Production Deployment

### Infrastructure Requirements

**Server Specifications:**
- Ubuntu 22.04+ LTS
- 4+ CPU cores
- 8+ GB RAM
- 100+ GB SSD storage
- Network connectivity

**Software Requirements:**
- Nginx 1.18+
- PostgreSQL 14+
- Let's Encrypt certificates
- systemd

### Deployment Process

1. **Prepare Server Environment:**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install dependencies
   sudo apt install -y nginx postgresql postgresql-contrib \
                       build-essential cmake libboost-all-dev \
                       libpq-dev libssl-dev nlohmann-json3-dev
   ```

2. **Deploy Database Service:**
   ```bash
   # Build production version
   cd database-service
   mkdir build && cd build
   cmake -DCMAKE_BUILD_TYPE=Release \
         -DCMAKE_INSTALL_PREFIX=/opt/database-service \
         -DBUILD_TESTS=OFF ..
   make -j$(nproc)
   sudo make install
   
   # Configure production settings
   sudo cp ../config/config-prod.json /opt/database-service/etc/database-service/config.json
   sudo nano /opt/database-service/etc/database-service/config.json
   
   # Install and start systemd service
   sudo systemctl enable database-service
   sudo systemctl start database-service
   ```

3. **Deploy React UI:**
   ```bash
   # Build production UI
   cd database-service-ui
   npm ci --production
   npm run build
   
   # Deploy to web server
   sudo cp -r build/* /var/www/html/database-ui/
   sudo chown -R www-data:www-data /var/www/html/database-ui/
   ```

4. **Configure Nginx Integration:**
   ```bash
   # Integrate with existing git.chcit.org configuration
   sudo ./scripts/integrate-with-git-server.sh
   
   # Test configuration
   sudo nginx -t
   sudo systemctl reload nginx
   ```

### Production Configuration

#### **Reverse Proxy SSL Configuration Changes**

The following configuration changes implement the reverse proxy SSL termination strategy:

**1. Database Service Configuration (`config-prod.json`):**
```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "gitdashboard",
    "user": "database_service_user",
    "ssl": {
      "enabled": true,        // SSL for database connections (client certs)
      "mode": "verify-full"   // Full certificate verification for DB
    }
  },
  "api": {
    "port": 8080,
    "host": "127.0.0.1",     // CHANGED: Bind to localhost only
    "ssl": {
      "enabled": false,      // CHANGED: SSL disabled for API server
      "note": "SSL termination handled by Nginx reverse proxy"
    },
    "cors": {
      "enabled": true,
      "allowed_origins": [
        "https://git.chcit.org"  // CHANGED: Updated for integrated domain
      ]
    }
  },
  "security": {
    "enable_authentication": true,
    "secure_credential_storage": {
      "enabled": true
    }
  }
}
```

**Key Changes Made:**
- ✅ **API SSL Disabled**: `api.ssl.enabled = false` (was true in original design)
- ✅ **Localhost Binding**: `api.host = "127.0.0.1"` (prevents external access)
- ✅ **CORS Origins Updated**: Changed from separate subdomain to integrated path
- ✅ **Database SSL Kept**: Database connections still use SSL for security

**2. Development Configuration (`config-dev.json`):**
```json
{
  "database": {
    "ssl": {
      "enabled": false,      // No SSL needed for development
      "note": "SSL disabled for development"
    }
  },
  "api": {
    "ssl": {
      "enabled": false,      // No SSL needed for development
      "note": "SSL termination handled by Nginx in production"
    },
    "cors": {
      "allowed_origins": ["*"]  // Permissive CORS for development
    }
  }
}
```

**React UI Configuration (`.env.production`):**
```bash
REACT_APP_API_URL=https://git.chcit.org/database-api
REACT_APP_DATABASE_API_URL=https://git.chcit.org/database-api
REACT_APP_ENVIRONMENT=production
```

**3. Nginx Reverse Proxy Configuration:**

The Nginx configuration implements SSL termination and reverse proxy functionality:

```nginx
# STEP 1: Upstream backend (added to http context)
upstream database_service_backend {
    server 127.0.0.1:8080;    # Database service HTTP endpoint
    keepalive 32;             # Connection pooling
}

# STEP 2: Rate limiting zones (added to http context)
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;

# STEP 3: Location blocks (added to existing git.chcit.org HTTPS server block)
location /database-api/ {
    # Rate limiting
    limit_req zone=api_limit burst=20 nodelay;

    # URL rewriting: /database-api/health → /api/health
    rewrite ^/database-api/(.*)$ /api/$1 break;

    # Reverse proxy to HTTP backend
    proxy_pass http://database_service_backend;
    proxy_http_version 1.1;

    # Forward client information
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;  # Tells backend it's HTTPS
    proxy_set_header X-Forwarded-Host $server_name;
    proxy_set_header X-Forwarded-Port $server_port;

    # Timeouts
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;

    # Security headers
    add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    add_header Pragma "no-cache" always;
    add_header Expires "0" always;
}

# Authentication endpoints with stricter rate limiting
location /database-api/auth/ {
    limit_req zone=auth_limit burst=10 nodelay;
    rewrite ^/database-api/(.*)$ /api/$1 break;
    proxy_pass http://database_service_backend;
    # ... same proxy settings as above
}

# UI static files
location /database-ui/ {
    alias /var/www/html/database-ui/;
    try_files $uri $uri/ /database-ui/index.html;

    # Static file caching
    expires 1h;
    add_header Cache-Control "public, immutable";
}
```

**Key Nginx Features Implemented:**
- ✅ **SSL Termination**: Uses existing git.chcit.org certificates
- ✅ **Reverse Proxy**: Forwards HTTPS requests to HTTP backend
- ✅ **URL Rewriting**: Maps `/database-api/*` to `/api/*`
- ✅ **Rate Limiting**: Protects against abuse
- ✅ **Security Headers**: Adds security headers to responses
- ✅ **Connection Pooling**: Maintains persistent connections to backend

## API Integration

### Authentication Flow

1. **Login Process:**
   ```typescript
   // Frontend login request
   const response = await fetch('/database-api/auth/login', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ username, password })
   });
   
   const { accessToken, refreshToken } = await response.json();
   ```

2. **Token Management:**
   ```typescript
   // Automatic token refresh
   class ApiService {
     private async refreshToken() {
       const response = await fetch('/database-api/auth/refresh', {
         method: 'POST',
         headers: { 'Authorization': `Bearer ${this.refreshToken}` }
       });
       return response.json();
     }
   }
   ```

### API Endpoint Mapping

| UI Function | HTTP Method | Endpoint | Backend Handler |
|-------------|-------------|----------|-----------------|
| User Login | POST | `/database-api/auth/login` | `SecurityManager::authenticate()` |
| Get Databases | GET | `/database-api/databases` | `DatabaseService::listDatabases()` |
| Get Metrics | GET | `/database-api/database/metrics` | `MetricsCollector::getMetrics()` |
| Store Credential | POST | `/database-api/credentials/store` | `CredentialStore::storeCredential()` |
| Health Check | GET | `/database-api/health` | `DatabaseService::healthCheck()` |

### Error Handling Integration

**Backend Error Response Format:**
```cpp
// C++ error response structure
nlohmann::json errorResponse = {
    {"success", false},
    {"error", {
        {"code", 400},
        {"message", "Invalid request"},
        {"details", "Username is required"}
    }},
    {"timestamp", std::chrono::system_clock::now()}
};
```

**Frontend Error Handling:**
```typescript
// TypeScript error handling
interface ApiError {
  success: false;
  error: {
    code: number;
    message: string;
    details?: string;
  };
  timestamp: string;
}

class ApiService {
  private async handleResponse(response: Response) {
    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(`API Error ${error.error.code}: ${error.error.message}`);
    }
    return response.json();
  }
}
```
