# Test SSH Connection Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
# Import UI module
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force
# Import SSHManager module
try {
    # Check if the module exists first
    if (Test-Path -Path "$PSScriptRoot\SSHManager.psm1") {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
        Write-Log -Message "SSHManager module loaded successfully." -Level "Debug" -Component "Test-SSHConnection"
    } else {
        Write-Log -Message "SSHManager module file not found at: $PSScriptRoot\SSHManager.psm1" -Level "Warning" -Component "Test-SSHConnection"
    }
} catch {
    Write-Log -Message "Failed to load SSHManager module: $_" -Level "Warning" -Component "Test-SSHConnection"
}

function Test-SSHConnectionUI {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }
    Show-MenuTitle -Title "Test SSH Connection"
    Show-MenuDivider
    Write-Log -Message " " -Level "UI"

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Log -Message "Configuration is not loaded. Loading configuration..." -Level "UI" -ForegroundColor Yellow
        # Try to load configuration
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        Write-Log -Message "Loading configuration from $configPath" -Level "UI" -ForegroundColor Yellow

        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Log -Message "Configuration loaded successfully." -Level "UI" -ForegroundColor Green
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            # Don't try to call Show-MainMenu here - just return
            return
        }
    }

    # Check if SSH configuration exists
    if ($null -eq $script:Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        # Don't try to call Show-MainMenu here - just return
        return
    }

    # Display SSH configuration
    Write-Log -Message "SSH Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "Host: $($script:Config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "User: $($script:Config.ssh.username)" -Level "UI" -ForegroundColor White
    Write-Log -Message "Port: $($script:Config.ssh.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "Key Path: $($script:Config.ssh.local_key_path)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "Testing SSH Connection to $($script:Config.ssh.host)..." -Level "UI" -ForegroundColor Cyan

    # Check if key file exists
    if (-not (Test-Path -Path $script:Config.ssh.local_key_path)) {
        Write-Log -Message "SSH key file not found at: $($script:Config.ssh.local_key_path)" -Level "UI" -ForegroundColor Red
        Write-Log -Message "Please make sure the SSH key file exists." -Level "UI" -ForegroundColor Yellow
        return
    }

    # Use our Test-SSHConnection function
    try {
        Write-Log -Message "Attempting SSH connection..." -Level "UI" -ForegroundColor Cyan
        $connected = Test-SSHConnection -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path

        if ($connected) {
            Write-Log -Message "SSH connection successful!" -Level "UI" -ForegroundColor Green
            Write-Log -Message "Output: SSH connection test successful" -Level "UI" -ForegroundColor White
        } else {
            Write-Log -Message "SSH connection failed!" -Level "UI" -ForegroundColor Red
            Write-Log -Message "Please check your SSH configuration and make sure the server is reachable." -Level "UI" -ForegroundColor Yellow
        }
    } catch {
        Write-Log -Message "Exception during SSH connection test: $_" -Level "UI" -ForegroundColor Red
    }

    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }

    Get-UserInput -Prompt "Press Enter to continue..." | Out-Null

    # Don't try to call Show-MainMenu here - just return
    return
}

# Create a proper Test-SSHConnection function
function Test-SSHConnection {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$HostName,

        [Parameter(Mandatory = $true)]
        [string]$User,

        [Parameter(Mandatory = $false)]
        [int]$Port = 22,

        [Parameter(Mandatory = $true)]
        [string]$KeyPath,

        [Parameter(Mandatory = $false)]
        [switch]$Silent
    )

    # Check if SSHManager module is available
    $useSSHManager = $false
    try {
        if (Get-Module -Name "SSHManager") {
            $useSSHManager = $true
            if (-not $Silent) {
                Write-Log -Message "Using SSHManager module for SSH connection test" -Level "Debug" -Component "Test-SSHConnection"
            }

            # Try to use the SSHManager module's Test-SSHConnection function
            try {
                # Use Get-Command to check if the function exists in the SSHManager module
                if (Get-Command -Name "SSHManager\Test-SSHConnection" -ErrorAction SilentlyContinue) {
                    return SSHManager\Test-SSHConnection -HostName $HostName -User $User -Port $Port -KeyPath $KeyPath -Silent:$Silent
                } else {
                    # Try to call the function directly if it's already imported into the global namespace
                    if (Get-Command -Name "Invoke-SSHCommand" -ErrorAction SilentlyContinue) {
                        if (-not $Silent) {
                            Write-Log -Message "Using global Invoke-SSHCommand function" -Level "Debug" -Component "Test-SSHConnection"
                        }
                        $result = Invoke-SSHCommand -Command "echo 'Connection successful'" -HostName $HostName -User $User -Port $Port -KeyPath $KeyPath -Silent:$Silent
                        return ($result.Success -and $result.Output -match "Connection successful")
                    } else {
                        $useSSHManager = $false
                    }
                }
            } catch {
                if (-not $Silent) {
                    Write-Log -Message "Exception during SSHManager.Test-SSHConnection: $_" -Level "Warning" -Component "Test-SSHConnection"
                }
                $useSSHManager = $false
                # Fall through to direct SSH command
            }
        }
    } catch {
        $useSSHManager = $false
        if (-not $Silent) {
            Write-Log -Message "Error checking for SSHManager module: $_" -Level "Warning" -Component "Test-SSHConnection"
        }
    }

    if (-not $useSSHManager) {
        if (-not $Silent) {
            Write-Log -Message "SSHManager module not available or failed, using direct SSH command" -Level "Debug" -Component "Test-SSHConnection"
        }
    }

    # Use direct SSH command
    try {
        # Prepare SSH arguments
        $sshArgs = @()

        # Add identity file
        if (-not [string]::IsNullOrEmpty($KeyPath)) {
            if (Test-Path -Path $KeyPath) {
                $sshArgs += @("-i", "$KeyPath")
            } else {
                if (-not $Silent) {
                    Write-Log -Message "SSH key not found at path: $KeyPath" -Level "Warning" -Component "SSH"
                }
                return $false
            }
        }

        # Add port if not default
        if ($Port -ne 22) {
            $sshArgs += @("-p", "$Port")
        }

        # Add common options
        $sshArgs += @(
            "-o", "StrictHostKeyChecking=no",
            "-o", "BatchMode=yes",
            "-o", "ConnectTimeout=10"
        )

        # Add destination and command
        $sshArgs += @("${User}@${HostName}", "echo 'CONNECTION_OK'")

        # Create temporary files for output and error
        $outputFile = [System.IO.Path]::GetTempFileName()
        $errorFile = [System.IO.Path]::GetTempFileName()

        # Execute the command
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -NoNewWindow -PassThru -Wait -RedirectStandardOutput $outputFile -RedirectStandardError $errorFile

        # Read output and error
        $output = Get-Content -Path $outputFile -Raw -ErrorAction SilentlyContinue
        $errorOutput = Get-Content -Path $errorFile -Raw -ErrorAction SilentlyContinue

        # Clean up temporary files
        Remove-Item -Path $outputFile -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $errorFile -Force -ErrorAction SilentlyContinue

        if ($process.ExitCode -eq 0 -and $output -match "CONNECTION_OK") {
            return $true
        } else {
            if (-not $Silent) {
                Write-Log -Message "SSH connection failed with exit code: $($process.ExitCode)" -Level "Warning" -Component "Test-SSHConnection"
                if (-not [string]::IsNullOrEmpty($errorOutput)) {
                    Write-Log -Message "Error output: $errorOutput" -Level "Warning" -Component "Test-SSHConnection"
                }
            }
            return $false
        }
    } catch {
        if (-not $Silent) {
            Write-Log -Message "Exception during direct SSH command: $_" -Level "Warning" -Component "Test-SSHConnection"
        }
        return $false
    }
}

# Export the functions
Export-ModuleMember -Function Test-SSHConnectionUI, Test-SSHConnection
