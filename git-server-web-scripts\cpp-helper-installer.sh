#!/bin/bash
# C++ Helper Program Installation Script
# This script creates and installs the certificate sync helper program
# Run this script as root

# Color codes for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting C++ helper installation...${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}This script must be run as root${NC}"
    exit 1
fi

# Create directory structure
echo "Creating directory structure..."
mkdir -p /opt/git-dashboard/bin
mkdir -p /opt/git-dashboard/logs

# Install required packages
echo "Checking for required build packages..."
if command -v apt-get >/dev/null 2>&1; then
    apt-get update
    apt-get install -y build-essential g++ libssl-dev
elif command -v yum >/dev/null 2>&1; then
    yum install -y gcc-c++ openssl-devel
else
    echo -e "${YELLOW}Could not determine package manager. Please install C++ compiler manually if not already installed.${NC}"
fi

# Install the C++ certificate sync helper
echo "Creating certificate sync helper..."
cat > /opt/git-dashboard/bin/cert_sync_helper.cpp << 'EOF'
// cert_sync_helper.cpp
// A setuid helper program to safely run the certificate sync script
// Compile with:
// g++ -std=c++17 -o cert_sync_helper cert_sync_helper.cpp
// Then set permissions:
// sudo chown root:www-data cert_sync_helper
// sudo chmod 4750 cert_sync_helper

#include <iostream>
#include <string>
#include <array>
#include <memory>
#include <stdexcept>
#include <cstring>
#include <cerrno>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <grp.h>
#include <syslog.h>

// Path to the certificate sync script (constant and not modifiable)
constexpr const char* SYNC_SCRIPT = "/opt/git-dashboard/sync-certificates.sh";

// Log a message to syslog and stdout
void log_message(const std::string& message, bool is_error = false) {
    int priority = is_error ? LOG_ERR : LOG_INFO;
    syslog(priority, "%s", message.c_str());
    if (is_error) {
        std::cerr << "ERROR: " << message << std::endl;
    } else {
        std::cout << message << std::endl;
    }
}

// Execute the sync script and capture output
std::string exec_sync_script() {
    std::array<char, 4096> buffer;
    std::string result;
    
    // Open pipe to command
    std::unique_ptr<FILE, decltype(&pclose)> pipe(
        popen(SYNC_SCRIPT, "r"), 
        pclose
    );
    
    if (!pipe) {
        throw std::runtime_error("popen() failed: " + std::string(strerror(errno)));
    }
    
    // Read command output
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    
    return result;
}

// Check if the user is in the www-data group
bool is_in_www_data_group() {
    // Get user info
    uid_t uid = geteuid();
    struct passwd *pw = getpwuid(uid);
    if (!pw) {
        log_message("Failed to get user info", true);
        return false;
    }
    
    // Get www-data group info
    struct group *gr = getgrnam("www-data");
    if (!gr) {
        log_message("Failed to get www-data group info", true);
        return false;
    }
    
    // Check if user is in www-data group
    for (char **members = gr->gr_mem; *members != nullptr; members++) {
        if (strcmp(*members, pw->pw_name) == 0) {
            return true;
        }
    }
    
    // Also check if user's primary group is www-data
    return pw->pw_gid == gr->gr_gid;
}

int main() {
    try {
        // Set up syslog
        openlog("cert_sync_helper", LOG_PID | LOG_PERROR, LOG_DAEMON);
        
        // Security check: only allow www-data group to run this
        if (!is_in_www_data_group()) {
            log_message("Access denied: Only www-data group can run this helper", true);
            return 1;
        }
        
        // Log the execution attempt
        log_message("Certificate sync helper started by uid=" + std::to_string(getuid()));
        
        // Execute the sync script
        std::string output = exec_sync_script();
        
        // Send the output as JSON response
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"success\",\n";
        std::cout << "  \"message\": \"Certificate sync completed\",\n";
        std::cout << "  \"output\": \"" << output << "\"\n";
        std::cout << "}\n";
        
        log_message("Certificate sync completed successfully");
        return 0;
    } 
    catch (const std::exception& e) {
        log_message(std::string("Error: ") + e.what(), true);
        
        // Send error as JSON
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"error\",\n";
        std::cout << "  \"message\": \"" << e.what() << "\"\n";
        std::cout << "}\n";
        
        return 1;
    }
}
EOF

# Compile the helper
echo "Compiling C++ helper..."
cd /opt/git-dashboard/bin
g++ -std=c++17 -o cert_sync_helper cert_sync_helper.cpp

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to compile certificate sync helper${NC}"
    exit 1
fi

# Set permissions
echo "Setting permissions on helper..."
chown root:www-data /opt/git-dashboard/bin/cert_sync_helper
chmod 4750 /opt/git-dashboard/bin/cert_sync_helper

echo -e "${GREEN}C++ helper installation completed successfully!${NC}"
echo "The setuid helper program has been installed at /opt/git-dashboard/bin/cert_sync_helper"
echo "You can now run the second installation script to update the rest of the components."
