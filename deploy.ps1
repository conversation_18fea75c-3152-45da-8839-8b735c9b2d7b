# Git Server Deployment Script

# Define paths
$localPath = "d:/Codeium/CHCIT/project-tracker/git-server-web"
$remotePath = "/opt/git-dashboard"
$remoteHost = "git-server"
$remoteUser = "ubuntu"

# Files to deploy
$filesToDeploy = @(
    "app.py",
    "js/git-repositories.js"
)

Write-Host "Starting deployment to $remoteHost..." -ForegroundColor Green

# Create a temporary directory for files to transfer
$tempDir = "./temp-deploy"
if (Test-Path $tempDir) {
    Remove-Item -Path $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# Copy files to temporary directory with proper structure
foreach ($file in $filesToDeploy) {
    $sourceFile = Join-Path $localPath $file
    $targetDir = Join-Path $tempDir (Split-Path $file)
    $targetFile = Join-Path $tempDir $file
    
    # Create directory structure if needed
    if (!(Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
    }
    
    # Copy the file
    Copy-Item -Path $sourceFile -Destination $targetFile -Force
    Write-Host "Prepared $file for deployment" -ForegroundColor Cyan
}

# Create archive
$archiveName = "git-dashboard-deploy.zip"
Compress-Archive -Path "$tempDir/*" -DestinationPath $archiveName -Force
Write-Host "Created deployment archive" -ForegroundColor Cyan

# Transfer archive to server
Write-Host "Transferring files to $remoteHost..." -ForegroundColor Yellow
scp $archiveName "$remoteUser@$remoteHost:/tmp/"

# Execute remote commands to deploy
$remoteCommands = @"
sudo unzip -o /tmp/$archiveName -d /tmp/git-dashboard-deploy
sudo cp -R /tmp/git-dashboard-deploy/* $remotePath/
sudo chown -R www-data:www-data $remotePath
sudo systemctl restart git-dashboard
rm -rf /tmp/git-dashboard-deploy
rm /tmp/$archiveName
echo "Deployment completed successfully!"
"@

Write-Host "Deploying files on server..." -ForegroundColor Yellow
ssh "$remoteUser@$remoteHost" "$remoteCommands"

# Clean up local files
Remove-Item -Path $tempDir -Recurse -Force
Remove-Item -Path $archiveName -Force

Write-Host "Deployment completed!" -ForegroundColor Green
