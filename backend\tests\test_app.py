"""
Test suite for Project Tracker backend application
"""

import pytest
import json
from datetime import datetime
from backend.src.back_app import app, Config
import psycopg2
import redis
from unittest.mock import patch, MagicMock

@pytest.fixture
def client():
    """Create a test client for the Flask app."""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

@pytest.fixture
def mock_db_pool():
    """Mock the database connection pool."""
    with patch('backend.src.back_app.get_db_pool') as mock:
        pool = MagicMock()
        mock.return_value = pool
        yield pool

@pytest.fixture
def mock_redis():
    """Mock the Redis connection."""
    with patch('backend.src.back_app.get_redis') as mock:
        redis_client = MagicMock()
        mock.return_value = redis_client
        yield redis_client

class TestProjects:
    """Test cases for project-related endpoints."""
    
    def test_list_projects(self, client, mock_db_pool):
        """Test listing all projects."""
        # Mock data
        mock_projects = [
            {
                'id': 1,
                'name': 'Test Project',
                'description': 'Test Description',
                'improvements_count': 5,
                'categories_count': 2
            }
        ]
        
        # Set up mock cursor
        mock_cursor = MagicMock()
        mock_cursor.__enter__.return_value.fetchall.return_value = mock_projects
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_db_pool.getconn.return_value = mock_conn
        
        # Make request
        response = client.get('/api/projects')
        data = json.loads(response.data)
        
        # Assertions
        assert response.status_code == 200
        assert len(data) == 1
        assert data[0]['name'] == 'Test Project'
        assert data[0]['improvements_count'] == 5

    def test_create_project(self, client, mock_db_pool):
        """Test project creation."""
        # Mock data
        project_data = {
            'name': 'New Project',
            'description': 'Project Description',
            'repository_url': 'https://github.com/user/repo'
        }
        
        # Set up mock cursor
        mock_cursor = MagicMock()
        mock_cursor.__enter__.return_value.fetchone.return_value = {'id': 1}
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_db_pool.getconn.return_value = mock_conn
        
        # Make request
        response = client.post(
            '/api/projects',
            data=json.dumps(project_data),
            content_type='application/json'
        )
        data = json.loads(response.data)
        
        # Assertions
        assert response.status_code == 200
        assert data['status'] == 'success'
        assert data['project_id'] == 1

class TestCategories:
    """Test cases for category-related endpoints."""
    
    def test_list_categories(self, client, mock_db_pool):
        """Test listing categories for a project."""
        # Mock data
        mock_categories = [
            {
                'id': 1,
                'name': 'Test Category',
                'description': 'Test Description',
                'improvements_count': 3
            }
        ]
        
        # Set up mock cursor
        mock_cursor = MagicMock()
        mock_cursor.__enter__.return_value.fetchall.return_value = mock_categories
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_db_pool.getconn.return_value = mock_conn
        
        # Make request
        response = client.get('/api/projects/1/categories')
        data = json.loads(response.data)
        
        # Assertions
        assert response.status_code == 200
        assert len(data) == 1
        assert data[0]['name'] == 'Test Category'
        assert data[0]['improvements_count'] == 3

class TestCache:
    """Test cases for caching functionality."""
    
    def test_cache_decorator(self, client, mock_db_pool, mock_redis):
        """Test that the cache decorator works correctly."""
        # Mock Redis get/set
        mock_redis.get.return_value = None
        
        # Mock database response
        mock_projects = [{'id': 1, 'name': 'Cached Project'}]
        mock_cursor = MagicMock()
        mock_cursor.__enter__.return_value.fetchall.return_value = mock_projects
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_db_pool.getconn.return_value = mock_conn
        
        # First request - should hit database
        response1 = client.get('/api/projects')
        assert response1.status_code == 200
        
        # Verify Redis set was called
        mock_redis.setex.assert_called_once()
        
        # Mock cached response
        mock_redis.get.return_value = json.dumps(mock_projects)
        
        # Second request - should hit cache
        response2 = client.get('/api/projects')
        assert response2.status_code == 200
        
        # Data should be the same
        assert response1.data == response2.data
