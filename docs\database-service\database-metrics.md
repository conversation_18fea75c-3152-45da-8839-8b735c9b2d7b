# Database Metrics

The Database Service provides specific metrics about database operations that can be accessed via API endpoints. These metrics are focused on database-specific information rather than general system metrics.

## Available Metrics

The following categories of metrics are available:

### Connection Pool Metrics

- **Active Connections**: Number of connections currently in use
- **Idle Connections**: Number of connections in the pool but not currently in use
- **Waiting Connections**: Number of requests waiting for a connection
- **Max Connections**: Maximum number of connections allowed
- **Connection Utilization**: Percentage of connections in use (active / max)
- **Max Connections Reached Count**: Number of times the maximum connection limit was reached

### Query Performance Metrics

For each query type (SELECT, INSERT, UPDATE, DELETE, OTHER):

- **Count**: Number of queries executed
- **Error Count**: Number of queries that resulted in an error
- **Error Rate**: Percentage of queries that resulted in an error
- **Min Time (ms)**: Minimum query execution time in milliseconds
- **Max Time (ms)**: Maximum query execution time in milliseconds
- **Avg Time (ms)**: Average query execution time in milliseconds

### Transaction Metrics

- **Commit Count**: Number of transactions committed
- **Rollback Count**: Number of transactions rolled back
- **Total Count**: Total number of transactions
- **Commit Rate**: Percentage of transactions that were committed
- **Avg Transaction Time (ms)**: Average transaction duration in milliseconds
- **Max Transaction Time (ms)**: Maximum transaction duration in milliseconds

### Authentication Metrics

- **Success Count**: Number of successful authentication attempts
- **Failure Count**: Number of failed authentication attempts
- **Total Count**: Total number of authentication attempts
- **Success Rate**: Percentage of authentication attempts that were successful

## API Endpoints

The following API endpoints are available for accessing metrics:

### Get All Metrics

```
GET /api/database/metrics
```

Returns all available metrics in all categories.

**Authentication Required**: Yes (Admin only)

**Response Example**:

```json
{
  "success": true,
  "data": {
    "connection_pool": {
      "active_connections": 5,
      "idle_connections": 3,
      "waiting_connections": 0,
      "max_connections": 10,
      "connection_utilization": 0.5,
      "max_connections_reached_count": 2
    },
    "query_performance": {
      "SELECT": {
        "count": 1000,
        "error_count": 5,
        "error_rate": 0.005,
        "min_time_ms": 1.2,
        "max_time_ms": 150.5,
        "avg_time_ms": 15.3
      },
      "INSERT": {
        "count": 500,
        "error_count": 2,
        "error_rate": 0.004,
        "min_time_ms": 2.1,
        "max_time_ms": 95.7,
        "avg_time_ms": 12.8
      },
      "UPDATE": {
        "count": 300,
        "error_count": 1,
        "error_rate": 0.003,
        "min_time_ms": 1.8,
        "max_time_ms": 85.2,
        "avg_time_ms": 10.5
      },
      "DELETE": {
        "count": 100,
        "error_count": 0,
        "error_rate": 0.0,
        "min_time_ms": 1.5,
        "max_time_ms": 45.3,
        "avg_time_ms": 8.2
      }
    },
    "transactions": {
      "commit_count": 450,
      "rollback_count": 50,
      "total_count": 500,
      "commit_rate": 0.9,
      "avg_transaction_time_ms": 25.7,
      "max_transaction_time_ms": 350.2
    },
    "authentication": {
      "success_count": 950,
      "failure_count": 50,
      "total_count": 1000,
      "success_rate": 0.95
    },
    "timestamp": 1621234567
  }
}
```

### Get Connection Pool Metrics

```
GET /api/database/metrics/connection-pool
```

Returns only connection pool metrics.

**Authentication Required**: Yes (Admin only)

### Get Query Performance Metrics

```
GET /api/database/metrics/query-performance
```

Returns only query performance metrics.

**Authentication Required**: Yes (Admin only)

## Integration with Dashboard

The Database Service metrics can be integrated with the existing dashboard by configuring the dashboard to call these API endpoints. The metrics are provided in a JSON format that can be easily consumed by dashboard visualization tools.

## Security

Access to metrics endpoints is restricted to users with admin privileges. All metrics endpoints require authentication with a valid JWT token.
