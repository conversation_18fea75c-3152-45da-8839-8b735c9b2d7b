/**
 * Git Commit History Chart Module
 * Handles the visualization of repository commit history
 */
const GitCommitHistoryChart = {
    // Module configuration
    config: {
        chartId: 'commit-history-chart',
        containerId: 'commit-history-container',
        errorId: 'commit-chart-error',
        messageId: 'commit-chart-message',
        daysSelector: 'commit-history-days',
        days: 30,
        colors: {
            background: 'rgba(54, 162, 235, 0.5)',
            border: 'rgba(54, 162, 235, 1)'
        },
        dayOptions: [
            { value: 7, label: '7 Days' },
            { value: 14, label: '14 Days' },
            { value: 30, label: '30 Days' },
            { value: 60, label: '60 Days' },
            { value: 90, label: '90 Days' }
        ]
    },

    // Module state
    state: {
        chart: null
    },

    /**
     * Initialize the chart module
     * @returns {Object} This module for chaining
     */
    init() {
        console.log('GitCommitHistoryChart.init() - Initialization complete');
        return this;
    },

    /**
     * Clean up chart instance
     */
    cleanupChart() {
        if (this.state.chart) {
            this.state.chart.destroy();
            this.state.chart = null;
            console.log('Chart instance cleaned up');
        }
    },

    /**
     * Initialize chart for a repository
     * @param {string} repoName - Repository name
     */
    initChart(repoName) {
        console.log(`initChart called for ${repoName}`);
        this.fetchAndRenderChart(repoName);
    },

    /**
     * Fetch data and render chart
     * @param {string} repoName - Repository name
     */
    async fetchAndRenderChart(repoName) {
        console.log(`fetchAndRenderChart called for ${repoName}`);

        // Get elements
        const container = document.getElementById(this.config.containerId);
        const error = document.getElementById(this.config.errorId);
        const message = document.getElementById(this.config.messageId);

        // Check if container exists
        if (!container) {
            console.error('Chart container not found');
            return;
        }

        // Create days selector if it doesn't exist
        this.createDaysSelector(container, repoName);

        try {
            // Ensure the container is properly styled
            container.style.position = 'relative';
            container.style.height = '400px';
            container.style.display = 'block';

            // Hide error and message elements
            if (error) error.style.display = 'none';
            if (message) message.style.display = 'none';

            // Remove any loading spinners that might exist
            const loadingSpinner = document.getElementById('commit-chart-loading');
            if (loadingSpinner && loadingSpinner.parentNode) {
                loadingSpinner.parentNode.removeChild(loadingSpinner);
            }

            // Clean up existing chart
            this.cleanupChart();

            // Format repo name (strip .git extension)
            const cleanName = repoName.trim().replace(/\.git$/, '');

            // Fetch data from API
            const apiUrl = `/api/git/repository/${encodeURIComponent(cleanName)}/commits?days=${this.config.days}`;
            console.log(`Fetching commit history from ${apiUrl}`);

            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('API response:', data);

            // Check for API error
            if (data.error) {
                throw new Error(data.error);
            }

            // Create or update chart container
            let chartCanvas = document.getElementById(this.config.chartId);
            if (!chartCanvas) {
                console.log('Canvas not found, creating new one');
                chartCanvas = document.createElement('canvas');
                chartCanvas.id = this.config.chartId;
                chartCanvas.style.width = '100%';
                chartCanvas.style.height = '100%';
                chartCanvas.style.display = 'block';
                container.appendChild(chartCanvas);
            } else {
                console.log('Using existing canvas');
                chartCanvas.style.display = 'block';
            }

            let chartData;

            // Check for data array (from C++ service) or commits array (fallback)
            if (data.data && Array.isArray(data.data)) {
                // Use pre-formatted data from C++ service
                console.log('Using pre-formatted data from C++ service');
                chartData = {
                    labels: data.data.map(item => item.displayDate),
                    datasets: [{
                        label: 'Commits',
                        data: data.data.map(item => item.count),
                        backgroundColor: this.config.colors.background,
                        borderColor: this.config.colors.border,
                        borderWidth: 1
                    }]
                };
            } else if (data.commits && Array.isArray(data.commits)) {
                // Format data for chart using raw commits
                console.log('Using raw commits data');
                chartData = this.formatCommitData(data.commits);
            } else {
                throw new Error('Invalid response format: missing data or commits array');
            }

            // Handle empty data
            if (chartData.labels.length === 0 || chartData.datasets[0].data.every(val => val === 0)) {
                console.log('No commit data found');
                if (message) {
                    message.textContent = 'No commits found in the selected time period';
                    message.style.display = 'block';
                }
                return;
            }

            // Create chart
            this.createChart(chartCanvas, chartData);

        } catch (error) {
            console.error('Error fetching/rendering commit history:', error);
            const errorEl = document.getElementById(this.config.errorId);

            if (errorEl) {
                errorEl.textContent = `Failed to load commit history: ${error.message}`;
                errorEl.style.display = 'flex';
            }
        }
    },

    /**
     * Format commit data for chart
     * @param {Array} commits - Array of commit objects
     * @returns {Object} Formatted chart data
     */
    formatCommitData(commits) {
        // Group commits by date
        const commitsByDate = {};
        const today = new Date();

        // Initialize all dates in the last N days with 0 commits
        for (let i = 0; i < this.config.days; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            commitsByDate[dateStr] = 0;
        }

        // Count commits for each date
        commits.forEach(commit => {
            const date = new Date(commit.date);
            const dateStr = date.toISOString().split('T')[0];

            if (commitsByDate[dateStr] !== undefined) {
                commitsByDate[dateStr]++;
            }
        });

        // Convert to arrays for Chart.js
        const labels = [];
        const data = [];

        // Get all dates and sort them chronologically
        const dates = Object.keys(commitsByDate).sort((a, b) => {
            // Sort dates in ascending order (oldest to newest)
            return new Date(a) - new Date(b);
        });

        // Add dates and commit counts to arrays
        dates.forEach(date => {
            // Format date for display (MM-DD)
            const displayDate = new Date(date);
            const formattedDate = `${displayDate.getMonth() + 1}/${displayDate.getDate()}`;

            labels.push(formattedDate);
            data.push(commitsByDate[date]);
        });

        return {
            labels: labels,
            datasets: [{
                label: 'Commits',
                data: data,
                backgroundColor: this.config.colors.background,
                borderColor: this.config.colors.border,
                borderWidth: 1
            }]
        };
    },

    /**
     * Create days selector dropdown
     * @param {HTMLElement} container - Container element
     * @param {string} repoName - Repository name
     */
    createDaysSelector(container, repoName) {
        // Check if selector already exists
        let selector = document.getElementById(this.config.daysSelector);

        if (!selector) {
            // Find the header container for the selector
            const headerContainer = document.getElementById('history-range-container');

            if (headerContainer) {
                // Create selector container for header
                const selectorContainer = document.createElement('div');
                selectorContainer.className = 'd-flex align-items-center';

                // Create label
                const label = document.createElement('label');
                label.htmlFor = this.config.daysSelector;
                label.className = 'me-2 text-muted small';
                label.textContent = 'Date Range:';

                // Create select element
                selector = document.createElement('select');
                selector.id = this.config.daysSelector;
                selector.className = 'form-select form-select-sm';
                selector.style.width = 'auto';

                // Add options
                this.config.dayOptions.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.label;
                    if (option.value === this.config.days) {
                        optionElement.selected = true;
                    }
                    selector.appendChild(optionElement);
                });

                // Add event listener
                selector.addEventListener('change', () => {
                    this.config.days = parseInt(selector.value, 10);
                    this.fetchAndRenderChart(repoName);
                });

                // Add to header container
                selectorContainer.appendChild(label);
                selectorContainer.appendChild(selector);
                headerContainer.appendChild(selectorContainer);
            } else {
                // Fallback to original container if header container not found
                console.warn('History range container not found, using fallback placement');

                // Create selector container
                const selectorContainer = document.createElement('div');
                selectorContainer.className = 'mb-3 d-flex justify-content-end align-items-center';

                // Create label
                const label = document.createElement('label');
                label.htmlFor = this.config.daysSelector;
                label.className = 'me-2 text-muted small';
                label.textContent = 'Date Range:';

                // Create select element
                selector = document.createElement('select');
                selector.id = this.config.daysSelector;
                selector.className = 'form-select form-select-sm';
                selector.style.width = 'auto';

                // Add options
                this.config.dayOptions.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.label;
                    if (option.value === this.config.days) {
                        optionElement.selected = true;
                    }
                    selector.appendChild(optionElement);
                });

                // Add event listener
                selector.addEventListener('change', () => {
                    this.config.days = parseInt(selector.value, 10);
                    this.fetchAndRenderChart(repoName);
                });

                // Add to container
                selectorContainer.appendChild(label);
                selectorContainer.appendChild(selector);

                // Insert at the top of the container
                if (container.firstChild) {
                    container.insertBefore(selectorContainer, container.firstChild);
                } else {
                    container.appendChild(selectorContainer);
                }
            }
        }
    },

    /**
     * Create the chart with the provided data
     * @param {HTMLCanvasElement} canvas - Canvas element
     * @param {Object} chartData - Data for the chart
     */
    createChart(canvas, chartData) {
        console.log('createChart called with canvas and data:', canvas, chartData);

        if (!canvas) {
            console.error('Cannot create chart: canvas element not provided');
            return;
        }

        if (!chartData || !chartData.labels || !chartData.datasets) {
            console.error('Cannot create chart: invalid chart data', chartData);
            return;
        }

        try {
            // Ensure the canvas is visible and properly sized
            canvas.style.display = 'block';
            canvas.style.width = '100%';
            canvas.style.height = '100%';

            // Remove any loading spinners again (double-check)
            const loadingSpinner = document.getElementById('commit-chart-loading');
            if (loadingSpinner && loadingSpinner.parentNode) {
                loadingSpinner.parentNode.removeChild(loadingSpinner);
            }

            // Destroy any existing chart instance
            if (this.state.chart) {
                this.state.chart.destroy();
                this.state.chart = null;
            }

            // Configuration for chart
            const config = {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const count = context.raw;
                                    return `${count} commit${count !== 1 ? 's' : ''}`;
                                }
                            }
                        }
                    }
                }
            };

            // Create chart with a small delay to ensure DOM is ready
            setTimeout(() => {
                try {
                    // Double check that the canvas is still in the DOM
                    if (!document.body.contains(canvas)) {
                        console.error('Canvas is no longer in the DOM');
                        return;
                    }

                    // Remove any loading spinners one final time
                    const finalLoadingSpinner = document.getElementById('commit-chart-loading');
                    if (finalLoadingSpinner && finalLoadingSpinner.parentNode) {
                        finalLoadingSpinner.parentNode.removeChild(finalLoadingSpinner);
                    }

                    // Create chart
                    this.state.chart = new Chart(canvas, config);
                    console.log('Chart created successfully');
                } catch (err) {
                    console.error('Error creating chart in timeout:', err);
                }
            }, 100);
        } catch (error) {
            console.error('Error creating chart:', error);
            throw new Error(`Chart creation failed: ${error.message}`);
        }
    }
};

// Expose the module globally
window.GitCommitHistoryChart = GitCommitHistoryChart;

// Verify module is registered
console.log('GitCommitHistoryChart module registered:', typeof window.GitCommitHistoryChart !== 'undefined');

