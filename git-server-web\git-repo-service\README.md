# Git Repository Service

A C++23 service that provides API endpoints for Git repository information, replacing the JavaScript functionality in the Repositories tab of the Git Dashboard.

## Features

- List all Git repositories with detailed information
- View commit history for specific repositories
- Repository health monitoring
- Branch information
- Size statistics

## API Endpoints

- `/api/git/repositories` - Get all repositories with metadata
- `/api/git/repository/{name}/commits` - Get commit history for a specific repository

## Requirements

- C++23 compatible compiler (GCC 12+ or Clang 15+)
- CMake 3.16+
- Boost libraries
- libgit2
- libcurl
- jsoncpp
- OpenSSL

## Building

```bash
mkdir build
cd build
cmake ..
make
```

## Deployment

Use the provided PowerShell deployment script:

```powershell
.\deploy-git-repo-service.ps1
```

This will:
1. Build the application
2. Copy it to the server
3. Install dependencies
4. Set up the systemd service
5. Configure permissions

## Configuration

The service can be configured through environment variables:

- `GIT_REPO_PATH` - Path to Git repositories (default: `/home/<USER>
- `SERVER_PORT` - HTTP server port (default: 8080)
- `LOG_LEVEL` - Logging level (default: INFO)
- `LOG_FILE` - Log file path (default: `/var/log/git-repo-service.log`)

## Integration with Git Dashboard

This service is designed to replace the JavaScript functionality in the Repositories tab of the Git Dashboard. The API endpoints are compatible with the existing JavaScript code, allowing for a seamless transition.
