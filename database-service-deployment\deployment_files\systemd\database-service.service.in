[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
ExecStart=@CMAKE_INSTALL_PREFIX@/bin/database-service --config @CMAKE_INSTALL_PREFIX@/config/config.json
Restart=on-failure
WorkingDirectory=@CMAKE_INSTALL_PREFIX@
Environment="LD_LIBRARY_PATH=@CMAKE_INSTALL_PREFIX@/lib"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target

