#include "api_server.hpp"
#include <iostream>
#include <sstream>
#include <jsoncpp/json/json.h>

// Handler for GET /api/logs/available
void ApiServer::handleGetAvailableLogs(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Get available logs from the logging service
        std::string logsJson = loggingService_->getAvailableLogs();
        
        // Set response
        res.result(http::status::ok);
        res.set(http::field::content_type, "application/json");
        res.body() = logsJson;
    } catch (const std::exception& e) {
        // Handle error
        std::cerr << "Error handling GET /api/logs/available: " << e.what() << std::endl;
        
        // Create error response
        Json::Value errorResponse;
        errorResponse["success"] = false;
        errorResponse["message"] = e.what();
        
        // Set response
        res.result(http::status::internal_server_error);
        res.set(http::field::content_type, "application/json");
        
        Json::StreamWriterBuilder writer;
        res.body() = Json::writeString(writer, errorResponse);
    }
}

// Handler for POST /api/logs/scan
void ApiServer::handleScanLogs(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Scan for logs
        std::string resultJson = loggingService_->scanForLogs();
        
        // Set response
        res.result(http::status::ok);
        res.set(http::field::content_type, "application/json");
        res.body() = resultJson;
    } catch (const std::exception& e) {
        // Handle error
        std::cerr << "Error handling POST /api/logs/scan: " << e.what() << std::endl;
        
        // Create error response
        Json::Value errorResponse;
        errorResponse["success"] = false;
        errorResponse["message"] = e.what();
        
        // Set response
        res.result(http::status::internal_server_error);
        res.set(http::field::content_type, "application/json");
        
        Json::StreamWriterBuilder writer;
        res.body() = Json::writeString(writer, errorResponse);
    }
}

// Handler for POST /api/logs/configure
void ApiServer::handleConfigureLogs(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    try {
        // Get request body
        std::string requestBody = req.body();
        
        // Configure logs
        std::string resultJson = loggingService_->configureLogs(requestBody);
        
        // Set response
        res.result(http::status::ok);
        res.set(http::field::content_type, "application/json");
        res.body() = resultJson;
    } catch (const std::exception& e) {
        // Handle error
        std::cerr << "Error handling POST /api/logs/configure: " << e.what() << std::endl;
        
        // Create error response
        Json::Value errorResponse;
        errorResponse["success"] = false;
        errorResponse["message"] = e.what();
        
        // Set response
        res.result(http::status::internal_server_error);
        res.set(http::field::content_type, "application/json");
        
        Json::StreamWriterBuilder writer;
        res.body() = Json::writeString(writer, errorResponse);
    }
}
