import React, { useState, useEffect } from 'react';
import { metricsAPI } from '../services/api';
import MetricsCard from '../components/MetricsCard';
import { formatDate } from '../utils/formatters';
import './MetricsPage.css';

const MetricsPage: React.FC = () => {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  
  useEffect(() => {
    const fetchMetrics = async () => {
      setLoading(true);
      setError(null);
      
      try {
        let response;
        
        switch (activeTab) {
          case 'connection-pool':
            response = await metricsAPI.getConnectionPoolMetrics();
            break;
          case 'query-performance':
            response = await metricsAPI.getQueryPerformanceMetrics();
            break;
          case 'all':
          default:
            response = await metricsAPI.getAllMetrics();
            break;
        }
        
        if (response.data.success) {
          setMetrics(response.data.data);
        } else {
          setError(response.data.error?.message || 'Failed to load metrics');
        }
      } catch (err: any) {
        setError(err.response?.data?.error?.message || err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMetrics();
    
    // Refresh metrics every 30 seconds
    const intervalId = setInterval(fetchMetrics, 30000);
    
    return () => clearInterval(intervalId);
  }, [activeTab]);
  
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };
  
  return (
    <div className="metrics-page">
      <div className="container">
        <h1 className="page-title">Database Metrics</h1>
        
        <div className="metrics-tabs">
          <button 
            className={`metrics-tab ${activeTab === 'all' ? 'active' : ''}`}
            onClick={() => handleTabChange('all')}
          >
            All Metrics
          </button>
          <button 
            className={`metrics-tab ${activeTab === 'connection-pool' ? 'active' : ''}`}
            onClick={() => handleTabChange('connection-pool')}
          >
            Connection Pool
          </button>
          <button 
            className={`metrics-tab ${activeTab === 'query-performance' ? 'active' : ''}`}
            onClick={() => handleTabChange('query-performance')}
          >
            Query Performance
          </button>
        </div>
        
        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading metrics data...</p>
          </div>
        ) : error ? (
          <div className="alert alert-danger">{error}</div>
        ) : (
          <div className="metrics-content">
            {activeTab === 'all' && (
              <>
                <div className="metrics-timestamp">
                  Last updated: {formatDate(metrics?.timestamp || Date.now() / 1000)}
                </div>
                
                <div className="metrics-grid">
                  <MetricsCard 
                    title="Connection Pool Metrics" 
                    metrics={metrics?.connection_pool || {}} 
                  />
                  
                  <MetricsCard 
                    title="Transaction Metrics" 
                    metrics={metrics?.transactions || {}} 
                  />
                  
                  <MetricsCard 
                    title="Authentication Metrics" 
                    metrics={metrics?.authentication || {}} 
                  />
                </div>
                
                <h2 className="metrics-section-title">Query Performance Metrics</h2>
                
                <div className="metrics-grid">
                  {Object.entries(metrics?.query_performance || {}).map(([queryType, queryMetrics]) => (
                    <MetricsCard 
                      key={queryType}
                      title={`${queryType} Queries`}
                      metrics={queryMetrics as Record<string, any>}
                    />
                  ))}
                </div>
              </>
            )}
            
            {activeTab === 'connection-pool' && (
              <MetricsCard 
                title="Connection Pool Metrics" 
                metrics={metrics || {}} 
              />
            )}
            
            {activeTab === 'query-performance' && (
              <div className="metrics-grid">
                {Object.entries(metrics || {}).map(([queryType, queryMetrics]) => (
                  <MetricsCard 
                    key={queryType}
                    title={`${queryType} Queries`}
                    metrics={queryMetrics as Record<string, any>}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MetricsPage;
