from flask import Flask, request, jsonify
from datetime import datetime
import os
from pathlib import Path
from monitoring import SystemMonitor, DatabaseMonitor
import psycopg2
from psycopg2.extras import RealDictCursor
import redis
from functools import wraps
import json
from typing import Optional, Dict, Any

app = Flask(__name__)

# Configuration
class Config:
    POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
    POSTGRES_PORT = int(os.getenv('POSTGRES_PORT', '5432'))
    POSTGRES_DB = os.getenv('POSTGRES_DB', 'project_tracker')
    POSTGRES_USER = os.getenv('POSTGRES_USER', 'project_tracker')
    POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', '')
    
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
    REDIS_DB = int(os.getenv('REDIS_DB', '0'))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')
    
    CACHE_TIMEOUT = 300  # 5 minutes
    CACHE_ENABLED = True

# Initialize monitors with PostgreSQL connection
db_path = os.path.join(os.path.dirname(__file__), '..', 'database')
system_monitor = SystemMonitor(Config.POSTGRES_HOST)
db_monitor = DatabaseMonitor(Config.POSTGRES_HOST)

# Database connection pool
def get_db_pool():
    if not hasattr(app, 'db_pool'):
        app.db_pool = psycopg2.pool.SimpleConnectionPool(
            minconn=1,
            maxconn=20,
            host=Config.POSTGRES_HOST,
            port=Config.POSTGRES_PORT,
            database=Config.POSTGRES_DB,
            user=Config.POSTGRES_USER,
            password=Config.POSTGRES_PASSWORD,
            cursor_factory=RealDictCursor
        )
    return app.db_pool

# Redis connection pool
def get_redis_pool():
    if not hasattr(app, 'redis_pool'):
        app.redis_pool = redis.ConnectionPool(
            host=Config.REDIS_HOST,
            port=Config.REDIS_PORT,
            db=Config.REDIS_DB,
            password=Config.REDIS_PASSWORD,
            decode_responses=True
        )
    return app.redis_pool

def get_redis():
    return redis.Redis(connection_pool=get_redis_pool())

def get_db():
    return get_db_pool().getconn()

def return_db(conn):
    get_db_pool().putconn(conn)

# Cache decorator
def cache(timeout: Optional[int] = None):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not Config.CACHE_ENABLED:
                return f(*args, **kwargs)

            cache_key = f"{f.__name__}:{str(args)}:{str(kwargs)}"
            redis_client = get_redis()
            
            # Try to get from cache
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # If not in cache, execute function
            result = f(*args, **kwargs)
            
            # Store in cache
            cache_timeout = timeout or Config.CACHE_TIMEOUT
            redis_client.setex(
                cache_key,
                cache_timeout,
                json.dumps(result)
            )
            
            return result
        return decorated_function
    return decorator

def log_activity(conn, project_id: int, action_type: str, entity_type: str, 
                 entity_id: str, details: Optional[Dict[str, Any]] = None):
    with conn.cursor() as cursor:
        cursor.execute('''
            INSERT INTO project_activity (
                project_id, action_type, entity_type, 
                entity_id, details
            ) VALUES (%s, %s, %s, %s, %s)
        ''', (project_id, action_type, entity_type, entity_id, 
              json.dumps(details) if details else None))

# Project endpoints
@app.route('/api/projects', methods=['GET'])
@cache()
def list_projects():
    try:
        conn = get_db()
        with conn.cursor() as cursor:
            cursor.execute('''
                SELECT 
                    p.*,
                    COUNT(DISTINCT i.id) as improvements_count,
                    COUNT(DISTINCT c.id) as categories_count
                FROM projects p
                LEFT JOIN improvements i ON p.id = i.project_id
                LEFT JOIN categories c ON p.id = c.project_id
                GROUP BY p.id
                ORDER BY p.name
            ''')
            projects = cursor.fetchall()
            return jsonify(projects)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        return_db(conn)

@app.route('/api/projects', methods=['POST'])
def create_project():
    try:
        data = request.json
        conn = get_db()
        with conn.cursor() as cursor:
            cursor.execute('''
                INSERT INTO projects (name, description, repository_url)
                VALUES (%s, %s, %s)
                RETURNING id
            ''', (data['name'], data.get('description'), 
                  data.get('repository_url')))
            
            project_id = cursor.fetchone()['id']
            log_activity(conn, project_id, 'create', 'project', str(project_id))
            
            conn.commit()
            return jsonify({'status': 'success', 'project_id': project_id})
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            return_db(conn)

@app.route('/api/projects/<int:project_id>/categories', methods=['GET'])
@cache()
def list_categories(project_id):
    try:
        conn = get_db()
        with conn.cursor() as cursor:
            cursor.execute('''
                SELECT c.*, COUNT(i.id) as improvements_count
                FROM categories c
                LEFT JOIN improvements i ON c.id = i.category_id
                WHERE c.project_id = %s
                GROUP BY c.id
                ORDER BY c.name
            ''', (project_id,))
            categories = cursor.fetchall()
            return jsonify(categories)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        return_db(conn)

@app.route('/api/projects/<int:project_id>/improvements', methods=['GET'])
@cache(timeout=60)  # Shorter cache for improvements
def list_improvements(project_id):
    try:
        conn = get_db()
        with conn.cursor() as cursor:
            cursor.execute('''
                SELECT 
                    i.*,
                    c.name as category_name,
                    array_agg(json_build_object(
                        'description', s.description,
                        'completed', s.completed
                    )) as subtasks
                FROM improvements i
                LEFT JOIN categories c ON i.category_id = c.id
                LEFT JOIN subtasks s ON i.improvement_id = s.improvement_id 
                    AND i.project_id = s.project_id
                WHERE i.project_id = %s
                GROUP BY i.id, c.name
                ORDER BY i.date DESC
            ''', (project_id,))
            
            improvements = cursor.fetchall()
            return jsonify(improvements)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        return_db(conn)

@app.route('/api/projects/<int:project_id>/improvements', methods=['POST'])
def add_improvement(project_id):
    conn = None
    try:
        data = request.json
        conn = get_db()
        
        with conn.cursor() as cursor:
            # Insert or get category using upsert
            cursor.execute('''
                INSERT INTO categories (project_id, name)
                VALUES (%s, %s)
                ON CONFLICT (project_id, name) DO UPDATE
                SET name = EXCLUDED.name
                RETURNING id
            ''', (project_id, data['category']))
            
            category_id = cursor.fetchone()['id']

            # Insert improvement
            cursor.execute('''
                INSERT INTO improvements (
                    improvement_id, project_id, date, commit_hash,
                    category_id, affected_files, version,
                    rollback_command, description, potential_impacts
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (
                data['improvement_id'],
                project_id,
                data['date'],
                data['commit_hash'],
                category_id,
                data['affected_files'],
                data['version'],
                data['rollback_command'],
                data['description'],
                data['potential_impacts']
            ))
            
            improvement_id = cursor.fetchone()['id']

            # Insert subtasks
            for subtask in data.get('subtasks', []):
                cursor.execute('''
                    INSERT INTO subtasks (
                        improvement_id, project_id, description, completed
                    ) VALUES (%s, %s, %s, %s)
                ''', (
                    improvement_id,
                    project_id,
                    subtask['description'],
                    subtask.get('completed', False)
                ))

            log_activity(conn, project_id, 'create', 'improvement', 
                        str(improvement_id))
            
            # Invalidate cache for affected endpoints
            redis_client = get_redis()
            redis_client.delete(f"list_improvements:{(project_id,)}:{{}}")
            redis_client.delete(f"list_categories:{(project_id,)}:{{}}")
            
            conn.commit()
            return jsonify({
                'status': 'success',
                'message': 'Improvement added successfully',
                'improvement_id': improvement_id
            })
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            return_db(conn)

@app.route('/api/projects/<int:project_id>/improvements/<improvement_id>', methods=['PUT'])
def update_improvement(project_id, improvement_id):
    conn = None
    try:
        data = request.json
        conn = get_db()
        
        with conn.cursor() as cursor:
            # Update category if provided
            if 'category' in data:
                cursor.execute('''
                    INSERT INTO categories (project_id, name)
                    VALUES (%s, %s)
                    ON CONFLICT (project_id, name) DO UPDATE
                    SET name = EXCLUDED.name
                    RETURNING id
                ''', (project_id, data['category']))
                
                category_id = cursor.fetchone()['id']
                data['category_id'] = category_id

            # Update improvement
            update_fields = []
            update_values = []
            for key, value in data.items():
                if key in ['date', 'commit_hash', 'category_id', 'version', 'rollback_command', 'description', 'potential_impacts']:
                    update_fields.append(f"{key} = %s")
                    update_values.append(value)
                elif key == 'affected_files':
                    update_fields.append("affected_files = %s")
                    update_values.append(','.join(value))

            if update_fields:
                update_values.extend([project_id, improvement_id])
                cursor.execute(f'''
                    UPDATE improvements 
                    SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
                    WHERE project_id = %s AND improvement_id = %s
                ''', update_values)

            # Update subtasks if provided
            if 'subtasks' in data:
                cursor.execute('''
                    DELETE FROM subtasks 
                    WHERE project_id = %s AND improvement_id = %s
                ''', (project_id, improvement_id))
                
                for subtask in data['subtasks']:
                    cursor.execute('''
                        INSERT INTO subtasks (
                            improvement_id, project_id, description, completed
                        ) VALUES (%s, %s, %s, %s)
                    ''', (
                        improvement_id,
                        project_id,
                        subtask['description'],
                        subtask.get('completed', False)
                    ))

            log_activity(conn, project_id, 'update', 'improvement', improvement_id)
            
            # Invalidate cache for affected endpoints
            redis_client = get_redis()
            redis_client.delete(f"list_improvements:{(project_id,)}:{{}}")
            redis_client.delete(f"list_categories:{(project_id,)}:{{}}")
            
            conn.commit()
            return jsonify({
                'status': 'success',
                'message': 'Improvement updated successfully'
            })
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            return_db(conn)

@app.route('/api/projects/<int:project_id>/activity', methods=['GET'])
def get_project_activity(project_id):
    try:
        conn = get_db()
        with conn.cursor() as cursor:
            days = request.args.get('days', 30, type=int)
            cursor.execute('''
                SELECT * FROM project_activity
                WHERE project_id = %s
                AND timestamp >= NOW() - INTERVAL '%s days'
                ORDER BY timestamp DESC
            ''', (project_id, days))
            
            activities = cursor.fetchall()
            return jsonify(activities)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        return_db(conn)

@app.route('/api/projects/<int:project_id>/improvements/search', methods=['GET'])
def search_improvements(project_id):
    try:
        query = request.args.get('q', '')
        category = request.args.get('category', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        
        conn = get_db()
        with conn.cursor() as cursor:
            # Build query conditions
            conditions = ['i.project_id = %s']
            params = [project_id]
            
            if query:
                conditions.append('''(
                    i.description ILIKE %s OR 
                    i.affected_files ILIKE %s OR 
                    i.commit_hash ILIKE %s OR
                    i.potential_impacts ILIKE %s
                )''')
                query_param = f'%{query}%'
                params.extend([query_param] * 4)
            
            if category:
                conditions.append('c.name = %s')
                params.append(category)
            
            if date_from:
                conditions.append('i.date >= %s')
                params.append(date_from)
            
            if date_to:
                conditions.append('i.date <= %s')
                params.append(date_to)
            
            where_clause = ' AND '.join(conditions)
            
            cursor.execute(f'''
                SELECT 
                    i.*,
                    c.name as category_name,
                    array_agg(json_build_object(
                        'description', s.description,
                        'completed', s.completed
                    )) as subtasks
                FROM improvements i
                LEFT JOIN categories c ON i.category_id = c.id
                LEFT JOIN subtasks s ON i.improvement_id = s.improvement_id 
                    AND i.project_id = s.project_id
                WHERE {where_clause}
                GROUP BY i.id, c.name
                ORDER BY i.date DESC
            ''', params)
            
            improvements = cursor.fetchall()
            return jsonify(improvements)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        return_db(conn)

@app.route('/api/metrics', methods=['GET'])
def get_metrics():
    try:
        system_metrics = system_monitor.get_system_metrics()
        db_stats = db_monitor.get_db_stats()
        return jsonify({
            'system': system_metrics,
            'database': db_stats
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/backup', methods=['POST'])
def create_backup():
    try:
        success = db_monitor.create_backup()
        if success:
            return jsonify({'status': 'success', 'message': 'Backup created successfully'})
        return jsonify({'status': 'error', 'message': 'Backup failed'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Ensure database connection pool is initialized
    with get_db() as conn:
        conn.close()
    app.run(debug=True)
