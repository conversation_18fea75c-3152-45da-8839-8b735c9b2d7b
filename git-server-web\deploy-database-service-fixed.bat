@echo off
REM Database Service Deployment Script
REM This script deploys the Database Service to the Git server

setlocal enabledelayedexpansion

REM Configuration
set GIT_SERVER=git.chcit.org
set SSH_USER=btaylor-admin
set INSTALL_DIR=/opt/database-service
set CONFIG_DIR=/etc/database-service
set LOG_DIR=/var/log/database-service
set BUILD_DIR=/home/<USER>/database-service-build
set SOURCE_DIR=D:\Augment\project-tracker\database-service

REM Colors for better readability
set RESET=[0m
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set MAGENTA=[95m
set CYAN=[96m

echo %CYAN%Database Service Deployment Script%RESET%
echo =====================================
echo.

REM Check if SSH key exists
if not exist "%USERPROFILE%\.ssh\id_rsa" (
    echo %RED%Error: SSH key not found at %USERPROFILE%\.ssh\id_rsa%RESET%
    echo Please generate an SSH key pair and add the public key to the server.
    exit /b 1
)

:menu
echo.
echo %CYAN%Deployment Options:%RESET%
echo %YELLOW%1.%RESET% Check SSH Connection
echo %YELLOW%2.%RESET% Install Dependencies and Setup Certificate Access
echo %YELLOW%3.%RESET% Comprehensive Server Readiness Check
echo %YELLOW%4.%RESET% Deploy Source Code
echo %YELLOW%5.%RESET% Build Application
echo %YELLOW%6.%RESET% Configure Service
echo %YELLOW%7.%RESET% Install Service
echo %YELLOW%8.%RESET% Start Service
echo %YELLOW%9.%RESET% Full Deployment (Steps 1-8)
echo %YELLOW%10.%RESET% Check Service Status
echo %YELLOW%11.%RESET% View Logs
echo %YELLOW%12.%RESET% Uninstall Service
echo %YELLOW%13.%RESET% Database Management
echo %YELLOW%14.%RESET% Backup Configuration
echo %YELLOW%15.%RESET% Restore Configuration
echo %YELLOW%0.%RESET% Exit
echo.
set /p choice=Enter your choice (0-15):

if "%choice%"=="0" goto :eof
if "%choice%"=="1" goto check_ssh
if "%choice%"=="2" goto install_dependencies
if "%choice%"=="3" goto server_readiness
if "%choice%"=="4" goto deploy_source
if "%choice%"=="5" goto build_app
if "%choice%"=="6" goto configure_service
if "%choice%"=="7" goto install_service
if "%choice%"=="8" goto start_service
if "%choice%"=="9" goto full_deployment
if "%choice%"=="10" goto check_status
if "%choice%"=="11" goto view_logs
if "%choice%"=="12" goto uninstall_service
if "%choice%"=="13" goto database_management
if "%choice%"=="14" goto backup_config
if "%choice%"=="15" goto restore_config

echo %RED%Invalid choice. Please try again.%RESET%
goto menu

:check_ssh
echo.
echo %CYAN%Checking SSH connection to %GIT_SERVER%...%RESET%
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    echo %RED%Failed to connect to %GIT_SERVER%. Please check your SSH configuration.%RESET%
) else (
    echo %GREEN%SSH connection successful.%RESET%
)
goto menu

:install_dependencies
echo.
echo %CYAN%Installing dependencies and setting up certificate access...%RESET%

REM Copy the certificate access setup script to the server
echo Copying certificate access setup script...
scp "%SOURCE_DIR%\scripts\setup-certificate-access.sh" %SSH_USER%@%GIT_SERVER%:/home/<USER>/

REM Install required packages
echo Installing required packages...
ssh %SSH_USER%@%GIT_SERVER% "sudo apt-get update && sudo apt-get install -y build-essential cmake libpq-dev libssl-dev libcurl4-openssl-dev libjsoncpp-dev nlohmann-json3-dev ninja-build"

REM Set up certificate access
echo Setting up certificate access...
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /home/<USER>/setup-certificate-access.sh && /home/<USER>/setup-certificate-access.sh"

echo %GREEN%Dependencies installed and certificate access set up successfully.%RESET%
goto menu

:server_readiness
echo.
echo %CYAN%Performing comprehensive server readiness check...%RESET%

REM Check SSH connection
echo Checking SSH connection...
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    echo %RED%SSH connection failed. Please check your SSH configuration.%RESET%
    goto menu
)

REM Check required packages
echo Checking required packages...
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -E 'build-essential|cmake|libpq-dev|libssl-dev|libcurl4-openssl-dev|libjsoncpp-dev|nlohmann-json3-dev|ninja-build'"

REM Check PostgreSQL
echo Checking PostgreSQL...
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active postgresql"

REM Check certificate access
echo Checking certificate access...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null 2>&1 && echo 'Certificate access OK' || echo 'Certificate access FAILED'"

REM Check disk space
echo Checking disk space...
ssh %SSH_USER%@%GIT_SERVER% "df -h | grep -E '/$|/home'"

REM Check memory
echo Checking memory...
ssh %SSH_USER%@%GIT_SERVER% "free -h"

REM Check CPU
echo Checking CPU...
ssh %SSH_USER%@%GIT_SERVER% "lscpu | grep -E 'Model name|CPU\(s\)'"

echo %GREEN%Server readiness check completed.%RESET%
goto menu

:deploy_source
echo.
echo %CYAN%Deploying source code to %GIT_SERVER%...%RESET%

REM Create build directory
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %BUILD_DIR%"

REM Copy source files
echo Copying source files...
scp -r "%SOURCE_DIR%\src" "%SOURCE_DIR%\include" "%SOURCE_DIR%\CMakeLists.txt" "%SOURCE_DIR%\scripts" %SSH_USER%@%GIT_SERVER%:%BUILD_DIR%/

echo %GREEN%Source code deployed successfully.%RESET%
goto menu

:build_app
echo.
echo %CYAN%Building application on %GIT_SERVER%...%RESET%

REM Create build directory and build the application
ssh %SSH_USER%@%GIT_SERVER% "cd %BUILD_DIR% && mkdir -p build && cd build && cmake -G Ninja .. -DCMAKE_BUILD_TYPE=Release && ninja"

echo %GREEN%Application built successfully.%RESET%
goto menu

:configure_service
echo.
echo %CYAN%Configuring service on %GIT_SERVER%...%RESET%

REM Create configuration directory
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %CONFIG_DIR% %LOG_DIR%"

REM Copy configuration files
echo Copying configuration files...
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/*.json %CONFIG_DIR%/"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR% %LOG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %LOG_DIR%"

echo %GREEN%Service configured successfully.%RESET%
goto menu

:install_service
echo.
echo %CYAN%Installing service on %GIT_SERVER%...%RESET%

REM Create installation directory
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %INSTALL_DIR%/bin %INSTALL_DIR%/lib"

REM Copy binaries and libraries
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/bin/* %INSTALL_DIR%/bin/"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/lib/* %INSTALL_DIR%/lib/ 2>/dev/null || echo No libraries to copy"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R root:database-service %INSTALL_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR% %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR%/bin/*"

REM Install systemd service
echo Creating systemd service...
ssh %SSH_USER%@%GIT_SERVER% "cat > /home/<USER>/database-service.service << 'EOF'
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=%INSTALL_DIR%/bin/database-service --config %CONFIG_DIR%/config.json
Restart=on-failure
WorkingDirectory=%INSTALL_DIR%
Environment=\"LD_LIBRARY_PATH=%INSTALL_DIR%/lib\"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF"

ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/database-service.service /etc/systemd/system/"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

echo %GREEN%Service installed successfully.%RESET%
goto menu

:start_service
echo.
echo %CYAN%Starting service on %GIT_SERVER%...%RESET%

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable database-service && sudo systemctl start database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"

echo %GREEN%Service started successfully.%RESET%
goto menu

:full_deployment
echo.
echo %CYAN%Performing full deployment...%RESET%

REM Check SSH connection
echo %YELLOW%Step 1: Checking SSH connection...%RESET%
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    echo %RED%SSH connection failed. Please check your SSH configuration.%RESET%
    goto menu
)
echo %GREEN%SSH connection successful.%RESET%

REM Install dependencies and set up certificate access
echo %YELLOW%Step 2: Installing dependencies and setting up certificate access...%RESET%
scp "%SOURCE_DIR%\scripts\setup-certificate-access.sh" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo apt-get update && sudo apt-get install -y build-essential cmake libpq-dev libssl-dev libcurl4-openssl-dev libjsoncpp-dev nlohmann-json3-dev ninja-build"
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /home/<USER>/setup-certificate-access.sh && /home/<USER>/setup-certificate-access.sh"
echo %GREEN%Dependencies installed and certificate access set up successfully.%RESET%

REM Perform server readiness check
echo %YELLOW%Step 3: Performing server readiness check...%RESET%
ssh %SSH_USER%@%GIT_SERVER% "sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null 2>&1 && echo 'Certificate access OK' || echo 'Certificate access FAILED'"
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active postgresql"
echo %GREEN%Server readiness check completed.%RESET%

REM Deploy source code
echo %YELLOW%Step 4: Deploying source code...%RESET%
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %BUILD_DIR%"
scp -r "%SOURCE_DIR%\src" "%SOURCE_DIR%\include" "%SOURCE_DIR%\CMakeLists.txt" "%SOURCE_DIR%\scripts" %SSH_USER%@%GIT_SERVER%:%BUILD_DIR%/
echo %GREEN%Source code deployed successfully.%RESET%

REM Build application
echo %YELLOW%Step 5: Building application...%RESET%
ssh %SSH_USER%@%GIT_SERVER% "cd %BUILD_DIR% && mkdir -p build && cd build && cmake -G Ninja .. -DCMAKE_BUILD_TYPE=Release && ninja"
echo %GREEN%Application built successfully.%RESET%

REM Configure service
echo %YELLOW%Step 6: Configuring service...%RESET%
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %CONFIG_DIR% %LOG_DIR%"
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/*.json %CONFIG_DIR%/"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR% %LOG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %LOG_DIR%"
echo %GREEN%Service configured successfully.%RESET%

REM Install service
echo %YELLOW%Step 7: Installing service...%RESET%
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/bin/* %INSTALL_DIR%/bin/"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/lib/* %INSTALL_DIR%/lib/ 2>/dev/null || echo No libraries to copy"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R root:database-service %INSTALL_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR% %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR%/bin/*"
ssh %SSH_USER%@%GIT_SERVER% "cat > /home/<USER>/database-service.service << 'EOF'
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=%INSTALL_DIR%/bin/database-service --config %CONFIG_DIR%/config.json
Restart=on-failure
WorkingDirectory=%INSTALL_DIR%
Environment=\"LD_LIBRARY_PATH=%INSTALL_DIR%/lib\"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/database-service.service /etc/systemd/system/"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"
echo %GREEN%Service installed successfully.%RESET%

REM Start service
echo %YELLOW%Step 8: Starting service...%RESET%
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable database-service && sudo systemctl start database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"
echo %GREEN%Service started successfully.%RESET%

echo %GREEN%Full deployment completed successfully.%RESET%
goto menu

:check_status
echo.
echo %CYAN%Checking service status on %GIT_SERVER%...%RESET%

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"

goto menu

:view_logs
echo.
echo %CYAN%Viewing logs on %GIT_SERVER%...%RESET%

ssh %SSH_USER%@%GIT_SERVER% "sudo journalctl -u database-service -n 50"

goto menu

:uninstall_service
echo.
echo %CYAN%Uninstalling service from %GIT_SERVER%...%RESET%

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl stop database-service && sudo systemctl disable database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo rm /etc/systemd/system/database-service.service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

echo %YELLOW%Do you want to remove the installation directory and configuration? (y/n)%RESET%
set /p remove_files=

if /i "%remove_files%"=="y" (
    ssh %SSH_USER%@%GIT_SERVER% "sudo rm -rf %INSTALL_DIR% %CONFIG_DIR%"
    echo %GREEN%Service and files removed successfully.%RESET%
) else (
    echo %GREEN%Service uninstalled, but files preserved.%RESET%
)

goto menu

:database_management
echo.
echo %CYAN%Database Management Options:%RESET%
echo %YELLOW%1.%RESET% Check PostgreSQL Status
echo %YELLOW%2.%RESET% List Databases
echo %YELLOW%3.%RESET% Create Database
echo %YELLOW%4.%RESET% Drop Database
echo %YELLOW%5.%RESET% List Users
echo %YELLOW%6.%RESET% Create User
echo %YELLOW%7.%RESET% Drop User
echo %YELLOW%8.%RESET% Back to Main Menu
echo.
set /p db_choice=Enter your choice (1-8):

if "%db_choice%"=="1" (
    echo.
    echo %CYAN%Checking PostgreSQL status...%RESET%
    ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status postgresql"
    goto database_management
)

if "%db_choice%"=="2" (
    echo.
    echo %CYAN%Listing databases...%RESET%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\l'"
    goto database_management
)

if "%db_choice%"=="3" (
    echo.
    set /p db_name=Enter database name:
    echo %CYAN%Creating database %db_name%...%RESET%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'CREATE DATABASE %db_name%;'"
    goto database_management
)

if "%db_choice%"=="4" (
    echo.
    set /p db_name=Enter database name to drop:
    echo %RED%WARNING: This will permanently delete the database %db_name% and all its data!%RESET%
    set /p confirm=Are you sure? (y/n): 
    if /i "%confirm%"=="y" (
        echo %CYAN%Dropping database %db_name%...%RESET%
        ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP DATABASE %db_name%;'"
    )
    goto database_management
)

if "%db_choice%"=="5" (
    echo.
    echo %CYAN%Listing users...%RESET%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\du'"
    goto database_management
)

if "%db_choice%"=="6" (
    echo.
    set /p user_name=Enter user name:
    set /p user_pass=Enter password:
    echo %CYAN%Creating user %user_name%...%RESET%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c \"CREATE USER %user_name% WITH PASSWORD '%user_pass%';\""
    goto database_management
)

if "%db_choice%"=="7" (
    echo.
    set /p user_name=Enter user name to drop:
    echo %RED%WARNING: This will permanently delete the user %user_name%!%RESET%
    set /p confirm=Are you sure? (y/n):
    if /i "%confirm%"=="y" (
        echo %CYAN%Dropping user %user_name%...%RESET%
        ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP USER %user_name%;'"
    )
    goto database_management
)

if "%db_choice%"=="8" goto menu

echo %RED%Invalid choice. Please try again.%RESET%
goto database_management

:backup_config
echo.
echo %CYAN%Backing up configuration from %GIT_SERVER%...%RESET%

set timestamp=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set backup_dir=backups\%timestamp%

mkdir "%SOURCE_DIR%\%backup_dir%" 2>nul

ssh %SSH_USER%@%GIT_SERVER% "sudo tar -czf /home/<USER>/database-config-backup.tar.gz %CONFIG_DIR%"
scp %SSH_USER%@%GIT_SERVER%:/home/<USER>/database-config-backup.tar.gz "%SOURCE_DIR%\%backup_dir%\"
ssh %SSH_USER%@%GIT_SERVER% "rm /home/<USER>/database-config-backup.tar.gz"

echo %GREEN%Configuration backed up to %SOURCE_DIR%\%backup_dir%\database-config-backup.tar.gz%RESET%
goto menu

:restore_config
echo.
echo %CYAN%Available backups:%RESET%
dir /b /ad "%SOURCE_DIR%\backups"

echo.
set /p backup_timestamp=Enter backup timestamp to restore:

if not exist "%SOURCE_DIR%\backups\%backup_timestamp%\database-config-backup.tar.gz" (
    echo %RED%Backup not found.%RESET%
    goto menu
)

echo %CYAN%Restoring configuration to %GIT_SERVER%...%RESET%

scp "%SOURCE_DIR%\backups\%backup_timestamp%\database-config-backup.tar.gz" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo tar -xzf /home/<USER>/database-config-backup.tar.gz -C /"
ssh %SSH_USER%@%GIT_SERVER% "rm /home/<USER>/database-config-backup.tar.gz"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"

echo %GREEN%Configuration restored successfully.%RESET%
goto menu
