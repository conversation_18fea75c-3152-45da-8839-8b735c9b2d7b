import axios, { AxiosRequestConfig } from 'axios';

// Define the base URL for the API
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // If the error is 401 and we haven't already tried to refresh the token
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Try to refresh the token
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          // No refresh token, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }
        
        const response = await axios.post(`${API_URL}/auth/refresh`, {
          refresh_token: refreshToken
        });
        
        if (response.data.success) {
          // Save the new tokens
          localStorage.setItem('access_token', response.data.data.access_token);
          localStorage.setItem('refresh_token', response.data.data.refresh_token);
          
          // Update the authorization header
          originalRequest.headers['Authorization'] = `Bearer ${response.data.data.access_token}`;
          
          // Retry the original request
          return api(originalRequest);
        } else {
          // Refresh failed, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: (username: string, password: string) => {
    return api.post('/auth/login', { username, password });
  },
  
  logout: () => {
    return api.post('/auth/logout');
  },
  
  getUserInfo: () => {
    return api.get('/auth/user');
  }
};

// Database Metrics API
export const metricsAPI = {
  getAllMetrics: () => {
    return api.get('/database/metrics');
  },
  
  getConnectionPoolMetrics: () => {
    return api.get('/database/metrics/connection-pool');
  },
  
  getQueryPerformanceMetrics: () => {
    return api.get('/database/metrics/query-performance');
  }
};

// Credential Management API
export const credentialsAPI = {
  storeCredential: (key: string, value: string) => {
    return api.post('/credentials/store', { key, value });
  },
  
  getCredential: (key: string) => {
    return api.get(`/credentials/get?key=${key}`);
  },
  
  removeCredential: (key: string) => {
    return api.delete(`/credentials/remove?key=${key}`);
  }
};

// Database Operations API
export const databaseAPI = {
  executeQuery: (query: string, params: any[] = []) => {
    return api.post('/query', { query, params });
  },
  
  executeNonQuery: (statement: string, params: any[] = []) => {
    return api.post('/execute', { statement, params });
  }
};

export default api;
