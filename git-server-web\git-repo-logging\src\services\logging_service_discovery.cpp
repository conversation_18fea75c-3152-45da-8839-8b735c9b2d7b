#include "logging_service.hpp"
#include <iostream>
#include <jsoncpp/json/json.h>

// Get available logs
std::string LoggingService::getAvailableLogs() {
    try {
        // Get discovered logs from the log discovery service
        auto logs = logDiscoveryService_->getDiscoveredLogs();
        
        // Create JSON response
        Json::Value response;
        response["success"] = true;
        
        // Add logs to response
        for (const auto& log : logs) {
            Json::Value logJson;
            logJson["path"] = log.path;
            logJson["name"] = log.name;
            logJson["category"] = log.category;
            logJson["parser"] = log.parser;
            logJson["binary"] = log.binary;
            logJson["enabled"] = log.enabled;
            logJson["size"] = static_cast<Json::UInt64>(log.size);
            
            // Convert last modified time to string
            auto lastModifiedTime = std::chrono::file_clock::to_sys(log.lastModified);
            auto lastModifiedTimeT = std::chrono::system_clock::to_time_t(lastModifiedTime);
            std::string lastModifiedStr = std::ctime(&lastModifiedTimeT);
            // Remove trailing newline
            if (!lastModifiedStr.empty() && lastModifiedStr.back() == '\n') {
                lastModifiedStr.pop_back();
            }
            logJson["lastModified"] = lastModifiedStr;
            
            response["logs"].append(logJson);
        }
        
        // Convert to string
        Json::StreamWriterBuilder writer;
        return Json::writeString(writer, response);
    } catch (const std::exception& e) {
        std::cerr << "Error getting available logs: " << e.what() << std::endl;
        
        // Create error response
        Json::Value errorResponse;
        errorResponse["success"] = false;
        errorResponse["message"] = std::string("Error getting available logs: ") + e.what();
        
        // Convert to string
        Json::StreamWriterBuilder writer;
        return Json::writeString(writer, errorResponse);
    }
}

// Scan for logs
std::string LoggingService::scanForLogs() {
    try {
        // Scan for logs
        bool success = logDiscoveryService_->scanLogSources();
        
        if (!success) {
            throw std::runtime_error("Failed to scan log sources");
        }
        
        // Get discovered logs
        auto logs = logDiscoveryService_->getDiscoveredLogs();
        
        // Create JSON response
        Json::Value response;
        response["success"] = true;
        response["message"] = "Log scan completed successfully";
        response["count"] = static_cast<Json::UInt>(logs.size());
        
        // Add logs to response
        for (const auto& log : logs) {
            Json::Value logJson;
            logJson["path"] = log.path;
            logJson["name"] = log.name;
            logJson["category"] = log.category;
            logJson["parser"] = log.parser;
            logJson["binary"] = log.binary;
            logJson["enabled"] = log.enabled;
            logJson["size"] = static_cast<Json::UInt64>(log.size);
            
            // Convert last modified time to string
            auto lastModifiedTime = std::chrono::file_clock::to_sys(log.lastModified);
            auto lastModifiedTimeT = std::chrono::system_clock::to_time_t(lastModifiedTime);
            std::string lastModifiedStr = std::ctime(&lastModifiedTimeT);
            // Remove trailing newline
            if (!lastModifiedStr.empty() && lastModifiedStr.back() == '\n') {
                lastModifiedStr.pop_back();
            }
            logJson["lastModified"] = lastModifiedStr;
            
            response["logs"].append(logJson);
        }
        
        // Convert to string
        Json::StreamWriterBuilder writer;
        return Json::writeString(writer, response);
    } catch (const std::exception& e) {
        std::cerr << "Error scanning for logs: " << e.what() << std::endl;
        
        // Create error response
        Json::Value errorResponse;
        errorResponse["success"] = false;
        errorResponse["message"] = std::string("Error scanning for logs: ") + e.what();
        
        // Convert to string
        Json::StreamWriterBuilder writer;
        return Json::writeString(writer, errorResponse);
    }
}

// Configure logs
std::string LoggingService::configureLogs(const std::string& configJson) {
    try {
        // Parse JSON
        Json::Value config;
        Json::CharReaderBuilder reader;
        std::istringstream configStream(configJson);
        std::string parseErrors;
        
        if (!Json::parseFromStream(reader, configStream, &config, &parseErrors)) {
            throw std::runtime_error("Failed to parse configuration JSON: " + parseErrors);
        }
        
        // Check if logs array exists
        if (!config.isMember("logs") || !config["logs"].isArray()) {
            throw std::runtime_error("Invalid configuration: 'logs' array is missing");
        }
        
        // Update log settings
        const Json::Value& logs = config["logs"];
        for (const auto& logConfig : logs) {
            // Check if path and enabled fields exist
            if (!logConfig.isMember("path") || !logConfig.isMember("enabled")) {
                continue;
            }
            
            // Get path and enabled status
            std::string path = logConfig["path"].asString();
            bool enabled = logConfig["enabled"].asBool();
            
            // Update log enabled status
            logDiscoveryService_->setLogEnabled(path, enabled);
        }
        
        // Save configuration
        const char* configPathEnv = std::getenv("CONFIG_PATH");
        std::string configPath = configPathEnv ? configPathEnv : "/etc/git-dashboard/logging-config.json";
        
        bool saveSuccess = logDiscoveryService_->saveConfiguration(configPath);
        
        if (!saveSuccess) {
            throw std::runtime_error("Failed to save configuration");
        }
        
        // Create JSON response
        Json::Value response;
        response["success"] = true;
        response["message"] = "Log configuration updated successfully";
        
        // Convert to string
        Json::StreamWriterBuilder writer;
        return Json::writeString(writer, response);
    } catch (const std::exception& e) {
        std::cerr << "Error configuring logs: " << e.what() << std::endl;
        
        // Create error response
        Json::Value errorResponse;
        errorResponse["success"] = false;
        errorResponse["message"] = std::string("Error configuring logs: ") + e.what();
        
        // Convert to string
        Json::StreamWriterBuilder writer;
        return Json::writeString(writer, errorResponse);
    }
}
