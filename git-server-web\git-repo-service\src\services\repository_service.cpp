#include "repository_service.hpp"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iostream>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <regex>
#include <random>
#include <jsoncpp/json/json.h>
#include <git2.h>

RepositoryService::RepositoryService(const std::string& basePath) : basePath_(basePath) {
    // Initialize libgit2
    git_libgit2_init();
}

std::string RepositoryService::getAllRepositories() {
    Json::Value root;
    root["repositories"] = Json::Value(Json::arrayValue);

    try {
        // Get current timestamp
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::gmtime(&now_time_t), "%Y-%m-%dT%H:%M:%SZ");
        root["timestamp"] = ss.str();

        std::cout << "Scanning directory: " << basePath_ << std::endl;

        // Check if directory exists
        if (!std::filesystem::exists(basePath_)) {
            std::cerr << "Repository base path does not exist: " << basePath_ << std::endl;
            root["error"] = "Repository base path does not exist";
            root["path"] = basePath_;
            Json::StreamWriterBuilder writer;
            return Json::writeString(writer, root);
        }

        // Count repositories
        int repoCount = 0;

        // Scan directory for repositories
        for (const auto& entry : std::filesystem::directory_iterator(basePath_)) {
            std::cout << "Found directory: " << entry.path().string() << std::endl;

            if (std::filesystem::is_directory(entry)) {
                bool isRepo = false;
                std::string repoPath = entry.path().string();

                // Check if it's a standard repository with a .git directory
                std::string gitDirPath = repoPath + "/.git";
                std::cout << "Checking for .git directory: " << gitDirPath << std::endl;
                if (std::filesystem::exists(gitDirPath)) {
                    std::cout << "Found standard Git repository: " << repoPath << std::endl;
                    isRepo = true;
                }
                // Check if it's a bare repository (ends with .git)
                else if (repoPath.size() > 4 && repoPath.substr(repoPath.size() - 4) == ".git") {
                    std::cout << "Found bare Git repository: " << repoPath << std::endl;
                    isRepo = true;
                }

                if (isRepo) {
                    repoCount++;

                    Repository repo = scanRepository(repoPath);

                    // Add to JSON response
                    Json::Value repoJson;
                    repoJson["name"] = repo.name;
                    repoJson["path"] = repo.path;
                    repoJson["size"] = static_cast<Json::UInt64>(repo.size);
                    repoJson["size_formatted"] = formatSize(repo.size);
                    repoJson["last_commit"] = repo.lastCommitDate;
                    repoJson["last_commit_hash"] = repo.lastCommitHash;
                    repoJson["branches"] = static_cast<Json::UInt>(repo.branchCount);
                    repoJson["health_score"] = repo.healthScore;
                    repoJson["health_status"] = repo.healthStatus;
                    repoJson["health_message"] = repo.healthMessage;

                    // Add branch list
                    Json::Value branchesJson(Json::arrayValue);
                    for (const auto& branch : repo.branches) {
                        branchesJson.append(branch);
                    }
                    repoJson["branch_list"] = branchesJson;

                    root["repositories"].append(repoJson);
                }
            }
        }

        std::cout << "Found " << repoCount << " repositories" << std::endl;
        root["count"] = repoCount;

    } catch (const std::exception& e) {
        std::cerr << "Error scanning repositories: " << e.what() << std::endl;
        root["error"] = e.what();
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    std::string jsonStr = Json::writeString(writer, root);
    std::cout << "JSON response: " << jsonStr << std::endl;
    return jsonStr;
}

std::string RepositoryService::getCommitHistory(const std::string& repoName, int days) {
    Json::Value root;
    root["repository"] = repoName;
    root["days"] = days;
    root["commits"] = Json::Value(Json::arrayValue);
    root["data"] = Json::Value(Json::arrayValue);

    try {
        // Find the repository
        std::string repoPath;
        bool found = false;

        std::cout << "Searching for repository: " << repoName << " in " << basePath_ << std::endl;

        for (const auto& entry : std::filesystem::directory_iterator(basePath_)) {
            if (std::filesystem::is_directory(entry)) {
                std::string entryName = entry.path().filename().string();
                std::string entryPath = entry.path().string();

                // Standard repository check
                if (std::filesystem::exists(entry.path() / ".git") && entryName == repoName) {
                    repoPath = entryPath;
                    found = true;
                    std::cout << "Found standard repository: " << entryPath << std::endl;
                    break;
                }
                // Bare repository with same name (with .git extension)
                else if (entryName == repoName && entryName.size() > 4 &&
                         entryName.substr(entryName.size() - 4) == ".git") {
                    repoPath = entryPath;
                    found = true;
                    std::cout << "Found bare repository with exact name: " << entryPath << std::endl;
                    break;
                }
                // Bare repository with base name (plus .git extension)
                else if (entryName == (repoName + ".git")) {
                    repoPath = entryPath;
                    found = true;
                    std::cout << "Found bare repository with name + .git: " << entryPath << std::endl;
                    break;
                }
            }
        }

        if (!found) {
            std::cerr << "Repository not found: " << repoName << std::endl;
            root["error"] = "Repository not found";
            Json::StreamWriterBuilder writer;
            return Json::writeString(writer, root);
        }

        std::cout << "Using repository path: " << repoPath << std::endl;

        // Open the repository
        git_repository* repo = nullptr;
        if (git_repository_open(&repo, repoPath.c_str()) != 0) {
            const git_error* e = giterr_last();
            std::string error = e ? e->message : "Unknown error";
            root["error"] = "Failed to open repository: " + error;
            Json::StreamWriterBuilder writer;
            return Json::writeString(writer, root);
        }

        // Create a commit walker
        git_revwalk* walker = nullptr;
        if (git_revwalk_new(&walker, repo) != 0) {
            git_repository_free(repo);
            root["error"] = "Failed to create revision walker";
            Json::StreamWriterBuilder writer;
            return Json::writeString(writer, root);
        }

        // Configure the walker to sort by time (newest first)
        git_revwalk_sorting(walker, GIT_SORT_TIME);

        // Start from HEAD
        if (git_revwalk_push_head(walker) != 0) {
            git_revwalk_free(walker);
            git_repository_free(repo);
            root["error"] = "Failed to push HEAD to revision walker";
            Json::StreamWriterBuilder writer;
            return Json::writeString(writer, root);
        }

        // Calculate cutoff date
        auto now = std::chrono::system_clock::now();
        auto cutoff = now - std::chrono::hours(24 * days);
        auto cutoff_time_t = std::chrono::system_clock::to_time_t(cutoff);

        // Create a map to store commits by date
        std::map<std::string, int> commitsByDate;

        // Initialize all dates in the range with 0 commits
        for (int i = 0; i < days; i++) {
            auto date = now - std::chrono::hours(24 * i);
            auto time_t = std::chrono::system_clock::to_time_t(date);
            char date_str[11]; // YYYY-MM-DD + null terminator
            std::tm* tm_info = std::gmtime(&time_t);
            std::strftime(date_str, sizeof(date_str), "%Y-%m-%d", tm_info);
            commitsByDate[std::string(date_str)] = 0;
        }

        // Walk commits
        git_oid oid;
        int count = 0;
        const int MAX_COMMITS = 1000; // Increased limit for better history

        while (git_revwalk_next(&oid, walker) == 0 && count < MAX_COMMITS) {
            git_commit* commit = nullptr;
            if (git_commit_lookup(&commit, repo, &oid) != 0) {
                continue;
            }

            // Get commit time
            git_time_t commit_time_t = git_commit_time(commit);
            if (commit_time_t < cutoff_time_t) {
                git_commit_free(commit);
                break; // Stop if we've reached commits older than our cutoff
            }

            // Format date string (YYYY-MM-DD)
            char date_str[11]; // YYYY-MM-DD + null terminator
            std::tm* tm_info = std::gmtime(&commit_time_t);
            std::strftime(date_str, sizeof(date_str), "%Y-%m-%d", tm_info);

            // Increment commit count for this date
            commitsByDate[std::string(date_str)]++;

            // Format commit data
            Json::Value commitJson;

            // Get commit hash
            char hash[GIT_OID_HEXSZ + 1] = {0};
            git_oid_fmt(hash, &oid);
            commitJson["hash"] = std::string(hash);

            // Get commit time
            char time_str[32];
            std::strftime(time_str, sizeof(time_str), "%Y-%m-%dT%H:%M:%SZ", tm_info);
            commitJson["date"] = std::string(time_str);

            // Get commit message
            const char* message = git_commit_message(commit);
            commitJson["message"] = message ? std::string(message) : "";

            // Get author
            const git_signature* author = git_commit_author(commit);
            if (author) {
                commitJson["author"] = author->name ? std::string(author->name) : "";
                commitJson["email"] = author->email ? std::string(author->email) : "";
            }

            root["commits"].append(commitJson);
            git_commit_free(commit);
            count++;
        }

        // Add commit count
        root["count"] = count;

        // Format data for chart display - sort by date (oldest to newest)
        for (const auto& [date, commitCount] : commitsByDate) {
            // Format display date (MM/DD)
            std::tm tm = {};
            std::istringstream ss(date);
            ss >> std::get_time(&tm, "%Y-%m-%d");

            char display_date[6]; // MM/DD + null terminator
            std::strftime(display_date, sizeof(display_date), "%m/%d", &tm);

            Json::Value dateData;
            dateData["date"] = date;
            dateData["displayDate"] = std::string(display_date);
            dateData["count"] = commitCount;

            root["data"].append(dateData);
        }

        // Clean up
        git_revwalk_free(walker);
        git_repository_free(repo);

    } catch (const std::exception& e) {
        std::cerr << "Error getting commit history: " << e.what() << std::endl;
        root["error"] = e.what();
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

std::string RepositoryService::getRepositoryDetails(const std::string& repoName) {
    Json::Value root;
    root["name"] = repoName;

    try {
        // Find the repository
        std::string repoPath;
        bool found = false;

        for (const auto& entry : std::filesystem::directory_iterator(basePath_)) {
            if (std::filesystem::is_directory(entry) &&
                std::filesystem::exists(entry.path() / ".git") &&
                entry.path().filename() == repoName) {
                repoPath = entry.path().string();
                found = true;
                break;
            }
        }

        if (!found) {
            root["error"] = "Repository not found";
            Json::StreamWriterBuilder writer;
            return Json::writeString(writer, root);
        }

        Repository repo = scanRepository(repoPath);

        // Add to JSON response
        root["path"] = repo.path;
        root["size"] = static_cast<Json::UInt64>(repo.size);
        root["size_formatted"] = formatSize(repo.size);
        root["last_commit"] = repo.lastCommitDate;
        root["last_commit_hash"] = repo.lastCommitHash;
        root["branches"] = static_cast<Json::UInt>(repo.branchCount);
        root["health_score"] = repo.healthScore;
        root["health_status"] = repo.healthStatus;
        root["health_message"] = repo.healthMessage;

        // Add branch list
        Json::Value branchesJson(Json::arrayValue);
        for (const auto& branch : repo.branches) {
            branchesJson.append(branch);
        }
        root["branch_list"] = branchesJson;

    } catch (const std::exception& e) {
        std::cerr << "Error getting repository details: " << e.what() << std::endl;
        root["error"] = e.what();
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

Repository RepositoryService::scanRepository(const std::string& path) {
    Repository repo;
    repo.name = std::filesystem::path(path).filename().string();
    repo.path = path;

    // Calculate size
    repo.size = calculateDirectorySize(path);

    // Get Git information
    git_repository* git_repo = nullptr;
    if (git_repository_open(&git_repo, path.c_str()) == 0) {
        // Get last commit
        git_commit* commit = nullptr;
        git_object* obj = nullptr;

        if (git_revparse_single(&obj, git_repo, "HEAD") == 0) {
            if (git_commit_lookup(&commit, git_repo, git_object_id(obj)) == 0) {
                // Get commit hash
                const git_oid* oid = git_commit_id(commit);
                char hash[GIT_OID_HEXSZ + 1] = {0};
                git_oid_fmt(hash, oid);
                repo.lastCommitHash = std::string(hash);

                // Get commit time
                git_time_t commit_time_t = git_commit_time(commit);
                char time_str[32];
                struct tm* tm_info = gmtime(&commit_time_t);
                strftime(time_str, sizeof(time_str), "%Y-%m-%dT%H:%M:%SZ", tm_info);
                repo.lastCommitDate = std::string(time_str);

                git_commit_free(commit);
            }
            git_object_free(obj);
        }

        // Get branches
        repo.branches = getBranches(git_repo);
        repo.branchCount = repo.branches.size();

        git_repository_free(git_repo);
    } else {
        // If we can't open the repository, set defaults
        repo.lastCommitHash = "";
        repo.lastCommitDate = "";
        repo.branchCount = 0;
    }

    // Calculate health
    repo.healthScore = calculateHealth(repo);
    auto [status, message] = getHealthStatus(repo.healthScore);
    repo.healthStatus = status;
    repo.healthMessage = message;

    return repo;
}

uint64_t RepositoryService::calculateDirectorySize(const std::string& path) {
    uint64_t size = 0;

    try {
        for (const auto& entry : std::filesystem::recursive_directory_iterator(path)) {
            if (std::filesystem::is_regular_file(entry)) {
                size += std::filesystem::file_size(entry);
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error calculating directory size: " << e.what() << std::endl;
    }

    return size;
}

std::vector<std::string> RepositoryService::getBranches(const std::string& path) {
    std::vector<std::string> branches;

    git_repository* repo = nullptr;
    if (git_repository_open(&repo, path.c_str()) != 0) {
        return branches;
    }

    git_branch_iterator* iter = nullptr;
    if (git_branch_iterator_new(&iter, repo, GIT_BRANCH_LOCAL) != 0) {
        git_repository_free(repo);
        return branches;
    }

    git_reference* ref = nullptr;
    git_branch_t type;

    while (git_branch_next(&ref, &type, iter) == 0) {
        const char* branch_name = nullptr;
        if (git_branch_name(&branch_name, ref) == 0) {
            branches.push_back(branch_name);
        }
        git_reference_free(ref);
    }

    git_branch_iterator_free(iter);
    git_repository_free(repo);

    return branches;
}

std::vector<std::string> RepositoryService::getBranches(git_repository* repo) {
    std::vector<std::string> branches;

    git_branch_iterator* iter = nullptr;
    if (git_branch_iterator_new(&iter, repo, GIT_BRANCH_LOCAL) != 0) {
        return branches;
    }

    git_reference* ref = nullptr;
    git_branch_t type;

    while (git_branch_next(&ref, &type, iter) == 0) {
        const char* branch_name = nullptr;
        if (git_branch_name(&branch_name, ref) == 0) {
            branches.push_back(branch_name);
        }
        git_reference_free(ref);
    }

    git_branch_iterator_free(iter);

    return branches;
}

double RepositoryService::calculateHealth(const Repository& repo) {
    double health = 100.0;

    // If we couldn't get commit info, penalize heavily
    if (repo.lastCommitDate.empty()) {
        return 0.0;
    }

    // Calculate based on last commit date
    std::tm tm = {};
    std::istringstream ss(repo.lastCommitDate);
    ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%SZ");

    auto lastCommitTime = std::chrono::system_clock::from_time_t(std::mktime(&tm));
    auto now = std::chrono::system_clock::now();

    auto daysSinceLastCommit = std::chrono::duration_cast<std::chrono::hours>(
        now - lastCommitTime).count() / 24;

    // Deduct points for old commits
    if (daysSinceLastCommit > 365) {  // 1 year
        health -= 70;
    } else if (daysSinceLastCommit > 180) {  // 6 months
        health -= 50;
    } else if (daysSinceLastCommit > 90) {  // 3 months
        health -= 25;
    } else if (daysSinceLastCommit > 30) {  // 1 month
        health -= 10;
    }

    // Deduct points for very large repositories
    if (repo.size > 1024 * 1024 * 1024) {  // 1 GB
        health -= 10;
    }

    // Ensure health is between 0 and 100
    return std::max(0.0, std::min(100.0, health));
}

std::pair<std::string, std::string> RepositoryService::getHealthStatus(double health) {
    if (health >= 80) {
        return {"good", "Repository is healthy"};
    } else if (health >= 50) {
        return {"warning", "Repository needs attention"};
    } else {
        return {"critical", "Repository requires immediate attention"};
    }
}

std::string RepositoryService::formatSize(uint64_t sizeInBytes) {
    const double KB = 1024.0;
    const double MB = 1024.0 * KB;
    const double GB = 1024.0 * MB;
    const double TB = 1024.0 * GB;

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2);

    if (sizeInBytes < KB) {
        oss << sizeInBytes << " B";
    } else if (sizeInBytes < MB) {
        oss << (sizeInBytes / KB) << " KB";
    } else if (sizeInBytes < GB) {
        oss << (sizeInBytes / MB) << " MB";
    } else if (sizeInBytes < TB) {
        oss << (sizeInBytes / GB) << " GB";
    } else {
        oss << (sizeInBytes / TB) << " TB";
    }

    return oss.str();
}

uint64_t RepositoryService::parseSize(const std::string& sizeStr) {
    std::regex pattern("([0-9.]+)\\s*([KMGT]?B)", std::regex::icase);
    std::smatch matches;

    if (std::regex_search(sizeStr, matches, pattern) && matches.size() > 2) {
        double size = std::stod(matches[1].str());
        std::string unit = matches[2].str();

        // Convert to bytes based on unit
        if (unit == "KB" || unit == "kb") {
            return static_cast<uint64_t>(size * 1024);
        } else if (unit == "MB" || unit == "mb") {
            return static_cast<uint64_t>(size * 1024 * 1024);
        } else if (unit == "GB" || unit == "gb") {
            return static_cast<uint64_t>(size * 1024 * 1024 * 1024);
        } else if (unit == "TB" || unit == "tb") {
            return static_cast<uint64_t>(size * 1024 * 1024 * 1024 * 1024);
        }
    }

    // Default to the original string as bytes
    try {
        return std::stoull(sizeStr);
    } catch (...) {
        return 0;
    }
}
