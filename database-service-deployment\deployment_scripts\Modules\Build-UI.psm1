
function Invoke-BuildUI {
    [CmdletBinding()]
    param (
        [string]$ProjectRoot = "D:\Augment\project-tracker\database-service-ui"
    )

    Write-Host "[Build-UI] Starting UI build in $ProjectRoot" -ForegroundColor Cyan

    if (-Not (Test-Path $ProjectRoot)) {
        Write-Host "[Build-UI] UI project directory not found: $ProjectRoot" -ForegroundColor Red
        Read-Host "Press Enter to continue..." | Out-Null
        return $null # Indicate failure
    }

    Push-Location $ProjectRoot
    try {
        if (-Not (Test-Path "package.json")) {
            Write-Host "[Build-UI] package.json not found in $ProjectRoot" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null # Indicate failure
        }

        Write-Host "[Build-UI] Running 'npm install'..." -ForegroundColor Yellow
        npm install 2>&1 | Write-Host # Consider capturing output for better error checking
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[Build-UI] 'npm install' failed." -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null # Indicate failure
        }

        Write-Host "[Build-UI] Running 'npm run build'..." -ForegroundColor Yellow
        npm run build 2>&1 | Write-Host # Consider capturing output for better error checking
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[Build-UI] 'npm run build' failed." -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null # Indicate failure
        }

        $buildOutputPath = Join-Path -Path $ProjectRoot -ChildPath "build"
        if (-Not (Test-Path $buildOutputPath)) {
            Write-Host "[Build-UI] Build output directory not found after build: $buildOutputPath" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null # Indicate failure
        }

        Write-Host "[Build-UI] Local UI build completed successfully. Output: $buildOutputPath" -ForegroundColor Green
        return $buildOutputPath

    } catch {
        Write-Host "[Build-UI] Error during local UI build: $_" -ForegroundColor Red
        Read-Host "Press Enter to continue..." | Out-Null
        return $null # Indicate failure
    } finally {
        Pop-Location
        # No Read-Host here, let the calling script manage flow control
    }
}

Export-ModuleMember -Function Invoke-BuildUI
