# Validate Build Script for Project Tracker
# For Windows Server 2022 Build Environment

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet('Development', 'Staging', 'Production')]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigPath = ".\config\validation-config.json",
    
    [Parameter(Mandatory=$false)]
    [switch]$DetailedOutput
)

# Import required modules
Import-Module .\modules\ValidationHelpers.psm1
Import-Module .\modules\SecurityChecks.psm1

# Initialize logging
$LogFile = ".\logs\validation-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
Start-Transcript -Path $LogFile

class ValidationResult {
    [string]$Component
    [bool]$Success
    [string[]]$Errors
    [string[]]$Warnings
    
    ValidationResult([string]$component) {
        $this.Component = $component
        $this.Success = $true
        $this.Errors = @()
        $this.Warnings = @()
    }
}

class BuildValidator {
    [string]$Environment
    [hashtable]$Config
    [ValidationResult[]]$Results
    
    BuildValidator([string]$env, [string]$configPath) {
        $this.Environment = $env
        $this.Config = Get-Content $configPath | ConvertFrom-Json -AsHashtable
        $this.Results = @()
    }
    
    [void] ValidateAll() {
        $this.ValidateSecurityConfiguration()
        $this.ValidateDeploymentFiles()
        $this.ValidateDependencies()
        $this.ValidateConfigurations()
        $this.ValidatePermissions()
    }
    
    [void] ValidateSecurityConfiguration() {
        $result = [ValidationResult]::new("Security")
        
        # Check SSL certificates
        if (-not (Test-SSLCertificates -Environment $this.Environment)) {
            $result.Success = $false
            $result.Errors += "Invalid SSL certificate configuration"
        }
        
        # Validate security headers
        $headers = Get-SecurityHeaders -ConfigPath $this.Config.SecurityConfig
        if (-not $headers.Valid) {
            $result.Warnings += "Security headers need review: $($headers.Issues)"
        }
        
        # Check sensitive data exposure
        $sensitiveData = Find-SensitiveData -Path $this.Config.BuildPath
        if ($sensitiveData.Found) {
            $result.Success = $false
            $result.Errors += "Sensitive data found in build: $($sensitiveData.Files)"
        }
        
        $this.Results += $result
    }
    
    [void] ValidateDeploymentFiles() {
        $result = [ValidationResult]::new("Deployment Files")
        
        # Check required files
        foreach ($file in $this.Config.RequiredFiles) {
            if (-not (Test-Path $file)) {
                $result.Success = $false
                $result.Errors += "Missing required file: $file"
            }
        }
        
        # Validate file sizes
        $sizeValidation = Test-FileSizes -Path $this.Config.BuildPath
        if (-not $sizeValidation.Valid) {
            $result.Warnings += $sizeValidation.Issues
        }
        
        $this.Results += $result
    }
    
    [void] ReportResults() {
        foreach ($result in $this.Results) {
            Write-Host "`nValidating $($result.Component)..."
            if ($result.Success) {
                Write-Host "✅ Passed" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed" -ForegroundColor Red
            }
            
            if ($this.DetailedOutput) {
                if ($result.Errors) {
                    Write-Host "Errors:" -ForegroundColor Red
                    $result.Errors | ForEach-Object { Write-Host "  - $_" }
                }
                if ($result.Warnings) {
                    Write-Host "Warnings:" -ForegroundColor Yellow
                    $result.Warnings | ForEach-Object { Write-Host "  - $_" }
                }
            }
        }
    }
}

# Main execution
try {
    $validator = [BuildValidator]::new($Environment, $ConfigPath)
    $validator.ValidateAll()
    $validator.ReportResults()
    
    if ($validator.Results.Success -contains $false) {
        throw "Validation failed. Check the log file for details: $LogFile"
    }
} catch {
    Write-Error $_.Exception.Message
    exit 1
} finally {
    Stop-Transcript
}
