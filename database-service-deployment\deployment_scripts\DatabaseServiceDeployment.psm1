# DatabaseServiceDeployment.psm1 - Main module for database service deployment

# Import required modules
try {
    # Import core modules first
    Import-Module -Name "$PSScriptRoot\Common.psm1" -Force -ErrorAction Stop
    Import-Module -Name "$PSScriptRoot\Modules\Logger.psm1" -Force -ErrorAction Stop

    # Log module loading
    if (Get-Command -Name Write-Log -ErrorAction SilentlyContinue) {
        Write-Log -Message "Loading deployment modules..." -Level "Info" -Component "Main"
    } else {
        Write-Host "Loading deployment modules..." -ForegroundColor Cyan
    }

    # Import remaining modules
    Import-Module -Name "$PSScriptRoot\Modules\ConfigValidator.psm1" -Force -ErrorAction Stop
    Import-Module -Name "$PSScriptRoot\Modules\SSHManager.psm1" -Force -Global -ErrorAction Stop
    Import-Module -Name "$PSScriptRoot\Modules\BuildProject.psm1" -Force -ErrorAction Stop

    # Log successful loading
    if (Get-Command -Name Write-Log -ErrorAction SilentlyContinue) {
        Write-Log -Message "All modules loaded successfully" -Level "Info" -Component "Main"
    } else {
        Write-Host "All modules loaded successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "Error loading modules: $_" -ForegroundColor Red
    Write-Host "This may cause functionality issues. Please ensure all module files exist." -ForegroundColor Yellow
}

# Initialize the deployment system
function Initialize-Deployment {
    param (
        [Parameter(Mandatory=$false)]
        [string]$LogLevel = "Info",

        [Parameter(Mandatory=$false)]
        [string]$ConfigFile = $null
    )

    try {
        # Initialize logger
        $logInitialized = Initialize-Logger -Level $LogLevel
        if (-not $logInitialized) {
            Write-Host "Failed to initialize logger" -ForegroundColor Red
            return $false
        }

        Write-Log -Message "Initializing database service deployment" -Level "Info" -Component "Main"

        # Load configuration if specified
        if (-not [string]::IsNullOrEmpty($ConfigFile)) {
            if (Test-Path -Path $ConfigFile) {
                Write-Log -Message "Loading configuration from $ConfigFile" -Level "Info" -Component "Main"
                $configLoaded = Import-Configuration -ConfigFile $ConfigFile

                if (-not $configLoaded) {
                    Write-Log -Message "Failed to load configuration" -Level "Error" -Component "Main"
                    return $false
                }

                # Validate configuration
                $validationResult = Test-Configuration -Config $script:Config -Detailed
                if (-not $validationResult.IsValid) {
                    Write-Log -Message "Configuration validation failed" -Level "Warning" -Component "Main"

                    # Try to repair the configuration
                    Write-Log -Message "Attempting to repair configuration..." -Level "Info" -Component "Main"
                    $repairResult = Repair-Configuration -Config $script:Config

                    if ($repairResult.Modified) {
                        $script:Config = $repairResult.Config
                        Write-Log -Message "Configuration repaired successfully" -Level "Info" -Component "Main"

                        # Save the repaired configuration
                        Save-Configuration
                    } else {
                        Write-Log -Message "Failed to repair configuration" -Level "Error" -Component "Main"
                        return $false
                    }
                }
            } else {
                Write-Log -Message "Configuration file not found: $ConfigFile" -Level "Error" -Component "Main"
                return $false
            }
        }

        Write-Log -Message "Deployment system initialized successfully" -Level "Info" -Component "Main"
        return $true
    } catch {
        Write-Host "Error initializing deployment: $_" -ForegroundColor Red
        return $false
    }
}

# Deploy the database service
function Deploy-DatabaseService {
    param (
        [Parameter(Mandatory=$false)]
        [switch]$SkipBuild,

        [Parameter(Mandatory=$false)]
        [switch]$SkipInstall,

        [Parameter(Mandatory=$false)]
        [switch]$SkipDependencies,

        [Parameter(Mandatory=$false)]
        [switch]$SkipDatabase
    )

    try {
        Write-Log -Message "Starting database service deployment" -Level "Info" -Component "Main"

        # Check if configuration is loaded
        if ($null -eq $script:Config) {
            Write-Log -Message "No configuration loaded" -Level "Error" -Component "Main"
            return $false
        }

        # Step 1: Build the project
        if (-not $SkipBuild) {
            Write-Log -Message "Building the project..." -Level "Info" -Component "Main"
            $buildResult = Build-Project -Configuration $script:Config

            if (-not $buildResult) {
                Write-Log -Message "Build failed" -Level "Error" -Component "Main"
                return $false
            }
        } else {
            Write-Log -Message "Skipping build step" -Level "Info" -Component "Main"
        }

        # Step 2: Install dependencies
        if (-not $SkipDependencies) {
            Write-Log -Message "Installing dependencies..." -Level "Info" -Component "Main"

            # Import the module dynamically
            Import-Module -Name "$PSScriptRoot\Modules\Install-Dependencies.ps1" -Force
            $installDepsResult = Install-Dependencies

            if (-not $installDepsResult) {
                Write-Log -Message "Failed to install dependencies" -Level "Error" -Component "Main"
                return $false
            }
        } else {
            Write-Log -Message "Skipping dependencies installation" -Level "Info" -Component "Main"
        }

        # Step 3: Initialize database
        if (-not $SkipDatabase) {
            Write-Log -Message "Initializing database..." -Level "Info" -Component "Main"

            # Import the module dynamically
            Import-Module -Name "$PSScriptRoot\Modules\Initialize-Database.ps1" -Force
            $initDbResult = Initialize-Database

            if (-not $initDbResult) {
                Write-Log -Message "Failed to initialize database" -Level "Error" -Component "Main"
                return $false
            }
        } else {
            Write-Log -Message "Skipping database initialization" -Level "Info" -Component "Main"
        }

        # Step 4: Install service
        if (-not $SkipInstall) {
            Write-Log -Message "Installing service..." -Level "Info" -Component "Main"

            # Import the module dynamically
            Import-Module -Name "$PSScriptRoot\Modules\Install-Service.ps1" -Force
            $installServiceResult = Install-Service

            if (-not $installServiceResult) {
                Write-Log -Message "Failed to install service" -Level "Error" -Component "Main"
                return $false
            }
        } else {
            Write-Log -Message "Skipping service installation" -Level "Info" -Component "Main"
        }

        Write-Log -Message "Database service deployment completed successfully" -Level "Info" -Component "Main"
        return $true
    } catch {
        Write-Log -Message "Exception in deployment: $_" -Level "Error" -Component "Main"
        Write-Log -Message $_.Exception.StackTrace -Level "Debug" -Component "Main"
        return $false
    }
}

# Export functions
Export-ModuleMember -Function Initialize-Deployment, Deploy-DatabaseService
