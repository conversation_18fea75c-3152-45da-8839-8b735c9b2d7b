# Database Integration Implementation Guide

This guide provides detailed instructions for integrating the Database Service with other applications in the Project Tracker ecosystem.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Integration Steps](#integration-steps)
4. [Client Library Integration](#client-library-integration)
5. [Database Schema Design](#database-schema-design)
6. [JSON Storage Implementation](#json-storage-implementation)
7. [Security Considerations](#security-considerations)
8. [Performance Optimization](#performance-optimization)
9. [Troubleshooting](#troubleshooting)
10. [Examples](#examples)

## Overview

The Database Service provides a centralized database management solution for all applications in the Project Tracker ecosystem. This guide explains how to integrate your application with the Database Service using the provided client library.

## Prerequisites

Before integrating with the Database Service, ensure you have:

1. **Database Service Installed**: The Database Service should be installed and running on your server
2. **PostgreSQL 17+**: PostgreSQL 17 or later should be installed and configured
3. **C++23 Compiler**: A C++23 compatible compiler (GCC 11+, Clang 14+, MSVC 19.30+)
4. **Client Library**: The Database Service client library
5. **API Key**: An API key for authenticating with the Database Service

## Integration Steps

### Step 1: Include the Client Library

Add the Database Service client library to your project:

```cpp
#include "database-service/client/database_client.hpp"
```

### Step 2: Create a Database Client

Create a database client instance:

```cpp
// Create database client
dbservice::client::DatabaseClient dbClient(
    "http://localhost:8080",  // Database Service URL
    "your-application",       // Application name
    "your-api-key",           // API key
    "your_app_db"             // Database name
);
```

### Step 3: Check Database Availability

Check if the database is available:

```cpp
// Check if database is available
if (!dbClient.isAvailable()) {
    std::cerr << "Database is not available" << std::endl;
    return 1;
}

// Get database version
std::string version = dbClient.getVersion();
std::cout << "Connected to database version: " << version << std::endl;
```

### Step 4: Create Database Schema

Create your application's database schema:

```cpp
// Create database schema
dbClient.execute(
    "CREATE TABLE IF NOT EXISTS your_table ("
    "  id SERIAL PRIMARY KEY,"
    "  name VARCHAR(100) NOT NULL,"
    "  value INTEGER,"
    "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
    ");"
);
```

### Step 5: Implement CRUD Operations

Implement CRUD (Create, Read, Update, Delete) operations:

```cpp
// Create
int rowsAffected = dbClient.execute(
    "INSERT INTO your_table (name, value) VALUES ($1, $2)",
    {{"$1", "example"}, {"$2", "123"}}
);

// Read
nlohmann::json results = dbClient.query(
    "SELECT * FROM your_table WHERE name = $1",
    {{"$1", "example"}}
);

// Update
rowsAffected = dbClient.execute(
    "UPDATE your_table SET value = $1 WHERE name = $2",
    {{"$1", "456"}, {"$2", "example"}}
);

// Delete
rowsAffected = dbClient.execute(
    "DELETE FROM your_table WHERE name = $1",
    {{"$1", "example"}}
);
```

## Client Library Integration

### Connection Management

The client library handles connection management automatically, but you can customize it:

```cpp
// Create a connection pool
dbservice::client::ConnectionPool pool(
    "http://localhost:8080",  // Database Service URL
    "your-application",       // Application name
    "your-api-key",           // API key
    "your_app_db",            // Database name
    10                        // Pool size
);

// Get a client from the pool
auto client = pool.getClient();

// Use the client
nlohmann::json results = client->query("SELECT * FROM your_table");
```

### Asynchronous Operations

For long-running operations, use asynchronous methods:

```cpp
// Execute a query asynchronously
auto future = dbClient.queryAsync(
    "SELECT * FROM your_table WHERE name = $1",
    {{"$1", "example"}}
);

// Do other work while the query is executing
// ...

// Get the results
try {
    nlohmann::json results = future.get();
    
    // Process results
    for (const auto& row : results) {
        std::cout << "ID: " << row["id"].get<int>() << ", "
                 << "Name: " << row["name"].get<std::string>() << std::endl;
    }
} catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
}
```

### Error Handling

Implement proper error handling:

```cpp
try {
    // Execute a query
    nlohmann::json results = dbClient.query(
        "SELECT * FROM non_existent_table"
    );
} catch (const dbservice::client::DatabaseError& e) {
    std::cerr << "Database error: " << e.what() << std::endl;
    std::cerr << "Error code: " << e.getCode() << std::endl;
} catch (const dbservice::client::AuthenticationError& e) {
    std::cerr << "Authentication error: " << e.what() << std::endl;
} catch (const dbservice::client::ConnectionError& e) {
    std::cerr << "Connection error: " << e.what() << std::endl;
} catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
}
```

## Database Schema Design

### Multi-Database Architecture

The Database Service uses a multi-database architecture:

```
PostgreSQL Server
 database_service (database)
    public (schema)
        schema_version, applications, users, permissions (tables)

 your_app_db (database)
    public (schema)
       your_tables (tables)
    custom_schema (schema)
        additional_tables (tables)
```

### Schema Creation

Create a dedicated database for your application:

```sql
-- Create application database
CREATE DATABASE your_app_db;

-- Connect to application database
\c your_app_db

-- Create custom schema
CREATE SCHEMA custom_schema;

-- Create tables in public schema
CREATE TABLE your_table (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  value INTEGER,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create tables in custom schema
CREATE TABLE custom_schema.additional_table (
  id SERIAL PRIMARY KEY,
  your_table_id INTEGER REFERENCES your_table(id),
  data TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

### Schema Migration

For schema migrations, use the Database Service schema migration API:

```cpp
// Apply schema migration
nlohmann::json result = dbClient.sendRequest(
    "/api/schema/migrate",
    "POST",
    {
        {"application", "your-application"},
        {"database", "your_app_db"},
        {"target_version", "1.1.0"}
    }
);
```

## JSON Storage Implementation

### JSON Column Types

PostgreSQL provides two JSON column types:

- **JSON**: Stores JSON data as text
- **JSONB**: Stores JSON data in a binary format for faster querying and indexing

```sql
-- Create table with JSON column
CREATE TABLE your_table_with_json (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  metadata JSON,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create table with JSONB column (recommended)
CREATE TABLE your_table_with_jsonb (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

### Storing JSON Data

Store JSON data using the client library:

```cpp
// Create JSON data
nlohmann::json metadata = {
    {"key1", "value1"},
    {"key2", 123},
    {"key3", {
        {"nested1", "value2"},
        {"nested2", 456}
    }},
    {"key4", {"item1", "item2", "item3"}}
};

// Store JSON data
dbClient.execute(
    "INSERT INTO your_table_with_jsonb (name, metadata) VALUES ($1, $2)",
    {
        {"$1", "example"},
        {"$2", metadata.dump()}
    }
);
```

### Querying JSON Data

Query JSON data using PostgreSQL's JSON operators:

```cpp
// Query JSON data with equality
nlohmann::json results = dbClient.query(
    "SELECT * FROM your_table_with_jsonb WHERE metadata->'key1' = 'value1'::jsonb"
);

// Query JSON data with containment
results = dbClient.query(
    "SELECT * FROM your_table_with_jsonb WHERE metadata @> '{\\"key2\\": 123}'::jsonb"
);

// Query nested JSON data
results = dbClient.query(
    "SELECT * FROM your_table_with_jsonb WHERE metadata->'key3'->'nested1' = 'value2'::jsonb"
);

// Query JSON array
results = dbClient.query(
    "SELECT * FROM your_table_with_jsonb WHERE metadata->'key4' ? 'item2'"
);
```

### Indexing JSON Data

Create indexes on JSON data for better performance:

```sql
-- Create index on specific JSON key
CREATE INDEX idx_metadata_key1 ON your_table_with_jsonb ((metadata->'key1'));

-- Create GIN index for containment queries
CREATE INDEX idx_metadata_gin ON your_table_with_jsonb USING GIN (metadata);

-- Create GIN index for specific JSON path
CREATE INDEX idx_metadata_key3 ON your_table_with_jsonb USING GIN ((metadata->'key3'));
```

## Security Considerations

### Authentication

Always use authentication when connecting to the Database Service:

```cpp
// Create database client with authentication
dbservice::client::DatabaseClient dbClient(
    "http://localhost:8080",  // Database Service URL
    "your-application",       // Application name
    "your-api-key",           // API key
    "your_app_db"             // Database name
);
```

### SSL/TLS

Enable SSL/TLS for secure communication:

```cpp
// Create database client with SSL/TLS
dbservice::client::DatabaseClient dbClient(
    "https://localhost:8080",  // HTTPS URL
    "your-application",
    "your-api-key",
    "your_app_db"
);
```

### Data Encryption

For sensitive data, use the Database Service encryption functions:

```cpp
// Encrypt sensitive data
std::string encryptedData = dbClient.sendRequest(
    "/api/encrypt",
    "POST",
    {
        {"data", "sensitive-data"}
    }
)["encrypted"].get<std::string>();

// Store encrypted data
dbClient.execute(
    "INSERT INTO your_table (name, value) VALUES ($1, $2)",
    {{"$1", "encrypted-example"}, {"$2", encryptedData}}
);

// Decrypt data
std::string decryptedData = dbClient.sendRequest(
    "/api/decrypt",
    "POST",
    {
        {"data", encryptedData}
    }
)["decrypted"].get<std::string>();
```

## Performance Optimization

### Connection Pooling

Use connection pooling for better performance:

```cpp
// Create a connection pool
dbservice::client::ConnectionPool pool(
    "http://localhost:8080",
    "your-application",
    "your-api-key",
    "your_app_db",
    10  // Pool size
);
```

### Batch Operations

Use transactions for batch operations:

```cpp
// Execute multiple statements in a transaction
nlohmann::json results = dbClient.transaction({
    "INSERT INTO your_table (name, value) VALUES ('batch1', 1);",
    "INSERT INTO your_table (name, value) VALUES ('batch2', 2);",
    "INSERT INTO your_table (name, value) VALUES ('batch3', 3);"
});
```

### Query Optimization

Optimize your queries for better performance:

```cpp
// Use indexes
dbClient.execute(
    "CREATE INDEX idx_your_table_name ON your_table(name);"
);

// Use prepared statements
dbClient.query(
    "SELECT * FROM your_table WHERE name = $1",
    {{"$1", "example"}}
);

// Limit result set size
dbClient.query(
    "SELECT * FROM your_table LIMIT 100"
);
```

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Check if the Database Service is running: `sudo systemctl status database-service`
   - Verify network connectivity: `curl -v http://localhost:8080/api/status`
   - Check firewall settings: `sudo ufw status`

2. **Authentication Failed**:
   - Check API key in configuration
   - Verify token generation and validation
   - Check permissions in the database

3. **Database Not Found**:
   - Verify database existence: `sudo -u postgres psql -c "\l" | grep your_app_db`
   - Check database creation in the Database Service

4. **Query Failed**:
   - Check SQL syntax
   - Verify table existence
   - Check column names and types

### Logging

Enable detailed logging for troubleshooting:

```cpp
// Enable debug logging
dbClient.setHeader("X-Debug-Level", "debug");

// Execute query with logging
nlohmann::json results = dbClient.query(
    "SELECT * FROM your_table WHERE name = $1",
    {{"$1", "example"}}
);
```

### Diagnostic Queries

Use diagnostic queries to troubleshoot issues:

```cpp
// Check database status
nlohmann::json status = dbClient.sendRequest("/api/status", "GET");

// Check schema version
nlohmann::json version = dbClient.sendRequest(
    "/api/schema/version",
    "GET",
    {
        {"application", "your-application"},
        {"database", "your_app_db"}
    }
);

// Check database connections
nlohmann::json connections = dbClient.sendRequest("/api/connections", "GET");
```

## Examples

### Basic CRUD Example

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>

int main() {
    try {
        // Create database client
        dbservice::client::DatabaseClient dbClient(
            "http://localhost:8080",
            "example-app",
            "api-key-123",
            "example_db"
        );
        
        // Check if database is available
        if (!dbClient.isAvailable()) {
            std::cerr << "Database is not available" << std::endl;
            return 1;
        }
        
        // Create table
        dbClient.execute(
            "CREATE TABLE IF NOT EXISTS users ("
            "  id SERIAL PRIMARY KEY,"
            "  username VARCHAR(100) NOT NULL,"
            "  email VARCHAR(100) NOT NULL,"
            "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
            ");"
        );
        
        // Insert data
        int rowsAffected = dbClient.execute(
            "INSERT INTO users (username, email) VALUES ($1, $2)",
            {{"$1", "john"}, {"$2", "<EMAIL>"}}
        );
        
        std::cout << "Inserted " << rowsAffected << " row(s)" << std::endl;
        
        // Query data
        nlohmann::json results = dbClient.query(
            "SELECT * FROM users WHERE username = $1",
            {{"$1", "john"}}
        );
        
        // Display results
        for (const auto& row : results) {
            std::cout << "ID: " << row["id"].get<int>() << ", "
                     << "Username: " << row["username"].get<std::string>() << ", "
                     << "Email: " << row["email"].get<std::string>() << std::endl;
        }
        
        // Update data
        rowsAffected = dbClient.execute(
            "UPDATE users SET email = $1 WHERE username = $2",
            {{"$1", "<EMAIL>"}, {"$2", "john"}}
        );
        
        std::cout << "Updated " << rowsAffected << " row(s)" << std::endl;
        
        // Delete data
        rowsAffected = dbClient.execute(
            "DELETE FROM users WHERE username = $1",
            {{"$1", "john"}}
        );
        
        std::cout << "Deleted " << rowsAffected << " row(s)" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
```

### JSON Storage Example

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>

int main() {
    try {
        // Create database client
        dbservice::client::DatabaseClient dbClient(
            "http://localhost:8080",
            "example-app",
            "api-key-123",
            "example_db"
        );
        
        // Create table with JSONB column
        dbClient.execute(
            "CREATE TABLE IF NOT EXISTS user_profiles ("
            "  id SERIAL PRIMARY KEY,"
            "  user_id INTEGER NOT NULL,"
            "  profile JSONB NOT NULL,"
            "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
            ");"
        );
        
        // Create JSON data
        nlohmann::json profile = {
            {"first_name", "John"},
            {"last_name", "Doe"},
            {"age", 30},
            {"address", {
                {"street", "123 Main St"},
                {"city", "Anytown"},
                {"state", "CA"},
                {"zip", "12345"}
            }},
            {"interests", {"programming", "reading", "hiking"}}
        };
        
        // Insert JSON data
        int rowsAffected = dbClient.execute(
            "INSERT INTO user_profiles (user_id, profile) VALUES ($1, $2)",
            {{"$1", "1"}, {"$2", profile.dump()}}
        );
        
        std::cout << "Inserted " << rowsAffected << " row(s)" << std::endl;
        
        // Query JSON data
        nlohmann::json results = dbClient.query(
            "SELECT * FROM user_profiles WHERE profile->'first_name' = 'John'::jsonb"
        );
        
        // Display results
        for (const auto& row : results) {
            std::cout << "ID: " << row["id"].get<int>() << ", "
                     << "User ID: " << row["user_id"].get<int>() << std::endl;
            
            nlohmann::json profile = nlohmann::json::parse(row["profile"].get<std::string>());
            std::cout << "Profile: " << profile.dump(2) << std::endl;
        }
        
        // Query nested JSON data
        results = dbClient.query(
            "SELECT * FROM user_profiles WHERE profile->'address'->'city' = 'Anytown'::jsonb"
        );
        
        std::cout << "Users in Anytown: " << results.size() << std::endl;
        
        // Query JSON array
        results = dbClient.query(
            "SELECT * FROM user_profiles WHERE profile->'interests' ? 'programming'"
        );
        
        std::cout << "Users interested in programming: " << results.size() << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
```

### Transaction Example

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>

int main() {
    try {
        // Create database client
        dbservice::client::DatabaseClient dbClient(
            "http://localhost:8080",
            "example-app",
            "api-key-123",
            "example_db"
        );
        
        // Create tables
        dbClient.execute(
            "CREATE TABLE IF NOT EXISTS orders ("
            "  id SERIAL PRIMARY KEY,"
            "  customer_id INTEGER NOT NULL,"
            "  total DECIMAL(10, 2) NOT NULL,"
            "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
            ");"
        );
        
        dbClient.execute(
            "CREATE TABLE IF NOT EXISTS order_items ("
            "  id SERIAL PRIMARY KEY,"
            "  order_id INTEGER NOT NULL REFERENCES orders(id),"
            "  product_id INTEGER NOT NULL,"
            "  quantity INTEGER NOT NULL,"
            "  price DECIMAL(10, 2) NOT NULL,"
            "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
            ");"
        );
        
        // Execute transaction
        nlohmann::json results = dbClient.transaction({
            "INSERT INTO orders (customer_id, total) VALUES (1, 100.00) RETURNING id;",
            "INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (currval('orders_id_seq'), 1, 2, 50.00);",
            "INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (currval('orders_id_seq'), 2, 1, 50.00);"
        });
        
        std::cout << "Transaction results: " << results.dump(2) << std::endl;
        
        // Query order and items
        nlohmann::json order = dbClient.query(
            "SELECT * FROM orders ORDER BY id DESC LIMIT 1"
        )[0];
        
        std::cout << "Order ID: " << order["id"].get<int>() << ", "
                 << "Customer ID: " << order["customer_id"].get<int>() << ", "
                 << "Total: " << order["total"].get<std::string>() << std::endl;
        
        nlohmann::json items = dbClient.query(
            "SELECT * FROM order_items WHERE order_id = $1",
            {{"$1", std::to_string(order["id"].get<int>())}}
        );
        
        std::cout << "Order Items:" << std::endl;
        for (const auto& item : items) {
            std::cout << "  Product ID: " << item["product_id"].get<int>() << ", "
                     << "Quantity: " << item["quantity"].get<int>() << ", "
                     << "Price: " << item["price"].get<std::string>() << std::endl;
        }
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
```

