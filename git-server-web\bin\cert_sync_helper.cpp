// cert_sync_helper.cpp
// A setuid helper program to safely run the certificate sync script
// Compile with:
// g++ -std=c++17 -o cert_sync_helper cert_sync_helper.cpp
// Then set permissions:
// sudo chown root:www-data cert_sync_helper
// sudo chmod 4750 cert_sync_helper

#include <iostream>
#include <string>
#include <array>
#include <memory>
#include <stdexcept>
#include <cstring>
#include <cerrno>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <grp.h>
#include <syslog.h>

// Path to the certificate sync script (constant and not modifiable)
constexpr const char* SYNC_SCRIPT = "/opt/git-dashboard/sync-certificates.sh";

// Log a message to syslog and stdout
void log_message(const std::string& message, bool is_error = false) {
    int priority = is_error ? LOG_ERR : LOG_INFO;
    syslog(priority, "%s", message.c_str());
    if (is_error) {
        std::cerr << "ERROR: " << message << std::endl;
    } else {
        std::cout << message << std::endl;
    }
}

// Execute the sync script and capture output
std::string exec_sync_script() {
    std::array<char, 4096> buffer;
    std::string result;
    
    // Use sudo to run as btaylor-admin which has SSH key access
    std::unique_ptr<FILE, decltype(&pclose)> pipe(
        popen("sudo -u btaylor-admin /opt/git-dashboard/sync-certificates.sh", "r"), 
        pclose
    );
    
    if (!pipe) {
        throw std::runtime_error("popen() failed: " + std::string(strerror(errno)));
    }
    
    // Read command output
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    
    return result;
}

// Check if the user is in the www-data group or is www-data user
bool is_www_data_user_or_group() {
    // Get user info
    uid_t uid = getuid(); // Use real user ID, not effective user ID
    struct passwd *pw = getpwuid(uid);
    if (!pw) {
        log_message("Failed to get user info", true);
        return false;
    }
    
    // Check if user is www-data
    if (strcmp(pw->pw_name, "www-data") == 0) {
        return true;
    }
    
    // Get www-data group info
    struct group *gr = getgrnam("www-data");
    if (!gr) {
        log_message("Failed to get www-data group info", true);
        return false;
    }
    
    // Check if user is in www-data group
    for (char **members = gr->gr_mem; *members != nullptr; members++) {
        if (strcmp(*members, pw->pw_name) == 0) {
            return true;
        }
    }
    
    // Also check if user's primary group is www-data
    return pw->pw_gid == gr->gr_gid;
}

int main() {
    try {
        // Set up syslog
        openlog("cert_sync_helper", LOG_PID | LOG_PERROR, LOG_DAEMON);
        
        // Log the user information for debugging
        uid_t real_uid = getuid();
        uid_t effective_uid = geteuid();
        struct passwd *pw_real = getpwuid(real_uid);
        struct passwd *pw_effective = getpwuid(effective_uid);
        
        std::string real_user = pw_real ? pw_real->pw_name : "unknown";
        std::string effective_user = pw_effective ? pw_effective->pw_name : "unknown";
        
        log_message("Real UID: " + std::to_string(real_uid) + " (" + real_user + ")");
        log_message("Effective UID: " + std::to_string(effective_uid) + " (" + effective_user + ")");
        
        // Security check: only allow www-data user or group to run this
        if (!is_www_data_user_or_group()) {
            log_message("Access denied: Only www-data user or group can run this helper", true);
            return 1;
        }
        
        log_message("Certificate sync helper started by user " + real_user);
        
        // Execute the sync script
        std::string output = exec_sync_script();
        
        // Send the output as JSON response
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"success\",\n";
        std::cout << "  \"message\": \"Certificate sync completed\",\n";
        std::cout << "  \"output\": \"" << output << "\"\n";
        std::cout << "}\n";
        
        log_message("Certificate sync completed successfully");
        return 0;
    } 
    catch (const std::exception& e) {
        log_message(std::string("Error: ") + e.what(), true);
        
        // Send error as JSON
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"error\",\n";
        std::cout << "  \"message\": \"" << e.what() << "\"\n";
        std::cout << "}\n";
        
        return 1;
    }
}
