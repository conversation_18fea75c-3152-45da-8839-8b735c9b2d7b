/**
 * Git Repository Modal Module
 * Handles the details modal for repositories
 */
const GitRepositoryModal = {
    // Module state
    state: {
        currentRepository: null,
        modalElement: null,
        modalInstance: null, // Store reference to Bootstrap modal instance
        initialized: false,
        chartInstance: null
    },

    /**
     * Initialize the repository modal module
     * @returns {Object} This module for chaining
     */
    init() {
        try {
            console.log('Initializing GitRepositoryModal...');

            // Initialize state
            this.state = {
                initialized: false,
                modalElement: null,
                modalInstance: null,
                currentRepository: null,
                chartInstance: null
            };

            // Create modal immediately
            this.createRepositoryDetailsModal();

            // Set up event listeners
            this.setupEventListeners();

            console.log('GitRepositoryModal initialized successfully');
            return this;
        } catch (error) {
            console.error('Error initializing GitRepositoryModal:', error);
            return this;
        }
    },

    /**
     * Create the repository details modal
     */
    createRepositoryDetailsModal() {
        try {
            // Check if modal already exists
            const existingModal = document.getElementById('repositoryDetailsModal');
            if (existingModal) {
                console.log('Modal already exists, using existing one');
                this.state.modalElement = existingModal;
                this.state.initialized = true;
                return;
            }

            // Create modal container
            const modalContainer = document.createElement('div');
            modalContainer.className = 'modal fade';
            modalContainer.id = 'repositoryDetailsModal';
            modalContainer.tabIndex = '-1';
            modalContainer.setAttribute('aria-labelledby', 'repositoryDetailsModalLabel');
            modalContainer.setAttribute('aria-hidden', 'true');

            // Create modal content
            modalContainer.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-xl">
                    <div class="modal-content">
                        <div class="modal-header py-2">
                            <h5 class="modal-title" id="repositoryDetailsModalLabel">Repository Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <!-- Loading state -->
                            <div id="repository-details-loading" class="p-4 text-center" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading repository details...</p>
                            </div>

                            <!-- Error state -->
                            <div id="repository-details-error" class="p-4 text-center" style="display: none;">
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <p>Failed to load repository details.</p>
                            </div>

                            <!-- Content state -->
                            <div id="repository-details-content" style="display: none;">
                                <!-- Repository Info -->
                                <div class="p-4 border-bottom">
                                    <h5 id="repo-name">Repository Name</h5>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <p id="repo-description" class="text-muted">Repository description</p>
                                            <div class="d-flex flex-wrap">
                                                <div class="me-4 mb-3">
                                                    <strong>Size:</strong>
                                                    <span id="repo-size">0 KB</span>
                                                </div>
                                                <div class="me-4 mb-3">
                                                    <strong>Last Updated:</strong>
                                                    <span id="repo-last-commit">Never</span>
                                                </div>
                                                <div class="me-4 mb-3">
                                                    <strong>Branches:</strong>
                                                    <span id="repo-branch-count">0</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="health-status p-3 border rounded">
                                                <h6 class="mb-2">Repository Health</h6>
                                                <div class="progress mb-2">
                                                    <div id="health-progress" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <p id="health-message" class="mb-0 small">No health data available</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Commit History -->
                                <div class="p-3 border-bottom">
                                    <h6 class="mb-2">Commit History</h6>
                                    <div id="commit-history-container" style="height: 400px; position: relative;" class="col-md-12">
                                        <canvas id="commit-history-chart"></canvas>
                                        <div id="commit-chart-error" class="text-center text-danger mt-2" style="display: none;"></div>
                                        <div id="commit-chart-message" class="text-center text-muted mt-2" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to document body
            document.body.appendChild(modalContainer);

            // Store reference to modal element
            this.state.modalElement = modalContainer;
            this.state.initialized = true;

            console.log('Repository details modal created');
        } catch (error) {
            console.error('Error creating repository details modal:', error);
        }
    },

    /**
     * Set up event listeners for modal events
     */
    setupEventListeners() {
        if (!this.state.modalElement) {
            console.error('Modal element not found');
            return;
        }

        // Listen for modal shown event
        this.state.modalElement.addEventListener('shown.bs.modal', this.handleModalShown.bind(this));

        // Listen for modal hidden event
        this.state.modalElement.addEventListener('hidden.bs.modal', this.handleModalHidden.bind(this));
    },

    /**
     * Handle modal shown event - initialize content
     */
    handleModalShown() {
        console.log('Modal shown event triggered');

        // Check if we have a current repository
        if (!this.state.currentRepository) {
            console.error('No repository selected');
            this.showError('No repository selected');
            return;
        }

        // Update modal content
        this.updateModalContent(this.state.currentRepository);

        // Fetch and display commit history (legacy method, will be skipped if GitCommitHistoryChart is available)
        this.fetchAndDisplayCommitHistory(this.state.currentRepository.name);
    },

    /**
     * Handle modal hidden event - clean up resources
     */
    handleModalHidden() {
        console.log('Modal hidden event triggered');

        // Clean up charts
        this.cleanupCharts();
    },

    /**
     * Clean up chart instances
     */
    cleanupCharts() {
        // Clean up internal chart instance (if any)
        if (this.state.chartInstance) {
            this.state.chartInstance.destroy();
            this.state.chartInstance = null;
            console.log('Internal chart instance cleaned up');
        }

        // Clean up commit history chart
        if (window.GitCommitHistoryChart) {
            GitCommitHistoryChart.cleanupChart();
            console.log('Commit history chart cleaned up');
        }

        // Clean up size comparison chart
        if (window.GitSizeComparisonChart) {
            GitSizeComparisonChart.cleanupChart();
            console.log('Size comparison chart cleaned up');
        }
    },

    /**
     * Clean up chart instance
     */
    cleanupChart() {
        if (this.state.chartInstance) {
            this.state.chartInstance.destroy();
            this.state.chartInstance = null;
            console.log('Chart instance cleaned up');
        }
    },

    /**
     * Show repository details in modal
     * @param {Object} repo - Repository object
     */
    showRepositoryDetails(repo) {
        if (!repo) {
            console.error('No repository provided');
            return;
        }

        console.log(`Showing details for repository: ${repo.name}`);

        // Store current repository
        this.state.currentRepository = repo;

        // Show the modal
        this.showModal();
    },

    /**
     * Show the modal
     */
    showModal() {
        if (!this.state.modalElement) {
            console.error('Modal element not found');
            return;
        }

        // Show loading state
        this.showLoading();

        // Initialize Bootstrap modal if not already initialized
        if (!this.state.modalInstance) {
            this.state.modalInstance = new bootstrap.Modal(this.state.modalElement);
        }

        // Show the modal
        this.state.modalInstance.show();
    },

    /**
     * Show loading state in modal
     */
    showLoading() {
        const loadingElement = document.getElementById('repository-details-loading');
        const errorElement = document.getElementById('repository-details-error');
        const contentElement = document.getElementById('repository-details-content');

        if (loadingElement) loadingElement.style.display = 'block';
        if (errorElement) errorElement.style.display = 'none';
        if (contentElement) contentElement.style.display = 'none';
    },

    /**
     * Show error state in modal
     * @param {string} message - Error message
     */
    showError(message) {
        const loadingElement = document.getElementById('repository-details-loading');
        const errorElement = document.getElementById('repository-details-error');
        const contentElement = document.getElementById('repository-details-content');

        if (loadingElement) loadingElement.style.display = 'none';
        if (errorElement) {
            errorElement.style.display = 'block';

            // Update error message
            const errorParagraph = errorElement.querySelector('p');
            if (errorParagraph) {
                errorParagraph.textContent = message || 'Failed to load repository details.';
            }
        }
        if (contentElement) contentElement.style.display = 'none';
    },

    /**
     * Show content state in modal
     */
    showContent() {
        const loadingElement = document.getElementById('repository-details-loading');
        const errorElement = document.getElementById('repository-details-error');
        const contentElement = document.getElementById('repository-details-content');

        if (loadingElement) loadingElement.style.display = 'none';
        if (errorElement) errorElement.style.display = 'none';
        if (contentElement) contentElement.style.display = 'block';
    },

    /**
     * Update modal content with repository details
     * @param {Object} repo - Repository object
     */
    updateModalContent(repo) {
        // Show content state
        this.showContent();

        // Update repository name
        const repoNameElement = document.getElementById('repo-name');
        if (repoNameElement) {
            repoNameElement.textContent = repo.name;
        }

        // Update repository description
        const repoDescriptionElement = document.getElementById('repo-description');
        if (repoDescriptionElement) {
            repoDescriptionElement.textContent = repo.description || 'No description';
        }

        // Update repository size
        const repoSizeElement = document.getElementById('repo-size');
        if (repoSizeElement) {
            repoSizeElement.textContent = repo.size_formatted || '0 KB';
        }

        // Update last commit date
        const lastCommitElement = document.getElementById('repo-last-commit');
        if (lastCommitElement) {
            if (repo.last_commit) {
                const lastCommitDate = new Date(repo.last_commit);
                lastCommitElement.textContent = lastCommitDate.toLocaleDateString();
            } else {
                lastCommitElement.textContent = 'No commits';
            }
        }

        // Update branch count
        const branchCountElement = document.getElementById('repo-branch-count');
        if (branchCountElement) {
            branchCountElement.textContent = `${repo.branch_count || 0} branch${repo.branch_count !== 1 ? 'es' : ''}`;
        }

        // Update health indicator
        this.updateHealthIndicator(repo);

        // Initialize commit history chart
        if (window.GitCommitHistoryChart) {
            GitCommitHistoryChart.initChart(repo.name);
        } else {
            console.error('GitCommitHistoryChart module not available');
        }

        // Hide size comparison chart container
        const sizeComparisonContainer = document.getElementById('size-comparison-container');
        if (sizeComparisonContainer) {
            sizeComparisonContainer.style.display = 'none';
        }

        // Make commit history container larger
        const commitHistoryContainer = document.getElementById('commit-history-container');
        if (commitHistoryContainer) {
            commitHistoryContainer.style.height = '400px';
            commitHistoryContainer.classList.remove('col-md-6');
            commitHistoryContainer.classList.add('col-md-12');
        }
    },

    /**
     * Update repository health indicator
     * @param {Object} repo - Repository object
     */
    updateHealthIndicator(repo) {
        const healthProgressElement = document.getElementById('health-progress');
        const healthMessageElement = document.getElementById('health-message');

        if (!repo.health) {
            // Default health status
            if (healthProgressElement) {
                healthProgressElement.style.width = '0%';
                healthProgressElement.setAttribute('aria-valuenow', '0');
                healthProgressElement.className = 'progress-bar bg-secondary';
            }

            if (healthMessageElement) {
                healthMessageElement.textContent = 'No health data available';
            }

            return;
        }

        // Update progress bar
        if (healthProgressElement) {
            healthProgressElement.style.width = `${repo.health.score}%`;
            healthProgressElement.setAttribute('aria-valuenow', repo.health.score);

            // Set color based on health status
            let colorClass = 'bg-secondary';
            switch (repo.health.status) {
                case 'good':
                    colorClass = 'bg-success';
                    break;
                case 'fair':
                    colorClass = 'bg-info';
                    break;
                case 'poor':
                    colorClass = 'bg-warning';
                    break;
            }

            healthProgressElement.className = `progress-bar ${colorClass}`;
        }

        // Update health message
        if (healthMessageElement) {
            healthMessageElement.textContent = repo.health.message || 'No health data available';
        }
    },

    /**
     * Fetch and display commit history
     * @param {string} repoName - Repository name
     */
    async fetchAndDisplayCommitHistory(repoName) {
        console.log(`fetchAndDisplayCommitHistory called for ${repoName}`);

        // Check if we have the GitCommitHistoryChart module
        if (window.GitCommitHistoryChart) {
            console.log('Using GitCommitHistoryChart module');
            // The chart module will handle everything
            return;
        }

        // Fallback to legacy implementation if the module is not available
        console.warn('GitCommitHistoryChart module not available, using legacy implementation');

        // Get elements
        const canvas = document.getElementById('commit-history-chart');
        const error = document.getElementById('commit-chart-error');

        if (!canvas) {
            console.error('Chart canvas not found');
            return;
        }

        // Hide error and prepare canvas
        if (error) error.style.display = 'none';
        if (canvas) canvas.style.display = 'block';

        try {
            // Clean up existing chart
            this.cleanupChart();

            // Fetch commit history data
            let commitData;

            if (window.GitRepositoryManager) {
                try {
                    commitData = await GitRepositoryManager.fetchCommitHistory(repoName);
                    console.log('Commit history data:', commitData);
                } catch (apiError) {
                    console.error('Error fetching commit history:', apiError);
                    throw new Error('Failed to fetch commit history data');
                }
            } else {
                throw new Error('GitRepositoryManager not available');
            }

            // Check if we have valid commit data
            if (!commitData || !commitData.commits || !Array.isArray(commitData.commits)) {
                throw new Error('Invalid commit history data');
            }

            // Check if we have any commits
            if (commitData.commits.length === 0) {
                // Show message for no commits
                if (loading) loading.style.display = 'none';
                if (error) {
                    error.textContent = 'No commits found for this repository';
                    error.style.display = 'block';
                }
                return;
            }

            // Prepare data for chart
            const chartData = this.prepareCommitDataForChart(commitData.commits);

            // Create chart
            this.renderCommitChart(canvas, chartData);

            // Hide loading, show canvas
            if (loading) loading.style.display = 'none';
            if (canvas) canvas.style.display = 'block';

        } catch (error) {
            console.error('Error displaying commit history:', error);

            // Show error message
            if (loading) loading.style.display = 'none';
            if (canvas) canvas.style.display = 'none';

            const errorElement = document.getElementById('commit-chart-error');
            if (errorElement) {
                errorElement.textContent = error.message || 'Failed to load commit history';
                errorElement.style.display = 'block';
            }

            // Create sample data chart as fallback
            this.createSampleCommitChart();
        }
    },

    /**
     * Create a sample commit chart as fallback
     */
    createSampleCommitChart() {
        const canvas = document.getElementById('commit-history-chart');
        if (!canvas) return;

        // Create sample data
        const labels = [];
        const data = [];

        // Generate dates for the last 30 days
        const today = new Date();
        for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));

            // Generate random commit count (0-10)
            data.push(Math.floor(Math.random() * 10));
        }

        // Create chart data
        const chartData = {
            labels: labels,
            datasets: [{
                label: 'Commits',
                data: data,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };

        // Render chart
        this.renderCommitChart(canvas, chartData);

        // Show canvas
        canvas.style.display = 'block';

        // Show note about sample data
        const errorElement = document.getElementById('commit-chart-error');
        if (errorElement) {
            errorElement.textContent = 'Showing sample data (API data unavailable)';
            errorElement.className = 'text-center text-warning mt-2';
            errorElement.style.display = 'block';
        }
    },

    /**
     * Prepare commit data for chart
     * @param {Array} commits - Commit objects
     * @param {number} days - Number of days to include
     * @returns {Object} Chart data
     */
    prepareCommitDataForChart(commits, days = 30) {
        // Create date range map
        const commitsByDate = {};
        const dateFormat = new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: 'numeric'
        });

        // Create date range for past 30 days
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        // Initialize all dates with 0 count
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            const dateKey = d.toISOString().split('T')[0];
            const displayDate = dateFormat.format(d);
            commitsByDate[dateKey] = {
                count: 0,
                displayDate
            };
        }

        // Count commits by date
        for (const commit of commits) {
            if (commit.date) {
                const dateKey = commit.date.split('T')[0];
                if (commitsByDate[dateKey]) {
                    commitsByDate[dateKey].count++;
                }
            }
        }

        // Convert to arrays for Chart.js
        const labels = [];
        const counts = [];

        // Sort dates chronologically
        const sortedDates = Object.keys(commitsByDate).sort();
        for (const dateKey of sortedDates) {
            labels.push(commitsByDate[dateKey].displayDate);
            counts.push(commitsByDate[dateKey].count);
        }

        return {
            labels,
            datasets: [{
                label: 'Commits',
                data: counts,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
    },

    /**
     * Render the commit history chart
     * @param {HTMLElement} canvas - Canvas element
     * @param {Object} chartData - Chart data
     */
    renderCommitChart(canvas, chartData) {
        if (!canvas) {
            console.error('Canvas element not found');
            return;
        }

        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not available');
            return;
        }

        // Clean up existing chart
        this.cleanupChart();

        // Create new chart
        this.state.chartInstance = new Chart(canvas, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                return `${value} commit${value !== 1 ? 's' : ''}`;
                            }
                        }
                    }
                }
            }
        });

        console.log('Commit history chart created');
    }
};

// Expose the module globally
window.GitRepositoryModal = GitRepositoryModal;

/**
 * Show repository details
 * @param {string} repoName - Repository name
 */
showRepositoryDetails(repoName) {
    // Get elements
    const loadingElement = document.getElementById('repository-details-loading');
    const contentElement = document.getElementById('repository-details-content');
    const detailsCard = document.getElementById('repository-details-card');
    const noRepoSelected = document.getElementById('no-repository-selected');

    // Hide loading spinner and show content
    if (loadingElement) loadingElement.style.display = 'none';
    if (contentElement) contentElement.style.display = 'block';
    if (detailsCard) detailsCard.style.display = 'block';
    if (noRepoSelected) noRepoSelected.style.display = 'none';

    // Update repository name in header
    const repoNameElement = document.getElementById('repo-name');
    if (repoNameElement) repoNameElement.textContent = repoName;

    // Initialize commit history chart
    if (window.GitCommitHistoryChart) {
        window.GitCommitHistoryChart.initChart(repoName);
    } else {
        console.error('GitCommitHistoryChart module is not available');
        const errorElement = document.getElementById('commit-chart-error');
        if (errorElement) {
            errorElement.textContent = 'Chart module is not available';
            errorElement.style.display = 'block';
        }
    }
}


