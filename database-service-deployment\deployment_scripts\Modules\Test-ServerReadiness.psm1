# Test Server Readiness Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
# Import UI module
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force
# Import SSHManager module
Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force

# Define a helper function to execute SSH commands directly
function Invoke-DirectSSHCommand {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$Command,

        [Parameter(Mandatory=$true)]
        [string]$HostName,

        [Parameter(Mandatory=$true)]
        [string]$User,

        [Parameter(Mandatory=$false)]
        [int]$Port = 22,

        [Parameter(Mandatory=$true)]
        [string]$KeyPath,

        [Parameter(Mandatory=$false)]
        [switch]$Silent
    )

    try {
        # Prepare SSH arguments
        $sshArgs = @()

        # Add identity file
        if (Test-Path -Path $KeyPath) {
            $sshArgs += @("-i", "$KeyPath")
        } else {
            if (-not $Silent) {
                Write-Log -Message "SSH key not found at path: $KeyPath" -Level "Warning" -Component "SSH"
            }
            return @{
                Output = $null
                Error = "SSH key not found"
                ExitCode = -1
                Success = $false
            }
        }

        # Add port if not default
        if ($Port -ne 22) {
            $sshArgs += @("-p", "$Port")
        }

        # Add common options
        $sshArgs += @(
            "-o", "StrictHostKeyChecking=no",
            "-o", "BatchMode=yes",
            "-o", "ConnectTimeout=10"
        )

        # Add destination and command
        $sshArgs += @("${User}@${HostName}", "$Command")

        # Create temporary files for output and error
        $outputFile = [System.IO.Path]::GetTempFileName()
        $errorFile = [System.IO.Path]::GetTempFileName()

        # Execute the command
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -NoNewWindow -PassThru -Wait -RedirectStandardOutput $outputFile -RedirectStandardError $errorFile

        # Read output and error
        $output = Get-Content -Path $outputFile -Raw -ErrorAction SilentlyContinue
        $errorOutput = Get-Content -Path $errorFile -Raw -ErrorAction SilentlyContinue

        # Clean up temporary files
        Remove-Item -Path $outputFile -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $errorFile -Force -ErrorAction SilentlyContinue

        return @{
            Output = $output
            Error = $errorOutput
            ExitCode = $process.ExitCode
            Success = ($process.ExitCode -eq 0)
        }
    } catch {
        if (-not $Silent) {
            Write-Log -Message "Exception during SSH command execution: $_" -Level "Error" -Component "SSH"
        }
        return @{
            Output = $null
            Error = $_.Exception.Message
            ExitCode = -1
            Success = $false
        }
    }
}

function Test-ServerReadiness {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }

    # Use UI module functions for consistent display
    Show-MenuTitle -Title "Test Server Readiness"
    #Show-MenuDivider
    Write-Log -Message " " -Level "UI"

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Log -Message "Configuration is not loaded. Loading configuration..." -Level "UI" -ForegroundColor Yellow
        # Try to load configuration
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        Write-Log -Message "Loading configuration from $configPath" -Level "UI" -ForegroundColor Yellow

        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Log -Message "Configuration loaded successfully." -Level "UI" -ForegroundColor Green
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "UI" -ForegroundColor Red
            Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
            # Don't try to call Show-MainMenu here - just return
            return
        }
    }

    # Check if SSH configuration exists
    if ($null -eq $script:Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        # Don't try to call Show-MainMenu here - just return
        return
    }

    Write-Log -Message "Testing connectivity to ${script:Config.ssh.host}..." -Level "UI" -ForegroundColor Cyan

    # Check if git.chcit.org is reachable
    $checkServer = $true
    if ($script:Config.ssh.host -eq "git.chcit.org") {
        Write-Log -Message "Checking if git.chcit.org is reachable..." -Level "UI" -ForegroundColor Cyan
        try {
            # Use a more PowerShell-friendly way to check connectivity
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            try {
                # Set a timeout of 5 seconds
                $connectionResult = $tcpClient.BeginConnect($script:Config.ssh.host, $script:Config.ssh.port, $null, $null)
                $waitResult = $connectionResult.AsyncWaitHandle.WaitOne(5000, $false)
                $pingResult = New-Object PSObject -Property @{
                    TcpTestSucceeded = $waitResult -and $tcpClient.Connected
                }
            } catch {
                $pingResult = New-Object PSObject -Property @{
                    TcpTestSucceeded = $false
                }
            } finally {
                if ($tcpClient.Connected) { $tcpClient.Close() }
                $tcpClient.Dispose()
            }
            if ($pingResult.TcpTestSucceeded) {
                Write-Log -Message "git.chcit.org is reachable." -Level "UI" -ForegroundColor Green
            } else {
                Write-Log -Message "git.chcit.org is not reachable!" -Level "UI" -ForegroundColor Red
                $checkServer = $false
            }
        } catch {
            Write-Log -Message "Error checking git.chcit.org: $_" -Level "UI" -ForegroundColor Red
            $checkServer = $false
        }
    }

    # Variable to track overall server readiness
    $serverReady = $false

    if ($checkServer) {
        Write-Log -Message "Testing SSH Connection to ${script:Config.ssh.host}..." -Level "UI" -ForegroundColor Cyan
        $connected = $false
        try {
            # Import and use the Test-SSHConnection module
            Write-Log -Message "Importing Test-SSHConnection module..." -Level "Debug" -Component "ServerReadinessTest"
            Import-Module -Name "$PSScriptRoot\Test-SSHConnection.psm1" -Force

            # Display SSH connection details for debugging
            Write-Log -Message "SSH connection details:" -Level "Debug" -Component "ServerReadinessTest"
            Write-Log -Message "Host: $($script:Config.ssh.host)" -Level "Debug" -Component "ServerReadinessTest"
            Write-Log -Message "User: $($script:Config.ssh.username)" -Level "Debug" -Component "ServerReadinessTest"
            Write-Log -Message "Port: $($script:Config.ssh.port)" -Level "Debug" -Component "ServerReadinessTest"
            Write-Log -Message "Key Path: $($script:Config.ssh.local_key_path)" -Level "Debug" -Component "ServerReadinessTest"

            # Check if key file exists
            if (-not (Test-Path -Path $script:Config.ssh.local_key_path)) {
                Write-Log -Message "SSH key file not found at: $($script:Config.ssh.local_key_path)" -Level "UI" -ForegroundColor Red
                Write-Log -Message "Please make sure the SSH key file exists." -Level "UI" -ForegroundColor Yellow
                $connected = $false
            } else {
                # Use the Test-SSHConnection function
                Write-Log -Message "Testing SSH connection..." -Level "UI" -ForegroundColor Cyan
                try {
                    # First try using Invoke-SSHCommand directly if available
                    $directTestSuccessful = $false

                    if (Get-Command -Name Invoke-SSHCommand -ErrorAction SilentlyContinue) {
                        Write-Log -Message "Using Invoke-SSHCommand for SSH test..." -Level "Debug" -Component "ServerReadinessTest"
                        $result = Invoke-SSHCommand -Command "echo 'Connection successful'" -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                        if ($result.Success -and $result.Output -match "Connection successful") {
                            $connected = $true
                            $directTestSuccessful = $true
                            Write-Log -Message "SSH connection successful using Invoke-SSHCommand!" -Level "Debug" -Component "ServerReadinessTest"
                        }
                    }

                    # If direct test wasn't successful, try Test-SSHConnection
                    if (-not $directTestSuccessful) {
                        Write-Log -Message "Using Test-SSHConnection module..." -Level "Debug" -Component "ServerReadinessTest"
                        # Call Test-SSHConnection directly with parameters
                        $connected = Test-SSHConnection -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path
                    }

                    # If both methods failed, try direct SSH command as last resort
                    if (-not $connected) {
                        Write-Log -Message "SSH connection failed. Trying direct SSH command..." -Level "Debug" -Component "ServerReadinessTest"

                        # Fallback to direct SSH command
                        $sshArgs = @()

                        # Add identity file
                        if (Test-Path -Path $script:Config.ssh.local_key_path) {
                            $sshArgs += @("-i", "$($script:Config.ssh.local_key_path)")
                        }

                        # Add port if not default
                        if ($script:Config.ssh.port -ne 22) {
                            $sshArgs += @("-p", "$($script:Config.ssh.port)")
                        }

                        # Add common options
                        $sshArgs += @(
                            "-o", "StrictHostKeyChecking=no",
                            "-o", "BatchMode=yes",
                            "-o", "ConnectTimeout=10"
                        )

                        # Add destination and command
                        $sshArgs += @("$($script:Config.ssh.username)@$($script:Config.ssh.host)", "echo 'CONNECTION_OK'")

                        # Create temporary files for output and error
                        $outputFile = [System.IO.Path]::GetTempFileName()
                        $errorFile = [System.IO.Path]::GetTempFileName()

                        # Execute the command
                        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -NoNewWindow -PassThru -Wait -RedirectStandardOutput $outputFile -RedirectStandardError $errorFile

                        # Read output and error
                        $output = Get-Content -Path $outputFile -Raw -ErrorAction SilentlyContinue
                        $errorOutput = Get-Content -Path $errorFile -Raw -ErrorAction SilentlyContinue

                        # Clean up temporary files
                        Remove-Item -Path $outputFile -Force -ErrorAction SilentlyContinue
                        Remove-Item -Path $errorFile -Force -ErrorAction SilentlyContinue

                        if ($process.ExitCode -eq 0 -and $output -match "CONNECTION_OK") {
                            $connected = $true
                            Write-Log -Message "Direct SSH connection successful!" -Level "Debug" -Component "ServerReadinessTest"
                        } else {
                            Write-Log -Message "Direct SSH connection failed with exit code: $($process.ExitCode)" -Level "Debug" -Component "ServerReadinessTest"
                            if (-not [string]::IsNullOrEmpty($errorOutput)) {
                                Write-Log -Message "Error output: $errorOutput" -Level "Debug" -Component "ServerReadinessTest"
                            }
                        }
                    }
                } catch {
                    Write-Log -Message "Exception during SSH connection test: $_" -Level "UI" -ForegroundColor Red
                    $connected = $false
                }
            }

            # Clear any previous SSH connection messages to avoid confusion
            Write-Host ""

            if ($connected) {
                Write-Log -Message "SSH connection successful!" -Level "UI" -ForegroundColor Green
                Write-Log -Message "User: $($script:Config.ssh.username)" -Level "UI" -ForegroundColor White

                # Now perform comprehensive server readiness checks
                Show-MenuDivider
                Write-Log -Message "Performing server readiness checks..." -Level "UI" -ForegroundColor Cyan
                Show-MenuDivider

                # 1. Check disk space
                Write-Log -Message "Checking disk space..." -Level "UI" -ForegroundColor Cyan
                $diskSpaceCmd = "df -h | grep -E '/$|/home'"
                $diskSpaceResult = Invoke-DirectSSHCommand -Command $diskSpaceCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                if ($diskSpaceResult.Success) {
                    Write-Log -Message "Disk space information:" -Level "UI" -ForegroundColor White
                    $diskSpaceLines = $diskSpaceResult.Output -split "`n"
                    foreach ($line in $diskSpaceLines) {
                        if ($line.Trim()) {
                            Write-Log -Message "  $line" -Level "UI" -ForegroundColor White

                            # Check if disk usage is over 90%
                            if ($line -match "(\d+)%" -and [int]$Matches[1] -gt 90) {
                                Write-Log -Message "  WARNING: Disk usage is over 90%!" -Level "UI" -ForegroundColor Yellow
                            }
                        }
                    }
                } else {
                    Write-Log -Message "Failed to check disk space: $($diskSpaceResult.Error)" -Level "UI" -ForegroundColor Red
                }

                # 2. Check if required directories exist
                Write-Log -Message "`nChecking required directories..." -Level "UI" -ForegroundColor Cyan

                # Get deployment directory from config or use default
                $deploymentDir = "/opt/database-service"
                if ($null -ne $script:Config.deployment -and $null -ne $script:Config.deployment.remote_path) {
                    $deploymentDir = $script:Config.deployment.remote_path
                }

                $dirCheckCmd = "if [ -d '$deploymentDir' ]; then echo 'EXISTS'; else echo 'MISSING'; fi"
                $dirCheckResult = Invoke-DirectSSHCommand -Command $dirCheckCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                if ($dirCheckResult.Success) {
                    if ($dirCheckResult.Output.Trim() -eq "EXISTS") {
                        Write-Log -Message "Deployment directory '$deploymentDir' exists." -Level "UI" -ForegroundColor Green

                        # Check permissions
                        $permCheckCmd = "ls -ld '$deploymentDir'"
                        $permCheckResult = Invoke-DirectSSHCommand -Command $permCheckCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                        if ($permCheckResult.Success) {
                            Write-Log -Message "Directory permissions:" -Level "UI" -ForegroundColor White
                            Write-Log -Message "  $($permCheckResult.Output.Trim())" -Level "UI" -ForegroundColor White

                            # Check if current user has write access
                            $writeCheckCmd = "if [ -w '$deploymentDir' ]; then echo 'WRITABLE'; else echo 'NOT_WRITABLE'; fi"
                            $writeCheckResult = Invoke-DirectSSHCommand -Command $writeCheckCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                            if ($writeCheckResult.Success) {
                                if ($writeCheckResult.Output.Trim() -eq "WRITABLE") {
                                    Write-Log -Message "  Directory is writable by current user." -Level "UI" -ForegroundColor Green
                                } else {
                                    Write-Log -Message "  WARNING: Directory is not writable by current user!" -Level "UI" -ForegroundColor Yellow
                                }
                            }
                        }
                    } else {
                        Write-Log -Message "Deployment directory '$deploymentDir' does not exist!" -Level "UI" -ForegroundColor Yellow
                        Write-Log -Message "The directory will need to be created during deployment." -Level "UI" -ForegroundColor White

                        # Check if parent directory is writable
                        $parentDir = $deploymentDir.Substring(0, $deploymentDir.LastIndexOf('/'))
                        $parentWriteCmd = "if [ -d '$parentDir' ] && [ -w '$parentDir' ]; then echo 'WRITABLE'; else echo 'NOT_WRITABLE'; fi"
                        $parentWriteResult = Invoke-DirectSSHCommand -Command $parentWriteCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                        if ($parentWriteResult.Success) {
                            if ($parentWriteResult.Output.Trim() -eq "WRITABLE") {
                                Write-Log -Message "  Parent directory '$parentDir' is writable." -Level "UI" -ForegroundColor Green
                            } else {
                                Write-Log -Message "  WARNING: Parent directory '$parentDir' is not writable!" -Level "UI" -ForegroundColor Yellow
                            }
                        }
                    }
                } else {
                    Write-Log -Message "Failed to check directory existence: $($dirCheckResult.Error)" -Level "UI" -ForegroundColor Red
                }

                # 3. Check Git access and version
                Write-Log -Message "`nChecking Git availability..." -Level "UI" -ForegroundColor Cyan
                $gitVersionCmd = "git --version"
                $gitVersionResult = Invoke-DirectSSHCommand -Command $gitVersionCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                if ($gitVersionResult.Success) {
                    Write-Log -Message "Git is available: $($gitVersionResult.Output.Trim())`n" -Level "UI" -ForegroundColor Green
                } else {
                    Write-Log -Message "WARNING: Git is not available on the server!`n" -Level "UI" -ForegroundColor Yellow
                }

                # 4. Check for required software
                Show-MenuDivider
                Write-Log -Message "Checking for required software..." -Level "UI" -ForegroundColor Cyan
                Show-MenuDivider

                # Load requirements.json
                $requirementsPath = Join-Path -Path $PSScriptRoot -ChildPath "..\requirements.json"
                try {
                    $requirementsContent = Get-Content -Path $requirementsPath -Raw -ErrorAction Stop
                    $requirements = ConvertFrom-Json -InputObject $requirementsContent -ErrorAction Stop
                    Write-Log -Message "Requirements loaded successfully." -Level "UI" -ForegroundColor Green
                } catch {
                    Write-Log -Message "Failed to load requirements: $_" -Level "UI" -ForegroundColor Red
                    Write-Log -Message "Using default software checks." -Level "UI" -ForegroundColor Yellow
                    $requirements = $null
                }

                # Track software check results
                $softwareChecksPassed = 0
                $softwareChecksTotal = 0
                $softwareChecksFailed = @()

                if ($null -ne $requirements) {
                    # Check compiler
                    Write-Log -Message "`nChecking compiler..." -Level "UI" -ForegroundColor Cyan
                    foreach ($compiler in $requirements.compiler.PSObject.Properties) {
                        $softwareChecksTotal++
                        $compilerInfo = $compiler.Value
                        $compilerName = $compiler.Name
                        $binary = $compilerInfo.binary
                        $versionCmd = $compilerInfo.version_command

                        Write-Log -Message "Checking $($compilerInfo.description)..." -Level "UI" -ForegroundColor White

                        # Check if compiler exists
                        $checkCmd = "which $binary 2>/dev/null || echo 'NOT_INSTALLED'"
                        $checkResult = Invoke-DirectSSHCommand -Command $checkCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                        if ($checkResult.Success -and $checkResult.Output.Trim() -ne "NOT_INSTALLED") {
                            # Check version
                            $versionResult = Invoke-DirectSSHCommand -Command $versionCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                            if ($versionResult.Success) {
                                $version = $versionResult.Output.Trim()
                                Write-Log -Message "  $compilerName version: $version" -Level "UI" -ForegroundColor Green

                                # Compare with minimum version if specified
                                if ($compilerInfo.minimum_version) {
                                    if ([System.Version]$version -ge [System.Version]$compilerInfo.minimum_version) {
                                        Write-Log -Message "  Version meets minimum requirement ($($compilerInfo.minimum_version))" -Level "UI" -ForegroundColor Green
                                        $softwareChecksPassed++
                                    } else {
                                        Write-Log -Message "  WARNING: Version does not meet minimum requirement ($($compilerInfo.minimum_version))" -Level "UI" -ForegroundColor Yellow
                                        $softwareChecksFailed += "$compilerName (version too old: $version < $($compilerInfo.minimum_version))"
                                    }
                                } else {
                                    $softwareChecksPassed++
                                }
                            } else {
                                Write-Log -Message "  $compilerName found but couldn't determine version" -Level "UI" -ForegroundColor Yellow
                                $softwareChecksPassed++  # Still count as passed if the tool exists
                            }
                        } else {
                            Write-Log -Message "  WARNING: $compilerName is not installed!" -Level "UI" -ForegroundColor Yellow
                            $softwareChecksFailed += "$compilerName (not installed)"
                        }
                    }

                    # Check build tools
                    Write-Log -Message "`nChecking build tools..." -Level "UI" -ForegroundColor Cyan
                    foreach ($tool in $requirements.build_tools.PSObject.Properties) {
                        $softwareChecksTotal++
                        $toolInfo = $tool.Value
                        $toolName = $tool.Name
                        $checkCmd = $toolInfo.check_command

                        if (-not $checkCmd -and $toolInfo.binary) {
                            $checkCmd = "which $($toolInfo.binary) 2>/dev/null || echo 'NOT_INSTALLED'"
                        }

                        Write-Log -Message "Checking $($toolInfo.description)..." -Level "UI" -ForegroundColor White

                        $checkResult = Invoke-DirectSSHCommand -Command $checkCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                        if ($checkResult.Success -and $checkResult.Output.Trim() -ne "NOT_INSTALLED") {
                            if ($toolInfo.version_command) {
                                $versionResult = Invoke-DirectSSHCommand -Command $toolInfo.version_command -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                                if ($versionResult.Success) {
                                    $version = $versionResult.Output.Trim()
                                    Write-Log -Message "  $toolName version: $version" -Level "UI" -ForegroundColor Green

                                    if ($toolInfo.minimum_version) {
                                        try {
                                            if ([System.Version]$version -ge [System.Version]$toolInfo.minimum_version) {
                                                Write-Log -Message "  Version meets minimum requirement ($($toolInfo.minimum_version))" -Level "UI" -ForegroundColor Green
                                                $softwareChecksPassed++
                                            } else {
                                                Write-Log -Message "  WARNING: Version does not meet minimum requirement ($($toolInfo.minimum_version))" -Level "UI" -ForegroundColor Yellow
                                                $softwareChecksFailed += "$toolName (version too old: $version < $($toolInfo.minimum_version))"
                                            }
                                        } catch {
                                            Write-Log -Message "  Could not compare versions, assuming compatible" -Level "UI" -ForegroundColor Yellow
                                            $softwareChecksPassed++
                                        }
                                    } else {
                                        $softwareChecksPassed++
                                    }
                                } else {
                                    Write-Log -Message "  $toolName found but couldn't determine version" -Level "UI" -ForegroundColor Yellow
                                    $softwareChecksPassed++  # Still count as passed if the tool exists
                                }
                            } else {
                                Write-Log -Message "  $toolName is installed" -Level "UI" -ForegroundColor Green
                                $softwareChecksPassed++
                            }
                        } else {
                            Write-Log -Message "  WARNING: $toolName is not installed!" -Level "UI" -ForegroundColor Yellow
                            $softwareChecksFailed += "$toolName (not installed)"
                        }
                    }

                    # Check libraries
                    Write-Log -Message "`nChecking libraries..." -Level "UI" -ForegroundColor Cyan
                    foreach ($lib in $requirements.libraries.PSObject.Properties) {
                        $softwareChecksTotal++
                        $libInfo = $lib.Value
                        $libName = $lib.Name
                        Write-Log -Message " Checking $($libInfo.description)..." -Level "UI" -ForegroundColor White
                        $libCheck = Invoke-DirectSSHCommand -Command $libInfo.check_command -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true
                        if ($libCheck.Success -and $libCheck.Output -match "Status: install ok installed") {
                            Write-Log -Message "  $libName is installed" -Level "UI" -ForegroundColor Green
                            $softwareChecksPassed++
                            # Additional checks for headers or versions
                            if ($libInfo.header_check) {
                                $headerResult = Invoke-DirectSSHCommand -Command $libInfo.header_check -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true
                                if ($headerResult.Success -and $headerResult.Output -match "found") {
                                    Write-Log -Message "  Header files found" -Level "UI" -ForegroundColor Green
                                } else {
                                    Write-Log -Message "  WARNING: Header files missing" -Level "UI" -ForegroundColor Yellow
                                }
                            }
                        } else {
                            Write-Log -Message "  $libName is NOT installed" -Level "UI" -ForegroundColor Red
                            $softwareChecksFailed += "$libName (not installed)"
                        }
                    }

                    # Check system components
                    Write-Log -Message "`nChecking system components..." -Level "UI" -ForegroundColor Cyan
                    foreach ($component in $requirements.system.PSObject.Properties) {
                        $softwareChecksTotal++
                        $componentInfo = $component.Value
                        $componentName = $component.Name
                        $binary = $componentInfo.binary

                        Write-Log -Message "Checking $($componentInfo.description)..." -Level "UI" -ForegroundColor White

                        # Check if binary exists
                        $checkCmd = "which $binary 2>/dev/null || echo 'NOT_INSTALLED'"
                        $checkResult = Invoke-DirectSSHCommand -Command $checkCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                        if ($checkResult.Success -and $checkResult.Output.Trim() -ne "NOT_INSTALLED") {
                            # Check version
                            $versionResult = Invoke-DirectSSHCommand -Command $componentInfo.version_command -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                            if ($versionResult.Success) {
                                $version = $versionResult.Output.Trim()
                                Write-Log -Message "  $componentName version: $version" -Level "UI" -ForegroundColor Green

                                # Compare with minimum version if specified
                                if ($componentInfo.minimum_version) {
                                    try {
                                        if ([System.Version]$version -ge [System.Version]$componentInfo.minimum_version) {
                                            Write-Log -Message "  Version meets minimum requirement ($($componentInfo.minimum_version))" -Level "UI" -ForegroundColor Green
                                            $softwareChecksPassed++
                                        } else {
                                            Write-Log -Message "  WARNING: Version does not meet minimum requirement ($($componentInfo.minimum_version))" -Level "UI" -ForegroundColor Yellow
                                            $softwareChecksFailed += "$componentName (version too old: $version < $($componentInfo.minimum_version))"
                                        }
                                    } catch {
                                        Write-Log -Message "  Could not compare versions, assuming compatible" -Level "UI" -ForegroundColor Yellow
                                        $softwareChecksPassed++
                                    }
                                } else {
                                    $softwareChecksPassed++
                                }
                            } else {
                                Write-Log -Message "  $componentName found but couldn't determine version" -Level "UI" -ForegroundColor Yellow
                                $softwareChecksPassed++  # Still count as passed if the component exists
                            }

                            # Check service if specified
                            if ($componentInfo.service_check) {
                                $serviceResult = Invoke-DirectSSHCommand -Command $componentInfo.service_check -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                                # More robust check for service status - check for multiple active indicators
                                if ($serviceResult.Success -and ($serviceResult.Output -match "active" -or $serviceResult.Output -match "running" -or $serviceResult.Output -match "online")) {
                                    Write-Log -Message "  Service is running" -Level "UI" -ForegroundColor Green
                                } else {
                                    # Add detailed diagnostics
                                    Write-Log -Message "  WARNING: Service is not running!" -Level "UI" -ForegroundColor Yellow
                                    
                                    # Try to get more detailed service status information
                                    if ($componentName -eq "postgresql_server") {
                                        $detailedStatusCmd = "systemctl status postgresql 2>/dev/null || service postgresql status 2>/dev/null || pg_isready 2>/dev/null || echo 'Could not determine detailed status'"
                                        $detailedResult = Invoke-DirectSSHCommand -Command $detailedStatusCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true
                                        
                                        if ($detailedResult.Success) {
                                            # Check if detailed results indicate the service is actually running
                                            if ($detailedResult.Output -match "accepting connections" -or $detailedResult.Output -match "active \(running\)") {
                                                Write-Log -Message "  Service appears to be running based on detailed check" -Level "UI" -ForegroundColor Green
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            Write-Log -Message "  WARNING: $componentName is not installed!" -Level "UI" -ForegroundColor Yellow
                            $softwareChecksFailed += "$componentName (not installed)"
                        }
                    }

                    # Display software check summary
                    Write-Log -Message "Software check summary:`n" -Level "UI" -ForegroundColor Cyan
                    Write-Log -Message "  Passed: $softwareChecksPassed of $softwareChecksTotal checks" -Level "UI" -ForegroundColor $(if ($softwareChecksPassed -eq $softwareChecksTotal) { "Green" } else { "Yellow" })

                    if ($softwareChecksFailed.Count -gt 0) {
                        Write-Log -Message "Failed software checks:" -Level "UI" -ForegroundColor Yellow
                        foreach ($failure in $softwareChecksFailed) {
                            Write-Log -Message "  - $failure" -Level "UI" -ForegroundColor Yellow
                        }
                    }
                } else {
                    # Fallback to basic checks if requirements.json couldn't be loaded
                    # Check for PostgreSQL client
                    $pgVersionCmd = "psql --version 2>/dev/null || echo 'NOT_INSTALLED'"
                    $pgVersionResult = Invoke-SSHCommand -Command $pgVersionCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                    if ($pgVersionResult.Success) {
                        if ($pgVersionResult.Output.Trim() -ne "NOT_INSTALLED") {
                            Write-Log -Message "PostgreSQL client is available: $($pgVersionResult.Output.Trim())`n" -Level "UI" -ForegroundColor Green
                        } else {
                            Write-Log -Message "WARNING: PostgreSQL client is not installed!`n" -Level "UI" -ForegroundColor Yellow
                        }
                    }

                    # Check for GCC
                    $gccVersionCmd = "g++ --version 2>/dev/null || echo 'NOT_INSTALLED'"
                    $gccVersionResult = Invoke-SSHCommand -Command $gccVersionCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                    if ($gccVersionResult.Success) {
                        if ($gccVersionResult.Output.Trim() -ne "NOT_INSTALLED") {
                            Write-Log -Message "GCC is available: $($gccVersionResult.Output.Split("`n")[0].Trim())`n" -Level "UI" -ForegroundColor Green
                        } else {
                            Write-Log -Message "WARNING: GCC is not installed!`n" -Level "UI" -ForegroundColor Yellow
                        }
                    }

                    # Check for CMake
                    $cmakeVersionCmd = "cmake --version 2>/dev/null || echo 'NOT_INSTALLED'"
                    $cmakeVersionResult = Invoke-SSHCommand -Command $cmakeVersionCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                    if ($cmakeVersionResult.Success) {
                        if ($cmakeVersionResult.Output.Trim() -ne "NOT_INSTALLED") {
                            Write-Log -Message "CMake is available: $($cmakeVersionResult.Output.Split("`n")[0].Trim())`n" -Level "UI" -ForegroundColor Green
                        } else {
                            Write-Log -Message "WARNING: CMake is not installed!`n" -Level "UI" -ForegroundColor Yellow
                        }
                    }
                }

                # 5. Check system resources
                Write-Log -Message "`n" -Level "UI" -ForegroundColor White
                Show-MenuDivider
                Write-Log -Message "Checking system resources..." -Level "UI" -ForegroundColor Cyan
                Show-MenuDivider
                # Check memory
                $memoryCmd = "free -h"
                $memoryResult = Invoke-DirectSSHCommand -Command $memoryCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                if ($memoryResult.Success) {
                    Write-Log -Message "Memory information:" -Level "UI" -ForegroundColor White
                    $memoryLines = $memoryResult.Output -split "`n"
                    foreach ($line in $memoryLines) {
                        if ($line.Trim()) {
                            Write-Log -Message "  $line" -Level "UI" -ForegroundColor White
                        }
                    }
                }

                # Check CPU load
                $loadCmd = "uptime"
                $loadResult = Invoke-DirectSSHCommand -Command $loadCmd -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                if ($loadResult.Success) {
                    Write-Log -Message "System uptime and load:" -Level "UI" -ForegroundColor White
                    Write-Log -Message "  $($loadResult.Output.Trim())`n" -Level "UI" -ForegroundColor White
                }

                # 6. Final readiness assessment
                Show-MenuDivider
                Write-Log -Message "Server readiness assessment:" -Level "UI" -ForegroundColor Cyan
                Show-MenuDivider

                # Set server ready flag to true - we'll set it to false if any critical checks fail
                $serverReady = $true

                # Display summary of findings
                Write-Log -Message "Server connectivity: PASSED" -Level "UI" -ForegroundColor Green

                # Check if deployment directory exists or can be created
                if ($dirCheckResult.Success -and $dirCheckResult.Output.Trim() -eq "EXISTS") {
                    if ($writeCheckResult.Success -and $writeCheckResult.Output.Trim() -eq "WRITABLE") {
                        Write-Log -Message "Deployment directory: PASSED" -Level "UI" -ForegroundColor Green
                    } else {
                        Write-Log -Message "Deployment directory: WARNING - Not writable" -Level "UI" -ForegroundColor Yellow
                        $serverReady = $false
                    }
                } else {
                    if ($parentWriteResult.Success -and $parentWriteResult.Output.Trim() -eq "WRITABLE") {
                        Write-Log -Message "Deployment directory: WARNING - Will be created during deployment" -Level "UI" -ForegroundColor Yellow
                    } else {
                        Write-Log -Message "Deployment directory: FAILED - Cannot be created" -Level "UI" -ForegroundColor Red
                        $serverReady = $false
                    }
                }

                # Check Git availability
                if ($gitVersionResult.Success -and $gitVersionResult.Output -notmatch "NOT_INSTALLED") {
                    Write-Log -Message "Git availability: PASSED" -Level "UI" -ForegroundColor Green
                } else {
                    Write-Log -Message "Git availability: FAILED" -Level "UI" -ForegroundColor Red
                    $serverReady = $false
                }

                # Check required software
                if ($null -ne $requirements -and $softwareChecksTotal -gt 0) {
                    $softwarePassRate = [math]::Round(($softwareChecksPassed / $softwareChecksTotal) * 100)

                    if ($softwareChecksPassed -eq $softwareChecksTotal) {
                        Write-Log -Message "Required software: PASSED ($softwarePassRate%)" -Level "UI" -ForegroundColor Green
                    } elseif ($softwarePassRate -ge 80) {
                        Write-Log -Message "Required software: WARNING - Some optional components missing ($softwarePassRate%)" -Level "UI" -ForegroundColor Yellow
                        # Don't fail the server readiness check for minor software issues
                    } else {
                        Write-Log -Message "Required software: FAILED - Critical components missing ($softwarePassRate%)" -Level "UI" -ForegroundColor Red
                        $serverReady = $false
                    }

                    # Check for critical software failures
                    $criticalFailures = @()
                    foreach ($failure in $softwareChecksFailed) {
                        # Check if this is a critical component
                        if ($failure -match "gcc|postgresql|cmake|boost") {
                            $criticalFailures += $failure
                        }
                    }

                    if ($criticalFailures.Count -gt 0) {
                        Write-Log -Message "Critical software missing:" -Level "UI" -ForegroundColor Red
                        foreach ($critical in $criticalFailures) {
                            Write-Log -Message "  - $critical" -Level "UI" -ForegroundColor Red
                        }
                        $serverReady = $false
                    }
                }

                # Final verdict
                Show-MenuDivider
                if ($serverReady) {
                    Write-Log -Message "FINAL ASSESSMENT: Server is READY for deployment!" -Level "UI" -ForegroundColor Green
                } else {
                    Write-Log -Message "FINAL ASSESSMENT: Server is NOT READY for deployment!" -Level "UI" -ForegroundColor Red
                    Write-Log -Message "Please address the issues above before proceeding with deployment." -Level "UI" -ForegroundColor Yellow
                }
            } else {
                Write-Log -Message "SSH connection failed!" -Level "UI" -ForegroundColor Red
            }
        } catch {
            Write-Log -Message "Exception during SSH connection test: $_" -Level "UI" -ForegroundColor Red

            # Fallback to using Invoke-SSHCommand directly
            if (Get-Command -Name Invoke-SSHCommand -ErrorAction SilentlyContinue) {
                Write-Log -Message "Trying fallback SSH connection method..." -Level "UI" -ForegroundColor Yellow
                $result = Invoke-DirectSSHCommand -Command "echo 'Connection test'" -HostName $script:Config.ssh.host -User $script:Config.ssh.username -Port $script:Config.ssh.port -KeyPath $script:Config.ssh.local_key_path -Silent:$true

                if ($result.Success) {
                    Write-Log -Message "SSH connection successful!" -Level "UI" -ForegroundColor Green
                    Write-Log -Message "User: $($script:Config.ssh.username)" -Level "UI" -ForegroundColor White
                    # We could add the same server readiness checks here, but for simplicity, we'll skip that
                } else {
                    Write-Log -Message "SSH connection failed!" -Level "UI" -ForegroundColor Red
                    Write-Log -Message "Error: $($result.Error)" -Level "UI" -ForegroundColor White
                }
            } else {
                Write-Log -Message "Invoke-SSHCommand function not found. Cannot test server." -Level "Error" -Component "ServerReadinessTest"
            }
        }
    }

    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }

    Get-UserInput -Prompt "Press Enter to continue..." | Out-Null

    # Don't try to call Show-MainMenu here - just return
    return
}

# Export the function
Export-ModuleMember -Function Test-ServerReadiness
