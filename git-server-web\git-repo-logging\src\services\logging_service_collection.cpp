#include "logging_service.hpp"
#include "../api/api_server.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <regex>
#include <chrono>
#include <thread>
#include <filesystem>
#include <jsoncpp/json/json.h>

namespace fs = std::filesystem;

// Log collection methods
void LoggingService::startLogCollection() {
    if (running_) return;
    
    running_ = true;
    
    // Start collection thread
    collectionThread_ = std::thread([this]() {
        collectLogs();
    });
    
    std::cout << "Log collection started" << std::endl;
}

void LoggingService::stopLogCollection() {
    if (!running_) return;
    
    running_ = false;
    
    // Notify collection thread to stop
    collectionCV_.notify_one();
    
    // Wait for collection thread to finish
    if (collectionThread_.joinable()) {
        collectionThread_.join();
    }
    
    std::cout << "Log collection stopped" << std::endl;
}

void LoggingService::collectLogs() {
    while (running_) {
        try {
            // Collect logs from all sources
            collectSystemLogs();
            collectNginxLogs();
            collectGitLogs();
            collectAuthLogs();
            collectDashboardLogs();
            
            // Prune old logs
            pruneOldLogs();
            
            // Save logs to file periodically
            saveLogsToFile();
            
        } catch (const std::exception& e) {
            std::cerr << "Error collecting logs: " << e.what() << std::endl;
        }
        
        // Wait for next collection cycle or until stopped
        std::unique_lock<std::mutex> lock(logsMutex_);
        collectionCV_.wait_for(lock, std::chrono::seconds(10), [this]() { return !running_; });
    }
}

void LoggingService::collectSystemLogs() {
    try {
        // Check if system log file exists
        if (!fs::exists(systemLogPath_)) {
            return;
        }
        
        // Get last modification time
        auto lastModTime = fs::last_write_time(systemLogPath_);
        
        // Skip if file hasn't been modified since last read
        if (lastReadTimes_.find("system") != lastReadTimes_.end() && 
            lastReadTimes_["system"] >= lastModTime) {
            return;
        }
        
        // Open system log file
        std::ifstream file(systemLogPath_);
        if (!file.is_open()) {
            std::cerr << "Failed to open system log file: " << systemLogPath_ << std::endl;
            return;
        }
        
        // Read new lines
        std::string line;
        while (std::getline(file, line)) {
            // Parse and process log entry
            LogEntry entry = parseLogLine(line, "system");
            processLogEntry(entry);
        }
        
        // Update last read time
        lastReadTimes_["system"] = lastModTime;
        
    } catch (const std::exception& e) {
        std::cerr << "Error collecting system logs: " << e.what() << std::endl;
    }
}

void LoggingService::collectNginxLogs() {
    try {
        // Check if nginx log file exists
        if (!fs::exists(nginxLogPath_)) {
            return;
        }
        
        // Get last modification time
        auto lastModTime = fs::last_write_time(nginxLogPath_);
        
        // Skip if file hasn't been modified since last read
        if (lastReadTimes_.find("nginx") != lastReadTimes_.end() && 
            lastReadTimes_["nginx"] >= lastModTime) {
            return;
        }
        
        // Open nginx log file
        std::ifstream file(nginxLogPath_);
        if (!file.is_open()) {
            std::cerr << "Failed to open nginx log file: " << nginxLogPath_ << std::endl;
            return;
        }
        
        // Read new lines
        std::string line;
        while (std::getline(file, line)) {
            // Parse and process log entry
            LogEntry entry = parseLogLine(line, "nginx");
            processLogEntry(entry);
        }
        
        // Update last read time
        lastReadTimes_["nginx"] = lastModTime;
        
    } catch (const std::exception& e) {
        std::cerr << "Error collecting nginx logs: " << e.what() << std::endl;
    }
}

void LoggingService::collectGitLogs() {
    try {
        // Check if git log file exists
        if (!fs::exists(gitLogPath_)) {
            return;
        }
        
        // Get last modification time
        auto lastModTime = fs::last_write_time(gitLogPath_);
        
        // Skip if file hasn't been modified since last read
        if (lastReadTimes_.find("git") != lastReadTimes_.end() && 
            lastReadTimes_["git"] >= lastModTime) {
            return;
        }
        
        // Open git log file
        std::ifstream file(gitLogPath_);
        if (!file.is_open()) {
            std::cerr << "Failed to open git log file: " << gitLogPath_ << std::endl;
            return;
        }
        
        // Read new lines
        std::string line;
        while (std::getline(file, line)) {
            // Parse and process log entry
            LogEntry entry = parseLogLine(line, "git");
            processLogEntry(entry);
        }
        
        // Update last read time
        lastReadTimes_["git"] = lastModTime;
        
    } catch (const std::exception& e) {
        std::cerr << "Error collecting git logs: " << e.what() << std::endl;
    }
}

void LoggingService::collectAuthLogs() {
    try {
        // Check if auth log file exists
        if (!fs::exists(authLogPath_)) {
            return;
        }
        
        // Get last modification time
        auto lastModTime = fs::last_write_time(authLogPath_);
        
        // Skip if file hasn't been modified since last read
        if (lastReadTimes_.find("auth") != lastReadTimes_.end() && 
            lastReadTimes_["auth"] >= lastModTime) {
            return;
        }
        
        // Open auth log file
        std::ifstream file(authLogPath_);
        if (!file.is_open()) {
            std::cerr << "Failed to open auth log file: " << authLogPath_ << std::endl;
            return;
        }
        
        // Read new lines
        std::string line;
        while (std::getline(file, line)) {
            // Parse and process log entry
            LogEntry entry = parseLogLine(line, "auth");
            processLogEntry(entry);
        }
        
        // Update last read time
        lastReadTimes_["auth"] = lastModTime;
        
    } catch (const std::exception& e) {
        std::cerr << "Error collecting auth logs: " << e.what() << std::endl;
    }
}

void LoggingService::collectDashboardLogs() {
    try {
        // Check if dashboard log file exists
        if (!fs::exists(dashboardLogPath_)) {
            return;
        }
        
        // Get last modification time
        auto lastModTime = fs::last_write_time(dashboardLogPath_);
        
        // Skip if file hasn't been modified since last read
        if (lastReadTimes_.find("dashboard") != lastReadTimes_.end() && 
            lastReadTimes_["dashboard"] >= lastModTime) {
            return;
        }
        
        // Open dashboard log file
        std::ifstream file(dashboardLogPath_);
        if (!file.is_open()) {
            std::cerr << "Failed to open dashboard log file: " << dashboardLogPath_ << std::endl;
            return;
        }
        
        // Read new lines
        std::string line;
        while (std::getline(file, line)) {
            // Parse and process log entry
            LogEntry entry = parseLogLine(line, "dashboard");
            processLogEntry(entry);
        }
        
        // Update last read time
        lastReadTimes_["dashboard"] = lastModTime;
        
    } catch (const std::exception& e) {
        std::cerr << "Error collecting dashboard logs: " << e.what() << std::endl;
    }
}
