#!/bin/bash

# Simple metrics collector script
# This script collects basic server metrics and Git repository statistics

# Directory paths
DASHBOARD_DIR="/opt/git-dashboard"
DATA_DIR="$DASHBOARD_DIR/data"
HISTORY_DIR="$DATA_DIR/history"
METRICS_FILE="$DATA_DIR/metrics_history.json"

# Ensure directories exist
mkdir -p "$DATA_DIR"
mkdir -p "$HISTORY_DIR"

# Current timestamp
timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# System metrics - use very simple commands
# Default to 0 if commands fail
cpu_usage=0
mem_usage=0
load_avg=0
disk_usage=0

# Try to get CPU usage
if [ -f "/proc/stat" ]; then
    cpu_line=$(grep "^cpu " /proc/stat)
    if [ -n "$cpu_line" ]; then
        # Extract values from line
        set -- $cpu_line
        user=$2
        nice=$3
        system=$4
        idle=$5
        # Calculate CPU usage
        total=$((user + nice + system + idle))
        if [ $total -gt 0 ]; then
            cpu_usage=$(( (user + nice + system) * 100 / total ))
        fi
    fi
fi

# Try to get memory usage
mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
mem_free=$(grep MemFree /proc/meminfo | awk '{print $2}')
mem_buffers=$(grep Buffers /proc/meminfo | awk '{print $2}')
mem_cached=$(grep "^Cached:" /proc/meminfo | awk '{print $2}')
if [ -n "$mem_total" ] && [ -n "$mem_free" ] && [ -n "$mem_buffers" ] && [ -n "$mem_cached" ] && [ $mem_total -gt 0 ]; then
    mem_used=$((mem_total - mem_free - mem_buffers - mem_cached))
    mem_usage=$((mem_used * 100 / mem_total))
fi

# Try to get load average
if [ -f "/proc/loadavg" ]; then
    load_avg=$(cat /proc/loadavg | cut -d' ' -f1)
fi

# Try to get disk usage
df_line=$(df -P / | tail -1)
if [ -n "$df_line" ]; then
    disk_usage=$(echo "$df_line" | awk '{print $5}' | tr -d '%')
fi

# Git repository metrics
repo_path="/home/<USER>/repositories/project-tracker.git"
total_commits=0
active_branches=0
last_commit=""

if [ -d "$repo_path" ]; then
    if command -v git >/dev/null 2>&1; then
        # Get total commits if possible
        commits=$(cd "$repo_path" && git rev-list --all --count 2>/dev/null)
        if [ -n "$commits" ] && [ "$commits" -eq "$commits" ] 2>/dev/null; then
            total_commits=$commits
        fi
        
        # Get active branches if possible
        branches=$(cd "$repo_path" && git branch | wc -l 2>/dev/null)
        if [ -n "$branches" ] && [ "$branches" -eq "$branches" ] 2>/dev/null; then
            active_branches=$branches
        fi
        
        # Get last commit date if possible
        last_commit=$(cd "$repo_path" && git log -1 --format=%cd --date=iso8601 2>/dev/null || echo "")
    fi
fi

# Create metrics JSON object
metrics="{
    \"timestamp\": \"$timestamp\",
    \"system\": {
        \"cpu_usage\": $cpu_usage,
        \"mem_usage\": $mem_usage,
        \"load_avg\": $load_avg,
        \"disk_usage\": $disk_usage
    },
    \"git\": {
        \"total_commits\": $total_commits,
        \"active_branches\": $active_branches,
        \"last_commit\": \"$last_commit\"
    }
}"

# Create a temporary file
tmp_file=$(mktemp)

# Create or update metrics file
if [ -f "$METRICS_FILE" ]; then
    # Check if the file has valid JSON
    if grep -q "^\[" "$METRICS_FILE" && grep -q "\]$" "$METRICS_FILE"; then
        # Remove the closing bracket, add new metrics and closing bracket
        sed 's/\]$//' "$METRICS_FILE" > "$tmp_file"
        if grep -q "}$" "$tmp_file"; then
            # Add comma if not the first entry
            echo "," >> "$tmp_file"
        fi
        echo "$metrics" >> "$tmp_file"
        echo "]" >> "$tmp_file"
    else
        # Invalid JSON, create new file
        echo "[" > "$tmp_file"
        echo "$metrics" >> "$tmp_file"
        echo "]" >> "$tmp_file"
    fi
else
    # Create new file
    echo "[" > "$tmp_file"
    echo "$metrics" >> "$tmp_file"
    echo "]" >> "$tmp_file"
fi

# Atomically move temporary file to final location
mv "$tmp_file" "$METRICS_FILE"

# Save a copy to history directory with timestamp
history_file="$HISTORY_DIR/metrics_$(date -u +"%Y%m%d_%H%M%S").json"
cp "$METRICS_FILE" "$history_file"

# Clean up old history files (keep last 7 days)
find "$HISTORY_DIR" -name "metrics_*.json" -type f -mtime +7 -delete 2>/dev/null

echo "Metrics collection completed at $(date -u +"%Y-%m-%dT%H:%M:%SZ")"
