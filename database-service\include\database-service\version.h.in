#pragma once

#define DATABASE_SERVICE_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define DATABASE_SERVICE_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define DATABASE_SERVICE_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define DATABASE_SERVICE_VERSION "@PROJECT_VERSION@"

namespace dbservice {

/**
 * @brief Get the version string
 * @return Version string in format "major.minor.patch"
 */
constexpr const char* getVersionString() noexcept {
    return DATABASE_SERVICE_VERSION;
}

/**
 * @brief Get the version major number
 * @return Major version number
 */
constexpr int getVersionMajor() noexcept {
    return DATABASE_SERVICE_VERSION_MAJOR;
}

/**
 * @brief Get the version minor number
 * @return Minor version number
 */
constexpr int getVersionMinor() noexcept {
    return DATABASE_SERVICE_VERSION_MINOR;
}

/**
 * @brief Get the version patch number
 * @return Patch version number
 */
constexpr int getVersionPatch() noexcept {
    return DATABASE_SERVICE_VERSION_PATCH;
}

} // namespace dbservice
