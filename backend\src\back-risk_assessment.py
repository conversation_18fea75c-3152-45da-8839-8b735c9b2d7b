from typing import Dict, List, Optional
from datetime import datetime, timedelta
from functools import lru_cache
import logging
from dataclasses import dataclass
from redis import Redis
from .exceptions import RiskAssessmentError

logger = logging.getLogger(__name__)

@dataclass
class RiskFactor:
    """Data class for risk factor validation"""
    value: float
    weight: float
    threshold: float

    def __post_init__(self):
        if not 0 <= self.value <= 1:
            raise ValueError("Risk factor value must be between 0 and 1")
        if not 0 <= self.weight <= 1:
            raise ValueError("Weight must be between 0 and 1")

class RiskAssessment:
    def __init__(self, git_integration, db_connection, cache_client: Redis):
        self.git = git_integration
        self.db = db_connection
        self.cache = cache_client
        self.CACHE_TTL = 3600  # 1 hour

    @lru_cache(maxsize=1000)
    def assess_improvement_risk(self, improvement_id: str) -> Dict:
        """Assess risk level with caching and validation"""
        cache_key = f"risk_assessment:{improvement_id}"
        
        # Try to get from cache first
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return json.loads(cached_result)

        try:
            risk_factors = self._calculate_risk_factors(improvement_id)
            risk_score = self._compute_weighted_risk_score(risk_factors)
            
            result = {
                "risk_score": risk_score,
                "risk_factors": {k: v.value for k, v in risk_factors.items()},
                "recommendations": self._generate_risk_recommendations(risk_factors),
                "assessed_at": datetime.utcnow().isoformat(),
                "improvement_id": improvement_id
            }

            # Cache the result
            self.cache.setex(
                cache_key,
                self.CACHE_TTL,
                json.dumps(result)
            )

            return result

        except Exception as e:
            logger.error(f"Risk assessment failed for improvement {improvement_id}: {str(e)}")
            raise RiskAssessmentError(f"Failed to assess risk: {str(e)}")

    def _calculate_risk_factors(self, improvement_id: str) -> Dict[str, RiskFactor]:
        """Calculate individual risk factors with validation"""
        affected_files = self.git.get_affected_files(improvement_id)
        
        if not affected_files:
            logger.warning(f"No affected files found for improvement {improvement_id}")
            return {}

        return {
            "code_complexity": RiskFactor(
                value=self._analyze_code_complexity(affected_files),
                weight=0.3,
                threshold=0.7
            ),
            "change_scope": RiskFactor(
                value=self._calculate_change_scope(affected_files),
                weight=0.3,
                threshold=0.6
            ),
            "historical_issues": RiskFactor(
                value=self._analyze_historical_issues(affected_files),
                weight=0.2,
                threshold=0.5
            ),
            "deployment_impact": RiskFactor(
                value=self._assess_deployment_impact(improvement_id),
                weight=0.2,
                threshold=0.8
            )
        }

    def _compute_weighted_risk_score(self, risk_factors: Dict[str, RiskFactor]) -> float:
        """Compute weighted risk score with validation"""
        if not risk_factors:
            return 0.0

        total_weight = sum(factor.weight for factor in risk_factors.values())
        if not math.isclose(total_weight, 1.0, rel_tol=1e-9):
            raise ValueError("Risk factor weights must sum to 1.0")

        return sum(
            factor.value * factor.weight
            for factor in risk_factors.values()
        )
