<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Permissions Diagnostics</title>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
        }
        .test-card {
            margin-bottom: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-success {
            border-left: 5px solid #28a745;
        }
        .test-error {
            border-left: 5px solid #dc3545;
        }
        .test-warning {
            border-left: 5px solid #ffc107;
        }
        .test-running {
            border-left: 5px solid #17a2b8;
        }
        .details-container {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 0.9rem;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        #summary-card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        #refresh-btn {
            margin-left: 10px;
        }
        .card-header {
            font-weight: 600;
            background-color: rgba(0,0,0,0.03);
        }
        .solution-box {
            background-color: #e9f7ef;
            border-left: 4px solid #28a745;
            padding: 10px 15px;
            margin-top: 10px;
            border-radius: 4px;
        }
        .solution-box h6 {
            color: #28a745;
            margin-bottom: 5px;
        }
        .solution-box code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .test-group {
            margin-bottom: 30px;
        }
        .test-group-title {
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }
        .accordion-button:not(.collapsed) {
            background-color: #e7f1ff;
            color: #0c63e4;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1>Git Permissions Diagnostics</h1>
                <p class="lead">This page tests various permissions required for the Git Repository Dashboard to function properly.</p>

                <div id="summary-card" class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Summary</h5>
                    </div>
                    <div class="card-body">
                        <div id="summary-content">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Running diagnostics...</span>
                                <button id="refresh-btn" class="btn btn-sm btn-outline-primary ms-2">Refresh</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tests-container" class="mt-4">
                    <!-- Test results will be inserted here -->
                </div>

                <div class="card mt-5" id="troubleshootingAccordion">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Troubleshooting Guide</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion">
                            <!-- Sudo Permissions -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingSudo">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSudo" aria-expanded="true" aria-controls="collapseSudo">
                                        <i class="fas fa-key me-2"></i>Sudo Permissions Issues
                                    </button>
                                </h2>
                                <div id="collapseSudo" class="accordion-collapse collapse show" aria-labelledby="headingSudo">
                                    <div class="accordion-body">
                                        <p>If tests related to sudo fail, you need to configure sudo permissions for the web server user (typically www-data):</p>
                                        <ol>
                                            <li>Edit the sudoers file: <code>sudo visudo -f /etc/sudoers.d/git-dashboard</code></li>
                                            <li>Add the following line: <code>www-data ALL=(git) NOPASSWD: /usr/bin/git</code></li>
                                            <li>Save and exit</li>
                                            <li>Restart the web server: <code>sudo systemctl restart apache2</code> or <code>sudo systemctl restart nginx</code></li>
                                        </ol>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Note:</strong> This allows the web server user (www-data) to run git commands as the git user without a password.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Repository Access -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingRepo">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRepo" aria-expanded="false" aria-controls="collapseRepo">
                                        <i class="fas fa-folder-open me-2"></i>Repository Access Issues
                                    </button>
                                </h2>
                                <div id="collapseRepo" class="accordion-collapse collapse" aria-labelledby="headingRepo">
                                    <div class="accordion-body">
                                        <p>If tests related to repository access fail, check the following:</p>
                                        <ul class="list-group mb-3">
                                            <li class="list-group-item">Ensure the git user exists: <code>id git</code></li>
                                            <li class="list-group-item">Check repository path permissions: <code>ls -la /home/<USER>/repositories</code></li>
                                            <li class="list-group-item">Ensure the git user has access to the repositories: <code>sudo -u git ls -la /home/<USER>/repositories</code></li>
                                            <li class="list-group-item">Make sure the web server user can execute commands as the git user: <code>sudo -u www-data sudo -u git whoami</code></li>
                                        </ul>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Important:</strong> Repository paths should be owned by the git user and have appropriate permissions (typically 755 for directories).
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Git Command Issues -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingGit">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGit" aria-expanded="false" aria-controls="collapseGit">
                                        <i class="fas fa-code-branch me-2"></i>Git Command Issues
                                    </button>
                                </h2>
                                <div id="collapseGit" class="accordion-collapse collapse" aria-labelledby="headingGit">
                                    <div class="accordion-body">
                                        <p>If git commands fail, check the following:</p>
                                        <ul class="list-group mb-3">
                                            <li class="list-group-item">Ensure git is installed: <code>which git</code></li>
                                            <li class="list-group-item">Check git version: <code>git --version</code></li>
                                            <li class="list-group-item">Verify the git user can run git commands: <code>sudo -u git git --version</code></li>
                                            <li class="list-group-item">Check if the git user has proper SSH keys configured (if needed): <code>sudo -u git ls -la ~/.ssh</code></li>
                                        </ul>
                                        <div class="alert alert-info">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <strong>Tip:</strong> If git user configuration is missing, set it up with:<br>
                                            <code>git config --global user.name "Git Server"</code><br>
                                            <code>git config --global user.email "***************"</code>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Safe Directory Issues -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="headingSafe">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSafe" aria-expanded="false" aria-controls="collapseSafe">
                                        <i class="fas fa-shield-alt me-2"></i>Git Safe Directory Issues
                                    </button>
                                </h2>
                                <div id="collapseSafe" class="accordion-collapse collapse" aria-labelledby="headingSafe">
                                    <div class="accordion-body">
                                        <p>If you encounter "dubious ownership" errors with Git 2.35.2 or newer, you need to configure safe.directory settings:</p>
                                        <div class="card mb-3">
                                            <div class="card-header bg-light">Error Example</div>
                                            <div class="card-body">
                                                <pre class="mb-0 text-danger">fatal: unsafe repository ('/path/to/repo' is owned by someone else)
To add an exception for this directory, call:

	git config --global --add safe.directory /path/to/repo</pre>
                                            </div>
                                        </div>
                                        <p>To fix this issue:</p>
                                        <ol>
                                            <li>Add the repository to the safe.directory list:<br>
                                                <code>git config --global --add safe.directory /path/to/repo</code></li>
                                            <li>Or, to trust all repositories regardless of ownership (less secure):<br>
                                                <code>git config --global --add safe.directory '*'</code></li>
                                        </ol>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Security Note:</strong> Using <code>safe.directory '*'</code> reduces security. Only use this in controlled environments.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Run diagnostics on page load
            runDiagnostics();

            // Set up refresh button using event delegation
            document.addEventListener('click', function(event) {
                if (event.target && event.target.id === 'refresh-btn' ||
                    (event.target.parentElement && event.target.parentElement.id === 'refresh-btn')) {
                    console.log('Refresh button clicked');
                    runDiagnostics();
                }
            });
        });

        function runDiagnostics() {
            // Reset the UI
            document.getElementById('summary-content').innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Running diagnostics...</span>
                    <button id="refresh-btn" class="btn btn-sm btn-outline-primary ms-2">Refresh</button>
                </div>
            `;
            document.getElementById('tests-container').innerHTML = '';

            // Fetch diagnostic results
            fetch('/api/diagnostics/git-permissions')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Diagnostic data received:', data);
                    // Update summary
                    updateSummary(data);

                    // Update test results - pass the entire data object, not just data.tests
                    updateTestResults(data);
                })
                .catch(error => {
                    console.error('Error fetching diagnostics:', error);
                    document.getElementById('summary-content').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error:</strong> Failed to fetch diagnostic results. ${error.message}
                            <button id="refresh-btn" class="btn btn-sm btn-outline-primary ms-2">Retry</button>
                        </div>
                    `;
                });
        }

        function updateSummary(data) {
            const summaryContent = document.getElementById('summary-content');

            // Check if data is valid
            if (!data || typeof data !== 'object') {
                console.error('Invalid summary data:', data);
                summaryContent.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Invalid diagnostic data format. This diagnostic tool must be run on the Git server.
                        <div class="mt-2">
                            <p>Possible reasons for this error:</p>
                            <ul>
                                <li>You're not running this on the actual Git server</li>
                                <li>The API endpoint is not accessible from your current location</li>
                                <li>The server is not properly configured</li>
                            </ul>
                        </div>
                        <button id="refresh-btn" class="btn btn-sm btn-outline-primary mt-2">Try Again</button>
                    </div>
                `;
                return;
            }

            // Format timestamp if available
            let timestamp = 'Unknown';
            if (data.timestamp) {
                try {
                    timestamp = new Date(data.timestamp).toLocaleString();
                } catch (e) {
                    console.error('Error formatting timestamp:', e);
                    timestamp = data.timestamp;
                }
            } else {
                // If no timestamp, use current time
                timestamp = new Date().toLocaleString() + ' (client time)';
            }

            let statusClass = '';
            let statusIcon = '';

            if (data.status === 'success') {
                statusClass = 'alert-success';
                statusIcon = '<i class="fas fa-check-circle"></i>';
            } else if (data.status === 'error') {
                statusClass = 'alert-danger';
                statusIcon = '<i class="fas fa-exclamation-circle"></i>';
            } else {
                statusClass = 'alert-info';
                statusIcon = '<i class="fas fa-info-circle"></i>';
            }

            // Check if we have errors in the data
            const hasErrors = data.errors && Array.isArray(data.errors) && data.errors.length > 0;

            summaryContent.innerHTML = `
                <div class="alert ${statusClass}">
                    ${statusIcon} <strong>${data.message || 'Diagnostic completed'}</strong>
                    <div class="mt-2 small">Timestamp: ${timestamp}</div>
                    ${hasErrors ? `<div class="mt-2 text-danger"><strong>Errors detected:</strong> ${data.errors.length} issue(s) found</div>` : ''}
                    <button id="refresh-btn" class="btn btn-sm btn-outline-primary mt-2">Run Again</button>
                </div>
            `;
        }

        function updateTestResults(data) {
            const testsContainer = document.getElementById('tests-container');
            testsContainer.innerHTML = '';

            // Check if data is valid
            if (!data || typeof data !== 'object') {
                console.error('Invalid data:', data);
                testsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Invalid data format. Please check the API response.
                    </div>
                `;
                return;
            }

            // Group tests by category
            const testGroups = [
                {
                    title: 'Git Configuration',
                    description: 'Basic Git configuration and version information',
                    tests: [
                        { key: 'git_version', title: 'Git Version', icon: 'code-branch' },
                        { key: 'git_config', title: 'Git Configuration', icon: 'cog' },
                        { key: 'git_safe_directory', title: 'Git Safe Directory Settings', icon: 'shield-alt' },
                        { key: 'git_user', title: 'Git User', icon: 'user' }
                    ]
                },
                {
                    title: 'Server Permissions',
                    description: 'Server user and sudo permissions',
                    tests: [
                        { key: 'web_server_user', title: 'Web Server User', icon: 'user-shield' },
                        { key: 'sudo_access', title: 'Sudo Access', icon: 'key' }
                    ]
                },
                {
                    title: 'Repository Access',
                    description: 'Git repository access and permissions',
                    tests: [
                        { key: 'repository_access', title: 'Repository Access', icon: 'folder-open' }
                    ]
                }
            ];

            // Check if we have any test data
            const allTests = testGroups.flatMap(group => group.tests);
            const hasAnyTests = allTests.some(test => data[test.key] !== undefined);

            if (!hasAnyTests) {
                testsContainer.innerHTML = `
                    <div class="alert alert-info">
                        <strong>Info:</strong> No test results available. The API response format may have changed.
                    </div>
                `;
                return;
            }

            // Track overall status
            let hasErrors = false;
            let errorCount = 0;

            // Process each test group
            testGroups.forEach(group => {
                // Check if this group has any tests with data
                const hasGroupData = group.tests.some(test => data[test.key] !== undefined);
                if (!hasGroupData) return;

                // Create group container
                let groupHtml = `
                    <div class="test-group">
                        <h4 class="test-group-title">
                            <i class="fas fa-${group.tests[0].icon} me-2"></i>${group.title}
                        </h4>
                        <p class="text-muted mb-3">${group.description}</p>
                        <div class="row">
                `;

                // Process each test in this group
                group.tests.forEach(test => {
                    const testValue = data[test.key];
                    if (testValue === undefined) return; // Skip undefined properties

                    // Determine test status
                    let statusClass = 'test-success';
                    let statusBadge = '<span class="badge bg-success status-badge">Success</span>';
                    let hasError = false;

                    // Check if this test has an error
                    if (testValue === null) {
                        statusClass = 'test-error';
                        statusBadge = '<span class="badge bg-danger status-badge">Error</span>';
                        hasError = true;
                        hasErrors = true;
                        errorCount++;
                    } else if (typeof testValue === 'object' && testValue.error) {
                        statusClass = 'test-error';
                        statusBadge = '<span class="badge bg-danger status-badge">Error</span>';
                        hasError = true;
                        hasErrors = true;
                        errorCount++;
                    }

                    // Create the test card
                    let testCard = `
                        <div class="col-md-6 mb-3">
                            <div class="card test-card ${statusClass} h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-${test.icon} me-2"></i>${test.title}</h5>
                                    ${statusBadge}
                                </div>
                                <div class="card-body">
                    `;

                    // Add test content based on the type of data
                    if (hasError) {
                        // Show error message
                        const errorMsg = typeof testValue === 'object' ? testValue.error : 'Test failed';
                        testCard += `<div class="alert alert-danger">${errorMsg}</div>`;

                        // Add solution guidance based on the test key
                        if (test.key === 'git_user') {
                            testCard += `
                                <div class="solution-box">
                                    <h6><i class="fas fa-lightbulb me-1"></i>Solution</h6>
                                    <p>Git user configuration is missing. Try the following:</p>
                                    <ol>
                                        <li>Configure git user globally: <code>git config --global user.name "Your Name"</code></li>
                                        <li>Configure git email: <code>git config --global user.email "<EMAIL>"</code></li>
                                    </ol>
                                </div>
                            `;
                        } else if (test.key === 'sudo_access') {
                            testCard += `
                                <div class="solution-box">
                                    <h6><i class="fas fa-lightbulb me-1"></i>Solution</h6>
                                    <p>The web server user needs sudo access to run git commands as the git user. Add this to sudoers:</p>
                                    <ol>
                                        <li>Edit sudoers file: <code>sudo visudo -f /etc/sudoers.d/git-dashboard</code></li>
                                        <li>Add line: <code>www-data ALL=(git) NOPASSWD: /usr/bin/git</code></li>
                                        <li>Save and exit</li>
                                        <li>Restart the web server: <code>sudo systemctl restart apache2</code> or <code>sudo systemctl restart nginx</code></li>
                                    </ol>
                                </div>
                            `;
                        }
                    } else if (testValue === null) {
                        // Null value
                        testCard += `<p class="card-text">No data available</p>`;
                    } else if (typeof testValue === 'string') {
                        // String value
                        testCard += `<p class="card-text">${testValue}</p>`;
                    } else if (Array.isArray(testValue)) {
                        // Array value
                        if (testValue.length === 0) {
                            testCard += `<p class="card-text">No items found</p>`;

                            // Add guidance for empty safe directory settings
                            if (test.key === 'git_safe_directory') {
                                testCard += `
                                    <div class="solution-box">
                                        <h6><i class="fas fa-info-circle me-1"></i>Information</h6>
                                        <p>No safe.directory settings found. This might be needed if you get "dubious ownership" errors.</p>
                                        <p>Add with: <code>git config --global --add safe.directory /path/to/repo</code></p>
                                    </div>
                                `;
                            }
                        } else {
                            testCard += `<ul class="list-group">`;
                            testValue.forEach(item => {
                                testCard += `<li class="list-group-item">${item}</li>`;
                            });
                            testCard += `</ul>`;
                        }
                    } else if (typeof testValue === 'object') {
                        // Format object data for better display
                        let formattedContent = '';

                        // Special handling for repository_access
                        if (test.key === 'repository_access') {
                            if (testValue.exists) {
                                formattedContent = `
                                    <div class="mb-3">
                                        <strong>Repository:</strong> ${testValue.path}<br>
                                        <strong>Type:</strong> ${testValue.is_bare_repo ? 'Bare Repository' : 'Standard Repository'}<br>
                                        <strong>Permissions:</strong> ${testValue.permissions || 'Unknown'}
                                    </div>
                                `;

                                if (testValue.latest_commit) {
                                    formattedContent += `
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong>Latest Commit:</strong> ${testValue.latest_commit}
                                        </div>
                                    `;
                                }
                            } else {
                                formattedContent = `
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Repository not found at: ${testValue.path}
                                    </div>
                                `;
                            }
                        } else if (test.key === 'web_server_user') {
                            formattedContent = `
                                <div class="mb-3">
                                    <strong>Username:</strong> ${testValue.username || 'Unknown'}<br>
                                    <strong>UID:</strong> ${testValue.uid || 'Unknown'}<br>
                                    <strong>GID:</strong> ${testValue.gid || 'Unknown'}<br>
                                    <strong>Home:</strong> ${testValue.home || 'Unknown'}
                                </div>
                            `;
                        }

                        testCard += formattedContent;

                        // Add details button for raw data
                        const detailsId = `details-${test.key}`;
                        testCard += `
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#${detailsId}">
                                    <i class="fas fa-code me-1"></i>Show Details
                                </button>
                                <div class="collapse mt-2" id="${detailsId}">
                                    <div class="details-container">${JSON.stringify(testValue, null, 2)}</div>
                                </div>
                            </div>
                        `;
                    } else {
                        // Other values
                        testCard += `<p class="card-text">${JSON.stringify(testValue)}</p>`;
                    }

                    testCard += `
                                </div>
                            </div>
                        </div>
                    `;

                    groupHtml += testCard;
                });

                groupHtml += `
                        </div>
                    </div>
                `;

                testsContainer.innerHTML += groupHtml;
            });

            // Add errors section if there are any
            if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
                let errorsCard = `
                    <div class="test-group">
                        <h4 class="test-group-title text-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Errors (${data.errors.length})
                        </h4>
                        <div class="card test-card test-error">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Detected Issues</h5>
                                <span class="badge bg-danger status-badge">Error</span>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>${data.errors.length} error(s) detected</strong> that need to be resolved for proper functionality.
                                </div>
                                <ul class="list-group">
                `;

                data.errors.forEach(error => {
                    errorsCard += `<li class="list-group-item list-group-item-danger"><i class="fas fa-times-circle me-2"></i>${error}</li>`;
                });

                errorsCard += `
                                </ul>
                                <div class="mt-3 alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Next Steps:</strong> Review the errors above and check the troubleshooting guide below for solutions.
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                testsContainer.innerHTML += errorsCard;
            }

            // Add overall status summary
            if (hasErrors) {
                testsContainer.innerHTML = `
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">Issues Detected</h5>
                                <p class="mb-0">Found ${errorCount} issue(s) that need to be fixed for proper functionality.</p>
                            </div>
                            <div class="ms-auto">
                                <a href="#troubleshootingAccordion" class="btn btn-warning btn-sm">View Solutions</a>
                            </div>
                        </div>
                    </div>
                ` + testsContainer.innerHTML;
            } else {
                testsContainer.innerHTML = `
                    <div class="alert alert-success mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">All Tests Passed</h5>
                                <p class="mb-0">Your Git server configuration is working correctly.</p>
                            </div>
                        </div>
                    </div>
                ` + testsContainer.innerHTML;
            }
        }
    </script>
</body>
</html>
