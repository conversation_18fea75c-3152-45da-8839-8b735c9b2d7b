/**
 * Git Repository Manager Module
 * Core module that initializes and coordinates Git repository functionality
 */
const GitRepositoryManager = {
    // Configuration
    config: {
        endpoints: {
            repositories: '/api/git/repositories',
            commitHistory: '/api/git/repository/{repo_name}/commits'
        },
        refreshInterval: 60000, // 1 minute
        maxRecentCommitAge: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        requestTimeout: 8000 // 8 seconds timeout for fetch requests
    },
    
    // Core state
    state: {
        repositoriesData: [],
        lastUpdated: null,
        status: 'idle',
        error: null,
        refreshTimer: null
    },
    
    /**
     * Initialize the Git Repository Manager
     * @returns {Object} This instance for chaining
     */
    async init() {
        console.log('GitRepositoryManager.init() - Starting initialization');
        
        // Create UI containers
        this.createSummaryContainer();
        
        // Initialize sub-modules
        this.initializeSubModules();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Initial data load
        try {
            await this.fetchRepositories();
            console.log('Initial repository data loaded successfully');
        } catch (error) {
            console.error('Failed to load initial repository data:', error);
            this.handleError('Failed to load repositories', error);
        }
        
        // Set up refresh timer
        this.setupRefreshTimer();
        
        console.log('GitRepositoryManager.init() - Initialization complete');
        return this;
    },
    
    /**
     * Initialize all sub-modules
     */
    initializeSubModules() {
        try {
            // Initialize all dependent modules with proper error handling
            this.repositoryList = window.GitRepositoryList ? 
                GitRepositoryList.init(this) : null;
                
            if (window.GitRepositoryModal) {
                GitRepositoryModal.init(this);
            }
            
            if (window.GitCommitHistoryChart) {
                GitCommitHistoryChart.init();
            }
            
            if (window.GitSizeComparisonChart) {
                GitSizeComparisonChart.init();
            }
            
            console.log('Sub-modules initialized successfully');
        } catch (error) {
            console.error('Error initializing sub-modules:', error);
            this.handleError('Failed to initialize repository components', error);
        }
    },
    
    /**
     * Create the Git repository summary container
     */
    createSummaryContainer() {
        try {
            // Get the parent container
            const gitRepoSection = document.getElementById('git-repo-section');
            if (!gitRepoSection) {
                console.error('Git repository section not found');
                return;
            }
            
            // Check if the summary container already exists
            const existingSummary = document.getElementById('git-summary-row');
            if (existingSummary) {
                console.log('Summary container already exists, skipping creation');
                return;
            }
            
            // Create the summary container
            const container = document.createElement('div');
            container.className = 'row';
            container.id = 'git-summary-row';
            
            // Create repository count card
            const repoCountCard = document.createElement('div');
            repoCountCard.className = 'col-md-4';
            repoCountCard.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title text-muted">Total Repositories</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-database fa-2x text-primary me-3"></i>
                            <div>
                                <h3 id="repo-count" class="mb-0">-</h3>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Create recent commits card
            const recentCommitsCard = document.createElement('div');
            recentCommitsCard.className = 'col-md-4';
            recentCommitsCard.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title text-muted">Recent Commits (24h)</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-code-branch fa-2x text-success me-3"></i>
                            <div>
                                <h3 id="recent-commits" class="mb-0">-</h3>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Create largest repository card
            const largestRepoCard = document.createElement('div');
            largestRepoCard.className = 'col-md-4';
            largestRepoCard.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title text-muted">Largest Repository</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-weight-hanging fa-2x text-warning me-3"></i>
                            <div>
                                <h5 id="largest-repo-name" class="mb-0">-</h5>
                                <small id="largest-repo-size" class="text-muted">-</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Assemble the summary container
            container.appendChild(repoCountCard);
            container.appendChild(recentCommitsCard);
            container.appendChild(largestRepoCard);
            
            // Add to parent container
            gitRepoSection.appendChild(container);
            console.log('Summary container created successfully');
        } catch (error) {
            console.error('Error creating summary container:', error);
            // Non-critical error, don't need to update global error state
        }
    },
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        try {
            // Refresh button
            const refreshButton = document.getElementById('refresh-dashboard');
            if (refreshButton) {
                refreshButton.addEventListener('click', this.handleManualRefresh.bind(this));
            }
            
            // When a tab is shown, trigger chart resizing
            document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tabEl => {
                tabEl.addEventListener('shown.bs.tab', event => {
                    // Check if we're viewing the repositories tab
                    if (event.target.getAttribute('data-bs-target') === '#repositories') {
                        // Force resize of any visible charts
                        window.dispatchEvent(new Event('resize'));
                    }
                });
            });
            
            console.log('Event listeners set up successfully');
        } catch (error) {
            console.error('Error setting up event listeners:', error);
        }
    },
    
    /**
     * Set up the refresh timer
     */
    setupRefreshTimer() {
        // Clear existing timer
        if (this.state.refreshTimer) {
            clearInterval(this.state.refreshTimer);
        }
        
        // Set up new timer
        this.state.refreshTimer = setInterval(() => {
            this.fetchRepositories().catch(error => {
                console.error('Error in refresh timer:', error);
            });
        }, this.config.refreshInterval);
        
        console.log(`Refresh timer set up with interval ${this.config.refreshInterval}ms`);
    },
    
    /**
     * Handle manual refresh button click
     * @param {Event} event - Click event
     */
    handleManualRefresh(event) {
        if (event) {
            event.preventDefault();
        }
        
        // Log manual refresh
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Manually refreshing Git repository data');
        }
        
        // Fetch repositories
        this.fetchRepositories().catch(error => {
            console.error('Error during manual refresh:', error);
        });
    },
    
    /**
     * Handle errors in a consistent way
     * @param {string} message - Error message
     * @param {Error} error - Error object
     */
    handleError(message, error) {
        const errorMessage = error ? `${message}: ${error.message}` : message;
        console.error(errorMessage, error);
        
        // Update state
        this.state.status = 'error';
        this.state.error = errorMessage;
        
        // Log to dashboard logger
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('error', errorMessage);
        }
        
        // Update UI to show error
        if (this.repositoryList && typeof this.repositoryList.renderRepositoryList === 'function') {
            this.repositoryList.renderRepositoryList();
        } else {
            // Force hide loading indicators
            document.querySelectorAll('[id$="-loading"]').forEach(element => {
                element.style.display = 'none';
            });
            
            // Show error messages
            document.querySelectorAll('[id$="-error"]').forEach(element => {
                element.style.display = 'block';
                element.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> ${errorMessage}`;
            });
        }
    },
    
    /**
     * Update the last updated timestamp
     */
    updateLastUpdated() {
        const lastUpdated = new Date();
        this.state.lastUpdated = lastUpdated;
        
        // Update last updated element
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = lastUpdated.toLocaleTimeString();
        }
    },
    
    /**
     * Fetch data with timeout
     * @param {string} url - URL to fetch
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>} Fetch response
     */
    async fetchWithTimeout(url, options = {}) {
        const controller = new AbortController();
        const { signal } = controller;
        
        const timeout = setTimeout(() => {
            controller.abort();
        }, this.config.requestTimeout);
        
        try {
            const response = await fetch(url, { ...options, signal });
            clearTimeout(timeout);
            return response;
        } catch (error) {
            clearTimeout(timeout);
            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${this.config.requestTimeout / 1000}s`);
            }
            throw error;
        }
    },
    
    /**
     * Fetch repositories from the API
     * @returns {Promise<Array>} Promise that resolves to an array of repositories
     */
    async fetchRepositories() {
        // Update state to indicate loading
        this.state.status = 'loading';
        
        // Update UI to show loading state
        if (this.repositoryList && typeof this.repositoryList.renderRepositoryList === 'function') {
            this.repositoryList.renderRepositoryList();
        }
        
        console.log('Fetching repositories from API...');
        
        try {
            // Fetch with timeout
            const response = await this.fetchWithTimeout(this.config.endpoints.repositories);
            
            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }
            
            // Parse response safely
            let data;
            try {
                const text = await response.text();
                data = text ? JSON.parse(text) : null;
            } catch (error) {
                throw new Error(`Invalid JSON response: ${error.message}`);
            }
            
            if (!data) {
                throw new Error('API returned empty response');
            }
            
            console.log('API response:', data);
            
            // Validate repositories array
            if (!Array.isArray(data.repositories)) {
                throw new Error('API response did not contain a valid repositories array');
            }
            
            console.log(`Processed ${data.repositories.length} repositories`);
            
            // Update state
            this.state.repositoriesData = data.repositories;
            this.state.status = 'loaded';
            this.state.error = null;
            this.state.lastUpdated = new Date();
            
            // Process repository data
            this.processRepositoriesData();
            
            // Update UI
            this.updateLastUpdated();
            this.updateSummaryUI();
            
            if (this.repositoryList && typeof this.repositoryList.renderRepositoryList === 'function') {
                this.repositoryList.renderRepositoryList();
            }
            
            return this.state.repositoriesData;
        } catch (error) {
            this.handleError('Failed to fetch repositories', error);
            return [];
        }
    },
    
    /**
     * Process repositories data to ensure all required fields
     */
    processRepositoriesData() {
        if (!this.state.repositoriesData || !Array.isArray(this.state.repositoriesData)) {
            console.error('Invalid repositories data');
            return;
        }
        
        console.log('Processing repository data');
        
        // Process each repository object to ensure it has all required fields
        this.state.repositoriesData = this.state.repositoriesData.map(repo => {
            const processedRepo = { ...repo };
            
            // Ensure name is a string
            if (!processedRepo.name) {
                processedRepo.name = 'Unnamed Repository';
            }
            
            // Ensure size is a number
            if (typeof processedRepo.size !== 'number') {
                processedRepo.size = 0;
            }
            
            // Ensure formatted size 
            if (!processedRepo.size_formatted) {
                processedRepo.size_formatted = this.formatSize(processedRepo.size);
            }
            
            // Convert date strings to Date objects for easier comparison
            if (processedRepo.last_commit && typeof processedRepo.last_commit === 'string') {
                try {
                    processedRepo.last_commit_date = new Date(processedRepo.last_commit);
                } catch (error) {
                    processedRepo.last_commit_date = null;
                }
            }
            
            // Calculate health status if not provided
            if (!processedRepo.health_status) {
                const healthInfo = this.calculateRepositoryHealth(processedRepo);
                processedRepo.health_status = healthInfo.status;
                processedRepo.health_score = healthInfo.score;
                processedRepo.health_message = healthInfo.message;
            }
            
            return processedRepo;
        });
        
        console.log('Repository data processed successfully');
    },
    
    /**
     * Format size in bytes to human-readable format
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        if (!bytes || isNaN(bytes)) return 'Unknown';
        
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
    },
    
    /**
     * Calculate repository health based on last commit date
     * @param {Object} repo - Repository object
     * @returns {Object} Health information
     */
    calculateRepositoryHealth(repo) {
        if (!repo.last_commit_date) {
            return { status: 'unknown', score: 0, message: 'No commit history available' };
        }
        
        const now = new Date();
        const lastCommitDate = repo.last_commit_date;
        const daysSinceLastCommit = Math.floor((now - lastCommitDate) / (1000 * 60 * 60 * 24));
        
        if (daysSinceLastCommit <= 7) {
            return { 
                status: 'good', 
                score: 80 + Math.min(20, (7 - daysSinceLastCommit) * 3), 
                message: 'Regular activity in the last week' 
            };
        } else if (daysSinceLastCommit <= 30) {
            return { 
                status: 'fair', 
                score: 50 + Math.min(30, (30 - daysSinceLastCommit)), 
                message: 'Some activity in the last month' 
            };
        } else {
            return { 
                status: 'poor', 
                score: Math.max(10, 50 - Math.min(40, (daysSinceLastCommit - 30) / 10)), 
                message: `Inactive for ${daysSinceLastCommit} days` 
            };
        }
    },
    
    /**
     * Update summary UI elements
     */
    updateSummaryUI() {
        try {
            // Get counts
            const repoCount = this.state.repositoriesData.length;
            const recentCommits = this.calculateRecentCommits();
            const largestRepo = this.findLargestRepository();
            
            // Update repository count
            const repoCountElement = document.getElementById('repo-count');
            if (repoCountElement) {
                repoCountElement.textContent = repoCount.toString();
            }
            
            // Update recent commits
            const recentCommitsElement = document.getElementById('recent-commits');
            if (recentCommitsElement) {
                recentCommitsElement.textContent = recentCommits.toString();
            }
            
            // Update largest repository
            const largestRepoNameElement = document.getElementById('largest-repo-name');
            const largestRepoSizeElement = document.getElementById('largest-repo-size');
            
            if (largestRepoNameElement && largestRepoSizeElement) {
                if (largestRepo) {
                    largestRepoNameElement.textContent = largestRepo.name;
                    largestRepoSizeElement.textContent = largestRepo.size_formatted;
                } else {
                    largestRepoNameElement.textContent = '-';
                    largestRepoSizeElement.textContent = '-';
                }
            }
            
            console.log('Summary UI updated successfully');
        } catch (error) {
            console.error('Error updating summary UI:', error);
        }
    },
    
    /**
     * Calculate number of recent commits (within 24 hours)
     * @returns {number} Count of recent commits
     */
    calculateRecentCommits() {
        if (!Array.isArray(this.state.repositoriesData)) {
            return 0;
        }
        
        const now = new Date();
        const cutoff = new Date(now.getTime() - this.config.maxRecentCommitAge);
        
        let recentCount = 0;
        
        this.state.repositoriesData.forEach(repo => {
            if (repo.last_commit_date && repo.last_commit_date > cutoff) {
                recentCount++;
            }
        });
        
        return recentCount;
    },
    
    /**
     * Find the largest repository by size
     * @returns {Object|null} Largest repository or null if none
     */
    findLargestRepository() {
        if (!Array.isArray(this.state.repositoriesData) || this.state.repositoriesData.length === 0) {
            return null;
        }
        
        return this.state.repositoriesData.reduce((largest, current) => {
            if (!largest || (current.size > largest.size)) {
                return current;
            }
            return largest;
        }, null);
    },
    
    /**
     * Get a repository by name
     * @param {string} repoName - Repository name
     * @returns {Object|null} Repository object or null if not found
     */
    getRepositoryByName(repoName) {
        if (!Array.isArray(this.state.repositoriesData)) {
            return null;
        }
        
        return this.state.repositoriesData.find(repo => repo.name === repoName) || null;
    },
    
    /**
     * Show repository details
     * @param {string} repoName - Repository name
     */
    showRepositoryDetails(repoName) {
        if (window.GitRepositoryModal && typeof GitRepositoryModal.showRepositoryDetails === 'function') {
            GitRepositoryModal.showRepositoryDetails(repoName);
        } else {
            console.error('Repository modal module not available');
        }
    },
    
    /**
     * Fetch commit history for a repository
     * @param {string} repoName - Repository name
     * @param {number} days - Number of days of history
     * @returns {Promise<Object>} Commit history data
     */
    async fetchCommitHistory(repoName, days = 30) {
        if (!repoName) {
            throw new Error('Repository name is required');
        }
        
        const url = this.config.endpoints.commitHistory.replace('{repo_name}', encodeURIComponent(repoName));
        
        try {
            const response = await this.fetchWithTimeout(`${url}?days=${days}`);
            
            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log(`Fetched commit history for ${repoName}:`, data);
            
            return data;
        } catch (error) {
            console.error(`Error fetching commit history for ${repoName}:`, error);
            throw error;
        }
    }
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // The initialization is handled in the main index.html script now
    // GitRepositoryManager.init();
});

// Explicitly expose the module to the global window object
window.GitRepositoryManager = GitRepositoryManager;
