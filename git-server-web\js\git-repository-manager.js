/**
 * Git Repository Manager Module
 * Core module that initializes and coordinates Git repository functionality
 */
const GitRepositoryManager = {
    // Configuration
    config: {
        endpoints: {
            repositories: '/api/git/repositories',
            commitHistory: '/api/git/repository/{repo_name}/commits'
        },
        refreshInterval: 60000, // 1 minute
        maxRecentCommitAge: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        requestTimeout: 8000 // 8 seconds timeout for fetch requests
    },

    // Core state
    state: {
        repositoriesData: [],
        lastUpdated: null,
        status: 'idle',
        error: null,
        refreshTimer: null
    },

    /**
     * Initialize the Git Repository Manager
     * @returns {Object} This instance for chaining
     */
    async init() {
        console.log('GitRepositoryManager.init() - Starting initialization');

        // Set up event listeners
        this.setupEventListeners();

        // Add refresh button handler
        const refreshBtn = document.getElementById('refresh-repos-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                console.log('Manual repository refresh triggered');
                this.fetchRepositories().catch(err => {
                    console.error('Error refreshing repositories:', err);
                });
            });
        }

        // Initial data load
        try {
            await this.fetchRepositories();
            console.log('Initial repository data loaded successfully');
        } catch (error) {
            console.error('Failed to load initial repository data:', error);
            this.updateStatusIndicator('error');
        }

        console.log('GitRepositoryManager.init() - Initialization complete');
        return this;
    },

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Add refresh button event listener
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.handleManualRefresh.bind(this));
        }
    },

    /**
     * Set up the refresh timer
     */
    setupRefreshTimer() {
        // Clear any existing timer
        if (this.state.refreshTimer) {
            clearInterval(this.state.refreshTimer);
        }

        // Set up new timer
        this.state.refreshTimer = setInterval(async () => {
            try {
                await this.fetchRepositories();
                console.log('Repositories refreshed successfully');
            } catch (error) {
                console.error('Failed to refresh repositories:', error);
            }
        }, this.config.refreshInterval);
    },

    /**
     * Handle manual refresh button click
     * @param {Event} event - Click event
     */
    async handleManualRefresh(event) {
        if (event) event.preventDefault();

        // Prevent rapid clicking
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            setTimeout(() => {
                refreshBtn.disabled = false;
            }, 2000);
        }

        try {
            await this.fetchRepositories();
            console.log('Repositories refreshed manually');
        } catch (error) {
            console.error('Failed to refresh repositories manually:', error);
            this.handleError('Failed to refresh repositories', error);
        }
    },

    /**
     * Handle errors in a consistent way
     * @param {string} message - Error message
     * @param {Error} error - Error object
     */
    handleError(message, error) {
        // Update state
        this.state.status = 'error';
        this.state.error = error;

        // Log error
        console.error(message, error);

        // Show error in UI if DashboardLogger is available
        if (window.DashboardLogger) {
            window.DashboardLogger.logError(message, error);
        }

        // Update status indicator
        const statusIndicator = document.getElementById('repo-list-status-indicator');
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator error';
            statusIndicator.title = message;
        }
    },

    /**
     * Get the current repositories data
     * @returns {Array} Array of repository objects
     */
    getRepositories() {
        return this.state.repositoriesData || [];
    },

    /**
     * Update the last updated timestamp
     */
    updateLastUpdated() {
        this.state.lastUpdated = new Date();

        // Update UI
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = this.state.lastUpdated.toLocaleTimeString();
        }
    },

    /**
     * Fetch with timeout
     * @param {string} url - URL to fetch
     * @param {Object} options - Fetch options
     * @returns {Promise<Response>} Fetch response
     */
    fetchWithTimeout(url, options = {}) {
        const timeout = options.timeout || this.config.requestTimeout;

        return Promise.race([
            fetch(url, options),
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`Request timed out after ${timeout}ms`));
                }, timeout);
            })
        ]);
    },

    /**
     * Check if the data structure from the API matches what our code expects
     * @param {Object} data - Data from API
     * @returns {Object} Compatibility report
     */
    checkDataFormat(data) {
        const report = {
            status: 'unknown',
            issues: [],
            suggestions: []
        };

        // Check if data is valid
        if (!data) {
            report.status = 'error';
            report.issues.push('Data is null or undefined');
            report.suggestions.push('Check if API is returning data');
            return report;
        }

        // Check repositories array
        if (!data.repositories) {
            report.status = 'error';
            report.issues.push('No "repositories" array in data');

            // Check if data itself is an array
            if (Array.isArray(data)) {
                report.suggestions.push('API is returning an array directly instead of {repositories: [...]}');
                report.suggestions.push('Update processRepositoriesData() to handle direct array');
            } else {
                report.suggestions.push('Check API response format - expected {repositories: [...]}');
            }
            return report;
        }

        if (!Array.isArray(data.repositories)) {
            report.status = 'error';
            report.issues.push('Data.repositories is not an array');
            report.suggestions.push('Check API response format - expected {repositories: [...]}');
            return report;
        }

        // Data structure looks good at high level
        report.status = 'ok';

        // If no repositories, note that
        if (data.repositories.length === 0) {
            report.issues.push('Repositories array is empty');
            report.suggestions.push('Check if repositories exist in the source location');
            return report;
        }

        // Check first repository for expected fields
        const sampleRepo = data.repositories[0];
        const expectedFields = ['name', 'path', 'size', 'last_commit'];
        const missingFields = expectedFields.filter(field => typeof sampleRepo[field] === 'undefined');

        if (missingFields.length > 0) {
            report.status = 'warning';
            report.issues.push(`Repository objects missing expected fields: ${missingFields.join(', ')}`);
            report.suggestions.push('Update code to handle missing fields with defaults');
        }

        // Check specific field formats that might cause issues
        if (sampleRepo.last_commit && typeof sampleRepo.last_commit !== 'string') {
            report.issues.push('last_commit is not a string as expected');
            report.suggestions.push('Convert last_commit to string format');
        }

        if (sampleRepo.size && typeof sampleRepo.size !== 'number') {
            report.issues.push('size is not a number as expected');
            report.suggestions.push('Convert size to number format');
        }

        return report;
    },

    /**
     * Fetch repositories from API
     * @returns {Promise<Array>} Array of repository objects
     */
    async fetchRepositories() {
        console.log('Fetching repositories from API...');

        try {
            // Update status indicator
            this.updateStatusIndicator('loading');

            // Fetch data from API
            console.log('Fetching from:', this.config.endpoints.repositories);
            const response = await this.fetchWithTimeout(this.config.endpoints.repositories);

            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Repositories data received:', data);

            // Check data compatibility
            const compatibilityReport = this.checkDataFormat(data);
            console.log('Data compatibility report:', compatibilityReport);

            // Debug data
            console.log('Raw data type:', typeof data);
            console.log('Is array?', Array.isArray(data));
            console.log('Has repositories property?', data.hasOwnProperty('repositories'));

            // Enhanced processing to handle different data formats
            if (Array.isArray(data)) {
                // Direct array (unusual format but handle gracefully)
                console.log('API returned direct repository array');
                this.state.repositoriesData = this.normalizeRepositories(data);
            } else if (data && data.repositories && Array.isArray(data.repositories)) {
                // Standard format with repositories property
                console.log('API returned standard format with repositories array');
                this.state.repositoriesData = this.normalizeRepositories(data.repositories);
            } else if (data && data.status === 'success' && Array.isArray(data.repositories)) {
                // Status wrapper format
                console.log('API returned success status with repositories array');
                this.state.repositoriesData = this.normalizeRepositories(data.repositories);
            } else {
                // Unknown format - try to handle gracefully
                console.warn('Unknown API response format:', data);
                // Try to extract repositories if present in any form
                if (data && typeof data === 'object') {
                    for (const key in data) {
                        if (Array.isArray(data[key]) && data[key].length > 0 && data[key][0].name) {
                            console.log(`Found potential repositories array in property: ${key}`);
                            this.state.repositoriesData = this.normalizeRepositories(data[key]);
                            break;
                        }
                    }
                }

                // If we still couldn't find repositories, set to empty array
                if (!this.state.repositoriesData || this.state.repositoriesData.length === 0) {
                    console.warn('No repositories data found in API response');
                    this.state.repositoriesData = [];
                }
            }

            // Initialize GitRepositoryList if available
            if (window.GitRepositoryList) {
                if (!GitRepositoryList.state.isInitialized) {
                    console.log('Initializing GitRepositoryList...');
                    GitRepositoryList.init(this.state.repositoriesData);
                } else {
                    console.log('Updating GitRepositoryList with', this.state.repositoriesData.length, 'repositories');
                    GitRepositoryList.renderRepositoryList(this.state.repositoriesData);
                }
            } else {
                console.error('GitRepositoryList module not available');
            }

            // Update dashboard overview stats
            this.updateDashboardStats(this.state.repositoriesData);

            // Update status indicator
            this.updateStatusIndicator('success');

            return this.state.repositoriesData;
        } catch (error) {
            console.error('Error fetching repositories:', error);

            // Update status indicator
            this.updateStatusIndicator('error');

            // Don't throw the error, we're handling it with an empty data set
            return this.state.repositoriesData || [];
        }
    },

    /**
     * Normalize repositories array to ensure consistent format
     * @param {Array} repositories - Repositories from API
     * @returns {Array} Normalized repositories
     */
    normalizeRepositories(repositories) {
        if (!Array.isArray(repositories)) {
            console.error('Cannot normalize non-array repositories:', repositories);
            return [];
        }

        return repositories.map(repo => {
            // Create a normalized repository object with all required properties
            return {
                name: repo.name || 'Unknown',
                path: repo.path || '',
                size: typeof repo.size === 'number' ? repo.size : 0,
                size_bytes: typeof repo.size === 'number' ? repo.size : 0,
                size_formatted: repo.size_formatted || this.formatSize(repo.size || 0),
                last_commit: repo.last_commit || null,
                last_commit_hash: repo.last_commit_hash || '',
                last_commit_date: repo.last_commit || null,
                branches: repo.branches || [],
                branch_count: typeof repo.branches === 'number' ? repo.branches :
                              (Array.isArray(repo.branches) ? repo.branches.length : 0),
                description: repo.description || '',
                health: {
                    score: repo.health_score || 50,
                    status: repo.health_status || 'unknown',
                    message: repo.health_message || 'Unknown status'
                }
            };
        });
    },

    /**
     * Format size in bytes to human-readable format
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    formatSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Calculate repository health based on last commit date
     * @param {Object} repo - Repository object
     * @returns {Object} Health information
     */
    calculateRepositoryHealth(repo) {
        // Default health status
        const health = {
            status: 'unknown',
            message: 'No commit history available',
            indicator: 'health-unknown',
            score: 0
        };

        // Check if last commit exists
        if (!repo.last_commit) {
            return health;
        }

        // Calculate days since last commit
        const lastCommitDate = new Date(repo.last_commit);
        const now = new Date();
        const diffTime = Math.abs(now - lastCommitDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Determine health status based on days since last commit
        if (diffDays <= 7) {
            health.status = 'good';
            health.message = 'Active development';
            health.indicator = 'health-good';
            health.score = 100;
        } else if (diffDays <= 30) {
            health.status = 'fair';
            health.message = 'Regular updates';
            health.indicator = 'health-fair';
            health.score = 70;
        } else {
            health.status = 'poor';
            health.message = 'Infrequent updates';
            health.indicator = 'health-poor';
            health.score = 30;
        }

        return health;
    },

    /**
     * Update status indicator
     * @param {string} status - Status to update to (loading, success, error)
     */
    updateStatusIndicator(status) {
        const statusIndicator = document.getElementById('repo-list-status-indicator');
        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${status}`;
            if (status === 'loading') {
                statusIndicator.title = 'Loading...';
            } else if (status === 'success') {
                statusIndicator.title = 'Loaded successfully';
            } else if (status === 'error') {
                statusIndicator.title = 'Error loading data';
            }
        }
    },

    /**
     * Fetch commit history for a repository
     * @param {string} repoName - Repository name
     * @param {number} days - Number of days of history to fetch
     * @returns {Promise<Object>} Commit history data
     */
    async fetchCommitHistory(repoName, days = 30) {
        console.log(`Fetching commit history for ${repoName} (${days} days)`);

        try {
            // Construct the URL
            const url = this.config.endpoints.commitHistory.replace('{repo_name}', encodeURIComponent(repoName)) + `?days=${days}`;
            console.log('Commit history URL:', url);

            // Update status indicator
            this.updateStatusIndicator('loading');

            // Fetch data from API
            const response = await this.fetchWithTimeout(url);

            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Commit history data received:', data);

            // Update status indicator
            this.updateStatusIndicator('success');

            return data;
        } catch (error) {
            console.error('Error fetching commit history:', error);

            // Update status indicator
            this.updateStatusIndicator('error');

            // Return empty commit data
            return { commits: [] };
        }
    },

    /**
     * Update dashboard overview stats with repository data
     * @param {Array} repositories - Array of repository objects
     */
    updateDashboardStats(repositories) {
        console.log('Updating dashboard stats with', repositories.length, 'repositories');

        // Update total repositories count
        const totalReposElement = document.getElementById('total-repos');
        if (totalReposElement) {
            totalReposElement.textContent = repositories.length;
        }

        // Update total size
        const totalSizeElement = document.getElementById('total-size');
        if (totalSizeElement) {
            const totalSize = repositories.reduce((sum, repo) => sum + (repo.size || 0), 0);
            totalSizeElement.textContent = this.formatSize(totalSize);
        }

        // Update recent commits (24h)
        const recentCommitsElement = document.getElementById('recent-commits');
        if (recentCommitsElement) {
            // Get current time and 24 hours ago time
            const now = new Date();
            const oneDayAgo = new Date(now);
            oneDayAgo.setHours(now.getHours() - 24);

            // Count repositories with commits in the last 24 hours
            let recentCommits = 0;
            repositories.forEach(repo => {
                if (repo.last_commit) {
                    const lastCommitDate = new Date(repo.last_commit);
                    if (lastCommitDate >= oneDayAgo) {
                        recentCommits++;
                    }
                }
            });

            recentCommitsElement.textContent = recentCommits;
        }

        // Update largest repository
        const largestRepoElement = document.getElementById('largest-repo');
        if (largestRepoElement && repositories.length > 0) {
            // Find the largest repository
            const largestRepo = repositories.reduce((largest, repo) => {
                return (repo.size > largest.size) ? repo : largest;
            }, { size: 0, name: 'None' });

            largestRepoElement.textContent = largestRepo.name;
        }

        // Update git status indicator
        const gitStatusIndicator = document.getElementById('git-status-indicator');
        if (gitStatusIndicator) {
            gitStatusIndicator.className = 'status-indicator success';
        }
    }
};

// Expose the module globally
window.GitRepositoryManager = GitRepositoryManager;