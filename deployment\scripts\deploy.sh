#!/bin/bash

# Exit on any error
set -e

# Configuration
APP_NAME="project-tracker"
DEPLOY_DIR="/opt/project-tracker"
VENV_DIR="$DEPLOY_DIR/venv"
LOG_DIR="/var/log/project-tracker"
USER="ubuntu"
GROUP="ubuntu"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Starting deployment of $APP_NAME...${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}Please run as root${NC}"
    exit 1
fi

# Create directories
echo "Creating directories..."
mkdir -p $DEPLOY_DIR
mkdir -p $LOG_DIR
mkdir -p $DEPLOY_DIR/database
mkdir -p $DEPLOY_DIR/backups

# Set permissions
echo "Setting permissions..."
chown -R $USER:$GROUP $DEPLOY_DIR
chown -R $USER:$GROUP $LOG_DIR

# Install system dependencies
echo "Installing system dependencies..."
apt update
apt install -y python3-pip python3-venv nginx supervisor ufw

# Create virtual environment
echo "Setting up Python environment..."
su - $USER -c "python3 -m venv $VENV_DIR"
su - $USER -c "$VENV_DIR/bin/pip install -r $DEPLOY_DIR/requirements.txt"
su - $USER -c "$VENV_DIR/bin/pip install gunicorn"

# Copy configuration files
echo "Configuring services..."
cp deployment/project-tracker.service /etc/systemd/system/
cp deployment/nginx-project-tracker /etc/nginx/sites-available/project-tracker
ln -sf /etc/nginx/sites-available/project-tracker /etc/nginx/sites-enabled/

# Initialize database
echo "Initializing database..."
su - $USER -c "cd $DEPLOY_DIR && $VENV_DIR/bin/python src/app.py"
su - $USER -c "cd $DEPLOY_DIR && $VENV_DIR/bin/python src/import_tasks.py"

# Setup backup script
echo "Setting up backup..."
cp deployment/backup.sh $DEPLOY_DIR/
chmod +x $DEPLOY_DIR/backup.sh
chown $USER:$GROUP $DEPLOY_DIR/backup.sh

# Add backup to crontab
(crontab -l 2>/dev/null; echo "0 0 * * * $DEPLOY_DIR/backup.sh") | crontab -

# Configure firewall
echo "Configuring firewall..."
ufw allow 'Nginx Full'
ufw --force enable

# Start services
echo "Starting services..."
systemctl daemon-reload
systemctl enable project-tracker
systemctl start project-tracker
systemctl restart nginx

# Verify deployment
echo "Verifying deployment..."
if systemctl is-active --quiet project-tracker; then
    echo -e "${GREEN}Project Tracker service is running${NC}"
else
    echo -e "${RED}Project Tracker service failed to start${NC}"
    exit 1
fi

if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}Nginx is running${NC}"
else
    echo -e "${RED}Nginx failed to start${NC}"
    exit 1
fi

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo "You can now access the application at http://your-server-ip"
echo "Check logs at $LOG_DIR for any issues"
