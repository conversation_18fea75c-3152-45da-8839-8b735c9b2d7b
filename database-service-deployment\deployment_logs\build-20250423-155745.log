-- The CXX compiler identification is Clang 18.1.3
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/clang++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options 
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.4")  
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found CURL: /usr/lib/x86_64-linux-gnu/libcurl.so (found version "8.5.0")  
-- pqxx package not found, will attempt to use system libraries
-- Configuring done (0.9s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
[1/30] Scanning /home/<USER>/database-service-build/src/core/connection_manager.cpp for CXX dependencies
[2/30] Scanning /home/<USER>/database-service-build/src/client/database_client.cpp for CXX dependencies
[3/30] Scanning /home/<USER>/database-service-build/src/main.cpp for CXX dependencies
[4/30] Scanning /home/<USER>/database-service-build/src/schema/schema_manager.cpp for CXX dependencies
[5/30] Scanning /home/<USER>/database-service-build/src/security/security_manager.cpp for CXX dependencies
[6/30] Scanning /home/<USER>/database-service-build/src/utils/logger.cpp for CXX dependencies
[7/30] Scanning /home/<USER>/database-service-build/src/database_service.cpp for CXX dependencies
[8/30] Scanning /home/<USER>/database-service-build/src/api/api_server.cpp for CXX dependencies
[9/30] Scanning /home/<USER>/database-service-build/src/modules/dbcore.cppm for CXX dependencies
[10/30] Scanning /home/<USER>/database-service-build/src/modules/dbschema.cppm for CXX dependencies
[11/30] Scanning /home/<USER>/database-service-build/src/modules/dbapi.cppm for CXX dependencies
[12/30] Scanning /home/<USER>/database-service-build/src/modules/dbsec.cppm for CXX dependencies
[13/30] Scanning /home/<USER>/database-service-build/src/modules/dbmain.cppm for CXX dependencies
FAILED: CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.ddi 
"/usr/bin/clang-scan-deps-18" -format=p1689 -- /usr/bin/clang++ -DBOOST_PROGRAM_OPTIONS_DYN_LINK -DBOOST_PROGRAM_OPTIONS_NO_LIB -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/home/<USER>/database-service-build/include -I/usr/include/postgresql -fmodules -fcxx-modules -O3 -DNDEBUG -O3 -std=c++23 -Wall -Wextra -Wpedantic -x c++ /home/<USER>/database-service-build/src/modules/dbmain.cppm -c -o CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o -MT CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.ddi -MD -MF CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.ddi.d > CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.ddi.tmp && mv CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.ddi.tmp CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.ddi
Error while scanning dependencies for /home/<USER>/database-service-build/src/modules/dbmain.cppm:
/home/<USER>/database-service-build/src/modules/dbmain.cppm:17:10: fatal error: 'stop_source' file not found
[14/30] Scanning /home/<USER>/database-service-build/src/modules/dbsvc.cppm for CXX dependencies
ninja: build stopped: subcommand failed.
