[Unit]
Description=Git Repository Logging Agent
After=network.target

[Service]
Type=simple
User=root
Group=www-data
WorkingDirectory=/opt/git-dashboard
ExecStart=/opt/git-dashboard/bin/git-repo-logging
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
Environment="LOG_SOURCES_PATH=/var/log"
Environment="SERVER_PORT=8081"
Environment="LOG_LEVEL=info"
Environment="CONFIG_PATH=/etc/git-dashboard/logging-config.json"

# Create directories with appropriate permissions
ExecStartPre=/bin/mkdir -p /var/log/git-dashboard/logs
ExecStartPre=/bin/mkdir -p /etc/git-dashboard
ExecStartPre=/bin/chown -R root:www-data /var/log/git-dashboard
ExecStartPre=/bin/chmod -R 775 /var/log/git-dashboard
ExecStartPre=/bin/chown -R root:www-data /etc/git-dashboard
ExecStartPre=/bin/chmod -R 775 /etc/git-dashboard

[Install]
WantedBy=multi-user.target
