#pragma once
#include <string>
#include <chrono>
#include <optional>
#include <unordered_map>
#include <vector>

namespace logging {

// Log types
enum class LogType {
    INFO,
    WARNING,
    ERROR,
    SUCCESS,
    DEBUG
};

// Log categories
enum class LogCategory {
    SYSTEM,
    SECURITY,
    APPLICATION,
    DATABASE,
    CUSTOM
};

// Log entry structure
struct LogEntry {
    // Required fields
    std::chrono::system_clock::time_point timestamp;
    LogType type;
    std::string message;
    std::string source;

    // Optional fields
    std::optional<std::string> component;
    std::optional<std::string> unit;
    std::optional<std::string> hostname;
    std::optional<std::string> commit;
    std::optional<std::string> author;
    std::optional<std::string> rawLine;
    std::optional<LogCategory> category;

    // Storage metadata
    std::optional<std::string> storageId;  // ID in the storage system
    std::optional<std::string> storageTier; // Which tier is storing this entry

    // Additional metadata
    std::unordered_map<std::string, std::string> metadata;

    // Constructor with required fields
    LogEntry(
        std::chrono::system_clock::time_point timestamp,
        LogType type,
        std::string message,
        std::string source
    ) : timestamp(timestamp), type(type), message(std::move(message)), source(std::move(source)) {}

    // Default constructor
    LogEntry() : timestamp(std::chrono::system_clock::now()), type(LogType::INFO), message(""), source("dashboard") {}
};

// Helper functions for LogEntry
inline std::string logTypeToString(LogType type) {
    switch (type) {
        case LogType::INFO: return "info";
        case LogType::WARNING: return "warning";
        case LogType::ERROR: return "error";
        case LogType::SUCCESS: return "success";
        case LogType::DEBUG: return "debug";
        default: return "unknown";
    }
}

inline LogType logTypeFromString(const std::string& typeStr) {
    if (typeStr == "info") return LogType::INFO;
    if (typeStr == "warning") return LogType::WARNING;
    if (typeStr == "error") return LogType::ERROR;
    if (typeStr == "success") return LogType::SUCCESS;
    if (typeStr == "debug") return LogType::DEBUG;
    return LogType::INFO;
}

inline std::string logCategoryToString(LogCategory category) {
    switch (category) {
        case LogCategory::SYSTEM: return "system";
        case LogCategory::SECURITY: return "security";
        case LogCategory::APPLICATION: return "application";
        case LogCategory::DATABASE: return "database";
        case LogCategory::CUSTOM: return "custom";
        default: return "unknown";
    }
}

inline LogCategory logCategoryFromString(const std::string& categoryStr) {
    if (categoryStr == "system") return LogCategory::SYSTEM;
    if (categoryStr == "security") return LogCategory::SECURITY;
    if (categoryStr == "application") return LogCategory::APPLICATION;
    if (categoryStr == "database") return LogCategory::DATABASE;
    if (categoryStr == "custom") return LogCategory::CUSTOM;
    return LogCategory::SYSTEM;
}

} // namespace logging
