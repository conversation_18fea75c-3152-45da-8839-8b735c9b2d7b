#!/bin/bash
# Setup script for Git Repository Logging Agent database

# Default values
DB_NAME="git_logging"
DB_USER="git_logger"
DB_PASSWORD="change_me_please"
DB_HOST="localhost"
DB_PORT="5432"
SCHEMA_FILE="$(dirname "$0")/schema.sql"
CURRENT_SCHEMA_VERSION="1.0.0"
FORCE_UPDATE=false
SKIP_VERSION_CHECK=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --db-name)
      DB_NAME="$2"
      shift 2
      ;;
    --db-user)
      DB_USER="$2"
      shift 2
      ;;
    --db-password)
      DB_PASSWORD="$2"
      shift 2
      ;;
    --db-host)
      DB_HOST="$2"
      shift 2
      ;;
    --db-port)
      DB_PORT="$2"
      shift 2
      ;;
    --schema-file)
      SCHEMA_FILE="$2"
      shift 2
      ;;
    --force-update)
      FORCE_UPDATE=true
      shift
      ;;
    --skip-version-check)
      SKIP_VERSION_CHECK=true
      shift
      ;;
    --list-databases)
      echo "Listing all PostgreSQL databases:"
      sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -c "\l"
      exit 0
      ;;
    --check-version)
      if [ -z "$2" ]; then
        echo "Error: Database name required for version check"
        exit 1
      fi
      echo "Checking schema version for database: $2"
      VERSION=$(sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -d "$2" -t -c "SELECT version FROM schema_version ORDER BY id DESC LIMIT 1;" 2>/dev/null)
      if [ $? -eq 0 ] && [ ! -z "$VERSION" ]; then
        echo "Schema version: $VERSION"
      else
        echo "No schema version found or database does not exist"
      fi
      exit 0
      ;;
    --delete-database)
      if [ -z "$2" ]; then
        echo "Error: Database name required for deletion"
        exit 1
      fi
      read -p "Are you sure you want to delete database '$2'? This cannot be undone. (y/n): " confirm
      if [[ $confirm == [yY] ]]; then
        echo "Deleting database: $2"
        sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -c "DROP DATABASE IF EXISTS \"$2\";"
        if [ $? -eq 0 ]; then
          echo "Database deleted successfully"
        else
          echo "Failed to delete database"
          exit 1
        fi
      else
        echo "Database deletion cancelled"
      fi
      exit 0
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --db-name NAME       Database name (default: git_logging)"
      echo "  --db-user USER       Database user (default: git_logger)"
      echo "  --db-password PASS   Database password (default: change_me_please)"
      echo "  --db-host HOST       Database host (default: localhost)"
      echo "  --db-port PORT       Database port (default: 5432)"
      echo "  --schema-file FILE   Path to schema file (default: ./schema.sql)"
      echo "  --force-update       Force schema update even if version mismatch"
      echo "  --skip-version-check Skip schema version check"
      echo "  --list-databases     List all PostgreSQL databases and exit"
      echo "  --check-version DB   Check schema version for specified database and exit"
      echo "  --delete-database DB Delete specified database (with confirmation) and exit"
      echo "  --help               Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL client not found. Please install PostgreSQL first."
    exit 1
fi

# Check if schema file exists
if [ ! -f "$SCHEMA_FILE" ]; then
    echo "Schema file not found: $SCHEMA_FILE"
    exit 1
fi

echo "Setting up Git Repository Logging Agent database..."
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo "Host: $DB_HOST:$DB_PORT"

# Check if PostgreSQL server is running
if ! sudo -u postgres pg_isready -h "$DB_HOST" -p "$DB_PORT" &> /dev/null; then
    echo "PostgreSQL server is not running on $DB_HOST:$DB_PORT"
    exit 1
fi

# Check if database exists
if sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
    echo "Database $DB_NAME already exists."

    # Check schema version if database exists and version check is not skipped
    if [ "$SKIP_VERSION_CHECK" = false ]; then
        echo "Checking schema version..."
        DB_VERSION=$(sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -t -c "SELECT version FROM schema_version ORDER BY id DESC LIMIT 1;" 2>/dev/null)

        if [ $? -eq 0 ] && [ ! -z "$DB_VERSION" ]; then
            DB_VERSION=$(echo $DB_VERSION | tr -d '[:space:]')
            echo "Current schema version: $DB_VERSION"
            echo "Required schema version: $CURRENT_SCHEMA_VERSION"

            if [ "$DB_VERSION" != "$CURRENT_SCHEMA_VERSION" ]; then
                if [ "$FORCE_UPDATE" = true ]; then
                    echo "Schema version mismatch, but --force-update is set. Proceeding with update."
                else
                    echo "Schema version mismatch. Use --force-update to override or --skip-version-check to ignore."
                    echo "WARNING: Updating the schema may cause data loss if the versions are incompatible."
                    exit 1
                fi
            else
                echo "Schema version matches. No update needed."
            fi
        else
            echo "No schema version found. Database may not be initialized properly."
            if [ "$FORCE_UPDATE" = true ]; then
                echo "Proceeding with schema update due to --force-update."
            else
                echo "Use --force-update to apply the schema anyway."
                exit 1
            fi
        fi
    else
        echo "Skipping schema version check due to --skip-version-check."
    fi
else
    echo "Creating database $DB_NAME..."
    sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -c "CREATE DATABASE $DB_NAME;"
    if [ $? -ne 0 ]; then
        echo "Failed to create database."
        exit 1
    fi
fi

# Check if user exists
if sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -c "\du" | grep -qw "$DB_USER"; then
    echo "User $DB_USER already exists."
else
    echo "Creating user $DB_USER..."
    sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -c "CREATE USER $DB_USER WITH ENCRYPTED PASSWORD '$DB_PASSWORD';"
    if [ $? -ne 0 ]; then
        echo "Failed to create user."
        exit 1
    fi
fi

# Grant privileges
echo "Granting privileges to $DB_USER on $DB_NAME..."
sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"
if [ $? -ne 0 ]; then
    echo "Failed to grant privileges."
    exit 1
fi

# Apply schema
echo "Applying database schema..."
sudo -u postgres psql -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -f "$SCHEMA_FILE"
if [ $? -ne 0 ]; then
    echo "Failed to apply schema."
    exit 1
fi

# Create cron job for pruning
echo "Setting up cron job for log pruning..."
CRON_JOB="0 2 * * * sudo -u postgres psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -c 'SELECT prune_old_logs();' > /dev/null 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "prune_old_logs"; then
    echo "Cron job for log pruning already exists."
else
    # Add to crontab
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    if [ $? -ne 0 ]; then
        echo "Failed to create cron job. You may need to manually set up log pruning."
    else
        echo "Cron job for log pruning created successfully."
    fi
fi

echo "Database setup completed successfully."

# Generate connection string for the logging agent
CONNECTION_STRING="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
echo ""
echo "Use the following connection string in your configuration:"
echo "$CONNECTION_STRING"
echo ""
echo "You can also set it as an environment variable:"
echo "export DB_CONNECTION_STRING=\"$CONNECTION_STRING\""

# Save connection string to a file
echo "Saving connection string to .env file..."
echo "DB_CONNECTION_STRING=\"$CONNECTION_STRING\"" > "$(dirname "$0")/.env"
echo "Connection string saved to $(dirname "$0")/.env"
