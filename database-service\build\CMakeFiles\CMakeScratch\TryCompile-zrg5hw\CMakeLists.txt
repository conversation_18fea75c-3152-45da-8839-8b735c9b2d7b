cmake_minimum_required(VERSION 3.31.5.0)
cmake_policy(SET CMP0126 OLD)
cmake_policy(SET CMP0128 OLD)
project(CMAKE_TRY_COMPILE CXX)
set(CMAKE_VERBOSE_MAKEFILE 1)
set(CMAKE_CXX_FLAGS "")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${COMPILE_DEFINITIONS}")
set(CMAKE_CXX_FLAGS_DEBUG "-g")
set(CMAKE_EXE_LINKER_FLAGS "")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${EXE_LINKER_FLAGS}")
include_directories(${INCLUDE_DIRECTORIES})
set(CMAKE_SUPPRESS_REGENERATION 1)
link_directories(${LINK_DIRECTORIES})
add_definitions([==[-DHAVE_STD_FORMAT]==])
cmake_policy(SET CMP0065 NEW)
cmake_policy(SET CMP0083 NEW)
cmake_policy(SET CMP0155 OLD)
cmake_policy(SET CMP0157 OLD)
include("${CMAKE_ROOT}/Modules/Internal/HeaderpadWorkaround.cmake")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "D:/Augment/project-tracker/database-service/build/CMakeFiles/CMakeScratch/TryCompile-zrg5hw")
add_executable(cmTC_0ee64)
target_sources(cmTC_0ee64 PRIVATE
  "D:/Augment/project-tracker/database-service/build/CMakeFiles/CMakeScratch/TryCompile-zrg5hw/src.cxx"
)
file(GENERATE OUTPUT "${CMAKE_BINARY_DIR}/cmTC_0ee64_loc"
     CONTENT $<TARGET_FILE:cmTC_0ee64>)
set_property(TARGET cmTC_0ee64 PROPERTY "CXX_EXTENSIONS" "OFF")
set_property(TARGET cmTC_0ee64 PROPERTY "CXX_STANDARD" "23")
set_property(TARGET cmTC_0ee64 PROPERTY "CXX_STANDARD_REQUIRED" "ON")
target_link_libraries(cmTC_0ee64 ${LINK_LIBRARIES})
