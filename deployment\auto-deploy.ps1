# Project Tracker Auto-Deployment Script
# Version: 2.0.0
# Last Updated: 2025-03-04
# Author: CHCIT DevOps Team
#
# This script automates deployment from Windows build servers to Ubuntu targets
# using Windows Subsystem for Linux (WSL) for Linux command execution.

param(
    [Parameter(Mandatory=$true)]
    [string]$TargetServer,
    
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$CloudflareToken,
    
    [Parameter(Mandatory=$false)]
    [string]$SshKeyPath = "$env:USERPROFILE\.ssh\id_rsa",
    
    [Parameter(Mandatory=$false)]
    [string]$RemoteUser = "ubuntu"
)

# Error handling
$ErrorActionPreference = "Stop"

# Logging function
function Write-Log {
    param($Message, $Level = "INFO")
    Write-Host "[$((Get-Date).ToString('yyyy-MM-dd HH:mm:ss'))][$Level] $Message"
}

# Function to check WSL installation
function Test-WslInstallation {
    try {
        $wslCheck = wsl --status
        if ($LASTEXITCODE -ne 0) {
            throw "WSL check failed"
        }
        Write-Log "WSL is properly installed"
        return $true
    }
    catch {
        Write-Log "WSL is not properly installed. Please install WSL and Ubuntu." "ERROR"
        Write-Log "Run: wsl --install -d Ubuntu" "INFO"
        return $false
    }
}

# Function to ensure WSL Ubuntu is installed
function Test-UbuntuDistro {
    $ubuntu = wsl --list | Select-String "Ubuntu"
    if (-not $ubuntu) {
        Write-Log "Ubuntu distribution not found in WSL" "ERROR"
        Write-Log "Installing Ubuntu in WSL..." "INFO"
        wsl --install -d Ubuntu
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to install Ubuntu in WSL"
        }
    }
    Write-Log "Ubuntu distribution is available"
}

# Function to copy files to WSL
function Copy-ToWsl {
    param($Source, $Destination)
    Write-Log "Copying $Source to WSL at $Destination"
    wsl mkdir -p "$(dirname $Destination)"
    Get-Content $Source | wsl tee $Destination > $null
    wsl chmod 600 $Destination
}

# Function to prepare SSL setup in WSL
function Initialize-SslSetup {
    param($Token)
    
    # Create WSL directories
    Write-Log "Creating SSL directories in WSL"
    wsl sudo mkdir -p /etc/letsencrypt/cloudflare
    
    # Store Cloudflare token
    $tokenFile = "/etc/letsencrypt/cloudflare/cf-token.secret"
    $Token | wsl sudo tee $tokenFile > $null
    wsl sudo chmod 600 $tokenFile
    
    # Create credentials file
    $credContent = @"
# Cloudflare API credentials for DNS validation
dns_cloudflare_api_token = $Token
"@
    $credContent | wsl sudo tee /etc/letsencrypt/cloudflare/credentials.ini > $null
    wsl sudo chmod 600 /etc/letsencrypt/cloudflare/credentials.ini
    
    Write-Log "SSL setup initialized in WSL"
}

# Function to run SSL setup in WSL
function Start-SslSetup {
    param($Domain)
    Write-Log "Starting SSL setup for $Domain"
    
    # Copy SSL setup script to WSL
    $scriptPath = Join-Path $PSScriptRoot "scripts\ssl-setup.sh"
    $wslPath = "/tmp/ssl-setup.sh"
    Copy-ToWsl -Source $scriptPath -Destination $wslPath
    
    # Run SSL setup
    wsl sudo bash $wslPath $Domain
    if ($LASTEXITCODE -ne 0) {
        throw "SSL setup failed"
    }
    
    Write-Log "SSL setup completed successfully"
}

# Function to deploy certificates to target server
function Copy-CertificatesToTarget {
    param($Server, $Domain)
    Write-Log "Deploying certificates to $Server"
    
    # Create archive of certificates
    wsl sudo tar czf /tmp/certs.tar.gz -C /etc/letsencrypt/live/$Domain .
    
    # Copy to target server
    $scpCommand = "scp -i `"$SshKeyPath`" -o StrictHostKeyChecking=no /tmp/certs.tar.gz $RemoteUser@$Server:/tmp/"
    wsl $scpCommand
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to copy certificates to target server"
    }
    
    # Extract on target server
    $sshCommand = "ssh -i `"$SshKeyPath`" -o StrictHostKeyChecking=no $RemoteUser@$Server 'sudo mkdir -p /etc/letsencrypt/live/$Domain && sudo tar xzf /tmp/certs.tar.gz -C /etc/letsencrypt/live/$Domain && sudo rm /tmp/certs.tar.gz'"
    wsl $sshCommand
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to extract certificates on target server"
    }
    
    Write-Log "Certificates deployed successfully"
}

# Main execution
try {
    Write-Log "Starting deployment process"
    
    # Validate WSL setup
    if (-not (Test-WslInstallation)) {
        throw "WSL setup validation failed"
    }
    Test-UbuntuDistro
    
    # Initialize SSL in WSL
    Initialize-SslSetup -Token $CloudflareToken
    
    # Run SSL setup
    Start-SslSetup -Domain $Domain
    
    # Deploy to target server
    Copy-CertificatesToTarget -Server $TargetServer -Domain $Domain
    
    Write-Log "Deployment completed successfully"
}
catch {
    Write-Log $_.Exception.Message "ERROR"
    exit 1
}
