#include "database-service/metrics/metrics_collector.hpp"
#include "database-service/utils/logger.hpp"
#include <format>
#include <algorithm>
#include <chrono>
#include <thread>

namespace dbservice::metrics {

MetricsCollector& MetricsCollector::getInstance() {
    static MetricsCollector instance;
    return instance;
}

MetricsCollector::MetricsCollector()
    : enabled_(false),
      collectionIntervalSeconds_(60),
      retentionDays_(7),
      running_(false) {
}

MetricsCollector::~MetricsCollector() {
    stop();
}

bool MetricsCollector::initialize(bool enabled, int collectionIntervalSeconds, int retentionDays) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    enabled_ = enabled;
    collectionIntervalSeconds_ = collectionIntervalSeconds;
    retentionDays_ = retentionDays;
    
    utils::Logger::info(std::format("Metrics collector initialized: enabled={}, interval={}s, retention={}d",
                                  enabled_ ? "true" : "false", collectionIntervalSeconds_, retentionDays_));
    
    return true;
}

void MetricsCollector::start() {
    if (!enabled_) {
        utils::Logger::info("Metrics collection is disabled");
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (running_) {
        utils::Logger::info("Metrics collector is already running");
        return;
    }
    
    running_ = true;
    collectionThread_ = std::thread(&MetricsCollector::run, this);
    
    utils::Logger::info("Metrics collector started");
}

void MetricsCollector::stop() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    if (collectionThread_.joinable()) {
        collectionThread_.join();
    }
    
    utils::Logger::info("Metrics collector stopped");
}

void MetricsCollector::recordMetric(const std::string& name, double value) {
    if (!enabled_ || !running_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    MetricValue metricValue;
    metricValue.value = value;
    metricValue.timestamp = std::chrono::system_clock::now();
    
    metrics_[name].push_back(metricValue);
}

std::vector<MetricValue> MetricsCollector::getMetrics(const std::string& name, 
                                                   const std::chrono::system_clock::time_point& startTime,
                                                   const std::chrono::system_clock::time_point& endTime) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<MetricValue> result;
    
    auto it = metrics_.find(name);
    if (it == metrics_.end()) {
        return result;
    }
    
    for (const auto& metricValue : it->second) {
        if (metricValue.timestamp >= startTime && metricValue.timestamp <= endTime) {
            result.push_back(metricValue);
        }
    }
    
    return result;
}

std::vector<std::string> MetricsCollector::getMetricNames() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<std::string> names;
    names.reserve(metrics_.size());
    
    for (const auto& [name, _] : metrics_) {
        names.push_back(name);
    }
    
    return names;
}

std::shared_ptr<MetricValue> MetricsCollector::getLatestMetric(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = metrics_.find(name);
    if (it == metrics_.end() || it->second.empty()) {
        return nullptr;
    }
    
    const auto& values = it->second;
    auto latestIt = std::max_element(values.begin(), values.end(),
        [](const MetricValue& a, const MetricValue& b) {
            return a.timestamp < b.timestamp;
        });
    
    if (latestIt == values.end()) {
        return nullptr;
    }
    
    return std::make_shared<MetricValue>(*latestIt);
}

void MetricsCollector::collectSystemMetrics() {
    try {
        // CPU usage
        // This is a simplified example. In a real implementation, you would use
        // platform-specific APIs to get CPU usage.
        recordMetric("system.cpu.usage", 0.5); // 50% CPU usage
        
        // Memory usage
        // This is a simplified example. In a real implementation, you would use
        // platform-specific APIs to get memory usage.
        recordMetric("system.memory.usage", 0.3); // 30% memory usage
        
        // Disk usage
        // This is a simplified example. In a real implementation, you would use
        // platform-specific APIs to get disk usage.
        recordMetric("system.disk.usage", 0.4); // 40% disk usage
        
        // Network usage
        // This is a simplified example. In a real implementation, you would use
        // platform-specific APIs to get network usage.
        recordMetric("system.network.rx", 1024); // 1 KB/s received
        recordMetric("system.network.tx", 512); // 0.5 KB/s transmitted
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Error collecting system metrics: {}", e.what()));
    }
}

void MetricsCollector::cleanupOldMetrics() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto now = std::chrono::system_clock::now();
    auto cutoff = now - std::chrono::hours(24 * retentionDays_);
    
    for (auto& [name, values] : metrics_) {
        values.erase(
            std::remove_if(values.begin(), values.end(),
                [&cutoff](const MetricValue& value) {
                    return value.timestamp < cutoff;
                }),
            values.end());
    }
}

void MetricsCollector::run() {
    utils::Logger::info(std::format("Metrics collection thread started with interval {}s", collectionIntervalSeconds_));
    
    while (running_) {
        try {
            // Collect metrics
            collectSystemMetrics();
            
            // Clean up old metrics
            cleanupOldMetrics();
            
            // Sleep until next collection
            std::this_thread::sleep_for(std::chrono::seconds(collectionIntervalSeconds_));
        } catch (const std::exception& e) {
            utils::Logger::error(std::format("Error in metrics collection thread: {}", e.what()));
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    utils::Logger::info("Metrics collection thread stopped");
}

} // namespace dbservice::metrics
