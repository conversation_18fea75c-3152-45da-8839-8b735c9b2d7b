# Simple Git Server Deployment Script

# Define paths
$localPath = "d:/Codeium/CHCIT/project-tracker/git-server-web"

# Files to deploy
$filesToDeploy = @(
    "app.py",
    "js/git-repositories.js"
)

Write-Host "Starting deployment preparation..." -ForegroundColor Green

# Create a temporary directory for files to transfer
$tempDir = "./temp-deploy"
if (Test-Path $tempDir) {
    Remove-Item -Path $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# Copy files to temporary directory with proper structure
foreach ($file in $filesToDeploy) {
    $sourceFile = Join-Path $localPath $file
    $targetDir = Join-Path $tempDir (Split-Path $file)
    $targetFile = Join-Path $tempDir $file
    
    # Create directory structure if needed
    if (!(Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
    }
    
    # Copy the file
    Copy-Item -Path $sourceFile -Destination $targetFile -Force
    Write-Host "Prepared $file for deployment" -ForegroundColor Cyan
}

# Create archive
$archiveName = "git-dashboard-deploy.zip"
Compress-Archive -Path "$tempDir/*" -DestinationPath $archiveName -Force
Write-Host "Created deployment archive: $archiveName" -ForegroundColor Cyan

Write-Host "\nSimulated deployment completed! Files are ready for manual transfer." -ForegroundColor Green
Write-Host "The following files have been updated and are ready for deployment:" -ForegroundColor Green
foreach ($file in $filesToDeploy) {
    Write-Host "- $file" -ForegroundColor Cyan
}

Write-Host "\nTo complete deployment:" -ForegroundColor Yellow
Write-Host "1. Transfer the $archiveName file to the git server" -ForegroundColor Yellow
Write-Host "2. Extract the files to the appropriate locations" -ForegroundColor Yellow
Write-Host "3. Restart the git dashboard service" -ForegroundColor Yellow
