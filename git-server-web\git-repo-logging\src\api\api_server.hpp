#pragma once
#include "../services/logging_service.hpp"
#include <memory>
#include <string>
#include <functional>
#include <unordered_map>
#include <boost/asio.hpp>
#include <boost/beast.hpp>
#include <boost/beast/websocket.hpp>
#include <thread>
#include <mutex>
#include <atomic>
#include <vector>

namespace beast = boost::beast;
namespace http = beast::http;
namespace websocket = beast::websocket;
namespace net = boost::asio;
using tcp = boost::asio::ip::tcp;

// Forward declaration
class WebSocketSession;

class ApiServer {
public:
    ApiServer(unsigned short port, std::shared_ptr<LoggingService> service);
    ~ApiServer();

    void start();
    void stop();

private:
    // HTTP request handler
    using HttpHandler = std::function<void(http::request<http::string_body>&, http::response<http::string_body>&)>;

    // WebSocket handler
    using WebSocketHandler = std::function<void(std::shared_ptr<WebSocketSession>, const std::string&)>;

    // Setup API endpoints
    void setupEndpoints();

    // HTTP handlers
    void handleGetLogs(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleGetLogSources(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleGetLogStats(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleGetAvailableLogs(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleScanLogs(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleConfigureLogs(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleHealthCheck(http::request<http::string_body>& req, http::response<http::string_body>& res);

    // WebSocket handlers
    void handleWebSocketUpgrade(http::request<http::string_body>& req, http::response<http::string_body>& res);
    void handleWebSocketMessage(std::shared_ptr<WebSocketSession> session, const std::string& message);

    // Accept incoming connections
    void doAccept();

    // Process HTTP request
    void handleRequest(tcp::socket socket);

    // Parse query parameters
    std::unordered_map<std::string, std::string> parseQueryParams(const std::string& query);

    // Server state
    unsigned short port_;
    std::shared_ptr<LoggingService> loggingService_;
    std::atomic<bool> running_;

    // ASIO context and acceptor
    net::io_context ioc_;
    tcp::acceptor acceptor_;
    std::thread acceptorThread_;

    // HTTP route handlers
    std::unordered_map<std::string, HttpHandler> httpHandlers_;

    // WebSocket sessions
    std::vector<std::shared_ptr<WebSocketSession>> webSocketSessions_;
    std::mutex webSocketSessionsMutex_;

    // WebSocket route handlers
    std::unordered_map<std::string, WebSocketHandler> webSocketHandlers_;
};

// WebSocket session class
class WebSocketSession : public std::enable_shared_from_this<WebSocketSession> {
public:
    WebSocketSession(tcp::socket socket, ApiServer& server);
    ~WebSocketSession();

    void start();
    void send(const std::string& message);
    void close();

private:
    void doRead();

    websocket::stream<tcp::socket> ws_;
    ApiServer& server_;
    beast::flat_buffer buffer_;
    std::atomic<bool> closed_;
};
