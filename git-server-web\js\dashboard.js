// Debug wrapper for fetch to diagnose API issues
const originalFetch = window.fetch;
window.fetch = function(url, options) {
    console.log(`Fetching: ${url}`);
    return originalFetch(url, options)
        .then(response => {
            console.log(`Response for ${url}: status=${response.status}`);
            return response;
        })
        .catch(error => {
            console.error(`Error fetching ${url}:`, error);
            throw error;
        });
};

/**
 * Dashboard Core Module
 * Handles core dashboard functionality and coordinates components
 */
const Dashboard = {
    // Configuration
    config: {
        refreshInterval: 30000, // 30 seconds - used for global refresh button
        // No more endpoints - each component manages its own now
    },

    // Application state
    state: {
        isInitialized: false,
        status: 'loading',
        error: null,
        lastUpdated: null,
        refreshInProgress: false
        // No more historyData or charts - moved to MetricsHistory
    },

    // Initialize dashboard
    init() {
        console.log('Dashboard.init() - Starting initialization');

        if (this.state.isInitialized) {
            console.warn('Dashboard already initialized');
            return this;
        }

        // Update last refreshed timestamp
        this.state.lastUpdated = new Date();

        // Update the global last updated timestamp
        const lastUpdatedEl = document.getElementById('last-updated');
        if (lastUpdatedEl) {
            lastUpdatedEl.textContent = this.state.lastUpdated.toLocaleString();
        }

        // Set up the event listeners
        this.setupEventListeners();

        // Initialize Git repositories if available
        this.initializeGitComponents();

        // Activate the repositories tab programmatically
        this.activateRepositoriesTab();

        // Start refresh timer
        this.startRefreshTimer();

        // Set initialization state
        this.state.isInitialized = true;

        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Dashboard initialized', 'dashboard');
        }

        console.log('Dashboard.init() - Initialization complete');
        return this;
    },

    /**
     * Initialize Git repository components
     */
    initializeGitComponents() {
        console.log('Initializing Git components');

        // Initialize Git Repository Manager if available
        if (window.GitRepositoryManager) {
            console.log('Found GitRepositoryManager, initializing...');
            GitRepositoryManager.init().catch(err => {
                console.error('Error initializing GitRepositoryManager:', err);
            });
        } else {
            console.warn('GitRepositoryManager not available');
        }
    },

    // Set up the event listeners for the dashboard
    setupEventListeners() {
        // Tab change event listener
        const tabs = document.querySelectorAll('a[data-bs-toggle="tab"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', (event) => {
                const targetTab = event.target.getAttribute('href');
                console.log(`Tab changed to: ${targetTab}`);

                // Handle specific tab actions
                if (targetTab === '#repositories') {
                    console.log('Repositories tab selected, refreshing repository data');
                    if (window.GitRepositoryManager) {
                        // First try to select a repository immediately if data is already available
                        const existingRepos = window.GitRepositoryManager.getRepositories();
                        if (existingRepos && existingRepos.length > 0 && window.GitRepositoryList) {
                            console.log('Auto-selecting first repository from existing data');
                            setTimeout(() => {
                                window.GitRepositoryList.selectRepository(existingRepos[0].name);
                            }, 100);
                        }

                        // Then refresh the data
                        GitRepositoryManager.fetchRepositories()
                            .then(() => {
                                // Select the first repository after fetching
                                if (window.GitRepositoryList) {
                                    const repos = window.GitRepositoryManager.getRepositories();
                                    if (repos && repos.length > 0) {
                                        console.log('Auto-selecting first repository after data refresh');
                                        window.GitRepositoryList.selectRepository(repos[0].name);
                                    }
                                }
                            })
                            .catch(err => {
                                console.error('Error refreshing repositories on tab selection:', err);
                            });
                    }
                }

                // Log the tab change
                if (window.DashboardLogger) {
                    window.DashboardLogger.addLog('info', `Tab changed to ${targetTab}`, 'ui');
                }
            });
        });

        // Refresh button event listener
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                // Prevent rapid successive clicks
                if (this.state.refreshInProgress) {
                    console.log('Refresh already in progress, ignoring click');
                    return;
                }

                this.state.refreshInProgress = true;

                // Store initial button HTML
                const originalButtonHTML = refreshBtn.innerHTML;

                // Replace with spinning icon
                refreshBtn.innerHTML = `<i class="fas fa-sync spinning"></i> Refreshing...`;
                refreshBtn.disabled = true;

                // Perform refresh
                this.refreshDashboard().then(() => {
                    // After refresh completes, restore original button after a delay
                    setTimeout(() => {
                        refreshBtn.innerHTML = originalButtonHTML;
                        refreshBtn.disabled = false;
                        this.state.refreshInProgress = false;
                    }, 1000);
                }).catch(error => {
                    console.error('Error during refresh:', error);

                    // Still restore button on error
                    setTimeout(() => {
                        refreshBtn.innerHTML = originalButtonHTML;
                        refreshBtn.disabled = false;
                        this.state.refreshInProgress = false;
                    }, 1000);
                });

                // Log the refresh action
                if (window.DashboardLogger) {
                    window.DashboardLogger.addLog('info', 'Manual dashboard refresh triggered', 'ui');
                }
            });
        }
    },

    /**
     * Activate the overview tab programmatically
     */
    activateRepositoriesTab() {
        // This method is kept for backward compatibility but now activates the overview tab
        // Find the overview tab and activate it programmatically
        const overviewTab = document.getElementById('overview-tab');
        if (overviewTab) {
            console.log('Activating overview tab programmatically');
            const tab = new bootstrap.Tab(overviewTab);
            tab.show();
        }
    },

    // Refresh dashboard
    async refreshDashboard() {
        console.log('Manual refresh triggered');

        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Manual dashboard refresh triggered', 'dashboard');
        }

        // Update last refreshed timestamp
        this.state.lastUpdated = new Date();

        // Update the global last updated timestamp
        const lastUpdatedEl = document.getElementById('last-updated');
        if (lastUpdatedEl) {
            lastUpdatedEl.textContent = this.state.lastUpdated.toLocaleString();
        }

        // Refresh Git components if available
        if (window.GitRepositoryManager) {
            try {
                await GitRepositoryManager.fetchRepositories();
                console.log('Git repositories refreshed');
            } catch (error) {
                console.error('Error refreshing Git repositories:', error);
            }
        }

        // Note: We don't need to explicitly refresh logs here anymore
        // The logging.js module now hooks into this method and handles its own refresh
    },

    // Start refresh timer
    startRefreshTimer() {
        // Clear existing timer
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        // Set up new timer
        this.refreshTimer = setInterval(() => {
            this.refreshDashboard();
        }, this.config.refreshInterval);
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Dashboard.init();
});
