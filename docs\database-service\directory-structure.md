# Database Service Directory Structure

This document describes the directory structure of the Database Service project.

## Project Organization

The Database Service project follows a traditional C++ project structure with separate directories for headers and implementation files:

```
database-service-traditional/
├── CMakeLists.txt                  - Main CMake configuration
├── README.md                       - Project documentation
├── config/                         - Configuration files
│   ├── config.json                 - Main configuration
│   └── config.template.json        - Configuration template
├── include/                        - Header files
│   └── database-service/           - Public API headers
│       ├── api/                    - API server headers
│       │   └── api_server.hpp      - API server interface
│       ├── core/                   - Core functionality headers
│       │   ├── connection.hpp      - Database connection interface
│       │   ├── connection_manager.hpp - Connection pool interface
│       │   └── transaction.hpp     - Transaction interface
│       ├── database_service.hpp    - Main service interface
│       ├── schema/                 - Schema management headers
│       │   └── schema_manager.hpp  - Schema manager interface
│       ├── security/               - Security headers
│       │   └── security_manager.hpp - Security manager interface
│       └── utils/                  - Utility headers
│           ├── config_manager.hpp  - Configuration manager
│           └── logger.hpp          - Logging utility
├── src/                            - Implementation files
│   ├── api/                        - API implementation
│   │   └── api_server.cpp          - API server implementation
│   ├── core/                       - Core implementation
│   │   ├── connection.cpp          - Database connection implementation
│   │   ├── connection_manager.cpp  - Connection pool implementation
│   │   └── transaction.cpp         - Transaction implementation
│   ├── database_service.cpp        - Main service implementation
│   ├── main.cpp                    - Entry point
│   ├── schema/                     - Schema implementation
│   │   └── schema_manager.cpp      - Schema manager implementation
│   ├── security/                   - Security implementation
│   │   └── security_manager.cpp    - Security manager implementation
│   └── utils/                      - Utility implementation
│       ├── config_manager.cpp      - Configuration manager implementation
│       └── logger.cpp              - Logging utility implementation
├── systemd/                        - Systemd service files
│   └── database-service.service.in - Service template
└── tests/                          - Test files
    ├── CMakeLists.txt              - Test configuration
    ├── api_server_test.cpp         - API server tests
    ├── connection_test.cpp         - Connection tests
    ├── main.cpp                    - Test entry point
    ├── schema_manager_test.cpp     - Schema manager tests
    └── security_manager_test.cpp   - Security manager tests
```

## Source Code Organization

The source code is organized into the following components:

1. **Core**: Database connection and transaction management
2. **API**: HTTP API server and request handlers
3. **Schema**: Database schema management and migrations
4. **Security**: Authentication, authorization, and user management
5. **Utils**: Utility classes for configuration and logging
6. **Main Service**: Coordinates all components

## Production Server Structure

On the Linux production server, the Database Service is installed in the following directory structure:

```
/opt/database-service/
├── bin/                            - Executable files
│   └── database-service            - Main executable
├── config/                         - Configuration files
│   └── config.json                 - Production configuration
├── lib/                            - Library files
│   └── ...                         - Shared libraries
├── logs/                           - Log files
│   └── database-service.log        - Main log file
└── schemas/                        - Schema files
    └── ...                         - SQL schema files
```

The service is managed by systemd with a service file at:

```
/etc/systemd/system/database-service.service
```

Log files may also be found in the system journal:

```
/var/log/journal/
```

## Configuration Files

The Database Service uses the following configuration files:

1. **config.json**: Main configuration file
2. **config.template.json**: Template configuration file with placeholders

## Log Files

The Database Service generates the following log files:

1. **database-service.log**: Main log file
2. **database-service-error.log**: Error log file (if configured)

## Systemd Service

The Database Service is managed by systemd with the following service file:

```
/etc/systemd/system/database-service.service
```

This service file is generated from the template in `systemd/database-service.service.in` during installation.
