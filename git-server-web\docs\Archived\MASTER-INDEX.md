# Git Dashboard Documentation Master Index

## Overview

This document serves as the central hub for all Git Dashboard configuration and setup documentation. Use this index to navigate through the various guides and find specific information quickly.

## Core Documentation

1. [**Git Dashboard Setup Guide**](../git-server-web/GIT-DASHBOARD-SETUP-GUIDE.md)
   - Complete setup instructions
   - Prerequisites and quick installation
   - SSH and SSL configuration
   - Network settings and security features

2. [**Nginx Configuration Guide**](./NGINX-CONFIGURATION-GUIDE.md)
   - Detailed Nginx setup and configuration
   - SSL termination and security headers
   - Rate limiting and proxy settings
   - Performance optimization

3. [**Flask and Gunicorn Guide**](./FLASK-GUNICORN-GUIDE.md)
   - Flask application structure and configuration
   - Gunicorn WSGI server setup
   - Application service management
   - Metrics collection script

4. [**Ubuntu Configuration Guide**](./UBUNTU-CONFIGURATION-GUIDE.md)
   - Ubuntu-specific system settings
   - Security hardening (SSH, AppArmor, Fail2Ban)
   - Performance optimization
   - Log rotation and system monitoring

5. [**Cron Jobs and Server Synchronization Guide**](./CRON-SSL-SYNC-GUIDE.md)
   - Cron job configuration
   - SSL certificate synchronization
   - Multi-server deployment
   - IP binding options

6. [**Git JavaScript Modules Analysis**](./git-javascript-modules-analysis.md)
   - Detailed analysis of Git JavaScript modules
   - Module relationships and architecture
   - Key features and enhancements
   - Technical implementation details
   - Potential areas for improvement

7. [**Git Dashboard Complete Documentation**](./git-dashboard-complete-documentation.md)
   - Comprehensive overview of the entire project
   - Complete file and directory listing
   - Component relationships
   - Deployment and configuration details

## Quick Reference

### Installation Paths

- Installation Directory: `/opt/git-dashboard/`
- Nginx Configuration: `/etc/nginx/sites-enabled/git-dashboard`
- Systemd Service: `/etc/systemd/system/git-dashboard.service`
- Log Files: `/opt/git-dashboard/logs/`
- Data Storage: `/opt/git-dashboard/data/`

### Default Network Configuration

- **IP Binding**: `0.0.0.0` (all interfaces)
- **Nginx Ports**: 80 (HTTP), 443 (HTTPS)
- **Flask Backend**: Port 8000 (internal)
- **Firewall**: UFW enabled (Ports 80, 443 open)

### Security Features

- Root access for system-wide functionality
- File permissions set to 755 for directories, 644 for files
- Rate limiting on API endpoints
- SSL certificate auto-renewal
- Regular security updates via unattended-upgrades

## Common Tasks Reference

### Service Management

```bash
# Start services
sudo systemctl start git-dashboard
sudo systemctl start nginx

# Restart services
sudo systemctl restart git-dashboard
sudo systemctl restart nginx

# Check service status
sudo systemctl status git-dashboard
sudo journalctl -u git-dashboard --since today
```

### Backup and Restore

```bash
# Backup configuration
sudo tar -czf git-dashboard-config-backup-$(date +%Y%m%d).tar.gz -C /opt git-dashboard /etc/nginx/sites-available/git-dashboard.conf /etc/systemd/system/git-dashboard.service

# Restore from backup
sudo tar -xzf git-dashboard-config-backup-*.tar.gz -C /
sudo systemctl daemon-reload
sudo systemctl restart git-dashboard nginx
```

### Log Management

```bash
# View application logs
tail -f /opt/git-dashboard/logs/git-dashboard.log

# View Nginx access logs
tail -f /var/log/nginx/git-dashboard.access.log

# View Nginx error logs
tail -f /var/log/nginx/git-dashboard.error.log
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Dashboard Not Loading**
   - Check Nginx status: `sudo systemctl status nginx`
   - Verify backend service: `sudo systemctl status git-dashboard`
   - Check logs: `tail -f /opt/git-dashboard/logs/git-dashboard.log`
   - Ensure proper firewall settings: `sudo ufw status`

2. **No Metrics Displayed**
   - Verify metrics file: `cat /opt/git-dashboard/data/metrics_history.json`
   - Check cron job: `sudo crontab -l | grep collect-metrics`
   - Run collector manually: `sudo /opt/git-dashboard/collect-metrics.sh`
   - Check script permissions: `ls -la /opt/git-dashboard/collect-metrics.sh`

3. **SSL Certificate Issues**
   - Check certificate status: `sudo certbot certificates`
   - Test renewal: `sudo certbot renew --dry-run`
   - Verify Nginx configuration: `sudo nginx -t`
   - Check certificate files: `ls -la /etc/letsencrypt/live/yourdomain.com/`

4. **Performance Problems**
   - Check system resources: `top`, `free -h`, `df -h`
   - Verify Nginx worker settings in `/etc/nginx/nginx.conf`
   - Check Gunicorn worker count in `/opt/git-dashboard/gunicorn_config.py`
   - Monitor rate limiting: `grep "limiting requests" /var/log/nginx/error.log`

## Maintenance Schedule

| Task | Frequency | Command/Action |
|------|-----------|----------------|
| Check logs | Daily | `tail -f /opt/git-dashboard/logs/git-dashboard.log` |
| Backup configuration | Weekly | `tar -czf git-dashboard-backup-$(date +%Y%m%d).tar.gz -C /opt git-dashboard` |
| Update system | Monthly | `sudo apt update && sudo apt upgrade -y` |
| Rotate metrics history | Monthly | `find /opt/git-dashboard/data/history -type f -mtime +30 -delete` |
| Check SSL certificates | Monthly | `sudo certbot certificates` |

## Version History

- **1.0.0** - Initial documentation release
- **1.1.0** - Added Ubuntu-specific configuration guide
- **1.2.0** - Added cron jobs and server synchronization guide
- **1.3.0** - Added master index and cross-references
- **1.4.0** - Added Git JavaScript modules analysis and complete project documentation

## Support Resources

- Report issues on the internal ticketing system
- Email support: <EMAIL>
- Documentation repository: https://github.com/yourdomain/git-dashboard-docs
