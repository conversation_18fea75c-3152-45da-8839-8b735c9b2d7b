#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <iostream>
#include <thread>
#include <sstream>
#include <nlohmann/json.hpp>
#include <pqxx/pqxx>

// Boost Beast and Asio headers
#include <boost/beast/core.hpp>
#include <boost/beast/http.hpp>
#include <boost/beast/version.hpp>
#include <boost/asio/ip/tcp.hpp>
#include <boost/asio/strand.hpp>

// Import the core module for Task type
import database.core;

namespace beast = boost::beast;
namespace http = beast::http;
namespace net = boost::asio;
using tcp = boost::asio::ip::tcp;
using json = nlohmann::json;

namespace dbservice {
namespace api {

// Helper class for API request handling
class RequestHandler {
public:
    RequestHandler(std::shared_ptr<core::ConnectionManager> connectionManager,
                  std::shared_ptr<security::SecurityManager> securityManager)
        : connectionManager_(connectionManager),
          securityManager_(securityManager) {}

    // Authentication helper
    bool authenticate(const http::request<http::string_body>& req, 
                     http::response<http::string_body>& res,
                     std::string& username) {
        auto authIter = req.find(http::field::authorization);
        if (authIter == req.end()) {
            res.result(http::status::unauthorized);
            res.body() = R"({"error":"Authentication required"})";
            return false;
        }

        std::string authHeader = std::string(authIter->value());
        if (authHeader.empty() || authHeader.substr(0, 7) != "Bearer ") {
            res.result(http::status::unauthorized);
            res.body() = R"({"error":"Authentication required"})";
            return false;
        }

        std::string token = authHeader.substr(7);
        if (!securityManager_->verifyToken(token, username)) {
            res.result(http::status::forbidden);
            res.body() = R"({"error":"Invalid authentication token"})";
            return false;
        }

        return true;
    }

    // Permission check helper
    bool checkPermission(const std::string& username,
                        const std::string& applicationName,
                        const std::string& schemaName,
                        security::Permission permission,
                        http::response<http::string_body>& res) {
        if (!securityManager_->hasPermission(username, applicationName, schemaName, permission)) {
            res.result(http::status::forbidden);
            res.body() = R"({"error":"Permission denied"})";
            return false;
        }
        return true;
    }

    // Error handling helper
    void handleError(http::response<http::string_body>& res, const std::string& error) {
        res.result(http::status::internal_server_error);
        res.body() = R"({"error":")" + error + R"("})";
    }

    // Query execution
    void executeQuery(const http::request<http::string_body>& req, 
                     http::response<http::string_body>& res) {
        try {
            // Authenticate user
            std::string username;
            if (!authenticate(req, res, username)) {
                return;
            }

            // Parse request body
            json requestBody = json::parse(req.body());

            // Extract query parameters
            std::string query = requestBody["query"].get<std::string>();
            std::string applicationName = requestBody.value("application", "");
            std::string schemaName = requestBody.value("schema", "");

            // Check permissions
            if (!checkPermission(username, applicationName, schemaName, security::Permission::Read, res)) {
                return;
            }

            // Execute query
            auto result = connectionManager_->executeWithConnection([&query](pqxx::connection& conn) {
                pqxx::work txn(conn);
                pqxx::result res = txn.exec(query);
                txn.commit();
                return res;
            });

            // Convert result to JSON
            json jsonResult = json::array();
            for (const auto& row : result) {
                json jsonRow = json::object();
                for (pqxx::row::size_type i = 0; i < row.size(); ++i) {
                    // Get column name directly from the field
                    std::string columnName = row[i].name();
                    if (!row[i].is_null()) {
                        jsonRow[columnName] = row[i].c_str();
                    } else {
                        jsonRow[columnName] = nullptr;
                    }
                }
                jsonResult.push_back(jsonRow);
            }

            // Set response
            res.result(http::status::ok);
            res.body() = jsonResult.dump();
        } catch (const std::exception& e) {
            handleError(res, e.what());
        }
    }

    // Command execution
    void executeCommand(const http::request<http::string_body>& req, 
                       http::response<http::string_body>& res) {
        try {
            // Authenticate user
            std::string username;
            if (!authenticate(req, res, username)) {
                return;
            }

            // Parse request body
            json requestBody = json::parse(req.body());

            // Extract query parameters
            std::string query = requestBody["query"].get<std::string>();
            std::string applicationName = requestBody.value("application", "");
            std::string schemaName = requestBody.value("schema", "");

            // Check permissions
            if (!checkPermission(username, applicationName, schemaName, security::Permission::Write, res)) {
                return;
            }

            // Execute query
            auto result = connectionManager_->executeWithConnection([&query](pqxx::connection& conn) {
                pqxx::work txn(conn);
                pqxx::result res = txn.exec(query);
                txn.commit();
                return res.affected_rows();
            });

            // Set response
            res.result(http::status::ok);
            res.body() = R"({"affected_rows":)" + std::to_string(result) + R"(})";
        } catch (const std::exception& e) {
            handleError(res, e.what());
        }
    }

    // Transaction execution
    void executeTransaction(const http::request<http::string_body>& req, 
                           http::response<http::string_body>& res) {
        try {
            // Authenticate user
            std::string username;
            if (!authenticate(req, res, username)) {
                return;
            }

            // Parse request body
            json requestBody = json::parse(req.body());

            // Extract query parameters
            json queries = requestBody["queries"];
            std::string applicationName = requestBody.value("application", "");
            std::string schemaName = requestBody.value("schema", "");

            // Check permissions
            if (!checkPermission(username, applicationName, schemaName, security::Permission::Write, res)) {
                return;
            }

            // Execute transaction
            auto results = connectionManager_->executeWithConnection([&queries](pqxx::connection& conn) {
                pqxx::work txn(conn);

                json jsonResults = json::array();
                for (const auto& query : queries) {
                    std::string queryStr = query.get<std::string>();
                    pqxx::result res = txn.exec(queryStr);

                    json jsonResult = json::object();
                    jsonResult["affected_rows"] = res.affected_rows();
                    jsonResults.push_back(jsonResult);
                }

                txn.commit();
                return jsonResults;
            });

            // Set response
            res.result(http::status::ok);
            res.body() = results.dump();
        } catch (const std::exception& e) {
            handleError(res, e.what());
        }
    }

    // Schema information
    void getSchemaInfo(const http::request<http::string_body>& req, 
                      http::response<http::string_body>& res) {
        try {
            // Authenticate user
            std::string username;
            if (!authenticate(req, res, username)) {
                return;
            }

            // Get database schema information
            auto result = connectionManager_->executeWithConnection([](pqxx::connection& conn) {
                pqxx::work txn(conn);

                // Get tables
                pqxx::result tables = txn.exec(
                    "SELECT table_name, table_schema "
                    "FROM information_schema.tables "
                    "WHERE table_schema NOT IN ('pg_catalog', 'information_schema') "
                    "ORDER BY table_schema, table_name"
                );

                json jsonTables = json::array();
                for (const auto& table : tables) {
                    std::string tableName = table["table_name"].as<std::string>();
                    std::string schemaName = table["table_schema"].as<std::string>();

                    // Get columns for this table
                    pqxx::result columns = txn.exec_params(
                        "SELECT column_name, data_type, is_nullable, column_default "
                        "FROM information_schema.columns "
                        "WHERE table_name = $1 AND table_schema = $2 "
                        "ORDER BY ordinal_position",
                        tableName, schemaName
                    );

                    json jsonColumns = json::array();
                    for (const auto& column : columns) {
                        json jsonColumn = json::object();
                        jsonColumn["name"] = column["column_name"].as<std::string>();
                        jsonColumn["type"] = column["data_type"].as<std::string>();
                        jsonColumn["nullable"] = (column["is_nullable"].as<std::string>() == "YES");

                        if (!column["column_default"].is_null()) {
                            jsonColumn["default"] = column["column_default"].as<std::string>();
                        }

                        jsonColumns.push_back(jsonColumn);
                    }

                    json jsonTable = json::object();
                    jsonTable["name"] = tableName;
                    jsonTable["schema"] = schemaName;
                    jsonTable["columns"] = jsonColumns;

                    jsonTables.push_back(jsonTable);
                }

                txn.commit();
                return jsonTables;
            });

            // Set response
            res.result(http::status::ok);
            res.body() = result.dump();
        } catch (const std::exception& e) {
            handleError(res, e.what());
        }
    }

    // Status information
    void getStatus(const http::request<http::string_body>& req, 
                  http::response<http::string_body>& res) {
        try {
            // Get database status
            bool available = connectionManager_->isAvailable();
            std::string version = connectionManager_->getDatabaseVersion();

            // Set response
            res.result(http::status::ok);
            res.body() = R"({"status":")" + std::string(available ? "online" : "offline") +
                        R"(","version":")" + version + R"("})";
        } catch (const std::exception& e) {
            handleError(res, e.what());
        }
    }

private:
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;
};

ApiServer::ApiServer(unsigned short port,
                   std::shared_ptr<core::ConnectionManager> connectionManager,
                   std::shared_ptr<security::SecurityManager> securityManager)
    : port_(port),
      connectionManager_(connectionManager),
      securityManager_(securityManager),
      ioc_(),
      acceptor_(ioc_) {

    // Set up API endpoints
    setupEndpoints();
}

ApiServer::~ApiServer() {
    stop();
}

void ApiServer::start() {
    if (running_) {
        return;
    }

    try {
        // Open acceptor
        tcp::endpoint endpoint(tcp::v4(), port_);
        acceptor_.open(endpoint.protocol());
        acceptor_.set_option(net::socket_base::reuse_address(true));
        acceptor_.bind(endpoint);
        acceptor_.listen(net::socket_base::max_listen_connections);

        // Start accepting connections
        running_ = true;
        acceptorThread_ = std::thread([this] {
            this->doAccept();
        });

        utils::Logger::info("API server started on port " + std::to_string(port_));
    } catch (const std::exception& e) {
        utils::Logger::error("Error starting API server: " + std::string(e.what()));
        running_ = false;
    }
}

void ApiServer::stop() {
    if (!running_) {
        return;
    }

    try {
        // Stop accepting connections
        running_ = false;
        ioc_.stop();

        // Close acceptor
        if (acceptor_.is_open()) {
            acceptor_.close();
        }

        // Wait for acceptor thread to finish
        if (acceptorThread_.joinable()) {
            acceptorThread_.join();
        }

        utils::Logger::info("API server stopped");
    } catch (const std::exception& e) {
        utils::Logger::error("Error stopping API server: " + std::string(e.what()));
    }
}

bool ApiServer::isRunning() const {
    return running_;
}

void ApiServer::setupEndpoints() {
    // Create request handler
    auto requestHandler = std::make_shared<RequestHandler>(connectionManager_, securityManager_);

    // Register API endpoints
    handlers_["/api/query"] = [requestHandler](http::request<http::string_body>& req, http::response<http::string_body>& res) {
        requestHandler->executeQuery(req, res);
    };

    handlers_["/api/execute"] = [requestHandler](http::request<http::string_body>& req, http::response<http::string_body>& res) {
        requestHandler->executeCommand(req, res);
    };

    handlers_["/api/transaction"] = [requestHandler](http::request<http::string_body>& req, http::response<http::string_body>& res) {
        requestHandler->executeTransaction(req, res);
    };

    handlers_["/api/schema"] = [requestHandler](http::request<http::string_body>& req, http::response<http::string_body>& res) {
        requestHandler->getSchemaInfo(req, res);
    };

    handlers_["/api/status"] = [requestHandler](http::request<http::string_body>& req, http::response<http::string_body>& res) {
        requestHandler->getStatus(req, res);
    };
}

void ApiServer::doAccept() {
    // Accept connections until stopped
    while (running_) {
        try {
            // Create a new socket for the connection
            tcp::socket socket(ioc_);

            // Accept a new connection
            acceptor_.accept(socket);

            // Handle the connection in a new thread
            std::thread([this, socket = std::move(socket)]() mutable {
                this->handleRequest(std::move(socket));
            }).detach();
        } catch (const std::exception& e) {
            if (running_) {
                utils::Logger::error("Error accepting connection: " + std::string(e.what()));
            }
        }
    }
}

void ApiServer::handleRequest(tcp::socket socket) {
    try {
        // Read the request
        beast::flat_buffer buffer;
        http::request<http::string_body> req;
        http::read(socket, buffer, req);

        // Prepare the response
        http::response<http::string_body> res{http::status::ok, req.version()};
        res.set(http::field::server, "Database Service");
        res.set(http::field::content_type, "application/json");
        res.set(http::field::access_control_allow_origin, "*");
        res.set(http::field::access_control_allow_methods, "GET, POST, PUT, DELETE, OPTIONS");
        res.set(http::field::access_control_allow_headers, "Content-Type, Authorization");
        res.keep_alive(req.keep_alive());

        // Handle CORS preflight request
        if (req.method() == http::verb::options) {
            res.result(http::status::no_content);
            res.body() = "";
        } else {
            // Route the request to the appropriate handler
            std::string target = std::string(req.target());

            // Extract path without query parameters
            std::string path = target;
            size_t queryPos = target.find("?");
            if (queryPos != std::string::npos) {
                path = target.substr(0, queryPos);
            }

            // Find handler for the path
            auto it = handlers_.find(path);
            if (it != handlers_.end()) {
                // Call the handler
                it->second(req, res);
            } else {
                // No handler found
                res.result(http::status::not_found);
                res.body() = R"({"error":"Endpoint not found"})";
            }
        }

        // Send the response
        res.prepare_payload();
        http::write(socket, res);
    } catch (const std::exception& e) {
        utils::Logger::error("Error handling request: " + std::string(e.what()));
    }

    // Close the socket
    beast::error_code ec;
    socket.shutdown(tcp::socket::shutdown_send, ec);
}

std::unordered_map<std::string, std::string> ApiServer::parseQueryParams(const std::string& query) {
    std::unordered_map<std::string, std::string> params;

    // Extract query parameters
    size_t queryPos = query.find("?");
    if (queryPos != std::string::npos) {
        std::string queryString = query.substr(queryPos + 1);
        std::stringstream ss(queryString);
        std::string param;

        while (std::getline(ss, param, '&')) {
            size_t equalPos = param.find("=");
            if (equalPos != std::string::npos) {
                std::string key = param.substr(0, equalPos);
                std::string value = param.substr(equalPos + 1);
                params[key] = value;
            }
        }
    }

    return params;
}

void ApiServer::handleQuery(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    // Delegate to the handler
    auto it = handlers_.find("/api/query");
    if (it != handlers_.end()) {
        it->second(req, res);
    } else {
        res.result(http::status::internal_server_error);
        res.body() = R"({"error":"Query handler not registered"})";
    }
}

void ApiServer::handleExecute(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    // Delegate to the handler
    auto it = handlers_.find("/api/execute");
    if (it != handlers_.end()) {
        it->second(req, res);
    } else {
        res.result(http::status::internal_server_error);
        res.body() = R"({"error":"Execute handler not registered"})";
    }
}

void ApiServer::handleTransaction(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    // Delegate to the handler
    auto it = handlers_.find("/api/transaction");
    if (it != handlers_.end()) {
        it->second(req, res);
    } else {
        res.result(http::status::internal_server_error);
        res.body() = R"({"error":"Transaction handler not registered"})";
    }
}

void ApiServer::handleSchema(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    // Delegate to the handler
    auto it = handlers_.find("/api/schema");
    if (it != handlers_.end()) {
        it->second(req, res);
    } else {
        res.result(http::status::internal_server_error);
        res.body() = R"({"error":"Schema handler not registered"})";
    }
}

void ApiServer::handleStatus(http::request<http::string_body>& req, http::response<http::string_body>& res) {
    // Delegate to the handler
    auto it = handlers_.find("/api/status");
    if (it != handlers_.end()) {
        it->second(req, res);
    } else {
        res.result(http::status::internal_server_error);
        res.body() = R"({"error":"Status handler not registered"})";
    }
}

} // namespace api
} // namespace dbservice
