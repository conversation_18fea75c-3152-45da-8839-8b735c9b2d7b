# Update Server Config Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
# Import UI module
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force

function Update-ServerConfig {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }
    Show-MenuTitle -Title "Update Server Configuration"
    Show-MenuDivider
    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "No configuration loaded. Please change environment first." -Level "UI" -ForegroundColor Red
        Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
        if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
            Show-MainMenu
        }
        return
    }
    $configScript = Get-UserInput -Prompt "Enter remote path to server config script (e.g. /opt/scripts/update-config.sh)"
    if ([string]::IsNullOrWhiteSpace($configScript)) {
        Write-Log -Message "No script path provided." -Level "UI" -ForegroundColor Red
        if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
            Show-MainMenu
        }
        return
    }
    Write-Log -Message "Updating server configuration..." -Level "UI" -ForegroundColor Cyan
    if (Get-Command -Name Invoke-RemoteCommand -ErrorAction SilentlyContinue) {
        $result = Invoke-RemoteCommand -Command "sudo $configScript"
    } else {
        Write-Log -Message "Invoke-RemoteCommand function not found. Cannot update server configuration." -Level "Error" -Component "ServerConfig"
        $result = $null
    }
    if ($result) {
        Write-Log -Message "Server configuration updated successfully." -Level "UI" -ForegroundColor Green
        Write-Log -Message $result -Level "UI" -ForegroundColor Gray
    } else {
        Write-Log -Message "Failed to update server configuration." -Level "UI" -ForegroundColor Red
    }
    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }
    Get-UserInput -Prompt "Press Enter to continue..." | Out-Null
    if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
        Show-MainMenu
    }
}

# Export the function
Export-ModuleMember -Function Update-ServerConfig
