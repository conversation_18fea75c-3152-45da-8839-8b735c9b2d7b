export module database.security;

// Import the core module
import database.core;

// Standard library imports
import <string>;
import <vector>;
import <unordered_map>;
import <chrono>;
import <optional>;

// Security module
export namespace dbservice::security {

    // User role
    export enum class UserRole {
        Admin,
        ReadWrite,
        ReadOnly
    };

    // User
    export struct User {
        std::string username;
        std::string passwordHash;
        UserRole role;
        std::chrono::system_clock::time_point createdAt;
        std::chrono::system_clock::time_point lastLogin;
    };

    // Token
    export struct Token {
        std::string value;
        std::string username;
        UserRole role;
        std::chrono::system_clock::time_point expiresAt;
    };

    // Security manager
    export class SecurityManager {
    public:
        /**
         * @brief Constructor
         * @param dbService Database service
         * @param jwtSecret Secret for JWT tokens
         */
        SecurityManager(
            std::shared_ptr<core::DatabaseInterface> dbService,
            const std::string& jwtSecret
        );

        /**
         * @brief Authenticate a user (synchronous)
         * @param username Username
         * @param password Password
         * @return Token if authentication is successful
         */
        core::Result<Token> authenticate(
            const std::string& username, 
            const std::string& password
        );

        /**
         * @brief Authenticate a user (asynchronous)
         * @param username Username
         * @param password Password
         * @return Task with token if authentication is successful
         */
        core::Task<core::Result<Token>> authenticateAsync(
            const std::string& username, 
            const std::string& password
        );

        /**
         * @brief Validate a token (synchronous)
         * @param token Token to validate
         * @return User if token is valid
         */
        core::Result<User> validateToken(const std::string& token);

        /**
         * @brief Validate a token (asynchronous)
         * @param token Token to validate
         * @return Task with user if token is valid
         */
        core::Task<core::Result<User>> validateTokenAsync(const std::string& token);

        /**
         * @brief Create a user (synchronous)
         * @param username Username
         * @param password Password
         * @param role User role
         * @return Result of the operation
         */
        core::Result<void> createUser(
            const std::string& username, 
            const std::string& password, 
            UserRole role
        );

        /**
         * @brief Create a user (asynchronous)
         * @param username Username
         * @param password Password
         * @param role User role
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> createUserAsync(
            const std::string& username, 
            const std::string& password, 
            UserRole role
        );

        /**
         * @brief Delete a user (synchronous)
         * @param username Username
         * @return Result of the operation
         */
        core::Result<void> deleteUser(const std::string& username);

        /**
         * @brief Delete a user (asynchronous)
         * @param username Username
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> deleteUserAsync(const std::string& username);

        /**
         * @brief Update a user's role (synchronous)
         * @param username Username
         * @param role New role
         * @return Result of the operation
         */
        core::Result<void> updateUserRole(const std::string& username, UserRole role);

        /**
         * @brief Update a user's role (asynchronous)
         * @param username Username
         * @param role New role
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> updateUserRoleAsync(
            const std::string& username, 
            UserRole role
        );

        /**
         * @brief Change a user's password (synchronous)
         * @param username Username
         * @param oldPassword Old password
         * @param newPassword New password
         * @return Result of the operation
         */
        core::Result<void> changePassword(
            const std::string& username, 
            const std::string& oldPassword, 
            const std::string& newPassword
        );

        /**
         * @brief Change a user's password (asynchronous)
         * @param username Username
         * @param oldPassword Old password
         * @param newPassword New password
         * @return Task with the result of the operation
         */
        core::Task<core::Result<void>> changePasswordAsync(
            const std::string& username, 
            const std::string& oldPassword, 
            const std::string& newPassword
        );

    private:
        /**
         * @brief Hash a password
         * @param password Password to hash
         * @return Hashed password
         */
        std::string hashPassword(const std::string& password);

        /**
         * @brief Verify a password
         * @param password Password to verify
         * @param hash Hash to verify against
         * @return True if password matches hash
         */
        bool verifyPassword(const std::string& password, const std::string& hash);

        /**
         * @brief Generate a JWT token
         * @param username Username
         * @param role User role
         * @param expiresIn Token expiration time
         * @return JWT token
         */
        std::string generateToken(
            const std::string& username, 
            UserRole role, 
            std::chrono::seconds expiresIn
        );

        /**
         * @brief Parse a JWT token
         * @param token Token to parse
         * @return Token data if valid
         */
        std::optional<Token> parseToken(const std::string& token);

        std::shared_ptr<core::DatabaseInterface> dbService_;
        std::string jwtSecret_;
    };

} // namespace dbservice::security
