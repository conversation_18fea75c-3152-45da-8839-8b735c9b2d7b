{"project": {"local_source_dir": "D:\\Augment\\project-tracker\\database-service", "name": "database-service", "remote_install_dir": "/opt/database-service", "description": "database-service for development environment", "remote_build_dir": "/home/<USER>/database-service-build"}, "ssh": {"local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa", "username": "btaylor-admin", "port": 22, "host": "git.chcit.org"}, "service": {"user": "database-service", "name": "database-service", "group": "database-service", "description": "database-service for development environment"}, "database": {"user": "database_service", "name": "database_service", "port": 5432, "password": "password2311", "host": "localhost"}, "version": {"number": 2, "updated": "2025-04-14 06:52:00", "created": "2025-04-13 09:55:21"}}