# Database Service Configuration Validation

> **Note:** The project has been reorganized. The configuration validation implementation is now in D:\Augment\project-tracker\database-service\include\database-service\utils\config_validator.hpp, while configuration templates are in D:\Augment\project-tracker\database-service-scripts\config.

## Overview

The Database Service includes a sophisticated configuration validation system that ensures all configuration parameters are valid and appropriate for the service's operation. This document describes the configuration validation features, how they work, and how to use them effectively.

## Configuration Sources

The Database Service can load configuration from two sources:

1. **Configuration File**: JSON format configuration file
2. **Environment Variables**: System environment variables

When both sources are available, environment variables take precedence over the configuration file.

## Command-Line Usage

The service can be started with an optional configuration file path:

```bash
./database-service /path/to/config.json
```

If no configuration file is specified, the service will use the default path `/etc/database-service/config.json`.

## Configuration Sections

The Database Service configuration is organized into several sections:

1. **Database Configuration**: Database connection and pool settings
2. **API Configuration**: API server settings
3. **Security Configuration**: Authentication and authorization settings
4. **Logging Configuration**: Logging settings
5. **Schema Configuration**: Database schema management settings

## Database Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `database.host` | String | `localhost` | Database server hostname |
| `database.port` | Integer | `5432` | Database server port |
| `database.name` | String | `postgres` | Database name |
| `database.user` | String | `postgres` | Database username |
| `database.password` | String | `` | Database password |
| `database.pool.min_connections` | Integer | `5` | Minimum number of connections in the pool |
| `database.pool.max_connections` | Integer | `20` | Maximum number of connections in the pool |
| `database.pool.connection_timeout` | Integer | `30` | Connection timeout in seconds |
| `database.ssl.enabled` | Boolean | `false` | Enable SSL for database connections |
| `database.ssl.mode` | String | `require` | SSL mode (disable, allow, prefer, require, verify-ca, verify-full) |
| `database.ssl.ca_cert` | String | `` | Path to CA certificate file |
| `database.test_connection` | Boolean | `false` | Test database connection at startup |

## API Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `api.host` | String | `127.0.0.1` | API server hostname |
| `api.port` | Integer | `8080` | API server port |
| `api.threads` | Integer | `4` | Number of API server threads |
| `api.timeout` | Integer | `30` | API request timeout in seconds |
| `api.ssl.enabled` | Boolean | `false` | Enable SSL for API server |
| `api.ssl.cert_file` | String | `` | Path to SSL certificate file |
| `api.ssl.key_file` | String | `` | Path to SSL key file |

## Security Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `security.authentication.enabled` | Boolean | `true` | Enable authentication |
| `security.authentication.type` | String | `jwt` | Authentication type (jwt, basic, api_key) |
| `security.authentication.jwt.secret` | String | Generated | JWT secret key |
| `security.authentication.jwt.expiration` | Integer | `3600` | JWT expiration time in seconds |
| `security.authentication.api_key.value` | String | Generated | API key value |
| `security.authorization.enabled` | Boolean | `true` | Enable authorization |
| `security.authorization.type` | String | `role_based` | Authorization type (role_based, acl) |

## Logging Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `logging.level` | String | `info` | Logging level (trace, debug, info, warning, error, critical) |
| `logging.file` | String | `` | Log file path (empty for console logging) |
| `logging.rotation.enabled` | Boolean | `true` | Enable log rotation |
| `logging.rotation.max_size` | Integer | `10` | Maximum log file size in MB |
| `logging.rotation.max_files` | Integer | `5` | Maximum number of log files to keep |

## Schema Configuration Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `schema.directory` | String | `` | Directory containing schema files |
| `schema.auto_migrate` | Boolean | `false` | Automatically apply schema migrations |

## Validation Features

The configuration validation system performs the following checks:

### Database Configuration Validation

- Validates database host, port, name, and user
- Checks if database password is provided or available via environment variable
- Validates connection pool settings
- Validates SSL settings if enabled
- Optionally tests the database connection

### API Configuration Validation

- Validates API host and port
- Ensures thread count is within reasonable limits
- Validates timeout settings
- Checks SSL certificate and key files if SSL is enabled

### Security Configuration Validation

- Validates authentication and authorization settings
- Generates secure random secrets for JWT and API keys if not provided
- Validates JWT expiration time
- Ensures authentication type is valid

### Logging Configuration Validation

- Validates logging level
- Checks if log file directory exists and creates it if necessary
- Verifies write permissions to the log file
- Validates log rotation settings

### Schema Configuration Validation

- Checks if schema directory exists and creates it if necessary
- Validates auto-migration settings

## Validation Behavior

When the service starts, it performs the following steps:

1. Loads configuration from the specified file (if provided)
2. Loads configuration from environment variables
3. Validates all configuration parameters
4. Reports any validation issues
5. Applies default values for invalid parameters
6. Optionally saves corrected configuration
7. Continues execution with the validated configuration

The service will attempt to continue running even if some configuration parameters are invalid, falling back to default values where possible.

## Example Configuration File

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "database_service",
    "user": "postgres",
    "password": "secure_password",
    "pool": {
      "min_connections": 5,
      "max_connections": 20,
      "connection_timeout": 30
    },
    "ssl": {
      "enabled": false,
      "mode": "require",
      "ca_cert": ""
    },
    "test_connection": true
  },
  "api": {
    "host": "127.0.0.1",
    "port": 8080,
    "threads": 4,
    "timeout": 30,
    "ssl": {
      "enabled": false,
      "cert_file": "",
      "key_file": ""
    }
  },
  "security": {
    "authentication": {
      "enabled": true,
      "type": "jwt",
      "jwt": {
        "secret": "your_secure_jwt_secret",
        "expiration": 3600
      },
      "api_key": {
        "value": ""
      }
    },
    "authorization": {
      "enabled": true,
      "type": "role_based"
    }
  },
  "logging": {
    "level": "info",
    "file": "/var/log/database-service.log",
    "rotation": {
      "enabled": true,
      "max_size": 10,
      "max_files": 5
    }
  },
  "schema": {
    "directory": "/opt/database-service/schemas",
    "auto_migrate": false
  },
  "config": {
    "save_corrected": false
  },
  "debug": {
    "dump_config": false
  }
}
```

## Environment Variables

Environment variables can be used to override configuration file settings. The naming convention is to use uppercase and underscores, for example:

```bash
export DATABASE_HOST=localhost
export DATABASE_PORT=5432
export DATABASE_NAME=database_service
export DATABASE_USER=postgres
export DATABASE_PASSWORD=secure_password
export API_PORT=8080
export LOGGING_LEVEL=info
export DB_PASSWORD=secure_password  # Alternative for DATABASE_PASSWORD
```

## Example Validation Output

```
Loading configuration from file: /etc/database-service/config.json
Invalid database port: 70000, must be between 1 and 65535
Setting database port to default: 5432
Warning: Empty database password, authentication may fail
Invalid API threads: 100, must be between 1 and 64
Setting API threads to default: 4
Generated random JWT secret
Log directory does not exist: /var/log
Created log directory: /var/log
Schema directory does not exist: /opt/database-service/schemas
Created schema directory: /opt/database-service/schemas
Configuration validation found issues, but will attempt to continue
```

## Best Practices

1. **Use a Configuration File**: For production deployments, use a configuration file rather than environment variables for better maintainability.

2. **Secure Sensitive Information**: Store sensitive information like database passwords in environment variables rather than configuration files.

3. **Generate Strong Secrets**: Let the service generate strong random secrets for JWT and API keys.

4. **Set Appropriate Permissions**: Ensure the service has appropriate permissions to read/write to the specified paths.

5. **Validate Before Deployment**: Test your configuration before deploying to production to catch any issues early.

## Troubleshooting

### Common Issues

1. **Database Connection Failures**: Check database host, port, credentials, and ensure the database server is running.

2. **API Server Binding Failures**: Ensure the specified API port is available and not in use by another service.

3. **Permission Denied Errors**: Check file and directory permissions for log files and schema directories.

4. **Authentication Failures**: Verify JWT secret or API key configuration.

5. **Configuration Not Being Applied**: Check if environment variables are overriding configuration file settings.

## Conclusion

The configuration validation system in the Database Service helps prevent runtime errors by validating configuration parameters before they are used. By providing sensible defaults, generating secure secrets, and giving clear error messages, it makes the service more robust and easier to configure correctly.

