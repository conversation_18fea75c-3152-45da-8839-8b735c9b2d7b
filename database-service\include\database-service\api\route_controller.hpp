#pragma once

#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include <memory>

namespace dbservice::api {

/**
 * @class RouteController
 * @brief Manages the registration of API routes
 */
class RouteController {
public:
    /**
     * @brief Constructor
     * @param connectionManager Shared pointer to the ConnectionManager
     * @param securityManager Shared pointer to the SecurityManager
     */
    RouteController(std::shared_ptr<core::ConnectionManager> connectionManager,
                    std::shared_ptr<security::SecurityManager> securityManager);

    /**
     * @brief Registers all API routes with the server
     * @param server The ApiServer instance
     */
    void registerRoutes(ApiServer& server);

private:
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;

    // Route handlers
    std::expected<Response, std::string> handleHealthCheck(const ParsedRequest& request);
};

} // namespace dbservice::api
