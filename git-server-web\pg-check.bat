@echo off
setlocal

set "GIT_SERVER=git.chcit.org"
set "SSH_USER=btaylor-admin"

echo PostgreSQL Check - Server: %GIT_SERVER%
echo.
echo 1. Check PostgreSQL Version
echo 2. List Databases
echo 3. Examine Database Schema
echo 4. Exit
echo.

set /p choice="Enter choice: "

if "%choice%"=="1" (
    echo Checking PostgreSQL version...
    ssh -t %SSH_USER%@%GIT_SERVER% "psql --version && sudo -u postgres psql -P pager=off -c 'SELECT version();'"
    pause
) else if "%choice%"=="2" (
    echo Listing databases...
    ssh -t %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -P pager=off -l"
    pause
) else if "%choice%"=="3" (
    set /p db="Enter database name: "
    echo Examining schema for %db%...
    ssh -t %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -P pager=off -d %db% -c '\d+'"
    pause
) else if "%choice%"=="4" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice
    pause
)

endlocal
