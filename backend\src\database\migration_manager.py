"""
Database Migration Manager for Project Tracker
Handles database schema migrations and version tracking
"""

import os
import psycopg2
from psycopg2.extras import RealDictCursor
import logging
from datetime import datetime
from typing import List, Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MigrationManager:
    def __init__(self, db_config: Dict[str, Any]):
        """Initialize the migration manager with database configuration."""
        self.db_config = db_config
        self.migrations_dir = os.path.join(os.path.dirname(__file__), 'migrations')
        self._ensure_migration_table()

    def _get_connection(self):
        """Create a database connection."""
        return psycopg2.connect(
            host=self.db_config['host'],
            port=self.db_config['port'],
            database=self.db_config['database'],
            user=self.db_config['user'],
            password=self.db_config['password'],
            cursor_factory=RealDictCursor
        )

    def _ensure_migration_table(self):
        """Ensure the schema_migrations table exists."""
        with self._get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        version VARCHAR(100) PRIMARY KEY,
                        applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()

    def _get_applied_migrations(self) -> List[str]:
        """Get list of already applied migrations."""
        with self._get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT version FROM schema_migrations ORDER BY version")
                return [row['version'] for row in cursor.fetchall()]

    def _get_migration_files(self) -> List[str]:
        """Get list of all migration files."""
        if not os.path.exists(self.migrations_dir):
            return []
        return sorted([
            f for f in os.listdir(self.migrations_dir)
            if f.endswith('.sql') and f.startswith('V')
        ])

    def _apply_migration(self, filename: str):
        """Apply a single migration file."""
        filepath = os.path.join(self.migrations_dir, filename)
        version = filename.split('_')[0]
        
        logger.info(f"Applying migration: {filename}")
        
        with open(filepath, 'r') as f:
            sql = f.read()
        
        with self._get_connection() as conn:
            with conn.cursor() as cursor:
                try:
                    # Execute migration in a transaction
                    cursor.execute(sql)
                    
                    # Record the migration
                    cursor.execute(
                        "INSERT INTO schema_migrations (version) VALUES (%s)",
                        (version,)
                    )
                    
                    conn.commit()
                    logger.info(f"Successfully applied migration: {filename}")
                    
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to apply migration {filename}: {str(e)}")
                    raise

    def migrate(self):
        """Apply all pending migrations."""
        applied = set(self._get_applied_migrations())
        available = self._get_migration_files()
        
        for migration in available:
            version = migration.split('_')[0]
            if version not in applied:
                self._apply_migration(migration)

    def status(self) -> List[Dict[str, Any]]:
        """Get the status of all migrations."""
        applied = set(self._get_applied_migrations())
        available = self._get_migration_files()
        
        status_list = []
        for migration in available:
            version = migration.split('_')[0]
            status_list.append({
                'version': version,
                'filename': migration,
                'status': 'applied' if version in applied else 'pending'
            })
        
        return status_list

if __name__ == '__main__':
    # Example usage
    db_config = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'port': int(os.getenv('POSTGRES_PORT', '5432')),
        'database': os.getenv('POSTGRES_DB', 'project_tracker'),
        'user': os.getenv('POSTGRES_USER', 'project_tracker'),
        'password': os.getenv('POSTGRES_PASSWORD', '')
    }
    
    manager = MigrationManager(db_config)
    
    # Print current status
    logger.info("Current migration status:")
    for migration in manager.status():
        logger.info(f"{migration['filename']}: {migration['status']}")
    
    # Apply pending migrations
    manager.migrate()
