# Manage Deployment Configurations Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force
# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Manage deployment configurations function
function Show-DeploymentConfigurations {
    Clear-Host

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "            Manage Deployment Configurations            " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "No configuration loaded. Please change environment first." -Level "UI" -ForegroundColor Red
        Wait-ForUser

        # Return to the main menu instead of trying to run Set-Environment.ps1 directly
        if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
            Show-MainMenu
        }
        return
    }

    Write-Log -Message "Current Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Project: $($script:Config.project.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Environment: $($script:Environment)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Server: $($script:Config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] View Configuration Details" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Export-Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Import-ExportedConfiguration" -Level "UI" -ForegroundColor White
    Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
    Write-Log -Message "[0] Back to Main Menu" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (0-4)"

    # Disable UI Mode after menu display
    Disable-UIMode

    switch ($choice) {
        "1" { Show-ConfigurationDetails }
        "2" { Edit-Configuration }
        "3" { Export-Configuration }
        "4" { Import-ExportedConfiguration }
        "0" { Show-MainMenu }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Show-DeploymentConfigurations
        }
    }
}

# For backward compatibility, create an alias only if it doesn't exist
if (-not (Get-Alias -Name Manage-DeploymentConfigurations -ErrorAction SilentlyContinue)) {
    New-Alias -Name Manage-DeploymentConfigurations -Value Show-DeploymentConfigurations -Scope Global
}

# Show configuration details function
function Show-ConfigurationDetails {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Configuration Details                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "No configuration loaded. Please change environment first." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    Write-Log -Message "Project Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Name: $($script:Config.project.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Description: $($script:Config.project.description)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Source Code: $($script:Config.project.local_source_dir)" -Level "UI" -ForegroundColor White
    if ($script:Config.project.deployment_scripts) {
        Write-Log -Message "  Deployment Scripts: $($script:Config.project.deployment_scripts)" -Level "UI" -ForegroundColor White
    }
    Write-Log -Message "  Install Directory: $($script:Config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
    if ($script:Config.project.remote_build_dir) {
        Write-Log -Message "  Build Directory: $($script:Config.project.remote_build_dir)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message "`nSSH Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Host: $($script:Config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $($script:Config.ssh.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Username: $($script:Config.ssh.username)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Key Path: $($script:Config.ssh.local_key_path)" -Level "UI" -ForegroundColor White

    Write-Log -Message "`nService Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Name: $($script:Config.service.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Description: $($script:Config.service.description)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  User: $($script:Config.service.user)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Group: $($script:Config.service.group)" -Level "UI" -ForegroundColor White

    Write-Log -Message "`nDatabase Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Host: $($script:Config.database.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $($script:Config.database.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Name: $($script:Config.database.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  User: $($script:Config.database.user)" -Level "UI" -ForegroundColor White
    if (-not [string]::IsNullOrEmpty($script:Config.database.password)) {
        Write-Log -Message "  Password: [Set]" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "  Password: [Not Set]" -Level "UI" -ForegroundColor Red
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "[1] Edit Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Back to Manage Deployment Configurations" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-2)"

    switch ($choice) {
        "1" { Edit-Configuration }
        "2" { Show-DeploymentConfigurations }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Show-ConfigurationDetails
        }
    }
}

# Edit configuration function
function Edit-Configuration {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Configuration                      " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] Edit Project Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Server Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Service Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Database Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Back to Manage Deployment Configurations" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-5)"

    switch ($choice) {
        "1" { Edit-ProjectSettings }
        "2" { Edit-ServerSettings }
        "3" { Edit-ServiceSettings }
        "4" { Edit-DatabaseSettings }
        "5" { Show-DeploymentConfigurations }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-Configuration
        }
    }
}

# Edit project settings
function Edit-ProjectSettings {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Project Settings                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "Current Project Settings:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Name: $($script:Config.project.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Description: $($script:Config.project.description)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Source Code: $($script:Config.project.local_source_dir)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Deployment Scripts: $($script:Config.project.deployment_scripts)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Install Directory: $($script:Config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
    if ($script:Config.project.remote_build_dir) {
        Write-Log -Message "  Build Directory: $($script:Config.project.remote_build_dir)" -Level "UI" -ForegroundColor White
    }
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] Edit Project Name" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Project Description" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Source Code" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Install Directory" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Edit Build Directory" -Level "UI" -ForegroundColor White
    Write-Log -Message "[6] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-6)"

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new project name"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.name = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ProjectSettings
        }
        "2" {
            $newValue = Read-Host "Enter new project description"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.description = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ProjectSettings
        }
        "3" {
            $newValue = Read-Host "Enter new source code"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.local_source_dir = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ProjectSettings
        }
        "4" {
            $newValue = Read-Host "Enter new install directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.remote_install_dir = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ProjectSettings
        }
        "5" {
            $newValue = Read-Host "Enter new build directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.remote_build_dir = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ProjectSettings
        }
        "6" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-ProjectSettings
        }
    }
}

# Edit server settings
function Edit-ServerSettings {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Server Settings                    " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "Current Server Settings:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Host: $($script:Config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $($script:Config.ssh.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Username: $($script:Config.ssh.username)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Key Path: $($script:Config.ssh.local_key_path)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] Edit Host" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Port" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Username" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit SSH Key Path" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-5)"

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new host"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.ssh.host = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ServerSettings
        }
        "2" {
            $newValue = Read-Host "Enter new port"
            [int]$port = 0
            if ([int]::TryParse($newValue, [ref]$port) -and $port -gt 0) {
                $script:Config.ssh.port = $port
                Save-ConfigurationWithVersioning
            } else {
                Write-Log -Message "Invalid port number." -Level "Error" -Component "Config"
                Start-Sleep -Seconds 1
            }
            Edit-ServerSettings
        }
        "3" {
            $newValue = Read-Host "Enter new username"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.ssh.username = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ServerSettings
        }
        "4" {
            $newValue = Read-Host "Enter new SSH key path"
            if (-not [string]::IsNullOrWhiteSpace($newValue) -and (Test-Path $newValue)) {
                $script:Config.ssh.local_key_path = $newValue
                Save-ConfigurationWithVersioning
            } else {
                Write-Log -Message "Invalid path or file does not exist." -Level "Error" -Component "Config"
                Start-Sleep -Seconds 1
            }
            Edit-ServerSettings
        }
        "5" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-ServerSettings
        }
    }
}

# Edit service settings
function Edit-ServiceSettings {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Service Settings                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "Current Service Settings:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Name: $($script:Config.service.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Description: $($script:Config.service.description)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  User: $($script:Config.service.user)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Group: $($script:Config.service.group)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] Edit Service Name" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Service Description" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Service User" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Service Group" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-5)"

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new service name"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.service.name = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ServiceSettings
        }
        "2" {
            $newValue = Read-Host "Enter new service description"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.service.description = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ServiceSettings
        }
        "3" {
            $newValue = Read-Host "Enter new service user"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.service.user = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ServiceSettings
        }
        "4" {
            $newValue = Read-Host "Enter new service group"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.service.group = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-ServiceSettings
        }
        "5" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-ServiceSettings
        }
    }
}

# Edit database settings
function Edit-DatabaseSettings {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Database Settings                  " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "Current Database Settings:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Host: $($script:Config.database.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $($script:Config.database.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Name: $($script:Config.database.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  User: $($script:Config.database.user)" -Level "UI" -ForegroundColor White
    if (-not [string]::IsNullOrEmpty($script:Config.database.password)) {
        Write-Log -Message "  Password: [Set]" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "  Password: [Not Set]" -Level "UI" -ForegroundColor Red
    }
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] Edit Database Host" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Database Port" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Database Name" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Database User" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Edit Database Password" -Level "UI" -ForegroundColor White
    Write-Log -Message "[6] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-6)"

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new database host"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.database.host = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-DatabaseSettings
        }
        "2" {
            $newValue = Read-Host "Enter new database port"
            [int]$port = 0
            if ([int]::TryParse($newValue, [ref]$port) -and $port -gt 0) {
                $script:Config.database.port = $port
                Save-ConfigurationWithVersioning
            } else {
                Write-Log -Message "Invalid port number." -Level "Error" -Component "Config"
                Start-Sleep -Seconds 1
            }
            Edit-DatabaseSettings
        }
        "3" {
            $newValue = Read-Host "Enter new database name"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.database.name = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-DatabaseSettings
        }
        "4" {
            $newValue = Read-Host "Enter new database user"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.database.user = $newValue
                Save-ConfigurationWithVersioning
            }
            Edit-DatabaseSettings
        }
        "5" {
            $password = Read-Host "Enter new database password" -AsSecureString
            $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

            if (-not [string]::IsNullOrWhiteSpace($plainPassword)) {
                $script:Config.database.password = $plainPassword
                Save-ConfigurationWithVersioning
            } else {
                Write-Log -Message "Password cannot be empty." -Level "Error" -Component "Config"
            }
            Start-Sleep -Seconds 1
            Edit-DatabaseSettings
        }
        "6" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-DatabaseSettings
        }
    }
}

# Save configuration with version tracking
function Save-ConfigurationWithVersioning {
    # Update version information
    if (-not $script:Config.version) {
        $script:Config.version = @{
            number = 1
            created = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
            updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        }
    } else {
        # Increment version number
        $script:Config.version.number = [int]$script:Config.version.number + 1
        $script:Config.version.updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    # Call the original Save-Configuration function
    Save-Configuration

    # Display version information
    Write-Log -Message "Configuration updated to version $($script:Config.version.number)" -Level "Info" -Component "Config"
}

# Export configuration function
function Export-Configuration {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Export Configuration                    " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "No configuration loaded. Please change environment first." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    $defaultPath = "$PSScriptRoot\..\config\exports"
    if (-not (Test-Path $defaultPath)) {
        New-Item -Path $defaultPath -ItemType Directory -Force | Out-Null
    }

    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $exportFileName = "$($script:Config.project.name)-$script:Environment-$timestamp.json"
    $exportPath = Join-Path -Path $defaultPath -ChildPath $exportFileName

    try {
        $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $exportPath -Encoding UTF8
        Write-Log -Message "Configuration exported successfully to:" -Level "UI" -ForegroundColor White
        Write-Log -Message $exportPath -Level "UI" -ForegroundColor White
    } catch {
        Write-Log -Message "Failed to export configuration: $_" -Level "Error" -Component "Config"
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Press Enter to return..." -Level "UI" -ForegroundColor White
    Wait-ForUser
    Show-DeploymentConfigurations
}

# Import configuration function
function Import-ExportedConfiguration {
    Clear-Host
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Import Configuration                    " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    $defaultPath = "$PSScriptRoot\..\config\exports"
    if (-not (Test-Path $defaultPath)) {
        Write-Log -Message "No exported configurations found." -Level "UI" -ForegroundColor Yellow
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    $exports = Get-ChildItem -Path $defaultPath -Filter "*.json"

    if ($exports.Count -eq 0) {
        Write-Log -Message "No exported configurations found." -Level "UI" -ForegroundColor Yellow
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    Write-Log -Message "Available configurations:" -Level "UI" -ForegroundColor Cyan
    for ($i = 0; $i -lt $exports.Count; $i++) {
        Write-Log -Message "[$($i+1)] $($exports[$i].Name)" -Level "UI" -ForegroundColor White
    }

    Write-Log -Message "[C] Cancel" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect a configuration to import"

    if ($choice -eq "C") {
        Show-DeploymentConfigurations
        return
    }

    [int]$index = 0
    if ([int]::TryParse($choice, [ref]$index) -and $index -ge 1 -and $index -le $exports.Count) {
        $selectedExport = $exports[$index-1]

        try {
            $importedConfig = Get-Content -Path $selectedExport.FullName -Raw | ConvertFrom-Json

            # Ask for confirmation
            Write-Log -Message "`nImporting this configuration will overwrite your current settings." -Level "UI" -ForegroundColor Yellow
            $confirm = Read-Host "Are you sure you want to continue? (y/n)"

            if ($confirm -eq "y") {
                # Update global configuration
                $script:Config = $importedConfig

                # Save to current environment
                if (Save-Configuration) {
                    Write-Log -Message "Configuration imported successfully." -Level "UI" -ForegroundColor White
                } else {
                    Write-Log -Message "Failed to save imported configuration." -Level "Error" -Component "Config"
                }
            }
        } catch {
            Write-Log -Message "Failed to import configuration: $_" -Level "Error" -Component "Config"
        }
    } else {
        Write-Log -Message "Invalid selection." -Level "Error" -Component "Config"
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Press Enter to return..." -Level "UI" -ForegroundColor White
    Wait-ForUser
    Show-DeploymentConfigurations
}

# Run the function when this script is directly invoked
if ($MyInvocation.InvocationName -eq '.') {
    # Script is being dot-sourced
} else {
    # Script is being run directly
    Show-DeploymentConfigurations
}

# Export functions and aliases
Export-ModuleMember -Function Show-DeploymentConfigurations, Show-ConfigurationDetails, Edit-Configuration, Export-Configuration, Import-ExportedConfiguration, Save-ConfigurationWithVersioning -Alias Manage-DeploymentConfigurations
