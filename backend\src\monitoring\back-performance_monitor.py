from functools import wraps
from typing import Callable, Any
import time
import asyncio

class PerformanceMonitor:
    def __init__(self, redis_client, threshold_ms: int = 1000):
        self.redis = redis_client
        self.threshold_ms = threshold_ms

    def monitor(self, operation_name: str) -> Callable:
        """Decorator for monitoring function performance"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def async_wrapper(*args, **kwargs) -> Any:
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    duration_ms = (time.perf_counter() - start_time) * 1000
                    await self._record_metric(operation_name, duration_ms)
                    
                    if duration_ms > self.threshold_ms:
                        await self._alert_slow_operation(
                            operation_name, 
                            duration_ms
                        )

            @wraps(func)
            def sync_wrapper(*args, **kwargs) -> Any:
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration_ms = (time.perf_counter() - start_time) * 1000
                    asyncio.create_task(
                        self._record_metric(operation_name, duration_ms)
                    )

            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator

    async def _record_metric(self, operation: str, duration_ms: float) -> None:
        """Record performance metric to Redis with TTL"""
        key = f"perf:{operation}:{int(time.time())}"
        await self.redis.setex(
            key,
            3600,  # 1 hour TTL
            duration_ms
        )