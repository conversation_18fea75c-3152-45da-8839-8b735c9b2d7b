#include "api/api_server.hpp"
#include "services/repository_service.hpp"
#include "utils/config.hpp"
#include <iostream>
#include <csignal>
#include <memory>
#include <thread>
#include <atomic>
#include <chrono>
#include <filesystem>

std::unique_ptr<ApiServer> server;
std::atomic<bool> running{true};

void signalHandler(int signal) {
    std::cout << "Received signal " << signal << ", shutting down..." << std::endl;
    running = false;
    if (server) {
        server->stop();
    }
}

int main(int argc, char* argv[]) {
    try {
        // Register signal handlers
        std::signal(SIGINT, signalHandler);
        std::signal(SIGTERM, signalHandler);

        std::cout << "Git Repository Service starting..." << std::endl;

        // Load and validate configuration
        auto& config = CONFIG;

        // Try to load from config file if specified
        if (argc > 1) {
            std::string configFile = argv[1];
            std::cout << "Loading configuration from file: " << configFile << std::endl;
            if (!config.loadFromFile(configFile)) {
                std::cerr << "Failed to load configuration from file, using environment variables" << std::endl;
            }
        }

        // Load from environment variables
        config.loadFromEnvironment();

        // Validate the configuration
        if (!config.validateConfig()) {
            std::cerr << "Configuration validation found issues, but will attempt to continue" << std::endl;
        }

        // Dump configuration for debugging
        if (config.getBool("DEBUG", false)) {
            config.dumpConfig();
        }

        // Get repository path from configuration
        std::string repoPath = config.get("GIT_REPO_PATH", "/home/<USER>/repositories");
        std::cout << "Using repository path: " << repoPath << std::endl;

        // Create repository service
        auto repoService = std::make_shared<RepositoryService>(repoPath);

        // Create and start API server with configured port
        int port = config.getInt("SERVER_PORT", 8080);
        server = std::make_unique<ApiServer>(port, repoService);

        std::cout << "Starting Git Repository Service on port " << port << "..." << std::endl;
        server->start();

        std::cout << "Server started, running until terminated..." << std::endl;

        // Keep the main thread alive until signaled to stop
        while (running) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        std::cout << "Git Repository Service shutting down..." << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
