# Database Service Deployment

This repository contains scripts for deploying the Database Service to various environments.

## Overview

The Database Service Deployment scripts provide a modular, robust system for deploying the Database Service to development, testing, and production environments. The scripts handle configuration management, SSH connections, build processes, and service management.

## Recent Improvements

The deployment scripts have been completely overhauled to address various issues and improve reliability:

1. **Standardized Module Structure**
   - Converted all PS1 files to proper PowerShell modules
   - Implemented consistent module import patterns
   - Added proper dependency management

2. **Improved Variable Scope Management**
   - Used `$script:` prefix consistently for all script-level variables
   - Passed variables as parameters between functions
   - Reduced reliance on global state

3. **Enhanced SSH Command Handling**
   - Created a dedicated SSHManager module for all SSH operations
   - Improved parameter handling for paths with spaces
   - Added retry logic for transient SSH failures

4. **Aligned Configuration Validation**
   - Updated ConfigValidator to match actual configuration structure
   - Made default values configurable
   - Added comprehensive validation for complex properties

5. **Strengthened Error Handling**
   - Implemented consistent error reporting across all modules
   - Added more context to error messages
   - Improved error propagation between functions

6. **Reduced Code Duplication**
   - Consolidated common functionality into shared modules
   - Used helper functions for repeated operations
   - Implemented a DRY (Don't Repeat Yourself) approach

7. **Improved Configuration Management**
   - Made all hardcoded values configurable
   - Added support for environment variables for sensitive settings
   - Implemented configuration versioning and migration

8. **Standardized Function Naming**
   - Followed PowerShell verb-noun naming conventions
   - Used consistent casing and terminology
   - Added comprehensive function documentation

9. **Enhanced Documentation**
   - Added comprehensive inline documentation
   - Created architecture and design documentation
   - Documented deployment workflows and requirements

## Directory Structure

- `deployment_scripts/`: Main deployment scripts
  - `Modules/`: Individual modules for specific tasks
  - `config/`: Configuration files for different environments
  - `logs/`: Log files generated during deployment

## Related Directories

- **Source Code**: D:\Augment\project-tracker\database-service
- **Documentation**: D:\Augment\project-tracker\docs\database-service

## Usage

1. Run the main deployment script:
   ```powershell
   .\deployment_scripts\deploy-database-service-modular.ps1
   ```

2. Select the appropriate option from the menu to perform deployment tasks.

## Configuration

Configuration files are stored in the `deployment_scripts/config` directory with the naming convention:
```
database-service-{environment}.json
```

Where `{environment}` is one of:
- development
- testing
- production

## Modules

The deployment scripts use a modular approach with the following components:

- **Common.psm1**: Core functions and variables shared across all scripts
- **Logger.psm1**: Centralized logging system
- **ConfigValidator.psm1**: Configuration validation and repair
- **SSHManager.psm1**: SSH command execution and file transfer
- **Modules/*.ps1**: Individual modules for specific deployment tasks

## Troubleshooting

If you encounter issues:

1. Check the logs in the `deployment_scripts/logs` directory
2. Ensure SSH keys are properly configured
3. Verify the configuration file for the selected environment exists
4. Make sure all required modules are available

For persistent issues, try cleaning the build directory on the remote server:
```
rm -rf /home/<USER>/database-service-build/*
```

## Documentation

For detailed documentation, see the [Database Service Documentation Index](../docs/database-service/Database Service Documentation Index.md).
