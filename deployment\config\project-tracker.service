[Unit]
Description=Project Tracker Service
After=network.target postgresql.service redis-server.service

[Service]
User=www-data
Group=www-data
WorkingDirectory=/opt/project-tracker
Environment=PATH=/opt/project-tracker/venv/bin:$PATH
ExecStart=/opt/project-tracker/venv/bin/python3 -m gunicorn --workers 3 --bind unix:project-tracker.sock -m 007 src.app:app
Restart=always

[Install]
WantedBy=multi-user.target
