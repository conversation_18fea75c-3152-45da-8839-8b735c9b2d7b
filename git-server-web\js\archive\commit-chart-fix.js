/**
 * Emergency fix for commit history chart
 * This is a standalone implementation that doesn't rely on the existing code
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('[ChartFix] Initializing commit chart fix');
    
    // Wait a bit for other scripts to load
    setTimeout(setupChartFix, 1000);
});

function setupChartFix() {
    console.log('[ChartFix] Setting up chart fix');
    
    // Listen for modal shown events
    document.addEventListener('shown.bs.modal', function(event) {
        // Check if this is the repository modal
        if (event.target.id === 'repositoryDetailsModal') {
            console.log('[ChartFix] Repository modal shown, setting up chart');
            fixCommitChart();
        }
    });
    
    // Add a global function that can be called from the console
    window.fixCommitChart = fixCommitChart;
}

async function fixCommitChart() {
    console.log('[ChartFix] Fixing commit chart');
    
    try {
        // Find the repository name
        const repoNameElement = document.getElementById('repo-name');
        if (!repoNameElement) {
            console.error('[ChartFix] Repository name element not found');
            return;
        }
        
        const repoName = repoNameElement.textContent.trim();
        console.log(`[ChartFix] Repository name: ${repoName}`);
        
        // Find or create chart container
        let container = document.getElementById('commit-history-container');
        if (!container) {
            console.log('[ChartFix] Container not found, creating it');
            
            // Find a place to add the container
            const modalBody = document.querySelector('#repositoryDetailsModal .modal-body');
            if (!modalBody) {
                console.error('[ChartFix] Modal body not found');
                return;
            }
            
            // Create container
            container = document.createElement('div');
            container.id = 'commit-history-container';
            container.className = 'chart-container border-bottom p-4';
            container.style.position = 'relative';
            container.style.height = '300px';
            container.innerHTML = `
                <h6>Commit History</h6>
                <div id="commit-chart-loading" class="d-flex justify-content-center align-items-center h-100">
                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>Loading commit history...</span>
                </div>
                <canvas id="commit-chart-fixed" style="display: none;"></canvas>
                <div id="commit-chart-message" class="text-center text-muted mt-2" style="display: none;"></div>
            `;
            
            // Add container to modal body
            modalBody.appendChild(container);
            console.log('[ChartFix] Container created and added to modal');
        }
        
        // Get UI elements
        const canvas = document.getElementById('commit-chart-fixed');
        const loading = document.getElementById('commit-chart-loading');
        const message = document.getElementById('commit-chart-message');
        
        if (!canvas) {
            console.error('[ChartFix] Canvas element not found');
            return;
        }
        
        // Show loading
        if (loading) loading.style.display = 'flex';
        if (message) message.style.display = 'none';
        if (canvas) canvas.style.display = 'none';
        
        // Clean repository name (remove .git extension if present)
        const cleanRepoName = repoName.replace(/\.git$/, '');
        
        // Fetch commit data
        console.log(`[ChartFix] Fetching commit data for ${cleanRepoName}`);
        const url = `/api/git/repository/${encodeURIComponent(cleanRepoName)}/commits?days=30`;
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('[ChartFix] Commit data:', data);
        
        if (!data.commits || !Array.isArray(data.commits) || data.commits.length === 0) {
            console.log('[ChartFix] No commits found');
            if (loading) loading.style.display = 'none';
            if (message) {
                message.textContent = 'No commits found in the selected time period';
                message.style.display = 'block';
            }
            return;
        }
        
        // Prepare data for chart
        const chartData = prepareCommitData(data.commits, 30);
        
        // Hide loading, show canvas
        if (loading) loading.style.display = 'none';
        if (canvas) canvas.style.display = 'block';
        
        // Render chart
        renderChart(canvas, chartData);
        
    } catch (error) {
        console.error('[ChartFix] Error fixing chart:', error);
        
        // Show error message
        const loading = document.getElementById('commit-chart-loading');
        const message = document.getElementById('commit-chart-message');
        
        if (loading) loading.style.display = 'none';
        if (message) {
            message.textContent = `Error loading commit history: ${error.message}`;
            message.style.display = 'block';
        }
    }
}

function prepareCommitData(commits, days = 30) {
    // Create date range map
    const commitsByDate = {};
    const dateFormat = new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric'
    });
    
    // Create date range for past N days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // Initialize all dates with 0 count
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dateKey = d.toISOString().split('T')[0];
        const displayDate = dateFormat.format(d);
        commitsByDate[dateKey] = {
            count: 0,
            displayDate
        };
    }
    
    // Count commits by date
    for (const commit of commits) {
        if (commit.date) {
            const dateKey = commit.date.split('T')[0];
            if (commitsByDate[dateKey]) {
                commitsByDate[dateKey].count++;
            }
        }
    }
    
    // Convert to arrays for Chart.js
    const labels = [];
    const counts = [];
    
    // Sort dates chronologically
    const sortedDates = Object.keys(commitsByDate).sort();
    for (const dateKey of sortedDates) {
        labels.push(commitsByDate[dateKey].displayDate);
        counts.push(commitsByDate[dateKey].count);
    }
    
    console.log('[ChartFix] Prepared chart data:', { labels, counts });
    
    return {
        labels,
        datasets: [{
            label: 'Commits',
            data: counts,
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    };
}

function renderChart(canvas, chartData) {
    try {
        // Make sure Chart.js is available
        if (typeof Chart === 'undefined') {
            console.error('[ChartFix] Chart.js is not available');
            throw new Error('Chart.js library not available');
        }
        
        // Set canvas dimensions
        const parentWidth = canvas.parentElement ? canvas.parentElement.clientWidth : 300;
        canvas.width = parentWidth;
        canvas.height = Math.min(parentWidth * 0.6, 300);
        
        // Configure chart
        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Commit History (Last 30 Days)'
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                return `${value} commit${value !== 1 ? 's' : ''}`;
                            }
                        }
                    }
                }
            }
        };
        
        // Destroy existing chart if it exists
        if (window.fixedChart) {
            console.log('[ChartFix] Destroying previous chart instance');
            window.fixedChart.destroy();
            window.fixedChart = null;
        }
        
        // Create chart
        window.fixedChart = new Chart(canvas, config);
        console.log('[ChartFix] Chart created successfully');
    } catch (error) {
        console.error('[ChartFix] Error rendering chart:', error);
        throw error;
    }
}
