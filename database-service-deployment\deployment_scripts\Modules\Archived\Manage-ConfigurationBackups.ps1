# Configuration Backup Management Module
# This module provides functions for managing configuration backups

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Function to list all available backups
function Show-ConfigurationBackups {
    param (
        [Parameter(Mandatory=$false)]
        [switch]$Silent
    )
    
    # Get backup directory
    $backupDir = Join-Path -Path $script:ConfigDir -ChildPath "backups"
    
    # Check if backup directory exists
    if (-not (Test-Path -Path $backupDir)) {
        if (-not $Silent) {
            Write-Log -Message "No backups found. Backup directory does not exist." -Level "Warning" -Component "Backup"
        }
        return $null
    }
    
    # Get backup files
    $backupFiles = Get-ChildItem -Path $backupDir -Filter "*.json"
    
    # Check if there are any backups
    if ($backupFiles.Count -eq 0) {
        if (-not $Silent) {
            Write-Log -Message "No backups found in $backupDir" -Level "Warning" -Component "Backup"
        }
        return $null
    }
    
    # Enable UI Mode for menu display
    Enable-UIMode
    
    # Create menu
    $index = 1
    $menu = @{}
    
    Write-Log -Message "Available Configuration Backups:" -Level "UI" -ForegroundColor Cyan
    
    # Sort backups by date (newest first)
    $backupFiles = $backupFiles | Sort-Object LastWriteTime -Descending
    
    foreach ($file in $backupFiles) {
        # Extract details from filename
        $fileName = $file.Name
        $datePattern = "\d{8}-\d{6}"
        
        if ($fileName -match "(.+?)_($datePattern)\.json") {
            $configName = $Matches[1]
            $timestamp = $Matches[2]
            
            # Format timestamp for display
            $formattedDate = [DateTime]::ParseExact(
                $timestamp, 
                "yyyyMMdd-HHmmss", 
                [System.Globalization.CultureInfo]::InvariantCulture
            ).ToString("yyyy-MM-dd HH:mm:ss")
            
            # Extract project and environment
            $parts = $configName.Split('-')
            if ($parts.Count -gt 1) {
                $projectName = $parts[0..($parts.Count-2)] -join '-'
                $envName = $parts[-1]
                
                # Color code environment
                $envColor = "White"
                if ($envName -eq "production") {
                    $envColor = "Red"
                } elseif ($envName -eq "development") {
                    $envColor = "Green"
                } elseif ($envName -eq "testing" -or $envName -eq "test") {
                    $envColor = "Yellow"
                }
                
                # Display backup info
                Write-Log -Message "[$index] $projectName" -Level "UI" -ForegroundColor Cyan -NoNewline
                Write-Log -Message " ($envName)" -Level "UI" -ForegroundColor $envColor -NoNewline
                Write-Log -Message ": $formattedDate" -Level "UI" -ForegroundColor White
            } else {
                # Handle case where filename doesn't follow the expected pattern
                Write-Log -Message "[$index] $fileName" -Level "UI" -ForegroundColor White
            }
        } else {
            # Handle legacy backup format
            Write-Log -Message "[$index] $fileName (Legacy Format)" -Level "UI" -ForegroundColor White
        }
        
        $menu[$index] = $file.FullName
        $index++
    }
    
    # Disable UI Mode after menu display
    Disable-UIMode
    
    # Return the menu
    return $menu
}

# Function to restore a backup
function Restore-ConfigurationBackup {
    param (
        [Parameter(Mandatory=$false)]
        [string]$ProjectFilter,
        
        [Parameter(Mandatory=$false)]
        [string]$EnvironmentFilter
    )
    
    # Show available backups
    $menu = Show-ConfigurationBackups
    
    if ($null -eq $menu -or $menu.Count -eq 0) {
        Wait-ForUser
        return
    }
    
    Write-Host
    Write-Host "[0] Back to Main Menu" -ForegroundColor White
    Write-Host
    
    # Get user selection
    $option = Read-Host "Select a backup to restore (or 0 to go back)"
    
    if ($option -eq "0" -or $option -eq "") {
        return
    }
    
    if ($option -match "^\d+$" -and $menu.ContainsKey([int]$option)) {
        $backupFile = $menu[[int]$option]
        
        # Extract the original filename (remove timestamp)
        $fileName = [System.IO.Path]::GetFileName($backupFile)
        
        if ($fileName -match "(.+?)_\d{8}-\d{6}\.json") {
            $originalName = "$($Matches[1]).json"
            
            # Destination path
            $destPath = Join-Path -Path $script:ConfigDir -ChildPath $originalName
            
            # Confirm restore
            Write-Host
            Write-Host "You are about to restore the following backup:" -ForegroundColor Yellow
            Write-Host "Source: $backupFile" -ForegroundColor White
            Write-Host "Destination: $destPath" -ForegroundColor White
            $confirm = Read-Host "Are you sure you want to restore this backup? (y/n)"
            
            if ($confirm -eq "y") {
                # Backup the current configuration if it exists
                if (Test-Path -Path $destPath) {
                    $backupResult = Backup-ConfigurationFile -ConfigFilePath $destPath
                    if ($backupResult) {
                        Write-Host "Backup of current configuration created." -ForegroundColor Green
                    }
                }
                
                # Restore the backup
                try {
                    Copy-Item -Path $backupFile -Destination $destPath -Force
                    Write-Host "Backup restored successfully!" -ForegroundColor Green
                } catch {
                    Write-Host "Failed to restore backup: $_" -ForegroundColor Red
                }
            } else {
                Write-Host "Restore cancelled." -ForegroundColor Yellow
            }
        } else {
            Write-Host "Could not determine original filename from backup." -ForegroundColor Red
        }
    } else {
        Write-Host "Invalid option." -ForegroundColor Red
    }
    
    Wait-ForUser
}

# Function to delete a configuration backup
function Remove-ConfigurationBackup {
    param (
        [Parameter(Mandatory=$false)]
        [string]$ProjectFilter,
        
        [Parameter(Mandatory=$false)]
        [string]$EnvironmentFilter
    )
    
    # Show available backups
    $menu = Show-ConfigurationBackups
    
    if ($null -eq $menu -or $menu.Count -eq 0) {
        Wait-ForUser
        return
    }
    
    Write-Host
    Write-Host "[0] Back to Main Menu" -ForegroundColor White
    Write-Host
    
    # Get user selection
    $option = Read-Host "Select a backup to delete (or 0 to go back)"
    
    if ($option -eq "0" -or $option -eq "") {
        return
    }
    
    if ($option -match "^\d+$" -and $menu.ContainsKey([int]$option)) {
        $backupFile = $menu[[int]$option]
        
        # Confirm deletion
        Write-Host
        Write-Host "You are about to delete the following backup:" -ForegroundColor Yellow
        Write-Host $backupFile -ForegroundColor White
        $confirm = Read-Host "Are you sure you want to delete this backup? (y/n)"
        
        if ($confirm -eq "y") {
            try {
                Remove-Item -Path $backupFile -Force
                Write-Host "Backup deleted successfully." -ForegroundColor Green
            } catch {
                Write-Host "Failed to delete backup: $_" -ForegroundColor Red
            }
        } else {
            Write-Host "Deletion cancelled." -ForegroundColor Yellow
        }
    } else {
        Write-Host "Invalid option." -ForegroundColor Red
    }
    
    Wait-ForUser
}

# Function to delete a configuration
function Remove-Configuration {
    param (
        [Parameter(Mandatory=$false)]
        [string]$ProjectFilter,
        
        [Parameter(Mandatory=$false)]
        [string]$EnvironmentFilter
    )
    
    # Get the configuration directory
    $configFiles = Get-ChildItem -Path $script:ConfigDir -Filter "*.json" | Where-Object { $_.Name -notmatch "_\d{8}-\d{6}\.json" }
    
    if ($configFiles.Count -eq 0) {
        Write-Host "No configurations found." -ForegroundColor Yellow
        Wait-ForUser
        return
    }
    
    # Enable UI Mode for menu display
    Enable-UIMode
    
    # Create menu
    $index = 1
    $configMenu = @{}
    
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Delete Configuration                  " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    
    Write-Log -Message "Available Configurations:" -Level "UI" -ForegroundColor Cyan
    
    foreach ($file in $configFiles) {
        # Extract details from filename
        $fileName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
        $parts = $fileName.Split('-')
        
        if ($parts.Count -gt 1) {
            $projectName = $parts[0..($parts.Count-2)] -join '-'
            $envName = $parts[-1]
            
            # Color code environment
            $envColor = "White"
            if ($envName -eq "production") {
                $envColor = "Red"
            } elseif ($envName -eq "development") {
                $envColor = "Green"
            } elseif ($envName -eq "testing" -or $envName -eq "test") {
                $envColor = "Yellow"
            }
            
            # Display configuration info
            Write-Log -Message "[$index] $projectName" -Level "UI" -ForegroundColor Cyan -NoNewline
            Write-Log -Message " ($envName)" -Level "UI" -ForegroundColor $envColor
        } else {
            # Handle case where filename doesn't follow the expected pattern
            Write-Log -Message "[$index] $fileName" -Level "UI" -ForegroundColor White
        }
        
        $configMenu[$index] = $file.FullName
        $index++
    }
    
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "[C] Cancel" -Level "UI" -ForegroundColor White
    
    # Disable UI Mode after menu display
    Disable-UIMode
    
    $choice = Read-Host "`nSelect a configuration to delete"
    
    if ($choice -eq "C" -or $choice -eq "c") {
        return
    }
    
    if ($choice -match "^\d+$" -and $configMenu.ContainsKey([int]$choice)) {
        $configFile = $configMenu[[int]$choice]
        
        # Ask for confirmation
        Write-Host "`nYou are about to delete the following configuration:" -ForegroundColor Yellow
        Write-Host $configFile -ForegroundColor White
        $confirm = Read-Host "Are you sure you want to delete this configuration? (y/n)"
        
        if ($confirm -eq "y") {
            # Create a backup before deletion
            Backup-ConfigurationFile -ConfigFilePath $configFile
            
            try {
                Remove-Item -Path $configFile -Force
                Write-Host "Configuration deleted successfully." -ForegroundColor Green
            } catch {
                Write-Host "Failed to delete configuration: $_" -ForegroundColor Red
            }
        } else {
            Write-Host "Deletion cancelled." -ForegroundColor Yellow
        }
    } else {
        Write-Host "Invalid selection." -ForegroundColor Red
    }
    
    Wait-ForUser
}

# Function to show backup management menu
function Show-BackupManagementMenu {
    $continueMenu = $true
    
    while ($continueMenu) {
        Clear-Host
        
        # Enable UI Mode for menu display
        Enable-UIMode
        
        Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
        Write-Log -Message "           Configuration Backup Management             " -Level "UI" -ForegroundColor Blue
        Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
        Write-Log -Message " " -Level "UI"
        
        Write-Log -Message "[1] View All Backups" -Level "UI" -ForegroundColor White
        Write-Log -Message "[2] View Backups by Project" -Level "UI" -ForegroundColor White
        Write-Log -Message "[3] View Backups by Environment" -Level "UI" -ForegroundColor White
        Write-Log -Message "[4] Restore a Backup" -Level "UI" -ForegroundColor White
        Write-Log -Message "[5] Delete a Backup" -Level "UI" -ForegroundColor White
        Write-Log -Message "[6] Delete a Configuration" -Level "UI" -ForegroundColor White
        Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
        Write-Log -Message "[0] Back to Main Menu" -Level "UI" -ForegroundColor White
        
        # Disable UI Mode after menu display
        Disable-UIMode
        
        $choice = Read-Host "`nSelect an option (0-6)"
        
        switch ($choice) {
            "1" {
                # Just display the backups, no need to store the result
                Show-ConfigurationBackups
                Wait-ForUser
            }
            "2" {
                # Show development backups
                Show-ConfigurationBackups
                Wait-ForUser
            }
            "3" {
                # Show production backups
                Show-ConfigurationBackups
                Wait-ForUser
            }
            "4" {
                Restore-ConfigurationBackup
            }
            "5" {
                Remove-ConfigurationBackup
            }
            "6" {
                Remove-Configuration
            }
            "0" { $continueMenu = $false }
            "B" { $continueMenu = $false }
            "b" { $continueMenu = $false }
            "" { $continueMenu = $false }
            default {
                Write-Host "Invalid option. Please try again." -ForegroundColor Red
                Start-Sleep -Seconds 1
            }
        }
    }
}

# Make functions available to the script that imports this file
