#pragma once
#include "parser_interface.hpp"
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>

namespace logging {

class ParserManager {
public:
    ParserManager();
    ~ParserManager() = default;
    
    // Register a parser
    void registerParser(std::shared_ptr<LogParser> parser);
    
    // Get a parser by name
    std::shared_ptr<LogParser> getParser(const std::string& name);
    
    // Get all registered parsers
    std::vector<std::shared_ptr<LogParser>> getParsers() const;
    
    // Find a parser that can handle the given log line
    std::shared_ptr<LogParser> findParser(const std::string& line);
    
    // Parse a log line using the appropriate parser
    std::optional<LogEntry> parse(const std::string& line, const std::string& source);
    
private:
    std::vector<std::shared_ptr<LogParser>> parsers_;
    std::unordered_map<std::string, std::shared_ptr<LogParser>> parsersByName_;
    mutable std::mutex mutex_;
};

} // namespace logging
