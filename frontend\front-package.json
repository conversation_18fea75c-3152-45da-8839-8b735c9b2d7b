{"name": "project-tracker-frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@tanstack/react-query": "^4.29.5", "@types/node": "^16.18.27", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@types/socket.io-client": "^3.0.0", "axios": "^1.4.0", "chart.js": "^4.3.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.9", "react-router-dom": "^6.11.1", "socket.io-client": "^4.7.2", "tailwindcss": "^3.3.3", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "lint": "eslint src --ext .ts,.tsx", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "typecheck": "tsc --noEmit"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.8", "react-scripts": "5.0.1"}}