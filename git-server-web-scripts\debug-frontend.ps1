# Debugging frontend issues on the server

# Configuration
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"

# Check API call from server side
Write-Host "Testing API access on server..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" @"
echo 'API Endpoints test:'
echo '-----------------------'
echo 'Metrics API:'
curl -s http://localhost:8000/api/metrics/current | grep -o '"status":"[^"]*"' || echo 'No status found'

echo '\nGit API:'
curl -s http://localhost:8000/api/git/repositories | grep -o '"status":"[^"]*"' || echo 'No status found'

echo '\nChecking JavaScript loading:'
ls -la ${RemotePath}/js/ | grep git-

echo '\nChecking for JavaScript console errors in browser:'
sudo apt-get update -qq && sudo apt-get install -y -qq nodejs npm chromium-browser > /dev/null 2>&1
npx --yes playwright@latest install-deps chromium > /dev/null 2>&1

cat > /tmp/check-console.js << 'EOF'
const { chromium } = require('playwright');

(async () => {
  try {
    console.log('Launching browser...');
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Collect console messages
    page.on('console', msg => {
      console.log(`CONSOLE ${msg.type()}: ${msg.text()}`);
    });
    
    // Collect JavaScript errors
    page.on('pageerror', error => {
      console.error(`PAGE ERROR: ${error.message}`);
    });
    
    // Network request monitoring
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`REQUEST: ${request.method()} ${request.url()}`);
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`RESPONSE: ${response.status()} ${response.url()}`);
      }
    });
    
    // Visit the dashboard
    console.log('Navigating to the dashboard...');
    await page.goto('http://localhost/', { waitUntil: 'networkidle', timeout: 30000 });
    
    console.log('\nChecking for GitRepositoryManager initialization:');
    const hasGitManager = await page.evaluate(() => {
      return typeof GitRepositoryManager !== 'undefined' && GitRepositoryManager;
    });
    console.log(`GitRepositoryManager defined: ${hasGitManager ? 'YES' : 'NO'}`);
    
    if (hasGitManager) {
      const gitState = await page.evaluate(() => {
        return JSON.stringify(GitRepositoryManager.state || {});
      });
      console.log(`GitRepositoryManager state: ${gitState}`);
    }
    
    console.log('\nPage title:', await page.title());
    await browser.close();
  } catch (error) {
    console.error('Error running browser tests:', error);
  }
})();
EOF

node /tmp/check-console.js
"@
