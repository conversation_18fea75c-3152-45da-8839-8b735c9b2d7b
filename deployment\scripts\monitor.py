from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import asyncio
import aiohttp
import logging
from prometheus_client import start_http_server, Counter, Gauge, Histogram
from abc import ABC, abstractmethod

# Metrics definitions
REQUEST_LATENCY = Histogram(
    'request_latency_seconds',
    'Request latency in seconds',
    ['endpoint', 'method']
)

ERROR_COUNTER = Counter(
    'error_total',
    'Total number of errors',
    ['type', 'service']
)

SYSTEM_METRICS = Gauge(
    'system_metrics',
    'System metrics',
    ['metric_name']
)

@dataclass
class HealthCheck:
    service: str
    status: bool
    latency: float
    last_check: datetime
    details: Dict[str, Any]

class MonitoringService:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.health_checks: Dict[str, HealthCheck] = {}
        
    async def start(self):
        """Start the monitoring service"""
        try:
            # Start Prometheus metrics server
            start_http_server(self.config['metrics_port'])
            
            # Start health check loop
            asyncio.create_task(self._health_check_loop())
            
            # Start system metrics collection
            asyncio.create_task(self._collect_system_metrics())
            
            self.logger.info("Monitoring service started successfully")
        except Exception as e:
            self.logger.error(f"Failed to start monitoring service: {e}")
            raise

    async def _health_check_loop(self):
        """Continuous health check loop"""
        while True:
            try:
                checks = await self._perform_health_checks()
                self._update_health_metrics(checks)
            except Exception as e:
                self.logger.error(f"Health check loop failed: {e}")
                ERROR_COUNTER.labels(
                    type='health_check_loop',
                    service='monitor'
                ).inc()
            await asyncio.sleep(self.config['health_check_interval'])

    async def _perform_health_checks(self) -> List[HealthCheck]:
        """Perform health checks for all services"""
        async with aiohttp.ClientSession() as session:
            tasks = []
            for service in self.config['services']:
                tasks.append(self._check_service(session, service))
            return await asyncio.gather(*tasks, return_exceptions=True)

    async def _check_service(
        self,
        session: aiohttp.ClientSession,
        service: Dict[str, Any]
    ) -> HealthCheck:
        """Check individual service health"""
        start_time = datetime.now()
        try:
            async with session.get(
                service['health_endpoint'],
                timeout=service.get('timeout', 5)
            ) as response:
                latency = (datetime.now() - start_time).total_seconds()
                return HealthCheck(
                    service=service['name'],
                    status=response.status == 200,
                    latency=latency,
                    last_check=datetime.now(),
                    details={'status_code': response.status}
                )
        except Exception as e:
            self.logger.error(f"Health check failed for {service['name']}: {e}")
            return HealthCheck(
                service=service['name'],
                status=False,
                latency=-1,
                last_check=datetime.now(),
                details={'error': str(e)}
            )

    def _update_health_metrics(self, checks: List[HealthCheck]):
        """Update Prometheus metrics based on health checks"""
        for check in checks:
            if isinstance(check, Exception):
                continue
            
            SYSTEM_METRICS.labels(
                f"{check.service}_health"
            ).set(1 if check.status else 0)
            
            SYSTEM_METRICS.labels(
                f"{check.service}_latency"
            ).set(check.latency)

    async def _collect_system_metrics(self):
        """Collect system metrics"""
        while True:
            try:
                metrics = await self._gather_system_metrics()
                self._update_system_metrics(metrics)
            except Exception as e:
                self.logger.error(f"System metrics collection failed: {e}")
                ERROR_COUNTER.labels(
                    type='system_metrics',
                    service='monitor'
                ).inc()
            await asyncio.sleep(self.config['metrics_interval'])