# Project Tracker Deployment Overview

# Project Tracker Overview

Project Tracker is a comprehensive project management and improvement tracking system that helps teams monitor progress, track improvements, analyze code changes, generate insights and collaborate effectively.

## Deployment

The Project Tracker uses a standardized deployment process that works from both Windows and Ubuntu environments:

### Windows Build Server

1. Use `prepare-deployment.ps1` to create the deployment package:
   ```powershell
   .\prepare-deployment.ps1
   ```

2. Deploy using `auto-deploy.ps1`:
   ```powershell
   .\auto-deploy.ps1 -Server "project-tracker.chcit.org" -Password $password
   ```

### Ubuntu Server

1. Deploy using `auto-deploy.sh`:
   ```bash
   sudo -S su - root << "EOF"
   bash auto-deploy.sh
   EOF
   ```

### Nginx Modules

The Project Tracker uses Nginx with Brotli compression and Image Filter modules:

1. **Brotli Compression**
   - Dynamic and static compression
   - Optimized for web assets
   - Configuration in `nginx-modules.conf`

2. **Image Filter**
   - Automatic image optimization
   - WebP conversion
   - Quality settings in `nginx-modules.conf`

### Validation

1. Check module installation:
   ```bash
   sudo -S su - root << "EOF"
   bash verify-nginx-modules.sh
   EOF
   ```

2. Verify deployment:
   ```bash
   sudo -S su - root << "EOF"
   python3 validate-deployment.py
   EOF
   ```

## Quick Start

### Automated Deployment from Windows

For fully automated deployment from your Windows build server:

```powershell
# 1. Navigate to the deployment directory
cd tools\project-tracker\deployment

# 2. Run the PowerShell deployment script (credentials pre-configured)
.\auto-deploy.ps1
```

The PowerShell script handles the entire deployment process:
- Uses WSL (Windows Subsystem for Linux) to run the bash deployment script
- Prepares the deployment package locally
- Copies files to the target Ubuntu server
- Automatically elevates privileges using sudo
- Configures the server with provided parameters
- Verifies network configuration and services
- Validates that the application is running correctly

For custom parameters, run `.\auto-deploy.ps1 -Help`

### Automated Deployment from Linux

If you're running from a Linux build server:

```bash
# 1. Clone the repository
git clone <repository-url>
cd project-tracker/deployment

# 2. Run the automated deployment script (credentials pre-configured)
./auto-deploy.sh
```

The `auto-deploy.sh` script handles the entire deployment process with the same functionality as the PowerShell version.

For custom parameters, run `./auto-deploy.sh --help`

### Manual Deployment

If you prefer to deploy manually:

```bash
# 1. Clone and prepare deployment package
git clone <repository-url>
cd project-tracker/deployment
chmod +x *.sh *.py
./prepare-deployment.sh  # Uses dash in filename

# 2. Copy to server and deploy
scp -r project-tracker-deploy user@***********:/tmp/
ssh btaylor-admin@***********
sudo mv /tmp/project-tracker-deploy /opt/project-tracker

# 3. Change to directory and set permissions
cd /opt/project-tracker
# Permission setting is now handled automatically by configure-server.sh

# 4. Run configuration script
sudo ./configure-server.sh
```

The `configure-server.sh` script automatically:
- Installs all system dependencies
- Sets up Python 3.12 environment
- Configures network settings with multiple fallback methods
- Sets up time synchronization
- Installs and configures PostgreSQL, Redis, and Nginx
- Creates and enables all necessary services
- Applies security hardening

## Features

### Authentication System
- Three-tier role-based access control
  * Site Admin: Full system access and configuration
  * Admin: Project and user management
  * User: Personal project and settings management

#### Authentication Mechanisms
- Secure password hashing using PBKDF2
- JWT-based token authentication
- Multi-factor authentication support
- Automatic account lockout after multiple failed login attempts

## System Requirements

- Ubuntu 24.04 (Noble)
- Python 3.12
- PostgreSQL 16
- Redis 7.2
- Nginx 1.25
- Chrony (time synchronization)

## Network Configuration

The deployment script handles network configuration with multiple approaches to ensure compatibility with Ubuntu 24.04:

1. **Automatic interface detection**: Identifies the correct network interface
2. **Primary configuration**: Uses netplan with standard syntax
3. **Fallback methods**:
   - Alternative netplan syntax with gateway4 parameter
   - Manual IP configuration using ip commands
   - Direct resolv.conf updates for DNS

If you encounter network issues, you can manually apply the configuration:

```bash
# Check detected interface
ip link show

# Apply IP configuration manually
sudo ip addr add ***********/24 dev <interface>
sudo ip route add default via *********** dev <interface>

# Verify configuration
ip addr show
ip route show
```

## Privilege Elevation

The automated deployment scripts handle privilege elevation automatically:

1. **Initial connection**: Connects to the server with regular user credentials
2. **Elevation method**: Uses `sudo -S su - root` with password piped from stdin
3. **Single session**: Executes all privileged commands in a single root session
4. **No interaction**: No manual password entry required during deployment

This approach ensures a smooth deployment process without requiring manual intervention for privilege elevation.

## Authentication Configuration

### User Roles
1. **Site Admin**
   - Complete system configuration
   - User management across all roles
   - Access to all system settings

2. **Admin**
   - Manage projects
   - Create and modify user accounts (except Site Admin)
   - Access project-level configurations

3. **User**
   - Manage personal projects
   - Update personal settings
   - Limited system access

### Security Features
- Password complexity requirements
- Automatic account lockout
- IP-based login tracking
- Secure token-based authentication

## Detailed Manual Steps
The following sections detail the individual steps that are automated by the scripts. You only need these if you want to understand the process or perform manual installation.

### Installation

#### 1. Prepare Deployment Package

```bash
# Clone the repository
git clone <repository-url>
cd project-tracker

# Make deployment scripts executable
cd deployment
chmod +x *.sh
chmod +x *.py

# Run deployment preparation script
./prepare-deployment.sh
```

#### 2. Deploy to Server

```bash
# Copy deployment package to server
scp -r project-tracker-deploy user@***********:/tmp/

# SSH into server
ssh user@***********

# Move to installation directory
sudo mv /tmp/project-tracker-deploy /opt/project-tracker
cd /opt/project-tracker
```

#### 3. Install Dependencies

```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install -y \
    python3.12 \
    python3.12-venv \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    python3-wheel \
    python3-setuptools \
    libpython3-dev \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    pkg-config \
    chrony

# Set Python 3.12 as default
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1

# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python packages
python3 -m pip install --upgrade pip wheel setuptools
python3 -m pip install -r requirements.txt
```

#### 4. Configure Server

```bash
# Run server configuration script
sudo ./configure-server.sh

# Run security hardening script
sudo ./security-hardening.sh --domain your-domain.com --email <EMAIL>

# Verify time synchronization
chronyc tracking
chronyc sources -v
```

#### 5. Set Up Services

```bash
# Copy and enable systemd service
sudo cp project-tracker.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable project-tracker
sudo systemctl start project-tracker

# Enable and start time synchronization
sudo systemctl enable chrony
sudo systemctl start chrony
```

## Troubleshooting Authentication

### Common Issues
- Locked accounts due to multiple failed login attempts
- Token expiration
- Permission-related access denials

### Debugging
```bash
# Check authentication service logs
journalctl -u project-tracker-auth

# Verify user account status
python3 manage.py user:status <username>
```

## Improvement Tracking

### Documentation
- Centralized markdown file for all improvements
- Categorized tracking of updates
- Clear status tracking

### Update Process
- Timestamp all updates
- Link to specific changes
- Regular status reviews

### Key Categories
- Configuration Management
- Frontend Architecture
- Communication Systems
- Authentication
- Error Handling
- Performance Optimization

## Validation and Monitoring

```bash
# Check environment setup
python3 validate_environment.py

# Fix common issues automatically
sudo python3 auto_fix.py

# View service status
sudo systemctl status project-tracker
sudo check-project-tracker
```

## Directory Structure

```
/opt/project-tracker/
├── src/                 # Application source code
├── venv/               # Python virtual environment
├── logs/               # Application logs
├── data/               # Application data
└── deployment/         # Deployment scripts and configurations
```

## Service Management

### Core Services
```bash
# Project Tracker
sudo systemctl start project-tracker
sudo systemctl stop project-tracker
sudo systemctl restart project-tracker

# Database
sudo systemctl start postgresql
sudo systemctl restart postgresql

# Cache
sudo systemctl start redis
sudo systemctl restart redis

# Web Server
sudo systemctl start nginx
sudo systemctl restart nginx

# Time Synchronization
sudo systemctl start chrony
sudo systemctl restart chrony
```

### View Service Status
```bash
# View specific service status
sudo systemctl status project-tracker
sudo systemctl status postgresql
sudo systemctl status redis
sudo systemctl status nginx
sudo systemctl status chrony

# View service logs
sudo journalctl -u project-tracker -f
sudo journalctl -u postgresql -f
sudo journalctl -u redis -f
sudo journalctl -u nginx -f
sudo journalctl -u chrony -f
```

### Time Synchronization Status
```bash
# Check time sync status
chronyc tracking

# View time sources
chronyc sources -v

# Check if system clock is synchronized
chronyc sources -v
```

## Security Features

- SSL/TLS configuration with automatic renewal
- Fail2ban for brute force protection
- UFW firewall rules
- Security headers
- ModSecurity WAF
- Automated security scans

## Troubleshooting

1. If package installation fails:
   ```bash
   # Install build dependencies
   sudo apt-get install -y python3-dev build-essential
   
   # Recreate virtual environment
   rm -rf venv
   python3 -m venv venv
   source venv/bin/activate
   ```

2. If services won't start:
   ```bash
   # Check service status
   sudo systemctl status project-tracker
   
   # View logs
   sudo journalctl -u project-tracker -n 50
   ```

3. For permission issues:
   ```bash
   # Fix common permission problems
   sudo python3 auto_fix.py
   ```

4. For Python version issues:
   ```bash
   # Verify Python version
   python3 --version  # Should show Python 3.12
   
   # If wrong version, set Python 3.12 as default
   sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1
   ```

5. For time synchronization issues:
   ```bash
   # Check chrony status
   sudo systemctl status chrony
   
   # View chrony tracking
   chronyc tracking
   
   # Restart time synchronization
   sudo systemctl restart chrony
   ```

## Support

For issues and support:
1. Check the logs in `/var/log/project-tracker/`
2. Run the validation script: `python3 validate_environment.py`
3. Try the auto-fix script: `sudo python3 auto_fix.py`
4. Contact the system administrator

## Related Documentation

- [Documentation Index](docs/DOCUMENTATION-INDEX.md)
- [API Documentation](docs/api/README.md)
- [Project Tracker Status](docs/tracking/project-tracker-status.md)