#!/bin/bash

# Script to check for self-signed certificate creation on the remote server
# This script should be run on the Git server to examine what's happening on the remote server

# Configuration
MAIN_SERVER="***********"
MAIN_SERVER_USER="btaylor-admin"
SSH_KEY="/home/<USER>/.ssh/id_ed25519"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "Checking for certificate creation scripts on remote server"

# Use appropriate command based on current user
if [ "$(id -un)" = "btaylor-admin" ]; then
    SSH_CMD="ssh -i \"$SSH_KEY\" -o ConnectTimeout=10 -o BatchMode=yes \"${MAIN_SERVER_USER}@${MAIN_SERVER}\""
else
    SSH_CMD="sudo -u btaylor-admin ssh -i \"$SSH_KEY\" -o ConnectTimeout=10 -o BatchMode=yes \"${MAIN_SERVER_USER}@${MAIN_SERVER}\""
fi

# Check for any scripts that might be creating self-signed certificates
log "Checking for OpenSSL commands in cron jobs"
eval "$SSH_CMD \"grep -r 'openssl' /etc/cron* /var/spool/cron 2>/dev/null || echo 'No OpenSSL commands found in cron jobs'\""

log "Checking for certificate creation scripts"
eval "$SSH_CMD \"find /etc /opt /usr/local/bin -name '*.sh' -type f -exec grep -l 'openssl.*req' {} \\; 2>/dev/null || echo 'No certificate creation scripts found'\""

log "Checking for recent certificate operations"
eval "$SSH_CMD \"grep -i 'certificate\\|openssl\\|self-signed' /var/log/syslog /var/log/auth.log 2>/dev/null | tail -20 || echo 'No recent certificate operations found in logs'\""

log "Remote server check completed"
