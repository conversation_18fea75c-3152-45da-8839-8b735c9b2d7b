# Git Server Web Documentation Index

This document serves as the master index for all documentation related to the Git Server Web project.

## Overview

The Git Server Web project consists of several components:
- JavaScript Dashboard
- C++23 Git Repository Service
- C++23 Logging Service
- C++23 Database Service

## Component Documentation

### Dashboard
- [Dashboard Overview](dashboard/overview.md)
- [Dashboard Installation](dashboard/installation.md)
- [Dashboard Configuration](dashboard/configuration.md)
- [Dashboard User Guide](dashboard/user-guide.md)

### Git Repository Service
- [Git Repository Service Overview](git-repo-service/overview.md)
- [Git Repository Service Installation](git-repo-service/installation.md)
- [Git Repository Service API](git-repo-service/api.md)

### Logging Service
- [Logging Service Overview](logging-service/overview.md)
- [Logging Service Installation](logging-service/installation.md)
- [Logging Service API](logging-service/api.md)
- [Logging Service Error Handling](logging-service/error-handling.md) (New)
- [Logging Service Health Monitoring](logging-service/health-monitoring.md) (New)

### Database Service
- [Database Service Overview](database-service/overview.md)
- [Database Service Installation](database-service/installation.md)
- [Database Service API](database-service/api.md)

## Deployment Documentation
- [Server Setup](deployment/server-setup.md)
- [Deployment Scripts](deployment/deployment-scripts.md)
- [Monitoring and Maintenance](deployment/monitoring.md)

## Architecture Documentation
- [System Architecture](architecture/system-architecture.md)
- [Data Flow](architecture/data-flow.md)
- [Security Model](architecture/security.md)

## Development Documentation
- [Development Environment Setup](development/setup.md)
- [Coding Standards](development/coding-standards.md)
- [Testing Procedures](development/testing.md)
- [Continuous Integration](development/ci.md)

## Last Updated: 2023-11-01

## Legacy Documentation

Some documentation has been archived as it refers to components that are being replaced by C++23 services:

- [Flask and Gunicorn Guide](Archived/FLASK-GUNICORN-GUIDE.md) (Archived)
- [Git JavaScript Modules Analysis](Archived/git-javascript-modules-analysis.md) (Archived)
- [CSS Implementation Guide](Archived/css-implementation-guide.md) (Archived)
- [Installation Steps](Archived/installation-steps.md) (Archived)
- [File Analysis](Archived/file-analysis.md) (Archived)
- [Git Dashboard Architecture](Archived/git-dashboard-architecture.md) (Archived)
- [Master Index](Archived/MASTER-INDEX.md) (Archived)
