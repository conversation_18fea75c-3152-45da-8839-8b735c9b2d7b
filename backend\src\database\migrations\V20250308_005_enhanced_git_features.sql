-- Enhanced Git Features Migration
-- Version: V20250308_005
-- Description: Adds advanced git operation tracking and analysis features

-- Git Commit Details Table
CREATE TABLE IF NOT EXISTS git_commits (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    commit_hash VARCHAR(40) NOT NULL,
    parent_hashes TEXT[], -- Array of parent commit hashes for merge commits
    author_name <PERSON><PERSON><PERSON><PERSON>(255),
    author_email VARCHAR(255),
    committer_name VARCHAR(255),
    committer_email VARCHAR(255),
    commit_message TEXT,
    commit_date TIMESTAMP WITH TIME ZONE,
    branch_name VA<PERSON><PERSON><PERSON>(255),
    is_merge BOOLEAN DEFAULT false,
    changed_files JSONB, -- Detailed file changes
    performance_impact JSONB, -- Performance metrics related to this commit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Git File History Table
CREATE TABLE IF NOT EXISTS git_file_history (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    file_path VARCHAR(512) NOT NULL,
    commit_hash VARCHAR(40) NOT NULL,
    change_type VARCHAR(50), -- 'added', 'modified', 'deleted', 'renamed'
    lines_added INTEGER DEFAULT 0,
    lines_deleted INTEGER DEFAULT 0,
    old_file_path VARCHAR(512), -- For renamed files
    binary_file BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Git Code Review Table
CREATE TABLE IF NOT EXISTS git_code_reviews (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    branch_name VARCHAR(255) NOT NULL,
    reviewer_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'changes_requested'
    review_comments TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    metrics JSONB -- Review metrics like time taken, number of comments, etc.
);

-- Git Performance Alerts Table
CREATE TABLE IF NOT EXISTS git_performance_alerts (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL, -- 'large_commit', 'slow_operation', 'frequent_commits'
    severity VARCHAR(50) NOT NULL, -- 'low', 'medium', 'high'
    message TEXT NOT NULL,
    metrics JSONB,
    resolved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Git Branch Protection Rules
CREATE TABLE IF NOT EXISTS git_branch_protection (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    branch_pattern VARCHAR(255) NOT NULL, -- Supports glob patterns
    require_code_review BOOLEAN DEFAULT true,
    required_reviewers INTEGER DEFAULT 1,
    dismiss_stale_reviews BOOLEAN DEFAULT true,
    require_up_to_date BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add columns to git_repositories for enhanced features
ALTER TABLE git_repositories
ADD COLUMN IF NOT EXISTS performance_settings JSONB DEFAULT '{
    "max_commit_size": 1000000,
    "alert_on_large_commits": true,
    "track_file_history": true,
    "monitor_branch_age": true
}'::jsonb,
ADD COLUMN IF NOT EXISTS analysis_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS last_analysis_at TIMESTAMP WITH TIME ZONE;

-- Indexes for performance
CREATE INDEX idx_git_commits_repo_hash ON git_commits(repository_id, commit_hash);
CREATE INDEX idx_git_commits_date ON git_commits(commit_date);
CREATE INDEX idx_git_file_history_repo_path ON git_file_history(repository_id, file_path);
CREATE INDEX idx_git_code_reviews_repo_status ON git_code_reviews(repository_id, status);
CREATE INDEX idx_git_performance_alerts_repo ON git_performance_alerts(repository_id, resolved);

-- Update triggers
CREATE TRIGGER update_git_branch_protection_updated_at
    BEFORE UPDATE ON git_branch_protection
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();
