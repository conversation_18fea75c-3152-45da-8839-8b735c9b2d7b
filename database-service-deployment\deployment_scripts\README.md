# Database Service Deployment Scripts

This directory contains PowerShell scripts for deploying the Database Service to various environments.

## Recent Fixes

The following issues have been fixed in the deployment scripts:

1. **Module Structure and Import Issues**
   - Fixed module import mechanism to properly handle PS1 files as modules
   - Added checks to prevent duplicate module imports
   - Implemented proper module export handling

2. **Variable Scope Conflicts**
   - Standardized use of `$script:` prefix for script-level variables
   - Fixed variable scope issues in configuration loading

3. **Configuration Loading and Access**
   - Improved configuration loading with better error handling
   - Added default values for missing configuration properties
   - Fixed environment detection from configuration filenames

4. **SSH Command Execution**
   - Fixed SSH command construction to properly handle paths with spaces
   - Improved error handling for SSH commands
   - Added better logging for SSH operations

5. **Menu System**
   - Updated menu options to use imported module functions
   - Added fallback to direct script execution when module import fails

## Usage

1. Run the main deployment script:
   ```powershell
   .\deploy-database-service-modular.ps1
   ```

2. Select the appropriate option from the menu to perform deployment tasks.

## Configuration

Configuration files are stored in the `config` directory with the naming convention:
```
database-service-{environment}.json
```

Where `{environment}` is one of:
- development
- testing
- production

## Modules

The deployment scripts use a modular approach with the following components:

- **Common.psm1**: Core functions and variables shared across all scripts
- **Logger.psm1**: Centralized logging system
- **Modules/*.ps1**: Individual modules for specific deployment tasks

## Troubleshooting

If you encounter issues:

1. Check the logs in the `logs` directory
2. Ensure SSH keys are properly configured
3. Verify the configuration file for the selected environment exists
4. Make sure all required modules are available

For persistent issues, try cleaning the build directory on the remote server:
```
rm -rf /home/<USER>/database-service-build/*
```
