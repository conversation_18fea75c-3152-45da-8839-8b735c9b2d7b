# Git Dashboard System Architecture

## Overview
The Git Dashboard is a web-based monitoring system that displays server performance metrics and Git repository statistics. It consists of a Flask backend, a frontend built with HTML/JavaScript/Chart.js, and uses Nginx as a reverse proxy. The system collects metrics periodically and displays them in a user-friendly dashboard.

## Backup Information
- **Backup File**: `git-dashboard-backup.tar.gz`
- **Backup Date**: March 13, 2025
- **Current Version**: 1.0

## System Components

### 1. Backend (Flask Application)

#### Files
- **Path**: `/opt/git-dashboard/app.py`
- **Purpose**: Provides the REST API endpoints for metrics data
- **Dependencies**: Flask, psutil, GitPython, datetime, os, json, logging
- **Associated Config**: systemd service file at `/etc/systemd/system/git-dashboard.service`
- **API Endpoints**:
  - `/api/metrics/current` - Current system metrics
  - `/api/metrics/history` - Historical metrics data

### 2. Metrics Collection

#### Files
- **Path**: `/opt/git-dashboard/collect-metrics.sh`
- **Purpose**: Bash script to collect system metrics and Git statistics
- **Dependencies**: Bash, standard Linux tools (awk, grep, etc.), Git
- **Data Storage**: `/opt/git-dashboard/data/metrics_history.json`
- **Historical Data**: `/opt/git-dashboard/data/history/`
- **Execution**: Runs as a cron job every minute

### 3. Frontend (Web UI)

#### HTML Files
- **Path**: `/opt/git-dashboard/index.html`
- **Purpose**: Main dashboard interface
- **Dependencies**: Bootstrap, Chart.js
- **Associated JS**: dashboard.js, metrics-history.js

#### JavaScript Files
- **Path**: `/opt/git-dashboard/js/dashboard.js`
- **Purpose**: Main dashboard logic, handles API calls and UI updates
- **Dependencies**: Chart.js
- **API Interactions**: Calls `/api/metrics/current` and `/api/metrics/history`

- **Path**: `/opt/git-dashboard/js/metrics-history.js`
- **Purpose**: Handles history chart creation and updates
- **Dependencies**: Chart.js, dashboard.js

#### CSS Files
- **Path**: `/opt/git-dashboard/css/`
- **Purpose**: Contains stylesheets for the dashboard

### 4. Web Server (Nginx)

#### Configuration Files
- **Path**: `/etc/nginx/sites-available/git-dashboard.conf`
- **Symlink**: `/etc/nginx/sites-enabled/git-dashboard.conf`
- **Purpose**: Reverse proxy configuration, static file serving, SSL
- **Dependencies**: Nginx, SSL certificates
- **Routes**:
  - `/` - Serves static files from `/opt/git-dashboard/`
  - `/api/` - Proxies requests to Flask backend on port 8000

### 5. Service Management

#### Systemd Service
- **Path**: `/etc/systemd/system/git-dashboard.service`
- **Purpose**: Manages the Flask application as a system service
- **Dependencies**: systemd
- **Security Features**: Resource limits, file access restrictions

## Directory Structure
```
/opt/git-dashboard/
├── app.py                  # Flask application
├── collect-metrics.sh      # Metrics collection script
├── css/                    # CSS stylesheets
├── data/                   # Data storage
│   ├── metrics_history.json # Current metrics data file
│   └── history/            # Historical metrics archives
├── debug.html              # Debug page for troubleshooting
├── index.html              # Main dashboard HTML
├── js/                     # JavaScript files
│   ├── dashboard.js        # Main dashboard logic
│   └── metrics-history.js  # Chart visualization logic
└── status.html             # Status check page
```

## Configuration Details

### Nginx Configuration
- **Server Name**: git.chcit.org
- **SSL**: Enabled with Let's Encrypt certificates
- **Listen Ports**: 443 (HTTPS)
- **Security Headers**: HSTS, CSP, XSS Protection, etc.
- **API Rate Limiting**: Enabled for `/api/` routes

### Systemd Service Configuration
- **User**: root
- **Working Directory**: `/opt/git-dashboard`
- **ExecStart**: `/opt/git-dashboard/venv/bin/python -m flask run --host=0.0.0.0 --port=8000`
- **Restart**: always (restarts on failure)
- **Dependencies**: Nginx service
- **Resource Limits**:
  - CPU Quota: 50%
  - Memory Max: 256M
  - File Descriptors: 65535

### Security Settings
- **Firewall**: UFW enabled with ports 80 and 443 open
- **File Permissions**: All files owned by root:root with 644/755 permissions
- **Service Isolation**: Enabled with systemd security directives
- **API Rate Limiting**: Prevents abuse of API endpoints

## Restoration Instructions

1. Extract the backup archive:
   ```bash
   sudo tar -xzf git-dashboard-backup.tar.gz -C /tmp
   ```

2. Restore the main application files:
   ```bash
   sudo mkdir -p /opt/git-dashboard
   sudo cp -r /tmp/git-dashboard-backup/* /opt/git-dashboard/
   ```

3. Restore configuration files:
   ```bash
   sudo cp /tmp/git-dashboard-backup/config/git-dashboard.conf /etc/nginx/sites-available/
   sudo ln -sf /etc/nginx/sites-available/git-dashboard.conf /etc/nginx/sites-enabled/
   sudo cp /tmp/git-dashboard-backup/config/git-dashboard.service /etc/systemd/system/
   ```

4. Set proper permissions:
   ```bash
   sudo chown -R root:root /opt/git-dashboard
   sudo chmod -R 755 /opt/git-dashboard
   sudo chmod 644 /opt/git-dashboard/*.html /opt/git-dashboard/js/*.js /opt/git-dashboard/css/*.css
   sudo chmod 755 /opt/git-dashboard/app.py /opt/git-dashboard/collect-metrics.sh
   ```

5. Reload service configurations:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl restart nginx
   sudo systemctl restart git-dashboard
   ```

## Known Issues

1. Git repository data not showing in the dashboard. This needs to be fixed in:
   - The metrics collection script to properly gather Git statistics
   - The dashboard.js file to correctly display Git data from the API

## Recommendations for Improvement

1. Separate server performance data from Git data into different views/tabs for a cleaner interface
2. Implement a responsive, modern UI with framework like Vue.js or React
3. Add user authentication for the dashboard
4. Improve error handling and offline support
5. Add alert notifications for anomalies in metrics
