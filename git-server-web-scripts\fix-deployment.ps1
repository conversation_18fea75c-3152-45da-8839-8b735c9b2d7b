# Fix deployment script for Git Server Dashboard

# Configuration
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"
$LocalProjectPath = "D:/Codeium/CHCIT/project-tracker/git-server-web"

Write-Host "Deploying JavaScript files..." -ForegroundColor Yellow

# Deploy all JavaScript files properly
$jsFiles = @(
    "git-utils.js",
    "git-commit-history-chart.js",
    "git-size-comparison-chart.js",
    "git-repository-list.js",
    "git-repository-modal.js",
    "git-repository-manager.js"
)

# Make sure JS directory exists
ssh "${RemoteUser}@${RemoteHost}" "sudo mkdir -p ${RemotePath}/js"

foreach ($file in $jsFiles) {
    Write-Host "  - Deploying $file..." -ForegroundColor Cyan
    # Use cat to ensure file is binary clean when transferred
    Get-Content -Path "${LocalProjectPath}/js/${file}" -Raw | ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/${file}"
    ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/${file} ${RemotePath}/js/${file} && sudo chown www-data:www-data ${RemotePath}/js/${file} && sudo chmod 644 ${RemotePath}/js/${file}"
}

# Deploy CSS file
Write-Host "Deploying CSS files..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo mkdir -p ${RemotePath}/css"
Get-Content -Path "${LocalProjectPath}/css/repositories.css" -Raw | ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/repositories.css"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/repositories.css ${RemotePath}/css/repositories.css && sudo chown www-data:www-data ${RemotePath}/css/repositories.css && sudo chmod 644 ${RemotePath}/css/repositories.css"

# Deploy index.html
Write-Host "Deploying index.html..." -ForegroundColor Yellow
Get-Content -Path "${LocalProjectPath}/index.html" -Raw | ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/index.html"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/index.html ${RemotePath}/index.html && sudo chown www-data:www-data ${RemotePath}/index.html && sudo chmod 644 ${RemotePath}/index.html"

# Restart services
Write-Host "Restarting services..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo systemctl restart git-dashboard.service && sudo systemctl restart nginx"

# Verify deployment
Write-Host "Verifying deployment..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "test -f ${RemotePath}/js/git-utils.js && echo 'Files deployed successfully' || echo 'Deployment failed'"

Write-Host "Deployment fix completed!" -ForegroundColor Green
