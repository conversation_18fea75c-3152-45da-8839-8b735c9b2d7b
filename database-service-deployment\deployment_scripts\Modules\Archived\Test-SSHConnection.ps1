# Test SSH Connection Module

# Set default environment if not already set
if (-not (Get-Variable -Name Environment -Scope Script -ErrorAction SilentlyContinue)) {
    $script:Environment = "development"
}

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
    }
} catch {
    Write-Host "Error loading required modules: $_" -ForegroundColor Red
}

# Test SSH connection
function Test-SSHConnectionUI {
    Clear-Host

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Test SSH Connection                    " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after menu display
    Disable-UIMode

    # Load configuration directly from file
    $configFile = "D:\Augment\project-tracker\database-service-deployment\deployment_scripts\config\database-service-development.json"
    $config = $null

    if (Test-Path -Path $configFile) {
        try {
            $config = Get-Content -Path $configFile -Raw | ConvertFrom-Json
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "Error" -Component "SSH"
            Wait-ForUser
            Show-MainMenu
            return
        }
    } else {
        Write-Log -Message "Configuration file not found: $configFile" -Level "Error" -Component "SSH"
        Wait-ForUser
        Show-MainMenu
        return
    }

    # Check if configuration is loaded
    if ($null -eq $config -or $null -eq $config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "SSH"
        Wait-ForUser
        Show-MainMenu
        return
    }

    # Get SSH connection details with proper property names
    $sshHost = $config.ssh.host
    $sshPort = $config.ssh.port
    $sshUser = $config.ssh.username
    $sshKeyPath = $null

    # Check for different possible key path property names
    if ($config.ssh.PSObject.Properties.Name -contains "local_key_path") {
        $sshKeyPath = $config.ssh.local_key_path
    } elseif ($config.ssh.PSObject.Properties.Name -contains "key_path") {
        $sshKeyPath = $config.ssh.key_path
    }

    # Enable UI Mode for displaying connection details
    Enable-UIMode

    Write-Log -Message "Testing SSH connection to $sshHost..." -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Host: $sshHost" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $sshPort" -Level "UI" -ForegroundColor White
    Write-Log -Message "  User: $sshUser" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Key: $sshKeyPath" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after displaying connection details
    Disable-UIMode

    # Check if SSH key path is valid
    if ([string]::IsNullOrWhiteSpace($sshKeyPath)) {
        Write-Log -Message "SSH key path is not set in the configuration." -Level "Error" -Component "SSH"
        Write-Log -Message "Please set up SSH keys first using option [2] from the main menu." -Level "Warning" -Component "SSH"
        Write-Log -Message "Select 'Manage SSH Keys' and then 'Set SSH Key Path' to configure your SSH key." -Level "Info" -Component "SSH"
        Wait-ForUser
        Show-MainMenu
        return
    }

    # Expand environment variables in the key path
    $expandedKeyPath = [Environment]::ExpandEnvironmentVariables($sshKeyPath)

    if (-not (Test-Path $expandedKeyPath)) {
        Write-Log -Message "SSH key file does not exist at: $expandedKeyPath" -Level "Error" -Component "SSH"
        Write-Log -Message "Please check your SSH key configuration." -Level "Warning" -Component "SSH"
        Write-Log -Message "You can generate a new SSH key using option [2] from the main menu." -Level "Info" -Component "SSH"
        Write-Log -Message "Select 'Manage SSH Keys' and then 'Generate New SSH Key'." -Level "Info" -Component "SSH"
        Wait-ForUser
        Show-MainMenu
        return
    }

    try {
        Write-Log -Message "Executing SSH connection test..." -Level "Info" -Component "SSH"

        # Use the SSHManager module if available
        if (Get-Module -Name "SSHManager") {
            # Call the Test-SSHConnection function from the SSHManager module
            $connected = SSHManager\Test-SSHConnection -HostName $sshHost -User $sshUser -Port $sshPort -KeyPath $expandedKeyPath -Component "SSH"

            if ($connected) {
                Write-Log -Message "SSH connection successful!" -Level "Success" -Component "SSH"
            } else {
                Write-Log -Message "SSH connection failed." -Level "Error" -Component "SSH"
                Write-Log -Message "Please check your SSH configuration." -Level "Warning" -Component "SSH"
            }
        } else {
            # Fallback to direct SSH command if SSHManager is not available
            $testCmd = "echo 'SSH connection successful'"

            # Add options to bypass host checking and increase verbosity
            $sshCommand = "ssh -i `"$expandedKeyPath`" -p $sshPort -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $sshUser@$sshHost `"$testCmd`""
            Write-Log -Message "Using direct SSH command" -Level "Debug" -Component "SSH"

            $result = Invoke-Expression $sshCommand 2>&1

            if ($null -ne $result -and $result -match "SSH connection successful") {
                Write-Log -Message "SSH connection successful!" -Level "Success" -Component "SSH"
            } else {
                Write-Log -Message "SSH connection failed." -Level "Error" -Component "SSH"
                Write-Log -Message "Error details: $result" -Level "Error" -Component "SSH"
                Write-Log -Message "Please check your SSH configuration." -Level "Warning" -Component "SSH"
            }
        }
    } catch {
        Write-Log -Message "Error testing SSH connection: $_" -Level "Error" -Component "SSH"
    }

    # Enable UI Mode for final message
    Enable-UIMode

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Press Enter to return to the main menu..." -Level "UI" -ForegroundColor Cyan

    # Disable UI Mode before waiting for user
    Disable-UIMode

    Wait-ForUser

    # Return to the main menu
    if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
        Show-MainMenu
    } else {
        # If Show-MainMenu is not available, try to import the Common module
        try {
            Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
            Show-MainMenu
        } catch {
            Write-Log -Message "Failed to return to main menu: $_" -Level "Error" -Component "SSH"
            Write-Host "Press Enter to exit..." -ForegroundColor Cyan
            $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        }
    }
}

# Run the function
Test-SSHConnectionUI
