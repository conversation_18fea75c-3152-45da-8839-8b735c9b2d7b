import os
import json
from typing import Dict, Any
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def load_config() -> Dict[str, Any]:
    """Load application configuration with WebSocket and monitoring settings."""
    
    # Base configuration
    config = {
        'ENV': os.getenv('FLASK_ENV', 'development'),
        'DEBUG': os.getenv('FLASK_DEBUG', 'true').lower() == 'true',
        
        # Database configuration
        'DATABASE_URL': os.getenv(
            'DATABASE_URL',
            'postgresql://localhost:5432/project_tracker'
        ),
        
        # Redis configuration
        'REDIS_URL': os.getenv(
            'REDIS_URL',
            'redis://localhost:6379/0'
        ),
        
        # WebSocket configuration (aligned with frontend)
        'WEBSOCKET': {
            'port': 8081,
            'ping_timeout': 10,
            'ping_interval': 5,
            'max_reconnection_attempts': 5,
            'token_expiration': 3600,  # 1 hour in seconds
        },
        
        # Security settings
        'SECURITY': {
            'token_secret': os.getenv('TOKEN_SECRET', 'your-secret-key'),
            'allowed_origins': [
                'https://projecttracker.local',
                'https://api.projecttracker.local',
                'https://cdn.projecttracker.local'
            ],
            'ssl_required': True,
            'rate_limit': {
                'enabled': True,
                'max_requests': 1000,
                'window_ms': 900000  # 15 minutes
            }
        },
        
        # Monitoring configuration
        'MONITORING': {
            'enabled': True,
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            'metrics': {
                'collection_interval': 300,  # 5 minutes
                'retention_days': 7
            },
            'error_tracking': {
                'enabled': True,
                'detailed_logs': True
            }
        },
        
        # Cache configuration
        'CACHE': {
            'enabled': True,
            'default_ttl': 3600,
            'max_size': 1000000,  # Maximum cache size in bytes
            'monitoring': {
                'enabled': True,
                'alert_threshold': {
                    'hit_rate': 0.85,
                    'error_rate': 0.01
                }
            }
        }
    }
    
    # Load environment-specific configuration
    config_path = Path(__file__).parent.parent.parent / 'config'
    env_config_file = config_path / f"{config['ENV']}.json"
    
    if env_config_file.exists():
        try:
            with open(env_config_file) as f:
                env_config = json.load(f)
                config.update(env_config)
                logger.info(f"Loaded configuration from {env_config_file}")
        except Exception as e:
            logger.error(
                f"Error loading config from {env_config_file}: {str(e)}",
                exc_info=True
            )
            
    return config
