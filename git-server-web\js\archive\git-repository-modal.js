/**
 * Git Repository Modal Module
 * Handles the details modal for repositories
 */
const GitRepositoryModal = {
    // Module state
    state: {
        currentRepository: null,
        modalElement: null,
        modalInstance: null, // Store reference to Bootstrap modal instance
        initialized: false
    },
    
    // Reference to the manager module
    manager: null,
    
    /**
     * Initialize the repository modal module
     * @param {Object} managerInstance - Reference to the GitRepositoryManager
     * @returns {Object} This module for chaining
     */
    init(managerInstance) {
        try {
            console.log('Initializing GitRepositoryModal...');
            
            // Store reference to manager
            this.manager = managerInstance;
            
            // Initialize state
            this.state = {
                initialized: false,
                modalElement: null,
                modalInstance: null,
                currentRepository: null
            };
            
            // Create modal immediately
            this.createRepositoryDetailsModal();
            
            // Set up event listeners
            this.setupEventListeners();
            
            console.log('GitRepositoryModal initialized successfully');
            return this;
        } catch (error) {
            console.error('Error initializing GitRepositoryModal:', error);
            return this;
        }
    },
    
    /**
     * Create the repository details modal
     */
    createRepositoryDetailsModal() {
        try {
            // Check if modal already exists
            const existingModal = document.getElementById('repositoryDetailsModal');
            if (existingModal) {
                console.log('Modal already exists, using existing one');
                this.state.modalElement = existingModal;
                this.state.initialized = true;
                return;
            }
            
            // Create modal container
            const modalContainer = document.createElement('div');
            modalContainer.className = 'modal fade';
            modalContainer.id = 'repositoryDetailsModal';
            modalContainer.tabIndex = '-1';
            modalContainer.setAttribute('aria-labelledby', 'repositoryDetailsModalLabel');
            modalContainer.setAttribute('aria-hidden', 'true');
            
            // Create modal content
            modalContainer.innerHTML = `
                <div class="modal-dialog modal-dialog-centered modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="repositoryDetailsModalLabel">Repository Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <!-- Loading state -->
                            <div id="repository-details-loading" class="p-4 text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading repository details...</p>
                            </div>
                            
                            <!-- Error state -->
                            <div id="repository-details-error" class="p-4 text-center" style="display: none;">
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <p>Failed to load repository details.</p>
                            </div>
                            
                            <!-- Content state -->
                            <div id="repository-details-content" style="display: none;">
                                <!-- Repository Info -->
                                <div class="p-4 border-bottom">
                                    <h5 id="repo-name">Repository Name</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Size:</strong> <span id="repo-size">Unknown</span></p>
                                            <p class="mb-1"><strong>Created:</strong> <span id="repo-created">Unknown</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Last Commit:</strong> <span id="repo-last-commit">Never</span></p>
                                            <p class="mb-1"><strong>Owner:</strong> <span id="repo-owner">Unknown</span></p>
                                        </div>
                                    </div>
                                    
                                    <!-- Repository Health -->
                                    <div class="mt-3">
                                        <h6>Repository Health</h6>
                                        <div class="progress">
                                            <div id="health-bar" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <p id="health-message" class="text-muted mt-1 small">Calculating repository health...</p>
                                    </div>
                                </div>
                                
                                <!-- Commit History Chart -->
                                <div class="border-bottom p-4">
                                    <h6>Commit History</h6>
                                    <div id="commit-history-container" class="chart-container" style="position: relative; height: 300px;">
                                        <div id="commit-chart-loading" class="d-flex justify-content-center align-items-center h-100">
                                            <div class="spinner-border spinner-border-sm text-primary me-2 spinning" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <span>Loading commit history...</span>
                                        </div>
                                        <div id="commit-chart-error" class="d-flex justify-content-center align-items-center h-100 text-danger" style="display: none;">
                                            Chart could not be loaded
                                        </div>
                                        <canvas id="commit-history-chart" style="display: none;"></canvas>
                                        <div id="commit-chart-message" class="text-center text-muted mt-2" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Append modal to body
            document.body.appendChild(modalContainer);
            
            // Verify the modal and canvas were added to the DOM
            const canvasElement = document.getElementById('commit-history-chart');
            console.log(`[Modal] Modal created, canvas element exists: ${!!canvasElement}`);
            
            // Store reference to modal element
            this.state.modalElement = modalContainer;
            this.state.initialized = true;
            
            console.log('Modal created successfully');
        } catch (error) {
            console.error('Error creating repository details modal:', error);
        }
    },
    
    /**
     * Set up event listeners for modal events
     */
    setupEventListeners() {
        if (!this.state.modalElement) {
            console.error('Cannot set up event listeners: modal element not found');
            return;
        }
        
        try {
            // Add event listeners for modal events
            this.state.modalElement.addEventListener('hidden.bs.modal', this.handleModalHidden.bind(this));
            this.state.modalElement.addEventListener('shown.bs.modal', this.handleModalShown.bind(this));
            
            // Don't add redundant event listeners to close buttons that already have data-bs-dismiss="modal"
            // Bootstrap's built-in dismissal will handle closing through data-bs-dismiss attribute
            
            console.log('Modal event listeners set up successfully');
        } catch (error) {
            console.error('Error setting up modal event listeners:', error);
        }
    },
    
    /**
     * Close the modal
     */
    closeModal() {
        try {
            // Check if modal is initialized
            if (!this.state.initialized) {
                console.error('Modal not initialized');
                return;
            }
            
            // Get modal element
            const modalElement = this.state.modalElement;
            if (!modalElement) {
                console.error('Modal element not found');
                return;
            }
            
            // Try to use Bootstrap's API
            if (typeof bootstrap !== 'undefined') {
                const bootstrapModal = bootstrap.Modal.getInstance(modalElement);
                if (bootstrapModal) {
                    console.log('Using Bootstrap API to close modal');
                    bootstrapModal.hide();
                    return;
                }
            }
            
            // Fallback to direct DOM manipulation
            modalElement.classList.remove('show');
            modalElement.setAttribute('aria-hidden', 'true');
            modalElement.style.display = 'none';
            
            // Remove modal backdrop if exists
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            
            // Enable scrolling on body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            console.log('Modal closed');
        } catch (error) {
            console.error('Error closing modal:', error);
        }
    },
    
    /**
     * Handle modal hidden event - clean up resources
     */
    handleModalHidden() {
        console.log('Modal hidden event triggered');
        
        // Clean up chart resources
        if (window.GitCommitHistoryChart && typeof GitCommitHistoryChart.cleanupChart === 'function') {
            GitCommitHistoryChart.cleanupChart();
        }
        
        // Reset selected repository
        this.state.currentRepository = null;
        this.state.modalInstance = null;
    },
    
    /**
     * Handle modal shown event - initialize content
     */
    handleModalShown() {
        console.log('[Modal] Shown event triggered');
        
        try {
            // Check if modal is initialized
            if (!this.state.initialized) {
                console.error('Modal not initialized');
                return;
            }
            
            // Get current repository
            const repo = this.state.currentRepository;
            if (!repo) {
                console.error('No repository selected');
                this.showError('No repository selected');
                return;
            }
            
            console.log(`[Modal] Initializing content for repository: ${repo.name}`);
            
            // Update modal content
            this.updateModalContent(repo);
            
            // Update health indicator
            this.updateHealthIndicator(repo);
            
            // Directly fetch and display commit history
            console.log(`[Modal] Requesting commit history fetch for ${repo.name}`);
            this.fetchAndDisplayCommitHistory(repo.name);
            
            // Add a failsafe to ensure the chart is displayed
            setTimeout(() => {
                console.log('[Modal] Failsafe timeout triggered');
                const canvas = document.getElementById('commit-history-chart');
                const container = document.getElementById('commit-history-container');
                const loading = document.getElementById('commit-chart-loading');
                
                if (container) {
                    container.style.display = 'block';
                    console.log('[Modal] Forced container to display: block');
                }
                
                if (loading) {
                    loading.style.display = 'none';
                    console.log('[Modal] Forced loading to display: none');
                }
                
                if (canvas) {
                    canvas.style.display = 'block';
                    console.log('[Modal] Forced canvas to display: block');
                } else if (container) {
                    // Create canvas if it doesn't exist
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = 'commit-history-chart';
                    newCanvas.style.display = 'block';
                    newCanvas.width = container.clientWidth || 300;
                    newCanvas.height = 200;
                    container.appendChild(newCanvas);
                    console.log('[Modal] Created new canvas element');
                    
                    // Try to render chart again
                    if (repo && repo.name) {
                        this.fetchAndDisplayCommitHistory(repo.name);
                    }
                }
            }, 1000);
        } catch (error) {
            console.error('[Modal] Error handling modal shown event:', error);
            this.showError('Failed to load repository details');
        }
    },
    
    /**
     * Show repository details in modal
     * @param {string} repoName - Repository name
     */
    showRepositoryDetails(repoName) {
        console.log(`Showing repository details for: ${repoName}`);
        
        if (!this.state.initialized) {
            console.error('Repository modal not initialized');
            return;
        }
        
        // Find repository by name
        if (!this.manager) {
            console.error('Repository manager not available');
            return;
        }
        
        const repo = this.manager.getRepositoryByName(repoName);
        if (!repo) {
            console.error(`Repository not found: ${repoName}`);
            this.showError('Repository not found');
            return;
        }
        
        // Store current repository
        this.state.currentRepository = repo;
        console.log(`[Modal] Current repository set to: ${repo.name}`);
        
        // Show modal
        this.showModal();
    },
    
    /**
     * Show loading state in modal
     */
    showLoading() {
        try {
            const loadingElement = document.getElementById('repository-details-loading');
            const errorElement = document.getElementById('repository-details-error');
            const contentElement = document.getElementById('repository-details-content');
            
            if (loadingElement) loadingElement.style.display = 'block';
            if (errorElement) errorElement.style.display = 'none';
            if (contentElement) contentElement.style.display = 'none';
        } catch (error) {
            console.error('Error showing loading state:', error);
        }
    },
    
    /**
     * Show error state in modal
     * @param {string} message - Error message
     */
    showError(message) {
        try {
            const loadingElement = document.getElementById('repository-details-loading');
            const errorElement = document.getElementById('repository-details-error');
            const contentElement = document.getElementById('repository-details-content');
            
            if (loadingElement) loadingElement.style.display = 'none';
            if (contentElement) contentElement.style.display = 'none';
            
            if (errorElement) {
                errorElement.style.display = 'block';
                errorElement.innerHTML = `
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <p>${message || 'Failed to load repository details'}</p>
                `;
            }
        } catch (error) {
            console.error('Error showing error state:', error);
        }
    },
    
    /**
     * Show content state in modal
     */
    showContent() {
        try {
            const loadingElement = document.getElementById('repository-details-loading');
            const errorElement = document.getElementById('repository-details-error');
            const contentElement = document.getElementById('repository-details-content');
            
            if (loadingElement) loadingElement.style.display = 'none';
            if (errorElement) errorElement.style.display = 'none';
            if (contentElement) contentElement.style.display = 'block';
        } catch (error) {
            console.error('Error showing content state:', error);
        }
    },
    
    /**
     * Update modal content with repository details
     * @param {Object} repo - Repository object
     */
    updateModalContent(repo) {
        if (!repo) {
            console.error('Cannot update modal content: repository object is null');
            this.showError('Invalid repository data');
            return;
        }
        
        try {
            // Update repository name
            const repoNameElement = document.getElementById('repo-name');
            if (repoNameElement) {
                repoNameElement.textContent = repo.name;
            }
            
            // Update repository size
            const repoSizeElement = document.getElementById('repo-size');
            if (repoSizeElement) {
                repoSizeElement.textContent = repo.size_formatted || 'Unknown';
            }
            
            // Update created date
            const repoCreatedElement = document.getElementById('repo-created');
            if (repoCreatedElement) {
                if (repo.created) {
                    try {
                        const createdDate = new Date(repo.created);
                        repoCreatedElement.textContent = createdDate.toLocaleDateString();
                    } catch (e) {
                        repoCreatedElement.textContent = 'Unknown';
                    }
                } else {
                    repoCreatedElement.textContent = 'Unknown';
                }
            }
            
            // Update last commit
            const repoLastCommitElement = document.getElementById('repo-last-commit');
            if (repoLastCommitElement) {
                if (repo.last_commit) {
                    try {
                        const lastCommitDate = new Date(repo.last_commit);
                        repoLastCommitElement.textContent = lastCommitDate.toLocaleString();
                    } catch (e) {
                        repoLastCommitElement.textContent = 'Unknown';
                    }
                } else {
                    repoLastCommitElement.textContent = 'Never';
                }
            }
            
            // Update owner
            const repoOwnerElement = document.getElementById('repo-owner');
            if (repoOwnerElement) {
                repoOwnerElement.textContent = repo.owner || 'Unknown';
            }
            
            // Update health indicator
            this.updateHealthIndicator(repo);
            
            // Show content
            this.showContent();
            
            console.log('Modal content updated successfully');
        } catch (error) {
            console.error('Error updating modal content:', error);
            this.showError('Error loading repository details');
        }
    },
    
    /**
     * Update repository health indicator
     * @param {Object} repo - Repository object
     */
    updateHealthIndicator(repo) {
        try {
            const healthBar = document.getElementById('health-bar');
            const healthMessage = document.getElementById('health-message');
            
            if (!healthBar || !healthMessage) {
                console.error('Health indicator elements not found');
                return;
            }
            
            // Get health status from repo
            const healthStatus = repo.health_status || 'unknown';
            const healthScore = repo.health_score || 0;
            const healthMsg = repo.health_message || 'No health information available';
            
            // Update health bar
            healthBar.style.width = `${healthScore}%`;
            healthBar.setAttribute('aria-valuenow', healthScore);
            
            // Set color based on health status
            healthBar.className = 'progress-bar';
            switch (healthStatus) {
                case 'good':
                    healthBar.classList.add('bg-success');
                    break;
                case 'fair':
                    healthBar.classList.add('bg-warning');
                    break;
                case 'poor':
                    healthBar.classList.add('bg-danger');
                    break;
                default:
                    healthBar.classList.add('bg-secondary');
            }
            
            // Update health message
            healthMessage.textContent = healthMsg;
            
            console.log(`Health indicator updated: ${healthStatus} (${healthScore}%)`);
        } catch (error) {
            console.error('Error updating health indicator:', error);
        }
    },
    
    /**
     * Ensure the chart canvas element exists
     * @returns {HTMLElement|null} The canvas element or null if it couldn't be created
     */
    ensureChartCanvasExists() {
        try {
            // Check if canvas already exists
            let canvas = document.getElementById('commit-history-chart');
            if (canvas) {
                console.log('[Chart] Canvas element already exists');
                return canvas;
            }
            
            // Try to find the container
            const container = document.getElementById('commit-history-container');
            if (!container) {
                console.error('[Chart] Cannot create canvas: container element not found');
                return null;
            }
            
            console.log('[Chart] Creating new canvas element');
            
            // Remove any existing canvas elements (just in case)
            const existingCanvases = container.querySelectorAll('canvas');
            existingCanvases.forEach(canvas => canvas.remove());
            
            // Create new canvas
            canvas = document.createElement('canvas');
            canvas.id = 'commit-history-chart';
            canvas.style.display = 'none';
            canvas.width = container.clientWidth || 300;
            canvas.height = 200;
            
            // Add canvas to container
            container.appendChild(canvas);
            
            console.log('[Chart] New canvas element created and added to DOM');
            return canvas;
        } catch (error) {
            console.error('[Chart] Error creating canvas element:', error);
            return null;
        }
    },
    
    /**
     * Fetch and display commit history
     * @param {string} repoName - Repository name
     */
    async fetchAndDisplayCommitHistory(repoName) {
        console.log(`[Chart] fetchAndDisplayCommitHistory called for ${repoName}`);
        
        // Get UI elements
        const container = document.getElementById('commit-history-container');
        const loading = document.getElementById('commit-chart-loading');
        const error = document.getElementById('commit-chart-error');
        const message = document.getElementById('commit-chart-message');
        
        // Log the presence of UI elements
        console.log(`[Chart] UI elements found: container=${!!container}, loading=${!!loading}, error=${!!error}, message=${!!message}`);
        
        if (!container) {
            console.error('[Chart] Container element not found');
            return;
        }
        
        // Ensure canvas exists
        const canvas = this.ensureChartCanvasExists();
        if (!canvas) {
            console.error('[Chart] Failed to create or find canvas element');
            if (error) {
                error.textContent = 'Failed to initialize chart canvas';
                error.style.display = 'flex';
            }
            if (loading) loading.style.display = 'none';
            return;
        }
        
        // Show loading state
        console.log('[Chart] Setting loading state...');
        if (loading) loading.style.display = 'flex';
        if (error) error.style.display = 'none';
        if (canvas) canvas.style.display = 'none';
        if (message) message.style.display = 'none';
        
        try {
            // Cleanup existing chart instance if exists
            if (window.currentChart) {
                console.log('[Chart] Destroying previous chart instance');
                window.currentChart.destroy();
                window.currentChart = null;
            }
            
            // Clean repository name (remove .git extension if present)
            const cleanRepoName = repoName.replace(/\.git$/, '');
            console.log(`[Chart] Using clean repository name: ${cleanRepoName}`);
            
            // Get commit history from GitRepositoryManager if available
            let commits = [];
            const days = 30;
            
            // Try to get data from GitRepositoryManager first
            if (window.GitRepositoryManager && typeof GitRepositoryManager.fetchCommitHistory === 'function') {
                console.log('[Chart] Attempting fetch via GitRepositoryManager');
                try {
                    const result = await GitRepositoryManager.fetchCommitHistory(cleanRepoName, days);
                    console.log('[Chart] Manager fetch result:', result);
                    if (result && result.commits) {
                        commits = result.commits;
                        console.log(`[Chart] Got ${commits.length} commits via Manager`);
                    }
                } catch (managerError) {
                    console.error('[Chart] Error fetching via Manager:', managerError);
                    // Continue to try direct API call
                }
            }
            
            // If no commits yet, try direct API call
            if (!commits.length) {
                console.log('[Chart] Manager fetch failed or returned no commits, falling back to direct API call');
                const url = `/api/git/repository/${encodeURIComponent(cleanRepoName)}/commits?days=${days}`;
                console.log(`[Chart] Fetching directly from URL: ${url}`);
                
                const response = await fetch(url);
                console.log(`[Chart] Direct API response status: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('[Chart] Direct API response data:', data);
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                if (data.commits && Array.isArray(data.commits)) {
                    commits = data.commits;
                    console.log(`[Chart] Got ${commits.length} commits via direct API`);
                }
            }
            
            console.log(`[Chart] Final commit count: ${commits.length}`);
            
            // If no commits, show message
            if (!commits.length) {
                console.log('[Chart] No commits found for the period.');
                if (loading) loading.style.display = 'none';
                if (message) {
                    message.textContent = 'No commits found in the selected time period';
                    message.style.display = 'block';
                }
                return;
            }
            
            // Format data for chart
            console.log('[Chart] Preparing data for chart...');
            const chartData = this.prepareCommitDataForChart(commits, days);
            
            // Verify canvas is still in the DOM
            if (!document.getElementById('commit-history-chart')) {
                console.error('[Chart] Canvas element disappeared from DOM before rendering');
                throw new Error('Canvas element not found in DOM');
            }
            
            // Create and display chart
            console.log('[Chart] Hiding loading, showing canvas...');
            if (loading) loading.style.display = 'none';
            if (canvas) canvas.style.display = 'block';
            
            // Create chart
            console.log('[Chart] Rendering chart...');
            this.renderCommitChart(canvas, chartData, days);
            
        } catch (error) {
            console.error('[Chart] Error fetching/rendering commit history:', error);
            if (loading) loading.style.display = 'none';
            if (error) {
                error.textContent = `Failed to load commit history: ${error.message}`;
                error.style.display = 'flex';
            }
        }
    },
    
    /**
     * Prepare commit data for chart
     * @param {Array} commits - Commit objects
     * @param {number} days - Number of days to include
     * @returns {Object} Chart data
     */
    prepareCommitDataForChart(commits, days = 30) {
        // Create date range map
        const commitsByDate = {};
        const dateFormat = new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: 'numeric'
        });
        
        // Create date range for past N days
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        
        // Initialize all dates with 0 count
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            const dateKey = d.toISOString().split('T')[0];
            const displayDate = dateFormat.format(d);
            commitsByDate[dateKey] = {
                count: 0,
                displayDate
            };
        }
        
        // Count commits by date
        for (const commit of commits) {
            if (commit.date) {
                const dateKey = commit.date.split('T')[0];
                if (commitsByDate[dateKey]) {
                    commitsByDate[dateKey].count++;
                }
            }
        }
        
        // Convert to arrays for Chart.js
        const labels = [];
        const counts = [];
        
        // Sort dates chronologically
        const sortedDates = Object.keys(commitsByDate).sort();
        for (const dateKey of sortedDates) {
            labels.push(commitsByDate[dateKey].displayDate);
            counts.push(commitsByDate[dateKey].count);
        }
        
        console.log('Prepared chart data:', { labels, counts });
        
        return {
            labels,
            datasets: [{
                label: 'Commits',
                data: counts,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
    },
    
    /**
     * Render the commit history chart
     * @param {HTMLElement} canvas - Canvas element
     * @param {Object} chartData - Chart data
     * @param {number} days - Number of days
     */
    renderCommitChart(canvas, chartData, days = 30) {
        try {
            // Make sure Chart.js is available
            if (typeof Chart === 'undefined') {
                console.error('[Chart] Chart.js is not available');
                throw new Error('Chart.js library not available');
            }
            
            // Verify canvas is still in the DOM
            if (!document.body.contains(canvas)) {
                console.error('[Chart] Canvas is no longer in the DOM');
                // Try to get it again
                canvas = document.getElementById('commit-history-chart');
                if (!canvas) {
                    throw new Error('Canvas element not found in DOM');
                }
            }
            
            // Set canvas dimensions
            const parentWidth = canvas.parentElement ? canvas.parentElement.clientWidth : 300;
            canvas.width = parentWidth;
            canvas.height = Math.min(parentWidth * 0.6, 300);
            
            // Force canvas to be visible
            canvas.style.display = 'block';
            
            // Configure chart
            const config = {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `Commit History (Last ${days} Days)`
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const value = context.raw;
                                    return `${value} commit${value !== 1 ? 's' : ''}`;
                                }
                            }
                        }
                    }
                }
            };
            
            // Destroy existing chart if it exists
            if (window.currentChart) {
                console.log('[Chart] Destroying previous chart instance');
                window.currentChart.destroy();
                window.currentChart = null;
            }
            
            // Create chart
            window.currentChart = new Chart(canvas, config);
            console.log('[Chart] Chart created successfully');
        } catch (error) {
            console.error('[Chart] Error rendering chart:', error);
            throw error;
        }
    },
    
    /**
     * Show the modal
     */
    showModal() {
        try {
            // Show loading state
            this.showLoading();
            
            // Get modal element
            const modalElement = this.state.modalElement;
            if (!modalElement) {
                console.error('[Modal] Modal element not found');
                return;
            }
            
            console.log('[Modal] Showing modal');
            
            // Try to use Bootstrap's API
            if (typeof bootstrap !== 'undefined') {
                // Get existing instance or create new one
                let modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (!modalInstance) {
                    modalInstance = new bootstrap.Modal(modalElement);
                    this.state.modalInstance = modalInstance;
                }
                
                console.log('[Modal] Using Bootstrap API to show modal');
                modalInstance.show();
                return;
            }
            
            // Fallback to direct DOM manipulation
            console.log('[Modal] Falling back to manual modal showing');
            modalElement.classList.add('show');
            modalElement.style.display = 'block';
            modalElement.removeAttribute('aria-hidden');
            modalElement.setAttribute('aria-modal', 'true');
            modalElement.setAttribute('role', 'dialog');
            
            // Add backdrop
            if (!document.querySelector('.modal-backdrop')) {
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            }
            
            // Disable scrolling on body
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
            
            // Trigger shown event manually
            setTimeout(() => {
                this.handleModalShown();
            }, 300);
        } catch (error) {
            console.error('[Modal] Error showing modal:', error);
        }
    },
};

// Expose the module globally
window.GitRepositoryModal = GitRepositoryModal;
