-- pqxx package not found, will attempt to use system libraries
-- Configuring done (0.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
[1/30] Scanning /home/<USER>/database-service-build/src/core/connection_manager.cpp for CXX dependencies
[2/30] Scanning /home/<USER>/database-service-build/src/client/database_client.cpp for CXX dependencies
[3/30] Scanning /home/<USER>/database-service-build/src/api/api_server.cpp for CXX dependencies
[4/30] Scanning /home/<USER>/database-service-build/src/main.cpp for CXX dependencies
[5/30] Scanning /home/<USER>/database-service-build/src/schema/schema_manager.cpp for CXX dependencies
[6/30] Scanning /home/<USER>/database-service-build/src/database_service.cpp for CXX dependencies
[7/30] Scanning /home/<USER>/database-service-build/src/utils/logger.cpp for CXX dependencies
[8/30] Scanning /home/<USER>/database-service-build/src/security/security_manager.cpp for CXX dependencies
[9/30] Scanning /home/<USER>/database-service-build/src/modules/dbapi.cppm for CXX dependencies
[10/30] Scanning /home/<USER>/database-service-build/src/modules/dbcore.cppm for CXX dependencies
[11/30] Scanning /home/<USER>/database-service-build/src/modules/dbschema.cppm for CXX dependencies
[12/30] Scanning /home/<USER>/database-service-build/src/modules/dbsec.cppm for CXX dependencies
[13/30] Scanning /home/<USER>/database-service-build/src/modules/dbsvc.cppm for CXX dependencies
[14/30] Scanning /home/<USER>/database-service-build/src/modules/dbmain.cppm for CXX dependencies
[15/30] Generating CXX dyndep file CMakeFiles/database-service.dir/CXX.dd
FAILED: CMakeFiles/database-service.dir/CXX.dd /home/<USER>/database-service-build/build/CMakeFiles/database-service.dir/CXXModules.json CMakeFiles/database-service.dir/src/api/api_server.cpp.o.modmap CMakeFiles/database-service.dir/src/client/database_client.cpp.o.modmap CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o.modmap CMakeFiles/database-service.dir/src/database_service.cpp.o.modmap CMakeFiles/database-service.dir/src/main.cpp.o.modmap CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o.modmap CMakeFiles/database-service.dir/src/security/security_manager.cpp.o.modmap CMakeFiles/database-service.dir/src/utils/logger.cpp.o.modmap CMakeFiles/database-service.dir/src/modules/dbcore.cppm.o.modmap CMakeFiles/database-service.dir/src/modules/dbapi.cppm.o.modmap CMakeFiles/database-service.dir/src/modules/dbschema.cppm.o.modmap CMakeFiles/database-service.dir/src/modules/dbsec.cppm.o.modmap CMakeFiles/database-service.dir/src/modules/dbsvc.cppm.o.modmap CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o.modmap 
/usr/bin/cmake -E cmake_ninja_dyndep --tdi=CMakeFiles/database-service.dir/CXXDependInfo.json --lang=CXX --modmapfmt=clang --dd=CMakeFiles/database-service.dir/CXX.dd @CMakeFiles/database-service.dir/CXX.dd.rsp
CMake Error: Output CMakeFiles/database-service.dir/src/modules/dbcore.cppm.o provides the `dbcore` module but it is not found in a `FILE_SET` of type `CXX_MODULES`
CMake Error: Output CMakeFiles/database-service.dir/src/modules/dbapi.cppm.o provides the `dbapi` module but it is not found in a `FILE_SET` of type `CXX_MODULES`
CMake Error: Output CMakeFiles/database-service.dir/src/modules/dbschema.cppm.o provides the `dbschema` module but it is not found in a `FILE_SET` of type `CXX_MODULES`
CMake Error: Output CMakeFiles/database-service.dir/src/modules/dbsec.cppm.o provides the `dbsec` module but it is not found in a `FILE_SET` of type `CXX_MODULES`
CMake Error: Output CMakeFiles/database-service.dir/src/modules/dbsvc.cppm.o provides the `dbsvc` module but it is not found in a `FILE_SET` of type `CXX_MODULES`
CMake Error: Output CMakeFiles/database-service.dir/src/modules/dbmain.cppm.o provides the `dbmain` module but it is not found in a `FILE_SET` of type `CXX_MODULES`
ninja: build stopped: subcommand failed.
