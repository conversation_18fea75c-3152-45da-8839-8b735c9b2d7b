from pathlib import Path
from typing import Dict
import yaml

class ConfigManager:
    def __init__(self, config_path: str = "config/"):
        self.config_path = Path(config_path)
        self._config: Dict = {}
        self.load_config()

    def load_config(self) -> None:
        """Load configuration from YAML files"""
        try:
            with open(self.config_path / "templates.yaml") as f:
                self._config["templates"] = yaml.safe_load(f)
            
            with open(self.config_path / "monitoring.yaml") as f:
                self._config["monitoring"] = yaml.safe_load(f)
                
        except FileNotFoundError as e:
            logger.critical(f"Configuration file not found: {e.filename}")
            raise ConfigurationError(f"Missing configuration file: {e.filename}")
            
        except yaml.YAMLError as e:
            logger.critical(f"Invalid YAML in configuration: {str(e)}")
            raise ConfigurationError(f"Invalid configuration format: {str(e)}")

    @property
    def templates(self) -> Dict:
        return self._config.get("templates", {})

    @property
    def monitoring(self) -> Dict:
        return self._config.get("monitoring", {})