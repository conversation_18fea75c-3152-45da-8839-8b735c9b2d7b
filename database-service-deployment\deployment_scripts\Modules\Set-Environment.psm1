# Set Environment Module

# NO DIRECT IMPORTS - use functions defined in main script

# Define a simple logging function that doesn't rely on external modules
function Write-SimpleLog {
    param([string]$Message, [ConsoleColor]$Color = [ConsoleColor]::White)
    # For UI output, don't show timestamp
    Write-Host $Message -ForegroundColor $Color
}

function Set-Environment {
    Clear-Host
    Write-Host "========== Set Environment ==========" -ForegroundColor Cyan
    Write-Host "               Set Environment                         " -ForegroundColor Cyan
    Write-Host "========== Set Environment ==========" -ForegroundColor Cyan
    Write-Host ""
    $envs = @("Development", "Staging", "Production")
    for ($i = 0; $i -lt $envs.Count; $i++) {
        Write-Host "[$($i+1)] $($envs[$i])" -ForegroundColor White
    }
    Write-Host
    $choice = Read-Host "Select environment (1-$($envs.Count))"
    if ($choice -match '^[1-9][0-9]*$' -and [int]$choice -ge 1 -and [int]$choice -le $envs.Count) {
        $selectedEnv = $envs[[int]$choice-1]
        $script:Environment = $selectedEnv
        # Clear the screen to show output clearly
        Clear-Host
        Write-Host "Environment set to: $selectedEnv" -ForegroundColor Green

        # Load configuration for selected environment
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-$($selectedEnv.ToLower()).json"
        if (Test-Path $configPath) {
            # Use the Import-Configuration function from main script
            $success = Import-Configuration -ConfigFile $configPath
            if ($success) {
                # Configuration loaded successfully
                Write-Host "Successfully loaded configuration for $selectedEnv." -ForegroundColor Green
            } else {
                Write-Host "Failed to load configuration for $selectedEnv." -ForegroundColor Red
            }
        } else {
            Write-Host "Configuration file not found for $($selectedEnv): $configPath" -ForegroundColor Red
            Write-Host "Creating a new configuration file..." -ForegroundColor Yellow
            # Try to create configuration
            if (Get-Command -Name New-DeploymentConfiguration -ErrorAction SilentlyContinue) {
                $created = New-DeploymentConfiguration -Environment $selectedEnv
                if ($created) {
                    Write-Host "Successfully created new configuration for $selectedEnv." -ForegroundColor Green
                } else {
                    Write-Host "Failed to create new configuration for $selectedEnv." -ForegroundColor Red
                }
            } else {
                Write-Host "Cannot create configuration - New-DeploymentConfiguration function not available." -ForegroundColor Red
            }
        }

        # Add a pause to view the output
        Write-Host ""
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null

        # Also log for record-keeping
        Write-Host "Environment set to: $selectedEnv" -ForegroundColor Green
    } else {
        Write-Host "Invalid selection. Please try again." -ForegroundColor Red
        Start-Sleep -Seconds 1
        Set-Environment
    }
}

# Export the main function
Export-ModuleMember -Function Set-Environment
