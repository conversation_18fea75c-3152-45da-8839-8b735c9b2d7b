/**
 * Git Repository List Module
 * Responsible for managing and rendering the repository list
 */
const GitRepositoryList = {
    // Module state
    state: {
        sortBy: 'name',
        sortDirection: 'asc',
        filterText: '',
        container: null,
        empty: false
    },
    
    // Reference to the manager module
    manager: null,
    
    /**
     * Initialize the repository list module
     * @param {Object} managerInstance - Reference to the GitRepositoryManager
     * @returns {Object} This module for chaining
     */
    init(managerInstance) {
        this.manager = managerInstance;
        this.createRepositoryListContainer();
        this.setupEventListeners();
        return this;
    },
    
    /**
     * Create the repository list container
     */
    createRepositoryListContainer() {
        // Get the parent container
        const gitRepoSection = document.getElementById('git-repo-section');
        if (!gitRepoSection) {
            console.error('Git repository section not found');
            return;
        }
        
        // Check if the container already exists
        const existingContainer = document.getElementById('repo-list-card');
        if (existingContainer) {
            console.log('Repository list container already exists, using existing one');
            this.state.container = existingContainer.querySelector('#repository-list');
            return;
        }
        
        // Create the container div for the repository list
        const container = document.createElement('div');
        container.className = 'card mt-3';
        container.id = 'repo-list-card';
        
        // Create the card header
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header d-flex justify-content-between align-items-center';
        cardHeader.innerHTML = `
            <h6 class="mb-0">Repositories</h6>
            <div class="repo-controls d-flex align-items-center">
                <div class="input-group input-group-sm me-2">
                    <input type="text" class="form-control" id="repo-filter" placeholder="Filter repositories...">
                    <button class="btn btn-outline-secondary" type="button" id="clear-filter">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-sort"></i> Sort
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                        <li><a class="dropdown-item sort-option" data-sort="name" data-direction="asc" href="#">Name (A-Z)</a></li>
                        <li><a class="dropdown-item sort-option" data-sort="name" data-direction="desc" href="#">Name (Z-A)</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item sort-option" data-sort="last_commit" data-direction="desc" href="#">Recently Updated</a></li>
                        <li><a class="dropdown-item sort-option" data-sort="last_commit" data-direction="asc" href="#">Least Recent Updates</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item sort-option" data-sort="size" data-direction="desc" href="#">Size (Largest First)</a></li>
                        <li><a class="dropdown-item sort-option" data-sort="size" data-direction="asc" href="#">Size (Smallest First)</a></li>
                    </ul>
                </div>
            </div>
        `;
        
        // Create the card body
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-0';
        
        // Create loading spinner container
        const loadingContainer = document.createElement('div');
        loadingContainer.id = 'repo-loading';
        loadingContainer.className = 'text-center p-4 d-flex justify-content-center align-items-center';
        loadingContainer.style.display = 'none';
        loadingContainer.innerHTML = `
            <div class="spinner-border text-primary me-2 spinning" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span class="ms-2">Loading repositories...</span>
        `;
        
        // Create empty state container
        const emptyContainer = document.createElement('div');
        emptyContainer.id = 'repo-empty';
        emptyContainer.className = 'text-center p-4';
        emptyContainer.style.display = 'none';
        emptyContainer.innerHTML = `
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <p class="mb-0">No repositories found</p>
            <p class="text-muted small">Try adjusting your filter criteria</p>
        `;
        
        // Create error container
        const errorContainer = document.createElement('div');
        errorContainer.id = 'repo-error';
        errorContainer.className = 'alert alert-danger m-3';
        errorContainer.style.display = 'none';
        
        // Create repository list container
        const repoList = document.createElement('div');
        repoList.className = 'list-group list-group-flush';
        repoList.id = 'repository-list';
        
        // Add all elements to the card body
        cardBody.appendChild(loadingContainer);
        cardBody.appendChild(errorContainer);
        cardBody.appendChild(emptyContainer);
        cardBody.appendChild(repoList);
        
        // Add header and body to the container
        container.appendChild(cardHeader);
        container.appendChild(cardBody);
        
        // Add the container to the parent
        gitRepoSection.appendChild(container);
        
        // Store reference to the repository list
        this.state.container = repoList;
    },
    
    /**
     * Set up event listeners for sorting and filtering
     */
    setupEventListeners() {
        // Add event listeners for sorting and filtering
        document.addEventListener('DOMContentLoaded', () => {
            // Filter input
            const filterInput = document.getElementById('repo-filter');
            if (filterInput) {
                filterInput.addEventListener('input', this.handleFilterChange.bind(this));
            }
            
            // Clear filter button
            const clearFilterBtn = document.getElementById('clear-filter');
            if (clearFilterBtn) {
                clearFilterBtn.addEventListener('click', this.handleClearFilter.bind(this));
            }
            
            // Sort options
            const sortOptions = document.querySelectorAll('.sort-option');
            sortOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    const sortBy = option.getAttribute('data-sort');
                    const sortDirection = option.getAttribute('data-direction');
                    this.handleSortChange(sortBy, sortDirection);
                });
            });
        });
    },
    
    /**
     * Handle filter text change
     * @param {Event} event - Input event
     */
    handleFilterChange(event) {
        const filterText = event.target.value.toLowerCase().trim();
        this.state.filterText = filterText;
        this.renderRepositoryList();
    },
    
    /**
     * Handle clear filter button click
     */
    handleClearFilter() {
        const filterInput = document.getElementById('repo-filter');
        if (filterInput) {
            filterInput.value = '';
            this.state.filterText = '';
            this.renderRepositoryList();
        }
    },
    
    /**
     * Handle sort option change
     * @param {string} sortBy - Field to sort by
     * @param {string} sortDirection - Sort direction (asc or desc)
     */
    handleSortChange(sortBy, sortDirection) {
        this.state.sortBy = sortBy;
        this.state.sortDirection = sortDirection;
        
        // Update dropdown button text
        const sortDropdown = document.getElementById('sortDropdown');
        if (sortDropdown) {
            let sortText = 'Sort';
            
            switch (sortBy) {
                case 'name':
                    sortText = `Name (${sortDirection === 'asc' ? 'A-Z' : 'Z-A'})`;
                    break;
                case 'last_commit':
                    sortText = sortDirection === 'desc' ? 'Recent Updates' : 'Oldest Updates';
                    break;
                case 'size':
                    sortText = `Size (${sortDirection === 'desc' ? 'Largest' : 'Smallest'})`;
                    break;
            }
            
            sortDropdown.innerHTML = `<i class="fas fa-sort"></i> ${sortText}`;
        }
        
        this.renderRepositoryList();
    },
    
    /**
     * Update display state
     * @param {string} state - State to display ('loading', 'error', 'empty', 'content')
     */
    updateDisplayState(state) {
        const loadingElement = document.getElementById('repo-loading');
        const errorElement = document.getElementById('repo-error');
        const emptyElement = document.getElementById('repo-empty');
        const contentElement = document.getElementById('repository-list');
        
        // Always hide the loading element regardless of state
        if (loadingElement) loadingElement.style.display = 'none';
        
        // Show the appropriate element based on state
        if (errorElement) errorElement.style.display = state === 'error' ? 'block' : 'none';
        if (emptyElement) emptyElement.style.display = state === 'empty' ? 'block' : 'none';
        if (contentElement) contentElement.style.display = state === 'content' ? 'block' : 'none';
    },
    
    /**
     * Filter repositories based on filter text
     * @param {Array} repositories - Array of repository objects
     * @returns {Array} Filtered repositories
     */
    filterRepositories(repositories) {
        if (!this.state.filterText) {
            return repositories;
        }
        
        return repositories.filter(repo => {
            const repoName = repo.name.toLowerCase();
            return repoName.includes(this.state.filterText);
        });
    },
    
    /**
     * Sort repositories based on sort criteria
     * @param {Array} repositories - Array of repository objects
     * @returns {Array} Sorted repositories
     */
    sortRepositories(repositories) {
        return [...repositories].sort((a, b) => {
            let result = 0;
            
            switch (this.state.sortBy) {
                case 'name':
                    result = a.name.localeCompare(b.name);
                    break;
                    
                case 'last_commit':
                    // Handle missing or invalid dates
                    if (!a.last_commit && !b.last_commit) result = 0;
                    else if (!a.last_commit) result = 1;
                    else if (!b.last_commit) result = -1;
                    else {
                        const dateA = new Date(a.last_commit);
                        const dateB = new Date(b.last_commit);
                        result = dateA - dateB;
                    }
                    break;
                    
                case 'size':
                    // Parse sizes to numbers for comparison
                    const sizeA = GitUtils.parseSize(a.size);
                    const sizeB = GitUtils.parseSize(b.size);
                    result = sizeA - sizeB;
                    break;
            }
            
            // Apply sort direction
            return this.state.sortDirection === 'asc' ? result : -result;
        });
    },
    
    /**
     * Render the repository list
     */
    renderRepositoryList() {
        console.log('Rendering repository list with status:', this.manager?.state?.status || 'unknown');
        
        // Safety check for manager
        if (!this.manager) {
            console.error('Repository manager not available');
            return;
        }
        
        // Skip loading spinner and go straight to either showing data or error
        // Handle error state
        if (this.manager.state.status === 'error') {
            console.log('Showing error state:', this.manager.state.error);
            const errorElement = document.getElementById('repo-error');
            if (errorElement) {
                errorElement.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading repositories: ${this.manager.state.error || 'Unknown error'}
                `;
            }
            this.updateDisplayState('error');
            return;
        }
        
        // Get repositories
        const repositories = this.manager.state.repositoriesData || [];
        console.log('Got repositories:', repositories.length);
        
        // Filter and sort repositories
        const filteredRepos = this.filterRepositories(repositories);
        const sortedRepos = this.sortRepositories(filteredRepos);
        console.log('Filtered and sorted repositories:', sortedRepos.length);
        
        // Show empty state if no repositories match filter
        if (sortedRepos.length === 0) {
            console.log('No repositories to display, showing empty state');
            this.updateDisplayState('empty');
            return;
        }
        
        // Get the repository list element
        const repoListElement = document.getElementById('repository-list');
        if (!repoListElement) {
            console.error('Repository list element not found');
            return;
        }
        
        // Clear existing list
        repoListElement.innerHTML = '';
        
        // Create repository list items
        sortedRepos.forEach(repo => {
            const repoItem = this.createRepositoryItem(repo);
            repoListElement.appendChild(repoItem);
        });
        
        // Show the list
        console.log('Showing repository list with', sortedRepos.length, 'items');
        this.updateDisplayState('content');
    },
    
    /**
     * Create a repository list item
     * @param {Object} repo - Repository object
     * @returns {HTMLElement} Repository list item element
     */
    createRepositoryItem(repo) {
        // Calculate repository health
        const health = repo.health_status ? {
            status: repo.health_status,
            message: repo.health_message || 'Repository health status',
            score: repo.health_score || 0
        } : GitUtils.calculateRepositoryHealth(repo);
        
        // Create list item
        const item = document.createElement('a');
        item.className = `list-group-item list-group-item-action d-flex justify-content-between align-items-center`;
        item.href = '#';
        item.setAttribute('data-repo', repo.name);
        
        // Left side - repository name and details
        const leftSide = document.createElement('div');
        leftSide.className = 'd-flex flex-column';
        
        // Repository name
        const repoName = document.createElement('div');
        repoName.className = 'fw-semibold';
        repoName.textContent = repo.name;
        
        // Repository details
        const repoDetails = document.createElement('div');
        repoDetails.className = 'small text-muted';
        
        // Format last commit date
        const lastCommitDate = repo.last_commit ? GitUtils.formatDate(repo.last_commit) : 'No commits';
        const branchInfo = repo.branches ? `${repo.branches} branch${repo.branches !== 1 ? 'es' : ''}` : 'Unknown branches';
        const activeBranch = repo.active_branch ? ` (${repo.active_branch})` : '';
        
        repoDetails.textContent = `Size: ${repo.size_formatted || 'Unknown'} | ${branchInfo}${activeBranch} | Last commit: ${lastCommitDate}`;
        
        leftSide.appendChild(repoName);
        leftSide.appendChild(repoDetails);
        
        // Right side - health indicator
        const rightSide = document.createElement('div');
        rightSide.className = 'health-indicator';
        
        const healthBadge = document.createElement('span');
        healthBadge.className = `badge health-${health.status}`;
        healthBadge.title = health.message;
        
        // Add health icon based on status
        let healthIcon = 'question-circle';
        switch (health.status) {
            case 'good':
                healthIcon = 'check-circle';
                break;
            case 'fair':
                healthIcon = 'info-circle';
                break;
            case 'poor':
                healthIcon = 'exclamation-circle';
                break;
        }
        
        healthBadge.innerHTML = `<i class="fas fa-${healthIcon}"></i>`;
        rightSide.appendChild(healthBadge);
        
        // Assemble the item
        item.appendChild(leftSide);
        item.appendChild(rightSide);
        
        // Add click event listener
        item.addEventListener('click', (e) => {
            e.preventDefault();
            this.manager.showRepositoryDetails(repo.name);
        });
        
        return item;
    }
};

// Expose the module globally
window.GitRepositoryList = GitRepositoryList;
