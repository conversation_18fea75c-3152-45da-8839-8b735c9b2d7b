from dataclasses import dataclass
from typing import Dict, List, Optional
from abc import ABC, abstractmethod
import re
from datetime import datetime, timedelta
import logging
from git_integration import GitIntegration
from templates import ProjectTemplate
import json
import numpy as np
from collections import defaultdict
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    complexity_score: float
    suggestions: List[str]
    risk_level: str
    affected_files: List[str]

class BaseAnalyzer(ABC):
    @abstractmethod
    def analyze(self) -> AnalysisResult:
        pass

class ComplexityAnalyzer(BaseAnalyzer):
    def __init__(self, config: Dict):
        self.config = config
        self.patterns = self._load_patterns()
    
    def analyze(self) -> AnalysisResult:
        try:
            score = self._calculate_complexity()
            suggestions = self._generate_suggestions(score)
            return AnalysisResult(
                complexity_score=score,
                suggestions=suggestions,
                risk_level=self._determine_risk_level(score),
                affected_files=self._identify_affected_files()
            )
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}", exc_info=True)
            raise AnalysisError(f"Failed to complete analysis: {str(e)}")

class AdvancedAutomation:
    def __init__(self, project_config: Dict, git_integration: GitIntegration):
        self.config = project_config
        self.git = git_integration
        self.setup_logging()
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.FileHandler('advanced_automation.log')]
        )

    def analyze_code_complexity(self, files: List[str]) -> Dict:
        """Analyze code complexity and suggest refactoring."""
        analysis = defaultdict(dict)
        
        complexity_patterns = {
            "nested_loops": r"for.*\s*for|while.*\s*while",
            "long_functions": r"def.*:.*\n(\s+.*\n){30,}",
            "many_parameters": r"def.*\((.*,){5,}.*\)",
            "deep_nesting": r"^\s{16,}",
            "complex_conditions": r"if.*and.*and.*or|if.*or.*or.*and"
        }
        
        for file in files:
            try:
                with open(file, 'r') as f:
                    content = f.read()
                    
                for pattern_name, pattern in complexity_patterns.items():
                    matches = len(re.findall(pattern, content, re.MULTILINE))
                    if matches > 0:
                        analysis[file][pattern_name] = matches
            except Exception as e:
                logging.error(f"Error analyzing {file}: {str(e)}")
        
        return dict(analysis)

    def detect_tech_debt(self, commits: List[Dict]) -> List[Dict]:
        """Detect potential technical debt from commit patterns."""
        debt_indicators = {
            "quick_fix": [r"quick fix", r"temporary", r"workaround", r"hack"],
            "postponed": [r"todo", r"fixme", r"xxx", r"revisit"],
            "complexity": [r"complex", r"complicated", r"refactor needed"],
            "performance": [r"slow", r"optimize later", r"performance issue"]
        }
        
        tech_debt = []
        for commit in commits:
            for debt_type, patterns in debt_indicators.items():
                if any(re.search(p, commit["message"], re.I) for p in patterns):
                    tech_debt.append({
                        "type": debt_type,
                        "commit": commit["hash"],
                        "message": commit["message"],
                        "date": commit["timestamp"],
                        "files": commit["affected_files"]
                    })
        
        return tech_debt

    def predict_high_risk_areas(self, history: List[Dict]) -> Dict:
        """Predict potentially risky areas based on historical patterns."""
        risk_factors = defaultdict(lambda: {
            "change_frequency": 0,
            "error_rate": 0,
            "complexity_score": 0,
            "security_issues": 0
        })
        
        for event in history:
            for file in event["affected_files"]:
                risk_factors[file]["change_frequency"] += 1
                if "fix" in event["message"].lower():
                    risk_factors[file]["error_rate"] += 1
                if "security" in event["message"].lower():
                    risk_factors[file]["security_issues"] += 1
        
        # Calculate composite risk scores
        risk_scores = {}
        for file, factors in risk_factors.items():
            score = (
                factors["change_frequency"] * 0.3 +
                factors["error_rate"] * 0.3 +
                factors["complexity_score"] * 0.2 +
                factors["security_issues"] * 0.2
            )
            risk_scores[file] = {
                "score": score,
                "factors": factors,
                "risk_level": "high" if score > 7 else "medium" if score > 4 else "low"
            }
        
        return risk_scores

    def generate_dependency_graph(self, files: List[str]) -> Dict:
        """Generate a dependency graph of project components."""
        dependencies = defaultdict(set)
        import_patterns = {
            "python": r"(?:from|import)\s+([\w.]+)",
            "cpp": r"#include\s+[<\"]([\w/.]+)[>\"]",
            "typescript": r"(?:import|export).*from\s+['\"](.+)['\"]"
        }
        
        for file in files:
            ext = file.split('.')[-1]
            pattern = None
            if ext in ['py']:
                pattern = import_patterns['python']
            elif ext in ['cpp', 'hpp', 'h']:
                pattern = import_patterns['cpp']
            elif ext in ['ts', 'tsx']:
                pattern = import_patterns['typescript']
            
            if pattern:
                try:
                    with open(file, 'r') as f:
                        content = f.read()
                        matches = re.findall(pattern, content)
                        dependencies[file].update(matches)
                except Exception as e:
                    logging.error(f"Error analyzing dependencies in {file}: {str(e)}")
        
        return dict(dependencies)

    def suggest_test_coverage(self, files: List[str]) -> List[Dict]:
        """Suggest areas needing better test coverage."""
        test_patterns = {
            "python": (r"test_.*\.py$", r".*_test\.py$"),
            "cpp": (r"test_.*\.cpp$", r".*_test\.cpp$"),
            "typescript": (r"test_.*\.ts$", r".*\.test\.ts$")
        }
        
        suggestions = []
        for file in files:
            ext = file.split('.')[-1]
            if ext in ['py']:
                patterns = test_patterns['python']
            elif ext in ['cpp']:
                patterns = test_patterns['cpp']
            elif ext in ['ts']:
                patterns = test_patterns['typescript']
            else:
                continue
            
            # Check if test file exists
            test_exists = any(
                any(re.match(p, tf) for p in patterns)
                for tf in files
            )
            
            if not test_exists:
                suggestions.append({
                    "file": file,
                    "suggestion": "Missing test file",
                    "priority": "high" if "security" in file or "auth" in file else "medium"
                })
        
        return suggestions

    def analyze_commit_patterns(self, commits: List[Dict]) -> Dict:
        """Analyze commit patterns to suggest process improvements."""
        analysis = {
            "commit_times": defaultdict(int),
            "commit_sizes": defaultdict(int),
            "common_fixes": defaultdict(int),
            "suggestions": []
        }
        
        for commit in commits:
            # Analyze commit timing
            hour = commit["timestamp"].hour
            analysis["commit_times"][hour] += 1
            
            # Analyze commit size
            size = len(commit["affected_files"])
            if size > 10:
                category = "large"
            elif size > 5:
                category = "medium"
            else:
                category = "small"
            analysis["commit_sizes"][category] += 1
            
            # Analyze common fixes
            if "fix" in commit["message"].lower():
                component = re.search(r"fix\((.*?)\)", commit["message"].lower())
                if component:
                    analysis["common_fixes"][component.group(1)] += 1
        
        # Generate suggestions
        if max(analysis["commit_times"].values()) > 5:
            analysis["suggestions"].append({
                "type": "workflow",
                "message": "Consider more evenly distributed commit patterns"
            })
        
        if analysis["commit_sizes"]["large"] > analysis["commit_sizes"]["small"]:
            analysis["suggestions"].append({
                "type": "practice",
                "message": "Consider breaking down large commits into smaller ones"
            })
        
        return dict(analysis)

    def suggest_code_improvements(self, complexity: Dict, risk_scores: Dict) -> List[Dict]:
        """Generate specific code improvement suggestions."""
        suggestions = []
        
        # Suggest improvements based on complexity
        for file, issues in complexity.items():
            if "nested_loops" in issues and issues["nested_loops"] > 2:
                suggestions.append({
                    "file": file,
                    "type": "refactor",
                    "message": "Consider extracting nested loops into separate functions",
                    "priority": "high"
                })
            if "long_functions" in issues:
                suggestions.append({
                    "file": file,
                    "type": "refactor",
                    "message": "Break down long functions into smaller, focused ones",
                    "priority": "medium"
                })
        
        # Suggest improvements based on risk scores
        for file, risk in risk_scores.items():
            if risk["risk_level"] == "high":
                suggestions.append({
                    "file": file,
                    "type": "quality",
                    "message": f"High risk file needs review: {risk['factors']}",
                    "priority": "high"
                })
        
        return suggestions

    def generate_sprint_insights(self, commits: List[Dict], days: int = 14) -> Dict:
        """Generate insights for sprint planning."""
        cutoff = datetime.now() - timedelta(days=days)
        recent_commits = [c for c in commits if c["timestamp"] > cutoff]
        
        insights = {
            "velocity": len(recent_commits) / days,
            "focus_areas": defaultdict(int),
            "bottlenecks": [],
            "suggestions": []
        }
        
        # Analyze focus areas
        for commit in recent_commits:
            category = commit.get("category", "unknown")
            insights["focus_areas"][category] += 1
        
        # Identify bottlenecks
        file_changes = defaultdict(int)
        for commit in recent_commits:
            for file in commit["affected_files"]:
                file_changes[file] += 1
        
        # Files changed too frequently might indicate design issues
        for file, changes in file_changes.items():
            if changes > days * 0.5:  # Changed more than every other day
                insights["bottlenecks"].append({
                    "file": file,
                    "changes": changes,
                    "suggestion": "Consider reviewing design/architecture"
                })
        
        # Generate planning suggestions
        top_focus = max(insights["focus_areas"].items(), key=lambda x: x[1])[0]
        insights["suggestions"].append({
            "type": "planning",
            "message": f"High activity in {top_focus} - consider dedicated sprint"
        })
        
        if insights["velocity"] < 1:
            insights["suggestions"].append({
                "type": "process",
                "message": "Lower velocity detected - review process bottlenecks"
            })
        
        return dict(insights)

class GitAnalytics:
    def __init__(self, git_client, config):
        self.git = git_client
        self.config = config
        self.setup_logging()

    def setup_logging(self):
        """Configure structured logging with correlation IDs"""
        logging.basicConfig(
            format='%(asctime)s - %(name)s - %(levelname)s - %(correlation_id)s - %(message)s',
            level=self.config.get('LOG_LEVEL', logging.INFO)
        )

    @contextmanager
    def error_boundary(self, operation: str):
        """Context manager for consistent error handling"""
        try:
            yield
        except Exception as e:
            logger.error(f"Error in {operation}: {str(e)}", exc_info=True)
            raise GitAnalyticsError(f"Failed to execute {operation}: {str(e)}")

    def analyze_git_patterns(self, days: int = 90) -> Dict:
        """Analyze git patterns with proper error handling and validation"""
        with self.error_boundary("git_pattern_analysis"):
            if not isinstance(days, int) or days <= 0:
                raise ValueError("Days must be a positive integer")

            analysis = {
                "commit_patterns": defaultdict(int),
                "high_risk_files": set(),  # Using set for deduplication
                "suggested_reviews": [],
                "optimization_opportunities": [],
                "analysis_timestamp": datetime.utcnow().isoformat()
            }

            since_date = datetime.now() - timedelta(days=days)
            commits = self.git.get_latest_commits(since=since_date)
            
            if not commits:
                logger.warning("No commits found for analysis period")
                return analysis

            self._process_commits(commits, analysis)
            return dict(analysis)  # Convert defaultdict to regular dict

    def _process_commits(self, commits: list, analysis: Dict) -> None:
        """Process commits with batching for performance"""
        BATCH_SIZE = 100
        
        for i in range(0, len(commits), BATCH_SIZE):
            batch = commits[i:i + BATCH_SIZE]
            try:
                self._process_commit_batch(batch, analysis)
            except Exception as e:
                logger.error(f"Error processing commit batch {i}-{i+BATCH_SIZE}: {str(e)}")
                continue

class GitAnalyticsError(Exception):
    """Custom exception for Git analytics operations"""
    pass
