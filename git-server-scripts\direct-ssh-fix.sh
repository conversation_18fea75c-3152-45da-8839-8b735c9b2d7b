#!/bin/bash

# Direct SSH Fix Script for Git User
# Run this script directly on the server as root

# Colors for output
RED="\033[0;31m"
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}=== Direct SSH Fix for Git User ===${NC}"

# Check if running as root
if [ $(id -u) -ne 0 ]; then
    echo -e "${RED}This script must be run as root${NC}"
    exit 1
fi

# Check git user existence
echo -e "${YELLOW}Checking if git user exists...${NC}"
if id "git" &>/dev/null; then
    echo -e "${GREEN}Git user exists.${NC}"
    GIT_HOME=$(getent passwd git | cut -d: -f6)
    echo -e "${BLUE}Git user home directory: ${GIT_HOME}${NC}"
else
    echo -e "${YELLOW}Git user does not exist. Creating git user...${NC}"
    adduser --system --shell /bin/bash --gecos 'Git Version Control' --group --disabled-password --home /home/<USER>
    GIT_HOME="/home/<USER>"
    echo -e "${GREEN}Git user created with home directory: ${GIT_HOME}${NC}"
fi

# Create .ssh directory if it doesn't exist
echo -e "${YELLOW}Setting up .ssh directory for git user...${NC}"
mkdir -p "${GIT_HOME}/.ssh"
chmod 700 "${GIT_HOME}/.ssh"
echo -e "${GREEN}.ssh directory set up.${NC}"

# Get the public key from btaylor-admin user
echo -e "${YELLOW}Getting public key from btaylor-admin user...${NC}"
ADMIN_HOME=$(getent passwd btaylor-admin | cut -d: -f6)

if [ -f "${ADMIN_HOME}/.ssh/id_rsa.pub" ]; then
    echo -e "${GREEN}Found public key in btaylor-admin's .ssh directory.${NC}"
    cp "${ADMIN_HOME}/.ssh/id_rsa.pub" /tmp/admin_key.pub
    PUBLIC_KEY=$(cat /tmp/admin_key.pub)
    rm /tmp/admin_key.pub
elif [ -f "${ADMIN_HOME}/.ssh/authorized_keys" ]; then
    echo -e "${YELLOW}Using first key from btaylor-admin's authorized_keys file.${NC}"
    PUBLIC_KEY=$(head -n 1 "${ADMIN_HOME}/.ssh/authorized_keys")
else
    echo -e "${RED}No SSH keys found for btaylor-admin user!${NC}"
    echo -e "${YELLOW}Please manually add your public key to the script.${NC}"
    exit 1
fi

# Add SSH key to authorized_keys
echo -e "${YELLOW}Adding SSH key to git user's authorized_keys...${NC}"
echo "${PUBLIC_KEY}" > "${GIT_HOME}/.ssh/authorized_keys"
chmod 600 "${GIT_HOME}/.ssh/authorized_keys"
chown -R git:git "${GIT_HOME}/.ssh"
echo -e "${GREEN}SSH key added to authorized_keys.${NC}"

# Verify SSH configuration
echo -e "${YELLOW}Verifying SSH configuration...${NC}"
if [ -f "/etc/ssh/sshd_config" ]; then
    # Check if public key authentication is enabled
    if grep -q "^PubkeyAuthentication yes" "/etc/ssh/sshd_config"; then
        echo -e "${GREEN}Public key authentication is already enabled.${NC}"
    else
        echo -e "${YELLOW}Enabling public key authentication in SSH config...${NC}"
        sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
        systemctl restart sshd
        echo -e "${GREEN}Public key authentication enabled and SSH service restarted.${NC}"
    fi
else
    echo -e "${RED}SSH config file not found!${NC}"
    exit 1
fi

# Set up git repository if it doesn't exist
echo -e "${YELLOW}Setting up git repository...${NC}"
REPO_PATH="${GIT_HOME}/project-tracker.git"

if [ ! -d "$REPO_PATH" ]; then
    echo -e "${YELLOW}Creating git repository...${NC}"
    mkdir -p "$REPO_PATH"
    cd "$REPO_PATH"
    git init --bare
    echo -e "${GREEN}Git repository created.${NC}"
else
    echo -e "${GREEN}Git repository already exists.${NC}"
fi

# Set proper permissions for the repository
echo -e "${YELLOW}Setting proper permissions for the repository...${NC}"
chown -R git:git "$REPO_PATH"
chmod -R 775 "$REPO_PATH"
echo -e "${GREEN}Repository permissions set.${NC}"

# Final verification
echo -e "${YELLOW}Performing final verification...${NC}"
if [ -f "${GIT_HOME}/.ssh/authorized_keys" ]; then
    KEYS_COUNT=$(grep -c '^ssh-' "${GIT_HOME}/.ssh/authorized_keys")
    echo -e "${GREEN}Git user has ${KEYS_COUNT} SSH keys in authorized_keys file.${NC}"
    
    # Check permissions
    SSH_DIR_PERMS=$(stat -c "%a" "${GIT_HOME}/.ssh")
    AUTH_KEYS_PERMS=$(stat -c "%a" "${GIT_HOME}/.ssh/authorized_keys")
    
    echo -e "${BLUE}.ssh directory permissions: ${SSH_DIR_PERMS} (should be 700)${NC}"
    echo -e "${BLUE}authorized_keys permissions: ${AUTH_KEYS_PERMS} (should be 600)${NC}"
    
    if [ "$SSH_DIR_PERMS" != "700" ] || [ "$AUTH_KEYS_PERMS" != "600" ]; then
        echo -e "${YELLOW}Warning: Permissions are not set correctly. Fixing...${NC}"
        chmod 700 "${GIT_HOME}/.ssh"
        chmod 600 "${GIT_HOME}/.ssh/authorized_keys"
        chown -R git:git "${GIT_HOME}/.ssh"
        echo -e "${GREEN}Permissions fixed.${NC}"
    else
        echo -e "${GREEN}All permissions are set correctly.${NC}"
    fi
else
    echo -e "${RED}No authorized_keys file found for git user after setup!${NC}"
    exit 1
fi

# Set proper permissions for git home directory
echo -e "${YELLOW}Setting proper permissions for git home directory...${NC}"
chmod 755 "${GIT_HOME}"
chown git:git "${GIT_HOME}"
echo -e "${GREEN}Git home directory permissions set.${NC}"

# Add btaylor-admin to git group
echo -e "${YELLOW}Adding btaylor-admin to git group...${NC}"
usermod -a -G git btaylor-admin
echo -e "${GREEN}btaylor-admin added to git group.${NC}"

# Test SSH connection locally
echo -e "${YELLOW}Testing SSH connection locally...${NC}"
su - btaylor-admin -c "ssh -o BatchMode=yes -o StrictHostKeyChecking=no git@localhost 'echo CONNECTION_TEST'"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Local SSH connection successful!${NC}"
else
    echo -e "${RED}Local SSH connection failed. Please check the setup.${NC}"
fi

echo -e "${GREEN}SSH key setup completed successfully!${NC}"
echo -e "${YELLOW}You can now use the git user for SSH connections.${NC}"
echo -e "${YELLOW}Test from your client machine with: ssh -o BatchMode=yes git@SERVER_IP 'echo CONNECTION_OK'${NC}"
