# Update Server Configuration Script

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Load the current configuration
$configDir = "$PSScriptRoot\..\config"
$configFile = Get-ChildItem -Path $configDir -Filter "database-service-*.json" | Select-Object -First 1

if ($null -eq $configFile) {
    Write-Host "No database-service configuration found." -ForegroundColor Red
    exit
}

Write-Host "Loading configuration from $($configFile.FullName)..." -ForegroundColor Yellow

try {
    $config = Get-Content -Path $configFile.FullName -Raw | ConvertFrom-Json
    
    # Check if the server needs to be updated
    if ($config.ssh.host -eq "project-tracker.chcit.org") {
        Write-Host "Updating server from project-tracker.chcit.org to git.chcit.org..." -ForegroundColor Yellow
        $config.ssh.host = "git.chcit.org"
        
        # Add deployment_scripts if it doesn't exist
        if (-not (Get-Member -InputObject $config.project -Name "deployment_scripts" -MemberType Properties)) {
            $config.project | Add-Member -MemberType NoteProperty -Name "deployment_scripts" -Value "D:\Augment\project-tracker\database-service-scripts\scripts\deployment"
        }
        
        # Fix source directory if needed
        if ($config.project.source_dir -like "*\config" -or $config.project.source_dir -like "*\deployment*") {
            $config.project.source_dir = "D:\Augment\project-tracker\database-service"
        }
        
        # Save the updated configuration
        $config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile.FullName -Force
        Write-Host "Configuration updated successfully." -ForegroundColor Green
    } else {
        Write-Host "Server is already set to $($config.ssh.host). No update needed." -ForegroundColor Green
    }
} catch {
    Write-Host "Error updating configuration: $_" -ForegroundColor Red
}

# Return to main menu
Show-MainMenu
