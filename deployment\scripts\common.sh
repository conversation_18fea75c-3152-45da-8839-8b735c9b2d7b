#!/bin/bash
#
# Project Tracker Common Functions
# Version: 2.0.0
# Last Updated: 2025-03-04
# Author: CHCIT DevOps Team

# Logging function
log() {
    local level=$1
    shift
    echo "[$(date +'%Y-%m-%d %H:%M:%S')][$level] $*"
}

# Progress tracking function
track_progress() {
    local stage=$1
    local message=$2
    log "INFO" "[$stage] $message"
}

# Error handling function
handle_error() {
    local error_message=$1
    log "ERROR" "$error_message"
    exit 1
}

# Check if running as root
check_root() {
    if [ "$(id -u)" != "0" ]; then
        handle_error "This script must be run as root"
    fi
}

# Validate file permissions
check_file_permissions() {
    local file=$1
    local perms=$2
    if [ -f "$file" ]; then
        current_perms=$(stat -c %a "$file")
        if [ "$current_perms" != "$perms" ]; then
            log "WARN" "Incorrect permissions on $file. Expected: $perms, Found: $current_perms"
            chmod "$perms" "$file"
        fi
    fi
}

# Check command availability
check_command() {
    local cmd=$1
    if ! command -v "$cmd" >/dev/null 2>&1; then
        handle_error "Required command not found: $cmd"
    fi
}
