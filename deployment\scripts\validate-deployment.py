#!/usr/bin/env python3
"""
Project Tracker Ubuntu Server Deployment Validator
Version: 1.0.0

Validates the deployment state of Project Tracker on the Ubuntu server.
This script is intended to run on the target Ubuntu server, not on the Windows build server.
Combines environment and installation validation checks.

Requirements:
- Ubuntu 24.04 LTS
- Python 3.12+
- Root/sudo access for service checks
"""

import sys
import os
import subprocess
import socket
import psutil
import json
from typing import Dict, List, Tuple

class DeploymentValidator:
    def __init__(self):
        self.results = {
            "python": {"status": "pending", "details": {}},
            "system": {"status": "pending", "details": {}},
            "services": {"status": "pending", "details": {}},
            "network": {"status": "pending", "details": {}},
            "dependencies": {"status": "pending", "details": {}},
            "cdn": {"status": "pending", "details": {}}
        }
        self.critical_services = [
            "project-tracker", 
            "nginx", 
            "postgresql", 
            "redis-server"
        ]

    def check_python_environment(self) -> None:
        """Validate Python installation and environment"""
        try:
            self.results["python"]["details"]["version"] = sys.version
            self.results["python"]["details"]["executable"] = sys.executable
            self.results["python"]["details"]["prefix"] = sys.prefix
            
            # Check Python version
            version = sys.version_info
            version_valid = version.major == 3 and version.minor == 12
            
            # Check if running in virtualenv
            in_venv = sys.prefix != sys.base_prefix
            self.results["python"]["details"]["virtualenv"] = in_venv
            
            if not version_valid:
                self.results["python"]["status"] = "error"
                self.results["python"]["details"]["message"] = f"Invalid Python version. Required 3.12, found {version.major}.{version.minor}"
            elif not in_venv:
                self.results["python"]["status"] = "warning"
                self.results["python"]["details"]["message"] = "Not running in a virtual environment"
            else:
                self.results["python"]["status"] = "ok"
        except Exception as e:
            self.results["python"]["status"] = "error"
            self.results["python"]["details"]["error"] = str(e)

    def check_system_resources(self) -> None:
        """Check system resources and configurations"""
        try:
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.results["system"]["details"]["cpu_usage"] = f"{cpu_percent}%"
            
            # Check memory
            memory = psutil.virtual_memory()
            self.results["system"]["details"]["memory_total"] = f"{memory.total / (1024**3):.2f} GB"
            self.results["system"]["details"]["memory_available"] = f"{memory.available / (1024**3):.2f} GB"
            
            # Check disk space
            disk = psutil.disk_usage('/')
            self.results["system"]["details"]["disk_total"] = f"{disk.total / (1024**3):.2f} GB"
            self.results["system"]["details"]["disk_free"] = f"{disk.free / (1024**3):.2f} GB"
            
            self.results["system"]["status"] = "ok"
        except Exception as e:
            self.results["system"]["status"] = "error"
            self.results["system"]["details"]["error"] = str(e)

    def check_critical_services(self) -> None:
        """Check status of critical services"""
        try:
            service_statuses = {}
            for service in self.critical_services:
                try:
                    result = subprocess.run(
                        ['systemctl', 'is-active', service], 
                        capture_output=True, 
                        text=True
                    )
                    service_statuses[service] = result.stdout.strip() == 'active'
                except Exception as e:
                    service_statuses[service] = False
            
            self.results["services"]["details"] = service_statuses
            
            # Check if all services are running
            all_services_running = all(service_statuses.values())
            self.results["services"]["status"] = "ok" if all_services_running else "error"
        except Exception as e:
            self.results["services"]["status"] = "error"
            self.results["services"]["details"]["error"] = str(e)

    def check_network_ports(self) -> None:
        """Check if required network ports are open"""
        try:
            required_ports = {
                "HTTP": 80,
                "HTTPS": 443,
                "PostgreSQL": 5432
            }
            port_statuses = {}
            
            for name, port in required_ports.items():
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex(('localhost', port))
                port_statuses[name] = result == 0
                sock.close()
            
            self.results["network"]["details"] = port_statuses
            self.results["network"]["status"] = "ok" if all(port_statuses.values()) else "warning"
        except Exception as e:
            self.results["network"]["status"] = "error"
            self.results["network"]["details"]["error"] = str(e)

    def check_cdn_configuration(self) -> None:
        """Validate CDN configuration and performance"""
        try:
            cdn_checks = {}
            
            # Check Brotli module
            try:
                result = subprocess.run(
                    ['nginx', '-V'],
                    capture_output=True,
                    text=True,
                    stderr=subprocess.STDOUT
                )
                cdn_checks["brotli_module"] = "ngx_brotli" in result.stdout
            except Exception:
                cdn_checks["brotli_module"] = False
            
            # Check CDN directories
            cdn_dirs = [
                "/opt/project-tracker/cdn/cache",
                "/var/log/project-tracker/cdn"
            ]
            for dir_path in cdn_dirs:
                cdn_checks[f"directory_{os.path.basename(dir_path)}"] = os.path.isdir(dir_path)
            
            # Check CDN monitoring script
            monitor_script = "/opt/project-tracker/scripts/monitor-cdn.sh"
            cdn_checks["monitor_script"] = os.path.isfile(monitor_script) and os.access(monitor_script, os.X_OK)
            
            # Check CDN cron job
            try:
                with open("/etc/cron.d/project-tracker-cdn", "r") as f:
                    cron_content = f.read()
                cdn_checks["cron_job"] = "monitor-cdn.sh" in cron_content
            except Exception:
                cdn_checks["cron_job"] = False
            
            # Check logrotate configuration
            try:
                with open("/etc/logrotate.d/project-tracker-cdn", "r") as f:
                    logrotate_content = f.read()
                cdn_checks["logrotate"] = "project-tracker/cdn" in logrotate_content
            except Exception:
                cdn_checks["logrotate"] = False
            
            # Check nginx configuration for CDN settings
            try:
                with open("/etc/nginx/sites-enabled/project-tracker", "r") as f:
                    nginx_conf = f.read()
                cdn_checks["nginx_cdn_config"] = all(
                    setting in nginx_conf
                    for setting in [
                        "brotli on",
                        "add_header Cache-Control",
                        "expires max"
                    ]
                )
            except Exception:
                cdn_checks["nginx_cdn_config"] = False
            
            self.results["cdn"]["details"] = cdn_checks
            self.results["cdn"]["status"] = "ok" if all(cdn_checks.values()) else "error"
            
        except Exception as e:
            self.results["cdn"]["status"] = "error"
            self.results["cdn"]["details"]["error"] = str(e)

    def validate_deployment(self) -> Dict:
        """Run all validation checks"""
        self.check_python_environment()
        self.check_system_resources()
        self.check_critical_services()
        self.check_network_ports()
        self.check_cdn_configuration()
        
        return self.results

    def print_report(self) -> None:
        """Print a human-readable validation report"""
        print("\n--- Project Tracker Deployment Validation Report ---")
        for category, data in self.results.items():
            print(f"\n{category.upper()} Status: {data['status']}")
            if data.get('details'):
                for key, value in data['details'].items():
                    print(f"  {key}: {value}")

def main():
    validator = DeploymentValidator()
    results = validator.validate_deployment()
    validator.print_report()
    
    # Exit with non-zero status if any critical checks fail
    sys.exit(0 if all(
        category['status'] in ['ok', 'warning'] 
        for category in results.values()
    ) else 1)

if __name__ == "__main__":
    main()
