// cert_sync_helper.cpp
// A setuid helper program to safely run the certificate sync script

#include <iostream>
#include <string>
#include <array>
#include <memory>
#include <stdexcept>
#include <cstring>
#include <cerrno>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <grp.h>
#include <syslog.h>

// Path to the certificate sync script (constant and not modifiable)
constexpr const char* SYNC_SCRIPT = "/opt/git-dashboard/sync-certificates.sh";

// Log a message to syslog and stdout
void log_message(const std::string& message, bool is_error = false) {
    int priority = is_error ? LOG_ERR : LOG_INFO;
    syslog(priority, "%s", message.c_str());
    if (is_error) {
        std::cerr << "ERROR: " << message << std::endl;
    } else {
        std::cout << message << std::endl;
    }
}

// Execute the sync script and capture output
std::string exec_sync_script() {
    std::array<char, 4096> buffer;
    std::string result;
    
    // Open pipe to command
    std::unique_ptr<FILE, decltype(&pclose)> pipe(
        popen(SYNC_SCRIPT, "r"), 
        pclose
    );
    
    if (!pipe) {
        throw std::runtime_error("popen() failed: " + std::string(strerror(errno)));
    }
    
    // Read command output
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    
    return result;
}

// Check if the user is in the www-data group
bool is_in_www_data_group() {
    // Get user info
    uid_t uid = geteuid();
    struct passwd *pw = getpwuid(uid);
    if (!pw) {
        log_message("Failed to get user info", true);
        return false;
    }
    
    // Get www-data group info
    struct group *gr = getgrnam("www-data");
    if (!gr) {
        log_message("Failed to get www-data group info", true);
        return false;
    }
    
    // Check if user is in www-data group
    for (char **members = gr->gr_mem; *members != nullptr; members++) {
        if (strcmp(*members, pw->pw_name) == 0) {
            return true;
        }
    }
    
    // Also check if user's primary group is www-data
    return pw->pw_gid == gr->gr_gid;
}

int main() {
    try {
        // Set up syslog
        openlog("cert_sync_helper", LOG_PID | LOG_PERROR, LOG_DAEMON);
        
        // Security check: only allow www-data group to run this
        if (!is_in_www_data_group()) {
            log_message("Access denied: Only www-data group can run this helper", true);
            return 1;
        }
        
        // Log the execution attempt
        log_message("Certificate sync helper started by uid=" + std::to_string(getuid()));
        
        // Execute the sync script
        std::string output = exec_sync_script();
        
        // Send the output as JSON response
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"success\",\n";
        std::cout << "  \"message\": \"Certificate sync completed\",\n";
        std::cout << "  \"output\": \"" << output << "\"\n";
        std::cout << "}\n";
        
        log_message("Certificate sync completed successfully");
        return 0;
    } 
    catch (const std::exception& e) {
        log_message(std::string("Error: ") + e.what(), true);
        
        // Send error as JSON
        std::cout << "Content-Type: application/json\n\n";
        std::cout << "{\n";
        std::cout << "  \"status\": \"error\",\n";
        std::cout << "  \"message\": \"" << e.what() << "\"\n";
        std::cout << "}\n";
        
        return 1;
    }
}