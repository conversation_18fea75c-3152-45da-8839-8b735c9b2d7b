Import-Module "$PSScriptRoot/../Common.psm1" -Force

function Invoke-DeployUI {
    [CmdletBinding()]
    param(
        [string]$BuildOutputDir = "D:\Augment\project-tracker\database-service-ui\build",
        # Always use new UI deployment path
        [string]$RemoteTempDir = "/tmp/database-service-ui-deploy",
        [string]$RemoteWebRoot = "/opt/database-service-ui",
        [string]$NginxLocalConfName = "database-service-ui.conf", # Name of the nginx config file
        [string]$HostName,
        [string]$User,
        [string]$KeyPath,
        [int]$Port = 22
    )
    Write-Host "[Deploy-UI] Deploying UI to Ubuntu server via SCP/SSH" -ForegroundColor Cyan

    # Auto-load config if needed
    if (-not $HostName -or -not $User -or -not $KeyPath) {
        if ($null -eq $script:Config) {
            $configPath = Join-Path -Path $PSScriptRoot -ChildPath "../config/database-service-development.json"
            if (Test-Path $configPath) {
                $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
                $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            }
        }
        if ($null -ne $script:Config -and $null -ne $script:Config.ssh) {
            if (-not $HostName) { $HostName = $script:Config.ssh.host }
            if (-not $User)    { $User    = $script:Config.ssh.username }
            if (-not $Port -or $Port -eq 22) { if ($script:Config.ssh.port) { $Port = $script:Config.ssh.port } }
            if (-not $KeyPath) { $KeyPath = $script:Config.ssh.local_key_path }
        }
    }

    if (-Not (Test-Path $BuildOutputDir)) {
        Write-Host "[Deploy-UI] Build output directory not found: $BuildOutputDir" -ForegroundColor Red
        Wait-ForUser
            return $false
    }
    if (-not $HostName -or -not $User -or -not $KeyPath) {
        Write-Host "[Deploy-UI] SSH deployment config missing (hostname, user, key)" -ForegroundColor Red
        Wait-ForUser
            return $false
    }
    try {
        # 1. Use scp to copy all UI files to remote temp dir
        Write-Host "[Deploy-UI] Copying UI files to ${User}@${HostName}:${RemoteTempDir} ..." -ForegroundColor Yellow
        $scpCmd = "scp -i `"${KeyPath}`" -P ${Port} -r `"${BuildOutputDir}/*`" ${User}@${HostName}:${RemoteTempDir}/"
        Write-Host "[Deploy-UI] Running: $scpCmd" -ForegroundColor Gray
        $scpResult = Invoke-Expression $scpCmd # Using Invoke-Expression for consistency
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[Deploy-UI] SCP of build artifacts failed: $scpResult" -ForegroundColor Red
            Wait-ForUser
            return $false
        }
        Write-Host "[Deploy-UI] Build artifacts copied to remote temp dir." -ForegroundColor Green

        # Determine Nginx local config path (assuming it's in the parent of BuildOutputDir)
        $NginxLocalConfPath = Join-Path (Get-Item $BuildOutputDir).Parent.FullName $NginxLocalConfName
        if (-Not (Test-Path $NginxLocalConfPath)) {
            Write-Host "[Deploy-UI] Nginx config file not found: $NginxLocalConfPath" -ForegroundColor Red
            Wait-ForUser
            return $false # Or decide if this is a non-fatal error
        }

        Write-Host "[Deploy-UI] Copying Nginx config ${NginxLocalConfPath} to ${User}@${HostName}:${RemoteTempDir}/${NginxLocalConfName} ..." -ForegroundColor Yellow
        $scpNginxCmd = "scp -i `"${KeyPath}`" -P ${Port} `"${NginxLocalConfPath}`" ${User}@${HostName}:${RemoteTempDir}/${NginxLocalConfName}"
        Write-Host "[Deploy-UI] Running: $scpNginxCmd" -ForegroundColor Gray
        $scpNginxResult = Invoke-Expression $scpNginxCmd
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[Deploy-UI] SCP of Nginx config failed: $scpNginxResult" -ForegroundColor Red
            Wait-ForUser
            return $false
        }
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[Deploy-UI] SCP failed: $scpResult" -ForegroundColor Red
            Wait-ForUser
            return $false
        }
        Write-Host "[Deploy-UI] Files copied to remote temp dir." -ForegroundColor Green

        # 2. Backup, deploy files, deploy Nginx config, and reload nginx/service
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupDir = "${RemoteWebRoot}-backup-$timestamp"

        # Define the first command using the format operator for clarity and correct escaping
        $backupCommand = 'if [ -d {0} ] && [ -n "$(ls -A {0})" ]; then sudo cp -rp {0} {1}; fi' -f $RemoteWebRoot, $backupDir

        $remoteCmd = @(
            $backupCommand, # Use the formatted command
            "sudo mkdir -p ${RemoteWebRoot}",
            "sudo rm -rf ${RemoteWebRoot}/*",
            "sudo cp -r ${RemoteTempDir}/* ${RemoteWebRoot}/",
            "sudo chown -R www-data:www-data ${RemoteWebRoot}",
            # Move Nginx config from temp to final location and test
            "if [ -f ${RemoteTempDir}/${NginxLocalConfName} ]; then sudo mv ${RemoteTempDir}/${NginxLocalConfName} /etc/nginx/conf.d/${NginxLocalConfName}; fi",
            "sudo nginx -t",
            "sudo systemctl reload nginx"
        ) -join " && "
        Write-Host "[Deploy-UI] Remote command to be executed:" -ForegroundColor Gray
        Write-Host $remoteCmd -ForegroundColor Gray
        Write-Host "[Deploy-UI] Running remote commands to finalize deployment..." -ForegroundColor Yellow
        Import-Module "$PSScriptRoot/SSHManager.psm1" -Force

        $result = Invoke-SSHCommand -Command $remoteCmd -HostName $HostName -User $User -KeyPath $KeyPath -Port $Port
        Write-Host "[Deploy-UI] Remote command output:" -ForegroundColor Cyan
        Write-Host $result.Output -ForegroundColor Cyan
        if ($result.Error) {
            Write-Host "[Deploy-UI] Remote command error:" -ForegroundColor Red
            Write-Host $result.Error -ForegroundColor Red
        }

        if ($result.Success) {
            Write-Host "[Deploy-UI] Remote deployment steps completed." -ForegroundColor Green
            Wait-ForUser
            return $true
        } else {
            Write-Host "[Deploy-UI] Remote command failed: $($result.Error)" -ForegroundColor Red
            Wait-ForUser
            return $false
        }
    } catch {
        Write-Host "[Deploy-UI] Error during remote UI deployment: $_" -ForegroundColor Red
        Wait-ForUser
            return $false
    }
}

Export-ModuleMember -Function Invoke-DeployUI
