# Git Dashboard Deployment Checklist

## File & Directory Permissions

- [ ] Verify `/opt/git-dashboard` directory is owned by www-data:www-data
  ```bash
  sudo chown -R www-data:www-data /opt/git-dashboard
  ```

- [ ] Ensure all application files have correct permissions
  ```bash
  sudo find /opt/git-dashboard -type f -exec chmod 644 {} \;
  sudo find /opt/git-dashboard -type d -exec chmod 755 {} \;
  ```

- [ ] Set executable permissions on script files
  ```bash
  sudo chmod +x /opt/git-dashboard/*.sh
  ```

- [ ] Verify metrics data file is readable/writable
  ```bash
  sudo chmod 664 /opt/git-dashboard/data/metrics_history.json
  ```

- [ ] Check SSL certificate access permissions
  ```bash
  # Ensure the www-data user can access certificates
  sudo usermod -a -G cert-access www-data
  sudo chgrp cert-access /etc/letsencrypt/live/chcit.org/privkey.pem
  sudo chmod 640 /etc/letsencrypt/live/chcit.org/privkey.pem
  ```

## Configuration Files

- [ ] Verify systemd service configuration
  ```bash
  sudo systemctl cat git-dashboard.service
  # Ensure User=www-data and Group=www-data are set
  ```

- [ ] Check Nginx configuration
  ```bash
  sudo nginx -t
  ```

- [ ] Ensure sudoers entry exists for certificate syncing
  ```bash
  sudo grep "www-data" /etc/sudoers /etc/sudoers.d/*
  # Should contain: www-data ALL=(ALL) NOPASSWD: /opt/git-dashboard/sync-certificates.sh
  ```

## Application Setup

- [ ] Verify Python virtual environment (if used)
  ```bash
  ls -la /opt/git-dashboard/venv/
  ```

- [ ] Check Python dependencies are installed
  ```bash
  # If using venv
  /opt/git-dashboard/venv/bin/pip list
  # Or for system Python
  pip list
  ```

- [ ] Ensure app logging is configured properly
  ```bash
  # Verify app.py uses console logging or www-data has write access to log directory
  grep -A 5 "logging" /opt/git-dashboard/app.py
  ```

## Service Status

- [ ] Verify Flask application is running
  ```bash
  sudo systemctl status git-dashboard.service
  sudo ss -tlnp | grep 8000
  ```

- [ ] Ensure Nginx is running
  ```bash
  sudo systemctl status nginx
  ```

## Post-Deployment Verification

- [ ] Check dashboard is accessible in browser
  ```
  https://git.chcit.org/
  ```

- [ ] Verify metrics data is loading
  ```
  https://git.chcit.org/api/metrics/current
  ```

- [ ] Test certificate status endpoint
  ```
  https://git.chcit.org/api/certificates/status
  ```

- [ ] Review logs for errors
  ```bash
  sudo journalctl -u git-dashboard.service -n 50
  sudo tail -f /var/log/nginx/git-dashboard.error.log
  ```

## Troubleshooting Commands

- [ ] Test file creation as www-data user
  ```bash
  sudo -u www-data touch /opt/git-dashboard/data/test.txt
  ```

- [ ] Check for read-only filesystem issues
  ```bash
  mount | grep " / "
  ```

- [ ] Verify process ownership
  ```bash
  ps -o user,group,pid,args -p $(pgrep -f "flask run")
  ```

Use this checklist before and after deployments to ensure all components of your Git dashboard are properly configured and have the correct permissions.
