#include "parser_manager.hpp"
#include <iostream>

namespace logging {

ParserManager::ParserManager() {
    // Default constructor
}

void ParserManager::registerParser(std::shared_ptr<LogParser> parser) {
    if (!parser) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Add to parsers list
    parsers_.push_back(parser);
    
    // Add to parsers map
    parsersByName_[parser->getName()] = parser;
    
    std::cout << "Registered parser: " << parser->getName() << std::endl;
}

std::shared_ptr<LogParser> ParserManager::getParser(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = parsersByName_.find(name);
    if (it != parsersByName_.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::vector<std::shared_ptr<LogParser>> ParserManager::getParsers() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return parsers_;
}

std::shared_ptr<LogParser> ParserManager::findParser(const std::string& line) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Try each parser
    for (const auto& parser : parsers_) {
        if (parser->canParse(line)) {
            return parser;
        }
    }
    
    return nullptr;
}

std::optional<LogEntry> ParserManager::parse(const std::string& line, const std::string& source) {
    // Find a parser that can handle this line
    auto parser = findParser(line);
    
    if (parser) {
        // Parse the line
        return parser->parse(line, source);
    }
    
    return std::nullopt;
}

} // namespace logging
