import React, { useState } from 'react';
import './SettingsPage.css';

// This is a placeholder component for the settings page
// In a real implementation, this would connect to API endpoints to manage service settings
const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    api: {
      port: 8080,
      ssl: {
        enabled: false,
        certPath: '/etc/letsencrypt/live/example.com/fullchain.pem',
        keyPath: '/etc/letsencrypt/live/example.com/privkey.pem'
      },
      cors: {
        enabled: true,
        allowedOrigins: '*',
        allowedMethods: 'GET, POST, PUT, DELETE, OPTIONS',
        allowedHeaders: 'Content-Type, Authorization',
        allowCredentials: true,
        maxAge: 86400
      }
    },
    security: {
      enableAuthentication: true,
      tokenExpirationSeconds: 3600,
      refreshTokenExpirationSeconds: 86400,
      secureCredentialStorage: {
        enabled: true
      }
    },
    database: {
      maxConnections: 10
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // This is a placeholder function for saving settings
  // In a real implementation, this would call an API endpoint
  const handleSaveSettings = (e: React.FormEvent) => {
    e.preventDefault();
    
    setLoading(true);
    setSuccess(null);
    setError(null);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSuccess('Settings saved successfully');
      
      // In a real implementation, this would be:
      // try {
      //   await api.saveSettings(settings);
      //   setSuccess('Settings saved successfully');
      // } catch (err) {
      //   setError('Failed to save settings');
      // } finally {
      //   setLoading(false);
      // }
    }, 1000);
  };
  
  const handleChange = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value
      }
    }));
  };
  
  const handleNestedChange = (section: string, subsection: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [subsection]: {
          ...prev[section as keyof typeof prev][subsection as keyof typeof prev[keyof typeof prev]],
          [key]: value
        }
      }
    }));
  };
  
  return (
    <div className="settings-page">
      <div className="container">
        <h1 className="page-title">Service Settings</h1>
        
        <div className="settings-description card">
          <h2>Database Service Configuration</h2>
          <p>
            Configure the Database Service settings. Changes to these settings may require a service restart to take effect.
          </p>
          <p>
            <strong>Note:</strong> Only administrators can modify service settings.
          </p>
        </div>
        
        {success && <div className="alert alert-success">{success}</div>}
        {error && <div className="alert alert-danger">{error}</div>}
        
        <form onSubmit={handleSaveSettings}>
          <div className="settings-section card">
            <h2>API Settings</h2>
            
            <div className="form-group">
              <label htmlFor="api-port">Port</label>
              <input
                type="number"
                id="api-port"
                className="form-control"
                value={settings.api.port}
                onChange={(e) => handleChange('api', 'port', parseInt(e.target.value))}
                min="1"
                max="65535"
              />
            </div>
            
            <div className="settings-subsection">
              <h3>SSL Configuration</h3>
              
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.api.ssl.enabled}
                    onChange={(e) => handleNestedChange('api', 'ssl', 'enabled', e.target.checked)}
                  />
                  Enable SSL
                </label>
              </div>
              
              <div className="form-group">
                <label htmlFor="ssl-cert-path">Certificate Path</label>
                <input
                  type="text"
                  id="ssl-cert-path"
                  className="form-control"
                  value={settings.api.ssl.certPath}
                  onChange={(e) => handleNestedChange('api', 'ssl', 'certPath', e.target.value)}
                  disabled={!settings.api.ssl.enabled}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="ssl-key-path">Key Path</label>
                <input
                  type="text"
                  id="ssl-key-path"
                  className="form-control"
                  value={settings.api.ssl.keyPath}
                  onChange={(e) => handleNestedChange('api', 'ssl', 'keyPath', e.target.value)}
                  disabled={!settings.api.ssl.enabled}
                />
              </div>
            </div>
            
            <div className="settings-subsection">
              <h3>CORS Configuration</h3>
              
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.api.cors.enabled}
                    onChange={(e) => handleNestedChange('api', 'cors', 'enabled', e.target.checked)}
                  />
                  Enable CORS
                </label>
              </div>
              
              <div className="form-group">
                <label htmlFor="cors-allowed-origins">Allowed Origins</label>
                <input
                  type="text"
                  id="cors-allowed-origins"
                  className="form-control"
                  value={settings.api.cors.allowedOrigins}
                  onChange={(e) => handleNestedChange('api', 'cors', 'allowedOrigins', e.target.value)}
                  disabled={!settings.api.cors.enabled}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="cors-allowed-methods">Allowed Methods</label>
                <input
                  type="text"
                  id="cors-allowed-methods"
                  className="form-control"
                  value={settings.api.cors.allowedMethods}
                  onChange={(e) => handleNestedChange('api', 'cors', 'allowedMethods', e.target.value)}
                  disabled={!settings.api.cors.enabled}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="cors-allowed-headers">Allowed Headers</label>
                <input
                  type="text"
                  id="cors-allowed-headers"
                  className="form-control"
                  value={settings.api.cors.allowedHeaders}
                  onChange={(e) => handleNestedChange('api', 'cors', 'allowedHeaders', e.target.value)}
                  disabled={!settings.api.cors.enabled}
                />
              </div>
              
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.api.cors.allowCredentials}
                    onChange={(e) => handleNestedChange('api', 'cors', 'allowCredentials', e.target.checked)}
                    disabled={!settings.api.cors.enabled}
                  />
                  Allow Credentials
                </label>
              </div>
              
              <div className="form-group">
                <label htmlFor="cors-max-age">Max Age (seconds)</label>
                <input
                  type="number"
                  id="cors-max-age"
                  className="form-control"
                  value={settings.api.cors.maxAge}
                  onChange={(e) => handleNestedChange('api', 'cors', 'maxAge', parseInt(e.target.value))}
                  disabled={!settings.api.cors.enabled}
                  min="0"
                />
              </div>
            </div>
          </div>
          
          <div className="settings-section card">
            <h2>Security Settings</h2>
            
            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.security.enableAuthentication}
                  onChange={(e) => handleChange('security', 'enableAuthentication', e.target.checked)}
                />
                Enable Authentication
              </label>
            </div>
            
            <div className="form-group">
              <label htmlFor="token-expiration">Access Token Expiration (seconds)</label>
              <input
                type="number"
                id="token-expiration"
                className="form-control"
                value={settings.security.tokenExpirationSeconds}
                onChange={(e) => handleChange('security', 'tokenExpirationSeconds', parseInt(e.target.value))}
                disabled={!settings.security.enableAuthentication}
                min="60"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="refresh-token-expiration">Refresh Token Expiration (seconds)</label>
              <input
                type="number"
                id="refresh-token-expiration"
                className="form-control"
                value={settings.security.refreshTokenExpirationSeconds}
                onChange={(e) => handleChange('security', 'refreshTokenExpirationSeconds', parseInt(e.target.value))}
                disabled={!settings.security.enableAuthentication}
                min="300"
              />
            </div>
            
            <div className="settings-subsection">
              <h3>Secure Credential Storage</h3>
              
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.security.secureCredentialStorage.enabled}
                    onChange={(e) => handleNestedChange('security', 'secureCredentialStorage', 'enabled', e.target.checked)}
                  />
                  Enable Secure Credential Storage
                </label>
              </div>
            </div>
          </div>
          
          <div className="settings-section card">
            <h2>Database Settings</h2>
            
            <div className="form-group">
              <label htmlFor="max-connections">Maximum Connections</label>
              <input
                type="number"
                id="max-connections"
                className="form-control"
                value={settings.database.maxConnections}
                onChange={(e) => handleChange('database', 'maxConnections', parseInt(e.target.value))}
                min="1"
                max="100"
              />
            </div>
          </div>
          
          <div className="settings-actions">
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Saving...' : 'Save Settings'}
            </button>
            <button type="button" className="btn btn-secondary" disabled={loading}>
              Reset to Defaults
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SettingsPage;
