import redis
import os
import json
from typing import Any, Dict, List, Optional, Union

class RedisClient:
    """Redis client for Project Tracker application.
    
    Handles caching, session management, and real-time updates.
    """
    
    def __init__(self, host: str = 'localhost', port: int = 6379, 
                 password: Optional[str] = None, db: int = 0):
        """Initialize Redis connection.
        
        Args:
            host: Redis server hostname
            port: Redis server port
            password: Redis authentication password
            db: Redis database number
        """
        self.redis = redis.Redis(
            host=host,
            port=port,
            password=password or os.environ.get('REDIS_PASSWORD'),
            db=db,
            decode_responses=True
        )
        
    def set_key(self, key: str, value: Any, expiry: Optional[int] = None) -> bool:
        """Set a key with optional expiration time.
        
        Args:
            key: Redis key
            value: Value to store (will be JSON serialized if not a string)
            expiry: Optional expiration time in seconds
            
        Returns:
            bool: Success status
        """
        if not isinstance(value, str):
            value = json.dumps(value)
            
        return bool(self.redis.set(key, value, ex=expiry))
    
    def get_key(self, key: str, default: Any = None) -> Any:
        """Get a value for a key with optional JSON deserialization.
        
        Args:
            key: Redis key
            default: Default value if key doesn't exist
            
        Returns:
            The stored value (deserialized if JSON)
        """
        value = self.redis.get(key)
        if value is None:
            return default
            
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value
    
    def delete_key(self, key: str) -> bool:
        """Delete a key from Redis.
        
        Args:
            key: Redis key to delete
            
        Returns:
            bool: Success status
        """
        return bool(self.redis.delete(key))
    
    def publish(self, channel: str, message: Any) -> int:
        """Publish a message to a channel.
        
        Args:
            channel: Redis pub/sub channel
            message: Message to publish (will be JSON serialized if not a string)
            
        Returns:
            int: Number of clients that received the message
        """
        if not isinstance(message, str):
            message = json.dumps(message)
            
        return self.redis.publish(channel, message)
    
    def cache_project_data(self, project_id: Union[int, str], data: Dict[str, Any], 
                          ttl: int = 3600) -> bool:
        """Cache project data with expiration.
        
        Args:
            project_id: Project identifier
            data: Project data to cache
            ttl: Time to live in seconds (default: 1 hour)
            
        Returns:
            bool: Success status
        """
        key = f"project:{project_id}:data"
        return self.set_key(key, data, expiry=ttl)
    
    def get_cached_project(self, project_id: Union[int, str]) -> Optional[Dict[str, Any]]:
        """Get cached project data.
        
        Args:
            project_id: Project identifier
            
        Returns:
            Optional[Dict]: Cached project data or None
        """
        key = f"project:{project_id}:data"
        return self.get_key(key)
    
    def store_session(self, session_id: str, user_data: Dict[str, Any], 
                      ttl: int = 86400) -> bool:
        """Store user session data.
        
        Args:
            session_id: Session identifier
            user_data: User session data
            ttl: Session time to live in seconds (default: 24 hours)
            
        Returns:
            bool: Success status
        """
        key = f"session:{session_id}"
        return self.set_key(key, user_data, expiry=ttl)
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get user session data.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Optional[Dict]: Session data or None if expired
        """
        key = f"session:{session_id}"
        return self.get_key(key)
    
    def invalidate_session(self, session_id: str) -> bool:
        """Invalidate a user session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            bool: Success status
        """
        key = f"session:{session_id}"
        return self.delete_key(key)


# Singleton instance for application-wide use
redis_client = RedisClient(
    host=os.environ.get('REDIS_HOST', 'localhost'),
    port=int(os.environ.get('REDIS_PORT', 6379)),
    password=os.environ.get('REDIS_PASSWORD'),
    db=int(os.environ.get('REDIS_DB', 0))
)
