# Quick fix for JavaScript paths

# Configurations
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"

# Create an updated index.html with correct script tags
Write-Host "Updating index.html with correct JavaScript paths..." -ForegroundColor Yellow

# Create the updated HTML content locally
$indexContent = Get-Content -Path "D:/Codeium/CHCIT/project-tracker/git-server-web/index.html" -Raw

# Upload the fixed index.html
Write-Host "Uploading fixed index.html..." -ForegroundColor Cyan
$indexContent | ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/index.html"
ssh "${RemoteUser}@${RemoteHost}" "sudo mv /tmp/index.html ${RemotePath}/index.html && sudo chown www-data:www-data ${RemotePath}/index.html && sudo chmod 644 ${RemotePath}/index.html"

# Verify the files exist on the server
Write-Host "Verifying JavaScript files exist..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "ls -la ${RemotePath}/js/git-*.js | wc -l"

# Recheck specific file content
Write-Host "Checking content of git-repository-manager.js..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "cat ${RemotePath}/js/git-repository-manager.js | head -n 10"

# Force reload browser cache
Write-Host "Creating cache-busting script..." -ForegroundColor Cyan
ssh "${RemoteUser}@${RemoteHost}" "echo '// Cache-busting timestamp: $(Get-Date -Format "yyyy-MM-dd-HH-mm-ss")' | sudo tee -a ${RemotePath}/js/git-repository-manager.js > /dev/null"

# Restart services
Write-Host "Restarting services..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "sudo systemctl restart git-dashboard.service && sudo systemctl restart nginx"

Write-Host "Fix completed!" -ForegroundColor Green
