#!/bin/bash
set -e

# Logging Configuration
MONITOR_LOG="/var/log/project-tracker/cdn-monitor.log"
METRICS_DIR="/var/lib/project-tracker/metrics"

# Color codes for logging
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$MONITOR_LOG"
}

# Create required directories
mkdir -p "$METRICS_DIR"
mkdir -p "$(dirname "$MONITOR_LOG")"

# Collect Nginx metrics
collect_nginx_metrics() {
    log "INFO" "Collecting Nginx metrics"
    
    # Parse Nginx access logs for CDN requests
    local metrics_file="$METRICS_DIR/nginx_metrics.json"
    
    {
        echo "{"
        echo "  \"timestamp\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\","
        echo "  \"metrics\": {"
        
        # Total requests
        local total_requests=$(wc -l < /var/log/nginx/project-tracker-access.log)
        echo "    \"total_requests\": $total_requests,"
        
        # Status code distribution
        echo "    \"status_codes\": {"
        awk '{print $9}' /var/log/nginx/project-tracker-access.log | sort | uniq -c | \
        while read count status; do
            echo "      \"$status\": $count,"
        done
        echo "      \"dummy\": 0"
        echo "    },"
        
        # Average response time
        local avg_time=$(awk '{sum+=$11; count++} END {print sum/count}' /var/log/nginx/project-tracker-access.log)
        echo "    \"avg_response_time\": $avg_time,"
        
        # Bandwidth usage
        local bandwidth=$(awk '{sum+=$10} END {print sum}' /var/log/nginx/project-tracker-access.log)
        echo "    \"bandwidth_bytes\": $bandwidth,"
        
        # Cache hit ratio
        local cache_hits=$(grep -c "HIT" /var/log/nginx/project-tracker-access.log)
        local cache_misses=$(grep -c "MISS" /var/log/nginx/project-tracker-access.log)
        local hit_ratio=$(echo "scale=2; $cache_hits/($cache_hits+$cache_misses)*100" | bc)
        echo "    \"cache_hit_ratio\": $hit_ratio"
        
        echo "  }"
        echo "}"
    } > "$metrics_file"
    
    # Send metrics to Azure Monitor
    if command -v az >/dev/null; then
        az monitor metrics put \
            --resource "/subscriptions/$AZURE_SUBSCRIPTION_ID/resourceGroups/project-tracker/providers/Microsoft.Cdn/profiles/project-tracker-cdn" \
            --metrics "@$metrics_file"
    fi
}

# Monitor SSL certificate expiry
check_ssl_cert() {
    log "INFO" "Checking SSL certificate"
    
    local domain="$1"
    local cert_info
    cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain":443 2>/dev/null | openssl x509 -noout -enddate)
    
    if [ $? -eq 0 ]; then
        local expiry_date=$(echo "$cert_info" | cut -d'=' -f2)
        local expiry_epoch=$(date -d "$expiry_date" +%s)
        local now_epoch=$(date +%s)
        local days_left=$(( (expiry_epoch - now_epoch) / 86400 ))
        
        if [ $days_left -lt 30 ]; then
            log "WARN" "SSL certificate for $domain expires in $days_left days"
        else
            log "INFO" "SSL certificate for $domain valid for $days_left days"
        fi
        
        # Record metric
        echo "{\"ssl_days_remaining\": $days_left}" > "$METRICS_DIR/ssl_metrics.json"
    else
        log "ERROR" "Failed to check SSL certificate for $domain"
    fi
}

# Monitor CDN edge nodes
check_edge_nodes() {
    log "INFO" "Checking CDN edge nodes"
    
    local edge_nodes=(
        "cdn1.project-tracker.chcit.org"
        "cdn2.project-tracker.chcit.org"
        "cdn3.project-tracker.chcit.org"
    )
    
    for node in "${edge_nodes[@]}"; do
        local response_time=$(curl -o /dev/null -s -w "%{time_total}\n" "https://$node/static/manifest.json")
        
        echo "{\"edge_node\": \"$node\", \"response_time\": $response_time}" >> "$METRICS_DIR/edge_metrics.json"
        
        if (( $(echo "$response_time > 1.0" | bc -l) )); then
            log "WARN" "Slow response from edge node $node: ${response_time}s"
        fi
    done
}

# Main monitoring loop
monitor_cdn() {
    log "INFO" "Starting CDN monitoring"
    
    while true; do
        # Collect all metrics
        collect_nginx_metrics
        check_ssl_cert "cdn.project-tracker.chcit.org"
        check_edge_nodes
        
        # Clean up old metrics files
        find "$METRICS_DIR" -type f -mtime +7 -delete
        
        # Wait before next check
        sleep 300
    done
}

# Start monitoring
monitor_cdn
