-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system 
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Found CURL: /usr/lib/x86_64-linux-gnu/libcurl.so (found version "8.5.0")  
-- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
-- Checking for module 'libgit2'
--   Found libgit2, version 1.7.2
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /opt/git-dashboard/git-repo-service/build
/usr/bin/cmake -S/opt/git-dashboard/git-repo-service -B/opt/git-dashboard/git-repo-service/build --check-build-system CMakeFiles/Makefile.cmake 0
/usr/bin/cmake -E cmake_progress_start /opt/git-dashboard/git-repo-service/build/CMakeFiles /opt/git-dashboard/git-repo-service/build//CMakeFiles/progress.marks
make  -f CMakeFiles/Makefile2 all
make[1]: Entering directory '/opt/git-dashboard/git-repo-service/build'
make  -f CMakeFiles/git-repo-service.dir/build.make CMakeFiles/git-repo-service.dir/depend
make[2]: Entering directory '/opt/git-dashboard/git-repo-service/build'
cd /opt/git-dashboard/git-repo-service/build && /usr/bin/cmake -E cmake_depends "Unix Makefiles" /opt/git-dashboard/git-repo-service /opt/git-dashboard/git-repo-service /opt/git-dashboard/git-repo-service/build /opt/git-dashboard/git-repo-service/build /opt/git-dashboard/git-repo-service/build/CMakeFiles/git-repo-service.dir/DependInfo.cmake "--color="
make[2]: Leaving directory '/opt/git-dashboard/git-repo-service/build'
make  -f CMakeFiles/git-repo-service.dir/build.make CMakeFiles/git-repo-service.dir/build
make[2]: Entering directory '/opt/git-dashboard/git-repo-service/build'
[ 50%] Building CXX object CMakeFiles/git-repo-service.dir/src/main.cpp.o
[ 50%] Building CXX object CMakeFiles/git-repo-service.dir/src/api/api_server.cpp.o
/usr/bin/c++ -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/opt/git-dashboard/git-repo-service/src -std=c++23 -MD -MT CMakeFiles/git-repo-service.dir/src/api/api_server.cpp.o -MF CMakeFiles/git-repo-service.dir/src/api/api_server.cpp.o.d -o CMakeFiles/git-repo-service.dir/src/api/api_server.cpp.o -c /opt/git-dashboard/git-repo-service/src/api/api_server.cpp
/usr/bin/c++ -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/opt/git-dashboard/git-repo-service/src -std=c++23 -MD -MT CMakeFiles/git-repo-service.dir/src/main.cpp.o -MF CMakeFiles/git-repo-service.dir/src/main.cpp.o.d -o CMakeFiles/git-repo-service.dir/src/main.cpp.o -c /opt/git-dashboard/git-repo-service/src/main.cpp
[ 75%] Building CXX object CMakeFiles/git-repo-service.dir/src/services/repository_service.cpp.o
/usr/bin/c++ -DBOOST_SYSTEM_DYN_LINK -DBOOST_SYSTEM_NO_LIB -I/opt/git-dashboard/git-repo-service/src -std=c++23 -MD -MT CMakeFiles/git-repo-service.dir/src/services/repository_service.cpp.o -MF CMakeFiles/git-repo-service.dir/src/services/repository_service.cpp.o.d -o CMakeFiles/git-repo-service.dir/src/services/repository_service.cpp.o -c /opt/git-dashboard/git-repo-service/src/services/repository_service.cpp
[100%] Linking CXX executable git-repo-service
/usr/bin/cmake -E cmake_link_script CMakeFiles/git-repo-service.dir/link.txt --verbose=1
/usr/bin/c++ "CMakeFiles/git-repo-service.dir/src/api/api_server.cpp.o" "CMakeFiles/git-repo-service.dir/src/main.cpp.o" "CMakeFiles/git-repo-service.dir/src/services/repository_service.cpp.o" -o git-repo-service  /usr/lib/x86_64-linux-gnu/libboost_system.so.1.83.0 /usr/lib/x86_64-linux-gnu/libssl.so /usr/lib/x86_64-linux-gnu/libcrypto.so /usr/lib/x86_64-linux-gnu/libcurl.so -ljsoncpp -lgit2 -lpthread 
make[2]: Leaving directory '/opt/git-dashboard/git-repo-service/build'
[100%] Built target git-repo-service
make[1]: Leaving directory '/opt/git-dashboard/git-repo-service/build'
/usr/bin/cmake -E cmake_progress_start /opt/git-dashboard/git-repo-service/build/CMakeFiles 0
BUILD_SUCCESS
