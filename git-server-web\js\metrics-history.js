// Metrics History Chart Functions

/**
 * @typedef {Object} SystemMetrics
 * @property {number} cpu_usage - CPU usage percentage
 * @property {number} mem_usage - Memory usage percentage
 * @property {number} disk_usage - Disk usage percentage
 * @property {number} load_avg - System load average
 */

/**
 * @typedef {Object} GitMetrics
 * @property {number} total_repos - Total number of repositories
 * @property {number} active_repos - Number of active repositories
 * @property {number} total_commits - Total number of commits
 * @property {number} active_branches - Number of active branches
 * @property {Array<Object>} recent_commits - Recent commit activity
 */

/**
 * @typedef {Object} MetricsData
 * @property {string} timestamp - ISO timestamp
 * @property {SystemMetrics} system - System metrics
 * @property {GitMetrics} git - Git repository metrics
 */

// Metrics History Controller
const MetricsHistory = {
    // Configuration
    config: {
        refreshInterval: 300000, // Default to 5 minutes
        endpoints: {
            history: '/api/metrics/history'
        },
        refreshIntervals: [
            { label: '5 minutes', value: 300000 },
            { label: '15 minutes', value: 900000 },
            { label: '1 hour', value: 3600000 },
            { label: '4 hours', value: 14400000 }
        ],
        timeWindows: {
            '15m': { 
                label: '15 Minutes', 
                value: 15, 
                unit: 'minutes',
                samplingMinutes: 0.5  // Show every 30 seconds (double density)
            },
            '1h': { 
                label: '1 Hour', 
                value: 1, 
                unit: 'hours',
                samplingMinutes: 2.5  // Show every 2.5 minutes (double density)
            },
            '4h': { 
                label: '4 Hours', 
                value: 4, 
                unit: 'hours',
                samplingMinutes: 5    // Show every 5 minutes (double density)
            },
            '8h': { 
                label: '8 Hours', 
                value: 8, 
                unit: 'hours',
                samplingMinutes: 7.5  // Show every 7.5 minutes (double density)
            }
        },
        defaultTimeWindow: '1h'
    },
    
    // State management
    state: {
        /**
         * @type {Chart|null}
         */
        systemChart: null,
        
        // Flag to prevent multiple concurrent chart operations
        isUpdatingChart: false,
        
        historyData: [],
        lastUpdated: null,
        status: 'loading',
        refreshTimer: null,
        countdownTimer: null,
        nextRefreshTime: null,
        selectedTimeWindow: '1h', // Default to 1 hour view
        isInitialized: false
    },

    /**
     * Initialize the component
     */
    init() {
        console.log('Initializing MetricsHistory component...');
        
        if (this.state.isInitialized) {
            console.warn('MetricsHistory already initialized');
            return this;
        }
        
        // Load saved preferences from localStorage
        this.loadUserPreferences();
        
        // Add refresh controls to header
        this.addRefreshControls();
        
        // Initial data fetch
        this.fetchHistoryData();
        
        // Start refresh timer
        this.startRefreshTimer();
        
        // Set initialization state
        this.state.isInitialized = true;
        
        return this;
    },
    
    /**
     * Load user preferences from local storage
     */
    loadUserPreferences() {
        try {
            // Load refresh interval preference
            const savedInterval = localStorage.getItem('metricsHistoryRefreshInterval');
            if (savedInterval && !isNaN(parseInt(savedInterval))) {
                this.config.refreshInterval = parseInt(savedInterval);
            }
            
            // Load time window preference
            const savedTimeWindow = localStorage.getItem('metricsHistoryTimeWindow');
            if (savedTimeWindow && this.config.timeWindows[savedTimeWindow]) {
                this.state.selectedTimeWindow = savedTimeWindow;
            }
        } catch (e) {
            console.warn('Could not load user preferences:', e);
        }
    },
    
    /**
     * Save user preferences to localStorage
     */
    saveUserPreferences() {
        try {
            localStorage.setItem('metricsHistoryRefreshInterval', this.config.refreshInterval.toString());
        } catch (error) {
            console.warn('Failed to save preferences to localStorage:', error);
        }
    },
    
    /**
     * Initialize state from local storage
     */
    loadSavedState() {
        try {
            // Load refresh interval preference
            const savedInterval = localStorage.getItem('metricsHistoryRefreshInterval');
            if (savedInterval && !isNaN(parseInt(savedInterval))) {
                this.config.refreshInterval = parseInt(savedInterval);
            }
            
            // Load time window preference
            const savedTimeWindow = localStorage.getItem('metricsHistoryTimeWindow');
            if (savedTimeWindow && this.config.timeWindows[savedTimeWindow]) {
                this.state.selectedTimeWindow = savedTimeWindow;
            }
        } catch (e) {
            console.warn('Could not load saved state:', e);
        }
    },
    
    /**
     * Add refresh controls to the Metrics History card header
     */
    addRefreshControls() {
        const headerDiv = document.querySelector('.card-header div:has(#history-status-indicator)');
        if (!headerDiv) {
            console.warn('Could not find metrics history card header');
            return;
        }
        
        // Check if controls already exist to avoid duplicates
        const existingControls = headerDiv.parentNode.querySelector('.refresh-controls');
        if (existingControls) {
            console.log('Refresh controls already exist, skipping creation');
            return;
        }
        
        // Create refresh controls container
        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'refresh-controls ms-auto';
        
        // Add the time window dropdown
        const timeWindowGroup = document.createElement('div');
        timeWindowGroup.className = 'btn-group me-2';
        
        const timeWindowButton = document.createElement('button');
        timeWindowButton.className = 'btn btn-sm btn-outline-secondary dropdown-toggle';
        timeWindowButton.setAttribute('type', 'button');
        timeWindowButton.setAttribute('data-bs-toggle', 'dropdown');
        timeWindowButton.setAttribute('aria-expanded', 'false');
        timeWindowButton.innerHTML = `<i class="fas fa-clock me-1"></i> ${this.config.timeWindows[this.state.selectedTimeWindow].label}`;
        
        const timeWindowMenu = document.createElement('ul');
        timeWindowMenu.className = 'dropdown-menu';
        
        // Add time window options
        Object.entries(this.config.timeWindows).forEach(([key, { label }]) => {
            const item = document.createElement('li');
            const link = document.createElement('a');
            link.className = 'dropdown-item' + (key === this.state.selectedTimeWindow ? ' active' : '');
            link.href = '#';
            link.textContent = label;
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.setTimeWindow(key);
                
                // Update button text
                timeWindowButton.innerHTML = `<i class="fas fa-clock me-1"></i> ${label}`;
                
                // Update active state in dropdown
                timeWindowMenu.querySelectorAll('.dropdown-item').forEach(el => {
                    el.classList.remove('active');
                });
                link.classList.add('active');
            });
            item.appendChild(link);
            timeWindowMenu.appendChild(item);
        });
        
        timeWindowGroup.appendChild(timeWindowButton);
        timeWindowGroup.appendChild(timeWindowMenu);
        controlsDiv.appendChild(timeWindowGroup);
        
        // Add refresh interval dropdown
        const intervalGroup = document.createElement('div');
        intervalGroup.className = 'btn-group me-2';
        
        const intervalButton = document.createElement('button');
        intervalButton.className = 'btn btn-sm btn-outline-secondary dropdown-toggle';
        intervalButton.setAttribute('type', 'button');
        intervalButton.setAttribute('data-bs-toggle', 'dropdown');
        intervalButton.setAttribute('aria-expanded', 'false');
        intervalButton.innerHTML = `<i class="fas fa-sync me-1"></i> Auto`;
        
        const intervalMenu = document.createElement('ul');
        intervalMenu.className = 'dropdown-menu';
        
        // Add refresh interval options
        this.config.refreshIntervals.forEach(option => {
            const item = document.createElement('li');
            const link = document.createElement('a');
            link.className = 'dropdown-item' + (this.config.refreshInterval === option.value ? ' active' : '');
            link.href = '#';
            link.textContent = option.label;
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.config.refreshInterval = option.value;
                this.saveUserPreferences();
                this.startRefreshTimer(); // Restart timer with new interval
                
                // Update button text
                intervalButton.innerHTML = `<i class="fas fa-sync me-1"></i> ${option.label}`;
                
                // Update active state in dropdown
                intervalMenu.querySelectorAll('.dropdown-item').forEach(el => {
                    el.classList.remove('active');
                });
                link.classList.add('active');
            });
            item.appendChild(link);
            intervalMenu.appendChild(item);
        });
        
        intervalGroup.appendChild(intervalButton);
        intervalGroup.appendChild(intervalMenu);
        controlsDiv.appendChild(intervalGroup);
        
        // Add countdown indicator
        const countdownSpan = document.createElement('span');
        countdownSpan.className = 'refresh-countdown ms-2 text-muted small';
        countdownSpan.id = 'metrics-history-countdown';
        countdownSpan.textContent = '';
        
        // Add to controls div
        controlsDiv.appendChild(countdownSpan);
        
        // Insert controls into header
        headerDiv.parentNode.appendChild(controlsDiv);
        
        // Add flex class to header for proper alignment
        headerDiv.parentNode.classList.add('d-flex', 'justify-content-between', 'align-items-center');
    },
    
    /**
     * Set the time window for the history chart
     * @param {string} windowKey - Key of the time window to set
     */
    setTimeWindow(windowKey) {
        if (!this.config.timeWindows[windowKey]) {
            console.warn(`Invalid time window: ${windowKey}`);
            return;
        }
        
        this.state.selectedTimeWindow = windowKey;
        
        // Save preference
        try {
            localStorage.setItem('metricsHistoryTimeWindow', windowKey);
        } catch (e) {
            console.warn('Could not save time window preference:', e);
        }
        
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 
                `Changed metrics history time window to ${this.config.timeWindows[windowKey].label}`, 
                'metrics');
        }
        
        // Fetch new data and update chart
        this.fetchHistoryData();
    },
    
    /**
     * Start the refresh timer
     */
    startRefreshTimer() {
        // Clear any existing timer
        if (this.state.refreshTimer) {
            clearInterval(this.state.refreshTimer);
            clearInterval(this.state.countdownTimer);
        }
        
        // Calculate next refresh time
        this.state.nextRefreshTime = Date.now() + this.config.refreshInterval;
        
        // Start a new timer
        this.state.refreshTimer = setInterval(() => {
            this.fetchHistoryData();
        }, this.config.refreshInterval);
        
        // Start countdown display timer
        this.updateCountdown();
        this.state.countdownTimer = setInterval(() => {
            this.updateCountdown();
        }, 1000); // Update every second
        
        console.log(`Metrics history refresh timer started: ${this.config.refreshInterval}ms`);
    },
    
    /**
     * Update the countdown display
     */
    updateCountdown() {
        const countdownEl = document.getElementById('metrics-history-countdown');
        if (!countdownEl || !this.state.nextRefreshTime) return;
        
        const remainingMs = this.state.nextRefreshTime - Date.now();
        if (remainingMs <= 0) {
            countdownEl.textContent = 'Refreshing...';
            return;
        }
        
        const seconds = Math.ceil(remainingMs / 1000);
        countdownEl.textContent = `Next: ${seconds}s`;
    },
    
    /**
     * Update module status
     * @param {string} status - New status (loading, success, error)
     */
    updateStatus(status) {
        this.state.status = status;
        
        // Update UI indicators if they exist
        const statusIndicator = document.getElementById('history-status-indicator');
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator ' + status;
        }
    },
    
    /**
     * Fetch metrics history data
     */
    async fetchHistoryData() {
        try {
            this.updateStatus('loading');
            
            const response = await fetch(this.config.endpoints.history);
            
            if (!response.ok) {
                if (response.status === 404) {
                    console.warn("No history data available");
                    if (window.DashboardLogger) {
                        window.DashboardLogger.addLog('warning', 'No metrics history data available', 'metrics');
                    }
                    this.state.historyData = [];
                    this.updateCharts([]);
                } else {
                    const errorMsg = `Failed to fetch history data: ${response.status}`;
                    if (window.DashboardLogger) {
                        window.DashboardLogger.addLog('error', errorMsg, 'metrics');
                    }
                    throw new Error(errorMsg);
                }
                this.updateStatus('error');
                return;
            }
            
            const data = await response.json();
            console.log('Fetched history data:', data);
            
            // Handle both formats: array or object with history property
            this.state.historyData = Array.isArray(data) ? data : (data.history || []);
            
            // Filter data based on selected time window
            const filteredData = this.filterDataByTimeWindow(this.state.historyData);
            console.log(`Filtered data to ${filteredData.length} points for ${this.config.timeWindows[this.state.selectedTimeWindow].label} window`);
            
            // Update last updated timestamp
            this.state.lastUpdated = new Date();
            this.updateStatus('success');
            
            // Update the charts with filtered data
            this.updateCharts(filteredData);
            
            // Update global last updated time
            const lastUpdatedEl = document.getElementById('last-updated');
            if (lastUpdatedEl) {
                lastUpdatedEl.textContent = this.state.lastUpdated.toLocaleString();
            }
            
        } catch (error) {
            console.error('Error fetching history data:', error);
            this.updateStatus('error');
            this.state.error = error.message;
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Error fetching history data: ${error.message}`, 'metrics');
            }
        }
    },
    
    /**
     * Filter history data based on selected time window
     * @param {Array} data - Full history data
     * @returns {Array} - Filtered data based on time window
     */
    filterDataByTimeWindow(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return [];
        }
        
        const timeWindow = this.config.timeWindows[this.state.selectedTimeWindow];
        let cutoffTime;
        
        // Calculate cutoff time based on window unit and value
        if (timeWindow.unit === 'minutes') {
            cutoffTime = new Date(Date.now() - (timeWindow.value * 60 * 1000));
        } else {
            // Default to hours
            cutoffTime = new Date(Date.now() - (timeWindow.value * 60 * 60 * 1000));
        }
        
        // Filter data to the selected time window
        let filteredData = data.filter(item => {
            const timestamp = new Date(item.timestamp);
            return timestamp >= cutoffTime;
        });
        
        // Sort by timestamp to ensure proper order
        filteredData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        
        // Sample the data based on the configured sampling interval for this time window
        if (filteredData.length > 0 && timeWindow.samplingMinutes > 0) {
            const samplingMs = timeWindow.samplingMinutes * 60 * 1000;
            const result = [];
            
            // Get the most recent timestamp as our anchor
            const lastTimestamp = new Date(filteredData[filteredData.length - 1].timestamp).getTime();
            
            // Work backwards in samplingMinutes increments to select data points
            for (let targetTime = lastTimestamp; targetTime >= cutoffTime.getTime(); targetTime -= samplingMs) {
                // Find the closest data point to this target time
                const closestPoint = this.findClosestDataPoint(filteredData, targetTime);
                if (closestPoint) {
                    result.unshift(closestPoint); // Add to beginning to maintain chronological order
                }
            }
            
            console.log(`Sampled data from ${filteredData.length} to ${result.length} points for ${timeWindow.label} window`);
            return result;
        }
        
        return filteredData;
    },
    
    /**
     * Find the closest data point to a target timestamp
     * @param {Array} data - Data array
     * @param {number} targetTime - Target timestamp in ms
     * @returns {Object|null} - The closest data point or null
     */
    findClosestDataPoint(data, targetTime) {
        if (!data || data.length === 0) return null;
        
        let closestPoint = null;
        let closestDistance = Infinity;
        
        for (const point of data) {
            const timestamp = new Date(point.timestamp).getTime();
            const distance = Math.abs(timestamp - targetTime);
            
            if (distance < closestDistance) {
                closestDistance = distance;
                closestPoint = point;
            }
        }
        
        return closestPoint;
    },
    
    /**
     * Update charts with fresh data
     * @param {MetricsData[]} data
     */
    updateCharts(data) {
        if (!data || data.length === 0) {
            console.warn("No history data available for charts");
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('warning', 'No metrics history data available for charts', 'metrics');
            }
            return;
        }
        
        // Hide loading indicator
        const loadingEl = document.getElementById('metrics-chart-loading');
        if (loadingEl) {
            loadingEl.style.display = 'none';
        }
        
        // Update the system metrics chart
        this.updateChart(data);
        
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 
                `Updated metrics history chart with ${data.length} data points (${this.config.timeWindows[this.state.selectedTimeWindow].label} window)`, 
                'metrics');
        }
    },

    /**
     * Create/update system metrics chart
     * @param {MetricsData[]} data
     */
    updateChart(data) {
        // Prevent multiple chart operations at the same time
        if (this.state.isUpdatingChart) {
            console.warn("Chart update already in progress, skipping");
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('warning', 'Chart update already in progress, skipping this update', 'metrics');
            }
            return;
        }
        
        this.state.isUpdatingChart = true;
        
        try {
            // Destroy any existing charts
            if (this.state.systemChart) {
                this.state.systemChart.destroy();
                this.state.systemChart = null;
            }
            
            const chartCanvas = document.getElementById('metrics-chart');
            if (!chartCanvas) {
                if (window.DashboardLogger) {
                    window.DashboardLogger.addLog('error', 'Chart canvas not found', 'metrics');
                }
                this.state.isUpdatingChart = false;
                return;
            }
            
            // Make sure canvas is visible
            chartCanvas.style.display = 'block';
            
            // Format timestamps and prepare data
            const labels = this.formatTimestamps(data);
            
            // Extract data series, handling missing values gracefully
            const cpuData = data.map(d => d.system?.cpu_usage || 0);
            const memoryData = data.map(d => d.system?.mem_usage || 0);
            const diskData = data.map(d => d.system?.disk_usage || 0);
            
            // Get fresh context
            const ctx = chartCanvas.getContext('2d');
            
            // Create chart based on basic line chart example from Chart.js docs
            this.state.systemChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'CPU (%)',
                            data: cpuData,
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderWidth: 1,
                            pointRadius: 3,
                            pointHoverRadius: 5
                        },
                        {
                            label: 'Memory (%)',
                            data: memoryData,
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderWidth: 1,
                            pointRadius: 3,
                            pointHoverRadius: 5
                        },
                        {
                            label: 'Disk (%)',
                            data: diskData,
                            borderColor: 'rgb(153, 102, 255)',
                            backgroundColor: 'rgba(153, 102, 255, 0.5)',
                            borderWidth: 1,
                            pointRadius: 3,
                            pointHoverRadius: 5
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false,
                            text: 'System Metrics History'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    hover: {
                        mode: 'nearest',
                        intersect: true
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Time'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Value (%)'
                            },
                            suggestedMin: 0,
                            suggestedMax: 100,
                            ticks: {
                                stepSize: 5 // Show ticks every 5%
                            }
                        }
                    }
                }
            });
            
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('success', 'System metrics chart created', 'metrics');
            }
        } catch (error) {
            console.error('Error creating system metrics chart:', error);
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Error creating metrics chart: ${error.message}`, 'metrics');
            }
        } finally {
            this.state.isUpdatingChart = false;
        }
    },

    /**
     * Format timestamps for display
     * @param {MetricsData[]} data
     * @returns {string[]}
     */
    formatTimestamps(data) {
        return data.map(item => {
            const date = new Date(item.timestamp);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        });
    },

    /**
     * Handle no data scenario
     */
    handleNoData() {
        const chartCanvas = document.getElementById('metrics-chart');
        if (!chartCanvas) return;
        
        // Hide the chart
        chartCanvas.style.display = 'none';
        
        // Show no data message
        const loadingEl = document.getElementById('metrics-chart-loading');
        if (loadingEl) {
            loadingEl.style.display = 'block';
            loadingEl.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No metrics history data available yet. Data will appear here as it's collected.
                </div>
            `;
        }
        
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('warning', 'No metrics history data available', 'metrics');
        }
    }
};

// Expose this module globally
window.MetricsHistory = MetricsHistory;

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    MetricsHistory.init();
});
