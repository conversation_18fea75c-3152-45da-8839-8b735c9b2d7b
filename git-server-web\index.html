<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Repository Dashboard</title>
    <!-- Ensure Chart.js is loaded directly -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.2.1/dist/chart.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Modular CSS files -->
    <link rel="stylesheet" href="/css/core.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link rel="stylesheet" href="/css/metrics.css">
    <link rel="stylesheet" href="/css/repositories.css">
    <link rel="stylesheet" href="/css/certificates.css">
    <link rel="stylesheet" href="/css/logging.css">
    <link rel="stylesheet" href="/css/animations.css">
    <link rel="stylesheet" href="/css/responsive.css">

    <!-- Add Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="dashboard-title">
                <i class="fas fa-code-branch"></i>
                <h1>Git Repository Dashboard</h1>
            </div>
            <div class="refresh-container">
                <button id="refresh-btn" class="refresh-btn">
                    <i class="fas fa-sync"></i> Refresh
                </button>
                <div class="last-updated-info">
                    <small class="text-muted">Last updated: <span id="last-updated">Never</span></small>
                </div>
            </div>
        </div>

        <div id="toast-container"></div>

        <!-- Tabs Navigation - Added Logs tab -->
        <ul class="nav nav-tabs mb-3" id="dashboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                    <i class="fas fa-tachometer-alt"></i> Overview
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="repositories-tab" data-bs-toggle="tab" data-bs-target="#repositories" type="button" role="tab" aria-controls="repositories" aria-selected="false">
                    <i class="fas fa-code-branch"></i> Repositories
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="metrics-tab" data-bs-toggle="tab" data-bs-target="#metrics" type="button" role="tab" aria-controls="metrics" aria-selected="false">
                    <i class="fas fa-chart-line"></i> Metrics
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="certificates-tab" data-bs-toggle="tab" data-bs-target="#certificates" type="button" role="tab" aria-controls="certificates" aria-selected="false">
                    <i class="fas fa-shield-alt"></i> Certificates
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">
                    <i class="fas fa-list-alt"></i> Logs
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="debug-tab" data-bs-toggle="tab" data-bs-target="#debug" type="button" role="tab" aria-controls="debug" aria-selected="false">
                    <i class="fas fa-bug"></i> Troubleshooting
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="dashboardTabContent">
            <!-- Overview Tab -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator loading" id="overview-status-indicator"></span>
                            Dashboard Overview
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Left Column: Server Performance -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="status-indicator loading" id="server-status-indicator"></span>
                                            Server Performance
                                        </div>
                                    </div>
                                    <div class="card-body" id="server-performance-container">
                                        <!-- Gauges will be inserted here by performance-gauges.js -->
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Git Stats and Certificate Status -->
                            <div class="col-md-6 d-flex flex-column justify-content-between">
                                <!-- Git Repository Metrics -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="status-indicator loading" id="git-status-indicator"></span>
                                            Git Repository Stats
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6 mb-3">
                                                <div class="metric-value" id="total-repos">--</div>
                                                <div class="metric-label">Total Repositories</div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="metric-value" id="total-size">--</div>
                                                <div class="metric-label">Total Size</div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="metric-value" id="recent-commits">--</div>
                                                <div class="metric-label">Recent Commits (24h)</div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="metric-value" id="largest-repo">--</div>
                                                <div class="metric-label">Largest Repository</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Certificate Information -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="status-indicator loading" id="cert-overview-status-indicator"></span>
                                            Certificate Status
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="cert-overview" class="row">
                                            <!-- Certificate content will be dynamically inserted here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Repositories Tab -->
            <div class="tab-pane fade" id="repositories" role="tabpanel" aria-labelledby="repositories-tab">
                <div class="row">
                    <!-- Repository List Section -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="status-indicator" id="repo-list-status-indicator"></span>
                                    Repository List
                                </div>
                                <div id="git-repository-controls">
                                    <button id="refresh-repos-btn" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0" id="git-repo-section">
                                <!-- Repository content will be dynamically inserted here by git-repository-list.js -->
                            </div>
                        </div>
                    </div>

                    <!-- Repository Details Section -->
                    <div class="col-md-8" id="repository-details-section">
                        <div class="card" id="repository-details-card" style="display: none;">
                            <div class="card-header d-flex justify-content-between align-items-center py-2">
                                <div>
                                    <span class="status-indicator" id="repo-details-status-indicator"></span>
                                    <span id="repo-name">Repository Details</span>
                                </div>
                                <div id="history-range-container">
                                    <!-- History Range selector will be placed here -->
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <!-- Loading State -->
                                <div id="repository-details-loading" class="p-4 text-center" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>

                                <!-- Error State -->
                                <div id="repository-details-error" class="p-4 text-center" style="display: none;">
                                    <div class="text-danger">
                                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                        <p>Failed to load repository details.</p>
                                    </div>
                                </div>

                                <!-- Content State -->
                                <div id="repository-details-content" style="display: none;">
                                    <!-- Repository Info - Compact Version -->
                                    <div class="p-2 border-bottom">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex flex-wrap">
                                                <div class="me-3 mb-0">
                                                    <small><strong>Size:</strong> <span id="repo-size">0 KB</span></small>
                                                </div>
                                                <div class="me-3 mb-0">
                                                    <small><strong>Date Range:</strong> <span id="repo-last-commit">Never</span></small>
                                                </div>
                                                <div class="me-3 mb-0">
                                                    <small><strong>Branches:</strong> <span id="repo-branch-count">0</span></small>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="progress" style="width: 100px; height: 8px;">
                                                    <div id="repo-health-progress" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <small class="text-muted" id="repo-health-message">Health: Unknown</small>
                                            </div>
                                        </div>
                                        <small class="text-muted" id="repo-description">No description</small>
                                    </div>

                                    <!-- Commit History -->
                                    <div class="p-3">
                                        <div id="commit-history-container" style="height: 450px; position: relative;">
                                            <div id="commit-chart-loading" class="d-flex justify-content-center align-items-center h-100" style="display: none;">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            <canvas id="commit-history-chart"></canvas>
                                            <div id="commit-chart-error" class="text-center text-danger mt-2" style="display: none;"></div>
                                            <div id="commit-chart-message" class="text-center text-muted mt-2" style="display: none;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- No Repository Selected State -->
                        <div class="card" id="no-repository-selected">
                            <div class="card-body text-center p-5">
                                <i class="fas fa-code-branch fa-4x text-muted mb-3"></i>
                                <h5>No Repository Selected</h5>
                                <p class="text-muted">Select a repository from the list to view details and commit history.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Metrics Tab -->
            <div class="tab-pane fade" id="metrics" role="tabpanel" aria-labelledby="metrics-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator loading" id="history-status-indicator"></span>
                            System Metrics History
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="metrics-chart-loading" style="display: flex; justify-content: center; align-items: center;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <canvas id="metrics-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Certificates Tab -->
            <div class="tab-pane fade" id="certificates" role="tabpanel" aria-labelledby="certificates-tab">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator loading"></span>
                            SSL Certificates
                        </div>
                    </div>
                    <div class="card-body p-3">
                        <div id="certStatus" class="w-100 h-100">
                            <!-- Certificate content will be dynamically inserted here by certificates.js -->
                        </div>

                        <!-- Loading State for Cert Status -->
                        <div id="cert-status-loading" style="display: none; justify-content: center; align-items: center;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs Tab - with controls in the header -->
            <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator loading" id="logs-status-indicator"></span>
                            System Logs <span id="log-count-display" class="ms-2 text-muted small"></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <!-- Log Settings Button -->
                            <button id="log-settings-button" class="btn btn-sm btn-secondary me-3">
                                <i class="fas fa-cog"></i> Settings
                            </button>

                            <!-- Log Count Selector -->
                            <div class="log-count-control me-3">
                                <select id="log-count-selector" class="form-select form-select-sm">
                                    <option value="5">5 logs</option>
                                    <option value="10" selected>10 logs</option>
                                    <option value="25">25 logs</option>
                                    <option value="50">50 logs</option>
                                    <option value="100">100 logs</option>
                                </select>
                            </div>

                            <!-- Log Source Selector -->
                            <div class="log-source-control me-3">
                                <select id="log-source-selector" class="form-select form-select-sm">
                                    <option value="dashboard" selected>Dashboard</option>
                                    <option value="system">System</option>
                                    <option value="nginx">Nginx</option>
                                    <option value="git">Git</option>
                                    <option value="auth">Auth</option>
                                </select>
                            </div>

                            <!-- Log Level Selector -->
                            <div class="log-level-control me-3">
                                <select id="log-level-selector" class="form-select form-select-sm">
                                    <option value="all" selected>All Levels</option>
                                    <option value="info">Info</option>
                                    <option value="warning">Warning</option>
                                    <option value="error">Error</option>
                                    <option value="success">Success</option>
                                </select>
                            </div>

                            <!-- Refresh Logs Button -->
                            <button id="refresh-logs-btn" class="btn btn-sm btn-primary">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Log container without its own header -->
                        <div id="dashboard-log-content" class="w-100"></div>
                    </div>
                </div>
            </div>

            <!-- Troubleshooting Tab -->
            <div class="tab-pane fade" id="debug" role="tabpanel" aria-labelledby="debug-tab">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator" id="debug-status-indicator"></span>
                            Troubleshooting Tools
                        </div>
                        <button id="refresh-debug-tools" class="btn btn-sm btn-secondary">
                            <i class="fas fa-sync"></i> Refresh Tools
                        </button>
                    </div>
                    <div class="card-body p-3">
                        <div id="troubleshooting-container" class="row">
                            <!-- Debug tools will be inserted here by git-debug.js and api-check.js -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- External JavaScript files - Order matters! -->
    <script src="/js/logging.js"></script>
    <script src="/js/log-settings-modal.js"></script>
    <script src="/js/dashboard.js"></script>
    <script src="/js/metrics-history.js"></script>
    <script src="/js/performance-gauges.js"></script>
    <script src="/js/server-metrics.js"></script>
    <script src="/js/certificates.js"></script>
    <!-- Git Repository Scripts - Ensure correct loading order -->
    <script src="/js/git-utils.js"></script>
    <!-- Explicitly load Chart.js again to ensure it's available -->
    <script>
        if (typeof Chart === 'undefined') {
            console.error('Chart.js not loaded, attempting to load it again');
            document.write('<script src="https://cdn.jsdelivr.net/npm/chart.js@4.2.1/dist/chart.umd.min.js"><\/script>');
        } else {
            console.log('Chart.js is already loaded');
        }
    </script>
    <script src="/js/git-commit-history-chart.js"></script>
    <!-- Fallback for GitCommitHistoryChart if it fails to load -->
    <script src="/js/chart-fallback.js"></script>
    <script src="/js/git-size-comparison-chart.js"></script>
    <script src="/js/git-summary-view.js"></script>
    <script src="/js/git-repository-list.js"></script>
    <script src="/js/git-repository-modal.js"></script>
    <script src="/js/git-repository-manager.js"></script>
    <!-- Troubleshooting Scripts -->
    <script src="/js/git-debug.js"></script>
    <script src="/js/api-check.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", async () => {
            // The logging system is initialized in its own file
            Dashboard.init();
            MetricsHistory.init();
            ServerMetricsManager.init();
            CertificateManager.init();

            try {
                // Initialize Git Summary View
                if (window.GitSummaryView) {
                    GitSummaryView.init();
                    console.log('GitSummaryView initialized successfully');
                }

                // Initialize chart modules
                if (window.GitCommitHistoryChart) {
                    GitCommitHistoryChart.init();
                    console.log('GitCommitHistoryChart initialized successfully');
                } else {
                    console.error('GitCommitHistoryChart not found, attempting to recreate');
                    // Recreate the module if it's missing
                    window.GitCommitHistoryChart = {
                        config: {
                            chartId: 'commit-history-chart',
                            containerId: 'commit-history-container',
                            loadingId: 'commit-chart-loading',
                            errorId: 'commit-chart-error',
                            messageId: 'commit-chart-message',
                            days: 30,
                            colors: {
                                background: 'rgba(54, 162, 235, 0.5)',
                                border: 'rgba(54, 162, 235, 1)'
                            }
                        },
                        state: { chart: null },
                        init() {
                            console.log('GitCommitHistoryChart.init() - Emergency initialization');
                            return this;
                        },
                        cleanupChart() {
                            if (this.state.chart) {
                                this.state.chart.destroy();
                                this.state.chart = null;
                            }
                        },
                        initChart(repoName) {
                            console.log(`Emergency initChart called for ${repoName}`);
                            this.fetchAndRenderChart(repoName);
                        },
                        fetchAndRenderChart(repoName) {
                            console.log(`Emergency chart rendering for ${repoName}`);
                            // Implementation will be minimal
                        }
                    };
                    console.log('Emergency GitCommitHistoryChart created');
                }

                if (window.GitSizeComparisonChart) {
                    GitSizeComparisonChart.init();
                    console.log('GitSizeComparisonChart initialized successfully');
                }

                // Check if GitRepositoryModal is loaded
                if (window.GitRepositoryModal) {
                    console.log('GitRepositoryModal is available');
                } else {
                    console.error('GitRepositoryModal not found, attempting to recreate');
                    // Create a minimal implementation
                    window.GitRepositoryModal = {
                        state: {
                            currentRepository: null,
                            initialized: true
                        },
                        init() {
                            console.log('GitRepositoryModal.init() - Emergency initialization');
                            return this;
                        },
                        showRepositoryDetails(repo) {
                            console.log('Emergency showRepositoryDetails called for', repo);
                            this.state.currentRepository = repo;
                        }
                    };
                    console.log('Emergency GitRepositoryModal created');

                    // Try to load the script again
                    const script = document.createElement('script');
                    script.src = '/js/git-repository-modal.js';
                    script.onload = function() {
                        console.log('GitRepositoryModal.js loaded successfully');
                    };
                    script.onerror = function() {
                        console.error('Failed to load GitRepositoryModal.js');
                    };
                    document.head.appendChild(script);
                }

                // Initialize Git Repository Manager - Wait for it to complete as it's async now
                await GitRepositoryManager.init();
                console.log('GitRepositoryManager initialized successfully');

                // Initialize the repository modal
                if (window.GitRepositoryModal) {
                    GitRepositoryModal.init(GitRepositoryManager);
                    console.log('GitRepositoryModal initialized successfully');
                } else {
                    console.error('GitRepositoryModal not available');
                }
            } catch (error) {
                console.error('Error initializing Git components:', error);
            }

            // Initialize debugging tools
            setTimeout(() => {
                const troubleshootingContainer = document.getElementById('troubleshooting-container');
                if (troubleshootingContainer) {
                    GitDebug.init();
                    ApiCheck.init();

                    // Add refresh button functionality
                    const refreshDebugBtn = document.getElementById('refresh-debug-tools');
                    if (refreshDebugBtn) {
                        refreshDebugBtn.addEventListener('click', function() {
                            // Clear existing debug content
                            troubleshootingContainer.innerHTML = '';

                            // Reinitialize tools
                            GitDebug.init();
                            ApiCheck.init();
                        });
                    }
                }
            }, 500);

            // Add refresh logs button functionality
            const refreshLogsBtn = document.getElementById('refresh-logs-btn');
            if (refreshLogsBtn && window.DashboardLogger) {
                refreshLogsBtn.addEventListener('click', function() {
                    window.DashboardLogger.refreshLogs();
                });
            }

            // Add tab change events
            const tabs = document.querySelectorAll('a[data-bs-toggle="tab"]');
            tabs.forEach(tab => {
                tab.addEventListener('shown.bs.tab', function (event) {
                    // Check if this is the troubleshooting tab
                    if (event.target.getAttribute('href') === '#debug') {
                        // Refresh the troubleshooting tools when the tab is activated
                        const troubleshootingContainer = document.getElementById('troubleshooting-container');
                        if (troubleshootingContainer && troubleshootingContainer.children.length === 0) {
                            GitDebug.init();
                            ApiCheck.init();
                        }
                    }
                });
            });
        });
    </script>
    <!-- API Debug Checker -->
    <script src="/js/api-check.js" defer></script>
    <!-- Git Component Debug -->
    <script src="/js/git-debug.js" defer></script>

    <!-- Debug script to ensure chart is visible -->
    <script>
        // Add a direct event listener to the modal to force the chart to be visible
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for everything else to load
            setTimeout(function() {
                console.log('[Debug] Setting up modal shown event listener');
                // Listen for modal shown event
                document.body.addEventListener('shown.bs.modal', function(event) {
                    console.log('[Debug] Modal shown event detected', event.target.id);
                    // Check if this is the repository modal
                    if (event.target.id === 'repositoryDetailsModal') {
                        console.log('[Debug] Repository modal shown, checking for chart canvas');
                        // Force chart container to be visible
                        const container = document.getElementById('commit-history-container');
                        if (container) {
                            container.style.display = 'block';
                            container.style.height = '300px';
                            console.log('[Debug] Chart container display set to block');
                        }

                        // Check for canvas
                        let canvas = document.getElementById('commit-history-chart');
                        if (!canvas) {
                            console.log('[Debug] Canvas not found, creating it');
                            // Create canvas if it doesn't exist
                            const chartContainer = document.getElementById('commit-history-container');
                            if (chartContainer) {
                                canvas = document.createElement('canvas');
                                canvas.id = 'commit-history-chart';
                                canvas.style.display = 'block';
                                canvas.width = chartContainer.clientWidth || 300;
                                canvas.height = 200;
                                chartContainer.appendChild(canvas);
                                console.log('[Debug] Canvas created and added to DOM');
                            }
                        } else {
                            // Force canvas to be visible
                            canvas.style.display = 'block';
                            console.log('[Debug] Existing canvas display set to block');
                        }

                        // Hide loading indicator
                        const loading = document.getElementById('commit-chart-loading');
                        if (loading) {
                            loading.style.display = 'none';
                            console.log('[Debug] Loading indicator hidden');
                        }
                    }
                });
            }, 1000);
        });
    </script>
    <!-- Debug function for chart visibility -->
    <script>
        // Add a global debug function
        window.debugChart = function() {
            console.log('[Debug] Manual chart debug function called');

            // Get elements
            const container = document.getElementById('commit-history-container');
            const canvas = document.getElementById('commit-history-chart');
            const loading = document.getElementById('commit-chart-loading');

            console.log('[Debug] Elements found:', {
                container: !!container,
                canvas: !!canvas,
                loading: !!loading
            });

            // Force container to be visible
            if (container) {
                container.style.display = 'block';
                container.style.height = '300px';
                console.log('[Debug] Container display set to block');
            }

            // Force loading to be hidden
            if (loading) {
                loading.style.display = 'none';
                console.log('[Debug] Loading display set to none');
            }

            // Check if canvas exists
            if (!canvas && container) {
                // Create canvas
                const newCanvas = document.createElement('canvas');
                newCanvas.id = 'commit-history-chart';
                newCanvas.style.display = 'block';
                newCanvas.width = container.clientWidth || 300;
                newCanvas.height = 200;
                container.appendChild(newCanvas);
                console.log('[Debug] New canvas created');

                // Try to render chart
                if (window.GitRepositoryModal && GitRepositoryModal.state && GitRepositoryModal.state.currentRepository) {
                    const repo = GitRepositoryModal.state.currentRepository;
                    console.log('[Debug] Attempting to render chart for repository:', repo.name);
                    GitRepositoryModal.fetchAndDisplayCommitHistory(repo.name);
                }
            } else if (canvas) {
                // Force canvas to be visible
                canvas.style.display = 'block';
                console.log('[Debug] Canvas display set to block');

                // Check if chart exists
                if (!window.currentChart && window.GitRepositoryModal && GitRepositoryModal.state && GitRepositoryModal.state.currentRepository) {
                    const repo = GitRepositoryModal.state.currentRepository;
                    console.log('[Debug] Attempting to render chart for repository:', repo.name);
                    GitRepositoryModal.fetchAndDisplayCommitHistory(repo.name);
                }
            }

            return 'Debug function executed';
        };
    </script>
</body>
</html>
