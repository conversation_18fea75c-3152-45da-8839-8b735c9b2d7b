import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Projects API
export const getProjects = () => api.get('/projects');
export const getProject = (id: string) => api.get(`/projects/${id}`);
export const createProject = (data: any) => api.post('/projects', data);
export const updateProject = (id: string, data: any) => api.put(`/projects/${id}`, data);
export const deleteProject = (id: string) => api.delete(`/projects/${id}`);

// Git Operations API
export const getGitOperations = () => api.get('/git/operations');
export const getGitOperation = (id: string) => api.get(`/git/operations/${id}`);
export const getGitPerformance = () => api.get('/git/performance');

// Authentication API
export const login = (credentials: { username: string; password: string }) => 
  api.post('/auth/login', credentials);
export const register = (userData: any) => api.post('/auth/register', userData);
export const getCurrentUser = () => api.get('/auth/user');

export default api;
