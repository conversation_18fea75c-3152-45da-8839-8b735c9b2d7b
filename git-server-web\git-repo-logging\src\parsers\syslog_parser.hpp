#pragma once
#include "parser_interface.hpp"
#include <regex>

namespace logging {

class SyslogParser : public LogParser {
public:
    SyslogParser();
    ~SyslogParser() = default;
    
    // LogParser interface implementation
    std::optional<LogEntry> parse(const std::string& line, const std::string& source) override;
    bool canParse(const std::string& line) override;
    std::string getName() const override { return "syslog"; }
    std::string getDescription() const override { return "Parser for standard syslog format"; }
    
private:
    // Regular expressions for different syslog formats
    std::regex traditionalFormat_;
    std::regex rfc3164Format_;
    std::regex rfc5424Format_;
};

} // namespace logging
