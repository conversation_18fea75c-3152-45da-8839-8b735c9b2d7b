// Simple script to check for JavaScript errors
const puppeteer = require('puppeteer');

(async () => {
  console.log('Starting browser...');
  const browser = await puppeteer.launch();
  console.log('Opening page...');
  const page = await browser.newPage();
  
  // Capture console logs
  page.on('console', msg => console.log('BROWSER CONSOLE:', msg.text()));
  page.on('pageerror', error => console.log('PAGE ERROR:', error.message));
  
  try {
    console.log('Navigating to git.chcit.org...');
    await page.goto('http://git.chcit.org/', { waitUntil: 'networkidle2' });
    console.log('Page loaded successfully');
    
    // Take a screenshot
    await page.screenshot({path: 'git-dashboard.png'});
    console.log('Screenshot saved as git-dashboard.png');
  } catch (error) {
    console.error('Error loading page:', error);
  }
  
  await browser.close();
})();
