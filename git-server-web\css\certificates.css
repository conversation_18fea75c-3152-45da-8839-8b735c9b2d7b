/* Raw certificate data button styling */
.raw-cert-btn {
    color: #6c757d;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.raw-cert-btn:hover {
    color: #495057;
    opacity: 1;
}

.raw-cert-btn:focus {
    box-shadow: none;
    outline: none;
}

/* Ensure the button doesn't affect layout */
.raw-cert-btn i {
    font-size: 1rem;
}

/* Only show the button if cert_info exists */
.raw-cert-btn {
    display: none;
}

.certificate-details .card.position-relative[data-has-cert-info="true"] .raw-cert-btn {
    display: block;
}

/* Make column spacing more consistent */
.card-body .row .col-md-4 {
    padding: 0 20px;
}

/* Ensure columns have equal height */
.card-body .row {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
}

/* Add consistent spacing to headings */
.card-body h6.text-muted {
    margin-top: 0;
    margin-bottom: 12px;
}

/* Make tables more compact */
.table-condensed td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

/* Domain items styling */
.domain-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.domain-item {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px 12px;
}

.domain-text {
    margin-right: 8px;
}

.domain-badge {
    padding: 3px 6px;
    font-size: 10px;
}

/* certificates.css - Certificate management styles */

/* Reset padding and margin for certificates tab */
#certificates .card,
#certificates .card-body,
#certStatus {
    width: 100%;
    box-sizing: border-box;
}

/* Make the certificate tab and content take full height */
#certificates {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

#certificates .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0;
    height: 100%;
}

#certificates .card-body {
    flex: 1;
    overflow-y: auto;
    display: flex;
    align-items: flex-start;
    padding: 1rem;
}

/* Container for certificate content */
.certificate-container {
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* Full-height container for certificate status */
#certStatus {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

/* Custom styling for the certificate details section */
.certificate-details {
    margin-top: 0.5rem;
}

/* Style the details section cards with consistent margins */
.certificate-details .card {
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.certificate-details .card:last-child {
    margin-bottom: 0;
}

/* Style card headers in certificate details */
.certificate-details .card-header {
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
    font-weight: 500;
}

/* Domain list styling */
.certificate-details .list-group-item {
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Raw certificate data styling */
.certificate-details pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
}

/* Certificate status badge styling */
.certificate-details .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Certificate summary styling */
.cert-header, .cert-expiry, .cert-actions {
    width: 100%;
    display: block;
    clear: both;
}

/* Smaller text for expiry information */
.smaller-text {
    font-size: 0.9rem;
}

/* Ensure the button is properly aligned to the right */
.cert-actions {
    text-align: right;
}

.cert-actions .btn {
    min-width: 150px;
}

/* Status message styling */
#status-message-container {
    min-height: 24px;
    text-align: right;
}

#cert-sync-status-message {
    font-size: 0.9rem;
    padding: 4px 8px;
    display: inline-block;
}

/* Ensure certificate details take full available space */
.certificate-details .col-md-4 {
    display: flex;
    flex-direction: column;
}

.certificate-details .col-md-4 > div {
    flex-grow: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #certificates {
        min-height: 500px;
    }
    
    /* Stack the layout on mobile */
    .certificate-details .row {
        flex-direction: column;
    }
    
    .certificate-details .col-md-4 {
        width: 100%;
        margin-bottom: 1rem;
        border-right: none !important;
        border-bottom: 1px solid #dee2e6;
    }
    
    /* Adjust sync button positioning on mobile */
    #certStatus .card-body .d-flex {
        flex-direction: column;
        align-items: flex-start;
    }
    
    #certStatus .card-body .d-flex > div:last-child {
        margin-top: 1rem;
        width: 100%;
    }
}
