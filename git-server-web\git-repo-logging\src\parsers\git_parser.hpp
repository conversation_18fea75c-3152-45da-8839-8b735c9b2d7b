#pragma once
#include "parser_interface.hpp"
#include <regex>

namespace logging {

class GitParser : public LogParser {
public:
    GitParser();
    ~GitParser() = default;
    
    // LogParser interface implementation
    std::optional<LogEntry> parse(const std::string& line, const std::string& source) override;
    bool canParse(const std::string& line) override;
    std::string getName() const override { return "git"; }
    std::string getDescription() const override { return "Parser for Git log format"; }
    
private:
    // Regular expressions for different Git log formats
    std::regex commitLogFormat_;  // For git log output
    std::regex operationLogFormat_; // For git operation logs
};

} // namespace logging
