# Fix Git Modules Script

# Configurations
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"
$LocalProjectPath = "D:/Codeium/CHCIT/project-tracker/git-server-web"

Write-Host "Starting Git Module Fix..." -ForegroundColor Yellow

# 1. First, check if all required files exist on the server
Write-Host "1. Checking files on server..." -ForegroundColor Cyan
$jsFiles = @(
    "git-utils.js",
    "git-commit-history-chart.js",
    "git-size-comparison-chart.js",
    "git-repository-list.js",
    "git-repository-modal.js",
    "git-repository-manager.js"
)

$allExist = $true
foreach ($file in $jsFiles) {
    $exists = ssh "${RemoteUser}@${RemoteHost}" "test -f ${RemotePath}/js/${file} && echo 'exists' || echo 'missing'"
    if ($exists -eq "exists") {
        Write-Host "  ✓ ${file} found" -ForegroundColor Green
    } else {
        Write-Host "  ✗ ${file} MISSING" -ForegroundColor Red
        $allExist = $false
    }
}

if (-not $allExist) {
    Write-Host "Some files are missing! Redeploying all JS files..." -ForegroundColor Red
    
    foreach ($file in $jsFiles) {
        Write-Host "  - Deploying ${file}..." -ForegroundColor Cyan
        Get-Content -Path "${LocalProjectPath}/js/${file}" -Raw | 
        ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/${file} && sudo mv /tmp/${file} ${RemotePath}/js/${file} && sudo chown www-data:www-data ${RemotePath}/js/${file} && sudo chmod 644 ${RemotePath}/js/${file}"
    }
}

# 2. Fix createSummaryContainer in git-repository-manager.js
Write-Host "2. Checking and fixing createSummaryContainer in git-repository-manager.js..." -ForegroundColor Cyan
$managerContent = Get-Content -Path "${LocalProjectPath}/js/git-repository-manager.js" -Raw

# Check if there's a potential issue with createSummaryContainer
if ($managerContent -match "createSummaryContainer") {
    Write-Host "  Found createSummaryContainer method - ensuring DOM element exists" -ForegroundColor Green
    
    # Check index.html for #git-summary-container
    $indexContent = Get-Content -Path "${LocalProjectPath}/index.html" -Raw
    if (-not ($indexContent -match "git-summary-container")) {
        Write-Host "  Adding git-summary-container to index.html..." -ForegroundColor Yellow
        $updatedIndex = $indexContent -replace '<div class="repository-content-container" id="repository-content">',
                                           '<div class="repository-content-container" id="repository-content">
            <div id="git-summary-container" class="mb-4"></div>'
        
        # Update index.html locally
        Set-Content -Path "${LocalProjectPath}/index.html" -Value $updatedIndex
        
        # Deploy updated index.html
        Get-Content -Path "${LocalProjectPath}/index.html" -Raw | 
        ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/index.html && sudo mv /tmp/index.html ${RemotePath}/index.html && sudo chown www-data:www-data ${RemotePath}/index.html && sudo chmod 644 ${RemotePath}/index.html"
    } else {
        Write-Host "  git-summary-container already exists in index.html" -ForegroundColor Green
    }
}

# 3. Update git-utils.js to ensure it has required utility functions
Write-Host "3. Checking git-utils.js for required functions..." -ForegroundColor Cyan
$utilsContent = Get-Content -Path "${LocalProjectPath}/js/git-utils.js" -Raw

# 4. Check repository list initialization
Write-Host "4. Checking repository list initialization..." -ForegroundColor Cyan
$listContent = Get-Content -Path "${LocalProjectPath}/js/git-repository-list.js" -Raw

# 5. Create a consolidated.js file with all modules for testing
Write-Host "5. Creating consolidated git-repositories-debug.js file..." -ForegroundColor Cyan

$consolidatedContent = @"
/**
 * Consolidated Git Repositories Module (Debug Version)
 * This file contains all git repository modules for easier debugging
 */

// GitUtils
$utilsContent

// GitCommitHistoryChart
$(Get-Content -Path "${LocalProjectPath}/js/git-commit-history-chart.js" -Raw)

// GitSizeComparisonChart
$(Get-Content -Path "${LocalProjectPath}/js/git-size-comparison-chart.js" -Raw)

// GitRepositoryList
$listContent

// GitRepositoryModal
$(Get-Content -Path "${LocalProjectPath}/js/git-repository-modal.js" -Raw)

// GitRepositoryManager
$managerContent

// Debug initialization code
document.addEventListener('DOMContentLoaded', () => {
    console.log('Debug consolidated Git module loaded');
    setTimeout(() => {
        // Initialize components manually
        try {
            console.log('Initializing GitRepositoryManager...');
            GitRepositoryManager.init();
            console.log('GitRepositoryManager initialized successfully');
        } catch (error) {
            console.error('Error initializing GitRepositoryManager:', error);
        }
    }, 1000);
});
"@

# Save locally
Set-Content -Path "${LocalProjectPath}/js/git-repositories-debug.js" -Value $consolidatedContent

# Deploy to server
Get-Content -Path "${LocalProjectPath}/js/git-repositories-debug.js" -Raw | 
ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/git-repositories-debug.js && sudo mv /tmp/git-repositories-debug.js ${RemotePath}/js/git-repositories-debug.js && sudo chown www-data:www-data ${RemotePath}/js/git-repositories-debug.js && sudo chmod 644 ${RemotePath}/js/git-repositories-debug.js"

# 6. Create a test HTML file that uses the consolidated version
Write-Host "6. Creating test.html with consolidated script..." -ForegroundColor Cyan

$testHtmlContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git Repository Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/repositories.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.2.1/dist/chart.umd.min.js"></script>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h1>Git Repository Test Page</h1>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Repository Summary</h5>
                <div id="git-summary-container"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Repository List</h5>
                <div id="repository-content">
                    <div id="repository-list-container"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal for Repository Details -->
    <div class="modal fade" id="repositoryDetailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Repository Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="repository-details-container">
                    <!-- Content will be added dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Consolidated Git Repository Scripts -->
    <script src="/js/git-repositories-debug.js"></script>
    
    <!-- Debug Script -->
    <script src="/js/git-debug.js"></script>
</body>
</html>
"@

# Save locally
Set-Content -Path "${LocalProjectPath}/test.html" -Value $testHtmlContent

# Deploy to server
Get-Content -Path "${LocalProjectPath}/test.html" -Raw | 
ssh "${RemoteUser}@${RemoteHost}" "cat > /tmp/test.html && sudo mv /tmp/test.html ${RemotePath}/test.html && sudo chown www-data:www-data ${RemotePath}/test.html && sudo chmod 644 ${RemotePath}/test.html"

# 7. Check for common errors in JavaScript files
Write-Host "7. Checking for common JavaScript errors..." -ForegroundColor Cyan

# Check for missing dependencies in git-repository-manager.js
$managerDependencies = @(
    "GitRepositoryList",
    "GitRepositoryModal"
)

foreach ($dep in $managerDependencies) {
    if ($managerContent -match "${dep}.init\(this\)") {
        Write-Host "  ✓ Found initialization of $dep" -ForegroundColor Green
    } else {
        Write-Host "  ✗ Missing initialization of $dep" -ForegroundColor Red
    }
}

# 8. Restart services
Write-Host "8. Restarting services..." -ForegroundColor Cyan
ssh "${RemoteUser}@${RemoteHost}" "sudo systemctl restart git-dashboard.service && sudo systemctl restart nginx"

Write-Host "Fix completed! Please try accessing the test page at http://git.chcit.org/test.html" -ForegroundColor Green
