# Architecture Diagram

```mermaid
graph TD
    subgraph "Front-end"
        A[Nginx] --> B[Git Dashboard]
    end
    
    subgraph "C++23 Applications"
        C[Git Repository Service]
        D[Logging Service]
        E[Database Service]
    end
    
    subgraph "Storage"
        F[(PostgreSQL Databases)]
        G[Git Repositories]
        H[Log Files]
    end
    
    B --> C
    B --> D
    B --> E
    
    C --> E
    D --> E
    
    C --> G
    D --> H
    E --> F
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px
    style D fill:#bfb,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#fbb,stroke:#333,stroke-width:2px
    style G fill:#fbb,stroke:#333,stroke-width:2px
    style H fill:#fbb,stroke:#333,stroke-width:2px
```

# Component Interaction Diagram

```mermaid
sequenceDiagram
    participant User
    participant Dashboard as Git Dashboard
    participant GitService as Git Repository Service
    participant LogService as Logging Service
    participant DBService as Database Service
    participant DB as PostgreSQL
    
    User->>Dashboard: Access Dashboard
    Dashboard->>GitService: Get Repositories
    GitService->>DBService: Query Repository Data
    DBService->>DB: Execute SQL Query
    DB-->>DBService: Return Results
    DBService-->>GitService: Return Repository Data
    GitService-->>Dashboard: Return Repositories
    Dashboard-->>User: Display Repositories
    
    User->>Dashboard: View Repository Details
    Dashboard->>GitService: Get Repository Details
    GitService->>DBService: Query Repository Details
    DBService->>DB: Execute SQL Query
    DB-->>DBService: Return Results
    DBService-->>GitService: Return Repository Details
    GitService-->>Dashboard: Return Repository Details
    Dashboard-->>User: Display Repository Details
    
    User->>Dashboard: View Logs
    Dashboard->>LogService: Get Logs
    LogService->>DBService: Query Log Data
    DBService->>DB: Execute SQL Query
    DB-->>DBService: Return Results
    DBService-->>LogService: Return Log Data
    LogService-->>Dashboard: Return Logs
    Dashboard-->>User: Display Logs
```

# Database Architecture Diagram

```mermaid
erDiagram
    POSTGRESQL-SERVER ||--o{ DATABASE : contains
    DATABASE ||--o{ SCHEMA : contains
    SCHEMA ||--o{ TABLE : contains
    
    DATABASE {
        string name
        string owner
        string encoding
    }
    
    SCHEMA {
        string name
        string owner
    }
    
    TABLE {
        string name
        string schema
        string columns
        string constraints
    }
    
    DATABASE ||--|| DATABASE-SERVICE : "central management"
    DATABASE ||--|| GIT-REPO-SERVICE : "repository data"
    DATABASE ||--|| LOGGING-SERVICE : "log data"
    
    DATABASE-SERVICE {
        string name "database_service"
    }
    
    GIT-REPO-SERVICE {
        string name "git_repo_db"
    }
    
    LOGGING-SERVICE {
        string name "logging_db"
    }
```

# Multi-Database Architecture

```mermaid
graph TD
    subgraph "PostgreSQL Server"
        subgraph "database_service"
            A1[schema_version]
            A2[applications]
            A3[users]
            A4[permissions]
        end
        
        subgraph "git_repo_db"
            subgraph "public schema"
                B1[repositories]
                B2[commits]
                B3[branches]
            end
            
            subgraph "metadata schema"
                B4[repository_stats]
                B5[user_activity]
            end
        end
        
        subgraph "logging_db"
            subgraph "public schema"
                C1[logs]
                C2[log_levels]
            end
            
            subgraph "archive schema"
                C3[historical_logs]
            end
            
            subgraph "stats schema"
                C4[log_statistics]
                C5[error_trends]
            end
        end
    end
    
    style A1 fill:#bbf,stroke:#333,stroke-width:1px
    style A2 fill:#bbf,stroke:#333,stroke-width:1px
    style A3 fill:#bbf,stroke:#333,stroke-width:1px
    style A4 fill:#bbf,stroke:#333,stroke-width:1px
    
    style B1 fill:#bfb,stroke:#333,stroke-width:1px
    style B2 fill:#bfb,stroke:#333,stroke-width:1px
    style B3 fill:#bfb,stroke:#333,stroke-width:1px
    style B4 fill:#bfb,stroke:#333,stroke-width:1px
    style B5 fill:#bfb,stroke:#333,stroke-width:1px
    
    style C1 fill:#fbb,stroke:#333,stroke-width:1px
    style C2 fill:#fbb,stroke:#333,stroke-width:1px
    style C3 fill:#fbb,stroke:#333,stroke-width:1px
    style C4 fill:#fbb,stroke:#333,stroke-width:1px
    style C5 fill:#fbb,stroke:#333,stroke-width:1px
```

# Logging Service Architecture

```mermaid
graph TD
    subgraph "Logging Service"
        A[API Server]
        B[Log Processor]
        C[Storage Manager]
    end
    
    subgraph "Storage Tiers"
        D[Memory Cache]
        E[File Storage]
        F[Database Storage]
    end
    
    subgraph "Database"
        G[Current Logs]
        H[Archived Logs]
        I[Statistics]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    F --> G
    F --> H
    F --> I
    
    style A fill:#bbf,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bfb,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
    style G fill:#fbb,stroke:#333,stroke-width:2px
    style H fill:#fbb,stroke:#333,stroke-width:2px
    style I fill:#fbb,stroke:#333,stroke-width:2px
```

# Git Repository Service Architecture

```mermaid
graph TD
    subgraph "Git Repository Service"
        A[API Server]
        B[Repository Manager]
        C[Git Operations]
        D[Statistics Collector]
    end
    
    subgraph "Storage"
        E[Git Repositories]
        F[Database]
    end
    
    subgraph "Database"
        G[Repository Metadata]
        H[User Access]
        I[Statistics]
    end
    
    A --> B
    A --> D
    B --> C
    C --> E
    B --> F
    D --> F
    F --> G
    F --> H
    F --> I
    
    style A fill:#bbf,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
    style G fill:#fbb,stroke:#333,stroke-width:2px
    style H fill:#fbb,stroke:#333,stroke-width:2px
    style I fill:#fbb,stroke:#333,stroke-width:2px
```

# Database Service Architecture

```mermaid
graph TD
    subgraph "Database Service"
        A[API Server]
        B[Connection Manager]
        C[Schema Manager]
        D[Query Processor]
        E[Security Manager]
    end
    
    subgraph "PostgreSQL"
        F[Central Management DB]
        G[Application DBs]
        H[Schemas]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    B --> F
    B --> G
    C --> H
    D --> B
    E --> B
    
    style A fill:#bbf,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
    style H fill:#bfb,stroke:#333,stroke-width:2px
```

# Deployment Architecture

```mermaid
graph TD
    subgraph "Server"
        A[Nginx]
        B[Git Dashboard]
        C[Git Repository Service]
        D[Logging Service]
        E[Database Service]
        F[PostgreSQL]
    end
    
    subgraph "File System"
        G[/opt/git-dashboard/]
        H[/etc/letsencrypt/]
        I[/var/lib/postgresql/]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    
    B --> G
    C --> G
    D --> G
    E --> G
    
    C --> F
    D --> F
    E --> F
    
    F --> I
    
    A --> H
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bfb,stroke:#333,stroke-width:2px
    style D fill:#bfb,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#fbb,stroke:#333,stroke-width:2px
    style G fill:#ddd,stroke:#333,stroke-width:2px
    style H fill:#ddd,stroke:#333,stroke-width:2px
    style I fill:#ddd,stroke:#333,stroke-width:2px
```

