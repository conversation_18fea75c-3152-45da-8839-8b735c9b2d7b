/**
 * Git Repository Module
 * Handles fetching and displaying git repository information
 */
const GitRepositoryManager = {
    // Configuration
    config: {
        endpoints: {
            repositories: '/api/git/repositories',
            commitHistory: '/api/git/repository/{repo_name}/commits'
        },
        refreshInterval: 60000, // 60 seconds (less frequent than main dashboard)
        sortOptions: {
            name: { label: 'Name', key: 'name' },
            lastCommit: { label: 'Last Commit', key: 'last_commit' },
            branches: { label: 'Branches', key: 'branches' },
            size: { label: 'Size', key: 'size' }
        },
        defaultSort: 'name',
        commitHistoryColors: {
            background: 'rgba(54, 162, 235, 0.2)',
            border: 'rgba(54, 162, 235, 1)'
        },
        commitHistoryDays: 30 // Number of days to show in commit history
    },
    
    // Application state
    state: {
        repositoriesData: null,
        lastUpdated: null,
        status: 'loading',
        refreshTimer: null,
        sortBy: 'name',
        sortDirection: 'asc',
        filterText: '',
        selectedRepository: null,
        commitHistoryChart: null,
        sizeComparisonChart: null,
        useFallbackData: localStorage.getItem('git_use_fallback_data') === 'true'
    },
    
    /**
     * Initialize the Git Repository module
     */
    init() {
        console.log('Initializing Git Repository Manager...');
        
        // Log initialization if logger is available
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Git Repository Manager initialized', 'git');
        }
        
        // Create repository list container
        this.createRepositoryListContainer();
        
        // Create repository details modal
        this.createRepositoryDetailsModal();
        
        // Fetch initial data
        this.fetchRepositories();
        
        // Set up auto-refresh
        this.state.refreshTimer = setInterval(() => {
            this.fetchRepositories();
        }, this.config.refreshInterval);
        
        // Listen for dashboard refresh button clicks
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.fetchRepositories();
            });
        }
        
        // Add global click handler to fix modal backdrop issues
        document.addEventListener('click', (e) => {
            // If clicking on the main content area (not within a modal)
            if (!e.target.closest('.modal-content') && !e.target.closest('.modal-dialog')) {
                // Check if there's a lingering backdrop but no visible modal
                const backdrop = document.querySelector('.modal-backdrop');
                const visibleModal = document.querySelector('.modal.show');
                
                if (backdrop && !visibleModal) {
                    console.log('Detected lingering backdrop without visible modal, fixing...');
                    this.fixModalBackdropIssues();
                }
            }
        });
        
        return this;
    },
    
    /**
     * Create repository list container
     */
    createRepositoryListContainer() {
        const gitContainer = document.getElementById('git-repository-container');
        if (!gitContainer) {
            console.error('Git repository container not found');
            return;
        }
        
        // Create list container for the repository list view
        const listContainer = document.createElement('div');
        listContainer.className = 'git-list-container';
        listContainer.style.display = 'block'; // Always visible now
        
        // Create search and filter controls
        const controlsRow = document.createElement('div');
        controlsRow.className = 'row mb-3';
        controlsRow.innerHTML = `
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control form-control-sm" placeholder="Search repositories..." id="repo-search">
                    <button class="btn btn-sm btn-outline-secondary" type="button"><i class="fas fa-search"></i></button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Sort: <span id="current-sort">Name</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" id="sort-options">
                        <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="lastCommit">Last Commit</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="branches">Branches</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="size">Size</a></li>
                    </ul>
                </div>
            </div>
        `;
        
        // Create repositories table
        const tableContainer = document.createElement('div');
        tableContainer.className = 'table-responsive';
        tableContainer.innerHTML = `
            <table class="table table-hover table-sm">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Branches</th>
                        <th>Last Commit</th>
                        <th>Size</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="repositories-table-body">
                    <tr>
                        <td colspan="5" class="text-center">Loading repositories...</td>
                    </tr>
                </tbody>
            </table>
        `;
        
        // Add controls and table to list container
        listContainer.appendChild(controlsRow);
        listContainer.appendChild(tableContainer);
        
        // Add list container to container
        gitContainer.innerHTML = ''; // Clear the container first
        gitContainer.appendChild(listContainer);
        
        // Add event listeners for search and sort
        const searchInput = listContainer.querySelector('#repo-search');
        searchInput.addEventListener('input', (e) => {
            this.state.filterText = e.target.value;
            this.renderRepositoryList();
        });
        
        const sortOptions = listContainer.querySelector('#sort-options');
        sortOptions.addEventListener('click', (e) => {
            if (e.target.tagName === 'A') {
                e.preventDefault();
                const sortKey = e.target.getAttribute('data-sort');
                if (sortKey === this.state.sortBy) {
                    // Toggle sort direction
                    this.state.sortDirection = this.state.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    this.state.sortBy = sortKey;
                    this.state.sortDirection = 'asc';
                }
                
                // Update sort display
                const currentSortEl = listContainer.querySelector('#current-sort');
                currentSortEl.textContent = this.config.sortOptions[sortKey].label;
                
                this.renderRepositoryList();
            }
        });
    },
    
    /**
     * Create repository details modal
     */
    createRepositoryDetailsModal() {
        console.log('Creating repository details modal');
        
        // Create modal container
        const modalContainer = document.createElement('div');
        modalContainer.id = 'repository-details-modal';
        modalContainer.className = 'modal fade';
        modalContainer.setAttribute('tabindex', '-1');
        modalContainer.setAttribute('aria-labelledby', 'repositoryDetailsModalLabel');
        modalContainer.setAttribute('aria-hidden', 'true');
        modalContainer.setAttribute('data-bs-backdrop', 'static'); // Prevent closing when clicking outside
        
        modalContainer.innerHTML = `
            <div class="modal-dialog modal-xl modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="repositoryDetailsModalLabel">Repository Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>Basic Information</h6>
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Name:</span>
                                        <span id="modal-repo-name" class="fw-bold">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Description:</span>
                                        <span id="modal-repo-description" class="text-muted">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Branches:</span>
                                        <span id="modal-repo-branches">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Size:</span>
                                        <span id="modal-repo-size">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Last Commit:</span>
                                        <span id="modal-repo-last-commit">-</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Repository Health</h6>
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>Health Score:</span>
                                            <span id="modal-repo-health" class="health-status">-</span>
                                        </div>
                                        <div class="progress mb-3">
                                            <div id="modal-repo-health-progress" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <p id="modal-repo-health-message" class="health-message small mb-0">-</p>
                                    </div>
                                </div>
                                <div class="card mt-3">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>Total Commits:</span>
                                            <span id="total-commits-value" class="fw-bold">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <ul class="nav nav-tabs" id="repoDetailsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="commit-history-tab" data-bs-toggle="tab" data-bs-target="#commit-history" type="button" role="tab" aria-controls="commit-history" aria-selected="true">Commit History</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="size-comparison-tab" data-bs-toggle="tab" data-bs-target="#size-comparison" type="button" role="tab" aria-controls="size-comparison" aria-selected="false">Size Comparison</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="repoDetailsTabsContent">
                            <div class="tab-pane fade show active" id="commit-history" role="tabpanel" aria-labelledby="commit-history-tab">
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="commit-history-chart"></canvas>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="size-comparison" role="tabpanel" aria-labelledby="size-comparison-tab">
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="size-comparison-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to body
        document.body.appendChild(modalContainer);
        
        // Clear charts when modal is hidden
        modalContainer.addEventListener('hidden.bs.modal', (event) => {
            console.log('Modal hidden event triggered, cleaning up');
            
            // Destroy charts
            if (this.state.commitHistoryChart) {
                this.state.commitHistoryChart.destroy();
                this.state.commitHistoryChart = null;
            }

            if (this.state.sizeComparisonChart) {
                this.state.sizeComparisonChart.destroy();
                this.state.sizeComparisonChart = null;
            }
            
            // Reset selected repository
            this.state.selectedRepository = null;
            
            // Fix any modal backdrop issues
            this.fixModalBackdropIssues();
        });
        
        // Add event listener for repository details buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('view-details-btn') || 
                e.target.closest('.view-details-btn')) {
                const button = e.target.classList.contains('view-details-btn') ? 
                               e.target : e.target.closest('.view-details-btn');
                const repoName = button.getAttribute('data-repo-name');
                const repo = this.state.repositoriesData.repositories.find(r => r.name === repoName);
                if (repo) {
                    this.showRepositoryDetails(repo);
                }
            }
        });
    },
    
    /**
     * Render repository list
     */
    renderRepositoryList() {
        const tableBody = document.getElementById('repositories-table-body');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        if (!this.state.repositoriesData || !this.state.repositoriesData.repositories) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No repository data available</td></tr>';
            return;
        }
        
        const repositories = [...this.state.repositoriesData.repositories];
        
        // Filter repositories
        const filteredRepositories = repositories.filter(repo => {
            if (!this.state.filterText) return true;
            const filterText = this.state.filterText.toLowerCase();
            return repo.name.toLowerCase().includes(filterText);
        });
        
        // Sort repositories
        filteredRepositories.sort((a, b) => {
            const sortKey = this.state.sortBy === 'lastCommit' ? 'last_commit' : 
                           (this.state.sortBy === 'branches' ? 'branches' : 
                           (this.state.sortBy === 'size' ? 'size' : 'name'));
            
            // Handle missing values
            const aValue = a[sortKey] || '';
            const bValue = b[sortKey] || '';
            
            // For dates, convert to timestamp for comparison
            if (sortKey === 'last_commit' && aValue && bValue) {
                const aDate = new Date(aValue).getTime();
                const bDate = new Date(bValue).getTime();
                return this.state.sortDirection === 'asc' ? aDate - bDate : bDate - aDate;
            }
            
            // For strings and numbers
            if (aValue < bValue) {
                return this.state.sortDirection === 'asc' ? -1 : 1;
            } else if (aValue > bValue) {
                return this.state.sortDirection === 'asc' ? 1 : -1;
            } else {
                return 0;
            }
        });
        
        // Display repositories
        if (filteredRepositories.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No repositories match your search</td></tr>';
            return;
        }
        
        filteredRepositories.forEach(repo => {
            const row = document.createElement('tr');
            
            // Format date
            let lastCommitDisplay = 'Never';
            if (repo.last_commit) {
                const commitDate = new Date(repo.last_commit);
                lastCommitDisplay = commitDate.toLocaleString();
            }
            
            // Create repository health indicator
            const healthClass = this.getRepositoryHealthClass(repo);
            
            row.innerHTML = `
                <td>
                    <span class="repo-health-indicator ${healthClass}"></span>
                    ${repo.name}
                </td>
                <td>${repo.branches || 'N/A'}</td>
                <td>${lastCommitDisplay}</td>
                <td>${repo.size || 'N/A'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary view-details-btn" data-repo-name="${repo.name}">
                        <i class="fas fa-info-circle"></i> Details
                    </button>
                </td>
            `;
            
            // Add click event for details button
            const detailsBtn = row.querySelector('.view-details-btn');
            detailsBtn.addEventListener('click', () => {
                this.showRepositoryDetails(repo);
            });
            
            tableBody.appendChild(row);
        });
    },
    
    /**
     * Get repository health class based on activity and other factors
     * @param {Object} repo - Repository object
     * @returns {string} CSS class for health indicator
     */
    getRepositoryHealthClass(repo) {
        if (!repo.last_commit) {
            return 'health-unknown';
        }
        
        const lastCommitDate = new Date(repo.last_commit);
        const now = new Date();
        const daysSinceLastCommit = Math.floor((now - lastCommitDate) / (1000 * 60 * 60 * 24));
        
        if (daysSinceLastCommit <= 7) {
            return 'health-good'; // Active in the last week
        } else if (daysSinceLastCommit <= 30) {
            return 'health-fair'; // Active in the last month
        } else {
            return 'health-poor'; // Inactive for more than a month
        }
    },
    
    /**
     * Calculate repository health metrics based on activity and other factors
     * @param {Object} repo - Repository object
     * @returns {Object} Health metrics object with percentage, status, and message
     */
    calculateRepositoryHealth(repo) {
        if (!repo.last_commit) {
            return {
                percentage: 0,
                status: 'secondary',
                message: 'No commit history available for this repository.'
            };
        }
        
        const lastCommitDate = new Date(repo.last_commit);
        const now = new Date();
        const daysSinceLastCommit = Math.floor((now - lastCommitDate) / (1000 * 60 * 60 * 24));
        
        // Calculate health percentage (0-100)
        let percentage = 0;
        let status = 'danger';
        let message = '';
        
        if (daysSinceLastCommit <= 7) {
            // Active in the last week - 70-100%
            percentage = 100 - (daysSinceLastCommit * 5);
            status = 'success';
            message = `Active repository with recent commits (${daysSinceLastCommit} days ago).`;
        } else if (daysSinceLastCommit <= 30) {
            // Active in the last month - 40-70%
            percentage = 70 - ((daysSinceLastCommit - 7) * 1.3);
            status = 'warning';
            message = `Moderately active repository (${daysSinceLastCommit} days since last commit).`;
        } else if (daysSinceLastCommit <= 90) {
            // Inactive for 1-3 months - 10-40%
            percentage = 40 - ((daysSinceLastCommit - 30) / 2);
            status = 'danger';
            message = `Inactive repository (${daysSinceLastCommit} days since last commit).`;
        } else {
            // Inactive for more than 3 months - 0-10%
            percentage = 10;
            status = 'danger';
            message = `Stale repository (${daysSinceLastCommit} days since last commit).`;
        }
        
        // Ensure percentage is within 0-100 range
        percentage = Math.max(0, Math.min(100, Math.round(percentage)));
        
        return {
            percentage,
            status,
            message
        };
    },
    
    /**
     * Show repository details in modal
     * @param {Object} repo - Repository object
     */
    showRepositoryDetails(repo) {
        if (!repo) {
            console.error('No repository provided for details');
            return;
        }
        
        console.log('Showing details for repository:', repo.name);
        this.state.selectedRepository = repo;
        
        // Update modal title
        const modalTitle = document.getElementById('repositoryDetailsModalLabel');
        if (modalTitle) {
            modalTitle.textContent = `${repo.name} Details`;
        }
        
        // Update repository info
        const repoNameEl = document.getElementById('modal-repo-name');
        const repoDescriptionEl = document.getElementById('modal-repo-description');
        const repoBranchesEl = document.getElementById('modal-repo-branches');
        const repoSizeEl = document.getElementById('modal-repo-size');
        const repoLastCommitEl = document.getElementById('modal-repo-last-commit');
        const repoHealthEl = document.getElementById('modal-repo-health');
        const repoHealthProgressEl = document.getElementById('modal-repo-health-progress');
        const repoHealthMessageEl = document.getElementById('modal-repo-health-message');
        
        if (repoNameEl) repoNameEl.textContent = repo.name || 'N/A';
        if (repoDescriptionEl) repoDescriptionEl.textContent = repo.description || 'No description available';
        if (repoBranchesEl) repoBranchesEl.textContent = repo.branches || 'N/A';
        if (repoSizeEl) repoSizeEl.textContent = repo.size_formatted || 'N/A';
        if (repoLastCommitEl) {
            if (repo.last_commit) {
                const date = new Date(repo.last_commit);
                repoLastCommitEl.textContent = date.toLocaleString();
            } else {
                repoLastCommitEl.textContent = 'N/A';
            }
        }
        
        // Calculate and display repository health
        if (repoHealthEl || repoHealthProgressEl || repoHealthMessageEl) {
            const health = this.calculateRepositoryHealth(repo);
            
            if (repoHealthEl) {
                repoHealthEl.textContent = `${health.percentage}%`;
                repoHealthEl.className = `health-status ${health.status}`;
            }
            
            if (repoHealthProgressEl) {
                repoHealthProgressEl.style.width = `${health.percentage}%`;
                repoHealthProgressEl.className = `progress-bar ${health.status}`;
                repoHealthProgressEl.setAttribute('aria-valuenow', health.percentage);
            }
            
            if (repoHealthMessageEl) {
                repoHealthMessageEl.textContent = health.message;
                repoHealthMessageEl.className = `health-message ${health.status}`;
            }
        }
        
        // Get the modal element
        const modalElement = document.getElementById('repository-details-modal');
        
        // Clean up any existing charts before creating new ones
        this.cleanupCharts();
        
        // Set up event listeners for tab changes
        const commitHistoryTab = document.getElementById('commit-history-tab');
        const sizeComparisonTab = document.getElementById('size-comparison-tab');
        
        if (commitHistoryTab) {
            commitHistoryTab.addEventListener('shown.bs.tab', () => {
                console.log('Commit history tab shown');
                this.renderCommitHistoryChart();
            });
        }
        
        if (sizeComparisonTab) {
            sizeComparisonTab.addEventListener('shown.bs.tab', () => {
                console.log('Size comparison tab shown');
                this.renderSizeComparisonChart();
            });
        }
        
        // Show the modal
        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.show();
        
        // Render commit history chart by default
        this.renderCommitHistoryChart();
    },
    
    /**
     * Clean up all chart instances
     */
    cleanupCharts() {
        console.log('Cleaning up chart instances');
        
        try {
            // Clean up commit history chart
            const commitHistoryCanvas = document.getElementById('commit-history-chart');
            if (commitHistoryCanvas) {
                const existingCommitChart = Chart.getChart(commitHistoryCanvas);
                if (existingCommitChart) {
                    console.log('Destroying existing commit history chart');
                    existingCommitChart.destroy();
                }
            }
            
            // Clean up size comparison chart
            const sizeComparisonCanvas = document.getElementById('size-comparison-chart');
            if (sizeComparisonCanvas) {
                const existingSizeChart = Chart.getChart(sizeComparisonCanvas);
                if (existingSizeChart) {
                    console.log('Destroying existing size comparison chart');
                    existingSizeChart.destroy();
                }
            }
            
            // Reset chart references in state
            this.state.commitHistoryChart = null;
            this.state.sizeComparisonChart = null;
            
            console.log('Chart cleanup completed successfully');
        } catch (error) {
            console.error('Error during chart cleanup:', error);
        }
    },
    
    /**
     * Render commit history chart for the selected repository
     * @param {boolean} useSampleData - Whether to use sample data instead of fetching from API
     */
    async renderCommitHistoryChart(useSampleData = false) {
        console.log('Rendering commit history chart, useSampleData:', useSampleData);
        
        if (!this.state.selectedRepository) {
            console.error('No repository selected for commit history chart');
            return;
        }
        
        // Get the canvas element
        const canvas = document.getElementById('commit-history-chart');
        if (!canvas) {
            console.error('Commit history chart canvas not found');
            return;
        }
        
        // Clean up any existing chart on this canvas
        try {
            // First try to get the chart from Chart.js registry
            let existingChart = Chart.getChart(canvas);
            if (existingChart) {
                console.log('Found existing chart on canvas, destroying it');
                existingChart.destroy();
            }
            
            // Also check our state-tracked chart and destroy if it exists
            if (this.state.commitHistoryChart && this.state.commitHistoryChart !== existingChart) {
                console.log('Destroying existing commit history chart from state');
                this.state.commitHistoryChart.destroy();
            }
            
            // Reset the state reference
            this.state.commitHistoryChart = null;
        } catch (error) {
            console.error('Error cleaning up existing chart:', error);
            // Continue anyway - we'll create a new chart
        }
        
        // Clear the canvas by resetting its dimensions
        const parent = canvas.parentElement;
        const width = canvas.width;
        const height = canvas.height;
        canvas.remove();
        const newCanvas = document.createElement('canvas');
        newCanvas.id = 'commit-history-chart';
        newCanvas.width = width;
        newCanvas.height = height;
        parent.appendChild(newCanvas);
        
        // Remove any existing loading/error messages
        const chartContainer = parent;
        const existingMessages = chartContainer.querySelectorAll('.chart-loading, .chart-error, .chart-no-data');
        existingMessages.forEach(msg => msg.remove());
        
        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'chart-loading';
        loadingMessage.textContent = 'Loading commit history...';
        chartContainer.appendChild(loadingMessage);
        
        try {
            // Fetch commit history data
            let commitData;
            if (useSampleData) {
                console.log('Using sample commit history data');
                commitData = this.generateSampleCommitData();
            } else {
                console.log('Fetching commit history from API for:', this.state.selectedRepository.name);
                commitData = await this.fetchCommitHistory(this.state.selectedRepository.name, this.state.useFallbackData);
            }
            
            // Remove loading message - safely
            try {
                if (chartContainer.contains(loadingMessage)) {
                    chartContainer.removeChild(loadingMessage);
                }
            } catch (e) {
                console.warn('Could not remove loading message:', e);
                // Continue anyway
            }
            
            // If no data, show message
            if (!commitData || !commitData.labels || commitData.labels.length === 0) {
                console.warn('No commit history data available');
                const noDataMessage = document.createElement('div');
                noDataMessage.className = 'chart-no-data';
                noDataMessage.textContent = 'No commit history data available';
                chartContainer.appendChild(noDataMessage);
                return;
            }
            
            // Create chart
            const ctx = newCanvas.getContext('2d');
            this.state.commitHistoryChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: commitData.labels,
                    datasets: [{
                        label: 'Commits',
                        data: commitData.data,
                        backgroundColor: this.config.commitHistoryColors.background,
                        borderColor: this.config.commitHistoryColors.border,
						borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: commitData.isSampleData ? 
                                'Commit History (Sample Data)' : 
                                `Commit History (Last ${this.config.commitHistoryDays} Days)`
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    return tooltipItems[0].label;
                                },
                                label: function(context) {
                                    const value = context.raw;
                                    return value === 1 ? '1 commit' : `${value} commits`;
                                }
                            }
                        }
                    }
                }
            });
            
            // Update total commits display if available
            const totalCommitsEl = document.getElementById('total-commits-value');
            if (totalCommitsEl && commitData.totalCommits !== undefined) {
                totalCommitsEl.textContent = commitData.totalCommits;
            }
            
            console.log('Commit history chart rendered successfully');
            
        } catch (error) {
            console.error('Error rendering commit history chart:', error);
            
            // Remove loading message - safely
            try {
                if (chartContainer.contains(loadingMessage)) {
                    chartContainer.removeChild(loadingMessage);
                }
            } catch (e) {
                console.warn('Could not remove loading message:', e);
                // Continue anyway
            }
            
            // Show error message
            const errorMessage = document.createElement('div');
            errorMessage.className = 'chart-error';
            errorMessage.textContent = `Error loading commit history: ${error.message}`;
            chartContainer.appendChild(errorMessage);
            
            // If not already using sample data, try with sample data after a short delay
            if (!useSampleData) {
                console.log('Falling back to sample data due to error');
                setTimeout(() => {
                    this.renderCommitHistoryChart(true);
                }, 1000);
            }
        }
    },
    
    /**
     * Render repository size comparison chart
     */
    renderSizeComparisonChart() {
        console.log('Rendering size comparison chart');
        
        if (!this.state.selectedRepository) {
            console.error('No repository selected for size comparison chart');
            return;
        }
        
        // Get the canvas element
        const canvas = document.getElementById('size-comparison-chart');
        if (!canvas) {
            console.error('Size comparison chart canvas not found');
            return;
        }
        
        // Clean up any existing chart on this canvas
        try {
            // First try to get the chart from Chart.js registry
            let existingChart = Chart.getChart(canvas);
            if (existingChart) {
                console.log('Found existing chart on canvas, destroying it');
                existingChart.destroy();
            }
            
            // Also check our state-tracked chart and destroy if it exists
            if (this.state.sizeComparisonChart && this.state.sizeComparisonChart !== existingChart) {
                console.log('Destroying existing size comparison chart from state');
                this.state.sizeComparisonChart.destroy();
            }
            
            // Reset the state reference
            this.state.sizeComparisonChart = null;
        } catch (error) {
            console.error('Error cleaning up existing chart:', error);
            // Continue anyway - we'll create a new chart
        }
        
        // Remove any existing loading/error messages
        const chartContainer = canvas.parentElement;
        const existingMessages = chartContainer.querySelectorAll('.chart-loading, .chart-error, .chart-no-data');
        existingMessages.forEach(msg => msg.remove());
        
        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'chart-loading';
        loadingMessage.textContent = 'Loading size comparison...';
        chartContainer.appendChild(loadingMessage);
        
        try {
            // Get repositories from state
            let repositories = [];
            
            // First check if we have repositoriesData with repositories array
            if (this.state.repositoriesData && Array.isArray(this.state.repositoriesData.repositories)) {
                repositories = this.state.repositoriesData.repositories;
            } 
            // Then check if we have a direct repositories array
            else if (Array.isArray(this.state.repositories)) {
                repositories = this.state.repositories;
            }
            // Finally check if we have a direct repositoriesData array
            else if (Array.isArray(this.state.repositoriesData)) {
                repositories = this.state.repositoriesData;
            }
            
            console.log('Repositories for size comparison:', repositories);
            
            if (!repositories || repositories.length === 0) {
                console.warn('No repository data available for size comparison');
                // Remove loading message - safely
                try {
                    if (chartContainer.contains(loadingMessage)) {
                        chartContainer.removeChild(loadingMessage);
                    }
                } catch (e) {
                    console.warn('Could not remove loading message:', e);
                    // Continue anyway
                }
                
                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'chart-error';
                errorMessage.textContent = 'No repository data available for size comparison';
                chartContainer.appendChild(errorMessage);
                return;
            }
            
            // Get top 5 repositories by size
            const topRepositories = [...repositories]
                .sort((a, b) => {
                    const sizeA = this.parseSize(a.size_formatted || a.size || '0');
                    const sizeB = this.parseSize(b.size_formatted || b.size || '0');
                    return sizeB - sizeA;
                })
                .slice(0, 5);
            
            // Remove loading message - safely
            try {
                if (chartContainer.contains(loadingMessage)) {
                    chartContainer.removeChild(loadingMessage);
                }
            } catch (e) {
                console.warn('Could not remove loading message:', e);
                // Continue anyway
            }
            
            // If no data, show message
            if (!topRepositories || topRepositories.length === 0) {
                console.warn('No repository size data available');
                const noDataMessage = document.createElement('div');
                noDataMessage.className = 'chart-no-data';
                noDataMessage.textContent = 'No repository size data available';
                chartContainer.appendChild(noDataMessage);
                return;
            }
            
            // Prepare chart data
            const labels = topRepositories.map(repo => repo.name);
            const sizes = topRepositories.map(repo => this.parseSize(repo.size_formatted || repo.size || '0'));
            
            // Create chart
            const ctx = canvas.getContext('2d');
            this.state.sizeComparisonChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Repository Size (MB)',
                        data: sizes,
                        backgroundColor: 'rgba(255, 159, 64, 0.5)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Size (MB)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + ' MB';
                                }
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Top 5 Repositories by Size'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw;
                                    return `${value.toFixed(2)} MB`;
                                }
                            }
                        }
                    }
                }
            });
            
            console.log('Size comparison chart rendered successfully');
            
        } catch (error) {
            console.error('Error rendering size comparison chart:', error);
            
            // Remove loading message - safely
            try {
                if (chartContainer.contains(loadingMessage)) {
                    chartContainer.removeChild(loadingMessage);
                }
            } catch (e) {
                console.warn('Could not remove loading message:', e);
                // Continue anyway
            }
            
            // Show error message
            const errorMessage = document.createElement('div');
            errorMessage.className = 'chart-error';
            errorMessage.textContent = `Error loading size comparison: ${error.message}`;
            chartContainer.appendChild(errorMessage);
        }
    },
    
    /**
     * Parse repository size string to number in MB
     * @param {string} sizeStr - Size string (e.g., "1.5 MB", "800 KB")
     * @returns {number} Size in MB
     */
    parseSize(sizeStr) {
        if (!sizeStr || typeof sizeStr !== 'string') return 0;
        
        // Handle empty strings or non-string values
        sizeStr = sizeStr.trim();
        if (!sizeStr) return 0;
        
        // Try to match the pattern: number followed by unit (KB, MB, GB, TB)
        const match = sizeStr.match(/^([\d.]+)\s*(B|KB|MB|GB|TB)$/i);
        if (!match) {
            console.warn(`Could not parse size string: ${sizeStr}`);
            return 0;
        }
        
        const [, size, unit] = match;
        const value = parseFloat(size);
        
        if (isNaN(value)) {
            console.warn(`Invalid size value: ${size}`);
            return 0;
        }
        
        // Convert to MB based on unit
        switch (unit.toUpperCase()) {
            case 'B':
                return value / (1024 * 1024);
            case 'KB':
                return value / 1024;
            case 'MB':
                return value;
            case 'GB':
                return value * 1024;
            case 'TB':
                return value * 1024 * 1024;
            default:
                console.warn(`Unknown size unit: ${unit}`);
                return 0;
        }
    },
/**
     * Fetch commit history for a repository
     * @param {string} repoName - Repository name
     * @param {boolean} useFallback - Whether to use fallback data if API fails
     * @returns {Promise<Object>} Commit history data
     */
    async fetchCommitHistory(repoName, useFallback = false) {
        console.log(`Fetching commit history for ${repoName}, useFallback: ${useFallback}`);
        
        if (!repoName) {
            console.error('No repository name provided for commit history');
            throw new Error('Repository name is required');
        }
        
        try {
            // Build API URL
            const apiUrl = `${this.config.endpoints.commitHistory.replace('{repo_name}', encodeURIComponent(repoName))}?days=${this.config.commitHistoryDays || 30}`;
            console.log('Fetching commit history from:', apiUrl);
            
            // Add fallback parameter if requested
            const url = useFallback ? `${apiUrl}&fallback=true` : apiUrl;
            
            // Fetch data from API
            const response = await fetch(url);
            
            // Check if response is ok
            if (!response.ok) {
                const errorText = await response.text();
                console.error(`API error (${response.status}):`, errorText);
                throw new Error(`API error: ${response.status} ${response.statusText}`);
            }
            
            // Parse response
            const data = await response.json();
            console.log('Commit history data received:', data);
            
            // Check if data is valid and handle different response formats
            if (data.status === 'error') {
                console.warn('API returned error status:', data.message);
                
                // If fallback data is provided with the error, use it
                if (data.data && Array.isArray(data.data)) {
                    console.log('Using fallback data provided with error response');
                    const labels = data.data.map(item => item.displayDate);
                    const commitCounts = data.data.map(item => item.count);
                    const totalCommits = commitCounts.reduce((sum, count) => sum + count, 0);
                    
                    return {
                        labels: labels,
                        data: commitCounts,
                        totalCommits: totalCommits,
                        isSampleData: true
                    };
                }
                
                // If no fallback data and we're not already using fallback, try with fallback
                if (!useFallback) {
                    console.log('Retrying with fallback=true');
                    return this.fetchCommitHistory(repoName, true);
                }
                
                throw new Error(data.message || 'Failed to fetch commit history');
            }
            
            // Process data based on the format
            let commitData = [];
            
            if (data.data && Array.isArray(data.data)) {
                // Format from GitPython method
                commitData = data.data;
            } else if (data.commits && Array.isArray(data.commits)) {
                // Alternative format
                commitData = data.commits;
            } else {
                console.error('Unexpected data format:', data);
                throw new Error('Unexpected data format from API');
            }
            
            // Process data for chart
            const labels = commitData.map(item => item.displayDate);
            const commitCounts = commitData.map(item => item.count);
            const totalCommits = commitCounts.reduce((sum, count) => sum + count, 0);
            
            return {
                labels: labels,
                data: commitCounts,
                totalCommits: totalCommits,
                isSampleData: data.method === 'fallback'
            };
            
        } catch (error) {
            console.error('Error fetching commit history:', error);
            
            // Use fallback data if requested
            if (useFallback) {
                console.log('Using fallback sample data due to API error');
                return this.generateSampleCommitData();
            }
            
            // Otherwise rethrow the error
            throw error;
        }
    },
    
    /**
     * Generate sample commit data for visualization
     * @returns {Object} Sample commit data object
     */
    generateSampleCommitData() {
        console.log('Generating sample commit data');
        
        const days = this.config.commitHistoryDays || 30;
        const data = [];
        const labels = [];
        let totalCommits = 0;
        
        // Generate random commit counts for the last 30 days
        const today = new Date();
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            // Format date as MM/DD
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const formattedDate = `${month}/${day}`;
            
            // Generate random commit count (0-10)
            const commitCount = Math.floor(Math.random() * 10);
            totalCommits += commitCount;
            
            // Add to data arrays
            data.push(commitCount);
            labels.push(formattedDate);
        }
        
        return {
            labels: labels,
            data: data,
            totalCommits: totalCommits,
            isSampleData: true
        };
    },
    
    /**
     * Update module status
     * @param {string} status - New status (loading, success, error)
     */
    updateStatus(status) {
        this.state.status = status;
        
        // Update UI indicators if they exist
        const gitStatusIndicator = document.getElementById('repo-list-status-indicator');
        if (gitStatusIndicator) {
            gitStatusIndicator.className = 'status-indicator ' + status;
        }
    },
    
    /**
     * Fetch Git repositories data
     * @returns {Promise<Object>} Repository data
     */
    async fetchRepositories() {
        this.updateStatus('loading');
        
        try {
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', 'Fetching Git repositories', 'git');
            }
            
            const response = await fetch(this.config.endpoints.repositories);
            if (!response.ok) {
                if (response.status === 404) {
                    console.warn("No repositories found");
                    if (window.DashboardLogger) {
                        window.DashboardLogger.addLog('warning', 'No repositories found', 'git');
                    }
                    this.state.repositoriesData = { repositories: [] };
                } else {
                    const errorMsg = `Failed to fetch repositories: ${response.status}`;
                    if (window.DashboardLogger) {
                        window.DashboardLogger.addLog('error', errorMsg, 'git');
                    }
                    throw new Error(errorMsg);
                }
            } else {
                const data = await response.json();
                console.log('Repository data:', data);
                this.state.repositoriesData = data;
                
                const repoCount = data.repositories ? data.repositories.length : 0;
                if (window.DashboardLogger) {
                    window.DashboardLogger.addLog('info', `Loaded ${repoCount} repositories`, 'git');
                }
            }
            
            this.state.lastUpdated = new Date();
            this.updateStatus('success');
            
            // Update repository list view directly (no toggle check)
            this.renderRepositoryList();
            
            // Update global last updated time if this is the most recent update
            const lastUpdatedEl = document.getElementById('last-updated');
            if (lastUpdatedEl && this.state.lastUpdated) {
                lastUpdatedEl.textContent = this.state.lastUpdated.toLocaleString();
            }
            
            return this.state.repositoriesData;
        } catch (error) {
            console.error('Error fetching repositories:', error);
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Error fetching repositories: ${error.message}`, 'git');
            }
            this.updateStatus('error');
            throw error;
        }
    },
    
    /**
     * Fix modal backdrop issues
     * This function ensures the modal backdrop is properly removed
     * and the body element is restored to its normal state
     */
    fixModalBackdropIssues() {
        console.log('Fixing modal backdrop issues');
        
        // Remove any lingering backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            console.log('Removing lingering modal backdrop');
            backdrop.remove();
        });
        
        // Fix body element
        if (document.body.classList.contains('modal-open')) {
            console.log('Removing modal-open class from body');
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    }
};

// Expose the module globally
window.GitRepositoryManager = GitRepositoryManager;

// Initialize when DOM is loaded (if not being controlled by another module)
document.addEventListener('DOMContentLoaded', () => {
    // Will be initialized via index.html
});
