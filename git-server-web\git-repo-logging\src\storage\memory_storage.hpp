#pragma once
#include "storage_types.hpp"
#include <mutex>
#include <deque>
#include <unordered_map>

namespace logging {

class MemoryStorage : public StorageTier {
public:
    MemoryStorage(size_t maxEntries, std::chrono::minutes maxAge);
    virtual ~MemoryStorage() = default;
    
    // StorageTier interface implementation
    bool store(const LogEntry& entry) override;
    bool storeBatch(const std::vector<LogEntry>& entries) override;
    std::vector<LogEntry> query(const LogQueryParams& params) override;
    StorageStats getStats() override;
    bool prune(std::chrono::system_clock::time_point olderThan) override;
    bool compact() override;
    bool backup(const std::string& backupPath) override;
    bool canHandle(const LogEntry& entry) const override;
    std::string getName() const override { return "Memory"; }
    int getPriority() const override { return 1; } // Highest priority
    
private:
    // Remove old entries based on max age and size
    void pruneOldEntries();
    
    // Check if entry matches query parameters
    bool matchesQuery(const LogEntry& entry, const LogQueryParams& params) const;
    
    std::deque<LogEntry> entries_;
    std::mutex mutex_;
    size_t maxEntries_;
    std::chrono::minutes maxAge_;
    std::unordered_map<std::string, size_t> sourceCount_;
    std::unordered_map<LogType, size_t> levelCount_;
};

} // namespace logging
