# Standardize-Configurations.psm1
# Module to ensure all configuration files use consistent field names

function Update-ConfigurationFiles {
    param(
        [switch]$Force
    )

    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
    
    if (-not (Test-Path $configDir)) {
        Write-Host "Configuration directory not found: $configDir" -ForegroundColor Red
        return $false
    }

    # Get all configuration files
    $configFiles = Get-ChildItem -Path $configDir -Filter "database-service-*.json"
    $configFiles = $configFiles | Where-Object { 
        $_.Name -notmatch "_\d{8}-\d{6}" -and 
        $_.Directory.Name -ne "backups" -and 
        $_.Directory.Name -ne "exports" 
    }

    Write-Host "Found $($configFiles.Count) configuration files to process." -ForegroundColor Cyan
    
    foreach ($file in $configFiles) {
        try {
            Write-Host "Processing $($file.Name)..." -ForegroundColor White
            
            # Create backup before modifying
            $backupDir = Join-Path -Path $configDir -ChildPath "backups"
            if (-not (Test-Path $backupDir)) {
                New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
            }
            
            $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
            $backupFile = Join-Path -Path $backupDir -ChildPath $file.Name.Replace(".json", "_$timestamp.json")
            Copy-Item -Path $file.FullName -Destination $backupFile -Force
            
            # Read configuration
            $config = Get-Content -Path $file.FullName -Raw | ConvertFrom-Json
            
            # Create a new standardized configuration
            $standardConfig = @{}
            
            # Process project section
            $standardConfig.project = @{
                local_source_dir = "D:\Augment\project-tracker\database-service"
                name = $config.project.name
                remote_install_dir = $config.project.remote_install_dir
                description = $config.project.description
                remote_build_dir = $config.project.remote_build_dir
            }
            
            # Process SSH section
            $standardConfig.ssh = @{
                local_key_path = "C:\Users\<USER>\.ssh\id_rsa"
                username = "btaylor-admin"
                port = $config.ssh.port
                host = $config.ssh.host
            }
            
            # If there were original values, keep them
            if ($config.ssh.local_key_path) { $standardConfig.ssh.local_key_path = $config.ssh.local_key_path }
            elseif ($config.ssh.key_path) { $standardConfig.ssh.local_key_path = $config.ssh.key_path }
            
            if ($config.ssh.username) { $standardConfig.ssh.username = $config.ssh.username }
            elseif ($config.ssh.user) { $standardConfig.ssh.username = $config.ssh.user }
            
            # Process service section
            $standardConfig.service = @{
                user = $config.service.user
                name = $config.service.name
                group = $config.service.group
                description = $config.service.description
                port = $config.service.port
            }
            
            # Process database section
            $standardConfig.database = @{
                username = if ($config.database.username) { $config.database.username } elseif ($config.database.user) { $config.database.user } else { "postgres" }
                name = $config.database.name
                port = $config.database.port
                password = $config.database.password
                host = $config.database.host
            }
            
            # Process version section
            if ($config.version) {
                $standardConfig.version = $config.version
            } else {
                $standardConfig.version = @{
                    number = 1
                    updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
                    created = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
                }
            }
            
            # Save the standardized configuration
            $standardConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $file.FullName -Encoding UTF8
            Write-Host "Updated $($file.Name) with standardized field names." -ForegroundColor Green
            
        } catch {
            Write-Host "Error processing $($file.Name): $_" -ForegroundColor Red
        }
    }
    
    return $true
}

# Export the functions
Export-ModuleMember -Function Update-ConfigurationFiles
