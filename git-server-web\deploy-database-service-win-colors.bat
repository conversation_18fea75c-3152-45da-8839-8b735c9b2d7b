@echo off
REM Database Service Deployment Script
REM This script deploys the Database Service to the Git server

setlocal enabledelayedexpansion

REM Configuration
set GIT_SERVER=git.chcit.org
set SSH_USER=btaylor-admin
set INSTALL_DIR=/opt/database-service
set CONFIG_DIR=/etc/database-service
set LOG_DIR=/var/log/database-service
set BUILD_DIR=/home/<USER>/database-service-build
set SOURCE_DIR=D:\Augment\project-tracker\database-service

REM Colors for Windows console
set COLOR_DEFAULT=07
set COLOR_RED=0C
set COLOR_GREEN=0A
set COLOR_YELLOW=0E
set COLOR_CYAN=0B

color %COLOR_DEFAULT%
echo.
color %COLOR_CYAN%
echo Database Service Deployment Script
color %COLOR_DEFAULT%
echo =====================================
echo.

REM Check if SSH key exists
if not exist "%USERPROFILE%\.ssh\id_rsa" (
    color %COLOR_RED%
    echo Error: SSH key not found at %USERPROFILE%\.ssh\id_rsa
    color %COLOR_DEFAULT%
    echo Please generate an SSH key pair and add the public key to the server.
    exit /b 1
)

:menu
echo.
color %COLOR_CYAN%
echo Deployment Options:
color %COLOR_DEFAULT%
color %COLOR_YELLOW%
echo 1.
color %COLOR_DEFAULT%
echo  Check SSH Connection
color %COLOR_YELLOW%
echo 2.
color %COLOR_DEFAULT%
echo  Install Dependencies and Setup Certificate Access
color %COLOR_YELLOW%
echo 3.
color %COLOR_DEFAULT%
echo  Comprehensive Server Readiness Check
color %COLOR_YELLOW%
echo 4.
color %COLOR_DEFAULT%
echo  Deploy Source Code
color %COLOR_YELLOW%
echo 5.
color %COLOR_DEFAULT%
echo  Build Application
color %COLOR_YELLOW%
echo 6.
color %COLOR_DEFAULT%
echo  Configure Service
color %COLOR_YELLOW%
echo 7.
color %COLOR_DEFAULT%
echo  Install Service
color %COLOR_YELLOW%
echo 8.
color %COLOR_DEFAULT%
echo  Start Service
color %COLOR_YELLOW%
echo 9.
color %COLOR_DEFAULT%
echo  Full Deployment (Steps 1-8)
color %COLOR_YELLOW%
echo 10.
color %COLOR_DEFAULT%
echo  Check Service Status
color %COLOR_YELLOW%
echo 11.
color %COLOR_DEFAULT%
echo  View Logs
color %COLOR_YELLOW%
echo 12.
color %COLOR_DEFAULT%
echo  Uninstall Service
color %COLOR_YELLOW%
echo 13.
color %COLOR_DEFAULT%
echo  Database Management
color %COLOR_YELLOW%
echo 14.
color %COLOR_DEFAULT%
echo  Backup Configuration
color %COLOR_YELLOW%
echo 15.
color %COLOR_DEFAULT%
echo  Restore Configuration
color %COLOR_YELLOW%
echo 0.
color %COLOR_DEFAULT%
echo  Exit
echo.
set /p choice=Enter your choice (0-15):

if "%choice%"=="0" goto :eof
if "%choice%"=="1" goto check_ssh
if "%choice%"=="2" goto install_dependencies
if "%choice%"=="3" goto server_readiness
if "%choice%"=="4" goto deploy_source
if "%choice%"=="5" goto build_app
if "%choice%"=="6" goto configure_service
if "%choice%"=="7" goto install_service
if "%choice%"=="8" goto start_service
if "%choice%"=="9" goto full_deployment
if "%choice%"=="10" goto check_status
if "%choice%"=="11" goto view_logs
if "%choice%"=="12" goto uninstall_service
if "%choice%"=="13" goto database_management
if "%choice%"=="14" goto backup_config
if "%choice%"=="15" goto restore_config

color %COLOR_RED%
echo Invalid choice. Please try again.
color %COLOR_DEFAULT%
goto menu

:check_ssh
echo.
color %COLOR_CYAN%
echo Checking SSH connection to %GIT_SERVER%...
color %COLOR_DEFAULT%
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    color %COLOR_RED%
    echo Failed to connect to %GIT_SERVER%. Please check your SSH configuration.
    color %COLOR_DEFAULT%
) else (
    color %COLOR_GREEN%
    echo SSH connection successful.
    color %COLOR_DEFAULT%
)
goto menu

:install_dependencies
echo.
color %COLOR_CYAN%
echo Installing dependencies and setting up certificate access...
color %COLOR_DEFAULT%

REM Copy the certificate access setup script to the server
echo Copying certificate access setup script...
scp "%SOURCE_DIR%\scripts\setup-certificate-access.sh" %SSH_USER%@%GIT_SERVER%:/home/<USER>/

REM Install required packages
echo Installing required packages...
ssh %SSH_USER%@%GIT_SERVER% "sudo apt-get update && sudo apt-get install -y build-essential cmake libpq-dev libssl-dev libcurl4-openssl-dev libjsoncpp-dev nlohmann-json3-dev"

REM Set up certificate access
echo Setting up certificate access...
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /home/<USER>/setup-certificate-access.sh && /home/<USER>/setup-certificate-access.sh"

color %COLOR_GREEN%
echo Dependencies installed and certificate access set up successfully.
color %COLOR_DEFAULT%
goto menu

:server_readiness
echo.
color %COLOR_CYAN%
echo Performing comprehensive server readiness check...
color %COLOR_DEFAULT%

REM Check SSH connection
echo Checking SSH connection...
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    color %COLOR_RED%
    echo SSH connection failed. Please check your SSH configuration.
    color %COLOR_DEFAULT%
    goto menu
)

REM Check required packages
echo Checking required packages...
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -E 'build-essential|cmake|libpq-dev|libssl-dev|libcurl4-openssl-dev|libjsoncpp-dev|nlohmann-json3-dev'"

REM Check PostgreSQL
echo Checking PostgreSQL...
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active postgresql"

REM Check certificate access
echo Checking certificate access...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null 2>&1 && echo 'Certificate access OK' || echo 'Certificate access FAILED'"

REM Check disk space
echo Checking disk space...
ssh %SSH_USER%@%GIT_SERVER% "df -h | grep -E '/$|/home'"

REM Check memory
echo Checking memory...
ssh %SSH_USER%@%GIT_SERVER% "free -h"

REM Check CPU
echo Checking CPU...
ssh %SSH_USER%@%GIT_SERVER% "lscpu | grep -E 'Model name|CPU\(s\)'"

color %COLOR_GREEN%
echo Server readiness check completed.
color %COLOR_DEFAULT%
goto menu

:deploy_source
echo.
color %COLOR_CYAN%
echo Deploying source code to %GIT_SERVER%...
color %COLOR_DEFAULT%

REM Create build directory
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %BUILD_DIR%"

REM Copy source files
echo Copying source files...
scp -r "%SOURCE_DIR%\src" "%SOURCE_DIR%\include" "%SOURCE_DIR%\CMakeLists.txt" "%SOURCE_DIR%\scripts" %SSH_USER%@%GIT_SERVER%:%BUILD_DIR%/

color %COLOR_GREEN%
echo Source code deployed successfully.
color %COLOR_DEFAULT%
goto menu

:build_app
echo.
color %COLOR_CYAN%
echo Building application on %GIT_SERVER%...
color %COLOR_DEFAULT%

REM Create build directory and build the application
ssh %SSH_USER%@%GIT_SERVER% "cd %BUILD_DIR% && mkdir -p build && cd build && cmake .. -DCMAKE_BUILD_TYPE=Release && make -j$(nproc)"

color %COLOR_GREEN%
echo Application built successfully.
color %COLOR_DEFAULT%
goto menu

:configure_service
echo.
color %COLOR_CYAN%
echo Configuring service on %GIT_SERVER%...
color %COLOR_DEFAULT%

REM Create configuration directory
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %CONFIG_DIR% %LOG_DIR%"

REM Copy configuration files
echo Copying configuration files...
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/*.json %CONFIG_DIR%/"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR% %LOG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %LOG_DIR%"

color %COLOR_GREEN%
echo Service configured successfully.
color %COLOR_DEFAULT%
goto menu

:install_service
echo.
color %COLOR_CYAN%
echo Installing service on %GIT_SERVER%...
color %COLOR_DEFAULT%

REM Create installation directory
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %INSTALL_DIR%/bin %INSTALL_DIR%/lib"

REM Copy binaries and libraries
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/bin/* %INSTALL_DIR%/bin/"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/lib/* %INSTALL_DIR%/lib/ 2>/dev/null || echo No libraries to copy"

REM Set appropriate permissions
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R root:database-service %INSTALL_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR% %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR%/bin/*"

REM Install systemd service
echo Creating systemd service...
ssh %SSH_USER%@%GIT_SERVER% "cat > /home/<USER>/database-service.service << 'EOF'
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=%INSTALL_DIR%/bin/database-service --config %CONFIG_DIR%/config.json
Restart=on-failure
WorkingDirectory=%INSTALL_DIR%
Environment=\"LD_LIBRARY_PATH=%INSTALL_DIR%/lib\"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF"

ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/database-service.service /etc/systemd/system/"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

color %COLOR_GREEN%
echo Service installed successfully.
color %COLOR_DEFAULT%
goto menu

:start_service
echo.
color %COLOR_CYAN%
echo Starting service on %GIT_SERVER%...
color %COLOR_DEFAULT%

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable database-service && sudo systemctl start database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"

color %COLOR_GREEN%
echo Service started successfully.
color %COLOR_DEFAULT%
goto menu

:full_deployment
echo.
color %COLOR_CYAN%
echo Performing full deployment...
color %COLOR_DEFAULT%

REM Check SSH connection
color %COLOR_YELLOW%
echo Step 1: Checking SSH connection...
color %COLOR_DEFAULT%
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo Connected successfully"
if %ERRORLEVEL% neq 0 (
    color %COLOR_RED%
    echo SSH connection failed. Please check your SSH configuration.
    color %COLOR_DEFAULT%
    goto menu
)
color %COLOR_GREEN%
echo SSH connection successful.
color %COLOR_DEFAULT%

REM Install dependencies and set up certificate access
color %COLOR_YELLOW%
echo Step 2: Installing dependencies and setting up certificate access...
color %COLOR_DEFAULT%
scp "%SOURCE_DIR%\scripts\setup-certificate-access.sh" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo apt-get update && sudo apt-get install -y build-essential cmake libpq-dev libssl-dev libcurl4-openssl-dev libjsoncpp-dev nlohmann-json3-dev"
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /home/<USER>/setup-certificate-access.sh && /home/<USER>/setup-certificate-access.sh"
color %COLOR_GREEN%
echo Dependencies installed and certificate access set up successfully.
color %COLOR_DEFAULT%

REM Perform server readiness check
color %COLOR_YELLOW%
echo Step 3: Performing server readiness check...
color %COLOR_DEFAULT%
ssh %SSH_USER%@%GIT_SERVER% "sudo -u database-service cat /etc/letsencrypt/live/chcit.org/privkey.pem > /dev/null 2>&1 && echo 'Certificate access OK' || echo 'Certificate access FAILED'"
ssh %SSH_USER%@%GIT_SERVER% "systemctl is-active postgresql"
color %COLOR_GREEN%
echo Server readiness check completed.
color %COLOR_DEFAULT%

REM Deploy source code
color %COLOR_YELLOW%
echo Step 4: Deploying source code...
color %COLOR_DEFAULT%
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %BUILD_DIR%"
scp -r "%SOURCE_DIR%\src" "%SOURCE_DIR%\include" "%SOURCE_DIR%\CMakeLists.txt" "%SOURCE_DIR%\scripts" %SSH_USER%@%GIT_SERVER%:%BUILD_DIR%/
color %COLOR_GREEN%
echo Source code deployed successfully.
color %COLOR_DEFAULT%

REM Build application
color %COLOR_YELLOW%
echo Step 5: Building application...
color %COLOR_DEFAULT%
ssh %SSH_USER%@%GIT_SERVER% "cd %BUILD_DIR% && mkdir -p build && cd build && cmake .. -DCMAKE_BUILD_TYPE=Release && make -j$(nproc)"
color %COLOR_GREEN%
echo Application built successfully.
color %COLOR_DEFAULT%

REM Configure service
color %COLOR_YELLOW%
echo Step 6: Configuring service...
color %COLOR_DEFAULT%
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %CONFIG_DIR% %LOG_DIR%"
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/*.json %CONFIG_DIR%/"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR% %LOG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %LOG_DIR%"
color %COLOR_GREEN%
echo Service configured successfully.
color %COLOR_DEFAULT%

REM Install service
color %COLOR_YELLOW%
echo Step 7: Installing service...
color %COLOR_DEFAULT%
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/bin/* %INSTALL_DIR%/bin/"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %BUILD_DIR%/build/lib/* %INSTALL_DIR%/lib/ 2>/dev/null || echo No libraries to copy"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R root:database-service %INSTALL_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR% %INSTALL_DIR%/bin %INSTALL_DIR%/lib"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %INSTALL_DIR%/bin/*"
ssh %SSH_USER%@%GIT_SERVER% "cat > /home/<USER>/database-service.service << 'EOF'
[Unit]
Description=Database Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=database-service
Group=database-service
ExecStart=%INSTALL_DIR%/bin/database-service --config %CONFIG_DIR%/config.json
Restart=on-failure
WorkingDirectory=%INSTALL_DIR%
Environment=\"LD_LIBRARY_PATH=%INSTALL_DIR%/lib\"

# Security enhancements
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF"
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /home/<USER>/database-service.service /etc/systemd/system/"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"
color %COLOR_GREEN%
echo Service installed successfully.
color %COLOR_DEFAULT%

REM Start service
color %COLOR_YELLOW%
echo Step 8: Starting service...
color %COLOR_DEFAULT%
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable database-service && sudo systemctl start database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"
color %COLOR_GREEN%
echo Service started successfully.
color %COLOR_DEFAULT%

color %COLOR_GREEN%
echo Full deployment completed successfully.
color %COLOR_DEFAULT%
goto menu

:check_status
echo.
color %COLOR_CYAN%
echo Checking service status on %GIT_SERVER%...
color %COLOR_DEFAULT%

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status database-service"

goto menu

:view_logs
echo.
color %COLOR_CYAN%
echo Viewing logs on %GIT_SERVER%...
color %COLOR_DEFAULT%

ssh %SSH_USER%@%GIT_SERVER% "sudo journalctl -u database-service -n 50"

goto menu

:uninstall_service
echo.
color %COLOR_CYAN%
echo Uninstalling service from %GIT_SERVER%...
color %COLOR_DEFAULT%

ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl stop database-service && sudo systemctl disable database-service"
ssh %SSH_USER%@%GIT_SERVER% "sudo rm /etc/systemd/system/database-service.service"
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

color %COLOR_YELLOW%
echo Do you want to remove the installation directory and configuration? (y/n)
color %COLOR_DEFAULT%
set /p remove_files=

if /i "%remove_files%"=="y" (
    ssh %SSH_USER%@%GIT_SERVER% "sudo rm -rf %INSTALL_DIR% %CONFIG_DIR%"
    color %COLOR_GREEN%
    echo Service and files removed successfully.
    color %COLOR_DEFAULT%
) else (
    color %COLOR_GREEN%
    echo Service uninstalled, but files preserved.
    color %COLOR_DEFAULT%
)

goto menu

:database_management
echo.
color %COLOR_CYAN%
echo Database Management Options:
color %COLOR_DEFAULT%
color %COLOR_YELLOW%
echo 1.
color %COLOR_DEFAULT%
echo  Check PostgreSQL Status
color %COLOR_YELLOW%
echo 2.
color %COLOR_DEFAULT%
echo  List Databases
color %COLOR_YELLOW%
echo 3.
color %COLOR_DEFAULT%
echo  Create Database
color %COLOR_YELLOW%
echo 4.
color %COLOR_DEFAULT%
echo  Drop Database
color %COLOR_YELLOW%
echo 5.
color %COLOR_DEFAULT%
echo  List Users
color %COLOR_YELLOW%
echo 6.
color %COLOR_DEFAULT%
echo  Create User
color %COLOR_YELLOW%
echo 7.
color %COLOR_DEFAULT%
echo  Drop User
color %COLOR_YELLOW%
echo 8.
color %COLOR_DEFAULT%
echo  Back to Main Menu
echo.
set /p db_choice=Enter your choice (1-8):

if "%db_choice%"=="1" (
    echo.
    color %COLOR_CYAN%
    echo Checking PostgreSQL status...
    color %COLOR_DEFAULT%
    ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status postgresql"
    goto database_management
)

if "%db_choice%"=="2" (
    echo.
    color %COLOR_CYAN%
    echo Listing databases...
    color %COLOR_DEFAULT%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\l'"
    goto database_management
)

if "%db_choice%"=="3" (
    echo.
    set /p db_name=Enter database name:
    color %COLOR_CYAN%
    echo Creating database %db_name%...
    color %COLOR_DEFAULT%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'CREATE DATABASE %db_name%;'"
    goto database_management
)

if "%db_choice%"=="4" (
    echo.
    set /p db_name=Enter database name to drop:
    color %COLOR_RED%
    echo WARNING: This will permanently delete the database %db_name% and all its data!
    color %COLOR_DEFAULT%
    set /p confirm=Are you sure? (y/n): 
    if /i "%confirm%"=="y" (
        color %COLOR_CYAN%
        echo Dropping database %db_name%...
        color %COLOR_DEFAULT%
        ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP DATABASE %db_name%;'"
    )
    goto database_management
)

if "%db_choice%"=="5" (
    echo.
    color %COLOR_CYAN%
    echo Listing users...
    color %COLOR_DEFAULT%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\du'"
    goto database_management
)

if "%db_choice%"=="6" (
    echo.
    set /p user_name=Enter user name:
    set /p user_pass=Enter password:
    color %COLOR_CYAN%
    echo Creating user %user_name%...
    color %COLOR_DEFAULT%
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c \"CREATE USER %user_name% WITH PASSWORD '%user_pass%';\""
    goto database_management
)

if "%db_choice%"=="7" (
    echo.
    set /p user_name=Enter user name to drop:
    color %COLOR_RED%
    echo WARNING: This will permanently delete the user %user_name%!
    color %COLOR_DEFAULT%
    set /p confirm=Are you sure? (y/n):
    if /i "%confirm%"=="y" (
        color %COLOR_CYAN%
        echo Dropping user %user_name%...
        color %COLOR_DEFAULT%
        ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP USER %user_name%;'"
    )
    goto database_management
)

if "%db_choice%"=="8" goto menu

color %COLOR_RED%
echo Invalid choice. Please try again.
color %COLOR_DEFAULT%
goto database_management

:backup_config
echo.
color %COLOR_CYAN%
echo Backing up configuration from %GIT_SERVER%...
color %COLOR_DEFAULT%

set timestamp=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
set backup_dir=backups\%timestamp%

mkdir "%SOURCE_DIR%\%backup_dir%" 2>nul

ssh %SSH_USER%@%GIT_SERVER% "sudo tar -czf /home/<USER>/database-config-backup.tar.gz %CONFIG_DIR%"
scp %SSH_USER%@%GIT_SERVER%:/home/<USER>/database-config-backup.tar.gz "%SOURCE_DIR%\%backup_dir%\"
ssh %SSH_USER%@%GIT_SERVER% "rm /home/<USER>/database-config-backup.tar.gz"

color %COLOR_GREEN%
echo Configuration backed up to %SOURCE_DIR%\%backup_dir%\database-config-backup.tar.gz
color %COLOR_DEFAULT%
goto menu

:restore_config
echo.
color %COLOR_CYAN%
echo Available backups:
color %COLOR_DEFAULT%
dir /b /ad "%SOURCE_DIR%\backups"

echo.
set /p backup_timestamp=Enter backup timestamp to restore:

if not exist "%SOURCE_DIR%\backups\%backup_timestamp%\database-config-backup.tar.gz" (
    color %COLOR_RED%
    echo Backup not found.
    color %COLOR_DEFAULT%
    goto menu
)

color %COLOR_CYAN%
echo Restoring configuration to %GIT_SERVER%...
color %COLOR_DEFAULT%

scp "%SOURCE_DIR%\backups\%backup_timestamp%\database-config-backup.tar.gz" %SSH_USER%@%GIT_SERVER%:/home/<USER>/
ssh %SSH_USER%@%GIT_SERVER% "sudo tar -xzf /home/<USER>/database-config-backup.tar.gz -C /"
ssh %SSH_USER%@%GIT_SERVER% "rm /home/<USER>/database-config-backup.tar.gz"
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R database-service:database-service %CONFIG_DIR%"
ssh %SSH_USER%@%GIT_SERVER% "sudo chmod 750 %CONFIG_DIR% && sudo chmod 640 %CONFIG_DIR%/*.json"

color %COLOR_GREEN%
echo Configuration restored successfully.
color %COLOR_DEFAULT%
goto menu
