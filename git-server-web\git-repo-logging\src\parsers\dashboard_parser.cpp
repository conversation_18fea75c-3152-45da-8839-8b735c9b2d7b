#include "dashboard_parser.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <jsoncpp/json/json.h>

namespace logging {

DashboardParser::DashboardParser() {
    // Initialize regular expressions for dashboard log formats
    
    // Standard format: [2023-05-20 10:15:30,123] INFO in app: message
    standardFormat_ = std::regex(
        R"(\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,.]\d{3})\]\s+(\w+)\s+in\s+(\w+):\s+(.*))"
    );
    
    // JSON format: {"timestamp":"2023-05-20T10:15:30.123Z","level":"info","component":"app","message":"log message"}
    jsonFormat_ = std::regex(
        R"(\{.*"timestamp"\s*:\s*"[^"]+"\s*,.*"level"\s*:\s*"[^"]+"\s*,.*"message"\s*:\s*"[^"]+"\s*,.*\})"
    );
}

std::optional<LogEntry> DashboardParser::parse(const std::string& line, const std::string& source) {
    std::smatch matches;
    
    // Try standard format
    if (std::regex_search(line, matches, standardFormat_)) {
        LogEntry entry;
        
        // Parse timestamp
        std::string timestampStr = matches[1].str();
        // Replace comma with dot for proper parsing
        std::replace(timestampStr.begin(), timestampStr.end(), ',', '.');
        
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S.%f");
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set other fields
        std::string level = matches[2].str();
        entry.component = matches[3].str();
        entry.message = matches[4].str();
        entry.source = source.empty() ? "dashboard" : source;
        entry.rawLine = line;
        entry.category = LogCategory::APPLICATION;
        
        // Determine log type based on level
        if (level == "ERROR" || level == "CRITICAL" || level == "FATAL") {
            entry.type = LogType::ERROR;
        } else if (level == "WARNING") {
            entry.type = LogType::WARNING;
        } else if (level == "INFO") {
            entry.type = LogType::INFO;
        } else if (level == "DEBUG") {
            entry.type = LogType::DEBUG;
        } else if (level == "SUCCESS") {
            entry.type = LogType::SUCCESS;
        } else {
            entry.type = LogType::INFO;
        }
        
        return entry;
    }
    
    // Try JSON format
    if (std::regex_search(line, matches, jsonFormat_)) {
        try {
            // Parse JSON
            Json::Value root;
            Json::CharReaderBuilder reader;
            std::string errors;
            
            std::istringstream jsonStream(line);
            if (Json::parseFromStream(reader, jsonStream, &root, &errors)) {
                LogEntry entry;
                
                // Parse timestamp
                if (root.isMember("timestamp")) {
                    std::string timestampStr = root["timestamp"].asString();
                    std::tm tm = {};
                    std::istringstream ss(timestampStr);
                    
                    // Try ISO 8601 format
                    if (timestampStr.find('T') != std::string::npos) {
                        ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%S");
                    } else {
                        ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
                    }
                    
                    // Convert to time_point
                    auto timeT = std::mktime(&tm);
                    entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
                } else {
                    // Use current time if no timestamp
                    entry.timestamp = std::chrono::system_clock::now();
                }
                
                // Set other fields
                if (root.isMember("component")) {
                    entry.component = root["component"].asString();
                }
                
                if (root.isMember("message")) {
                    entry.message = root["message"].asString();
                }
                
                entry.source = source.empty() ? "dashboard" : source;
                entry.rawLine = line;
                entry.category = LogCategory::APPLICATION;
                
                // Determine log type based on level
                std::string level = root.isMember("level") ? root["level"].asString() : "info";
                
                if (level == "error" || level == "critical" || level == "fatal") {
                    entry.type = LogType::ERROR;
                } else if (level == "warning" || level == "warn") {
                    entry.type = LogType::WARNING;
                } else if (level == "info") {
                    entry.type = LogType::INFO;
                } else if (level == "debug") {
                    entry.type = LogType::DEBUG;
                } else if (level == "success") {
                    entry.type = LogType::SUCCESS;
                } else {
                    entry.type = LogType::INFO;
                }
                
                // Add additional fields as metadata
                for (const auto& key : root.getMemberNames()) {
                    if (key != "timestamp" && key != "level" && key != "component" && key != "message") {
                        entry.metadata[key] = root[key].asString();
                    }
                }
                
                return entry;
            }
        } catch (const std::exception& e) {
            std::cerr << "Error parsing JSON log: " << e.what() << std::endl;
        }
    }
    
    // No match
    return std::nullopt;
}

bool DashboardParser::canParse(const std::string& line) {
    // Check if line matches any of the dashboard log formats
    return std::regex_search(line, standardFormat_) ||
           std::regex_search(line, jsonFormat_);
}

} // namespace logging
