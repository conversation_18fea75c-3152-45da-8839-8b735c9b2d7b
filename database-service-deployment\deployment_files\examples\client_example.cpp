#include "database-service/client/database_client.hpp"
#include <iostream>
#include <string>

using json = nlohmann::json;

int main() {
    try {
        // Create database client
        dbservice::client::DatabaseClient client(
            "http://localhost:8080",
            "example-app",
            "api-key-123"
        );
        
        // Check if database is available
        if (!client.isAvailable()) {
            std::cerr << "Database is not available" << std::endl;
            return 1;
        }
        
        std::cout << "Connected to database version: " << client.getVersion() << std::endl;
        
        // Create a table
        client.execute(
            "CREATE TABLE IF NOT EXISTS example_table ("
            "  id SERIAL PRIMARY KEY,"
            "  name VARCHAR(100) NOT NULL,"
            "  value INTEGER,"
            "  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()"
            ");"
        );
        
        // Insert data
        int rowsAffected = client.execute(
            "INSERT INTO example_table (name, value) VALUES ('test', 123);"
        );
        
        std::cout << "Inserted " << rowsAffected << " row(s)" << std::endl;
        
        // Query data
        json results = client.query(
            "SELECT * FROM example_table ORDER BY id DESC LIMIT 10;"
        );
        
        // Display results
        std::cout << "Query results:" << std::endl;
        for (const auto& row : results) {
            std::cout << "ID: " << row["id"].get<int>() << ", "
                     << "Name: " << row["name"].get<std::string>() << ", "
                     << "Value: " << row["value"].get<int>() << std::endl;
        }
        
        // Execute a transaction
        json txnResults = client.transaction({
            "INSERT INTO example_table (name, value) VALUES ('transaction-1', 456);",
            "INSERT INTO example_table (name, value) VALUES ('transaction-2', 789);"
        });
        
        std::cout << "Transaction results: " << txnResults.dump(2) << std::endl;
        
        // Get schema
        json schema = client.getSchema();
        
        std::cout << "Database schema:" << std::endl;
        for (const auto& table : schema) {
            std::cout << "Table: " << table["name"].get<std::string>() << " ("
                     << table["schema"].get<std::string>() << ")" << std::endl;
            
            std::cout << "  Columns:" << std::endl;
            for (const auto& column : table["columns"]) {
                std::cout << "    " << column["name"].get<std::string>() << " ("
                         << column["type"].get<std::string>() << ")"
                         << (column["nullable"].get<bool>() ? " NULL" : " NOT NULL");
                
                if (column.contains("default")) {
                    std::cout << " DEFAULT " << column["default"].get<std::string>();
                }
                
                std::cout << std::endl;
            }
        }
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

