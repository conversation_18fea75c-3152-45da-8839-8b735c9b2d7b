#!/bin/bash
# Helper script to safely access git logs
# This script should be owned by btaylor-admin and have the correct permissions

# Get the repository path from the first argument
REPO_PATH="$1"
MAX_COUNT="$2"

# Default to 10 logs if not specified
if [ -z "$MAX_COUNT" ]; then
    MAX_COUNT=10
fi

# Add the repository to safe.directory (this will work as btaylor-admin)
git config --global --add safe.directory "$REPO_PATH" 2>/dev/null

# Get the git logs
git -C "$REPO_PATH" log -n "$MAX_COUNT" --pretty=format:"%H|%an|%ad|%s"

# Exit with the same status as the git command
exit $?
