#include "api_server.hpp"
#include <iostream>
#include <regex>
#include <thread>

ApiServer::ApiServer(unsigned short port, std::shared_ptr<RepositoryService> service)
    : port_(port), repoService_(service), running_(false) {
    // Setup handlers
    setupEndpoints();
}

ApiServer::~ApiServer() {
    stop();
}

void ApiServer::start() {
    try {
        std::cout << "Starting API server on port " << port_ << "..." << std::endl;
        
        // Create an acceptor
        acceptor_ = std::make_shared<tcp::acceptor>(ioc_, tcp::endpoint(asio::ip::make_address("0.0.0.0"), port_));
        
        // Start accepting connections
        running_ = true;
        doAccept();
        
        std::cout << "API server started, accepting connections..." << std::endl;
        
        // Run the I/O service on the requested number of threads
        unsigned int threadCount = std::thread::hardware_concurrency();
        std::cout << "Starting " << threadCount << " worker threads" << std::endl;
        
        threads_.reserve(threadCount);
        for(auto i = 0u; i < threadCount; ++i) {
            threads_.emplace_back([this] {
                try {
                    ioc_.run();
                } catch (const std::exception& e) {
                    std::cerr << "Thread exception: " << e.what() << std::endl;
                }
            });
        }
    }
    catch(std::exception const& e) {
        std::cerr << "Error starting server: " << e.what() << std::endl;
    }
}

void ApiServer::stop() {
    if (!running_) return;
    
    running_ = false;
    
    if (acceptor_ && acceptor_->is_open()) {
        boost::system::error_code ec;
        acceptor_->close(ec);
    }
    
    ioc_.stop();
    
    for(auto& t : threads_) {
        if(t.joinable()) {
            t.join();
        }
    }
    
    threads_.clear();
}

void ApiServer::registerHandler(const std::string& path, 
                              std::function<http::response<http::string_body>(const http::request<http::string_body>&)> handler) {
    handlers_[path] = handler;
}

void ApiServer::doAccept() {
    if (!acceptor_ || !running_) {
        std::cerr << "Cannot accept connections: acceptor not initialized or server not running" << std::endl;
        return;
    }
    
    std::cout << "Waiting for connections..." << std::endl;
    
    // The new connection gets its own strand
    acceptor_->async_accept(
        [this](beast::error_code ec, tcp::socket socket) {
            if (ec) {
                std::cerr << "Accept error: " << ec.message() << std::endl;
            } else {
                std::cout << "Accepted connection from " << socket.remote_endpoint() << std::endl;
                
                // Create a session and run it
                try {
                    handleSession(std::move(socket));
                } catch (const std::exception& e) {
                    std::cerr << "Session handling error: " << e.what() << std::endl;
                }
            }
            
            // Accept another connection
            if (running_) {
                doAccept();
            }
        });
}

void ApiServer::handleSession(tcp::socket socket) {
    try {
        std::cout << "Handling new session..." << std::endl;
        
        // Set timeout
        beast::flat_buffer buffer;
        
        // Read the request
        http::request<http::string_body> req;
        http::read(socket, buffer, req);
        
        // Log request details
        std::string target(req.target().begin(), req.target().end());
        std::cout << "Received request: " << req.method_string() << " " << target << std::endl;
        
        // Handle the request
        http::response<http::string_body> res;
        
        // Find the appropriate handler based on the target path
        bool handled = false;
        
        // Check for exact path matches
        if (handlers_.find(target) != handlers_.end()) {
            std::cout << "Found exact handler for path: " << target << std::endl;
            res = handlers_[target](req);
            handled = true;
        } 
        // Check for commit history endpoint with repository name
        else if (target.find("/api/repository/") == 0 && target.find("/commits") != std::string::npos) {
            std::cout << "Handling commit history request: " << target << std::endl;
            res = handleCommitHistory(req);
            handled = true;
        }
        // Default to not found
        else {
            std::cout << "No handler found for path: " << target << std::endl;
            res = handleNotFound(req);
        }
        
        // Set CORS headers
        res.set(http::field::access_control_allow_origin, "*");
        res.set(http::field::access_control_allow_methods, "GET, POST, OPTIONS");
        res.set(http::field::access_control_allow_headers, "Content-Type");
        
        // Send the response
        std::cout << "Sending response with status: " << res.result_int() << std::endl;
        res.prepare_payload();
        http::write(socket, res);
        
        // Shutdown the socket
        beast::error_code ec;
        socket.shutdown(tcp::socket::shutdown_send, ec);
        std::cout << "Session completed successfully" << std::endl;
    }
    catch(std::exception const& e) {
        std::cerr << "Session error: " << e.what() << std::endl;
    }
}

void ApiServer::setupEndpoints() {
    // Register the repositories endpoint
    registerHandler("/api/repositories", 
        [this](const http::request<http::string_body>& req) {
            return handleRepositories(req);
        }
    );
    
    // Register the commit history endpoint with regex pattern matching
    registerHandler("/api/repository/([^/]+)/commits", 
        [this](const http::request<http::string_body>& req) {
            return handleCommitHistory(req);
        }
    );
}

http::response<http::string_body> ApiServer::handleNotFound(const http::request<http::string_body>& req) {
    http::response<http::string_body> res{http::status::not_found, req.version()};
    res.set(http::field::server, "Git Repository Service");
    res.set(http::field::content_type, "application/json");
    res.body() = "{\"error\":\"Not found\"}";
    return res;
}

http::response<http::string_body> ApiServer::handleRepositories(const http::request<http::string_body>& req) {
    http::response<http::string_body> res{http::status::ok, req.version()};
    res.set(http::field::server, "Git Repository Service");
    res.set(http::field::content_type, "application/json");
    
    try {
        res.body() = repoService_->getAllRepositories();
    } catch (const std::exception& e) {
        res.result(http::status::internal_server_error);
        res.body() = "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
    
    return res;
}

http::response<http::string_body> ApiServer::handleCommitHistory(const http::request<http::string_body>& req) {
    http::response<http::string_body> res{http::status::ok, req.version()};
    res.set(http::field::server, "Git Repository Service");
    res.set(http::field::content_type, "application/json");
    
    try {
        // Extract repository name from URL
        std::string path(req.target().begin(), req.target().end());
        std::regex repoPattern("/api/repository/([^/]+)/commits");
        std::smatch matches;
        
        if (std::regex_search(path, matches, repoPattern) && matches.size() > 1) {
            std::string repoName = matches[1].str();
            res.body() = repoService_->getCommitHistory(repoName);
        } else {
            res.result(http::status::bad_request);
            res.body() = "{\"error\":\"Invalid repository name\"}";
        }
    } catch (const std::exception& e) {
        res.result(http::status::internal_server_error);
        res.body() = "{\"error\":\"" + std::string(e.what()) + "\"}";
    }
    
    return res;
}
