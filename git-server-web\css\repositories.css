/* repositories.css - Git repository display styles */

/* Repository List View */
.git-list-container {
    padding: 10px 0;
}

.git-list-container .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.git-list-container .table td {
    vertical-align: middle;
}

/* Repository List Card */
#repository-list {
    width: 100%;
}

#repository-list .list-group-item {
    border-left: none;
    border-right: none;
    transition: background-color 0.2s;
}

#repository-list .list-group-item:first-child {
    border-top: none;
}

#repository-list .list-group-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Repository Health Indicators */
.health-indicator .badge {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.health-good {
    background-color: #28a745 !important;
    color: white !important;
}

.health-fair {
    background-color: #17a2b8 !important;
    color: white !important;
}

.health-poor {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.health-unknown {
    background-color: #6c757d !important;
    color: white !important;
}

/* Repository Details Modal */
.modal-body .chart-container {
    width: 100%;
    background-color: #f8f9fa;
    border-radius: 4px;
    position: relative;
    min-height: 300px; /* Ensure chart container has enough height */
}

.modal-body canvas {
    width: 100% !important;
    height: auto !important; /* Let Chart.js control the height */
}

#repository-details-content h5 {
    color: #007bff;
}

#repository-details-content .progress {
    height: 10px;
    border-radius: 5px;
}

/* Loading and Error States */
#repo-loading, #repo-empty, #repo-error,
#commit-chart-loading, #commit-chart-error,
#size-chart-loading, #size-chart-error,
#repository-details-loading, #repository-details-error {
    min-height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

/* Summary Cards */
#git-repo-section .card {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

#git-repo-section .card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

#git-repo-section .card-body {
    padding: 1.25rem;
}

/* Repository Details Modal - Enhanced Size */
#repositoryDetailsModal .modal-dialog.modal-lg {
    max-width: 800px;
}

#repositoryDetailsModal .modal-dialog.modal-xl {
    max-width: 1140px; /* Bootstrap 5 default for modal-xl */
}

#repositoryDetailsModal .modal-content {
    border-radius: 0.5rem;
    overflow: hidden;
}

#repositoryDetailsModal .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

#repositoryDetailsModal .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Repository Filter Controls */
.repo-controls {
    display: flex;
    align-items: center;
}

#repo-filter {
    min-width: 200px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .repo-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .repo-controls > div {
        margin-bottom: 0.5rem;
        width: 100%;
    }
    
    #repo-filter {
        width: 100%;
        min-width: auto;
    }
}

/* Git Summary View */
#git-summary-row .card {
    transition: transform 0.2s, box-shadow 0.2s;
    border-radius: 0.5rem;
    overflow: hidden;
}

#git-summary-row .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#git-summary-row .card-body {
    padding: 1.5rem;
    text-align: center;
}

#git-summary-row .fa-2x {
    margin-bottom: 1rem;
}

#git-summary-row .display-4 {
    font-size: 2.5rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

#git-summary-row .text-muted {
    font-size: 0.8rem;
}

/* Commit History Chart */
#commit-history-container {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin-bottom: 1rem;
    position: relative;
}

#commit-history-chart {
    width: 100% !important;
    height: 100% !important;
}

#commit-chart-message {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.25rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Size Comparison Chart */
#size-chart-container {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.5rem;
    position: relative;
}

#size-comparison-chart {
    width: 100% !important;
    height: 100% !important;
}

#size-chart-message {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.25rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Enhanced Repository Modal */
#repositoryDetailsModal .modal-body {
    padding: 0;
}

#repositoryDetailsModal .modal-body h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #495057;
}

#repositoryDetailsModal .health-status {
    background-color: #f8f9fa;
}

#repositoryDetailsModal .health-status h6 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

#repositoryDetailsModal .progress {
    height: 8px;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

#repositoryDetailsModal .small {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Repository side-by-side layout */
.repository-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.repository-item:hover {
    background-color: #f8f9fa;
}

.repository-item.active {
    background-color: #f0f7ff;
    border-left-color: #0d6efd;
}

/* Repository details section */
#repository-details-section {
    display: flex;
    flex-direction: column;
    height: 100%;
}

#repository-details-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

#repository-details-content {
    flex: 1;
    overflow-y: auto;
}

/* Repository health styles */
.progress {
    height: 0.5rem;
    border-radius: 1rem;
}

/* Commit history container */
#commit-history-container {
    height: 300px;
    width: 100%;
    position: relative;
    margin-top: 20px;
}

/* Fix loading spinner position */
#repository-details-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}
