from typing import Dict, Any, List, Optional
import psycopg2
from psycopg2.pool import SimpleConnectionPool
from psycopg2.extras import RealDictCursor
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseService:
    """PostgreSQL database service with connection pooling."""
    
    def __init__(self):
        self.pool = None
        self.metrics = {
            'queries_executed': 0,
            'errors': 0,
            'connection_errors': 0,
            'active_connections': 0
        }
        
    def init_app(self, app):
        """Initialize database connection pool."""
        try:
            self.pool = SimpleConnectionPool(
                minconn=1,
                maxconn=20,
                dsn=app.config['DATABASE_URL'],
                cursor_factory=RealDictCursor
            )
            logger.info("Database connection pool initialized")
        except Exception as e:
            self.metrics['connection_errors'] += 1
            logger.error(f"Database initialization error: {str(e)}", exc_info=True)
            raise
            
    def get_connection(self):
        """Get a connection from the pool."""
        if not self.pool:
            raise RuntimeError("Database pool not initialized")
        try:
            conn = self.pool.getconn()
            self.metrics['active_connections'] += 1
            return conn
        except Exception as e:
            self.metrics['connection_errors'] += 1
            logger.error(f"Connection acquisition error: {str(e)}", exc_info=True)
            raise
            
    def return_connection(self, conn):
        """Return a connection to the pool."""
        if self.pool:
            self.pool.putconn(conn)
            self.metrics['active_connections'] -= 1
            
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Execute a query and return results."""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cur:
                cur.execute(query, params)
                self.metrics['queries_executed'] += 1
                if cur.description:
                    return cur.fetchall()
                return []
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"Query execution error: {str(e)}\nQuery: {query}", exc_info=True)
            raise
        finally:
            if conn:
                self.return_connection(conn)
                
    def execute_transaction(self, queries: List[Dict[str, Any]]) -> bool:
        """Execute multiple queries in a transaction."""
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cur:
                for query in queries:
                    cur.execute(query['sql'], query.get('params'))
                    self.metrics['queries_executed'] += 1
                conn.commit()
                return True
        except Exception as e:
            if conn:
                conn.rollback()
            self.metrics['errors'] += 1
            logger.error(f"Transaction error: {str(e)}", exc_info=True)
            raise
        finally:
            if conn:
                self.return_connection(conn)
                
    def get_metrics(self) -> Dict[str, Any]:
        """Get database service metrics."""
        return {
            **self.metrics,
            'pool_size': self.pool.maxconn if self.pool else 0,
            'available_connections': (
                self.pool.maxconn - self.metrics['active_connections']
                if self.pool else 0
            )
        }
