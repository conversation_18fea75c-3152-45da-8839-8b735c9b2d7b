# Database Service Quick Start Guide

## Development Setup (No SSL Required)

### 1. Build the Service
```bash
mkdir build
cd build
cmake ..
make
```

### 2. Run in Development Mode
```bash
# Use development configuration (SSL disabled)
./database-service --config ../config/config-dev.json
```

### 3. Test the Service
```bash
# Health check
curl http://localhost:8080/api/health

# Should return: {"status": "ok", "service": "database-service"}
```

## Production Setup (with Nginx SSL Termination)

### 1. Prerequisites
- Ubuntu server with Nginx installed
- Let's Encrypt SSL certificates for `chcit.org`
- PostgreSQL database running

### 2. Install the Service
```bash
# Build and install
mkdir build
cd build
cmake -DCMAKE_INSTALL_PREFIX=/opt/database-service ..
make
sudo make install
```

### 3. Setup Reverse Proxy
```bash
# Run the setup script as root
sudo ./scripts/setup-reverse-proxy.sh
```

### 4. Configure Production Settings
```bash
# Copy production config
sudo cp config/config-prod.json /opt/database-service/etc/database-service/config.json

# Edit configuration
sudo nano /opt/database-service/etc/database-service/config.json

# Update these values:
# - database.password
# - security.jwt_secret  
# - security.secure_credential_storage.encryption_key
```

### 5. Start the Service
```bash
# Enable and start systemd service
sudo systemctl enable database-service
sudo systemctl start database-service

# Check status
sudo systemctl status database-service
```

### 6. Test Production Setup
```bash
# Test via HTTPS (through Nginx)
curl https://database-service.chcit.org/api/health

# Check logs
sudo tail -f /var/log/database-service/database-service.log
sudo tail -f /var/log/nginx/database-service.access.log
```

## Architecture Overview

```
Development:
Client → Database Service (HTTP :8080)

Production:
Client → Nginx (HTTPS :443) → Database Service (HTTP :8080)
```

## Configuration Files

- `config-dev.json` - Development (SSL disabled)
- `config-prod.json` - Production template
- `config.json` - Default configuration

## Key Features

### Security
- JWT authentication with refresh tokens
- Secure credential storage (production)
- Rate limiting via Nginx
- CORS protection

### Database
- PostgreSQL connection pooling
- SSL support for database connections
- Schema migration support
- Connection metrics

### Monitoring
- Health check endpoint
- Database metrics API
- Comprehensive logging
- Nginx access logs

## Troubleshooting

### Service Won't Start
```bash
# Check service logs
sudo journalctl -u database-service -f

# Check configuration
./database-service --config config.json --validate
```

### Database Connection Issues
```bash
# Test PostgreSQL connection
psql -h localhost -U database_service_user -d gitdashboard

# Check SSL certificates (if enabled)
openssl x509 -in /etc/letsencrypt/live/chcit.org/fullchain.pem -text -noout
```

### Nginx Issues
```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/database-service.error.log
```

### SSL Certificate Issues
```bash
# Check certificate validity
sudo certbot certificates

# Renew certificates
sudo certbot renew --dry-run
```

## API Endpoints

### Public Endpoints
- `GET /api/health` - Health check
- `POST /api/auth/login` - User authentication

### Authenticated Endpoints
- `GET /api/auth/user` - Get user info
- `POST /api/auth/logout` - Logout
- `POST /api/query` - Execute database query
- `POST /api/execute` - Execute database statement

### Admin Endpoints
- `GET /api/database/metrics` - Database metrics
- `POST /api/credentials/store` - Store credentials
- `GET /api/credentials/get` - Retrieve credentials

## Security Notes

### Development
- SSL disabled for simplicity
- Weak JWT secret (change in production)
- Debug logging enabled
- CORS allows all origins

### Production
- SSL required for database connections
- Strong JWT secrets required
- Secure credential storage enabled
- CORS restricted to specific domains
- Rate limiting enabled
- Security headers applied

## Next Steps

1. **Development**: Start with `config-dev.json` for local testing
2. **Production**: Use `config-prod.json` as template
3. **Integration**: Connect to your PostgreSQL database
4. **Monitoring**: Set up log monitoring and alerting
5. **Backup**: Configure database backup procedures
