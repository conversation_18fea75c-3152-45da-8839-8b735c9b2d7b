# Database Service

A standalone C++23 database management application designed for reusability across multiple projects and servers.

## Overview

The Database Service provides centralized database functionality for other C++23 applications, eliminating duplication and simplifying troubleshooting. It's designed to be reusable across different servers and projects with minimal refactoring.

## Key Features

- **Modern C++23 Features**: Utilizing C++23 features supported by GCC 14.2 (std::format, std::expected, etc.)
- **Component-Based Architecture**: Organized codebase with clear boundaries between components
- **Multi-Threaded Design**: Asynchronous database operations with thread pooling
- **Multi-Database Architecture**: Separate databases for each application with clear boundaries
- **Schema Management**: Create and manage multiple schemas within each database
- **Secure PostgreSQL Connection Management**: SSL-enabled connections with connection pooling
- **Schema Migration**: Versioned database schemas with automated migration
- **Role-Based Access Control**: Fine-grained permissions for database operations
- **RESTful API**: HTTP API for database operations with authentication
- **Client Library**: C++ client library for easy integration with other applications
- **Configuration Management**: Flexible configuration via files and environment variables
- **Comprehensive Logging**: Structured logging with multiple output destinations
- **Data Encryption**: AES-256 encryption for sensitive data
- **JSON Support**: Full support for JSON/JSONB data storage alongside relational models

## Architecture

### Component Structure

The Database Service is organized into the following components:

```
database-service-traditional
├── include/database-service/     - Header files
│   ├── core/                     - Core functionality and interfaces
│   ├── api/                      - API server and request handlers
│   ├── schema/                   - Schema management and migrations
│   ├── security/                 - Authentication and authorization
│   ├── utils/                    - Utility classes
│   └── database_service.hpp      - Main service interface
├── src/                          - Implementation files
│   ├── core/                     - Core implementation
│   ├── api/                      - API implementation
│   ├── schema/                   - Schema implementation
│   ├── security/                 - Security implementation
│   ├── utils/                    - Utility implementation
│   ├── database_service.cpp      - Main service implementation
│   └── main.cpp                  - Entry point
```

This component-based architecture provides:

1. **Clear Boundaries**: Each component has a well-defined responsibility
2. **Separation of Interface and Implementation**: Headers define the interface, source files contain the implementation
3. **Encapsulation**: Private implementation details are hidden from clients
4. **Explicit Dependencies**: Dependencies are clearly visible through include statements

### Multi-threaded Architecture for Asynchronous Operations

The Database Service uses multi-threading for asynchronous operations:

```cpp
// Asynchronous query execution using callbacks
void executeQueryAsync(const std::string& query,
                      std::function<void(Result<QueryResult>)> callback) {
    std::thread([this, query, callback]() {
        auto connection = connectionManager_->getConnection();
        auto result = connection->executeQuery(query);
        connectionManager_->returnConnection(connection);
        callback(result);
    }).detach();
}
```

This approach provides:

1. **Non-blocking I/O**: Database operations don't block the main thread
2. **Responsive API**: The API remains responsive while database operations are in progress
3. **Efficient Resource Usage**: Connection pooling ensures efficient use of database connections
4. **Error Handling**: Structured error handling with Result<T> type

### Database Architecture

The Database Service follows a multi-database architecture with clear separation between applications:

```
PostgreSQL Server
+-- database_service (database)
   +-- public (schema)
       +-- schema_version, applications, users, permissions (tables)

+-- git_repo_db (database)
   +-- public (schema)
      +-- repositories, commits, branches (tables)
   +-- metadata (schema)
       +-- repository_stats, user_activity (tables)

+-- logging_db (database)
    +-- public (schema)
       +-- logs, log_levels (tables)
    +-- archive (schema)
       +-- historical_logs (tables)
    +-- stats (schema)
        +-- log_statistics, error_trends (tables)
```

This architecture provides:

1. **Complete Isolation**: Each application has its own database
2. **Clear Ownership**: No confusion about which application owns which data
3. **Independent Management**: Each database can be backed up, restored, or migrated independently
4. **Security Boundaries**: Permissions can be set at the database level
5. **Performance Tuning**: Database settings can be optimized for each application's needs

## Getting Started

### Prerequisites

- C++23 compatible compiler (GCC 14.2+)
- CMake 3.20+
- Boost 1.74+
- PostgreSQL 17+ with development libraries
- OpenSSL 1.1.1+
- nlohmann/json 3.9.0+
- libpqxx (PostgreSQL C++ client library)

### Building

```bash
# Clone the repository
git clone https://github.com/your-org/database-service-traditional.git
cd database-service-traditional

# Create build directory
mkdir build
cd build

# Configure with CMake
cmake .. -DCMAKE_CXX_COMPILER=g++-14.2 -DCMAKE_CXX_STANDARD=23

# Build
cmake --build .

# Install (optional)
cmake --install .
```

### Configuration

The Database Service is configured using a JSON configuration file. A template is provided in `config/config.template.json`. Copy this file to `config/config.json` and modify it to suit your needs.

```json
{
  "application": {
    "name": "database-service",
    "environment": "{{ENVIRONMENT}}",
    "log_level": "{{LOG_LEVEL}}"
  },
  "database": {
    "host": "{{DB_HOST}}",
    "port": {{DB_PORT}},
    "admin_db": "database_service",
    "user": "{{DB_USER}}",
    "password": "{{DB_PASSWORD}}",
    "ssl": {
      "enabled": {{DB_SSL_ENABLED}},
      "cert_path": "{{SSL_CERT_PATH}}",
      "key_path": "{{SSL_KEY_PATH}}",
      "ca_path": "{{SSL_CA_PATH}}"
    },
    "pool_size": {{DB_POOL_SIZE}},
    "connection_timeout": {{DB_CONNECTION_TIMEOUT}}
  },
  "applications": [
    {
      "name": "git-repo-service",
      "database": "git_repo_db",
      "schemas": ["public", "metadata"]
    },
    {
      "name": "logging-service",
      "database": "logging_db",
      "schemas": ["public", "archive", "stats"]
    }
  ],
  "api": {
    "host": "{{API_HOST}}",
    "port": {{API_PORT}},
    "workers": {{API_WORKERS}},
    "ssl": {
      "enabled": {{API_SSL_ENABLED}},
      "cert_path": "{{SSL_CERT_PATH}}",
      "key_path": "{{SSL_KEY_PATH}}"
    }
  },
  "security": {
    "token_expiration_minutes": {{TOKEN_EXPIRATION}},
    "encryption_key": "{{ENCRYPTION_KEY}}"
  }
}
```

### Running

```bash
# Run with default configuration
./database-service

# Run with custom configuration
./database-service --config /path/to/config.json

# Run with custom port
./database-service --port 8081

# Run with custom log level
./database-service --log-level debug

# Run with log file
./database-service --log-file /var/log/database-service.log
```

## API Reference

The Database Service exposes a RESTful API for database operations. All endpoints require authentication using a Bearer token.

See the [API Documentation](docs/api.md) for detailed information.

## Client Library

The Database Service includes a C++ client library for easy integration with other applications. Here's a simple example:

```cpp
#include "database-service/client/database_client.hpp"
#include <iostream>
#include <future>

int main() {
    try {
        // Create database client
        dbservice::client::DatabaseClient client(
            "http://localhost:8080",  // Service URL
            "git-repo-service",       // Application name
            "api-key-123",            // API key
            "git_repo_db"             // Database name
        );

        // Check if database is available
        if (!client.isAvailable()) {
            std::cerr << "Database is not available" << std::endl;
            return 1;
        }

        // Query data (asynchronous with callback)
        std::promise<std::vector<nlohmann::json>> resultPromise;
        std::future<std::vector<nlohmann::json>> resultFuture = resultPromise.get_future();

        client.queryAsync(
            "SELECT * FROM repositories WHERE owner = $1",
            {{"$1", "btaylor-admin"}},
            [&resultPromise](auto result) {
                if (result) {
                    resultPromise.set_value(result.value());
                } else {
                    resultPromise.set_exception(std::make_exception_ptr(
                        std::runtime_error(result.error())
                    ));
                }
            }
        );

        // Do other work while query is executing
        std::cout << "Query is executing..." << std::endl;

        // Get results
        auto results = resultFuture.get();

        // Display results
        for (const auto& row : results) {
            std::cout << "ID: " << row["id"].get<int>() << ", "
                     << "Name: " << row["name"].get<std::string>() << std::endl;
        }

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
```

See the [Client Library Documentation](docs/client.md) for more details.

## Deployment\n\nThe Database Service is deployed as a systemd service on the production server (git.chcit.org - ***********). A deployment script is provided in `scripts/deploy-database-service.bat` for Windows environments.

```bash
# Deploy to server
./scripts/deploy-database-service.bat server-name
```

See the [Installation Guide](docs/installation.md) for detailed instructions.

## Database and Schema Management

The Database Service supports multiple databases and schemas for clear separation between applications:

1. **Central Management Database**: Tracks all application databases and schemas
2. **Application-Specific Databases**: Each application has its own dedicated database
3. **Multiple Schemas**: Each database can have multiple schemas for different aspects of functionality
4. **JSON Support**: Full support for JSON/JSONB data storage alongside relational models

This approach provides:

- **Clear Boundaries**: Each application has its own database with clear ownership
- **Flexibility**: Each database can have multiple schemas for different aspects of functionality
- **Performance**: Each database can be tuned for its specific workload
- **Security**: Permissions can be managed at the database level
- **Maintainability**: Databases can be backed up, restored, or migrated independently

## Security

The Database Service implements several security measures:

- **SSL/TLS**: All connections (API and PostgreSQL) are encrypted using SSL/TLS
- **Authentication**: API endpoints require authentication using Bearer tokens
- **Authorization**: Database operations are authorized using role-based access control
- **Data Encryption**: Sensitive data is encrypted using AES-256
- **Secure Configuration**: Passwords and encryption keys can be provided via environment variables

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

