cmake_minimum_required(VERSION 3.16)
project(git-repo-service VERSION 1.0.0 LANGUAGES CXX)

# Set C++23 standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find required packages
find_package(Boost REQUIRED COMPONENTS system)
find_package(OpenSSL REQUIRED)
find_package(CURL REQUIRED)

# Find JsonCpp using PkgConfig as a fallback if find_package fails
find_package(jsoncpp QUIET)
if(NOT jsoncpp_FOUND)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(J<PERSON><PERSON><PERSON> REQUIRED jsoncpp)
    set(JSONCPP_LIBRARIES jsoncpp)
endif()

# Find libgit2
find_package(PkgConfig REQUIRED)
pkg_check_modules(LIBGIT2 REQUIRED libgit2)

# Set include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${Boost_INCLUDE_DIRS}
    ${OPENSSL_INCLUDE_DIR}
    ${CURL_INCLUDE_DIRS}
    ${JSONCPP_INCLUDE_DIRS}
    ${LIBGIT2_INCLUDE_DIRS}
)

# Set link directories
link_directories(
    ${LIBGIT2_LIBRARY_DIRS}
)

# Add source files
file(GLOB_RECURSE SOURCES "src/*.cpp")

# Create executable
add_executable(git-repo-service ${SOURCES})

# Link libraries
target_link_libraries(git-repo-service
    ${Boost_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${CURL_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    jsoncpp
    ${LIBGIT2_LIBRARIES}
    pthread
)

# Install target
install(TARGETS git-repo-service
    RUNTIME DESTINATION bin
)
