# UI.psm1 - User interface helper functions for database service deployment scripts

<#
.SYNOPSIS
    Provides UI helper functions for database service deployment scripts.

.DESCRIPTION
    This module implements UI display functions and utilities for consistent
    menu rendering and user interaction across all deployment scripts.

.NOTES
    File Name      : UI.psm1
    Author         : Database Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Script-level variables
$script:UIMode = $false

<#
.SYNOPSIS
    Enables UI Mode for interactive displays.

.DESCRIPTION
    Enables UI Mode, which changes the logging behavior to only display UI-level messages
    on the console. Other messages are still logged to the file if file logging is enabled.

.EXAMPLE
    Enable-UIMode
    Write-Host "Menu Title" -ForegroundColor Cyan
#>
function Enable-UIMode {
    [CmdletBinding()]
    param()

    $script:UIMode = $true
    
    # If the Logger module is loaded, inform it about UI mode change
    if (Get-Module -Name "Logger" -ErrorAction SilentlyContinue) {
        if (Get-Command -Name "Write-Log" -ErrorAction SilentlyContinue) {
            Write-Log -Message "UI Mode enabled" -Level "Debug" -Component "UI"
        }
    } else {
        Write-Verbose "UI Mode enabled"
    }
}

<#
.SYNOPSIS
    Disables UI Mode and returns to normal logging.

.DESCRIPTION
    Disables UI Mode, returning to normal logging behavior where all messages at or above
    the current log level are displayed on the console.

.EXAMPLE
    Disable-UIMode
    Write-Host "Operation completed" -ForegroundColor Gray
#>
function Disable-UIMode {
    [CmdletBinding()]
    param()

    $script:UIMode = $false
    
    # If the Logger module is loaded, inform it about UI mode change
    if (Get-Module -Name "Logger" -ErrorAction SilentlyContinue) {
        if (Get-Command -Name "Write-Log" -ErrorAction SilentlyContinue) {
            Write-Log -Message "UI Mode disabled" -Level "Debug" -Component "UI"
        }
    } else {
        Write-Verbose "UI Mode disabled"
    }
}

<#
.SYNOPSIS
    Checks if UI Mode is currently enabled.

.DESCRIPTION
    Returns a boolean indicating whether UI Mode is currently enabled.

.EXAMPLE
    if (Test-UIMode) {
        # Format for UI display
    }

.RETURNS
    Boolean indicating whether UI Mode is enabled.
#>
function Test-UIMode {
    [CmdletBinding()]
    [OutputType([bool])]
    param()

    return $script:UIMode
}

<#
.SYNOPSIS
    Displays a menu title with consistent formatting.

.DESCRIPTION
    Shows a title line for a menu with consistent formatting and styling.

.PARAMETER Title
    The title text to display.

.EXAMPLE
    Show-MenuTitle -Title "Main Menu"
#>
function Show-MenuTitle {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$Title
    )

    # Enable UI Mode automatically
    Enable-UIMode

    # Use Write-Log if available, otherwise fall back to Write-Host
    if (Get-Command -Name "Write-Log" -ErrorAction SilentlyContinue) {
        Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
        Write-Log -Message "$(' ' * ((56 - $Title.Length) / 2))$Title" -Level "UI" -ForegroundColor Blue
        Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
        Write-Log -Message " " -Level "UI"
    } else {
        Write-Host "========================================================" -ForegroundColor Blue
        Write-Host "$(' ' * ((56 - $Title.Length) / 2))$Title" -ForegroundColor Blue
        Write-Host "========================================================" -ForegroundColor Blue
        Write-Host ""
    }
}

<#
.SYNOPSIS
    Displays a formatted menu option.

.DESCRIPTION
    Shows a formatted menu option with consistent styling.

.PARAMETER Number
    The option number/key.

.PARAMETER Text
    The description text for the option.

.EXAMPLE
    Show-MenuOption -Number "1" -Text "Edit Configuration"
#>
function Show-MenuOption {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$Number,

        [Parameter(Mandatory=$true)]
        [string]$Text
    )

    # Use Write-Log if available, otherwise fall back to Write-Host
    if (Get-Command -Name "Write-Log" -ErrorAction SilentlyContinue) {
        Write-Log -Message "[$Number] $Text" -Level "UI" -ForegroundColor White
    } else {
        Write-Host "[$Number] $Text" -ForegroundColor White
    }
}

<#
.SYNOPSIS
    Displays a section divider in menus.

.DESCRIPTION
    Shows a formatted section divider line for menus.

.EXAMPLE
    Show-MenuDivider
#>
function Show-MenuDivider {
    [CmdletBinding()]
    param()

    # Use Write-Log if available, otherwise fall back to Write-Host
    if (Get-Command -Name "Write-Log" -ErrorAction SilentlyContinue) {
        Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
    } else {
        Write-Host "---------------------------------------" -ForegroundColor Gray
    }
}

<#
.SYNOPSIS
    Prompts for user input with a formatted message.

.DESCRIPTION
    Displays a prompt and returns the user's input.

.PARAMETER Prompt
    The prompt message to display.

.EXAMPLE
    $choice = Get-UserInput -Prompt "Select an option (1-5)"

.RETURNS
    The user's input as a string.
#>
function Get-UserInput {
    [CmdletBinding()]
    [OutputType([string])]
    param(
        [Parameter(Mandatory=$true)]
        [string]$Prompt
    )

    return Read-Host "`n$Prompt"
}

# Export the UI functions
Export-ModuleMember -Function Enable-UIMode, Disable-UIMode, Test-UIMode, Show-MenuTitle, Show-MenuOption, Show-MenuDivider, Get-UserInput
