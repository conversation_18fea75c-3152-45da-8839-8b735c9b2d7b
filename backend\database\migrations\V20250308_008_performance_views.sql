-- Add version control and validation
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'schema_version') THEN
        INSERT INTO schema_version (version, description, applied_at)
        VALUES ('V20250308_008', 'Performance views for metrics', CURRENT_TIMESTAMP);
    END IF;
END $$;

-- Improved materialized view with partitioning and better indexing
CREATE MATERIALIZED VIEW mv_improvement_metrics (
    project_id,
    category_id,
    total_improvements,
    avg_change_size,
    max_impact_score,
    last_updated
) WITH (timescaledb.continuous) AS
SELECT 
    i.project_id,
    i.category_id,
    COUNT(*) as total_improvements,
    AVG(i.lines_added + i.lines_deleted) as avg_change_size,
    MAX(i.system_impact_score) as max_impact_score,
    CURRENT_TIMESTAMP as last_updated
FROM improvements i
WHERE i.created_at >= NOW() - INTERVAL '1 year'
GROUP BY i.project_id, i.category_id;

-- Create composite index for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mv_improvement_metrics_composite
ON mv_improvement_metrics (project_id, category_id, last_updated);

-- Add monitoring for refresh operations
CREATE TABLE mv_refresh_log (
    id SERIAL PRIMARY KEY,
    view_name VARCHAR(100),
    refresh_started_at TIMESTAMP WITH TIME ZONE,
    refresh_completed_at TIMESTAMP WITH TIME ZONE,
    success BOOLEAN,
    error_message TEXT
);

-- Improved refresh function with logging and error handling
CREATE OR REPLACE FUNCTION refresh_improvement_metrics()
RETURNS trigger AS $$
DECLARE
    start_time TIMESTAMP WITH TIME ZONE;
    success BOOLEAN := false;
BEGIN
    start_time := CURRENT_TIMESTAMP;
    
    BEGIN
        REFRESH MATERIALIZED VIEW CONCURRENTLY mv_improvement_metrics;
        success := true;
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO mv_refresh_log (view_name, refresh_started_at, refresh_completed_at, success, error_message)
        VALUES ('mv_improvement_metrics', start_time, CURRENT_TIMESTAMP, false, SQLERRM);
        RAISE;
    END;
    
    INSERT INTO mv_refresh_log (view_name, refresh_started_at, refresh_completed_at, success)
    VALUES ('mv_improvement_metrics', start_time, CURRENT_TIMESTAMP, success);
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;