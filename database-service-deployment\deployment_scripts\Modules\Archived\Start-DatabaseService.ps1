# Start Database Service Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Start database service function
function Start-DatabaseService {
    Clear-Host
    
    # Enable UI Mode for menu display
    Enable-UIMode
    
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Start Database Service                 " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    
    # Disable UI Mode after menu display
    Disable-UIMode
    
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Service"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    
    # Check if the service is installed
    $serviceName = $Config.service.name
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    
    if ($serviceExists -eq 0) {
        Write-Log -Message "Service $serviceName is not installed." -Level "Error" -Component "Service"
        Write-Log -Message "Please install the service first." -Level "Warning" -Component "Service"
        Wait-ForUser
        & "$PSScriptRoot\Install-Service.ps1"
        return
    }
    
    # Check the current status of the service
    $statusCmd = "systemctl is-active $serviceName"
    $serviceStatus = Invoke-RemoteCommand -Command $statusCmd -Silent
    
    if ($serviceStatus -eq "active") {
        Write-Log -Message "Service $serviceName is already running." -Level "Success" -Component "Service"
        $restart = Read-Host "Do you want to restart the service? (y/n)"
        
        if ($restart -eq "y") {
            Write-Log -Message "Restarting service..." -Level "Info" -Component "Service"
            $restartCmd = "sudo systemctl restart $serviceName"
            Invoke-RemoteCommand -Command $restartCmd
            
            # Verify the service is running
            $statusCmd = "systemctl is-active $serviceName"
            $serviceStatus = Invoke-RemoteCommand -Command $statusCmd -Silent
            
            if ($serviceStatus -eq "active") {
                Write-Log -Message "Service $serviceName restarted successfully!" -Level "Success" -Component "Service"
            } else {
                Write-Log -Message "Failed to restart service $serviceName." -Level "Error" -Component "Service"
                Write-Log -Message "Current status: $serviceStatus" -Level "Error" -Component "Service"
            }
        }
    } else {
        Write-Log -Message "Starting service $serviceName..." -Level "Info" -Component "Service"
        $startCmd = "sudo systemctl start $serviceName"
        Invoke-RemoteCommand -Command $startCmd
        
        # Verify the service is running
        $statusCmd = "systemctl is-active $serviceName"
        $serviceStatus = Invoke-RemoteCommand -Command $statusCmd -Silent
        
        if ($serviceStatus -eq "active") {
            Write-Log -Message "Service $serviceName started successfully!" -Level "Success" -Component "Service"
        } else {
            Write-Log -Message "Failed to start service $serviceName." -Level "Error" -Component "Service"
            Write-Log -Message "Current status: $serviceStatus" -Level "Error" -Component "Service"
            
            # Get service logs to help diagnose the issue
            Write-Log -Message " " -Level "UI"
            Write-Log -Message "Service logs:" -Level "Warning" -Component "Service"
            $logsCmd = "journalctl -u $serviceName -n 20 --no-pager"
            $logs = Invoke-RemoteCommand -Command $logsCmd
            Write-Log -Message $logs -Level "Verbose" -Component "Service"
        }
    }
    
    Show-MainMenu
}

# Run the function
Start-DatabaseService
