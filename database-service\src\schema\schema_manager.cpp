#include "database-service/schema/schema_manager.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <format> // C++20 feature
#include <filesystem>
#include <fstream>
#include <sstream>
#include <regex>

namespace dbservice::schema {

SchemaManager::SchemaManager(std::shared_ptr<core::ConnectionManager> connectionManager, 
                           const std::string& schemaDirectory)
    : connectionManager_(connectionManager),
      schemaDirectory_(schemaDirectory),
      initialized_(false) {
}

SchemaManager::~SchemaManager() {
}

bool SchemaManager::initialize() {
    if (initialized_) {
        return true;
    }
    
    try {
        utils::Logger::info("Initializing schema manager...");
        
        // Create migrations table
        if (!createMigrationsTable()) {
            utils::Logger::error("Failed to create migrations table");
            return false;
        }
        
        // Load migration files
        if (!loadMigrationFiles()) {
            utils::Logger::error("Failed to load migration files");
            return false;
        }
        
        initialized_ = true;
        utils::Logger::info("Schema manager initialized successfully");
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during schema manager initialization: {}", e.what()));
        return false;
    }
}

bool SchemaManager::createSchema(const std::string& schemaName) {
    if (!initialized_ && !initialize()) {
        return false;
    }
    
    try {
        // Check if schema already exists
        if (schemaExists(schemaName)) {
            utils::Logger::info(std::format("Schema {} already exists", schemaName));
            return true;
        }
        
        // Create schema
        std::string query = std::format("CREATE SCHEMA IF NOT EXISTS {}", schemaName);
        int result = connectionManager_->executeNonQuery(query, {});
        
        if (result < 0) {
            utils::Logger::error(std::format("Failed to create schema {}", schemaName));
            return false;
        }
        
        utils::Logger::info(std::format("Schema {} created successfully", schemaName));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during schema creation: {}", e.what()));
        return false;
    }
}

bool SchemaManager::dropSchema(const std::string& schemaName) {
    if (!initialized_ && !initialize()) {
        return false;
    }
    
    try {
        // Check if schema exists
        if (!schemaExists(schemaName)) {
            utils::Logger::info(std::format("Schema {} does not exist", schemaName));
            return true;
        }
        
        // Drop schema
        std::string query = std::format("DROP SCHEMA IF EXISTS {} CASCADE", schemaName);
        int result = connectionManager_->executeNonQuery(query, {});
        
        if (result < 0) {
            utils::Logger::error(std::format("Failed to drop schema {}", schemaName));
            return false;
        }
        
        utils::Logger::info(std::format("Schema {} dropped successfully", schemaName));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during schema deletion: {}", e.what()));
        return false;
    }
}

bool SchemaManager::schemaExists(const std::string& schemaName) {
    if (!initialized_ && !initialize()) {
        return false;
    }
    
    try {
        std::string query = "SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1";
        auto result = connectionManager_->executeQuery(query, {schemaName});
        
        return !result.empty();
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during schema existence check: {}", e.what()));
        return false;
    }
}

std::vector<std::string> SchemaManager::getSchemas() {
    std::vector<std::string> schemas;
    
    if (!initialized_ && !initialize()) {
        return schemas;
    }
    
    try {
        std::string query = "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('pg_catalog', 'information_schema', 'pg_toast')";
        auto result = connectionManager_->executeQuery(query, {});
        
        for (const auto& row : result) {
            if (!row.empty()) {
                schemas.push_back(row[0]);
            }
        }
        
        return schemas;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during schema list retrieval: {}", e.what()));
        return schemas;
    }
}

bool SchemaManager::applyMigration(const std::string& schemaName, const std::string& migrationName) {
    if (!initialized_ && !initialize()) {
        return false;
    }
    
    try {
        // Check if migration already applied
        auto appliedMigrations = getAppliedMigrations(schemaName);
        if (std::find(appliedMigrations.begin(), appliedMigrations.end(), migrationName) != appliedMigrations.end()) {
            utils::Logger::info(std::format("Migration {} already applied to schema {}", migrationName, schemaName));
            return true;
        }
        
        // Check if migration file exists
        auto it = migrationFiles_.find(migrationName);
        if (it == migrationFiles_.end()) {
            utils::Logger::error(std::format("Migration file {} not found", migrationName));
            return false;
        }
        
        // Create schema if it doesn't exist
        if (!schemaExists(schemaName)) {
            if (!createSchema(schemaName)) {
                utils::Logger::error(std::format("Failed to create schema {}", schemaName));
                return false;
            }
        }
        
        // Begin transaction
        auto transaction = connectionManager_->beginTransaction();
        if (!transaction) {
            utils::Logger::error("Failed to begin transaction for migration");
            return false;
        }
        
        try {
            // Apply migration
            std::string migrationSql = it->second;
            
            // Replace schema placeholder with actual schema name
            migrationSql = std::regex_replace(migrationSql, std::regex("\\{\\{schema\\}\\}"), schemaName);
            
            // Execute migration
            int result = transaction->getConnection()->executeNonQuery(migrationSql, {});
            
            if (result < 0) {
                utils::Logger::error(std::format("Failed to apply migration {} to schema {}", migrationName, schemaName));
                transaction->rollback();
                return false;
            }
            
            // Record migration
            std::string insertQuery = "INSERT INTO schema_migrations (schema_name, migration_name, applied_at) VALUES ($1, $2, NOW())";
            result = transaction->getConnection()->executeNonQuery(insertQuery, {schemaName, migrationName});
            
            if (result < 0) {
                utils::Logger::error(std::format("Failed to record migration {} for schema {}", migrationName, schemaName));
                transaction->rollback();
                return false;
            }
            
            // Commit transaction
            if (!transaction->commit()) {
                utils::Logger::error(std::format("Failed to commit migration {} for schema {}", migrationName, schemaName));
                return false;
            }
            
            utils::Logger::info(std::format("Migration {} applied to schema {} successfully", migrationName, schemaName));
            return true;
        } catch (const std::exception& e) {
            utils::Logger::error(std::format("Exception during migration application: {}", e.what()));
            transaction->rollback();
            return false;
        }
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during migration application: {}", e.what()));
        return false;
    }
}

std::vector<std::string> SchemaManager::getAppliedMigrations(const std::string& schemaName) {
    std::vector<std::string> migrations;
    
    if (!initialized_ && !initialize()) {
        return migrations;
    }
    
    try {
        std::string query = "SELECT migration_name FROM schema_migrations WHERE schema_name = $1 ORDER BY applied_at";
        auto result = connectionManager_->executeQuery(query, {schemaName});
        
        for (const auto& row : result) {
            if (!row.empty()) {
                migrations.push_back(row[0]);
            }
        }
        
        return migrations;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during applied migrations retrieval: {}", e.what()));
        return migrations;
    }
}

bool SchemaManager::createMigrationsTable() {
    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS schema_migrations (
                id SERIAL PRIMARY KEY,
                schema_name VARCHAR(255) NOT NULL,
                migration_name VARCHAR(255) NOT NULL,
                applied_at TIMESTAMP NOT NULL,
                UNIQUE (schema_name, migration_name)
            )
        )";
        
        int result = connectionManager_->executeNonQuery(query, {});
        
        if (result < 0) {
            utils::Logger::error("Failed to create migrations table");
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during migrations table creation: {}", e.what()));
        return false;
    }
}

bool SchemaManager::loadMigrationFiles() {
    try {
        // Check if schema directory exists
        if (schemaDirectory_.empty() || !std::filesystem::exists(schemaDirectory_)) {
            utils::Logger::warning(std::format("Schema directory not found: {}", schemaDirectory_));
            return true; // Not a fatal error
        }
        
        // Clear existing migration files
        migrationFiles_.clear();
        
        // Load migration files
        for (const auto& entry : std::filesystem::directory_iterator(schemaDirectory_)) {
            if (entry.is_regular_file() && entry.path().extension() == ".sql") {
                std::string filename = entry.path().filename().string();
                
                // Read file content
                std::ifstream file(entry.path());
                if (!file.is_open()) {
                    utils::Logger::warning(std::format("Failed to open migration file: {}", filename));
                    continue;
                }
                
                std::stringstream buffer;
                buffer << file.rdbuf();
                
                // Store migration file
                migrationFiles_[filename] = buffer.str();
                
                utils::Logger::debug(std::format("Loaded migration file: {}", filename));
            }
        }
        
        utils::Logger::info(std::format("Loaded {} migration files", migrationFiles_.size()));
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during migration files loading: {}", e.what()));
        return false;
    }
}

} // namespace dbservice::schema
