# Git Dashboard - Complete Project Documentation

This document provides a comprehensive overview of the Git Dashboard project, including all files, their relationships, and their purposes.

## Project Overview

The Git Dashboard is a web-based monitoring and management tool for a Git server running on Ubuntu. The dashboard runs on a Windows build server and connects to the Ubuntu Git server at git.chcit.org using SSH authentication with the btaylor-admin user.

### Architecture

- **Frontend**: Vanilla JavaScript, Bootstrap 5.2.3, Chart.js 4.2.1, Font Awesome 5.15.4
- **Backend**: Flask Python web server
- **Web Server**: Nginx (configured as a reverse proxy)
- **Data Collection**: Shell scripts for metrics gathering
- **Deployment**: Uses systemd for service management

### Environment

- Running on Windows build server
- Connects to Ubuntu Git server at git.chcit.org
- Uses SSH authentication with btaylor-admin user
- Uses PowerShell scripts with SSH keys

### Server Infrastructure

#### Build Server
- **OS**: Windows Server 2022
- **IP**: ***********
- **Function**: Development environment where Windsurf IDE is installed
- **SSH Configuration**: Has SSH keys configured to connect to both Ubuntu servers as btaylor-admin user

#### Git Dashboard Server
- **Hostname**: git.chcit.org
- **IP**: ***********
- **OS**: Ubuntu 24.04.2
- **Function**: Hosts Git server and dashboard
- **Services**: 
  - Git service (username: git)
  - Flask web application for dashboard
  - Nginx as web server

#### Project Tracker Server
- **Hostname**: project-tracker.chcit.org
- **IP**: ***********
- **OS**: Ubuntu 24.04.2
- **Function**: Main application server, also used to sync LetsEncrypt SSL certificates for *.chcit.org domains

## Project Structure

### Root Directory Files

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| README.md | 3,270 | Provides setup instructions and prerequisites for deploying the Git Dashboard on an Ubuntu server |
| app.py | 59,380 | The main Flask application file that defines the web server, routes, and API endpoints |
| collect-metrics.sh | 4,304 | Shell script for collecting server metrics and Git repository statistics |
| deploy.bat | 2,023 | Windows batch file for deployment |
| deploy.ps1 | 5,061 | PowerShell script for deployment |
| deployment-checklist.md | 3,052 | Checklist for ensuring proper file permissions and configurations before deploying |
| diagnostics.html | 13,485 | HTML page for diagnostic information |
| git-dashboard.conf | 5,223 | Nginx configuration file for the Git Dashboard |
| git-dashboard.conf-README.md | 158 | README for the Nginx configuration file |
| index.html | 18,546 | Main HTML file that serves as the user interface for the Git Dashboard |
| installation-steps.md | 3,427 | Step-by-step instructions for installing the Git Dashboard |
| requirements.txt | 78 | Lists Python dependencies (Flask, psutil, GitPython, etc.) |
| sudoers-config.txt | 145 | Configuration for sudo permissions |
| sync-certificates.sh | 11,097 | Script for synchronizing SSL certificates from the main server |
| test-console.js | 874 | JavaScript file for testing console functionality |
| test.html | 2,095 | HTML file for testing purposes |

### API Directory

The `api` directory is currently empty but is likely intended for future API modules.

### Bin Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| cert_sync_helper | 43,464 | Binary helper program for certificate synchronization |
| cert_sync_helper.cpp | 4,613 | C++ source code for the certificate synchronization helper |

### CSS Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| animations.css | 1,036 | CSS for animations and transitions |
| certificates.css | 4,735 | Styling for the certificates component |
| core.css | 2,827 | Core CSS styles for the dashboard |
| dashboard.css | 8,108 | Styling for the main dashboard layout |
| logging.css | 4,215 | Styling for the logging component |
| metrics.css | 945 | Styling for the metrics component |
| repositories.css | 3,048 | Styling for the Git repositories component |
| responsive.css | 3,167 | CSS for responsive design and mobile compatibility |

### Data Directory

The `data` directory contains a subdirectory `history` which is currently empty but is intended for storing metrics history data.

### Docs Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| BACKUP-RESTORE-GUIDE.md | 12,547 | Guide for backup and restore procedures |
| CRON-SSL-SYNC-GUIDE.md | 13,208 | Guide for setting up cron jobs for SSL certificate synchronization |
| ENVIRONMENT-VARIABLES-CONFIG.md | 6,510 | Documentation for environment variable configuration |
| FLASK-GUNICORN-GUIDE.md | 18,499 | Guide for setting up Flask with Gunicorn |
| GIT-DASHBOARD-SETUP-GUIDE.md | 10,668 | Comprehensive setup guide for the Git Dashboard |
| MASTER-INDEX.md | 5,301 | Master index of all documentation |
| NGINX-CONFIGURATION-GUIDE.md | 8,753 | Guide for configuring Nginx |
| SECURITY-ENHANCEMENTS.md | 14,955 | Documentation for security enhancements |
| UBUNTU-CONFIGURATION-GUIDE.md | 13,583 | Guide for configuring Ubuntu for the Git Dashboard |
| css-implementation-guide.md | 3,440 | Guide for CSS implementation |
| deployment-checklist.md | 3,052 | Checklist for deployment |
| file-analysis.md | 1,811 | Analysis of file structure |
| git-component-refactoring.md | 2,778 | Documentation for Git component refactoring |
| git-dashboard-architecture.md | 6,137 | Overview of the Git Dashboard architecture |
| git-dashboard.conf | 5,223 | Nginx configuration file (duplicate of root directory file) |
| git-dashboard.conf-README.md | 158 | README for the Nginx configuration file (duplicate) |
| installation-steps.md | 3,427 | Step-by-step installation instructions (duplicate) |
| requirements.txt | 78 | Python dependencies (duplicate) |
| sudoers-config.txt | 145 | Sudo configuration (duplicate) |

### JS Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| api-check.js | 7,362 | JavaScript for API health checks |
| certificates.js | 23,329 | JavaScript for managing SSL certificates |
| dashboard.js | 4,765 | Core dashboard functionality |
| git-commit-history-chart.js | 12,765 | Chart visualization for commit history |
| git-debug.js | 13,395 | Debugging tools for Git functionality |
| git-repositories.js | 55,051 | Main JavaScript for Git repository management |
| git-repository-list.js | 18,075 | JavaScript for repository list view |
| git-repository-manager.js | 15,949 | Manager class for Git repositories |
| git-repository-modal.js | 13,575 | JavaScript for repository details modal |
| git-size-comparison-chart.js | 10,102 | Chart for repository size comparison |
| git-summary-view.js | 18,293 | JavaScript for Git summary view |
| git-utils.js | 11,361 | Utility functions for Git operations |
| logging.js | 18,277 | Client-side and server-side logging system |
| metrics-history.js | 27,232 | Chart rendering for system metrics history |
| performance-gauges.js | 14,211 | JavaScript for performance gauge visualizations |
| server-metrics.js | 13,690 | JavaScript for server metrics display |

### Logs Directory

The `logs` directory contains a subdirectory `dashboard_logs` which is currently empty but is intended for storing application logs.

### Nginx Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| git-dashboard.conf | 5,223 | Nginx configuration file for the Git Dashboard |

### Static Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| diagnostics.html | 13,485 | Static HTML file for diagnostics |

### Systemd Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| git-dashboard.service | 680 | Systemd service configuration for the Git Dashboard |

### Templates Directory

| File | Size (bytes) | Purpose |
|------|-------------|--------|
| diagnostics.html | 13,485 | HTML template for diagnostics |

## Component Relationships

### Backend Components

1. **Flask Application (app.py)**
   - Serves as the backbone of the application
   - Handles HTTP requests and routes them to appropriate functions
   - Defines API endpoints for metrics, certificates, and Git repositories
   - Interacts with shell scripts for data collection and certificate synchronization

2. **Shell Scripts**
   - **collect-metrics.sh**: Collects server metrics and Git repository statistics
   - **sync-certificates.sh**: Synchronizes SSL certificates from the main server

3. **Certificate Sync Helper**
   - **cert_sync_helper**: Binary program for secure certificate synchronization
   - **cert_sync_helper.cpp**: Source code for the helper program

### Frontend Components

1. **HTML (index.html)**
   - Main user interface for the Git Dashboard
   - Organizes the dashboard into tabs for different functionalities
   - Links to CSS and JavaScript files

2. **CSS Files**
   - Provide styling for different components of the dashboard
   - Organized into modular files for different parts of the application

3. **JavaScript Files**
   - Implement client-side functionality for the dashboard
   - Organized into modular files for different components
   - Handle data fetching, visualization, and user interactions

### API Endpoints

- **/api/metrics/current**: Current server metrics
- **/api/metrics/history**: Historical metrics data
- **/api/certificates/status**: SSL certificate information
- **/api/certificates/sync**: Trigger certificate synchronization
- **/api/git/repositories**: Git repository statistics
- **/api/logs**: Dashboard logging functionality

## Git Repository Component Enhancements

### Implemented Enhancements

1. **CSS Styling**:
   - Added styles for repository list view
   - Implemented health indicator styling with color-coding (good, fair, poor, unknown)
   - Created styles for the repository details modal
   - Added styling for the view toggle button

2. **Commit History Visualization**:
   - Added a bar chart showing commit activity over the past 30 days
   - Implemented Chart.js integration for data visualization
   - Added chart initialization and cleanup in the repository details modal
   - Created sample data generation for demonstration purposes

3. **Repository Health Indicators**:
   - Implemented visual indicators showing repository health based on activity
   - Added health status calculation based on last commit date
   - Created a health progress bar in the repository details modal
   - Added descriptive health messages

### Tabbed Layout Implementation

1. **Tabbed Interface**:
   - Implemented a tabbed layout with four tabs: Overview, Repositories, Metrics, and Certificates
   - Each tab contains the relevant components, providing better organization and visibility
   - The Overview tab shows the server performance and git repository stats at a glance
   - The Repositories tab contains the repository list and details
   - The Metrics tab shows the historical metrics chart
   - The Certificates tab displays the SSL certificate information

2. **Dashboard Header**:
   - Updated the title to "Git Repository Dashboard"
   - Made the title smaller and more stylish with an icon
   - Made the refresh button smaller and moved it to a dedicated container
   - Moved the last updated timestamp below the refresh button
   - Added a border-bottom to separate the header from the content

3. **CSS Styling**:
   - Added styles for the new tabbed interface
   - Styled the tabs with icons and hover effects
   - Improved the tab content container styling
   - Enhanced mobile responsiveness for the tabbed layout

4. **Logging Component**:
   - Changed the default log count from 10 to 5 logs
   - Fixed the logging component at the bottom of the window
   - Set a fixed height of 200px for the logging component
   - Made the logging component the same width as the tab content area

## Potential Future Enhancements

1. **Repository Search and Filtering**: Controls to search, sort, and filter the repository list
2. **Repository Size Visualization**: Charts showing the relative sizes of repositories
3. **Branch Management Overview**: Statistics and management tools for branches across repositories
4. **User Activity Tracking**: Displays of user contribution metrics across repositories
5. **Repository Backup Status**: Information about backup status and health
6. **Repository Actions**: Buttons for common repository management tasks

## Deployment and Configuration

### Deployment Process

1. Copy all files to the server
2. Set appropriate file permissions
3. Configure Nginx with the provided configuration file
4. Set up systemd service for the Flask application
5. Configure cron jobs for metrics collection and certificate synchronization

### Configuration Files

1. **git-dashboard.conf**: Nginx configuration file
2. **git-dashboard.service**: Systemd service configuration
3. **requirements.txt**: Python dependencies
4. **sudoers-config.txt**: Sudo permissions configuration

## Conclusion

The Git Dashboard is a comprehensive web-based tool for monitoring and managing a Git server. It provides real-time metrics, repository information, and SSL certificate management through a user-friendly interface. The project follows a modular architecture with clear separation of concerns, making it easy to maintain and extend.
