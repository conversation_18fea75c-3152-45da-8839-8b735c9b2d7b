[2025-03-07 19:33:39] [INFO] Creating deployment directory: project-tracker-deploy
[2025-03-07 19:33:39] [PROGRESS] Deployment Preparation : Creating deployment directory
[2025-03-07 19:33:39] [INFO] Validating deployment prerequisites...
[2025-03-07 19:33:39] [PROGRESS] Prerequisite Check : Starting validation
[2025-03-07 19:33:39] [INFO] Available Disk Space: 24.5208396911621 GB
[2025-03-07 19:33:39] [PROGRESS] Prerequisite Check : Validation completed successfully
[2025-03-07 19:33:39] [INFO] Removing existing deployment directory
[2025-03-07 19:33:39] [INFO] Copying scripts/validate-deployment.py
[2025-03-07 19:33:39] [PROGRESS] File Copy : Copying scripts/validate-deployment.py
[2025-03-07 19:33:39] [INFO] Copying scripts/validate-configuration.py
[2025-03-07 19:33:39] [PROGRESS] File Copy : Copying scripts/validate-configuration.py
[2025-03-07 19:33:39] [INFO] Copying scripts/validate-build-environment.py
[2025-03-07 19:33:39] [PROGRESS] File Copy : Copying scripts/validate-build-environment.py
[2025-03-07 19:33:39] [INFO] Copying scripts/auto-fix.py
[2025-03-07 19:33:39] [PROGRESS] File Copy : Copying scripts/auto-fix.py
[2025-03-07 19:33:39] [INFO] Copying scripts/backup-cron.sh
[2025-03-07 19:33:39] [PROGRESS] File Copy : Copying scripts/backup-cron.sh
[2025-03-07 19:33:39] [INFO] Copying scripts/configure-server.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/configure-server.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/project-creation.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/project-creation.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/security-hardening.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/security-hardening.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/ssl-setup.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/ssl-setup.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/verify-nginx-modules.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/verify-nginx-modules.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/setup-code-quality.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/setup-code-quality.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/monitor-performance.sh
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/monitor-performance.sh
[2025-03-07 19:33:40] [INFO] Copying scripts/validate-optimizations.ps1
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/validate-optimizations.ps1
[2025-03-07 19:33:40] [INFO] Copying scripts/validate-frontend.ps1
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/validate-frontend.ps1
[2025-03-07 19:33:40] [INFO] Copying scripts/generate-performance-report.ps1
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying scripts/generate-performance-report.ps1
[2025-03-07 19:33:40] [INFO] Copying config/nginx.conf
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying config/nginx.conf
[2025-03-07 19:33:40] [INFO] Copying config/project-tracker.service
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying config/project-tracker.service
[2025-03-07 19:33:40] [INFO] Copying config/performance-dashboard.json
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying config/performance-dashboard.json
[2025-03-07 19:33:40] [INFO] Copying config/nginx-modules.conf
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying config/nginx-modules.conf
[2025-03-07 19:33:40] [INFO] Copying .pre-commit-config.yaml
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying .pre-commit-config.yaml
[2025-03-07 19:33:40] [INFO] Copying .flake8
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying .flake8
[2025-03-07 19:33:40] [INFO] Copying .eslintrc.json
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying .eslintrc.json
[2025-03-07 19:33:40] [INFO] Copying .prettierrc
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying .prettierrc
[2025-03-07 19:33:40] [INFO] Copying requirements.txt
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying requirements.txt
[2025-03-07 19:33:40] [INFO] Copying pyproject.toml
[2025-03-07 19:33:40] [PROGRESS] File Copy : Copying pyproject.toml
[2025-03-07 19:33:41] [INFO] Copying README.md
[2025-03-07 19:33:41] [PROGRESS] File Copy : Copying README.md
[2025-03-07 19:33:41] [WARNING] File not found: README.md
[2025-03-07 19:33:41] [INFO] Copying directory: src
[2025-03-07 19:33:41] [PROGRESS] Directory Copy : Copying src
[2025-03-07 19:33:41] [WARNING] Directory not found: src
[2025-03-07 19:33:41] [INFO] Copying directory: templates
[2025-03-07 19:33:41] [PROGRESS] Directory Copy : Copying templates
[2025-03-07 19:33:41] [WARNING] Directory not found: templates
[2025-03-07 19:33:41] [INFO] Copying directory: static
[2025-03-07 19:33:41] [PROGRESS] Directory Copy : Copying static
[2025-03-07 19:33:41] [WARNING] Directory not found: static
[2025-03-07 19:33:41] [INFO] Copying directory: database
[2025-03-07 19:33:41] [PROGRESS] Directory Copy : Copying database
[2025-03-07 19:33:41] [WARNING] Directory not found: database
[2025-03-07 19:33:41] [INFO] Creating directory: logs
[2025-03-07 19:33:41] [PROGRESS] Directory Creation : Creating logs
[2025-03-07 19:33:41] [INFO] Creating directory: data
[2025-03-07 19:33:41] [PROGRESS] Directory Creation : Creating data
[2025-03-07 19:33:41] [INFO] Deployment package created successfully
[2025-03-07 19:33:41] [PROGRESS] Deployment : Package creation completed
[2025-03-07 19:34:26] [INFO] Creating deployment directory: project-tracker-deploy
[2025-03-07 19:34:26] [PROGRESS] Deployment Preparation : Creating deployment directory
[2025-03-07 19:34:26] [INFO] Validating deployment prerequisites...
[2025-03-07 19:34:26] [PROGRESS] Prerequisite Check : Starting validation
[2025-03-07 19:34:26] [INFO] Available Disk Space: 24.5210380554199 GB
[2025-03-07 19:34:26] [PROGRESS] Prerequisite Check : Validation completed successfully
[2025-03-07 19:34:26] [INFO] Removing existing deployment directory
[2025-03-07 19:34:26] [INFO] Copying scripts/validate-deployment.py
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/validate-deployment.py
[2025-03-07 19:34:26] [INFO] Copying scripts/validate-configuration.py
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/validate-configuration.py
[2025-03-07 19:34:26] [INFO] Copying scripts/validate-build-environment.py
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/validate-build-environment.py
[2025-03-07 19:34:26] [INFO] Copying scripts/auto-fix.py
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/auto-fix.py
[2025-03-07 19:34:26] [INFO] Copying scripts/backup-cron.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/backup-cron.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/configure-server.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/configure-server.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/project-creation.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/project-creation.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/security-hardening.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/security-hardening.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/ssl-setup.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/ssl-setup.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/verify-nginx-modules.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/verify-nginx-modules.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/setup-code-quality.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/setup-code-quality.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/monitor-performance.sh
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/monitor-performance.sh
[2025-03-07 19:34:26] [INFO] Copying scripts/validate-optimizations.ps1
[2025-03-07 19:34:26] [PROGRESS] File Copy : Copying scripts/validate-optimizations.ps1
[2025-03-07 19:34:26] [INFO] Copying scripts/validate-frontend.ps1
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying scripts/validate-frontend.ps1
[2025-03-07 19:34:27] [INFO] Copying scripts/generate-performance-report.ps1
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying scripts/generate-performance-report.ps1
[2025-03-07 19:34:27] [INFO] Copying config/nginx.conf
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying config/nginx.conf
[2025-03-07 19:34:27] [INFO] Copying config/project-tracker.service
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying config/project-tracker.service
[2025-03-07 19:34:27] [INFO] Copying config/performance-dashboard.json
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying config/performance-dashboard.json
[2025-03-07 19:34:27] [INFO] Copying config/nginx-modules.conf
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying config/nginx-modules.conf
[2025-03-07 19:34:27] [INFO] Copying .pre-commit-config.yaml
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying .pre-commit-config.yaml
[2025-03-07 19:34:27] [INFO] Copying .flake8
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying .flake8
[2025-03-07 19:34:27] [INFO] Copying .eslintrc.json
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying .eslintrc.json
[2025-03-07 19:34:27] [INFO] Copying .prettierrc
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying .prettierrc
[2025-03-07 19:34:27] [INFO] Copying requirements.txt
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying requirements.txt
[2025-03-07 19:34:27] [INFO] Copying README.md
[2025-03-07 19:34:27] [PROGRESS] File Copy : Copying README.md
[2025-03-07 19:34:27] [WARNING] File not found: README.md
[2025-03-07 19:34:27] [INFO] Copying directory: src
[2025-03-07 19:34:27] [PROGRESS] Directory Copy : Copying src
[2025-03-07 19:34:27] [WARNING] Directory not found: src
[2025-03-07 19:34:27] [INFO] Copying directory: templates
[2025-03-07 19:34:27] [PROGRESS] Directory Copy : Copying templates
[2025-03-07 19:34:27] [WARNING] Directory not found: templates
[2025-03-07 19:34:27] [INFO] Copying directory: static
[2025-03-07 19:34:27] [PROGRESS] Directory Copy : Copying static
[2025-03-07 19:34:27] [WARNING] Directory not found: static
[2025-03-07 19:34:27] [INFO] Copying directory: database
[2025-03-07 19:34:27] [PROGRESS] Directory Copy : Copying database
[2025-03-07 19:34:27] [WARNING] Directory not found: database
[2025-03-07 19:34:27] [INFO] Creating directory: logs
[2025-03-07 19:34:27] [PROGRESS] Directory Creation : Creating logs
[2025-03-07 19:34:27] [INFO] Creating directory: data
[2025-03-07 19:34:27] [PROGRESS] Directory Creation : Creating data
[2025-03-07 19:35:57] [ERROR] Frontend build failed
[2025-03-07 19:35:57] [PROGRESS] Deployment Failed : Frontend build failed
