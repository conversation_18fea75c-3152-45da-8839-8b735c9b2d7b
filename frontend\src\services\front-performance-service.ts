/**
 * Performance Service
 * 
 * This service handles collection and reporting of performance metrics
 * for the Project Tracker application.
 */

import axios from 'axios';

/**
 * Interface for performance metric data
 */
export interface PerformanceMetric {
  category: string;
  operation: string;
  duration: number;
  status: 'success' | 'error' | 'warning';
  metadata?: Record<string, any>;
  timestamp: string;
}

// Configuration options
const CONFIG = {
  enabled: true,
  sampleRate: 1.0, // 1.0 = track all events, 0.5 = track 50% of events
  batchSize: 10,
  flushInterval: 30000, // 30 seconds
  endpoint: '/api/performance/metrics'
};

// Queue for batching metrics
let metricsQueue: PerformanceMetric[] = [];
let flushTimeoutId: number | null = null;

/**
 * Track a performance metric
 * 
 * @param metric - The performance metric to track
 */
export function trackMetric(metric: PerformanceMetric): void {
  if (!CONFIG.enabled) return;
  
  // Apply sampling rate
  if (Math.random() > CONFIG.sampleRate) return;
  
  // Add metric to queue
  metricsQueue.push(metric);
  
  // If we've reached batch size, flush immediately
  if (metricsQueue.length >= CONFIG.batchSize) {
    flushMetrics();
  } else if (!flushTimeoutId) {
    // Otherwise set a timeout to flush after interval
    flushTimeoutId = window.setTimeout(flushMetrics, CONFIG.flushInterval);
  }
}

/**
 * Flush metrics to the server
 */
export function flushMetrics(): void {
  if (metricsQueue.length === 0) return;
  
  // Clear timeout if it exists
  if (flushTimeoutId) {
    clearTimeout(flushTimeoutId);
    flushTimeoutId = null;
  }
  
  // Get metrics to send and clear queue
  const metricsToSend = [...metricsQueue];
  metricsQueue = [];
  
  // Send metrics to server
  axios.post(CONFIG.endpoint, { metrics: metricsToSend })
    .catch(error => {
      console.error('Failed to send performance metrics:', error);
      // In case of error, add the metrics back to the queue
      // but only if the queue isn't already getting too large
      if (metricsQueue.length < 100) {
        metricsQueue = [...metricsQueue, ...metricsToSend];
      }
    });
}

/**
 * Configure the performance service
 * 
 * @param options - Configuration options
 */
export function configurePerformanceService(options: Partial<typeof CONFIG>): void {
  Object.assign(CONFIG, options);
}

/**
 * Initialize the performance service
 */
export function initPerformanceService(): void {
  // Flush metrics when page is about to unload
  window.addEventListener('beforeunload', flushMetrics);
  
  // Start flush interval
  flushTimeoutId = window.setTimeout(flushMetrics, CONFIG.flushInterval);
  
  // Log initialization
  console.log('Performance service initialized with sample rate:', CONFIG.sampleRate);
}

/**
 * Measure the execution time of a function
 * 
 * @param category - The category of the operation
 * @param operation - The specific operation being performed
 * @param fn - The function to measure
 * @param metadata - Additional metadata
 * @returns The result of the function
 */
export async function measureExecutionTime<T>(
  category: string,
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const startTime = performance.now();
  let status: 'success' | 'error' = 'success';
  
  try {
    const result = await fn();
    return result;
  } catch (error) {
    status = 'error';
    throw error;
  } finally {
    const duration = performance.now() - startTime;
    trackMetric({
      category,
      operation,
      duration,
      status,
      metadata,
      timestamp: new Date().toISOString()
    });
  }
}

// Initialize the service when this module is imported
initPerformanceService();
