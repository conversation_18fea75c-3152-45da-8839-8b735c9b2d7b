/* metrics.css - Metrics display and chart styles */

/* Metrics Display */
.metric-value {
    font-size: 24px;
    font-weight: bold;
}
.metric-label {
    font-size: 14px;
    color: #6c757d;
}

.refresh-countdown {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Metrics History Chart */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-loading, 
.chart-error, 
.chart-no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    border: 1px solid #dee2e6;
    z-index: 10;
}

.chart-error {
    color: #dc3545;
}

.refresh-controls {
    display: flex;
    align-items: center;
}

/* Metrics refresh controls styling */
.btn-group .dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}
