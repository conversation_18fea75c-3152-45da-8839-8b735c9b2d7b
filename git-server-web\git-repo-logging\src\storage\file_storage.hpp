#pragma once
#include "storage_types.hpp"
#include <mutex>
#include <string>
#include <unordered_map>
#include <filesystem>

namespace logging {

class FileStorage : public StorageTier {
public:
    FileStorage(const std::string& baseDir, bool enableCompression, int compressionAgeDays);
    virtual ~FileStorage() = default;
    
    // StorageTier interface implementation
    bool store(const LogEntry& entry) override;
    bool storeBatch(const std::vector<LogEntry>& entries) override;
    std::vector<LogEntry> query(const LogQueryParams& params) override;
    StorageStats getStats() override;
    bool prune(std::chrono::system_clock::time_point olderThan) override;
    bool compact() override;
    bool backup(const std::string& backupPath) override;
    bool canHandle(const LogEntry& entry) const override;
    std::string getName() const override { return "File"; }
    int getPriority() const override { return 3; } // Lowest priority
    
private:
    // Helper methods
    std::string getFilePath(const LogEntry& entry) const;
    std::string getFilePath(const std::string& source, const std::chrono::system_clock::time_point& timestamp) const;
    std::string formatDate(const std::chrono::system_clock::time_point& timestamp) const;
    std::string formatTimestamp(const std::chrono::system_clock::time_point& timestamp) const;
    std::string entryToString(const LogEntry& entry) const;
    LogEntry stringToEntry(const std::string& line, const std::string& source) const;
    std::vector<std::string> getFilesInDateRange(const std::string& source, 
                                                std::optional<std::chrono::system_clock::time_point> startTime,
                                                std::optional<std::chrono::system_clock::time_point> endTime) const;
    bool compressFile(const std::string& filePath);
    std::vector<std::string> readLinesFromFile(const std::string& filePath) const;
    std::vector<std::string> readLinesFromCompressedFile(const std::string& filePath) const;
    bool matchesQuery(const LogEntry& entry, const LogQueryParams& params) const;
    void updateMetadata();
    void loadMetadata();
    
    std::string baseDir_;
    bool enableCompression_;
    int compressionAgeDays_;
    std::mutex mutex_;
    std::unordered_map<std::string, std::unordered_map<std::string, size_t>> fileStats_; // source -> date -> count
    std::filesystem::file_time_type lastMetadataUpdate_;
};

} // namespace logging
