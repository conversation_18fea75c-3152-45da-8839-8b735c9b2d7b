# Project Tracker Deployment Guide

## Prerequisites
- Ubuntu Server 24.04.2 LTS
- PowerShell 7+ (on deployment machine)
- System Requirements:
  - CPU: 2 vCPUs
  - RAM: 4GB
  - Storage: 20GB
  - Network: Configured static IP
- Cloudflare API token with DNS permissions

## Quick Start Guide

### Prerequisites
- Ubuntu Server 24.04.2 LTS
- Python 3.12+
- PostgreSQL 16+
- Nginx
- Redis

### Step 1: Prepare Deployment Package
```powershell
# On Windows build server
./prepare-deployment.ps1
```

### Step 2: Initial Validation
```bash
# On target Ubuntu server
sudo ./scripts/validate-deployment.py
```

### Step 3: Configure Server
```bash
sudo ./scripts/configure-server.sh
```

### Step 4: Deploy Application
```bash
sudo ./scripts/deploy.sh
```

## Detailed Deployment Steps

### 1. Preparation (Windows)
```powershell
# Run deployment preparation script
./prepare-deployment.ps1

# Verify deployment package
Get-ChildItem project-tracker-deploy/
```

### 2. Server Configuration (Ubuntu)

#### Base System Setup
```bash
# Copy deployment files
sudo mkdir -p /opt/project-tracker
sudo cp -r /tmp/project-tracker/* /opt/project-tracker/

# Run configuration script
cd /opt/project-tracker
sudo ./scripts/configure-server.sh

# Verify configuration
./scripts/validate-deployment.py
```

#### SSL Certificate Setup
```bash
# Set up Cloudflare token
sudo mkdir -p /etc/letsencrypt/cloudflare
sudo ./scripts/ssl-setup.sh project-tracker.chcit.org

# Verify SSL
curl -I https://project-tracker.chcit.org
```

#### Security Configuration
```bash
# Run security hardening
sudo ./scripts/security-hardening.sh

# Set up automated backups
sudo ./scripts/backup-cron.sh
```

## Configuration Files

### Service Configuration (`config/project-tracker.service`)
- Location: `/etc/systemd/system/project-tracker.service`
- Purpose: Manages application process
- Reload: `sudo systemctl daemon-reload`

### Nginx Configuration (`config/nginx.conf`)
- Location: `/etc/nginx/sites-available/project-tracker`
- Purpose: Web server and reverse proxy
- Test: `sudo nginx -t`
- Reload: `sudo systemctl reload nginx`

## Frontend Service Configuration

### Git Integration Service Setup
```typescript
// config/git-integration.config.ts
export const gitConfig = {
  baseUrl: process.env.GIT_API_URL || '/api/git',
  performance: {
    cloneTimeout: 30000,  // 30s target for 1GB repos
    fetchTimeout: 5000,   // 5s target for fetches
    pushThreshold: 10     // 10MB/s minimum push speed
  },
  cache: {
    enabled: true,
    maxSize: '2GB',
    ttl: 3600            // 1 hour cache lifetime
  }
};
```

### Task Management Service Setup
```typescript
// config/task-management.config.ts
export const taskConfig = {
  baseUrl: process.env.TASK_API_URL || '/api/tasks',
  batchSize: 50,         // Maximum tasks per bulk operation
  dependencies: {
    maxDepth: 5,         // Maximum dependency chain depth
    circular: false      // Prevent circular dependencies
  },
  statistics: {
    updateInterval: 300  // Stats refresh every 5 minutes
  }
};
```

### Environment Variables
```bash
# Git Integration
GIT_API_URL=http://localhost:3000/api/git
GIT_WEBHOOK_SECRET=your-secret-here
GIT_SSH_KEY_PATH=/path/to/ssh/key

# Task Management
TASK_API_URL=http://localhost:3000/api/tasks
TASK_BATCH_LIMIT=50
TASK_STATS_INTERVAL=300
```

## Maintenance

### Logs
- Application: `/var/log/project-tracker/app.log`
- Access: `/var/log/nginx/access.log`
- Error: `/var/log/nginx/error.log`
- Deployment: `/opt/project-tracker/deployment-*.log`

### Common Tasks
```bash
# Check service status
sudo systemctl status project-tracker

# View logs
sudo journalctl -u project-tracker -f

# Restart service
sudo systemctl restart project-tracker

# Run auto-fix script
sudo ./scripts/auto-fix.py
```

## Troubleshooting

### Common Issues

1. **Service Won't Start**
```bash
# Check logs
sudo journalctl -u project-tracker -n 50
# Verify permissions
sudo ./scripts/auto-fix.py --check-permissions
```

2. **SSL Certificate Issues**
```bash
# Check SSL status
sudo certbot certificates
# Renew certificates
sudo ./scripts/ssl-setup.sh --renew
```

3. **Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql
# Verify connection
sudo -u project-tracker psql -d project_tracker -c "\l"
```

### Getting Help
- Run validation: `./scripts/validate-deployment.py --verbose`
- Check auto-fix suggestions: `./scripts/auto-fix.py --analyze`
- View deployment guide: `less /opt/project-tracker/DEPLOYMENT.md`

## Security Notes
- All sensitive files are stored in `/etc/project-tracker/`
- SSL certificates in `/etc/letsencrypt/`
- Database credentials in `/etc/project-tracker/db.conf`
- Regular security updates via `unattended-upgrades`

## Version Information

### Component Versions
- Deployment Scripts: v1.0.0
- Configuration Files: v1.0.0
- Documentation: v1.0.0

### Version History
- v1.0.0 (2025-03-04)
  - Initial release
  - Standardized naming conventions
  - Added configuration validation
  - Improved documentation

## Configuration Reference

### nginx.conf
```nginx
# Version: 1.0.0
server {
    listen 443 ssl http2;
    server_name project-tracker.chcit.org;
    # ... configuration details ...
}
```

### project-tracker.service
```ini
# Version: 1.0.0
[Unit]
Description=Project Tracker Service
# ... service configuration ...

```

## Backend System Deployment Guide

### Overview
This document outlines the deployment process for the Project Tracker's backend system on Ubuntu target servers, including system configuration, security setup, and monitoring implementation.

### System Requirements
Last Updated: 2025-03-05 19:27

#### Hardware Requirements
- CPU: 2 vCPUs minimum
- RAM: 4GB minimum
- Storage: 20GB minimum
- Network: Configured static IP

#### Software Prerequisites
- Ubuntu Server 24.04.2 LTS
- Python 3.10+
- PostgreSQL 14+
- Nginx 1.24+ with Brotli and Image Filter modules
- Redis
- PowerShell 7+ (on deployment machine)
- Certbot 2.9.0+ with Cloudflare plugin

### Deployment Components

#### 1. Core Deployment Scripts
Located in `/scripts/`:

##### System Validation
- **validate-deployment.py**
  - Service status verification
  - Port availability checks
  - Database connectivity testing
  - Log file monitoring

- **validate-configuration.py**
  - Syntax validation for configs
  - Environment variable verification
  - Permission checks
  - Certificate validation

##### System Configuration
- **configure-server.sh**
  - System package installation
  - Python virtual environment
  - Nginx with Brotli and Image Filter
  - PostgreSQL database setup

- **project-creation.sh**
  - Directory structure creation
  - Permission setup
  - Initial database migration
  - Static file collection

##### Security Management
- **security-hardening.sh**
  - Firewall configuration
  - SSL/TLS hardening
  - File permission lockdown
  - Security headers setup

- **ssl-setup.sh**
  - Let's Encrypt integration
  - Auto-renewal setup
  - OCSP stapling
  - HTTP/2 configuration

##### Maintenance
- **auto-fix.py**
  - Package dependency conflicts
  - File permission issues
  - Common configuration errors
  - Service startup problems

- **backup-cron.sh**
  - Daily database backups
  - Weekly configuration backups
  - Monthly full system backups
  - 90-day retention policy

### 2. Configuration Files
Located in `/config/`:

#### Web Server
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name project-tracker.chcit.org;
    ssl_certificate /etc/letsencrypt/live/project-tracker.chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/project-tracker.chcit.org/privkey.pem;
    # Additional configuration...
}
```

#### Service Management
```ini
# project-tracker.service
[Unit]
Description=Project Tracker Service
After=network.target postgresql.service

[Service]
User=project-tracker
WorkingDirectory=/opt/project-tracker
ExecStart=/opt/project-tracker/venv/bin/gunicorn app:app
# Additional configuration...
```

### Deployment Steps

#### 1. Preparation (Windows Build Server)
```powershell
# Run deployment preparation
./prepare-deployment.ps1

# Verify package structure
Get-ChildItem project-tracker-deploy/
```

#### 2. Initial Setup (Ubuntu Server)
```bash
# Copy deployment package
sudo mkdir -p /opt/project-tracker
sudo cp -r project-tracker-deploy/* /opt/project-tracker/

# Validate environment
sudo ./scripts/validate-deployment.py --check-environment
```

#### 3. System Configuration
```bash
# Configure base system
sudo ./scripts/configure-server.sh

# Set up SSL certificates
sudo ./scripts/ssl-setup.sh project-tracker.chcit.org

# Apply security hardening
sudo ./scripts/security-hardening.sh

# Configure backups
sudo ./scripts/backup-cron.sh
```

#### 4. Application Deployment
```bash
# Initialize project
sudo ./scripts/project-creation.sh

# Deploy application
sudo ./scripts/deploy.sh

# Validate deployment
sudo ./scripts/validate-deployment.py --verbose
```

### Monitoring and Maintenance

#### System Logs
- Application: `/var/log/project-tracker/app.log`
- Access: `/var/log/nginx/access.log`
- Error: `/var/log/nginx/error.log`
- Deployment: `/opt/project-tracker/deployment-*.log`

#### Health Checks
```bash
# Service status
sudo systemctl status project-tracker

# Nginx status
sudo nginx -t
curl -I https://project-tracker.chcit.org

# Database status
sudo -u postgres psql -c "\l"
```

#### Common Maintenance Tasks
```bash
# Restart services
sudo systemctl restart project-tracker nginx

# Update SSL certificates
sudo ./scripts/ssl-setup.sh --renew

# Run automated fixes
sudo ./scripts/auto-fix.py --analyze
```

### Security Measures

#### File Permissions
- Configuration: `/etc/project-tracker/` (600)
- SSL Certificates: `/etc/letsencrypt/` (600)
- Application: `/opt/project-tracker/` (755)
- Logs: `/var/log/project-tracker/` (644)

#### SSL/TLS Configuration
- HTTP/2 and HTTP/3 enabled
- OCSP stapling
- Strong cipher suites
- Perfect forward secrecy

#### Backup Security
- Encrypted backups
- Secure transfer
- Access logging
- Retention policies

### Troubleshooting Guide

#### Common Issues

1. **Service Failures**
```bash
# Check service status
sudo systemctl status project-tracker
# View recent logs
sudo journalctl -u project-tracker -n 50
```

2. **Database Issues**
```bash
# Check PostgreSQL
sudo systemctl status postgresql
# Verify connections
sudo -u project-tracker psql -d project_tracker -c "\l"
```

3. **SSL Problems**
```bash
# Check certificates
sudo certbot certificates
# Test SSL configuration
curl -vI https://project-tracker.chcit.org
```

#### Auto-Fix Tool
```bash
# Analyze issues
sudo ./scripts/auto-fix.py --analyze

# Fix common problems
sudo ./scripts/auto-fix.py --fix-all

# Verify fixes
sudo ./scripts/validate-deployment.py
```

### Performance Optimization

#### Nginx Optimization
- Brotli compression (level 6)
- Image optimization
- HTTP/2 push
- Cache control

#### Database Tuning
- Connection pooling
- Query optimization
- Index management
- Regular vacuuming

#### System Tuning
- File descriptor limits
- Worker processes
- Buffer sizes
- Keep-alive settings

### Version Information

#### Current Versions
- Deployment Scripts: v1.0.0
- Configuration Files: v1.0.0
- Documentation: v1.0.0

#### Version History
- v1.0.0 (2025-03-05)
  - Initial release
  - Comprehensive deployment guide
  - Automated validation
  - Security hardening

## Backend System Deployment Guide

### Overview
This document outlines the deployment process for the Project Tracker's backend system, focusing on version control integration, development workflow, and local performance optimization.

### System Requirements

### Core Components
1. Git Server:
   - Git 2.34.0 or higher
   - Smart HTTP protocol support
   - SSH server for key-based auth
   - Webhook support

2. Development Environment:
   - Node.js 18.x or higher
   - Python 3.9 or higher
   - PostgreSQL 13 or higher

3. IDE Support:
   - VS Code 1.60 or higher
   - JetBrains IDEs 2023.1 or higher

### Deployment Components

### 1. Git Server Setup
```bash
# Install required packages
sudo apt-get update
sudo apt-get install -y git nginx

# Configure Git server
git config --system http.receivepack true
git config --system receive.denyNonFastForwards false

# Setup SSH access
sudo mkdir -p /var/git/keys
sudo chmod 700 /var/git/keys
```

### 2. Workspace Configuration
```bash
# Create workspace directories
sudo mkdir -p /opt/projects
sudo mkdir -p /tmp/workspace
sudo mkdir -p /backup/repos

# Set permissions
sudo chown -R git:git /opt/projects
sudo chmod -R 775 /opt/projects
```

### 3. IDE Integration Setup
```bash
# VS Code server setup
curl -fsSL https://code-server.dev/install.sh | sh
systemctl --user enable --now code-server

# JetBrains Projector setup
wget https://download.jetbrains.com/idea/ideaIU-2023.1.tar.gz
tar xzf ideaIU-2023.1.tar.gz
```

### Deployment Process

### 1. Initial Setup
```bash
# Clone deployment repository
git clone https://github.com/org/project-tracker.git
cd project-tracker

# Install dependencies
npm install
python -m pip install -r requirements.txt
```

### 2. Configuration
```yaml
# config/deployment.yml
git:
  server:
    protocol: smart-http
    port: 8080
    auth: basic
  hooks:
    - type: pre-receive
      script: hooks/validate-commit.sh
    - type: post-receive
      script: hooks/notify-ci.sh

workspace:
  root: /opt/projects
  temp: /tmp/workspace
  backup: /backup/repos

ide:
  vscode:
    extensions:
      - ms-vscode.git
      - eamodio.gitlens
  intellij:
    plugins:
      - Git4Idea
      - TaskManager
```

### 3. Git Workflow Setup
```bash
# Setup Git hooks
cp hooks/* /var/git/hooks/
chmod +x /var/git/hooks/*

# Configure branch protection
git config --system receive.denyDeletes true
git config --system receive.denyNonFastForwards true
```

### 4. IDE Integration
```bash
# VS Code settings
cat > /etc/vscode-server/config.yaml << EOF
git.enabled: true
git.autofetch: true
git.confirmSync: false
EOF

# JetBrains settings
cat > /etc/jetbrains/idea.properties << EOF
idea.git.ssh.auth=true
idea.git.autofetch=true
EOF
```

### Monitoring and Maintenance

### 1. Git Operations
```bash
# Monitor Git operations
tail -f /var/log/git/operations.log

# Check repository status
git count-objects -v
git fsck --full
```

### 2. Workspace Health
```bash
# Check disk usage
du -sh /opt/projects/*

# Backup repositories
./scripts/backup-repos.sh
```

### 3. IDE Performance
```bash
# VS Code diagnostics
code --status
code --performance

# JetBrains diagnostics
idea.sh diagnostic
```

### Security Configuration

### 1. Git Access Control
```bash
# Configure SSH keys
cat > /etc/git/auth.conf << EOF
[access "main"]
  read = @developers
  write = @maintainers
EOF
```

### 2. Workspace Isolation
```bash
# Set up workspace isolation
chroot /opt/projects
apparmor_parser -r /etc/apparmor.d/workspace
```

### 3. IDE Security
```bash
# Configure secure IDE access
cat > /etc/nginx/conf.d/ide.conf << EOF
location /ide/ {
    auth_request /auth;
    proxy_pass http://localhost:3000;
}
EOF
```

### Troubleshooting

### Common Issues

1. Git Access Issues:
```bash
# Check permissions
ls -la /var/git/repositories
# Fix permissions
chown -R git:git /var/git/repositories
```

2. Workspace Problems:
```bash
# Clear temporary files
rm -rf /tmp/workspace/*
# Rebuild workspace
./scripts/rebuild-workspace.sh
```

3. IDE Integration:
```bash
# Reset IDE configuration
rm -rf ~/.config/Code
rm -rf ~/.IntelliJIdea2023.1
# Reinstall extensions
code --install-extension ms-vscode.git
```

### Maintenance Schedule

### Daily Tasks
- Monitor Git operations
- Check workspace usage
- Verify IDE connections

### Weekly Tasks
- Backup repositories
- Update security rules
- Clean temporary files

### Monthly Tasks
- Full system backup
- Performance optimization
- Security audit

### Contributing
Follow the standard Git workflow:
1. Create feature branch
2. Make changes
3. Submit for review
4. Merge after approval

### Version Control
- Version: 1.0.0
- Last Updated: 2025-03-05
- Maintainer: DevOps Team
