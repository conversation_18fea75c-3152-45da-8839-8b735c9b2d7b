/**
 * Dashboard Logging System - MODIFIED for tab display
 * Provides logging functionality for the dashboard
 */
const DashboardLogger = {
    // Log storage
    logs: [],
    logCountOptions: [5, 10, 25, 50, 100],
    maxDisplayLogs: 10,  // Default number of logs to display
    currentLogLevel: 'all', // Default log level filter
    logLevelOptions: ['all', 'info', 'warning', 'error', 'success'],
    logSourceOptions: ['dashboard', 'system', 'nginx', 'git', 'auth'],
    currentLogSource: 'dashboard', // Default log source

    /**
     * Initialize the logger
     * @returns {Promise<void>}
     */
    async init() {
        console.log('Initializing dashboard logger...');
        try {
            // Set up event listeners for the controls (now in the tab header)
            this.setupControlListeners();

            // Fetch initial logs from the server - make sure we use the configured maxDisplayLogs
            console.log(`Initial fetch using maxDisplayLogs=${this.maxDisplayLogs}`);
            const logs = await this.fetchLogsFromServer(this.maxDisplayLogs);
            this.logs = logs || [];

            // Update the display
            await this.updateLogDisplay();

            console.log('Dashboard logger initialized successfully');
        } catch (error) {
            console.error('Error initializing dashboard logger:', error);
        }
    },

    /**
     * Set up event listeners for log controls
     */
    setupControlListeners() {
        // Log count selector
        const logCountSelector = document.getElementById('log-count-selector');
        if (logCountSelector) {
            logCountSelector.value = this.maxDisplayLogs; // Set initial value
            logCountSelector.addEventListener('change', async (event) => {
                const count = parseInt(event.target.value, 10);
                await this.changeLogCount(count);
            });
        }

        // Log level selector
        const logLevelSelector = document.getElementById('log-level-selector');
        if (logLevelSelector) {
            logLevelSelector.value = this.currentLogLevel; // Set initial value
            logLevelSelector.addEventListener('change', async (event) => {
                const level = event.target.value;
                await this.changeLogLevel(level);
            });
        }

        // Log source selector
        const logSourceSelector = document.getElementById('log-source-selector');
        if (logSourceSelector) {
            // Populate the source selector if it's empty
            if (logSourceSelector.options.length === 0) {
                this.logSourceOptions.forEach(source => {
                    const option = document.createElement('option');
                    option.value = source;
                    option.textContent = source.charAt(0).toUpperCase() + source.slice(1); // Capitalize
                    logSourceSelector.appendChild(option);
                });
            }

            logSourceSelector.value = this.currentLogSource; // Set initial value
            logSourceSelector.addEventListener('change', async (event) => {
                const source = event.target.value;
                await this.changeLogSource(source);
            });
        }

        // Refresh logs button
        const refreshLogsBtn = document.getElementById('refresh-logs-btn');
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', async () => {
                await this.refreshLogs();
            });
        }
    },

    /**
     * Add a log entry
     * @param {string} type - Log type (info, warning, error, success)
     * @param {string} message - Log message
     * @returns {Object} - Log entry
     */
    addLog(type, message) {
        // Create log entry
        const timestamp = new Date().toISOString();
        const logEntry = {
            type,
            message,
            timestamp,
            source: 'dashboard'
        };

        // Add to local logs
        this.logs.unshift(logEntry);

        // Update the display
        this.updateLogDisplay();

        // Save to server
        this.saveLogToServer(logEntry);

        // Return the log entry for potential further processing
        return logEntry;
    },

    /**
     * Save log entry to server
     * @param {Object} logEntry - Log entry to save
     * @returns {Promise<void>}
     */
    async saveLogToServer(logEntry) {
        try {
            // Clone the log entry and add date field for server if needed
            const serverLogEntry = { ...logEntry };
            if (!serverLogEntry.date) {
                serverLogEntry.date = new Date().toISOString(); // Add ISO format date for server-side storage
            }

            const response = await fetch('/api/logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(serverLogEntry)
            });

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error saving log to server:', error);
        }
    },

    /**
     * Fetch logs from server
     * @param {number} count - Number of logs to fetch
     * @param {string} source - Log source (dashboard, system, nginx, etc.)
     * @param {string} level - Log level filter (all, info, warning, error, success)
     * @returns {Promise<Array>} - Array of log entries
     */
    async fetchLogsFromServer(count = 10, source = null, level = null) {
        try {
            // Use current source and level if not specified
            const logSource = source || this.currentLogSource;
            const logLevel = level || this.currentLogLevel;
            console.log(`Fetching ${count} logs from server for source: ${logSource}, level: ${logLevel}`);

            // Make sure count is at least 1 and a number
            count = Math.max(1, parseInt(count) || 10);

            // Build the URL with parameters
            let url = `/api/logs?max=${count}`;
            if (logSource) {
                url += `&source=${logSource}`;
            }
            if (logLevel && logLevel !== 'all') {
                url += `&level=${logLevel}`;
            }

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            const logs = await response.json();
            console.log(`Fetched ${logs.length} logs from server for source: ${logSource}`);

            // Filter logs to ensure they match the requested source
            const filteredLogs = logs.filter(log => {
                // If no source specified in the log, default to dashboard
                const logSourceValue = log.source || 'dashboard';
                return logSourceValue === logSource;
            });

            console.log(`Filtered to ${filteredLogs.length} logs matching source: ${logSource}`);

            // Ensure logs are sorted by time (newest first)
            filteredLogs.sort((a, b) => {
                const dateA = a.timestamp || a.date || '';
                const dateB = b.timestamp || b.date || '';
                return new Date(dateB) - new Date(dateA);
            });

            return filteredLogs; // Return filtered logs
        } catch (error) {
            console.error('Error fetching logs from server:', error);
            return []; // Return empty array on error
        }
    },

    /**
     * Fetch all logs from server (for counting total available)
     * @param {string} source - Log source (dashboard, system, nginx, etc.)
     * @returns {Promise<Array>} - Array of all log entries
     */
    async fetchAllLogsFromServer(source = null) {
        try {
            // Use current source if not specified
            const logSource = source || this.currentLogSource;

            // Build the URL with parameters
            let url = '/api/logs?get_all=true';
            if (logSource && logSource !== 'dashboard') {
                url += `&source=${logSource}`;
            }

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            const logs = await response.json();
            console.log(`fetchAllLogsFromServer: Got ${logs.length} total logs from source ${logSource}`);
            return logs;
        } catch (error) {
            console.error('Error fetching all logs from server:', error);
            return [];
        }
    },

    /**
     * Format log message for display
     * @param {Object} log - Log entry
     * @returns {string} - Formatted message
     */
    formatLogMessage(log) {
        if (!log) return 'No message';

        let message = log.message || 'No message';

        // If the message is very long, truncate it for display
        if (message.length > 200) {
            message = message.substring(0, 197) + '...';
        }

        // If we have component information, add it to the message
        if (log.component) {
            message = `[${log.component}] ${message}`;
        }

        // If we have unit information, add it to the message
        if (log.unit && log.unit !== 'unknown') {
            message = `[${log.unit}] ${message}`;
        }

        // Escape HTML to prevent XSS
        message = message
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');

        return message;
    },

    /**
     * Filter logs by level
     * @param {Array} logs - Logs to filter
     * @returns {Array} - Filtered logs
     */
    filterLogsByLevel(logs) {
        if (!logs || !Array.isArray(logs)) {
            console.error('Invalid logs array passed to filterLogsByLevel:', logs);
            return [];
        }

        // If 'all' is selected, return all logs
        if (this.currentLogLevel === 'all') {
            return logs;
        }

        // Normalize log types for consistent filtering
        return logs.filter(log => {
            // Get the log type, defaulting to 'info' if not specified
            let logType = (log.type || 'info').toLowerCase();

            // Normalize log types to handle variations
            if (['err', 'error', 'critical', 'fatal', 'emergency', 'alert'].includes(logType)) {
                logType = 'error';
            } else if (['warn', 'warning', 'notice'].includes(logType)) {
                logType = 'warning';
            } else if (['success', 'ok', 'pass', 'passed'].includes(logType)) {
                logType = 'success';
            } else {
                // Default to info for any other type
                logType = 'info';
            }

            // Compare normalized type with selected level
            return logType === this.currentLogLevel;
        });
    },

    /**
     * Update the log display based on current logs
     */
    async updateLogDisplay() {
        try {
            const logCountDisplay = document.getElementById('log-count-display');
            const logContent = document.getElementById('dashboard-log-content');

            if (!logCountDisplay || !logContent) {
                console.error('Log display elements not found');
                return;
            }

            // Clear the log content
            logContent.innerHTML = '';

            // SIMPLIFIED APPROACH FOR LOG COUNTING

            // 1. Make sure we have the logs data
            if (!this.logs || !Array.isArray(this.logs)) {
                console.warn('No logs data available');
                logContent.innerHTML = '<div class="text-center p-3">No logs to display</div>';

                // Update header with 0 counts
                logCountDisplay.innerHTML = `(0/0 logs)`;
                return;
            }

            // 2. Filter logs based on current source and level
            // First filter by source
            const sourceFilteredLogs = this.logs.filter(log => {
                // Get the log source, defaulting to 'dashboard' if not specified
                const logSource = (log.source || 'dashboard').toLowerCase();
                const currentSource = this.currentLogSource.toLowerCase();

                // Exact match for source
                if (logSource === currentSource) {
                    return true;
                }

                // Special case for dashboard logs - they might have no source
                if (currentSource === 'dashboard' && !log.source) {
                    return true;
                }

                return false;
            });

            console.log(`Filtered ${this.logs.length} logs to ${sourceFilteredLogs.length} logs for source: ${this.currentLogSource}`);

            // Then filter by level
            const filteredLogs = this.filterLogsByLevel(sourceFilteredLogs);

            // Sort logs by timestamp (newest first)
            const sortedLogs = filteredLogs.sort((a, b) => {
                const timestampA = new Date(a.timestamp || a.date || 0).getTime();
                const timestampB = new Date(b.timestamp || b.date || 0).getTime();
                return timestampB - timestampA; // Descending order (newest first)
            });

            // 3. Limit to maximum display count
            const displayLogs = sortedLogs.slice(0, this.maxDisplayLogs);

            // 4. Calculate actual counts
            const showingCount = displayLogs.length;  // Actual number of logs we're displaying
            const totalFilteredCount = filteredLogs.length;  // Total logs after filtering

            // Make sure showing count never exceeds total count
            // This ensures we never display something logically impossible like "5/3 logs"
            const adjustedTotalCount = Math.max(totalFilteredCount, showingCount);

            // 5. Update header immediately with local counts
            logCountDisplay.innerHTML = `(${showingCount}/${adjustedTotalCount} logs)`;

            // 6. Display the logs
            if (displayLogs.length === 0) {
                const emptyRow = document.createElement('div');
                emptyRow.className = 'text-center p-3';
                emptyRow.innerText = 'No logs to display';
                logContent.appendChild(emptyRow);
            } else {
                // Loop through each log to display
                displayLogs.forEach((log, index) => {
                    // Format the timestamp
                    const timestamp = new Date(log.timestamp || log.date);
                    const formattedDate = timestamp.toLocaleString();

                    // Normalize and choose badge class based on log type
                    let badgeClass = 'bg-secondary';
                    let displayType = 'info';

                    // Get the log type, defaulting to 'info' if not specified
                    const logType = (log.type || 'info').toLowerCase();

                    // Normalize log types for display
                    if (['err', 'error', 'critical', 'fatal', 'emergency', 'alert'].includes(logType)) {
                        badgeClass = 'bg-danger';
                        displayType = 'error';
                    } else if (['warn', 'warning', 'notice'].includes(logType)) {
                        badgeClass = 'bg-warning';
                        displayType = 'warning';
                    } else if (['success', 'ok', 'pass', 'passed'].includes(logType)) {
                        badgeClass = 'bg-success';
                        displayType = 'success';
                    } else {
                        badgeClass = 'bg-info';
                        displayType = 'info';
                    }

                    // Create log row with log number
                    const logRow = document.createElement('div');
                    logRow.className = 'log-entry p-2';

                    // Calculate log number - use the actual position in displayed logs
                    const logNumber = index + 1;

                    // UPDATED: Modified HTML structure to keep entries on a single line
                    logRow.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <div class="log-content-wrapper">
                                <span class="log-timestamp me-2 text-muted">${formattedDate}</span>
                                <span class="log-type me-2"><span class="badge ${badgeClass}">${displayType}</span></span>
                                <span class="log-message text-truncate" title="${log.message || 'No message'}">
                                    ${this.formatLogMessage(log)}
                                </span>
                            </div>
                            <span class="log-number ms-2 badge bg-secondary rounded-pill" title="Log number">#${logNumber}</span>
                        </div>
                    `;

                    logContent.appendChild(logRow);
                });

                // Log what we've displayed for debugging
                console.log(`Displayed ${showingCount} logs out of ${adjustedTotalCount} total logs`);
            }

            // 7. Attempt to get total log count from server - this happens async and doesn't block UI
            this.fetchAllLogsFromServer().then(allLogs => {
                if (allLogs && Array.isArray(allLogs)) {
                    // Make sure we have logs data
                    if (allLogs.length > 0) {
                        // Get all currently displayed logs for an accurate showing count
                        const displayedLogs = document.querySelectorAll('#dashboard-log-content .log-entry').length;

                        // Update the total filtered count based on server data and current filter
                        const serverFilteredLogs = this.filterLogsByLevel(allLogs);
                        const serverFilteredCount = serverFilteredLogs.length;

                        // Ensure logical consistency - total count is never less than showing count
                        const finalTotalCount = Math.max(serverFilteredCount, displayedLogs);

                        // Update the display with more accurate counts from server data
                        logCountDisplay.innerHTML = `(${displayedLogs}/${finalTotalCount} logs)`;

                        console.log(`Updated log counts from server: ${displayedLogs}/${finalTotalCount} logs`);
                    }
                }
            }).catch(error => {
                console.error('Error getting total log count from server:', error);
            });

            // Update status indicator
            const statusIndicator = document.getElementById('logs-status-indicator');
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator success';
            }
        } catch (error) {
            console.error('Error updating log display:', error);

            // Update status indicator on error
            const statusIndicator = document.getElementById('logs-status-indicator');
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator error';
            }
        }
    },

    /**
     * Change the log level filter
     * @param {string} level - Log level (all, info, warning, error, success)
     */
    async changeLogLevel(level) {
        if (this.logLevelOptions.includes(level)) {
            console.log(`Changing log level filter to ${level}`);

            // Update internal state
            this.currentLogLevel = level;

            // Update selector if it exists
            const logLevelSelector = document.getElementById('log-level-selector');
            if (logLevelSelector) {
                logLevelSelector.value = level;
            }

            // Fetch new logs with the updated level filter
            try {
                const logs = await this.fetchLogsFromServer(this.maxDisplayLogs, this.currentLogSource, level);
                if (logs && logs.length > 0) {
                    this.logs = logs; // Replace the logs array with fresh data
                }
            } catch (error) {
                console.error('Error fetching logs after changing level:', error);
            }

            // Update the display with the new level filter
            await this.updateLogDisplay();

            console.log(`Log level filter changed to ${level}`);
        }
    },

    /**
     * Change the log source
     * @param {string} source - Log source (dashboard, system, nginx, etc.)
     */
    async changeLogSource(source) {
        if (this.logSourceOptions.includes(source)) {
            console.log(`Changing log source to ${source}`);

            // If we're already on this source, just refresh
            if (this.currentLogSource === source) {
                console.log(`Already on source ${source}, refreshing logs`);
                await this.refreshLogs(source);
                return;
            }

            // Update internal state
            this.currentLogSource = source;

            // Update selector if it exists
            const logSourceSelector = document.getElementById('log-source-selector');
            if (logSourceSelector) {
                logSourceSelector.value = source;
            }

            // Reset log level to 'all' when changing sources
            await this.changeLogLevel('all');

            // Fetch logs for the new source
            try {
                // Clear existing logs first to prevent mixing
                this.logs = [];

                // Fetch new logs for the selected source
                const logs = await this.fetchLogsFromServer(this.maxDisplayLogs, source);

                // Verify logs are for the correct source before adding them
                if (logs && logs.length > 0) {
                    // Double-check that logs are for the requested source
                    const validLogs = logs.filter(log => {
                        const logSource = (log.source || 'dashboard').toLowerCase();
                        const currentSource = source.toLowerCase();

                        // Exact match for source
                        if (logSource === currentSource) {
                            return true;
                        }

                        // Special case for dashboard logs - they might have no source
                        if (currentSource === 'dashboard' && !log.source) {
                            return true;
                        }

                        return false;
                    });

                    console.log(`Filtered ${logs.length} logs to ${validLogs.length} valid logs for source: ${source}`);
                    this.logs = validLogs; // Replace the logs array with filtered data
                }
            } catch (error) {
                console.error(`Error fetching ${source} logs:`, error);
                this.logs = []; // Clear logs on error
            }

            // Update the display
            await this.updateLogDisplay();

            console.log(`Log source changed to ${source}`);
        }
    },

    /**
     * Change the number of logs to display
     * @param {number} count - Number of logs to display
     */
    async changeLogCount(count) {
        count = parseInt(count);
        if (this.logCountOptions.includes(count)) {
            console.log(`Changing log count to ${count}`);

            // Update internal state
            this.maxDisplayLogs = count;

            // Update selector if it exists
            const logCountSelector = document.getElementById('log-count-selector');
            if (logCountSelector) {
                logCountSelector.value = count;
            }

            // Fetch fresh logs with the new count
            try {
                const logs = await this.fetchLogsFromServer(count);
                console.log(`Fetched ${logs.length} logs after changing count to ${count}`);
                if (logs && logs.length > 0) {
                    this.logs = logs; // Replace the logs array with fresh data
                }
            } catch (error) {
                console.error('Error fetching logs after changing count:', error);
            }

            // Update the display
            await this.updateLogDisplay();
        }
    },

    /**
     * Refresh logs data
     * @param {string} source - Log source (dashboard, system, nginx, etc.)
     * @returns {Promise<void>}
     */
    async refreshLogs(source = null) {
        // Use current source if not specified
        const logSource = source || this.currentLogSource;
        console.log(`Explicitly refreshing logs for source: ${logSource}`);

        // Update status indicator to loading
        const statusIndicator = document.getElementById('logs-status-indicator');
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator loading';
        }

        try {
            // Fetch fresh logs with current count setting and source
            const logs = await this.fetchLogsFromServer(this.maxDisplayLogs, logSource);

            if (logs && logs.length > 0) {
                this.logs = logs; // Replace the logs array with fresh data
            } else {
                this.logs = []; // Clear logs if none returned
            }

            // Update the display
            await this.updateLogDisplay();
            console.log(`Logs refreshed successfully for source: ${logSource}`);
        } catch (error) {
            console.error(`Error refreshing logs for source ${logSource}:`, error);

            // Update status indicator on error
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator error';
            }
        }
    }
};

// Expose the logger globally
window.DashboardLogger = DashboardLogger;

// Initialize the logger when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize with a short delay to ensure all DOM elements are fully loaded
    setTimeout(() => {
        DashboardLogger.init().catch(err => {
            console.error('Error initializing logger:', err);
        });
    }, 500);

    // Hook into the Dashboard refresh functionality if available
    if (window.Dashboard) {
        const originalRefresh = window.Dashboard.refreshDashboard;
        if (typeof originalRefresh === 'function') {
            window.Dashboard.refreshDashboard = async function() {
                // Call the original refresh function
                if (originalRefresh.apply) {
                    await originalRefresh.apply(window.Dashboard);
                } else {
                    await originalRefresh();
                }

                // Also refresh the logs
                console.log('Refreshing logs as part of dashboard refresh');
                if (window.DashboardLogger && typeof window.DashboardLogger.refreshLogs === 'function') {
                    await window.DashboardLogger.refreshLogs();
                }
            };
        }
    }
});
