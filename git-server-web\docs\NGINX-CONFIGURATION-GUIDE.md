# Nginx Configuration Guide for Git Dashboard

## Overview

This document provides detailed instructions for setting up and configuring <PERSON>in<PERSON> for the Git Dashboard system. Nginx serves as a reverse proxy for the Flask backend and handles static file serving, SSL termination, and security features.

## Basic Nginx Installation

```bash
# Update package lists
sudo apt update

# Install Nginx
sudo apt install -y nginx

# Enable and start Nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

## Nginx Configuration File Structure

Nginx uses a modular configuration structure on Ubuntu:

```
/etc/nginx/
├── nginx.conf            # Main configuration file
├── sites-available/      # Available site configurations
│   └── git-dashboard.conf # Our site configuration
└── sites-enabled/        # Enabled site configurations (symlinks)
    └── git-dashboard.conf # Symlink to sites-available
```

## Git Dashboard Nginx Configuration

Create the configuration file:

```bash
sudo nano /etc/nginx/sites-available/git-dashboard.conf
```

Add the following content:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/your-domain.com/chain.pem;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    
    # HSTS (comment out if testing)
    add_header Strict-Transport-Security "max-age=63072000; includeSubdomains; preload";
    
    # Other security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;";
    
    # Logs
    access_log /var/log/nginx/git-dashboard.access.log;
    error_log /var/log/nginx/git-dashboard.error.log;
    
    # Root directory for static files
    root /opt/git-dashboard;
    index index.html;
    
    # Static files
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
    }
    
    # JavaScript and CSS with longer cache
    location ~* \.(js|css)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }
    
    # API Proxy with rate limiting
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Rate limiting
        limit_req zone=api burst=10 nodelay;
        
        # Timeout settings
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # WebSocket support (if needed)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
}
```

## Nginx Rate Limiting Configuration

Add rate limiting to the main Nginx configuration file:

```bash
sudo nano /etc/nginx/nginx.conf
```

Add inside the `http` block:

```nginx
# Define rate limiting zone
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
```

## Enabling the Site Configuration

```bash
# Create a symbolic link to enable the site
sudo ln -sf /etc/nginx/sites-available/git-dashboard.conf /etc/nginx/sites-enabled/

# Remove default configuration if present
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# If test is successful, reload Nginx
sudo systemctl reload nginx
```

## SSL Certificate Setup with Let's Encrypt

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain certificate (interactive)
sudo certbot --nginx -d your-domain.com

# Or non-interactive for automation
sudo certbot --nginx --non-interactive --agree-tos --email <EMAIL> -d your-domain.com
```

## Diffie-Hellman Parameters for Enhanced Security

```bash
# Generate strong DH parameters (this takes time)
sudo openssl dhparam -out /etc/nginx/dhparam.pem 4096
```

Add to your SSL server block:

```nginx
ssl_dhparam /etc/nginx/dhparam.pem;
```

## IP Binding Configuration

To bind Nginx to specific IPs, adjust the `listen` directive in your configuration:

```nginx
# For IPv4 only on specific interface
listen *************:80;
listen *************:443 ssl http2;

# For both IPv4 and IPv6
listen 80;
listen [::]:80;
listen 443 ssl http2;
listen [::]:443 ssl http2;
```

## Custom Error Pages

Add to your server block:

```nginx
error_page 404 /404.html;
error_page 500 502 503 504 /50x.html;
location = /50x.html {
    root /opt/git-dashboard;
}
```

## Nginx Performance Tuning

Add to the `http` block in `nginx.conf`:

```nginx
# Worker configuration
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 2048;
    multi_accept on;
    use epoll;
}

# File descriptors
http {
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # Timeouts
    keepalive_timeout 65;
    keepalive_requests 100;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    
    # Buffer sizes
    client_body_buffer_size 10K;
    client_header_buffer_size 1k;
    client_max_body_size 8m;
    large_client_header_buffers 2 1k;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;
}
```

## Monitoring Nginx

```bash
# Check Nginx status
systemctl status nginx

# Check error logs
tail -f /var/log/nginx/error.log

# Check access logs
tail -f /var/log/nginx/access.log

# Check site-specific logs
tail -f /var/log/nginx/git-dashboard.error.log
```

## Nginx Security Hardening

1. **Hide Nginx version and OS information**

Add to the `http` block in `nginx.conf`:

```nginx
server_tokens off;
```

2. **Additional security headers**

```nginx
add_header Referrer-Policy "no-referrer-when-downgrade";
add_header Feature-Policy "camera 'none'; microphone 'none'; geolocation 'none'";
```

3. **Configure SSL handshake settings**

```nginx
ssl_buffer_size 8k;
ssl_stapling on;
ssl_stapling_verify on;
resolver 8.8.8.8 8.8.4.4 valid=300s;
resolver_timeout 5s;
```

## Troubleshooting Nginx

1. **Testing configuration syntax**
```bash
sudo nginx -t
```

2. **Checking permissions**
```bash
sudo namei -l /opt/git-dashboard/index.html
```

3. **Debugging SSL issues**
```bash
sudo openssl s_client -connect your-domain.com:443 -servername your-domain.com -tls1_2
```

4. **Enabling debug logging**

Modify `error_log` directive in `nginx.conf`:
```nginx
error_log /var/log/nginx/error.log debug;
```

5. **Common Nginx error codes**
   - 403: Permission issues with files or directories
   - 502: Flask backend not running or unreachable
   - 504: Backend request timeout
