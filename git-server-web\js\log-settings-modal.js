/**
 * Log Settings Modal
 * 
 * This module provides a modal dialog for configuring log settings
 * in the Git Dashboard.
 */

class LogSettingsModal {
    constructor() {
        this.modal = null;
        this.logCategories = {
            'system': { name: 'System Logs', description: 'Kernel, boot, and system messages' },
            'security': { name: 'Security Logs', description: 'Authentication, authorization, and audit logs' },
            'web': { name: 'Web Server Logs', description: 'Nginx, Apache, and other web server logs' },
            'database': { name: 'Database Logs', description: 'MySQL, PostgreSQL, and other database logs' },
            'mail': { name: 'Mail Logs', description: 'Mail server and delivery logs' },
            'git': { name: 'Git Logs', description: 'Git repository and server logs' },
            'dashboard': { name: 'Dashboard Logs', description: 'Git Dashboard application logs' },
            'other': { name: 'Other Logs', description: 'Miscellaneous and uncategorized logs' }
        };
        this.discoveredLogs = [];
        this.init();
    }

    /**
     * Initialize the modal
     */
    init() {
        // Create modal if it doesn't exist
        if (!document.getElementById('log-settings-modal')) {
            this.createModal();
        }

        // Add event listeners
        document.getElementById('log-settings-button').addEventListener('click', () => this.openModal());
        document.getElementById('log-settings-close').addEventListener('click', () => this.closeModal());
        document.getElementById('log-settings-save').addEventListener('click', () => this.saveSettings());
        document.getElementById('log-settings-scan').addEventListener('click', () => this.scanForLogs());
    }

    /**
     * Create the modal HTML
     */
    createModal() {
        const modalHtml = `
            <div id="log-settings-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Log Settings</h2>
                        <span id="log-settings-close" class="close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="log-settings-container">
                            <div class="log-settings-sidebar">
                                <div class="log-settings-categories">
                                    <h3>Categories</h3>
                                    <ul id="log-settings-category-list">
                                        <!-- Categories will be populated here -->
                                    </ul>
                                </div>
                            </div>
                            <div class="log-settings-main">
                                <div id="log-settings-logs-container">
                                    <!-- Log files will be populated here -->
                                </div>
                            </div>
                        </div>
                        <div class="log-settings-actions">
                            <button id="log-settings-scan" class="btn btn-secondary">Scan for Logs</button>
                            <button id="log-settings-save" class="btn btn-primary">Save Settings</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to the document
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer.firstElementChild);

        // Add button to the logs tab
        const logsTabHeader = document.querySelector('.logs-header');
        if (logsTabHeader) {
            const settingsButton = document.createElement('button');
            settingsButton.id = 'log-settings-button';
            settingsButton.className = 'btn btn-sm btn-secondary';
            settingsButton.innerHTML = '<i class="fas fa-cog"></i> Settings';
            logsTabHeader.appendChild(settingsButton);
        } else {
            // If logs-header doesn't exist, add to the logs tab header
            const logsTabHeader = document.querySelector('#logs .card-header > div:last-child');
            if (logsTabHeader) {
                const settingsButton = document.createElement('button');
                settingsButton.id = 'log-settings-button';
                settingsButton.className = 'btn btn-sm btn-secondary me-3';
                settingsButton.innerHTML = '<i class="fas fa-cog"></i> Settings';
                logsTabHeader.insertBefore(settingsButton, logsTabHeader.firstChild);
            }
        }

        // Add CSS
        const style = document.createElement('style');
        style.textContent = `
            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
            }
            
            .modal-content {
                background-color: #fefefe;
                margin: 5% auto;
                padding: 0;
                border: 1px solid #888;
                width: 80%;
                max-width: 900px;
                border-radius: 4px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            
            .modal-header {
                padding: 15px;
                border-bottom: 1px solid #e9ecef;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .modal-header h2 {
                margin: 0;
                font-size: 1.25rem;
            }
            
            .close {
                color: #aaa;
                font-size: 28px;
                font-weight: bold;
                cursor: pointer;
            }
            
            .close:hover {
                color: #000;
            }
            
            .modal-body {
                padding: 15px;
                max-height: 70vh;
                overflow-y: auto;
            }
            
            .log-settings-container {
                display: flex;
                gap: 20px;
            }
            
            .log-settings-sidebar {
                flex: 0 0 200px;
                border-right: 1px solid #e9ecef;
                padding-right: 15px;
            }
            
            .log-settings-main {
                flex: 1;
            }
            
            .log-settings-categories ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }
            
            .log-settings-categories li {
                padding: 8px 10px;
                cursor: pointer;
                border-radius: 4px;
            }
            
            .log-settings-categories li:hover {
                background-color: #f8f9fa;
            }
            
            .log-settings-categories li.active {
                background-color: #007bff;
                color: white;
            }
            
            .log-settings-logs-container {
                margin-bottom: 20px;
            }
            
            .log-file-item {
                padding: 10px;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin-bottom: 10px;
            }
            
            .log-file-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .log-file-name {
                font-weight: bold;
            }
            
            .log-file-path {
                color: #6c757d;
                font-size: 0.875rem;
                margin-top: 5px;
            }
            
            .log-file-details {
                margin-top: 10px;
                font-size: 0.875rem;
            }
            
            .log-settings-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 20px;
                border-top: 1px solid #e9ecef;
                padding-top: 15px;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Open the modal and load settings
     */
    openModal() {
        this.modal = document.getElementById('log-settings-modal');
        this.modal.style.display = 'block';
        
        // Load log settings
        this.loadLogSettings();
    }

    /**
     * Close the modal
     */
    closeModal() {
        this.modal.style.display = 'none';
    }

    /**
     * Load log settings from the server
     */
    loadLogSettings() {
        // Show loading indicator
        document.getElementById('log-settings-logs-container').innerHTML = '<div class="loading">Loading log settings...</div>';
        
        // Fetch log settings from the server
        fetch('/api/logs/available')
            .then(response => response.json())
            .then(data => {
                this.discoveredLogs = data.logs || [];
                this.renderCategories();
                this.renderLogFiles('system'); // Default to system logs
            })
            .catch(error => {
                console.error('Error loading log settings:', error);
                document.getElementById('log-settings-logs-container').innerHTML = 
                    '<div class="error">Failed to load log settings. Please try again.</div>';
            });
    }

    /**
     * Render the category list
     */
    renderCategories() {
        const categoryList = document.getElementById('log-settings-category-list');
        categoryList.innerHTML = '';
        
        // Count logs in each category
        const categoryCounts = {};
        this.discoveredLogs.forEach(log => {
            categoryCounts[log.category] = (categoryCounts[log.category] || 0) + 1;
        });
        
        // Create category list items
        Object.keys(this.logCategories).forEach(category => {
            if (categoryCounts[category] > 0 || category === 'other') {
                const li = document.createElement('li');
                li.dataset.category = category;
                li.textContent = `${this.logCategories[category].name} (${categoryCounts[category] || 0})`;
                
                li.addEventListener('click', () => {
                    // Remove active class from all categories
                    document.querySelectorAll('#log-settings-category-list li').forEach(el => {
                        el.classList.remove('active');
                    });
                    
                    // Add active class to clicked category
                    li.classList.add('active');
                    
                    // Render log files for this category
                    this.renderLogFiles(category);
                });
                
                // Set system as active by default
                if (category === 'system') {
                    li.classList.add('active');
                }
                
                categoryList.appendChild(li);
            }
        });
    }

    /**
     * Render log files for a specific category
     */
    renderLogFiles(category) {
        const logsContainer = document.getElementById('log-settings-logs-container');
        logsContainer.innerHTML = '';
        
        // Filter logs by category
        const logs = this.discoveredLogs.filter(log => log.category === category);
        
        if (logs.length === 0) {
            logsContainer.innerHTML = `<div class="no-logs">No ${this.logCategories[category].name.toLowerCase()} found.</div>`;
            return;
        }
        
        // Create log file items
        logs.forEach(log => {
            const logItem = document.createElement('div');
            logItem.className = 'log-file-item';
            logItem.dataset.path = log.path;
            
            const logHeader = document.createElement('div');
            logHeader.className = 'log-file-header';
            
            const logName = document.createElement('div');
            logName.className = 'log-file-name';
            logName.textContent = log.name;
            
            const logToggle = document.createElement('div');
            logToggle.className = 'log-file-toggle';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `log-toggle-${log.path.replace(/[^a-zA-Z0-9]/g, '-')}`;
            checkbox.checked = log.enabled;
            checkbox.addEventListener('change', () => {
                log.enabled = checkbox.checked;
            });
            
            const label = document.createElement('label');
            label.htmlFor = checkbox.id;
            label.textContent = 'Enabled';
            
            logToggle.appendChild(checkbox);
            logToggle.appendChild(label);
            
            logHeader.appendChild(logName);
            logHeader.appendChild(logToggle);
            
            const logPath = document.createElement('div');
            logPath.className = 'log-file-path';
            logPath.textContent = log.path;
            
            const logDetails = document.createElement('div');
            logDetails.className = 'log-file-details';
            
            // Format file size
            let sizeStr = '';
            if (log.size < 1024) {
                sizeStr = `${log.size} B`;
            } else if (log.size < 1024 * 1024) {
                sizeStr = `${(log.size / 1024).toFixed(1)} KB`;
            } else {
                sizeStr = `${(log.size / (1024 * 1024)).toFixed(1)} MB`;
            }
            
            // Format last modified time
            const lastModified = new Date(log.lastModified).toLocaleString();
            
            logDetails.innerHTML = `
                <div><strong>Size:</strong> ${sizeStr}</div>
                <div><strong>Last Modified:</strong> ${lastModified}</div>
                <div><strong>Parser:</strong> ${log.parser}</div>
                ${log.binary ? '<div><strong>Type:</strong> Binary</div>' : '<div><strong>Type:</strong> Text</div>'}
            `;
            
            logItem.appendChild(logHeader);
            logItem.appendChild(logPath);
            logItem.appendChild(logDetails);
            
            logsContainer.appendChild(logItem);
        });
    }

    /**
     * Scan for new log files
     */
    scanForLogs() {
        // Show loading indicator
        document.getElementById('log-settings-logs-container').innerHTML = '<div class="loading">Scanning for log files...</div>';
        
        // Call API to scan for logs
        fetch('/api/logs/scan', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.discoveredLogs = data.logs || [];
                    this.renderCategories();
                    
                    // Get active category
                    const activeCategory = document.querySelector('#log-settings-category-list li.active');
                    this.renderLogFiles(activeCategory ? activeCategory.dataset.category : 'system');
                } else {
                    throw new Error(data.message || 'Failed to scan for logs');
                }
            })
            .catch(error => {
                console.error('Error scanning for logs:', error);
                document.getElementById('log-settings-logs-container').innerHTML = 
                    '<div class="error">Failed to scan for logs: ' + error.message + '</div>';
            });
    }

    /**
     * Save log settings
     */
    saveSettings() {
        // Show loading indicator
        const saveButton = document.getElementById('log-settings-save');
        const originalText = saveButton.textContent;
        saveButton.textContent = 'Saving...';
        saveButton.disabled = true;
        
        // Prepare data to send
        const data = {
            logs: this.discoveredLogs.map(log => ({
                path: log.path,
                enabled: log.enabled
            }))
        };
        
        // Call API to save settings
        fetch('/api/logs/configure', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Log settings saved successfully');
                    this.closeModal();
                    
                    // Reload logs in the main view
                    if (typeof DashboardLogger !== 'undefined' && typeof DashboardLogger.refreshLogs === 'function') {
                        DashboardLogger.refreshLogs();
                    }
                } else {
                    throw new Error(data.message || 'Failed to save settings');
                }
            })
            .catch(error => {
                console.error('Error saving log settings:', error);
                alert('Failed to save log settings: ' + error.message);
            })
            .finally(() => {
                saveButton.textContent = originalText;
                saveButton.disabled = false;
            });
    }
}

// Initialize the log settings modal when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.logSettingsModal = new LogSettingsModal();
});
