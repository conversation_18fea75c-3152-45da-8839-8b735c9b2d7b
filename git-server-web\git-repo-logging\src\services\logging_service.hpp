#pragma once
#include "../models/log_entry.hpp"
#include "log_discovery_service.hpp"
#include "../parsers/parser_manager.hpp"
#include "../storage/storage_manager.hpp"
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <functional>
#include <chrono>
#include <filesystem>

// Forward declaration
class WebSocketSession;

class LoggingService {
public:
    LoggingService(
        const std::string& logSourcesPath,
        const std::string& logLevel,
        std::shared_ptr<logging::ParserManager> parserManager,
        std::shared_ptr<logging::StorageManager> storageManager
    );

    ~LoggingService();

    // API endpoint handlers
    std::string getLogs(const std::string& source, const std::string& level, int maxCount, bool getAll);
    std::string getLogSources();
    std::string getLogStats();
    std::string getAvailableLogs();
    std::string scanForLogs();
    std::string configureLogs(const std::string& configJson);

    // WebSocket subscription handlers
    void subscribeToLogs(std::shared_ptr<WebSocketSession> session, const std::string& source, const std::string& level);
    void unsubscribeFromLogs(std::shared_ptr<WebSocketSession> session);

    // Health check helpers
    std::vector<std::shared_ptr<logging::StorageTier>> getStorageTiers() const;
    std::chrono::steady_clock::time_point getStartTime() const;

private:
    // Log collection and processing
    void startLogCollection();
    void stopLogCollection();
    void collectLogs();

    // Log source handlers
    void collectSystemLogs();
    void collectNginxLogs();
    void collectGitLogs();
    void collectAuthLogs();
    void collectDashboardLogs();

    // Log parsing and processing
    LogEntry parseLogLine(const std::string& line, const std::string& source);
    void processLogEntry(const LogEntry& entry);

    // Log storage and retrieval
    void saveLogsToFile();
    void loadLogsFromFile();
    void pruneOldLogs();

    // Utility functions
    std::string formatTimestamp(const std::chrono::system_clock::time_point& time);
    std::chrono::system_clock::time_point parseTimestamp(const std::string& timestamp);
    std::string getLogTypeString(LogType type);
    LogType parseLogType(const std::string& typeStr);

    // Configuration
    std::string logSourcesPath_;
    std::string logLevel_;
    std::shared_ptr<logging::ParserManager> parserManager_;
    std::shared_ptr<logging::StorageManager> storageManager_;

    // Log storage
    std::vector<LogEntry> logs_;
    std::mutex logsMutex_;

    // Log collection thread
    std::thread collectionThread_;
    std::atomic<bool> running_;
    std::condition_variable collectionCV_;

    // WebSocket subscriptions
    struct Subscription {
        std::string source;
        std::string level;
    };

    std::unordered_map<std::shared_ptr<WebSocketSession>, Subscription> subscriptions_;
    std::mutex subscriptionsMutex_;

    // Log discovery service
    std::shared_ptr<logging::LogDiscoveryService> logDiscoveryService_;

    // Log source paths
    std::string systemLogPath_;
    std::string nginxLogPath_;
    std::string gitLogPath_;
    std::string authLogPath_;
    std::string dashboardLogPath_;

    // Service start time for uptime tracking
    std::chrono::steady_clock::time_point startTime_;

    // Log source last read positions
    std::unordered_map<std::string, std::filesystem::file_time_type> lastReadTimes_;
};
