#!/bin/bash
#
# Project Tracker Server Configuration Script
# Version: 2.0.0
# Last Updated: 2025-03-04
# Author: CHCIT DevOps Team
#
# Dependencies:
#   - Ubuntu 24.04 LTS
#   - Python 3.8+
#   - Nginx 1.24+ with modules:
#     - <PERSON>rotli (libnginx-mod-http-brotli)
#     - Image Filter (libnginx-mod-http-image-filter)
#   - Certbot 2.9.0+ with Cloudflare plugin
#   - PostgreSQL 15+
#
# This script configures the server environment for Project Tracker deployment.
# It handles package installation, service configuration, and security setup.

set -e

# Logging Configuration
DEPLOYMENT_LOG="/var/log/project-tracker/deployment.log"
PROGRESS_LOG="/var/log/project-tracker/progress.log"

# Color codes for logging
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Comprehensive Logging Function
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    
    # Console output
    case "$level" in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "PROGRESS")
            echo -e "${BLUE}[PROGRESS]${NC} $message"
            ;;
    esac
    
    # Log to deployment log file
    echo "[$timestamp] [$level] $message" >> "$DEPLOYMENT_LOG"
}

# Progress Tracking Function
track_progress() {
    local step="$1"
    local status="$2"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] $step: $status" >> "$PROGRESS_LOG"
    log "PROGRESS" "$step: $status"
}

# Error handling function
error_exit() {
    log "ERROR" "$1"
    track_progress "Deployment Failed" "Exiting due to error"
    exit 1
}

# Source common functions
source "$(dirname "$0")/common.sh"

# Function to install system packages
install_packages() {
    log "INFO" "Installing system packages"
    track_progress "Package Installation" "Installing required packages"
    
    # Update package lists
    apt-get update
    
    # Install Python and development tools
    apt-get install -y python3.8 python3.8-dev python3-pip python3-venv build-essential
    
    # Install Nginx and modules
    apt-get install -y nginx libnginx-mod-http-brotli libnginx-mod-http-image-filter
    
    # Install Certbot with Cloudflare plugin
    apt-get install -y certbot python3-certbot-dns-cloudflare
    
    # Fix Cloudflare package regex warning
    CLOUDFLARE_FILE="/usr/lib/python3/dist-packages/CloudFlare/api_decode_from_openapi.py"
    if [ -f "$CLOUDFLARE_FILE" ]; then
        log "INFO" "Patching Cloudflare package regex"
        # Use raw string for regex pattern
        sed -i 's/re.compile(.\\{[A-Za-z0-9_]*\\}.)/re.compile(r"\\{[A-Za-z0-9_]*\\}")/g' "$CLOUDFLARE_FILE"
    fi
    
    # Install PostgreSQL
    apt-get install -y postgresql-15 postgresql-contrib-15
    
    log "INFO" "Package installation completed"
    track_progress "Package Installation" "Packages installed successfully"
}

# Function to configure Nginx
configure_nginx() {
    log "INFO" "Configuring Nginx"
    track_progress "Nginx Setup" "Configuring web server"
    
    # Copy Nginx configuration
    cp /opt/project-tracker/config/nginx.conf /etc/nginx/nginx.conf
    cp /opt/project-tracker/config/nginx-modules.conf /etc/nginx/modules-enabled/
    
    # Test configuration
    nginx -t
    
    # Reload Nginx
    systemctl reload nginx
    
    log "INFO" "Nginx configuration completed"
    track_progress "Nginx Setup" "Web server configured"
}

# Function to setup SSL
setup_ssl() {
    log "INFO" "Setting up SSL certificates"
    track_progress "SSL Setup" "Configuring SSL certificates"
    
    # Run ssl-setup.sh script
    bash "$(dirname "$0")/ssl-setup.sh"
    
    log "INFO" "SSL setup completed"
    track_progress "SSL Setup" "SSL certificates configured"
}

# Function to configure PostgreSQL
setup_database() {
    log "INFO" "Configuring PostgreSQL"
    track_progress "Database Setup" "Setting up database"
    
    # Initialize database if needed
    if ! psql -lqt | cut -d \| -f 1 | grep -qw project_tracker; then
        sudo -u postgres createdb project_tracker
        sudo -u postgres psql -c "CREATE USER project_tracker WITH PASSWORD 'secure_password';"
        sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE project_tracker TO project_tracker;"
    fi
    
    log "INFO" "Database setup completed"
    track_progress "Database Setup" "Database configured"
}

# Main function
main() {
    install_packages
    configure_nginx
    setup_ssl
    setup_database
    
    log "INFO" "Server configuration completed successfully"
    track_progress "Configuration" "Server setup completed"
}

# Run main function
main

# Pre-flight system compatibility check
preflight_check() {
    log "INFO" "Starting pre-flight system compatibility check..."
    track_progress "Preflight Check" "Verifying system requirements"
    
    # Check Ubuntu version
    if ! grep -q "Ubuntu 24.04" /etc/os-release; then
        error_exit "Unsupported Ubuntu version. Requires Ubuntu 24.04 LTS"
    fi
    
    # Check minimum system requirements
    local cpu_cores=$(nproc)
    local total_memory=$(free -g | awk '/^Mem:/{print $2}')
    local disk_space=$(df -h / | awk '/\//{ print $4 }' | sed 's/G//')
    
    log "INFO" "System Resources:"
    log "INFO" "  CPU Cores: $cpu_cores"
    log "INFO" "  Total Memory: ${total_memory}GB"
    log "INFO" "  Available Disk Space: ${disk_space}GB"
    
    if [[ $cpu_cores -lt 2 ]]; then
        error_exit "Insufficient CPU cores. Minimum 2 cores required."
    fi
    
    if [[ $total_memory -lt 4 ]]; then
        error_exit "Insufficient RAM. Minimum 4GB required."
    fi
    
    if [[ $(echo "$disk_space < 20" | bc) -eq 1 ]]; then
        error_exit "Insufficient disk space. Minimum 20GB required."
    fi
    
    track_progress "Preflight Check" "System compatibility verified"
    log "INFO" "System compatibility check passed successfully."
}

# Validate root access
validate_root_access() {
    log "INFO" "Validating root access..."
    track_progress "Root Access Validation" "Checking for root privileges"
    
    if [[ $EUID -ne 0 ]]; then
        error_exit "This script must be run as root. Use sudo."
    fi
    
    track_progress "Root Access Validation" "Root access validated successfully"
    log "INFO" "Root access validated successfully."
}

# Create application directories
create_app_directories() {
    log "INFO" "Creating application directories..."
    track_progress "Directory Setup" "Creating project directories"
    
    mkdir -p /opt/project-tracker
    mkdir -p /opt/project-tracker/venv
    mkdir -p /var/log/project-tracker
    mkdir -p /var/lib/project-tracker/data
    mkdir -p /var/lib/project-tracker/projects
    
    # Set appropriate permissions
    chown -R root:root /opt/project-tracker
    chmod -R 755 /opt/project-tracker
    chmod -R 755 /var/log/project-tracker
    chmod -R 755 /var/lib/project-tracker
    
    track_progress "Directory Setup" "Directories created successfully"
    log "INFO" "Application directories created with correct permissions."
}

# System Update and Preparation
system_update() {
    log "INFO" "Updating system packages..."
    track_progress "System Update" "Updating package lists"
    
    apt-get update || error_exit "Failed to update package lists"
    
    track_progress "System Update" "Upgrading packages"
    apt-get upgrade -y || error_exit "Failed to upgrade packages"
    
    track_progress "System Update" "System packages updated successfully"
    log "INFO" "System packages updated successfully."
}

# Install Core Dependencies
install_dependencies() {
    log "INFO" "Installing core system dependencies..."
    track_progress "Dependency Installation" "Starting dependency installation"
    
    # Check if packages are available
    local required_packages=(
        "libnginx-mod-http-brotli-filter"
        "libnginx-mod-http-brotli-static"
        "libnginx-mod-http-image-filter"
    )
    
    for pkg in "${required_packages[@]}"; do
        if ! apt-cache show "$pkg" >/dev/null 2>&1; then
            error_exit "Package $pkg not found in repositories. Check your sources."
        fi
    done
    
    apt-get install -y \
        build-essential \
        python3.12 \
        python3.12-dev \
        python3.12-venv \
        postgresql \
        postgresql-contrib \
        nginx \
        redis-server \
        git \
        curl \
        wget \
        software-properties-common \
        libnginx-mod-http-brotli-filter \
        libnginx-mod-http-brotli-static \
        libnginx-mod-http-image-filter \
        || error_exit "Failed to install core dependencies"
    
    # Verify modules are installed
    for module in "${required_packages[@]}"; do
        if ! dpkg -l | grep -q "^ii.*$module"; then
            error_exit "Failed to install $module"
        fi
    done
    
    track_progress "Dependency Installation" "Core dependencies installed successfully"
    log "INFO" "Core system dependencies installed successfully."
}

# Configure Nginx with Brotli and Image Filter
configure_nginx() {
    log "INFO" "Configuring Nginx..."
    track_progress "Nginx Configuration" "Setting up Nginx configuration"
    
    # Create modules-enabled directory if it doesn't exist
    mkdir -p /etc/nginx/modules-enabled
    
    # Enable modules
    for module in brotli image-filter; do
        if [ -f "/usr/share/nginx/modules-available/mod-http-$module.conf" ]; then
            ln -sf "/usr/share/nginx/modules-available/mod-http-$module.conf" \
                  "/etc/nginx/modules-enabled/mod-http-$module.conf"
        fi
    done
    
    # Backup original nginx.conf
    cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.bak
    
    # Create Nginx configuration with Brotli and Image Filter
    cat > /etc/nginx/nginx.conf << 'EOL'
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 768;
}

http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Brotli Settings
    brotli on;
    brotli_comp_level 6;
    brotli_types text/plain text/css application/javascript application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    brotli_static on;

    # Image Filter Settings
    image_filter_buffer 10M;
    image_filter_jpeg_quality 85;
    image_filter_webp_quality 85;

    # SSL Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Logging Settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOL
    
    # Test Nginx configuration
    if ! nginx -t; then
        error_exit "Nginx configuration test failed"
    fi
    
    track_progress "Nginx Configuration" "Nginx configured successfully"
    log "INFO" "Nginx configuration completed successfully."
}

# Verify Nginx Modules
verify_nginx_modules() {
    log "INFO" "Verifying Nginx modules..."
    track_progress "Module Verification" "Checking Nginx modules"
    
    # Check if modules are loaded
    if ! nginx -V 2>&1 | grep -q "http_brotli_module"; then
        error_exit "Brotli module not loaded in Nginx"
    fi
    
    if ! nginx -V 2>&1 | grep -q "http_image_filter_module"; then
        error_exit "Image filter module not loaded in Nginx"
    fi
    
    # Test Brotli compression
    local test_file="/var/www/html/test.html"
    echo "<html><body>test</body></html>" > "$test_file"
    
    # Make a request with Brotli encoding
    if ! curl -sI -H "Accept-Encoding: br" "http://localhost/test.html" | grep -qi "content-encoding: br"; then
        error_exit "Brotli compression not working"
    fi
    
    # Clean up test file
    rm -f "$test_file"
    
    track_progress "Module Verification" "Nginx modules verified successfully"
    log "INFO" "Nginx modules verified successfully."
}

# PostgreSQL Configuration
configure_postgresql() {
    log "INFO" "Configuring PostgreSQL..."
    track_progress "PostgreSQL Configuration" "Configuring PostgreSQL database"
    
    # Create project tracker database and user
    sudo -u postgres psql -c "CREATE DATABASE project_tracker;" || true
    sudo -u postgres psql -c "CREATE USER project_tracker WITH PASSWORD 'your_secure_password';" || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE project_tracker TO project_tracker;" || true
    
    track_progress "PostgreSQL Configuration" "PostgreSQL configuration completed successfully"
    log "INFO" "PostgreSQL configuration completed successfully."
}

# Python Virtual Environment Setup
setup_python_environment() {
    log "INFO" "Setting up Python virtual environment..."
    track_progress "Python Environment Setup" "Creating virtual environment"
    
    # Create virtual environment
    python3.12 -m venv /opt/project-tracker/venv || error_exit "Failed to create virtual environment"
    
    # Activate virtual environment and install requirements
    source /opt/project-tracker/venv/bin/activate
    
    # Find and install Python dependencies
    requirements_files=(
        "/opt/project-deploy/requirements.txt"
        "/opt/project-tracker/requirements.txt"
        "/opt/project-deploy/requirements.txt"
        "/opt/project-deploy/requirements.txt"
    )
    
    for file in "${requirements_files[@]}"; do
        if [ -f "$file" ]; then
            log "INFO" "Found requirements file: $file"
            pip install -r "$file" && break
        fi
    done
    
    if [ $? -ne 0 ]; then
        error_exit "Failed to find or install Python dependencies"
    fi
    
    track_progress "Python Environment Setup" "Python environment setup completed successfully"
    log "INFO" "Python virtual environment setup completed successfully."
}

# Redis Configuration
configure_redis() {
    log "INFO" "Configuring Redis..."
    track_progress "Redis Configuration" "Configuring Redis server"
    
    # Enable and start Redis
    systemctl enable redis-server
    systemctl start redis-server
    
    track_progress "Redis Configuration" "Redis configuration completed successfully"
    log "INFO" "Redis configuration completed successfully."
}

# Systemd Service Configuration
configure_systemd_service() {
    log "INFO" "Configuring systemd service..."
    track_progress "Systemd Service Configuration" "Configuring systemd service"
    
    # Copy service file
    cp /opt/project-deploy/project-tracker.service /etc/systemd/system/
    
    # Reload systemd, enable and start service
    systemctl daemon-reload
    systemctl enable project-tracker
    systemctl start project-tracker
    
    track_progress "Systemd Service Configuration" "Systemd service configuration completed successfully"
    log "INFO" "Systemd service configuration completed successfully."
}

# Security Hardening
apply_security_hardening() {
    log "INFO" "Applying security hardening..."
    track_progress "Security Hardening" "Applying security hardening"
    
    # Run security hardening script
    bash /opt/project-deploy/security-hardening.sh
    
    track_progress "Security Hardening" "Security hardening completed successfully"
    log "INFO" "Security hardening completed successfully."
}

# Verify Services
verify_services() {
    log "INFO" "Verifying system services..."
    track_progress "Service Verification" "Verifying system services"
    
    # Check critical services
    services=("postgresql" "nginx" "redis-server" "project-tracker")
    
    for service in "${services[@]}"; do
        if ! systemctl is-active --quiet "$service"; then
            log "ERROR" "Service $service is not running!"
            systemctl status "$service"
        else
            log "INFO" "Service $service is running successfully."
        fi
    done
    
    track_progress "Service Verification" "Service verification completed successfully"
    log "INFO" "Service verification completed successfully."
}

# Configure CDN and Monitoring
configure_cdn() {
    log "INFO" "Configuring CDN and monitoring..."
    track_progress "CDN Setup" "Starting CDN configuration"
    
    # Create CDN directories
    mkdir -p /opt/project-tracker/cdn/cache
    mkdir -p /var/log/project-tracker/cdn
    
    # Set permissions
    chown -R www-data:www-data /opt/project-tracker/cdn
    chmod -R 755 /opt/project-tracker/cdn
    
    # Install CDN monitoring script
    cp /opt/project-deploy/scripts/monitor-cdn.sh /opt/project-tracker/scripts/
    chmod +x /opt/project-tracker/scripts/monitor-cdn.sh
    
    # Set up CDN monitoring cron job
    echo "*/5 * * * * /opt/project-tracker/scripts/monitor-cdn.sh" > /etc/cron.d/project-tracker-cdn
    chmod 644 /etc/cron.d/project-tracker-cdn
    
    # Configure logrotate for CDN logs
    cat > /etc/logrotate.d/project-tracker-cdn << 'EOF'
/var/log/project-tracker/cdn/*.log {
    daily
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data adm
    sharedscripts
    postrotate
        if /etc/init.d/nginx status > /dev/null; then
            /etc/init.d/nginx reload > /dev/null
        fi
    endscript
}
EOF
    
    track_progress "CDN Setup" "CDN configuration completed successfully"
    log "INFO" "CDN configuration completed successfully."
}

# Create Admin User Function
create_admin_user() {
    log "INFO" "Creating admin user..."
    track_progress "User Management" "Creating admin user"
    
    # Activate virtual environment
    source /opt/project-tracker/venv/bin/activate
    
    # Python script to create admin user with full permissions
    python3 << EOF
import sys
import os
import django
import bcrypt

# Add the project directory to Python path
sys.path.append('/opt/project-tracker/src')

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_tracker.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType

# Hash the password securely
hashed_password = bcrypt.hashpw('G8k33p3r!'.encode('utf-8'), bcrypt.gensalt())

# Create admin user
admin_user = User.objects.create_user(
    username='admin', 
    password='G8k33p3r!',
    email='<EMAIL>',
    is_staff=True,
    is_superuser=True
)

# Get all permissions
all_permissions = Permission.objects.all()

# Add all permissions to the admin user
admin_user.user_permissions.set(all_permissions)

# Save the user
admin_user.save()

print("Admin user created successfully with full permissions")
EOF
    
    # Deactivate virtual environment
    deactivate
    
    track_progress "User Management" "Admin user created successfully"
    log "INFO" "Admin user created successfully."
}

# Cloudflare Token Setup Function
setup_cloudflare_token() {
    log "INFO" "Setting up Cloudflare DNS validation token"
    
    # Ensure secure directory exists
    mkdir -p /etc/letsencrypt/cloudflare
    
    # Create token file with strict permissions
    TOKEN_FILE="/etc/letsencrypt/cloudflare/cf-token.secret"
    echo "****************************************" > "$TOKEN_FILE"
    chmod 600 "$TOKEN_FILE"
    chown root:root "$TOKEN_FILE"
    
    log "INFO" "Cloudflare token stored securely"
}

# Cleanup temporary files
cleanup() {
    log "INFO" "Cleaning up temporary files..."
    track_progress "Cleanup" "Cleaning up temporary files"
    
    rm -rf /opt/project-deploy
    
    track_progress "Cleanup" "Cleanup completed successfully"
    log "INFO" "Cleanup completed successfully."
}

# Main Deployment Function
main() {
    # Create log directories if they don't exist
    mkdir -p "$(dirname "$DEPLOYMENT_LOG")"
    mkdir -p "$(dirname "$PROGRESS_LOG")"
    
    # Start deployment
    log "INFO" "Starting Project Tracker Deployment..."
    track_progress "Deployment Start" "Initializing deployment process"
    
    preflight_check
    validate_root_access
    system_update
    create_app_directories
    install_dependencies
    configure_nginx
    verify_nginx_modules
    configure_postgresql
    setup_python_environment
    configure_redis
    configure_systemd_service
    apply_security_hardening
    verify_services
    configure_cdn
    verify_cdn
    create_admin_user
    setup_cloudflare_token
    cleanup
    
    log "INFO" "Deployment completed successfully"
    track_progress "Deployment" "All steps completed successfully"
}

# Execute main function
main
