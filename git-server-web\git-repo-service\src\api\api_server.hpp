#pragma once
#include <boost/asio.hpp>
#include <boost/beast.hpp>
#include <functional>
#include <string>
#include <unordered_map>
#include <thread>
#include <memory>
#include <atomic>
#include "../services/repository_service.hpp"

namespace beast = boost::beast;
namespace http = beast::http;
namespace asio = boost::asio;
using tcp = asio::ip::tcp;

class ApiServer {
public:
    ApiServer(unsigned short port, std::shared_ptr<RepositoryService> service);
    ~ApiServer();
    
    void start();
    void stop();
    
    // Register endpoint handlers
    void registerHandler(const std::string& path, 
                       std::function<http::response<http::string_body>(const http::request<http::string_body>&)> handler);
    
private:
    unsigned short port_;
    asio::io_context ioc_;
    std::shared_ptr<tcp::acceptor> acceptor_;
    std::shared_ptr<RepositoryService> repoService_;
    std::unordered_map<std::string, 
                     std::function<http::response<http::string_body>(const http::request<http::string_body>&)>> handlers_;
    std::vector<std::thread> threads_;
    std::atomic<bool> running_;
    
    void doAccept();
    void handleSession(tcp::socket socket);
    void setupEndpoints();
    
    // Default handlers
    http::response<http::string_body> handleNotFound(const http::request<http::string_body>& req);
    http::response<http::string_body> handleRepositories(const http::request<http::string_body>& req);
    http::response<http::string_body> handleCommitHistory(const http::request<http::string_body>& req);
};
