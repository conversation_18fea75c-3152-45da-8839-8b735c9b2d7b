from flask_socketio import <PERSON><PERSON><PERSON>, emit, disconnect
from functools import wraps
import jwt
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from flask import request

logger = logging.getLogger(__name__)

def require_token(f):
    """Decorator to enforce token-based WebSocket authentication."""
    @wraps(f)
    def wrapped(*args, **kwargs):
        token = kwargs.get('token')
        if not token:
            logger.error("No authentication token provided")
            disconnect()
            return False
        try:
            # Verify token with your secret key
            decoded = jwt.decode(token, 'your-secret-key', algorithms=['HS256'])
            kwargs['user_data'] = decoded
            return f(*args, **kwargs)
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token: {str(e)}")
            disconnect()
            return False
    return wrapped

class WebSocketService:
    """WebSocket service implementing token-based authentication and monitoring."""
    
    def __init__(self):
        self.socketio = SocketIO()
        self.connections: Dict[str, Any] = {}
        self.metrics = {
            'total_connections': 0,
            'active_connections': 0,
            'messages_sent': 0,
            'errors': 0
        }
        self.start_time = datetime.now()
        
    def init_app(self, app):
        """Initialize WebSocket with Flask app."""
        self.app = app
        self.socketio.init_app(
            app,
            cors_allowed_origins="*",
            async_mode='gevent',
            ping_timeout=10,
            ping_interval=5
        )
        self._register_handlers()
        
    def _register_handlers(self):
        """Register WebSocket event handlers."""
        
        @self.socketio.on('connect')
        @require_token
        def handle_connect(token=None, user_data=None):
            """Handle new WebSocket connections."""
            if not user_data:
                return False
                
            client_id = user_data.get('sub')
            self.connections[client_id] = {
                'user_data': user_data,
                'connection_time': datetime.now(),
                'messages_sent': 0
            }
            self.metrics['total_connections'] += 1
            self.metrics['active_connections'] += 1
            logger.info(f"Client connected: {client_id}")
            
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle WebSocket disconnections."""
            client_id = request.sid
            if client_id in self.connections:
                del self.connections[client_id]
                self.metrics['active_connections'] -= 1
            logger.info(f"Client disconnected: {client_id}")
            
        @self.socketio.on_error()
        def handle_error(e):
            """Handle WebSocket errors."""
            self.metrics['errors'] += 1
            logger.error(f"WebSocket error: {str(e)}", exc_info=True)
            
    def get_metrics(self) -> Dict[str, Any]:
        """Get current WebSocket metrics."""
        return {
            **self.metrics,
            'connections': len(self.connections),
            'uptime': (datetime.now() - self.start_time).total_seconds()
        }
        
    def broadcast(self, event: str, data: Dict[str, Any], room: Optional[str] = None):
        """Broadcast message to connected clients."""
        try:
            self.socketio.emit(event, data, room=room)
            self.metrics['messages_sent'] += 1
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"Broadcast error: {str(e)}", exc_info=True)
            raise
