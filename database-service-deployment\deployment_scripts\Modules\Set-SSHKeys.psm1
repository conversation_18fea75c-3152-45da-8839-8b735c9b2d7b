# Set SSH Keys Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Set-SSHKeys {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "                     Set SSH Keys                      " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    $sshKeyPath = Read-Host "Enter path to SSH private key (default: $HOME/.ssh/id_rsa)"
    if ([string]::IsNullOrWhiteSpace($sshKeyPath)) {
        $sshKeyPath = "$HOME/.ssh/id_rsa"
    }
    
    if (-not (Test-Path -Path $sshKeyPath)) {
        Write-Log -Message "SSH key not found at: $sshKeyPath" -Level "UI" -ForegroundColor Red
        if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
            Wait-ForUser
        } else {
            Write-Host "Press Enter to continue..." -ForegroundColor Yellow
            Read-Host | Out-Null
        }
        if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
            Show-MainMenu
        }
        return
    }
    
    if ($null -eq $script:Config -or $null -eq $script:Config.ssh) {
        # Create SSH configuration if it doesn't exist
        if ($null -eq $script:Config) {
            $script:Config = @{}
        }
        if ($null -eq $script:Config.ssh) {
            $script:Config.ssh = @{}
        }
    }
    
    # Update the configuration with the SSH key path
    $script:Config.ssh.local_key_path = $sshKeyPath
    
    # Also set a default SSH configuration if needed
    if ([string]::IsNullOrWhiteSpace($script:Config.ssh.host)) {
        $script:Config.ssh.host = "git.chcit.org"
    }
    if ([string]::IsNullOrWhiteSpace($script:Config.ssh.username)) {
        $script:Config.ssh.username = "btaylor-admin"
    }
    if ($null -eq $script:Config.ssh.port) {
        $script:Config.ssh.port = 22
    }
    
    # Save the configuration
    if (Get-Command -Name Save-Configuration -ErrorAction SilentlyContinue) {
        $saved = Save-Configuration
        if ($saved) {
            Write-Log -Message "SSH key path updated: $sshKeyPath" -Level "UI" -ForegroundColor Green
        } else {
            Write-Log -Message "Failed to save configuration." -Level "UI" -ForegroundColor Red
        }
    } else {
        Write-Log -Message "Save-Configuration function not found. Changes will not be saved." -Level "UI" -ForegroundColor Red
    }
    
    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }
    
    if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
        Wait-ForUser
    } else {
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null
    }
    
    if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
        Show-MainMenu
    }
}

# Export the function
Export-ModuleMember -Function Set-SSHKeys
