#!/bin/bash

# Script to diagnose and fix git permissions for the Project Tracker Dashboard
# This script should be run with sudo privileges

echo "Git Permissions Diagnostic and Fix Script"
echo "============================================"
echo ""

# Check if running as root
if [ "$(id -u)" -ne 0 ]; then
    echo "Error: This script must be run with sudo privileges."
    echo "Please run: sudo ./fix-git-permissions.sh"
    exit 1
fi

# Get the web server user
WEB_USER="www-data"
echo "Using web server user: $WEB_USER"

# Check if git user exists
if id "git" &>/dev/null; then
    echo "✓ Git user exists"
    GIT_USER="git"
    GIT_HOME=$(eval echo ~$GIT_USER)
    echo "  Git user home directory: $GIT_HOME"
else
    echo "✗ Git user does not exist. Creating git user..."
    useradd -m -s /bin/bash git
    GIT_USER="git"
    GIT_HOME=$(eval echo ~$GIT_USER)
    echo "  Created git user with home directory: $GIT_HOME"
fi

# Check repository path
REPO_PATH="/home/<USER>/repositories"
if [ -d "$REPO_PATH" ]; then
    echo "✓ Repository path exists: $REPO_PATH"
    echo "  Checking permissions..."
    
    # Check directory permissions
    REPO_PERMS=$(stat -c "%a" "$REPO_PATH")
    echo "  Current permissions: $REPO_PERMS"
    
    # Ensure git user owns the directory
    chown $GIT_USER:$GIT_USER "$REPO_PATH"
    
    # Ensure the directory has proper permissions
    chmod 755 "$REPO_PATH"
    
    echo "  Updated permissions: $(stat -c "%a" "$REPO_PATH")"
    echo "  Updated owner: $(stat -c "%U:%G" "$REPO_PATH")"
else
    echo "✗ Repository path does not exist: $REPO_PATH"
    echo "  Creating repository directory..."
    mkdir -p "$REPO_PATH"
    chown $GIT_USER:$GIT_USER "$REPO_PATH"
    chmod 755 "$REPO_PATH"
    echo "  Created repository directory with proper permissions"
fi

# Check if any repositories exist
REPO_COUNT=$(find "$REPO_PATH" -maxdepth 1 -type d -name "*.git" | wc -l)
echo "Found $REPO_COUNT git repositories in $REPO_PATH"

# Check sudoers configuration
echo "\nChecking sudoers configuration..."
SUDOERS_FILE="/etc/sudoers.d/git-dashboard"

if [ -f "$SUDOERS_FILE" ]; then
    echo "✓ Sudoers file exists: $SUDOERS_FILE"
    echo "  Current content:"
    cat "$SUDOERS_FILE"
else
    echo "✗ Sudoers file does not exist: $SUDOERS_FILE"
    echo "  Creating sudoers file..."
    
    # Create the sudoers file
    echo "$WEB_USER ALL=(git) NOPASSWD: /usr/bin/git" > "$SUDOERS_FILE"
    chmod 440 "$SUDOERS_FILE"
    
    echo "  Created sudoers file with content:"
    cat "$SUDOERS_FILE"
fi

# Check if the sudoers file has the correct content
if ! grep -q "$WEB_USER ALL=(git) NOPASSWD: /usr/bin/git" "$SUDOERS_FILE"; then
    echo "  Updating sudoers file with correct permissions..."
    echo "$WEB_USER ALL=(git) NOPASSWD: /usr/bin/git" > "$SUDOERS_FILE"
    chmod 440 "$SUDOERS_FILE"
    echo "  Updated sudoers file with content:"
    cat "$SUDOERS_FILE"
fi

# Test sudo permissions
echo "\nTesting sudo permissions..."
echo "  Running: sudo -u $WEB_USER sudo -u $GIT_USER whoami"
if sudo -u $WEB_USER sudo -u $GIT_USER whoami &>/dev/null; then
    echo "✓ Web user can run commands as git user"
else
    echo "✗ Web user cannot run commands as git user"
    echo "  This may require manual investigation"
fi

# Test git command
echo "\nTesting git command..."
echo "  Running: sudo -u $WEB_USER sudo -u $GIT_USER git --version"
if sudo -u $WEB_USER sudo -u $GIT_USER git --version &>/dev/null; then
    echo "✓ Web user can run git commands as git user"
else
    echo "✗ Web user cannot run git commands as git user"
    echo "  This may require manual investigation"
fi

# Check if any repositories can be accessed
if [ "$REPO_COUNT" -gt 0 ]; then
    echo "\nTesting repository access..."
    TEST_REPO=$(find "$REPO_PATH" -maxdepth 1 -type d -name "*.git" | head -1)
    
    if [ -n "$TEST_REPO" ]; then
        echo "  Testing access to: $TEST_REPO"
        
        # Test if git user can access the repository
        echo "  Running: sudo -u $GIT_USER git -C $TEST_REPO log -n 1 --format=%s"
        if sudo -u $GIT_USER git -C "$TEST_REPO" log -n 1 --format=%s &>/dev/null; then
            echo "✓ Git user can access the repository"
        else
            echo "✗ Git user cannot access the repository"
            echo "  Fixing repository permissions..."
            
            # Fix repository permissions
            chown -R $GIT_USER:$GIT_USER "$TEST_REPO"
            chmod -R 755 "$TEST_REPO"
            
            echo "  Retesting repository access..."
            if sudo -u $GIT_USER git -C "$TEST_REPO" log -n 1 --format=%s &>/dev/null; then
                echo "✓ Repository access fixed"
            else
                echo "✗ Repository access still failing"
                echo "  This may require manual investigation"
            fi
        fi
        
        # Test if web user can access the repository via git user
        echo "  Running: sudo -u $WEB_USER sudo -u $GIT_USER git -C $TEST_REPO log -n 1 --format=%s"
        if sudo -u $WEB_USER sudo -u $GIT_USER git -C "$TEST_REPO" log -n 1 --format=%s &>/dev/null; then
            echo "✓ Web user can access the repository via git user"
        else
            echo "✗ Web user cannot access the repository via git user"
            echo "  This may require manual investigation"
        fi
    fi
fi

# Check for and fix Git's dubious ownership issue
echo "\n[5] Checking for Git dubious ownership issues..."
for repo in $(find "$REPO_PATH" -type d -name "*.git"); do
    echo "  - Checking repository: $repo"
    
    # Try to run git command as www-data to see if there's a dubious ownership issue
    dubious_check=$(sudo -u www-data git -C "$repo" log -n 1 2>&1 | grep "dubious ownership" || true)
    
    if [ -n "$dubious_check" ]; then
        echo "    - Detected dubious ownership issue for $repo"
        
        # Add the repository to www-data's safe.directory configuration
        echo "    - Adding repository to www-data's safe.directory configuration"
        sudo -u www-data git config --global --add safe.directory "$repo"
        
        # Verify the fix
        after_fix=$(sudo -u www-data git -C "$repo" log -n 1 2>&1 | grep "dubious ownership" || true)
        if [ -n "$after_fix" ]; then
            echo "    - ❌ Failed to fix dubious ownership issue for $repo"
        else
            echo "    - ✅ Successfully fixed dubious ownership issue for $repo"
        fi
    else
        echo "    - ✅ No dubious ownership issues detected for $repo"
    fi
done

echo "\nDiagnostic and fix script completed"
echo "Please check the output above for any issues that still need to be resolved"
echo "You may need to restart your web server for changes to take effect"
echo "  sudo systemctl restart apache2  # For Apache"
echo "  sudo systemctl restart nginx    # For Nginx"
echo "  sudo systemctl restart git-server-dashboard  # For the dashboard service"
