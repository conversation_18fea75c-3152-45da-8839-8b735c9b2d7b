# Verify Git Server Integration Script
# This script verifies that the Git server integration is working correctly

# Configuration
$gitUser = "git"
$gitServer = "***********"
$repoPath = "D:\Codeium\CHCIT\project-tracker"

# Set working directory
Set-Location $repoPath

# Output with colors for better readability
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Color,
        
        [Parameter(Mandatory=$true, ValueFromPipeline=$true)]
        [string]$Message
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $Color
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Step 1: Verify SSH connection
Write-ColorOutput Green "Step 1: Verifying SSH connection to Git server..."
try {
    $sshResult = ssh -o BatchMode=yes "$gitUser@$gitServer" "echo CONNECTION_OK"
    if ($sshResult -eq "CONNECTION_OK") {
        Write-ColorOutput Green "✓ SSH connection successful!"
    } else {
        Write-ColorOutput Red "✗ SSH connection test failed. Unexpected response: $sshResult"
        exit 1
    }
} catch {
    Write-ColorOutput Red "✗ SSH connection failed: $_"
    Write-ColorOutput Yellow "Tip: Make sure your SSH key is properly set up and added to the git user's authorized_keys on the server."
    exit 1
}

# Step 2: Verify Git remote configuration
Write-ColorOutput Green "\nStep 2: Verifying Git remote configuration..."
try {
    $remoteUrl = git remote get-url origin
    if ($remoteUrl -like "*$gitUser@$gitServer*") {
        Write-ColorOutput Green "✓ Git remote correctly configured to: $remoteUrl"
    } else {
        Write-ColorOutput Yellow "! Git remote is not configured to use the Git server."
        Write-ColorOutput Yellow "Current remote URL: $remoteUrl"
        Write-ColorOutput Yellow "Expected format: $gitUser@$gitServer:/home/<USER>/repository.git"
    }
} catch {
    Write-ColorOutput Red "✗ Failed to get Git remote URL: $_"
    exit 1
}

# Step 3: Create and push a test commit
Write-ColorOutput Green "\nStep 3: Creating and pushing a test commit..."

# Create a test file with timestamp
$timestamp = Get-Date -Format "yyyy-MM-dd-HH-mm-ss"
$testFileName = ".git-server-test-$timestamp.txt"
$testFilePath = Join-Path -Path $repoPath -ChildPath $testFileName

try {
    # Create test file
    "This is a test file to verify Git server integration. Created at $timestamp" | Out-File -FilePath $testFilePath
    
    # Add and commit the file
    git add $testFileName
    git commit -m "Test commit to verify Git server integration at $timestamp"
    
    # Push to remote
    Write-ColorOutput Cyan "Pushing test commit to Git server..."
    git push
    
    Write-ColorOutput Green "✓ Test commit successfully pushed to Git server!"
    
    # Clean up - remove the test file
    git rm $testFileName --quiet
    git commit -m "Remove test file after Git server verification"
    git push
    
    Write-ColorOutput Green "✓ Test file removed successfully."
} catch {
    Write-ColorOutput Red "✗ Failed to create or push test commit: $_"
    exit 1
}

# Step 4: Verify commit on server
Write-ColorOutput Green "\nStep 4: Verifying commit on Git server..."
try {
    $verifyCommand = "cd /home/<USER>/project-tracker.git && git log -1 --grep='Test commit to verify Git server integration'"
    $verifyResult = ssh "$gitUser@$gitServer" $verifyCommand
    
    if ($verifyResult -like "*Test commit to verify Git server integration*") {
        Write-ColorOutput Green "✓ Test commit verified on Git server!"
    } else {
        Write-ColorOutput Yellow "! Could not verify test commit on server. This might be due to timing or repository structure."
    }
} catch {
    Write-ColorOutput Red "✗ Failed to verify commit on server: $_"
}

# Final summary
Write-ColorOutput Green "\n=============================================="
Write-ColorOutput Green "Git Server Integration Verification Complete!"
Write-ColorOutput Green "=============================================="
Write-ColorOutput Cyan "The Git server integration is working correctly."
Write-ColorOutput Cyan "You can now use the Git server for version control with Windsurf."
