# Deployment package script for Git Server Dashboard

# Configuration
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"
$LocalProjectPath = "D:/Codeium/CHCIT/project-tracker/git-server-web"
$DeployPackagePath = "$LocalProjectPath/git-component-deploy.zip"

# Create timestamp for backup
$BackupFolder = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupPath = "/opt/git-dashboard-backups/$BackupFolder"

Write-Host "Preparing deployment package..." -ForegroundColor Yellow

# Create a temporary directory for files to package
$tempDir = "$LocalProjectPath/temp-deploy"
if (Test-Path $tempDir) {
    Remove-Item -Path $tempDir -Recurse -Force
}
New-Item -Path $tempDir -ItemType Directory | Out-Null
New-Item -Path "$tempDir/js" -ItemType Directory | Out-Null
New-Item -Path "$tempDir/css" -ItemType Directory | Out-Null

# Copy all relevant files to the temp directory
Copy-Item -Path "$LocalProjectPath/index.html" -Destination "$tempDir/index.html"
Copy-Item -Path "$LocalProjectPath/js/git-utils.js" -Destination "$tempDir/js/git-utils.js"
Copy-Item -Path "$LocalProjectPath/js/git-commit-history-chart.js" -Destination "$tempDir/js/git-commit-history-chart.js"
Copy-Item -Path "$LocalProjectPath/js/git-size-comparison-chart.js" -Destination "$tempDir/js/git-size-comparison-chart.js"
Copy-Item -Path "$LocalProjectPath/js/git-repository-list.js" -Destination "$tempDir/js/git-repository-list.js"
Copy-Item -Path "$LocalProjectPath/js/git-repository-modal.js" -Destination "$tempDir/js/git-repository-modal.js"
Copy-Item -Path "$LocalProjectPath/js/git-repository-manager.js" -Destination "$tempDir/js/git-repository-manager.js"
Copy-Item -Path "$LocalProjectPath/css/repositories.css" -Destination "$tempDir/css/repositories.css"

# Create deployment script to run on server
$deployScriptContent = @"
#!/bin/bash
# Server-side deployment script
echo "Creating backup..."
sudo mkdir -p $BackupPath
sudo cp -r $RemotePath/* $BackupPath/

echo "Backing up original git-repositories.js"
sudo cp $RemotePath/js/git-repositories.js $RemotePath/js/git-repositories.js.bak

echo "Deploying files..."
sudo cp -f /tmp/git-component-deploy/index.html $RemotePath/
sudo cp -f /tmp/git-component-deploy/js/*.js $RemotePath/js/
sudo cp -f /tmp/git-component-deploy/css/*.css $RemotePath/css/

echo "Setting permissions..."
sudo chown -R www-data:www-data $RemotePath

echo "Restarting service..."
sudo systemctl restart git-dashboard.service

echo "Cleaning up..."
sudo rm -rf /tmp/git-component-deploy

echo "Deployment completed successfully!"
"@

Set-Content -Path "$tempDir/deploy.sh" -Value $deployScriptContent

# Create the zip file
Write-Host "Creating deployment package..." -ForegroundColor Yellow
if (Test-Path $DeployPackagePath) {
    Remove-Item -Path $DeployPackagePath -Force
}
Compress-Archive -Path "$tempDir/*" -DestinationPath $DeployPackagePath

# Upload and execute the deployment
Write-Host "Uploading deployment package..." -ForegroundColor Yellow
scp "$DeployPackagePath" "${RemoteUser}@${RemoteHost}:/tmp/git-component-deploy.zip"

Write-Host "Extracting and deploying..." -ForegroundColor Yellow
ssh "${RemoteUser}@${RemoteHost}" "mkdir -p /tmp/git-component-deploy && unzip -o /tmp/git-component-deploy.zip -d /tmp/git-component-deploy && chmod +x /tmp/git-component-deploy/deploy.sh && /tmp/git-component-deploy/deploy.sh"

# Clean up local temp directory
Write-Host "Cleaning up local files..." -ForegroundColor Yellow
Remove-Item -Path $tempDir -Recurse -Force

Write-Host "Deployment completed!" -ForegroundColor Green
Write-Host "You can now access the dashboard at http://git.chcit.org/" -ForegroundColor Cyan
