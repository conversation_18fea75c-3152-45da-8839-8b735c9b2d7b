#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include <format> // C++20 feature
#include <chrono>
#include <thread>
#include <filesystem>

namespace dbservice::core {

// Constructor with detailed SSL configuration
ConnectionManager::ConnectionManager(const std::string& connectionString, size_t maxConnections, const SSLConfig& sslConfig)
    : connectionString_(connectionString),
      maxConnections_(maxConnections),
      sslConfig_(sslConfig),
      shutdown_(false),
      activeConnections_(0),
      waitingConnections_(0),
      lastMetricsUpdate_(std::chrono::steady_clock::now()) {

    utils::Logger::info(std::format("Initializing connection manager with {} max connections, SSL {}",
        maxConnections, sslConfig_.enabled ? "enabled" : "disabled"));

    if (sslConfig_.enabled) {
        // Validate SSL configuration
        if (sslConfig_.mode != SSLMode::Disable) {
            // Check certificate paths if using verification modes
            if (sslConfig_.mode == SSLMode::VerifyCa || sslConfig_.mode == SSLMode::VerifyFull) {
                if (sslConfig_.caPath.empty()) {
                    utils::Logger::warning("SSL CA certificate path is empty but verification is enabled");
                } else if (!std::filesystem::exists(sslConfig_.caPath)) {
                    utils::Logger::warning(std::format("SSL CA certificate file not found: {}", sslConfig_.caPath));
                }
            }

            // Check client certificate paths if provided
            if (!sslConfig_.certPath.empty() && !std::filesystem::exists(sslConfig_.certPath)) {
                utils::Logger::warning(std::format("SSL client certificate file not found: {}", sslConfig_.certPath));
            }

            if (!sslConfig_.keyPath.empty() && !std::filesystem::exists(sslConfig_.keyPath)) {
                utils::Logger::warning(std::format("SSL client key file not found: {}", sslConfig_.keyPath));
            }
        }
    }

    // Pre-create some connections
    size_t initialConnections = std::min(maxConnections_ / 2, static_cast<size_t>(2));
    for (size_t i = 0; i < initialConnections; ++i) {
        auto expected_connection = createConnection();
        if (expected_connection) {
            if (*expected_connection && (*expected_connection)->isOpen()) {
                connections_.push_back(*expected_connection);
            } else if (*expected_connection) {
                 utils::Logger::warning(std::format("Initial connection created but failed to open (ID: {}). Not adding to pool.", (*expected_connection)->getId()));
            } else { // Should not happen if createConnection populates error correctly
                 utils::Logger::error(std::format("Initial connection creation failed to produce a connection object: {}", expected_connection.error()));
            }
        } else {
            utils::Logger::error(std::format("Initial connection creation failed: {}", expected_connection.error()));
        }
    }

    utils::Logger::info(std::format("Connection manager initialized with {} initial connections", connections_.size()));
}

// Constructor with simplified SSL configuration
ConnectionManager::ConnectionManager(const std::string& connectionString, size_t maxConnections, bool useSSL)
    : connectionString_(connectionString),
      maxConnections_(maxConnections),
      shutdown_(false),
      activeConnections_(0),
      waitingConnections_(0),
      lastMetricsUpdate_(std::chrono::steady_clock::now()) {

    // Create default SSL configuration
    sslConfig_.enabled = useSSL;
    sslConfig_.mode = SSLMode::VerifyFull;

    if (useSSL) {
        // Default paths for SSL certificates
        sslConfig_.certPath = "/etc/letsencrypt/live/chcit.org/fullchain.pem";
        sslConfig_.keyPath = "/etc/letsencrypt/live/chcit.org/privkey.pem";
        sslConfig_.caPath = "/etc/letsencrypt/live/chcit.org/chain.pem";

        utils::Logger::info("Using default SSL certificate paths");
    }

    // Pre-create some connections
    size_t initialConnections = std::min(maxConnections_ / 2, static_cast<size_t>(2));
    for (size_t i = 0; i < initialConnections; ++i) {
        auto expected_connection = createConnection();
        if (expected_connection) {
            if (*expected_connection && (*expected_connection)->isOpen()) {
                connections_.push_back(*expected_connection);
            } else if (*expected_connection) {
                 utils::Logger::warning(std::format("Initial connection created but failed to open (ID: {}). Not adding to pool.", (*expected_connection)->getId()));
            } else { // Should not happen if createConnection populates error correctly
                 utils::Logger::error(std::format("Initial connection creation failed to produce a connection object: {}", expected_connection.error()));
            }
        } else {
            utils::Logger::error(std::format("Initial connection creation failed: {}", expected_connection.error()));
        }
    }
}

ConnectionManager::~ConnectionManager() {
    shutdown();
}

std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::getConnection() {
    std::unique_lock<std::mutex> lock(mutex_);
    waitingConnections_++;
    // utils::Logger::debug(std::format("getConnection: Wait started. Pool: {}, Active: {}, Max: {}, Waiting: {}", connections_.size(), activeConnections_, maxConnections_, waitingConnections_.load()));

    // Wait until a connection is available, or we can create one, or shutdown
    if (!cv_.wait_for(lock, connectionTimeout_, [this] {
            return !connections_.empty() || (activeConnections_ < maxConnections_) || shutdown_;
        })) {
        // Timeout occurred
        waitingConnections_--;
        std::string errorMsg = std::format("Connection pool timeout after {}ms. Pool: {}, Active: {}, Max: {}", connectionTimeout_.count(), connections_.size(), activeConnections_, maxConnections_);
        utils::Logger::warning(errorMsg);
        // updateMetrics(); // Consider if a timeout should affect availability metrics
        return std::unexpected(errorMsg);
    }

    waitingConnections_--;
    // utils::Logger::debug(std::format("getConnection: Wait finished. Pool: {}, Active: {}, Max: {}, Waiting: {}", connections_.size(), activeConnections_, maxConnections_, waitingConnections_.load()));

    if (shutdown_) {
        std::string errorMsg = "Connection manager is shutting down, no new connections can be provided.";
        utils::Logger::info(errorMsg);
        return std::unexpected(errorMsg);
    }

    std::shared_ptr<Connection> connection_ptr;

    if (!connections_.empty()) {
        connection_ptr = connections_.front(); // Use front for FIFO
        connections_.pop_front();
        // utils::Logger::debug(std::format("Reusing connection. Pool size: {}, Active: {}", connections_.size(), activeConnections_ + 1));
    } else if (activeConnections_ < maxConnections_) {
        // Unlock while creating a new connection to prevent deadlock if createConnection() also logs/interacts
        // utils::Logger::debug("Pool empty, creating new connection.");
        lock.unlock();
        auto new_connection_expected = createConnection();
        lock.lock(); // Re-acquire lock

        if (shutdown_) { // Check shutdown again after re-acquiring lock and potentially long createConnection()
            if (new_connection_expected && *new_connection_expected) { // If connection was created and is valid
                 utils::Logger::info("Shutting down: returning newly created connection to pool (or closing if failed to open).");
                 // Attempt to return it properly, which might just close it if it didn't open.
                 // This relies on returnConnection being safe with possibly unopened connections.
                 // Alternatively, just ensure it's closed if it was indeed created.
                if ((*new_connection_expected)->isOpen()) {
                    returnConnection(*new_connection_expected);
                } else {
                    // It was created but not opened, just let it be destroyed.
                }
            } else if (new_connection_expected) {
                 // new_connection_expected has value, but *new_connection_expected is nullptr (should not happen with make_shared)
            } else {
                // new_connection_expected has error - connection was not created.
            }
            std::string errorMsg = "Connection manager shutting down during new connection creation.";
            utils::Logger::info(errorMsg);
            return std::unexpected(errorMsg);
        }

        if (!new_connection_expected) {
            std::string errorMsg = std::format("Failed to create new connection: {}", new_connection_expected.error());
            utils::Logger::error(errorMsg);
            return std::unexpected(errorMsg);
        }
        connection_ptr = *new_connection_expected;
        if (!connection_ptr) { // Should ideally not happen if createConnection returns valid ptr in expected
            std::string errorMsg = "createConnection() succeeded but returned null connection pointer.";
            utils::Logger::critical(errorMsg);
            return std::unexpected(errorMsg);
        }
        // utils::Logger::debug(std::format("Created new connection. Pool size: {}, Active: {}", connections_.size(), activeConnections_ + 1));
    } else {
        // This case should ideally not be reached if wait_for logic is correct and timeout is handled.
        // It implies we woke up, not shutdown, pool is empty, and we can't create more.
        std::string errorMsg = std::format("Connection pool logic error: Woke up but no action. Pool: {}, Active: {}, Max: {}", connections_.size(), activeConnections_, maxConnections_);
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }

    activeConnections_++;
    updateMetrics();
    // utils::Logger::debug(std::format("getConnection: Provided connection. Pool: {}, Active: {}, Max: {}", connections_.size(), activeConnections_, maxConnections_));
    return connection_ptr;
}

void ConnectionManager::returnConnection(std::shared_ptr<Connection> connection) {
    if (!connection) {
        return;
    }

    std::unique_lock<std::mutex> lock(mutex_);

    if (shutdown_) {
        // Don't return connections if shutting down
        return;
    }

    // Decrement active connections
    if (activeConnections_ > 0) {
        activeConnections_--;
    }

    // Check if connection is still valid
    if (connection->isOpen()) {
        connections_.push_back(connection);
        cv_.notify_one();
    }

    // Update metrics
    updateMetrics();
}

std::expected<std::vector<std::vector<std::string>>, std::string> ConnectionManager::executeQuery(const std::string& query, const std::vector<std::string>& params) {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = *expectedConnection;
    if (!connection) {
        return std::unexpected("Failed to obtain a valid connection from the pool.");
    }

    // Determine query type
    std::string queryType = "OTHER";
    if (query.find("SELECT") == 0) {
        queryType = "SELECT";
    } else if (query.find("INSERT") == 0) {
        queryType = "INSERT";
    } else if (query.find("UPDATE") == 0) {
        queryType = "UPDATE";
    } else if (query.find("DELETE") == 0) {
        queryType = "DELETE";
    }

    // Record query start time
    auto startTime = std::chrono::steady_clock::now();
    bool success = false;

    try {
        auto result = connection->executeQuery(query, params);

        // Calculate query execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        success = true;
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, success);

        returnConnection(connection);
        return result;
    } catch (const std::exception& e) {
        // Calculate query execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, false);

        utils::Logger::error(std::format("Exception during query execution: {}", e.what()));
        returnConnection(connection);
        return std::unexpected(std::format("Exception during query execution: {}", e.what()));
    }
}

std::expected<void, std::string> ConnectionManager::executeQueryWithCallback(const std::string& query, std::function<void(std::span<const std::string>)> callback, const std::vector<std::string>& params) {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = *expectedConnection;
    if (!connection) { 
        return std::unexpected("Failed to obtain a valid connection from the pool.");
    }

    try {
        connection->executeQueryWithCallback(query, callback, params);
        returnConnection(connection);
        return {};
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during query execution with callback: {}", e.what()));
        returnConnection(connection);
        return std::unexpected(std::format("Exception during query execution with callback: {}", e.what()));
    }
}

std::expected<int, std::string> ConnectionManager::executeNonQuery(const std::string& statement, const std::vector<std::string>& params) {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = *expectedConnection;
    if (!connection) {
        return std::unexpected("Failed to obtain a valid connection from the pool.");
    }

    // Determine statement type
    std::string queryType = "OTHER";
    if (statement.find("INSERT") == 0) {
        queryType = "INSERT";
    } else if (statement.find("UPDATE") == 0) {
        queryType = "UPDATE";
    } else if (statement.find("DELETE") == 0) {
        queryType = "DELETE";
    }

    // Record statement start time
    auto startTime = std::chrono::steady_clock::now();
    bool success = false;

    try {
        auto result = connection->executeNonQuery(statement, params);

        // Calculate statement execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        success = true;
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, success);

        returnConnection(connection);
        return result;
    } catch (const std::exception& e) {
        // Calculate statement execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, false);

        utils::Logger::error(std::format("Exception during statement execution: {}", e.what()));
        returnConnection(connection);
        return std::unexpected(std::format("Exception during statement execution: {}", e.what()));
    }
}

std::expected<std::shared_ptr<Transaction>, std::string> ConnectionManager::beginTransaction() {
    auto expectedConnection = getConnection();
    if (!expectedConnection) {
        return std::unexpected(expectedConnection.error());
    }
    std::shared_ptr<Connection> connection = *expectedConnection;
    if (!connection) {
        return std::unexpected("Failed to obtain a valid connection from the pool.");
    }

    try {
        auto transaction = connection->beginTransaction();
        if (!transaction) {
            returnConnection(connection);
            return std::unexpected("Failed to begin transaction.");
        }

        return transaction;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during transaction start: {}", e.what()));
        returnConnection(connection);
        return std::unexpected(std::format("Exception during transaction start: {}", e.what()));
    }
}

void ConnectionManager::shutdown() {
    std::unique_lock<std::mutex> lock(mutex_);

    if (shutdown_) {
        return;
    }

    shutdown_ = true;
    cv_.notify_all();

    // Close all connections
    for (auto& connection : connections_) {
        connection->close();
    }

    connections_.clear();
}

std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::createConnection() {
    try {
        // Create connection with SSL configuration
        auto connection = std::make_shared<Connection>(buildConnectionString(), sslConfig_.enabled);
        if (!connection->open()) {
            utils::Logger::error("Failed to open database connection");
            return std::unexpected("Failed to open database connection");
        }

        return connection;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during connection creation: {}", e.what()));
        return std::unexpected(std::format("Exception during connection creation: {}", e.what()));
    }
}

std::string ConnectionManager::buildConnectionString() const {
    // Start with the base connection string
    std::string connStr = connectionString_;

    // Add SSL parameters if enabled
    if (sslConfig_.enabled) {
        // Check if connection string already has parameters
        bool hasParams = connStr.find("?") != std::string::npos;

        // Add parameter separator
        if (!hasParams) {
            connStr += "?";
        } else if (connStr.back() != '&' && connStr.back() != '?') {
            connStr += "&";
        }

        // Add SSL mode
        connStr += "sslmode=" + sslModeToString(sslConfig_.mode);

        // Add certificate paths if provided
        if (!sslConfig_.certPath.empty()) {
            connStr += "&sslcert=" + sslConfig_.certPath;
        }

        if (!sslConfig_.keyPath.empty()) {
            connStr += "&sslkey=" + sslConfig_.keyPath;
        }

        if (!sslConfig_.caPath.empty()) {
            connStr += "&sslrootcert=" + sslConfig_.caPath;
        }

        if (!sslConfig_.crlPath.empty()) {
            connStr += "&sslcrl=" + sslConfig_.crlPath;
        }

        // Add option to reject expired certificates
        if (sslConfig_.rejectExpired) {
            connStr += "&sslrejectexpired=1";
        }
    }

    return connStr;
}

std::string ConnectionManager::sslModeToString(SSLMode mode) {
    switch (mode) {
        case SSLMode::Disable:
            return "disable";
        case SSLMode::Allow:
            return "allow";
        case SSLMode::Prefer:
            return "prefer";
        case SSLMode::Require:
            return "require";
        case SSLMode::VerifyCa:
            return "verify-ca";
        case SSLMode::VerifyFull:
            return "verify-full";
        default:
            return "prefer";
    }
}

const SSLConfig& ConnectionManager::getSSLConfig() const {
    return sslConfig_;
}

size_t ConnectionManager::getActiveConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return activeConnections_;
}

size_t ConnectionManager::getIdleConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return connections_.size();
}

size_t ConnectionManager::getWaitingConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return waitingConnections_;
}

size_t ConnectionManager::getMaxConnections() const {
    return maxConnections_;
}

void ConnectionManager::updateMetrics() {
    auto now = std::chrono::steady_clock::now();
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastMetricsUpdate_).count();

    // Only update metrics every second to avoid excessive updates
    if (elapsedMs >= 1000) {
        lastMetricsUpdate_ = now;

        // Update database metrics
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordConnectionPoolMetrics(
            activeConnections_,
            connections_.size(),
            waitingConnections_,
            maxConnections_
        );
    }
}

// --- ManagedConnection Implementation ---

ManagedConnection::ManagedConnection(ConnectionManager& manager)
    : manager_(&manager), connection_expected_(manager.getConnection()), moved_from_(false) {
    if (!connection_expected_ && manager_) { // Log if acquisition failed
        // Error already logged by getConnection or createConnection, but can add context
        // utils::Logger::warning(std::format("ManagedConnection: Failed to acquire connection: {}", connection_expected_.error()));
    } else if (connection_expected_ && !(*connection_expected_)) {
         utils::Logger::error("ManagedConnection: Acquired connection but it's null (this should not happen if ConnectionManager::createConnection is robust).");
         // Treat as error for bool() operator by ensuring it's an error state
         connection_expected_ = std::unexpected("Acquired null connection pointer internally.");
    } else if (connection_expected_ && (*connection_expected_) && !(*connection_expected_)->isOpen()) {
        utils::Logger::warning(std::format("ManagedConnection: Acquired connection (ID: {}) but it was not open. Returning to pool.", (*connection_expected_)->getId()));
        manager_->returnConnection(*connection_expected_); // Return the unopened connection
        connection_expected_ = std::unexpected("Acquired connection was not open."); // Mark as error for this ManagedConnection instance
    }
}

ManagedConnection::~ManagedConnection() {
    if (!moved_from_ && manager_ && connection_expected_ && *connection_expected_ && (*connection_expected_)->isOpen()) {
        // Only return if it was successfully acquired and opened, and not moved from.
        // utils::Logger::debug(std::format("ManagedConnection: Destructor returning connection ID: {}", (*connection_expected_)->getId()));
        manager_->returnConnection(*connection_expected_);
    } else if (!moved_from_ && manager_ && connection_expected_ && *connection_expected_ && !(*connection_expected_)->isOpen()) {
        // If it was acquired but not open (e.g. createConnection made the object but open() failed), it might have been returned already.
        // Or if it was closed by the user. For safety, we could check manager's active count or rely on returnConnection's idempotency.
        // utils::Logger::debug(std::format("ManagedConnection: Destructor for connection ID: {} (was not open or already returned)", (*connection_expected_)->getId()));
    }
}

ManagedConnection::ManagedConnection(ManagedConnection&& other) noexcept
    : manager_(other.manager_), connection_expected_(std::move(other.connection_expected_)), moved_from_(false) {
    other.moved_from_ = true;
}

ManagedConnection& ManagedConnection::operator=(ManagedConnection&& other) noexcept {
    if (this != &other) {
        // Return existing connection if any and not moved from, and it's valid
        if (!moved_from_ && manager_ && connection_expected_ && *connection_expected_ && (*connection_expected_)->isOpen()) {
            manager_->returnConnection(*connection_expected_);
        }

        manager_ = other.manager_;
        connection_expected_ = std::move(other.connection_expected_);
        moved_from_ = false;
        other.moved_from_ = true;
    }
    return *this;
}

Connection* ManagedConnection::operator->() const {
    if (connection_expected_ && *connection_expected_) {
        return (*connection_expected_).get();
    }
    // utils::Logger::warning("ManagedConnection: Attempted to dereference a null or errored connection.");
    return nullptr;
}

std::shared_ptr<Connection> ManagedConnection::get() const {
    if (connection_expected_ && *connection_expected_) {
        return *connection_expected_;
    }
    return nullptr;
}

ManagedConnection::operator bool() const {
    // Valid if expected has value, the shared_ptr is non-null, and connection is open.
    return connection_expected_.has_value() && connection_expected_.value() && connection_expected_.value()->isOpen();
}

const std::string* ManagedConnection::getError() const {
    if (!connection_expected_.has_value()) {
        return &connection_expected_.error();
    }
    return nullptr;
}

} // namespace dbservice::core
