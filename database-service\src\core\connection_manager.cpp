#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include <format> // C++20 feature
#include <chrono>
#include <thread>
#include <filesystem>

namespace dbservice::core {

// Constructor with detailed SSL configuration
ConnectionManager::ConnectionManager(const std::string& connectionString, size_t maxConnections, const SSLConfig& sslConfig)
    : connectionString_(connectionString),
      maxConnections_(maxConnections),
      sslConfig_(sslConfig),
      shutdown_(false),
      activeConnections_(0),
      waitingConnections_(0),
      lastMetricsUpdate_(std::chrono::steady_clock::now()) {

    utils::Logger::info(std::format("Initializing connection manager with {} max connections, SSL {}",
        maxConnections, sslConfig_.enabled ? "enabled" : "disabled"));

    if (sslConfig_.enabled) {
        // Validate SSL configuration
        if (sslConfig_.mode != SSLMode::Disable) {
            // Check certificate paths if using verification modes
            if (sslConfig_.mode == SSLMode::VerifyCa || sslConfig_.mode == SSLMode::VerifyFull) {
                if (sslConfig_.caPath.empty()) {
                    utils::Logger::warning("SSL CA certificate path is empty but verification is enabled");
                } else if (!std::filesystem::exists(sslConfig_.caPath)) {
                    utils::Logger::warning(std::format("SSL CA certificate file not found: {}", sslConfig_.caPath));
                }
            }

            // Check client certificate paths if provided
            if (!sslConfig_.certPath.empty() && !std::filesystem::exists(sslConfig_.certPath)) {
                utils::Logger::warning(std::format("SSL client certificate file not found: {}", sslConfig_.certPath));
            }

            if (!sslConfig_.keyPath.empty() && !std::filesystem::exists(sslConfig_.keyPath)) {
                utils::Logger::warning(std::format("SSL client key file not found: {}", sslConfig_.keyPath));
            }
        }
    }

    // Pre-create some connections
    size_t initialConnections = std::min(maxConnections_ / 2, static_cast<size_t>(2));
    for (size_t i = 0; i < initialConnections; ++i) {
        auto connection = createConnection();
        if (connection) {
            connections_.push_back(connection);
        }
    }

    utils::Logger::info(std::format("Connection manager initialized with {} initial connections", connections_.size()));
}

// Constructor with simplified SSL configuration
ConnectionManager::ConnectionManager(const std::string& connectionString, size_t maxConnections, bool useSSL)
    : connectionString_(connectionString),
      maxConnections_(maxConnections),
      shutdown_(false),
      activeConnections_(0),
      waitingConnections_(0),
      lastMetricsUpdate_(std::chrono::steady_clock::now()) {

    // Create default SSL configuration
    sslConfig_.enabled = useSSL;
    sslConfig_.mode = SSLMode::VerifyFull;

    if (useSSL) {
        // Default paths for SSL certificates
        sslConfig_.certPath = "/etc/letsencrypt/live/chcit.org/fullchain.pem";
        sslConfig_.keyPath = "/etc/letsencrypt/live/chcit.org/privkey.pem";
        sslConfig_.caPath = "/etc/letsencrypt/live/chcit.org/chain.pem";

        utils::Logger::info("Using default SSL certificate paths");
    }

    // Pre-create some connections
    size_t initialConnections = std::min(maxConnections_ / 2, static_cast<size_t>(2));
    for (size_t i = 0; i < initialConnections; ++i) {
        auto connection = createConnection();
        if (connection) {
            connections_.push_back(connection);
        }
    }
}

ConnectionManager::~ConnectionManager() {
    shutdown();
}

std::shared_ptr<Connection> ConnectionManager::getConnection() {
    std::unique_lock<std::mutex> lock(mutex_);

    // Track waiting connections
    waitingConnections_++;

    // Wait for an available connection or until shutdown
    auto waitStart = std::chrono::steady_clock::now();

    cv_.wait(lock, [this] {
        return !connections_.empty() || connections_.size() < maxConnections_ || shutdown_;
    });

    // Calculate wait time
    auto waitEnd = std::chrono::steady_clock::now();
    auto waitTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(waitEnd - waitStart).count();

    // Decrement waiting connections
    waitingConnections_--;

    if (shutdown_) {
        return nullptr;
    }

    std::shared_ptr<Connection> connection;

    if (!connections_.empty()) {
        // Get an existing connection
        connection = connections_.back();
        connections_.pop_back();
    } else if (connections_.size() < maxConnections_) {
        // Create a new connection
        lock.unlock();
        connection = createConnection();
        lock.lock();
    } else {
        // Wait for a connection to become available
        cv_.wait(lock, [this] { return !connections_.empty() || shutdown_; });

        if (shutdown_) {
            return nullptr;
        }

        connection = connections_.back();
        connections_.pop_back();
    }

    // Increment active connections
    activeConnections_++;

    // Update metrics
    updateMetrics();

    return connection;
}

void ConnectionManager::returnConnection(std::shared_ptr<Connection> connection) {
    if (!connection) {
        return;
    }

    std::unique_lock<std::mutex> lock(mutex_);

    if (shutdown_) {
        // Don't return connections if shutting down
        return;
    }

    // Decrement active connections
    if (activeConnections_ > 0) {
        activeConnections_--;
    }

    // Check if connection is still valid
    if (connection->isOpen()) {
        connections_.push_back(connection);
        cv_.notify_one();
    }

    // Update metrics
    updateMetrics();
}

std::vector<std::vector<std::string>> ConnectionManager::executeQuery(const std::string& query, const std::vector<std::string>& params) {
    auto connection = getConnection();
    if (!connection) {
        return {};
    }

    // Determine query type
    std::string queryType = "OTHER";
    if (query.find("SELECT") == 0) {
        queryType = "SELECT";
    } else if (query.find("INSERT") == 0) {
        queryType = "INSERT";
    } else if (query.find("UPDATE") == 0) {
        queryType = "UPDATE";
    } else if (query.find("DELETE") == 0) {
        queryType = "DELETE";
    }

    // Record query start time
    auto startTime = std::chrono::steady_clock::now();
    bool success = false;

    try {
        auto result = connection->executeQuery(query, params);

        // Calculate query execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        success = true;
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, success);

        returnConnection(connection);
        return result;
    } catch (const std::exception& e) {
        // Calculate query execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, false);

        utils::Logger::error(std::format("Exception during query execution: {}", e.what()));
        returnConnection(connection);
        return {};
    }
}

void ConnectionManager::executeQueryWithCallback(const std::string& query, std::function<void(const std::vector<std::string>&)> callback, const std::vector<std::string>& params) {
    auto connection = getConnection();
    if (!connection) {
        return;
    }

    try {
        connection->executeQueryWithCallback(query, callback, params);
        returnConnection(connection);
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during query execution with callback: {}", e.what()));
        returnConnection(connection);
    }
}

int ConnectionManager::executeNonQuery(const std::string& statement, const std::vector<std::string>& params) {
    auto connection = getConnection();
    if (!connection) {
        return -1;
    }

    // Determine statement type
    std::string queryType = "OTHER";
    if (statement.find("INSERT") == 0) {
        queryType = "INSERT";
    } else if (statement.find("UPDATE") == 0) {
        queryType = "UPDATE";
    } else if (statement.find("DELETE") == 0) {
        queryType = "DELETE";
    }

    // Record statement start time
    auto startTime = std::chrono::steady_clock::now();
    bool success = false;

    try {
        auto result = connection->executeNonQuery(statement, params);

        // Calculate statement execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        success = true;
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, success);

        returnConnection(connection);
        return result;
    } catch (const std::exception& e) {
        // Calculate statement execution time
        auto endTime = std::chrono::steady_clock::now();
        auto executionTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

        // Record query metrics
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordQueryMetric(queryType, executionTimeMs, false);

        utils::Logger::error(std::format("Exception during statement execution: {}", e.what()));
        returnConnection(connection);
        return -1;
    }
}

std::shared_ptr<Transaction> ConnectionManager::beginTransaction() {
    auto connection = getConnection();
    if (!connection) {
        return nullptr;
    }

    try {
        auto transaction = connection->beginTransaction();
        if (!transaction) {
            returnConnection(connection);
            return nullptr;
        }

        return transaction;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during transaction start: {}", e.what()));
        returnConnection(connection);
        return nullptr;
    }
}

void ConnectionManager::shutdown() {
    std::unique_lock<std::mutex> lock(mutex_);

    if (shutdown_) {
        return;
    }

    shutdown_ = true;
    cv_.notify_all();

    // Close all connections
    for (auto& connection : connections_) {
        connection->close();
    }

    connections_.clear();
}

std::shared_ptr<Connection> ConnectionManager::createConnection() {
    try {
        // Create connection with SSL configuration
        auto connection = std::make_shared<Connection>(buildConnectionString(), sslConfig_.enabled);
        if (!connection->open()) {
            utils::Logger::error("Failed to open database connection");
            return nullptr;
        }

        return connection;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during connection creation: {}", e.what()));
        return nullptr;
    }
}

std::string ConnectionManager::buildConnectionString() const {
    // Start with the base connection string
    std::string connStr = connectionString_;

    // Add SSL parameters if enabled
    if (sslConfig_.enabled) {
        // Check if connection string already has parameters
        bool hasParams = connStr.find("?") != std::string::npos;

        // Add parameter separator
        if (!hasParams) {
            connStr += "?";
        } else if (connStr.back() != '&' && connStr.back() != '?') {
            connStr += "&";
        }

        // Add SSL mode
        connStr += "sslmode=" + sslModeToString(sslConfig_.mode);

        // Add certificate paths if provided
        if (!sslConfig_.certPath.empty()) {
            connStr += "&sslcert=" + sslConfig_.certPath;
        }

        if (!sslConfig_.keyPath.empty()) {
            connStr += "&sslkey=" + sslConfig_.keyPath;
        }

        if (!sslConfig_.caPath.empty()) {
            connStr += "&sslrootcert=" + sslConfig_.caPath;
        }

        if (!sslConfig_.crlPath.empty()) {
            connStr += "&sslcrl=" + sslConfig_.crlPath;
        }

        // Add option to reject expired certificates
        if (sslConfig_.rejectExpired) {
            connStr += "&sslrejectexpired=1";
        }
    }

    return connStr;
}

std::string ConnectionManager::sslModeToString(SSLMode mode) {
    switch (mode) {
        case SSLMode::Disable:
            return "disable";
        case SSLMode::Allow:
            return "allow";
        case SSLMode::Prefer:
            return "prefer";
        case SSLMode::Require:
            return "require";
        case SSLMode::VerifyCa:
            return "verify-ca";
        case SSLMode::VerifyFull:
            return "verify-full";
        default:
            return "prefer";
    }
}

const SSLConfig& ConnectionManager::getSSLConfig() const {
    return sslConfig_;
}

size_t ConnectionManager::getActiveConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return activeConnections_;
}

size_t ConnectionManager::getIdleConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return connections_.size();
}

size_t ConnectionManager::getWaitingConnectionCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return waitingConnections_;
}

size_t ConnectionManager::getMaxConnections() const {
    return maxConnections_;
}

void ConnectionManager::updateMetrics() {
    auto now = std::chrono::steady_clock::now();
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastMetricsUpdate_).count();

    // Only update metrics every second to avoid excessive updates
    if (elapsedMs >= 1000) {
        lastMetricsUpdate_ = now;

        // Update database metrics
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordConnectionPoolMetrics(
            activeConnections_,
            connections_.size(),
            waitingConnections_,
            maxConnections_
        );
    }
}

} // namespace dbservice::core
