# Git Dashboard Security Enhancements

## Overview

This document provides advanced security configurations for the Git Dashboard system, focusing on access control, resource limitations, and GitHub/GitLab integration security. These enhancements supplement the baseline security configurations found in the main setup guides.

## Access Control Lists (ACLs)

### Setting Up ACLs for Data Directory

Implement detailed access control for critical directories:

```bash
# Install ACL package
sudo apt install -y acl

# Set default ACLs on data directory
sudo setfacl -R -m u:www-data:r-x /opt/git-dashboard/data
sudo setfacl -R -m d:u:www-data:r-x /opt/git-dashboard/data

# Allow write access only to specific subdirectories
sudo setfacl -R -m u:www-data:rwx /opt/git-dashboard/data/history
sudo setfacl -R -m d:u:www-data:rwx /opt/git-dashboard/data/history
```

### SELinux Configuration (if enabled)

If SELinux is enabled on your Ubuntu system:

```bash
# Install SELinux packages
sudo apt install -y selinux-utils selinux-basics policycoreutils

# Set proper contexts for web content
sudo semanage fcontext -a -t httpd_sys_content_t "/opt/git-dashboard(/.*)?"
sudo semanage fcontext -a -t httpd_sys_rw_content_t "/opt/git-dashboard/data(/.*)?"
sudo semanage fcontext -a -t httpd_log_t "/opt/git-dashboard/logs(/.*)?"

# Apply contexts
sudo restorecon -Rv /opt/git-dashboard

# Allow Nginx to connect to network
sudo setsebool -P httpd_can_network_connect 1
```

## Resource Limitation

### Systemd Service Resource Limits

Enhance the systemd service file with resource limitations:

```bash
sudo nano /etc/systemd/system/git-dashboard.service
```

Add resource constraints to the `[Service]` section:

```ini
[Service]
# Resource limitations
CPUQuota=50%
MemoryMax=512M
IOWeight=200
IODeviceWeight=/dev/sda 500
MemorySwapMax=0

# Security enhancements
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=read-only
PrivateTmp=true
ReadWritePaths=/opt/git-dashboard/data /opt/git-dashboard/logs
```

### Metrics Collection Script I/O Priority

Set appropriate I/O priorities for the background metrics collection to prevent overwhelming system resources:

```bash
sudo nano /opt/git-dashboard/collect-metrics.sh
```

Add at the beginning of the script:

```bash
#!/bin/bash

# Set I/O priority to be lower than other processes
ionice -c 3 -p $$
```

Update cron job to use ionice directly:

```bash
# Edit root's crontab
sudo crontab -e
```

Modify the metrics collection line:

```cron
* * * * * /usr/bin/ionice -c 3 /opt/git-dashboard/collect-metrics.sh > /dev/null 2>&1
```

## GitHub/GitLab Integration Security

### Secure Webhook Configuration

If integrating with GitHub or GitLab webhooks, implement these security measures:

1. **Create a Webhook Secret**:

```bash
# Generate a secure random token
sudo openssl rand -hex 32 > /opt/git-dashboard/webhook_secret.txt
sudo chmod 600 /opt/git-dashboard/webhook_secret.txt
sudo chown root:root /opt/git-dashboard/webhook_secret.txt
```

2. **Implement Webhook Signature Verification**:

Add to your Flask app (app.py):

```python
import hmac
import hashlib

# Load webhook secret
try:
    with open('/opt/git-dashboard/webhook_secret.txt', 'r') as f:
        WEBHOOK_SECRET = f.read().strip()
except Exception as e:
    print(f"Error loading webhook secret: {e}")
    WEBHOOK_SECRET = ""

@app.route('/api/webhook', methods=['POST'])
def webhook():
    # Verify signature for GitHub webhooks
    if request.headers.get('X-GitHub-Event'):
        signature = request.headers.get('X-Hub-Signature-256')
        if not signature:
            return jsonify({'error': 'No signature provided'}), 403
            
        # Calculate expected signature
        payload = request.data
        expected_signature = 'sha256=' + hmac.new(
            WEBHOOK_SECRET.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Verify signatures match
        if not hmac.compare_digest(expected_signature, signature):
            return jsonify({'error': 'Invalid signature'}), 403
    
    # Process webhook payload
    # ...
    
    return jsonify({'status': 'success'}), 200
```

### Secure API Token Storage

For integrating with GitHub/GitLab APIs:

1. **Store API Tokens Securely**:

```bash
# Create a secure directory for API tokens
sudo mkdir -p /opt/git-dashboard/secure
sudo chmod 700 /opt/git-dashboard/secure

# Create token file
sudo nano /opt/git-dashboard/secure/github_token.txt
```

Add your token to the file, then secure it:

```bash
sudo chmod 600 /opt/git-dashboard/secure/github_token.txt
sudo chown root:root /opt/git-dashboard/secure/github_token.txt
```

2. **Accessing Tokens in Flask Application**:

```python
def get_github_token():
    token_path = '/opt/git-dashboard/secure/github_token.txt'
    try:
        with open(token_path, 'r') as f:
            return f.read().strip()
    except Exception as e:
        logger.error(f"Error reading GitHub token: {e}")
        return ""

def github_api_request(endpoint, method='GET', data=None):
    token = get_github_token()
    if not token:
        return {'error': 'API token not configured'}
        
    headers = {
        'Authorization': f'token {token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    # Make API request with proper error handling
    # ...
```

## Network Security Hardening

### Enhanced Nginx Security Headers

Add additional security headers to your Nginx configuration:

```nginx
# Security headers
add_header X-Content-Type-Options nosniff always;
add_header X-Frame-Options SAMEORIGIN always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy strict-origin-when-cross-origin always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

# Enhanced Content-Security-Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self'; font-src 'self'; object-src 'none'; media-src 'self'; frame-src 'none'; base-uri 'self'; form-action 'self';" always;
```

### IP-based Access Restrictions

Limit access to admin endpoints based on IP address:

```nginx
# Admin access restriction
location /api/admin/ {
    # Allow specific IPs
    allow ***********/24;  # Internal network
    allow 10.0.0.0/8;      # VPN network
    deny all;              # Deny everyone else
    
    proxy_pass http://127.0.0.1:8000;
    # ... other proxy settings
}
```

## Error Handling and Recovery

### Dashboard Service Recovery

Enhance service recovery options in systemd:

```bash
sudo nano /etc/systemd/system/git-dashboard.service
```

Add reliable restart options:

```ini
[Service]
# ... existing settings

# Advanced restart behavior
Restart=on-failure
RestartSec=5
StartLimitInterval=600
StartLimitBurst=5
StartLimitAction=reboot-force

[Install]
WantedBy=multi-user.target
```

### Automated Recovery Script

Create a recovery script for automated healing:

```bash
sudo nano /opt/git-dashboard/recovery.sh
```

Add the following content:

```bash
#!/bin/bash

LOG_FILE="/opt/git-dashboard/logs/recovery.log"

# Log function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

log "Starting recovery process"

# Check if metrics file exists and is valid JSON
METRICS_FILE="/opt/git-dashboard/data/metrics_history.json"
if [ ! -f "$METRICS_FILE" ] || ! jq empty "$METRICS_FILE" 2>/dev/null; then
    log "Metrics file is missing or corrupt. Recreating..."
    echo '{"timestamp": "'$(date '+%Y-%m-%d %H:%M:%S')'", "system": {}, "git": {}}' > "$METRICS_FILE"
    log "Triggering metrics collection"
    /opt/git-dashboard/collect-metrics.sh
fi

# Check if application is responsive
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/status)
if [ "$API_STATUS" != "200" ]; then
    log "API is not responsive (status: $API_STATUS). Restarting service..."
    systemctl restart git-dashboard
    sleep 5
    
    # Verify restart worked
    NEW_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/status)
    if [ "$NEW_STATUS" != "200" ]; then
        log "Service failed to recover after restart. Performing deep recovery..."
        systemctl stop git-dashboard
        pkill -f gunicorn
        sleep 2
        systemctl start git-dashboard
    else
        log "Service successfully recovered after restart"
    fi
fi

log "Recovery process completed"
```

Make it executable and add to root's crontab to run every 15 minutes:

```bash
sudo chmod +x /opt/git-dashboard/recovery.sh
sudo crontab -e
```

Add:

```cron
*/15 * * * * /opt/git-dashboard/recovery.sh
```

## Configuration Version Control

### Tracking Configuration Changes with Git

Set up version control for configuration files:

```bash
# Create a git repository for configuration
sudo mkdir -p /opt/git-dashboard-config
sudo cd /opt/git-dashboard-config
sudo git init

# Copy configuration files
sudo cp /etc/nginx/sites-available/git-dashboard.conf .
sudo cp /etc/systemd/system/git-dashboard.service .
sudo cp /opt/git-dashboard/app.py .
sudo cp /opt/git-dashboard/gunicorn_config.py .
sudo cp /opt/git-dashboard/collect-metrics.sh .

# Create .gitignore for sensitive information
sudo echo "secure/" > .gitignore
sudo echo ".env" >> .gitignore

# Initial commit
sudo git config --global user.email "<EMAIL>"
sudo git config --global user.name "Git Dashboard Admin"
sudo git add .
sudo git commit -m "Initial configuration"
```

### Configuration Deployment Script

Create a script to apply version-controlled configurations:

```bash
sudo nano /opt/git-dashboard-config/deploy.sh
```

Add the following content:

```bash
#!/bin/bash

LOG_FILE="/opt/git-dashboard-config/deploy.log"

# Log function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

log "Deploying configuration"

# Check if there are local changes
if [ -n "$(git status --porcelain)" ]; then
    log "WARNING: There are uncommitted changes. Commit them before deploying."
    git status
    exit 1
fi

# Copy configuration files to their proper locations
cp git-dashboard.conf /etc/nginx/sites-available/
cp git-dashboard.service /etc/systemd/system/
cp app.py /opt/git-dashboard/
cp gunicorn_config.py /opt/git-dashboard/
cp collect-metrics.sh /opt/git-dashboard/

# Set proper permissions
chmod 644 /etc/nginx/sites-available/git-dashboard.conf
chmod 644 /etc/systemd/system/git-dashboard.service
chmod 644 /opt/git-dashboard/app.py /opt/git-dashboard/gunicorn_config.py
chmod 755 /opt/git-dashboard/collect-metrics.sh

# Reload services
systemctl daemon-reload
systemctl reload nginx
systemctl restart git-dashboard

log "Configuration deployed successfully"
```

Make it executable:

```bash
sudo chmod +x /opt/git-dashboard-config/deploy.sh
```

## Metrics Collection Script Error Handling

Enhance the metrics collection script with better error handling:

```bash
sudo nano /opt/git-dashboard/collect-metrics.sh
```

Add robust error handling:

```bash
#!/bin/bash

# Configuration
DASHBOARD_DIR="/opt/git-dashboard"
DATA_DIR="$DASHBOARD_DIR/data"
METRICS_FILE="$DATA_DIR/metrics_history.json"
LOG_FILE="$DASHBOARD_DIR/logs/metrics.log"
HISTORY_DIR="$DATA_DIR/history"
BACKUP_DIR="$DATA_DIR/backups"

# Set I/O priority to be lower than other processes
ionice -c 3 -p $$

# Ensure directories exist
mkdir -p "$DATA_DIR" "$HISTORY_DIR" "$BACKUP_DIR"

# Log function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Error handling function
handle_error() {
    log "ERROR: $1"
    
    # Check if we have a backup metrics file
    if [ -f "$BACKUP_DIR/metrics_latest.json" ]; then
        log "Restoring from backup"
        cp "$BACKUP_DIR/metrics_latest.json" "$METRICS_FILE"
    else
        # Create minimal valid metrics
        log "Creating minimal metrics file"
        echo '{"timestamp": "'$(date '+%Y-%m-%d %H:%M:%S')'", "system": {}, "git": {}}' > "$METRICS_FILE"
    fi
}

# Backup current metrics before making changes
if [ -f "$METRICS_FILE" ]; then
    cp "$METRICS_FILE" "$BACKUP_DIR/metrics_latest.json"
fi

# Get system metrics with error handling
log "Collecting system metrics"
cpu_percent=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' || echo "0.0")
memory=$(free -m | grep Mem | awk '{print $3","$2}' || echo "0,0")
disk=$(df -h / | awk 'NR==2 {print $5}' | tr -d '%' || echo "0")
uptime=$(uptime | grep -ohe 'load average[s:][: ].*' | awk '{print $3" "$4" "$5}' | tr -d ',' || echo "0.00 0.00 0.00")

# Get git metrics with error handling
log "Collecting git metrics"
GIT_DIR="/home/<USER>/project-tracker.git"

if [ -d "$GIT_DIR" ]; then
    # Navigate to Git directory with error handling
    if ! cd "$GIT_DIR"; then
        handle_error "Failed to access Git repository"
        exit 1
    fi
    
    # Get Git metrics with error handling
    total_commits=$(git rev-list --all --count 2>/dev/null || echo "0")
    total_branches=$(git branch -a | wc -l 2>/dev/null || echo "0")
    recent_activity=$(git log --format="%H|%an|%at|%s" -n 10 2>/dev/null || echo "")
    
    # Format Git activity as JSON array
    json_commits="[]"
    if [ -n "$recent_activity" ]; then
        json_commits="["
        while IFS='' read -r line || [ -n "$line" ]; do
            IFS='|' read -r hash author timestamp subject <<< "$line"
            date=$(date -d @"$timestamp" '+%Y-%m-%d %H:%M:%S')
            json_commits+="{\"hash\":\"${hash}\",\"author\":\"${author}\",\"date\":\"${date}\",\"subject\":\"${subject}\"},"
        done <<< "$recent_activity"
        json_commits=${json_commits%,}
        json_commits+="]"
    fi
else
    log "WARNING: Git directory not found at $GIT_DIR"
    total_commits=0
    total_branches=0
    json_commits="[]"
fi

# Format timestamp
timestamp=$(date '+%Y-%m-%d %H:%M:%S')

# Format all metrics as JSON
log "Formatting metrics as JSON"
metrics="{\
  \"timestamp\": \"$timestamp\",\
  \"system\": {\
    \"cpu\": $cpu_percent,\
    \"memory\": {\"used\": ${memory%,*}, \"total\": ${memory#*,}},\
    \"disk\": $disk,\
    \"load\": \"$uptime\"\
  },\
  \"git\": {\
    \"total_commits\": $total_commits,\
    \"total_branches\": $total_branches,\
    \"recent_commits\": $json_commits\
  }\
}"

# Validate JSON before writing to file
if ! echo "$metrics" | jq empty 2>/dev/null; then
    handle_error "Invalid JSON generated"
    exit 1
fi

# Write to file
echo "$metrics" > "$METRICS_FILE"

# Archive metrics to history directory (hourly)
current_hour=$(date '+%H')
current_minute=$(date '+%M')
if [ "$current_minute" == "00" ]; then
    archive_file="$HISTORY_DIR/metrics_$(date '+%Y%m%d%H').json"
    cp "$METRICS_FILE" "$archive_file"
    log "Archived metrics to $archive_file"
    
    # Clean up old history files (keep 30 days)
    find "$HISTORY_DIR" -name "metrics_*.json" -mtime +30 -delete
fi

log "Metrics collection completed successfully"
```
