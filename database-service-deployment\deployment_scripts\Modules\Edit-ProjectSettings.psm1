# Edit-ProjectSettings.psm1
# Module for editing project settings in the deployment configuration

# Import the central Configuration module
Import-Module "$PSScriptRoot\Configuration.psm1" -DisableNameChecking
Import-Module "$PSScriptRoot\..\Common.psm1" -DisableNameChecking
# Import Logger module for consistent logging
Import-Module "$PSScriptRoot\Logger\Logger.psm1" -Force

function Edit-ProjectSettings {
    param()
    
    try {
        # Clear the screen and show the header
        Clear-Host
        Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
        Write-Log -Message "               Edit Project Settings                   " -Level "UI" -ForegroundColor Blue
        Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
        Write-Log -Message "" -Level "UI"
        
        # Get the current configuration using the central configuration module
        $config = Get-Configuration
        
        # Check if configuration was loaded
        if ($null -eq $config -or $null -eq $config.project) {
            Write-Log -Message "No configuration loaded." -Level "UI" -ForegroundColor Red
            Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
            Read-Host | Out-Null
            return
        }
        
        # Get current environment
        $env = $script:Environment
        if ($null -ne $env:DeploymentEnvironment) {
            $env = $env:DeploymentEnvironment
        }
        
        # Show current settings
        Write-Log -Message "Current Project Settings:" -Level "UI" -ForegroundColor Yellow
        Write-Log -Message "  Name: $($config.project.name)" -Level "UI" -ForegroundColor White
        Write-Log -Message "  Description: $($config.project.description)" -Level "UI" -ForegroundColor White
        Write-Log -Message "  Local Source Directory: $($config.project.local_source_dir)" -Level "UI" -ForegroundColor White 
        Write-Log -Message "  Remote Install Directory: $($config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
        Write-Log -Message "  Remote Build Directory: $($config.project.remote_build_dir)" -Level "UI" -ForegroundColor White
        Write-Log -Message "" -Level "UI"
        
        # Show options
        Write-Log -Message "[1] Edit Project Name" -Level "UI" -ForegroundColor White
        Write-Log -Message "[2] Edit Project Description" -Level "UI" -ForegroundColor White
        Write-Log -Message "[3] Edit Local Source Directory" -Level "UI" -ForegroundColor White
        Write-Log -Message "[4] Edit Remote Install Directory" -Level "UI" -ForegroundColor White
        Write-Log -Message "[5] Edit Remote Build Directory" -Level "UI" -ForegroundColor White
        Write-Log -Message "[6] Back to Edit Configuration" -Level "UI" -ForegroundColor White
        
        $choice = Read-Host "`nSelect an option (1-6)"
        
        switch ($choice) {
            "1" {
                $newValue = Read-Host "Enter new project name"
                if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                    $config.project.name = $newValue
                    try {
                        Save-Configuration
                        Write-Log -Message "Project name updated to: $newValue" -Level "UI" -ForegroundColor Green
                    } catch {
                        Write-Log -Message "Error saving configuration: $_" -Level "UI" -ForegroundColor Red
                    }
                    Start-Sleep -Seconds 1
                }
                Edit-ProjectSettings
                break
            }
            "2" {
                $newValue = Read-Host "Enter new project description"
                if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                    $config.project.description = $newValue
                    try {
                        Save-Configuration
                        Write-Log -Message "Project description updated to: $newValue" -Level "UI" -ForegroundColor Green
                    } catch {
                        Write-Log -Message "Error saving configuration: $_" -Level "UI" -ForegroundColor Red
                    }
                    Start-Sleep -Seconds 1
                }
                Edit-ProjectSettings
                break
            }
            "3" {
                $newValue = Read-Host "Enter new local source directory"
                if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                    $config.project.local_source_dir = $newValue
                    try {
                        Save-Configuration
                        Write-Log -Message "Local source directory updated to: $newValue" -Level "UI" -ForegroundColor Green
                    } catch {
                        Write-Log -Message "Error saving configuration: $_" -Level "UI" -ForegroundColor Red
                    }
                    Start-Sleep -Seconds 1
                }
                Edit-ProjectSettings
                break
            }
            "4" {
                $newValue = Read-Host "Enter new remote install directory"
                if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                    $config.project.remote_install_dir = $newValue
                    try {
                        Save-Configuration
                        Write-Log -Message "Remote install directory updated to: $newValue" -Level "UI" -ForegroundColor Green
                    } catch {
                        Write-Log -Message "Error saving configuration: $_" -Level "UI" -ForegroundColor Red
                    }
                    Start-Sleep -Seconds 1
                }
                Edit-ProjectSettings
                break
            }
            "5" {
                $newValue = Read-Host "Enter new remote build directory"
                if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                    $config.project.remote_build_dir = $newValue
                    try {
                        Save-Configuration
                        Write-Log -Message "Remote build directory updated to: $newValue" -Level "UI" -ForegroundColor Green
                    } catch {
                        Write-Log -Message "Error saving configuration: $_" -Level "UI" -ForegroundColor Red
                    }
                    Start-Sleep -Seconds 1
                }
                Edit-ProjectSettings
                break
            }
            "6" {
                # Return to Edit Configuration menu
                if (Get-Command -Name Edit-Configuration -ErrorAction SilentlyContinue) {
                    Write-Log -Message "Returning to Edit Configuration menu..." -Level "UI" -ForegroundColor Yellow
                    Start-Sleep -Seconds 1
                    Edit-Configuration
                } else {
                    Write-Log -Message "Returning to previous menu..." -Level "UI" -ForegroundColor Yellow
                    Start-Sleep -Seconds 1
                }
                break
            }
            default {
                Write-Log -Message "Invalid option. Please try again." -Level "UI" -ForegroundColor Red
                Start-Sleep -Seconds 1
                Edit-ProjectSettings
                break
            }
        }
    }
    catch {
        Write-Log -Message "Error in Edit-ProjectSettings: $_" -Level "UI" -ForegroundColor Red
        Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
        Read-Host | Out-Null
    }
}

# Export the function
Export-ModuleMember -Function Edit-ProjectSettings
