#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"
#include <libpq-fe.h>
#include <stdexcept>
#include <iostream>
#include <format> // C++20 feature

namespace dbservice::core {

Connection::Connection(const std::string& connectionString, bool useSSL)
    : connectionString_(connectionString),
      useSSL_(useSSL),
      connection_(nullptr),
      isOpen_(false) {
}

Connection::~Connection() {
    close();
}

bool Connection::open() {
    if (isOpen_) {
        return true;
    }

    try {
        // Connect to the database
        // Note: SSL configuration is now handled in the connection string
        connection_ = PQconnectdb(connectionString_.c_str());

        // Check connection status
        if (PQstatus(static_cast<PGconn*>(connection_)) != CONNECTION_OK) {
            std::string errorMessage = PQerrorMessage(static_cast<PGconn*>(connection_));
            utils::Logger::error(std::format("Failed to connect to database: {}", errorMessage));
            close();
            return false;
        }

        // Verify SSL connection if SSL was requested
        if (useSSL_) {
            // Check if SSL is actually being used
            auto sslInUse = PQsslInUse(static_cast<PGconn*>(connection_));
            if (!sslInUse) {
                utils::Logger::warning("SSL was requested but connection is not using SSL");
                // We don't fail here as the connection might still be usable
                // depending on the security requirements
            } else {
                // Log SSL details
                auto sslAttribute = [this](const char* name) -> std::string {
                    const char* value = PQsslAttribute(static_cast<PGconn*>(connection_), name);
                    return value ? value : "";
                };

                utils::Logger::info(std::format("SSL connection established: protocol={}, cipher={}, bits={}",
                    sslAttribute("protocol"), sslAttribute("cipher"), sslAttribute("key_bits")));
            }
        }

        isOpen_ = true;
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during database connection: {}", e.what()));
        close();
        return false;
    }
}

void Connection::close() {
    if (connection_) {
        PQfinish(static_cast<PGconn*>(connection_));
        connection_ = nullptr;
    }
    isOpen_ = false;
}

bool Connection::isOpen() const {
    return isOpen_ && connection_ && (PQstatus(static_cast<PGconn*>(connection_)) == CONNECTION_OK);
}

std::vector<std::vector<std::string>> Connection::executeQuery(const std::string& query, const std::vector<std::string>& params) {
    std::vector<std::vector<std::string>> result;

    if (!isOpen() && !open()) {
        utils::Logger::error("Cannot execute query on closed connection");
        return result;
    }

    try {
        // Prepare statement
        void* stmt = prepareStatement(query, params);
        if (!stmt) {
            return result;
        }

        PGresult* pgResult = static_cast<PGresult*>(stmt);

        // Get number of rows and columns
        int rows = PQntuples(pgResult);
        int cols = PQnfields(pgResult);

        // Process results
        for (int i = 0; i < rows; ++i) {
            std::vector<std::string> row;
            for (int j = 0; j < cols; ++j) {
                char* value = PQgetvalue(pgResult, i, j);
                row.push_back(value ? value : "");
            }
            result.push_back(row);
        }

        // Clean up
        PQclear(pgResult);

        return result;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during query execution: {}", e.what()));
        return result;
    }
}

void Connection::executeQueryWithCallback(const std::string& query, std::function<void(const std::vector<std::string>&)> callback, const std::vector<std::string>& params) {
    if (!isOpen() && !open()) {
        utils::Logger::error("Cannot execute query on closed connection");
        return;
    }

    try {
        // Prepare statement
        void* stmt = prepareStatement(query, params);
        if (!stmt) {
            return;
        }

        PGresult* pgResult = static_cast<PGresult*>(stmt);

        // Get number of rows and columns
        int rows = PQntuples(pgResult);
        int cols = PQnfields(pgResult);

        // Process results
        for (int i = 0; i < rows; ++i) {
            std::vector<std::string> row;
            for (int j = 0; j < cols; ++j) {
                char* value = PQgetvalue(pgResult, i, j);
                row.push_back(value ? value : "");
            }
            callback(row);
        }

        // Clean up
        PQclear(pgResult);
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during query execution with callback: {}", e.what()));
    }
}

int Connection::executeNonQuery(const std::string& statement, const std::vector<std::string>& params) {
    if (!isOpen() && !open()) {
        utils::Logger::error("Cannot execute statement on closed connection");
        return -1;
    }

    try {
        // Prepare statement
        void* stmt = prepareStatement(statement, params);
        if (!stmt) {
            return -1;
        }

        PGresult* pgResult = static_cast<PGresult*>(stmt);

        // Get number of affected rows
        char* affectedRows = PQcmdTuples(pgResult);
        int result = affectedRows && *affectedRows ? std::stoi(affectedRows) : 0;

        // Clean up
        PQclear(pgResult);

        return result;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during statement execution: {}", e.what()));
        return -1;
    }
}

std::shared_ptr<Transaction> Connection::beginTransaction() {
    if (!isOpen() && !open()) {
        utils::Logger::error("Cannot begin transaction on closed connection");
        return nullptr;
    }

    try {
        // Execute BEGIN statement
        int result = executeNonQuery("BEGIN", {});
        if (result < 0) {
            utils::Logger::error("Failed to begin transaction");
            return nullptr;
        }

        return std::make_shared<Transaction>(shared_from_this());
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during transaction start: {}", e.what()));
        return nullptr;
    }
}

void* Connection::prepareStatement(const std::string& statement, const std::vector<std::string>& params) {
    if (!isOpen() && !open()) {
        utils::Logger::error("Cannot prepare statement on closed connection");
        return nullptr;
    }

    try {
        // Convert params to C-style array
        int nParams = static_cast<int>(params.size());
        const char** paramValues = nullptr;

        if (nParams > 0) {
            paramValues = new const char*[nParams];
            for (int i = 0; i < nParams; ++i) {
                paramValues[i] = params[i].c_str();
            }
        }

        // Execute query
        PGresult* result = PQexecParams(
            static_cast<PGconn*>(connection_),
            statement.c_str(),
            nParams,
            nullptr,  // param types
            paramValues,
            nullptr,  // param lengths
            nullptr,  // param formats
            0         // result format (0 = text)
        );

        // Clean up
        if (paramValues) {
            delete[] paramValues;
        }

        // Check result
        ExecStatusType status = PQresultStatus(result);
        if (status != PGRES_COMMAND_OK && status != PGRES_TUPLES_OK) {
            std::string errorMessage = PQerrorMessage(static_cast<PGconn*>(connection_));
            utils::Logger::error(std::format("Query execution failed: {}", errorMessage));
            PQclear(result);
            return nullptr;
        }

        return result;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during statement preparation: {}", e.what()));
        return nullptr;
    }
}

} // namespace dbservice::core
