#!/bin/bash

# Exit on any error
set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run this script as root"
    exit 1
fi

# Configuration variables
INSTALL_DIR="/opt/git-dashboard"
NGINX_CONF="/etc/nginx/sites-available/git-dashboard"
SYSTEMD_SERVICE="/etc/systemd/system/git-dashboard.service"
WEB_USER="root"  # Running as root for full system access
PYTHON_VERSION="3"  # Use default Python3 version
BIND_IP="0.0.0.0"  # Default to all interfaces
BIND_PORT="8000"  # Internal Flask port
DOMAIN="git.chcit.org"
MAIN_SERVER="***********"
MAIN_SERVER_USER="btaylor-admin"

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check command exists
check_command() {
    if ! command -v $1 &> /dev/null; then
        log "Error: $1 is not installed"
        exit 1
    fi
}

# Install required packages
log "Installing required packages..."
apt-get update
DEBIAN_FRONTEND=noninteractive apt-get install -y 
    python${PYTHON_VERSION} 
    python${PYTHON_VERSION}-venv 
    python3-pip 
    nginx 
    git 
    supervisor 
    ufw 
    curl 
    jq

# Verify installations
check_command python${PYTHON_VERSION}
check_command nginx
check_command git
check_command supervisord
check_command ufw
check_command curl
check_command jq

# Create directory structure
log "Creating directory structure..."
mkdir -p ${INSTALL_DIR}/{logs,data/history,static,js,css}

# Copy application files
log "Copying application files..."
cp app.py index.html ${INSTALL_DIR}/
cp -r css/* ${INSTALL_DIR}/css/
cp -r js/* ${INSTALL_DIR}/js/
cp collect-metrics.sh ${INSTALL_DIR}/
cp sync-certificates.sh ${INSTALL_DIR}/

# Make scripts executable
chmod +x ${INSTALL_DIR}/collect-metrics.sh
chmod +x ${INSTALL_DIR}/sync-certificates.sh
chmod +x /usr/local/bin/sync-certificates.sh

# Set up Python virtual environment
log "Setting up Python virtual environment..."
python${PYTHON_VERSION} -m venv ${INSTALL_DIR}/venv
${INSTALL_DIR}/venv/bin/pip install --upgrade pip
${INSTALL_DIR}/venv/bin/pip install Flask==3.0.0 psutil==5.9.5 GitPython==3.1.40 gunicorn==21.2.0 Werkzeug==3.0.1 python-dotenv

# Create metrics history file
log "Creating metrics history file..."
touch ${INSTALL_DIR}/data/history/metrics_history.json
chmod 644 ${INSTALL_DIR}/data/history/metrics_history.json

# Copy certificate sync script to system location
log "Setting up certificate sync..."
cp sync-certificates.sh /usr/local/bin/
chmod +x /usr/local/bin/sync-certificates.sh

# Configure Nginx with SSL and enhanced security
log "Configuring Nginx with SSL and security settings..."
cat > ${NGINX_CONF} << EOL
# Define rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=5r/s;

server {
    # Redirect HTTP to HTTPS
    listen 80;
    server_name ${DOMAIN};
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ${DOMAIN};

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chcit.org/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/chcit.org/chain.pem;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;

    # HSTS (ngx_http_headers_module is required) (63072000 seconds = 2 years)
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' https://cdn.jsdelivr.net 'unsafe-inline'; img-src 'self' data:;" always;

    # Logging configuration
    access_log /var/log/nginx/git-dashboard.access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/git-dashboard.error.log warn;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;

    # JavaScript and CSS files
    location ~* \.(js|css)$ {
        expires 1d;
        add_header Cache-Control "public, no-transform";
        access_log off;
        gzip_static on;
    }

    # Frontend Routes
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, no-transform";
        root ${INSTALL_DIR};
        index index.html;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # API Routes (Flask backend)
    location /api {
        proxy_pass http://127.0.0.1:${BIND_PORT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Rate limiting
        limit_req zone=api_limit burst=20 nodelay;
        limit_req_status 429;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Metrics history endpoint with caching
    location /api/metrics/history {
        proxy_pass http://127.0.0.1:${BIND_PORT};
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        proxy_cache_valid 200 5m;
        add_header X-Cache-Status $upstream_cache_status;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static Files
    location /static/ {
        alias ${INSTALL_DIR}/static/;
        expires 1d;
        add_header Cache-Control "public, no-transform";
        access_log off;

        # Compression
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        gzip_min_length 1000;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root ${INSTALL_DIR}/static;
    }
}
EOL

# Create symbolic link for Nginx
ln -sf ${NGINX_CONF} /etc/nginx/sites-enabled/git-dashboard

# Configure systemd service with enhanced security and resource limits
log "Configuring systemd service with security enhancements..."
cat > ${SYSTEMD_SERVICE} << EOL
[Unit]
Description=Git Dashboard Web Interface
After=network.target nginx.service
Requires=nginx.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=${INSTALL_DIR}
Environment="FLASK_APP=app.py"
Environment="FLASK_ENV=production"
Environment="PYTHONUNBUFFERED=1"
Environment="BIND_IP=${BIND_IP}"
Environment="BIND_PORT=${BIND_PORT}"
ExecStart=${INSTALL_DIR}/venv/bin/gunicorn \
    --workers 3 \
    --bind ${BIND_IP}:${BIND_PORT} \
    --access-logfile ${INSTALL_DIR}/logs/gunicorn.access.log \
    --error-logfile ${INSTALL_DIR}/logs/gunicorn.error.log \
    --capture-output \
    --log-level info \
    app:app
Restart=always
RestartSec=3

# Security
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
RestrictNamespaces=yes
RestrictRealtime=yes

# Resource limits
LimitNOFILE=65535
CPUQuota=50%
MemoryMax=256M

[Install]
WantedBy=multi-user.target
EOL

# Set up cron job for metrics collection
log "Setting up metrics collection cron job..."
cat > /etc/cron.d/git-dashboard-metrics << EOL
# Collect metrics every minute
* * * * * root ${INSTALL_DIR}/collect-metrics.sh >> ${INSTALL_DIR}/logs/metrics.log 2>&1
EOL
chmod 644 /etc/cron.d/git-dashboard-metrics

# Set up cron job for certificate sync
log "Setting up certificate sync cron job..."
cat > /etc/cron.d/git-dashboard-certs << EOL
# Sync certificates daily at 3:00 AM
0 3 * * * root /usr/local/bin/sync-certificates.sh >> ${INSTALL_DIR}/logs/cert-sync.log 2>&1
EOL
chmod 644 /etc/cron.d/git-dashboard-certs

# Configure UFW firewall
log "Configuring firewall..."
ufw allow ssh
ufw allow http
ufw allow https
ufw --force enable

# Set correct ownership and permissions
log "Setting correct ownership and permissions..."
chown -R root:root ${INSTALL_DIR}
chmod -R 755 ${INSTALL_DIR}
find ${INSTALL_DIR} -type f -exec chmod 644 {} \;
chmod +x ${INSTALL_DIR}/collect-metrics.sh
chmod +x ${INSTALL_DIR}/sync-certificates.sh
chmod +x /usr/local/bin/sync-certificates.sh

# Create log directories
mkdir -p ${INSTALL_DIR}/logs
mkdir -p /var/log/nginx
touch /var/log/nginx/git-dashboard.access.log /var/log/nginx/git-dashboard.error.log
chmod 644 /var/log/nginx/git-dashboard.access.log /var/log/nginx/git-dashboard.error.log

# Enable and start services
log "Enabling and starting services..."
systemctl daemon-reload
systemctl enable git-dashboard.service

# Test Nginx configuration before restarting
if nginx -t; then
    systemctl restart nginx
    systemctl start git-dashboard
    log "Services started successfully!"
else
    log "Nginx configuration test failed. Please check your configuration."
    exit 1
fi

# Final setup verification
log "Verifying installation..."
if systemctl is-active --quiet git-dashboard && systemctl is-active --quiet nginx; then
    log "Git Dashboard has been installed and configured successfully!"
    log "You can access the dashboard at https://${DOMAIN}"
else
    log "There was a problem with the installation. Please check the service status."
    systemctl status git-dashboard.service
    systemctl status nginx.service
fi
