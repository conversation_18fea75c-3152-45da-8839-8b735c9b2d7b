#include <gtest/gtest.h>
#include "database-service/schema/schema_manager.hpp"
#include "database-service/core/connection_manager.hpp"
#include <memory>
#include <filesystem>
#include <fstream>

namespace dbservice::schema {

class SchemaManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a test connection manager
        connectionString_ = "host=localhost port=5432 dbname=test_db user=test_user password=test_pass";
        sslConfig_.enabled = false;
        connectionManager_ = std::make_shared<core::ConnectionManager>(connectionString_, 5, sslConfig_);
        
        // Create a temporary schema directory
        schemaDirectory_ = std::filesystem::temp_directory_path() / "test_schemas";
        std::filesystem::create_directories(schemaDirectory_);
        
        // Create test schema files
        createTestSchemaFiles();
    }

    void TearDown() override {
        // Clean up test schema directory
        if (std::filesystem::exists(schemaDirectory_)) {
            std::filesystem::remove_all(schemaDirectory_);
        }
        
        if (connectionManager_) {
            connectionManager_->shutdown();
        }
    }

    void createTestSchemaFiles() {
        // Create version 1 schema
        std::ofstream v1File(schemaDirectory_ / "001_initial_schema.sql");
        v1File << "CREATE TABLE users (\n";
        v1File << "    id SERIAL PRIMARY KEY,\n";
        v1File << "    username VARCHAR(255) NOT NULL UNIQUE,\n";
        v1File << "    email VARCHAR(255) NOT NULL,\n";
        v1File << "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n";
        v1File << ");\n";
        v1File.close();

        // Create version 2 schema
        std::ofstream v2File(schemaDirectory_ / "002_add_user_roles.sql");
        v2File << "CREATE TABLE user_roles (\n";
        v2File << "    id SERIAL PRIMARY KEY,\n";
        v2File << "    user_id INTEGER REFERENCES users(id),\n";
        v2File << "    role VARCHAR(50) NOT NULL,\n";
        v2File << "    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n";
        v2File << ");\n";
        v2File.close();

        // Create version 3 schema
        std::ofstream v3File(schemaDirectory_ / "003_add_permissions.sql");
        v3File << "CREATE TABLE permissions (\n";
        v3File << "    id SERIAL PRIMARY KEY,\n";
        v3File << "    name VARCHAR(100) NOT NULL UNIQUE,\n";
        v3File << "    description TEXT\n";
        v3File << ");\n";
        v3File << "\n";
        v3File << "CREATE TABLE role_permissions (\n";
        v3File << "    role VARCHAR(50) NOT NULL,\n";
        v3File << "    permission_id INTEGER REFERENCES permissions(id),\n";
        v3File << "    PRIMARY KEY (role, permission_id)\n";
        v3File << ");\n";
        v3File.close();
    }

    std::string connectionString_;
    core::SSLConfig sslConfig_;
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::filesystem::path schemaDirectory_;
};

TEST_F(SchemaManagerTest, ConstructorTest) {
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, schemaDirectory_.string());
    ASSERT_NE(schemaManager, nullptr);
}

TEST_F(SchemaManagerTest, InitializeTest) {
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, schemaDirectory_.string());
    
    // Initialize should not throw (may fail if no DB connection)
    EXPECT_NO_THROW(schemaManager->initialize());
}

TEST_F(SchemaManagerTest, SchemaFileDiscoveryTest) {
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, schemaDirectory_.string());
    
    // Test that schema files are discovered
    // Note: This test assumes the schema manager has methods to list schema files
    // The actual implementation may vary
    EXPECT_NO_THROW(schemaManager->initialize());
}

TEST_F(SchemaManagerTest, EmptyDirectoryTest) {
    // Create an empty directory
    auto emptyDir = std::filesystem::temp_directory_path() / "empty_schemas";
    std::filesystem::create_directories(emptyDir);
    
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, emptyDir.string());
    
    // Should handle empty directory gracefully
    EXPECT_NO_THROW(schemaManager->initialize());
    
    // Clean up
    std::filesystem::remove_all(emptyDir);
}

TEST_F(SchemaManagerTest, NonExistentDirectoryTest) {
    auto nonExistentDir = std::filesystem::temp_directory_path() / "non_existent_schemas";
    
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, nonExistentDir.string());
    
    // Should handle non-existent directory gracefully
    EXPECT_NO_THROW(schemaManager->initialize());
}

TEST_F(SchemaManagerTest, InvalidSchemaFileTest) {
    // Create an invalid schema file
    std::ofstream invalidFile(schemaDirectory_ / "999_invalid.sql");
    invalidFile << "INVALID SQL SYNTAX HERE;\n";
    invalidFile << "CREATE TABLE invalid_table (\n";
    invalidFile << "    invalid_column INVALID_TYPE\n";
    invalidFile << ";\n"; // Missing closing parenthesis
    invalidFile.close();
    
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, schemaDirectory_.string());
    
    // Should handle invalid schema files gracefully
    EXPECT_NO_THROW(schemaManager->initialize());
}

TEST_F(SchemaManagerTest, SchemaVersioningTest) {
    auto schemaManager = std::make_unique<SchemaManager>(connectionManager_, schemaDirectory_.string());
    
    // Test schema versioning functionality
    EXPECT_NO_THROW(schemaManager->initialize());
    
    // Note: Actual migration testing would require a real database connection
    // In a production test environment, you would:
    // 1. Connect to a test database
    // 2. Run migrations
    // 3. Verify schema changes
    // 4. Test rollback functionality
}

// Test schema file parsing
TEST(SchemaFileTest, SchemaFileNamingTest) {
    // Test that schema files follow the expected naming convention
    std::string filename1 = "001_initial_schema.sql";
    std::string filename2 = "002_add_user_roles.sql";
    std::string filename3 = "999_final_schema.sql";
    
    // Extract version numbers (this would be done by SchemaManager)
    EXPECT_TRUE(filename1.substr(0, 4) == "001_");
    EXPECT_TRUE(filename2.substr(0, 4) == "002_");
    EXPECT_TRUE(filename3.substr(0, 4) == "999_");

    EXPECT_TRUE(filename1.substr(filename1.length() - 4) == ".sql");
    EXPECT_TRUE(filename2.substr(filename2.length() - 4) == ".sql");
    EXPECT_TRUE(filename3.substr(filename3.length() - 4) == ".sql");
}

TEST(SchemaFileTest, VersionExtractionTest) {
    // Test version extraction from filenames
    std::string filename = "042_add_new_feature.sql";
    
    // Extract version number (simplified version of what SchemaManager would do)
    size_t underscorePos = filename.find('_');
    if (underscorePos != std::string::npos) {
        std::string versionStr = filename.substr(0, underscorePos);
        try {
            int version = std::stoi(versionStr);
            EXPECT_EQ(version, 42);
        } catch (const std::exception&) {
            FAIL() << "Failed to extract version number";
        }
    } else {
        FAIL() << "Invalid filename format";
    }
}

// Test SQL parsing utilities
TEST(SQLParsingTest, BasicSQLTest) {
    std::string sql = "CREATE TABLE test (id INTEGER PRIMARY KEY);";
    
    // Basic validation that SQL contains expected keywords
    EXPECT_TRUE(sql.find("CREATE TABLE") != std::string::npos);
    EXPECT_TRUE(sql.find("PRIMARY KEY") != std::string::npos);
    EXPECT_TRUE(sql.back() == ';');
}

TEST(SQLParsingTest, MultiStatementSQLTest) {
    std::string sql = R"(
        CREATE TABLE users (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255)
        );
        
        CREATE INDEX idx_users_name ON users(name);
        
        INSERT INTO users (name) VALUES ('Test User');
    )";
    
    // Count statements (simplified)
    size_t semicolonCount = 0;
    for (char c : sql) {
        if (c == ';') semicolonCount++;
    }
    
    EXPECT_EQ(semicolonCount, 3);
}

} // namespace dbservice::schema
