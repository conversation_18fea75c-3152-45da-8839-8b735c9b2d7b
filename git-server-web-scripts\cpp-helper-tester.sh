#!/bin/bash
# C++ Helper Test Script
# This script is used to test the C++ helper application independently
# Run as a www-data user or another user in the www-data group

# Color codes for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

HELPER_PATH="/opt/git-dashboard/bin/cert_sync_helper"

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}   C++ Certificate Helper Test Script   ${NC}"
echo -e "${BLUE}=========================================${NC}"

# Check if the helper exists
if [ ! -f "$HELPER_PATH" ]; then
    echo -e "${RED}Error: C++ helper not found at $HELPER_PATH${NC}"
    echo "Please run the cpp-helper-installer.sh script first."
    exit 1
fi

# Check permissions
PERMS=$(stat -c "%a" "$HELPER_PATH")
OWNER=$(stat -c "%U:%G" "$HELPER_PATH")
echo -e "Helper permissions: ${YELLOW}$PERMS${NC} (should be 4750)"
echo -e "Helper owner: ${YELLOW}$OWNER${NC} (should be root:www-data)"

# Check if running as www-data or in www-data group
IS_WWW_DATA=0
if [ "$(id -u -n)" = "www-data" ]; then
    IS_WWW_DATA=1
    echo -e "${GREEN}Running as www-data user. Good!${NC}"
else
    # Check if the user is in the www-data group
    GROUPS=$(id -Gn)
    if [[ $GROUPS == *"www-data"* ]]; then
        IS_WWW_DATA=1
        echo -e "${GREEN}User is in www-data group. Good!${NC}"
    else
        echo -e "${RED}Warning: Not running as www-data or in www-data group.${NC}"
        echo "The helper may not run properly. Consider running this script as:"
        echo "  sudo -u www-data $0"
        echo ""
        echo -e "${YELLOW}Attempting to run anyway for testing purposes...${NC}"
    fi
fi

# Function to execute the helper with various options
run_helper() {
    local description=$1
    local expected_result=$2
    
    echo ""
    echo -e "${BLUE}Test: $description${NC}"
    echo -e "${YELLOW}Expected result: $expected_result${NC}"
    echo -e "${YELLOW}Command: $HELPER_PATH${NC}"
    echo -e "${BLUE}----- Output Start -----${NC}"
    
    # Run the helper and capture its output and exit code
    OUTPUT=$("$HELPER_PATH" 2>&1)
    EXIT_CODE=$?
    
    echo "$OUTPUT"
    echo -e "${BLUE}----- Output End -----${NC}"
    echo -e "Exit code: ${YELLOW}$EXIT_CODE${NC}"
    
    # Check if exit code matches expected
    if [ $EXIT_CODE -eq 0 ]; then
        echo -e "${GREEN}Test PASSED: Helper executed successfully${NC}"
    else
        echo -e "${RED}Test FAILED: Helper execution failed${NC}"
    fi
    
    # Try to parse the JSON output
    if [[ "$OUTPUT" == *"Content-Type: application/json"* ]]; then
        echo -e "${GREEN}Helper returned proper JSON headers${NC}"
        
        # Extract just the JSON part (remove headers)
        JSON_PART=$(echo "$OUTPUT" | awk '/^{/,/^}/')
        
        # Validate JSON if jq is available
        if command -v jq &> /dev/null; then
            if echo "$JSON_PART" | jq . > /dev/null 2>&1; then
                echo -e "${GREEN}JSON output is valid${NC}"
                echo "JSON structure:"
                echo "$JSON_PART" | jq .
            else
                echo -e "${RED}JSON output is invalid${NC}"
            fi
        else
            echo -e "${YELLOW}jq not installed, skipping JSON validation${NC}"
            echo "Raw JSON output:"
            echo "$JSON_PART"
        fi
    else
        echo -e "${RED}Helper did not return proper JSON response${NC}"
    fi
}

# Run the tests
echo ""
echo -e "${BLUE}Running basic execution test...${NC}"
run_helper "Basic execution" "Should execute successfully if permissions are correct"

# Check logs
echo ""
echo -e "${BLUE}Checking syslog for helper logs...${NC}"
if command -v grep &> /dev/null; then
    echo "Searching for recent log entries from cert_sync_helper:"
    sudo grep "cert_sync_helper" /var/log/syslog | tail -5
else
    echo -e "${YELLOW}grep not available, please check logs manually:${NC}"
    echo "sudo grep cert_sync_helper /var/log/syslog"
fi

# Check sync script
echo ""
echo -e "${BLUE}Checking sync script...${NC}"
SYNC_SCRIPT="/opt/git-dashboard/sync-certificates.sh"
if [ -f "$SYNC_SCRIPT" ]; then
    SYNC_PERMS=$(stat -c "%a" "$SYNC_SCRIPT")
    SYNC_OWNER=$(stat -c "%U:%G" "$SYNC_SCRIPT")
    echo -e "Sync script permissions: ${YELLOW}$SYNC_PERMS${NC} (should be 755)"
    echo -e "Sync script owner: ${YELLOW}$SYNC_OWNER${NC} (should be root:root)"
    
    # Check if the script is executable
    if [ -x "$SYNC_SCRIPT" ]; then
        echo -e "${GREEN}Sync script is executable. Good!${NC}"
    else
        echo -e "${RED}Warning: Sync script is not executable.${NC}"
        echo "Fix with: sudo chmod +x $SYNC_SCRIPT"
    fi
else
    echo -e "${RED}Sync script not found at $SYNC_SCRIPT${NC}"
fi

# Final summary
echo ""
echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}          Test Summary                  ${NC}"
echo -e "${BLUE}=========================================${NC}"
echo "The C++ helper test has completed."
if [ $IS_WWW_DATA -eq 1 ]; then
    echo -e "${GREEN}You have proper permissions to run the helper.${NC}"
else
    echo -e "${YELLOW}You may not have proper permissions to run the helper.${NC}"
    echo "Consider running as www-data user for proper testing."
fi

echo ""
echo "To debug further:"
echo "1. Check logs: sudo tail -f /var/log/syslog | grep cert_sync_helper"
echo "2. Verify helper permissions: ls -la $HELPER_PATH"
echo "3. Verify sync script: ls -la $SYNC_SCRIPT"
echo "4. Test the sync script directly: sudo $SYNC_SCRIPT"
echo ""
echo -e "${GREEN}Test completed!${NC}"
