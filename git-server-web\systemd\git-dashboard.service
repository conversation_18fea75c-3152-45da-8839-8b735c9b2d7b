[Unit]
Description=Git Dashboard Web Interface
After=network.target nginx.service git-repo-logging.service
Requires=nginx.service
Wants=git-repo-logging.service

[Service]
Type=simple
User=git
Group=git
WorkingDirectory=/opt/git-dashboard
Environment=FLASK_APP=app.py
Environment=FLASK_ENV=production
Environment=PYTHONUNBUFFERED=1
ExecStart=/usr/bin/python3 -m flask run --host=127.0.0.1 --port=8000
Restart=always
RestartSec=3

# Security
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
RestrictNamespaces=yes
RestrictRealtime=yes

# Resource limits
LimitNOFILE=65535
CPUQuota=50%
MemoryMax=256M

[Install]
WantedBy=multi-user.target
