# Install Dependencies Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Install dependencies function
function Install-Dependencies {
    Clear-Host
    Write-Log -Message "========== Install Dependencies ==========" -Level "Info" -Component "Dependencies"
    Write-Log -Message "               Install Dependencies                    " -Level "Info" -Component "Dependencies"
    Write-Log -Message "========== Install Dependencies ==========" -Level "Info" -Component "Dependencies"
    Write-Log -Message "" -Level "Info" -Component "Dependencies"
    
    # Load configuration if not already loaded
    if ($null -eq $script:Config -or $null -eq $script:Config.ssh) {
        # Try to load configuration from Common module first
        try {
            $commonModule = Get-Module -Name "Common"
            if ($null -ne $commonModule -and $null -ne $commonModule.SessionState.PSVariable.Get("Config")) {
                $script:Config = $commonModule.SessionState.PSVariable.Get("Config").Value
                Write-Log -Message "Loaded configuration from Common module" -Level "Info" -Component "Dependencies"
            }
        } catch {
            Write-Log -Message "Error loading configuration from Common module: $_" -Level "Verbose" -Component "Dependencies"
        }
        
        # If still no config, try to load directly from file
        if ($null -eq $script:Config -or $null -eq $script:Config.ssh) {
            # Look for configuration files
            $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
            $configDir = [System.IO.Path]::GetFullPath($configDir)  # Normalize path
            
            Write-Log -Message "Searching for configuration files in: $configDir" -Level "Info" -Component "Dependencies"
            
            if (Test-Path -Path $configDir) {
                $configFiles = @(Get-ChildItem -Path $configDir -Filter "*.json" | Select-Object -ExpandProperty FullName)
                
                if ($configFiles.Count -gt 0) {
                    # Use the first configuration file found
                    $configFile = $configFiles[0]
                    Write-Log -Message "Loading configuration from: $configFile" -Level "Info" -Component "Dependencies"
                    
                    # Load the configuration file directly
                    try {
                        if (Test-Path -Path $configFile) {
                            $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                            
                            # Convert from JSON to PowerShell hashtable
                            $script:Config = @{}
                            $configContent.PSObject.Properties | ForEach-Object {
                                $name = $_.Name
                                $value = $_.Value
                                
                                if ($value -is [System.Management.Automation.PSCustomObject]) {
                                    $valueHash = @{}
                                    $value.PSObject.Properties | ForEach-Object {
                                        $valueHash[$_.Name] = $_.Value
                                    }
                                    $script:Config[$name] = $valueHash
                                } else {
                                    $script:Config[$name] = $value
                                }
                            }
                            
                            # Make sure the configuration is available in the Common module
                            & {
                                # Get the Common module
                                $commonModule = Get-Module -Name "Common"
                                if ($null -ne $commonModule) {
                                    # Export the Config variable to the Common module
                                    $commonModule.SessionState.PSVariable.Set("Config", $script:Config)
                                }
                            }
                            
                            $result = $true
                        } else {
                            Write-Log -Message "Configuration file not found: $configFile" -Level "Error" -Component "Dependencies"
                            $result = $false
                        }
                    } catch {
                        Write-Log -Message "Error loading configuration: $_" -Level "Error" -Component "Dependencies"
                        $result = $false
                    }
                    
                    if (-not $result) {
                        Write-Log -Message "Failed to load configuration. Please configure SSH settings first." -Level "Error" -Component "Dependencies"
                        Wait-ForUser
                        return
                    }
                } else {
                    Write-Log -Message "No configuration files found in $configDir" -Level "Error" -Component "Dependencies"
                    Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Dependencies"
                    Wait-ForUser
                    return
                }
            } else {
                Write-Log -Message "Configuration directory not found: $configDir" -Level "Error" -Component "Dependencies"
                Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Dependencies"
                Wait-ForUser
                return
            }
        }
    }
    
    # Check for requirements.json in the deployment directory instead of source code directory
    $deploymentDir = "D:/Augment/project-tracker/database-service-deployment"
    $requirementsPath = Join-Path -Path $deploymentDir -ChildPath "requirements.json"
    
    if (-not (Test-Path -Path $requirementsPath)) {
        Write-Log -Message "Requirements file not found at: $requirementsPath" -Level "Error" -Component "Dependencies"
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        
        Write-Log -Message "What would you like to do?" -Level "Info" -Component "Dependencies"
        Write-Log -Message "[1] Search for requirements.json in source code directory" -Level "Info" -Component "Dependencies"
        Write-Log -Message "[2] Scan source code for dependencies" -Level "Info" -Component "Dependencies"
        Write-Log -Message "[3] Return to main menu" -Level "Info" -Component "Dependencies"
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        
        $choice = Read-Host "Select an option (1-3)"
        
        switch ($choice) {
            "1" {
                # Search for requirements.json files
                Write-Log -Message "Searching for requirements.json files..." -Level "Info" -Component "Dependencies"
                $sourceDir = $script:Config.project.source_dir
                $requirementsFiles = Get-ChildItem -Path $sourceDir -Filter "requirements.json" -Recurse | Select-Object -ExpandProperty FullName
                
                if ($requirementsFiles.Count -gt 0) {
                    Write-Log -Message "Found $($requirementsFiles.Count) requirements.json files:" -Level "Info" -Component "Dependencies"
                    
                    for ($i = 0; $i -lt $requirementsFiles.Count; $i++) {
                        Write-Log -Message "[$($i+1)] $($requirementsFiles[$i])" -Level "Info" -Component "Dependencies"
                    }
                    
                    Write-Log -Message "" -Level "Info" -Component "Dependencies"
                    $fileChoice = Read-Host "Select a requirements file (1-$($requirementsFiles.Count))"
                    
                    if ($fileChoice -match "^\d+$" -and [int]$fileChoice -ge 1 -and [int]$fileChoice -le $requirementsFiles.Count) {
                        $requirementsPath = $requirementsFiles[[int]$fileChoice - 1]
                        Write-Log -Message "Using requirements file: $requirementsPath" -Level "Info" -Component "Dependencies"
                    } else {
                        Write-Log -Message "Invalid selection. Returning to main menu." -Level "Error" -Component "Dependencies"
                        Show-MainMenu
                        return
                    }
                } else {
                    Write-Log -Message "No requirements.json files found in the source code directory." -Level "Error" -Component "Dependencies"
                    Show-MainMenu
                    return
                }
            }
            "2" {
                # Scan source code for dependencies (placeholder)
                Write-Log -Message "Scanning source code for dependencies..." -Level "Info" -Component "Dependencies"
                Write-Log -Message "This feature is not implemented yet." -Level "Info" -Component "Dependencies"
                Show-MainMenu
                return
            }
            "3" {
                Show-MainMenu
                return
            }
            default {
                Write-Log -Message "Returning to main menu." -Level "Info" -Component "Dependencies"
                Show-MainMenu
                return
            }
        }
    }
    
    # Load requirements from JSON
    try {
        $requirementsJson = Get-Content -Path $requirementsPath -Raw | ConvertFrom-Json
        
        # Extract system packages from the structured requirements.json
        $systemPackages = @()
        
        # Add compiler packages
        if ($null -ne $requirementsJson.compiler) {
            foreach ($compiler in $requirementsJson.compiler.PSObject.Properties) {
                if ($compiler.Value.package) {
                    $systemPackages += $compiler.Value.package
                }
            }
        }
        
        # Add build tools packages
        if ($null -ne $requirementsJson.build_tools) {
            foreach ($tool in $requirementsJson.build_tools.PSObject.Properties) {
                if ($tool.Value.package) {
                    $systemPackages += $tool.Value.package
                }
            }
        }
        
        # Add library packages
        if ($null -ne $requirementsJson.libraries) {
            foreach ($lib in $requirementsJson.libraries.PSObject.Properties) {
                if ($lib.Value.package) {
                    $systemPackages += $lib.Value.package
                }
            }
        }
        
        # Add system packages
        if ($null -ne $requirementsJson.system) {
            foreach ($sys in $requirementsJson.system.PSObject.Properties) {
                if ($sys.Value.package) {
                    $systemPackages += $sys.Value.package
                }
            }
        }
        
        # Define default system packages if not found in requirements.json
        if ($null -eq $systemPackages -or $systemPackages.Count -eq 0) {
            $systemPackages = @(
                "g++-14",
                "cmake",
                "build-essential",
                "libboost-all-dev",
                "libpq-dev",
                "libpqxx-dev",
                "libssl-dev",
                "nlohmann-json3-dev",
                "postgresql"
            )
            
            Write-Log -Message "Using default system packages: $($systemPackages -join ', ')" -Level "Info" -Component "Dependencies"
        } else {
            $packageCount = $systemPackages.Count
            Write-Log -Message "Found $packageCount system packages in requirements.json." -Level "Info" -Component "Dependencies"
        }
        
        # Confirm installation
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        Write-Log -Message "The following packages will be installed:" -Level "Info" -Component "Dependencies"
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        
        Write-Log -Message "System packages:" -Level "Info" -Component "Dependencies"
        foreach ($package in $systemPackages) {
            Write-Log -Message "  - $package" -Level "Info" -Component "Dependencies"
        }
        
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        $confirm = Read-Host "Do you want to install these packages? (y/n)"
        
        if ($confirm -ne "y") {
            Write-Log -Message "Installation cancelled." -Level "Info" -Component "Dependencies"
            Show-MainMenu
            return
        }
        
        # Install system packages
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        Write-Log -Message "Installing system packages..." -Level "Info" -Component "Dependencies"
        
        $installCmd = "sudo apt-get update && sudo apt-get install -y " + ($systemPackages -join " ")
        $result = Invoke-RemoteCommand -Command $installCmd
        
        Write-Log -Message $result -Level "Info" -Component "Dependencies"
        
        # Check if PostgreSQL is running
        $checkPgCmd = "systemctl is-active postgresql"
        $pgStatus = Invoke-RemoteCommand -Command $checkPgCmd -Silent
        
        if ($pgStatus -ne "active") {
            Write-Log -Message "Starting PostgreSQL service..." -Level "Info" -Component "Dependencies"
            $startPgCmd = "sudo systemctl start postgresql"
            Invoke-RemoteCommand -Command $startPgCmd -Silent
        }
        
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        Write-Log -Message "Dependencies installed successfully!" -Level "Info" -Component "Dependencies"
        
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        Write-Log -Message "Press Enter to return to the main menu..." -Level "Info" -Component "Dependencies"
        Wait-ForUser
    } catch {
        Write-Log -Message "Error installing dependencies: $_" -Level "Error" -Component "Dependencies"
        
        Write-Log -Message "" -Level "Info" -Component "Dependencies"
        Write-Log -Message "Press Enter to return to the main menu..." -Level "Info" -Component "Dependencies"
        Wait-ForUser
    }
    
    Show-MainMenu
}

# Call the function when this script is executed directly
Install-Dependencies
