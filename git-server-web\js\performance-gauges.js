/**
 * Performance Gauges Module - Horizontal Progress Bars Style
 * Renders modern animated horizontal progress bars for server performance metrics
 */
const PerformanceGauges = {
    // Configuration
    config: {
        // Thresholds for color changes
        thresholds: {
            warning: 60, // Warning starts at 60%
            critical: 80 // Critical starts at 80%
        },
        // Colors for status levels
        colors: {
            normal: "#22c55e",   // Bright green
            warning: "#f59e0b",  // Bright yellow
            critical: "#ef4444"  // Bright red
        },
        // Animation duration in ms
        animationDuration: 1000
    },
    
    // Application state
    state: {
        metrics: {
            cpu: 0,
            memory: 0,
            disk: 0,
            uptime: "Unknown"
        },
        isInitialized: false
    },
    
    /**
     * Initialize the Performance Gauges
     */
    init() {
        console.log('Initializing Performance Gauges (Horizontal Bars)...');
        
        if (this.state.isInitialized) {
            console.warn('Performance Gauges already initialized');
            return this;
        }
        
        // Get container element
        const container = document.getElementById('server-performance-container');
        if (!container) {
            console.error('Performance Gauges container not found');
            return this;
        }
        
        // Create horizontal bars HTML structure
        this.createHorizontalBars(container);
        
        // Add CSS styles
        this.addStyles();
        
        // Set initialization state
        this.state.isInitialized = true;
        
        // Log initialization
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Performance Gauges initialized with horizontal bars', 'metrics');
        }
        
        return this;
    },
    
    /**
     * Add required CSS styles
     */
    addStyles() {
        // Check if styles are already added
        if (document.getElementById('performance-gauges-styles')) {
            return;
        }
        
        // Create style element
        const style = document.createElement('style');
        style.id = 'performance-gauges-styles';
        style.textContent = `
            .metrics-container {
                background-color: #1e293b;
                border-radius: 0.5rem;
                padding: 1.5rem;
                color: white;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            
            .metric-row {
                margin-bottom: 1.5rem;
            }
            
            .metric-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.25rem;
                align-items: center;
            }
            
            .metric-name {
                color: #cbd5e1;
                font-weight: 500;
            }
            
            .metric-value {
                font-size: 1.25rem;
                font-weight: 700;
            }
            
            .progress-container {
                height: 1rem;
                background-color: #334155;
                border-radius: 9999px;
                overflow: hidden;
            }
            
            .progress-bar {
                height: 100%;
                border-radius: 9999px;
                transition: width ${this.config.animationDuration}ms ease-out, background-color 500ms ease;
            }
            
            .progress-bar.normal {
                background-color: ${this.config.colors.normal};
                box-shadow: 0 0 10px ${this.config.colors.normal};
            }
            
            .progress-bar.warning {
                background-color: ${this.config.colors.warning};
                box-shadow: 0 0 10px ${this.config.colors.warning};
            }
            
            .progress-bar.critical {
                background-color: ${this.config.colors.critical};
                box-shadow: 0 0 10px ${this.config.colors.critical};
            }
            
            .uptime-display {
                margin-top: 2rem;
                background-color: #334155;
                border-radius: 0.5rem;
                padding: 1rem;
                display: flex;
                align-items: center;
                border-left: 4px solid #3b82f6;
            }
            
            .uptime-icon {
                margin-right: 1rem;
                color: #3b82f6;
                font-size: 1.5rem;
            }
            
            .uptime-info {
                flex: 1;
            }
            
            .uptime-label {
                color: #94a3b8;
                font-size: 0.875rem;
            }
            
            .uptime-value {
                font-size: 1.25rem;
                font-family: monospace;
            }
            
            .system-insight {
                margin-top: 1.5rem;
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                font-size: 0.875rem;
                border-left-width: 4px;
                display: flex;
                align-items: flex-start;
            }
            
            .system-insight-icon {
                margin-right: 0.5rem;
                margin-top: 0.125rem;
            }
            
            .system-insight.normal {
                background-color: rgba(34, 197, 94, 0.1);
                border-left-color: ${this.config.colors.normal};
            }
            
            .system-insight.normal .system-insight-icon {
                color: ${this.config.colors.normal};
            }
            
            .system-insight.warning {
                background-color: rgba(245, 158, 11, 0.1);
                border-left-color: ${this.config.colors.warning};
            }
            
            .system-insight.warning .system-insight-icon {
                color: ${this.config.colors.warning};
            }
            
            .system-insight.critical {
                background-color: rgba(239, 68, 68, 0.1);
                border-left-color: ${this.config.colors.critical};
            }
            
            .system-insight.critical .system-insight-icon {
                color: ${this.config.colors.critical};
            }
        `;
        
        // Add to document head
        document.head.appendChild(style);
    },
    
    /**
     * Create horizontal bars HTML structure
     * @param {HTMLElement} container - Container element
     */
    createHorizontalBars(container) {
        // Create wrapper div (without the redundant header)
        const wrapper = document.createElement('div');
        wrapper.className = 'metrics-container';
        
        // Create CPU metric
        const cpuMetric = this.createMetricRow('cpu', 'CPU Usage');
        
        // Create memory metric
        const memoryMetric = this.createMetricRow('memory', 'Memory Usage');
        
        // Create disk metric
        const diskMetric = this.createMetricRow('disk', 'Disk Usage');
        
        // Create uptime display
        const uptimeDisplay = document.createElement('div');
        uptimeDisplay.className = 'uptime-display';
        uptimeDisplay.innerHTML = `
            <div class="uptime-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="uptime-info">
                <div class="uptime-label">Server Uptime</div>
                <div id="uptime-value" class="uptime-value">--</div>
            </div>
        `;
        
        // Create system insight
        const systemInsight = document.createElement('div');
        systemInsight.id = 'system-insight';
        systemInsight.className = 'system-insight normal';
        systemInsight.innerHTML = `
            <div class="system-insight-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div id="insight-text">
                All systems operating normally.
            </div>
        `;
        
        // Assemble all elements (without the title element)
        wrapper.appendChild(cpuMetric);
        wrapper.appendChild(memoryMetric);
        wrapper.appendChild(diskMetric);
        wrapper.appendChild(uptimeDisplay);
        wrapper.appendChild(systemInsight);
        
        // Clear container and add wrapper
        container.innerHTML = '';
        container.appendChild(wrapper);
    },
    
    /**
     * Create a metric row with name, value, and progress bar
     * @param {string} id - Metric ID (cpu, memory, disk)
     * @param {string} name - Display name
     * @returns {HTMLElement} - Metric row element
     */
    createMetricRow(id, name) {
        const row = document.createElement('div');
        row.className = 'metric-row';
        
        row.innerHTML = `
            <div class="metric-header">
                <div class="metric-name">${name}</div>
                <div id="${id}-value" class="metric-value">0%</div>
            </div>
            <div class="progress-container">
                <div id="${id}-progress" class="progress-bar normal" style="width: 0%"></div>
            </div>
        `;
        
        return row;
    },
    
    /**
     * Update metrics display with new values
     * @param {Object} metrics - Metrics object with cpu, memory, disk, and uptime
     */
    updateGauges(metrics) {
        // Make sure we're initialized
        if (!this.state.isInitialized) {
            console.warn('Performance Gauges not initialized');
            return;
        }
        
        // Update state
        this.state.metrics = metrics;
        
        // Update each metric bar
        this.updateMetricBar('cpu', metrics.cpu);
        this.updateMetricBar('memory', metrics.memory);
        this.updateMetricBar('disk', metrics.disk);
        
        // Update uptime
        const uptimeEl = document.getElementById('uptime-value');
        if (uptimeEl) {
            uptimeEl.textContent = metrics.uptime || 'Unknown';
        }
        
        // Update system status
        this.updateSystemStatus();
    },
    
    /**
     * Update a specific metric bar
     * @param {string} id - Metric ID (cpu, memory, disk)
     * @param {number} value - Value (0-100)
     */
    updateMetricBar(id, value) {
        // Ensure value is within 0-100 range and a number
        value = Math.max(0, Math.min(100, parseFloat(value) || 0));
        
        // Get elements
        const valueEl = document.getElementById(`${id}-value`);
        const progressEl = document.getElementById(`${id}-progress`);
        
        if (!valueEl || !progressEl) {
            console.warn(`Elements for ${id} metric not found`);
            return;
        }
        
        // Update value display
        valueEl.textContent = `${value}%`;
        
        // Update progress bar width
        progressEl.style.width = `${value}%`;
        
        // Update status class
        progressEl.classList.remove('normal', 'warning', 'critical');
        if (value >= this.config.thresholds.critical) {
            progressEl.classList.add('critical');
        } else if (value >= this.config.thresholds.warning) {
            progressEl.classList.add('warning');
        } else {
            progressEl.classList.add('normal');
        }
    },
    
    /**
     * Update system status indicators and message
     */
    updateSystemStatus() {
        const { cpu, memory, disk } = this.state.metrics;
        
        // Elements
        const insightEl = document.getElementById('system-insight');
        const insightIconEl = insightEl ? insightEl.querySelector('.system-insight-icon i') : null;
        const insightTextEl = document.getElementById('insight-text');
        const statusIndicatorEl = document.getElementById('system-status-indicator');
        
        if (!insightEl || !insightIconEl || !insightTextEl) {
            console.warn('System insight elements not found');
            return;
        }
        
        // Determine overall status
        let statusClass, statusIcon, statusText;
        
        if (cpu >= this.config.thresholds.critical || memory >= this.config.thresholds.critical || disk >= this.config.thresholds.critical) {
            statusClass = 'critical';
            statusIcon = 'exclamation-circle';
            statusText = cpu >= this.config.thresholds.critical ? 'Critical: High CPU usage detected!' :
                         memory >= this.config.thresholds.critical ? 'Critical: Memory pressure is high!' :
                         'Critical: Disk space is nearly full!';
        } else if (cpu >= this.config.thresholds.warning || memory >= this.config.thresholds.warning || disk >= this.config.thresholds.warning) {
            statusClass = 'warning';
            statusIcon = 'exclamation-triangle';
            statusText = cpu >= this.config.thresholds.warning ? 'Warning: CPU usage is elevated' :
                         memory >= this.config.thresholds.warning ? 'Warning: Memory usage is elevated' :
                         'Warning: Disk usage is elevated';
        } else {
            statusClass = 'normal';
            statusIcon = 'check-circle';
            statusText = 'All systems operating normally';
        }
        
        // Update elements
        insightEl.className = `system-insight ${statusClass}`;
        insightIconEl.className = `fas fa-${statusIcon}`;
        insightTextEl.textContent = statusText;
        
        // Update status indicator if it exists
        if (statusIndicatorEl) {
            if (statusClass === 'critical') {
                statusIndicatorEl.style.backgroundColor = this.config.colors.critical;
            } else if (statusClass === 'warning') {
                statusIndicatorEl.style.backgroundColor = this.config.colors.warning;
            } else {
                statusIndicatorEl.style.backgroundColor = this.config.colors.normal;
            }
        }
    }
};

// Expose the module globally
window.PerformanceGauges = PerformanceGauges;
