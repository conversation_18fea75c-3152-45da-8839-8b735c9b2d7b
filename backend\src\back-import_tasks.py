import psycopg2
import re
from pathlib import Path
from datetime import datetime

def parse_markdown_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    tasks = []
    current_category = None
    current_task = None

    for line in content.split('\n'):
        # Category detection (lines starting with ###)
        if line.startswith('###'):
            current_category = line.strip('# ').split('(')[0].strip()
            continue

        # Task detection (lines starting with ####)
        if line.startswith('####'):
            if current_task:
                tasks.append(current_task)
            current_task = {
                'category': current_category,
                'title': line.strip('# ').split('(')[0].strip(),
                'description': '',
                'subtasks': [],
                'priority': 'Medium',
                'status': 'Pending'
            }
            
            # Extract date if present
            date_match = re.search(r'\((\d{4}-\d{2}-\d{2})\)', line)
            if date_match:
                current_task['start_date'] = date_match.group(1)
            
            continue

        # Priority detection
        if current_task and 'Priority:' in line:
            current_task['priority'] = line.split(':')[1].strip()
            continue

        # Subtask detection (lines starting with -)
        if current_task and line.strip().startswith('-'):
            subtask = line.strip('- ').strip()
            if subtask:
                # Check if subtask is completed
                if subtask.startswith('[x]'):
                    status = True
                    subtask = subtask[3:].strip()
                elif subtask.startswith('[ ]'):
                    status = False
                    subtask = subtask[3:].strip()
                else:
                    status = False
                
                current_task['subtasks'].append({
                    'description': subtask,
                    'completed': status
                })
            continue

        # Description accumulation
        if current_task and line.strip() and not line.startswith('#'):
            current_task['description'] += line.strip() + '\n'

    # Add the last task if exists
    if current_task:
        tasks.append(current_task)

    return tasks

def import_to_database(tasks, db_path, db_user, db_password, db_name):
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_path
    )
    cursor = conn.cursor()

    for task in tasks:
        # Insert or get category
        cursor.execute('''
            INSERT INTO categories (name)
            VALUES (%s)
            ON CONFLICT (name) DO NOTHING
        ''', (task['category'],))
        
        cursor.execute('SELECT id FROM categories WHERE name = %s', 
                      (task['category'],))
        category_id = cursor.fetchone()[0]

        # Insert task
        cursor.execute('''
            INSERT INTO tasks (
                category_id, title, description, priority, 
                status, start_date, created_at
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        ''', (
            category_id,
            task['title'],
            task['description'].strip(),
            task['priority'],
            task['status'],
            task.get('start_date', datetime.now().strftime('%Y-%m-%d')),
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))
        
        task_id = cursor.lastrowid

        # Insert subtasks
        for subtask in task['subtasks']:
            cursor.execute('''
                INSERT INTO subtasks (
                    task_id, description, completed, 
                    created_at, completed_at
                )
                VALUES (%s, %s, %s, %s, %s)
            ''', (
                task_id,
                subtask['description'],
                subtask['completed'],
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S') if subtask['completed'] else None
            ))

    conn.commit()
    conn.close()

if __name__ == '__main__':
    project_root = Path(__file__).parent.parent.parent
    markdown_file = project_root / 'docs' / 'PROJECT_IMPROVEMENTS_TRACKER.md'
    db_path = 'localhost'
    db_user = 'your_username'
    db_password = 'your_password'
    db_name = 'your_database'

    print(f"Importing tasks from {markdown_file} to {db_name}")
    tasks = parse_markdown_file(markdown_file)
    import_to_database(tasks, db_path, db_user, db_password, db_name)
    print(f"Successfully imported {len(tasks)} tasks")
