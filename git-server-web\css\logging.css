/* logging.css - Simplified for Logs Tab Integration */

/* Log Content Styling */
#dashboard-log-content {
    padding: 0.5rem;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.85rem;
    background-color: #fff;
    flex: 1;
    height: auto;
    min-height: 0;
}

/* Log Entry Styling */
.log-entry {
    padding: 4px 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
}

.log-entry:hover {
    background-color: rgba(0,0,0,0.02);
}

/* Log Content Wrapper */
.log-content-wrapper {
    display: flex;
    flex-wrap: nowrap; /* Prevent wrapping to keep items on one line */
    flex: 1;
    padding-right: 10px;
    max-width: calc(100% - 40px); /* Leave space for log number */
    overflow: hidden; /* Prevent overflow */
    align-items: center; /* Vertically center items */
}

/* Log message styling */
.log-message {
    text-overflow: ellipsis; /* Add ellipsis for truncated text */
    white-space: nowrap;     /* Keep text on one line */
    overflow: hidden;        /* Hide overflow */
    flex: 1;                 /* Take up remaining space */
    margin-top: 0;           /* Remove top margin */
}

/* Timestamp styling */
.log-timestamp {
    white-space: nowrap;
    font-size: 0.75rem;
    color: #6c757d;
    margin-right: 8px;
    min-width: 140px; /* Give timestamp a fixed width for alignment */
    flex-shrink: 0;   /* Prevent timestamp from shrinking */
}

/* Log type badge styling */
.log-type {
    white-space: nowrap;
    margin-right: 8px;
    flex-shrink: 0;   /* Prevent badge from shrinking */
    min-width: 70px;  /* Give type badge a minimum width */
}

/* Log Number Styling */
.log-number {
    font-size: 0.75rem;
    min-width: 32px;
    text-align: center;
    background-color: #6c757d;
    color: white;
    margin-left: 8px;
    flex-shrink: 0;   /* Prevent number from shrinking */
    position: relative;
    right: 0;
}

/* Log type colors */
.log-entry.text-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.log-entry.text-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.log-entry.text-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.log-entry.text-info {
    background-color: rgba(23, 162, 184, 0.1);
}

/* Scrollbar styling for logs */
#dashboard-log-content::-webkit-scrollbar {
    width: 6px;
}

#dashboard-log-content::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.2);
    border-radius: 3px;
}

#dashboard-log-content::-webkit-scrollbar-track {
    background-color: rgba(0,0,0,0.05);
}

/* Header controls styling - UPDATED: wider dropdowns */
.log-count-control, .log-level-control {
    display: flex;
    align-items: center;
}

#log-count-selector, #log-level-selector {
    width: auto;
    min-width: 140px;  /* INCREASED: Wider dropdown to prevent text/icon overlap */
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 3px;
    z-index: 100; /* Ensure dropdowns appear above other content */
    padding-right: 24px; /* Extra padding on right side for dropdown arrow */
}

/* Make the level selector even wider since it has longer text options */
#log-level-selector {
    min-width: 160px;  /* INCREASED: Even wider for "All Levels" and other options */
}

/* Make logs tab pane consistent with other tabs */
#logs {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    margin-top: 0; /* Ensure no top margin */
}

#logs .card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0;
    height: 100%;
}

#logs .card-body {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* Remove any absolute positioning or fixed heights */
#logs .card-header {
    position: static;
    width: auto;
}

/* Log Settings Modal Styles */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    font-style: italic;
    color: #6c757d;
}

.error {
    padding: 15px;
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 15px;
}

.no-logs {
    padding: 15px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #logs .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    #logs .card-header > div:last-child {
        margin-top: 10px;
        width: 100%;
        justify-content: space-between;
    }

    .log-count-control, .log-level-control {
        margin-bottom: 0;
    }

    .log-settings-container {
        flex-direction: column;
    }

    .log-settings-sidebar {
        flex: none;
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 15px;
        margin-bottom: 15px;
    }
}