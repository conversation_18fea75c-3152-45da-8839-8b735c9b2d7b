# Logging Service Health Monitoring

This document describes the health monitoring capabilities of the C++23 Logging Service.

## Overview

The Logging Service includes comprehensive health monitoring features that provide visibility into the operational status of the service. These features enable proactive monitoring, troubleshooting, and automated recovery.

## Health Check Endpoint

### Endpoint Details

- **URL**: `/health`
- **Method**: GET
- **Content Type**: application/json
- **Authentication**: None (designed for internal monitoring)

### Response Format

The health check endpoint returns a JSON response with the following structure:

```json
{
  "status": "ok",
  "version": "1.0.0",
  "timestamp": "**********",
  "storage": {
    "database": "connected",
    "database_circuit": "CLOSED"
  },
  "uptime_seconds": 3600
}
```

### Response Fields

| Field | Description |
|-------|-------------|
| `status` | Overall service status: "ok" or "error" |
| `version` | Service version number |
| `timestamp` | Current server timestamp (Unix epoch) |
| `storage.database` | Database connection status: "connected" or "disconnected" |
| `storage.database_circuit` | Circuit breaker state: "CLOSED", "OPEN", or "HALF-OPEN" |
| `uptime_seconds` | Service uptime in seconds |

### Status Codes

- **200 OK**: Service is running and the health check was successful
- **500 Internal Server Error**: Service is running but encountered an error during the health check

## Integration with Dashboard

The Git Dashboard can integrate with the health check endpoint to display the status of the Logging Service to users:

1. **Status Indicator**: Display a status indicator (green/yellow/red) based on the `status` field
2. **Database Status**: Show database connectivity status
3. **Circuit Breaker Status**: Indicate the current state of the circuit breaker
4. **Uptime**: Display service uptime

Example Dashboard integration:

```javascript
// Fetch health status
fetch('/health')
  .then(response => response.json())
  .then(health => {
    // Update status indicator
    const statusIndicator = document.getElementById('logging-status');
    statusIndicator.className = health.status === 'ok' ? 'status-ok' : 'status-error';
    
    // Update database status
    const dbStatus = document.getElementById('db-status');
    dbStatus.textContent = health.storage.database;
    dbStatus.className = health.storage.database === 'connected' ? 'connected' : 'disconnected';
    
    // Update circuit breaker status
    const circuitStatus = document.getElementById('circuit-status');
    circuitStatus.textContent = health.storage.database_circuit;
    
    // Update uptime
    const uptime = document.getElementById('uptime');
    const hours = Math.floor(health.uptime_seconds / 3600);
    const minutes = Math.floor((health.uptime_seconds % 3600) / 60);
    uptime.textContent = `${hours}h ${minutes}m`;
  })
  .catch(error => {
    console.error('Failed to fetch health status:', error);
  });
```

## Integration with Monitoring Systems

The health check endpoint can be integrated with various monitoring systems:

### Prometheus Integration

Example Prometheus configuration:

```yaml
scrape_configs:
  - job_name: 'logging-service'
    metrics_path: '/health'
    scrape_interval: 15s
    static_configs:
      - targets: ['logging-service:8000']
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: '.*'
        target_label: __name__
        replacement: logging_service_health
```

### Nagios/Icinga Integration

Example Nagios check command:

```
define command {
    command_name    check_logging_service
    command_line    $USER1$/check_http -H $HOSTADDRESS$ -u /health -p 8000 -r '"status":"ok"'
}
```

### Custom Monitoring Script

Example bash script for monitoring:

```bash
#!/bin/bash

HEALTH_URL="http://localhost:8000/health"
RESPONSE=$(curl -s $HEALTH_URL)

# Check if service is up
if [ $? -ne 0 ]; then
    echo "CRITICAL: Logging service is not responding"
    exit 2
fi

# Check status
STATUS=$(echo $RESPONSE | jq -r '.status')
if [ "$STATUS" != "ok" ]; then
    echo "WARNING: Logging service status is $STATUS"
    exit 1
fi

# Check database connection
DB_STATUS=$(echo $RESPONSE | jq -r '.storage.database')
if [ "$DB_STATUS" != "connected" ]; then
    echo "WARNING: Database is disconnected"
    exit 1
fi

echo "OK: Logging service is healthy"
exit 0
```

## Automated Recovery

The health check endpoint can be used to implement automated recovery procedures:

1. **Service Restart**: If the service is unresponsive or reports an error status
2. **Database Reconnection**: If the database is disconnected for an extended period
3. **Circuit Breaker Reset**: If the circuit breaker is stuck in the OPEN state

Example automated recovery script:

```bash
#!/bin/bash

HEALTH_URL="http://localhost:8000/health"
RESPONSE=$(curl -s $HEALTH_URL)

# Check if service is up
if [ $? -ne 0 ]; then
    echo "Service is not responding, restarting..."
    systemctl restart git-repo-logging
    exit
fi

# Check circuit breaker state
CIRCUIT_STATE=$(echo $RESPONSE | jq -r '.storage.database_circuit')
DB_STATUS=$(echo $RESPONSE | jq -r '.storage.database')

if [ "$CIRCUIT_STATE" == "OPEN" ] && [ "$DB_STATUS" == "disconnected" ]; then
    # Check how long the circuit has been open
    UPTIME=$(echo $RESPONSE | jq -r '.uptime_seconds')
    
    if [ $UPTIME -gt 3600 ]; then
        echo "Circuit breaker has been open for over an hour, restarting service..."
        systemctl restart git-repo-logging
    fi
fi
```

## Best Practices

When using the health monitoring features:

1. **Regular Polling**: Poll the health endpoint at regular intervals (15-60 seconds)
2. **Alerting Thresholds**: Set appropriate alerting thresholds to avoid alert fatigue
3. **Trend Analysis**: Track health metrics over time to identify patterns
4. **Correlation**: Correlate health status with other system metrics
5. **Automated Recovery**: Implement automated recovery procedures for common issues
6. **Documentation**: Document health status meanings and recovery procedures

## Conclusion

The health monitoring capabilities of the Logging Service provide comprehensive visibility into the operational status of the service. By integrating with the health check endpoint, administrators can proactively monitor the service, troubleshoot issues, and implement automated recovery procedures.
