#!/bin/bash

# Certificate Sync Script for Git Dashboard
# This script synchronizes SSL certificates from the main server to this server

# Exit on any error
set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "This script must be run as root"
    exit 1
fi

# Configuration variables
MAIN_SERVER="***********"
MAIN_SERVER_USER="btaylor-admin"
CERT_DOMAIN="chcit.org"
CERT_DIR="/etc/letsencrypt"
LIVE_DIR="${CERT_DIR}/live/${CERT_DOMAIN}"
ARCHIVE_DIR="${CERT_DIR}/archive/${CERT_DOMAIN}"
LOG_FILE="/opt/git-dashboard/logs/cert-sync.log"

# Log function
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
touch "$LOG_FILE"
chmod 644 "$LOG_FILE"

log "Starting certificate sync from ${MAIN_SERVER}"

# Check SSH connectivity
log "Testing SSH connectivity to ${MAIN_SERVER}..."
if ! ssh -o BatchMode=yes -o ConnectTimeout=5 "${MAIN_SERVER_USER}@${MAIN_SERVER}" echo "SSH connection successful" > /dev/null 2>&1; then
    log "Error: Cannot connect to certificate server ${MAIN_SERVER}. Please verify SSH key setup"
    exit 1
fi

# Create certificate directories if they don't exist
log "Creating certificate directories if they don't exist..."
mkdir -p "$LIVE_DIR" "$ARCHIVE_DIR"

# Set proper permissions for the certificate directories
chmod 755 "$CERT_DIR" "${CERT_DIR}/live" "${CERT_DIR}/archive"
chmod 700 "$LIVE_DIR" "$ARCHIVE_DIR"

# Sync certificates using SSH and tar
log "Syncing certificates from ${MAIN_SERVER}..."
if ssh -o BatchMode=yes "${MAIN_SERVER_USER}@${MAIN_SERVER}" "sudo tar czf - /etc/letsencrypt/archive/${CERT_DOMAIN}/* /etc/letsencrypt/live/${CERT_DOMAIN}/*" | tar xzf - -C / 2>/dev/null; then
    log "Certificate sync completed successfully"
else
    log "Error: Certificate sync failed"
    exit 1
fi

# Verify that certificates are readable
log "Verifying certificate permissions and readability..."
if [ ! -r "${LIVE_DIR}/fullchain.pem" ] || [ ! -r "${LIVE_DIR}/privkey.pem" ]; then
    log "Error: Cannot read SSL certificates. Fixing permissions..."
    chmod 644 "${LIVE_DIR}/fullchain.pem" "${LIVE_DIR}/chain.pem"
    chmod 600 "${LIVE_DIR}/privkey.pem"
fi

# Check certificate validity
log "Checking certificate validity..."
EXPIRY_DATE=$(openssl x509 -enddate -noout -in "${LIVE_DIR}/fullchain.pem" | cut -d= -f2)
DAYS_LEFT=$(( ( $(date -d "$EXPIRY_DATE" +%s) - $(date +%s) ) / 86400 ))

if [ "$DAYS_LEFT" -lt 7 ]; then
    log "Warning: Certificate will expire in $DAYS_LEFT days"
else
    log "Certificate is valid for $DAYS_LEFT more days"
fi

# Restart Nginx to apply new certificates
log "Restarting Nginx to apply new certificates..."
if systemctl is-active --quiet nginx; then
    if nginx -t 2>/dev/null; then
        systemctl reload nginx
        log "Nginx reloaded successfully"
    else
        log "Error: Nginx configuration test failed, not reloading"
    fi
else
    log "Error: Nginx is not running, not reloading"
fi

log "Certificate sync process completed"
