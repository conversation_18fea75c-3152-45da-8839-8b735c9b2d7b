#pragma once
#include <string>
#include <unordered_map>
#include <fstream>
#include <iostream>
#include <filesystem>
#include <regex>
#include <vector>
#include <algorithm>
#include <jsoncpp/json/json.h>

class Config {
public:
    static Config& getInstance() {
        static Config instance;
        return instance;
    }

    bool loadFromFile(const std::string& configFile) {
        try {
            std::ifstream file(configFile);
            if (!file.is_open()) {
                std::cerr << "Failed to open config file: " << configFile << std::endl;
                return false;
            }

            Json::Value root;
            Json::CharReaderBuilder builder;
            std::string errs;

            if (!Json::parseFromStream(builder, file, &root, &errs)) {
                std::cerr << "Error parsing JSON config: " << errs << std::endl;
                return false;
            }

            // Validate schema
            if (!validateConfigSchema(root)) {
                std::cerr << "Invalid configuration schema" << std::endl;
                return false;
            }

            // Extract and validate settings
            for (const auto& key : root.getMemberNames()) {
                settings_[key] = root[key].asString();
            }

            return true;
        } catch (const std::exception& e) {
            std::cerr << "Error loading config: " << e.what() << std::endl;
            return false;
        }
    }

    bool loadFromEnvironment() {
        try {
            // Load and validate repository path
            std::string repoPath = loadAndValidateEnvVar("GIT_REPO_PATH", "/home/<USER>", [](const std::string& value) {
                if (!std::filesystem::exists(value)) {
                    std::cerr << "Warning: GIT_REPO_PATH does not exist: " << value << std::endl;
                    try {
                        std::filesystem::create_directories(value);
                        std::cerr << "Created directory: " << value << std::endl;
                        return true;
                    } catch (const std::exception& e) {
                        std::cerr << "Failed to create directory: " << e.what() << std::endl;
                        return false;
                    }
                }

                // Check if directory is readable
                try {
                    std::filesystem::directory_iterator test(value);
                    return true;
                } catch (const std::exception& e) {
                    std::cerr << "Warning: GIT_REPO_PATH is not readable: " << e.what() << std::endl;
                    return false;
                }
            });

            // Load and validate server port
            std::string portStr = loadAndValidateEnvVar("SERVER_PORT", "8080", [](const std::string& value) {
                try {
                    int port = std::stoi(value);
                    if (port <= 0 || port > 65535) {
                        std::cerr << "Invalid SERVER_PORT: " << port << ", must be between 1 and 65535" << std::endl;
                        return false;
                    }
                    return true;
                } catch (const std::exception& e) {
                    std::cerr << "Invalid SERVER_PORT: " << value << ", must be a number" << std::endl;
                    return false;
                }
            });

            // Load and validate log level
            std::string logLevel = loadAndValidateEnvVar("LOG_LEVEL", "INFO", [](const std::string& value) {
                std::vector<std::string> validLevels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"};
                std::string upperValue = value;
                std::transform(upperValue.begin(), upperValue.end(), upperValue.begin(), ::toupper);

                if (std::find(validLevels.begin(), validLevels.end(), upperValue) == validLevels.end()) {
                    std::cerr << "Invalid LOG_LEVEL: " << value << ", must be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL" << std::endl;
                    return false;
                }
                return true;
            });

            // Load and validate log file
            std::string logFile = loadAndValidateEnvVar("LOG_FILE", "/var/log/git-repo-service.log", [](const std::string& value) {
                // Check if directory exists
                std::filesystem::path logPath(value);
                std::filesystem::path logDir = logPath.parent_path();

                if (!std::filesystem::exists(logDir)) {
                    std::cerr << "Warning: Log directory does not exist: " << logDir.string() << std::endl;
                    try {
                        std::filesystem::create_directories(logDir);
                        std::cerr << "Created directory: " << logDir.string() << std::endl;
                    } catch (const std::exception& e) {
                        std::cerr << "Failed to create log directory: " << e.what() << std::endl;
                        return false;
                    }
                }

                // Check if file is writable
                try {
                    std::ofstream test(value, std::ios::app);
                    if (!test.is_open()) {
                        std::cerr << "Warning: LOG_FILE is not writable: " << value << std::endl;
                        return false;
                    }
                    test.close();
                    return true;
                } catch (const std::exception& e) {
                    std::cerr << "Warning: LOG_FILE is not writable: " << e.what() << std::endl;
                    return false;
                }
            });

            // Additional environment variables can be loaded and validated here

            return true;
        } catch (const std::exception& e) {
            std::cerr << "Error loading environment variables: " << e.what() << std::endl;
            return false;
        }
    }

    bool validateConfig() {
        bool isValid = true;

        // Validate GIT_REPO_PATH
        std::string repoPath = get("GIT_REPO_PATH", "/home/<USER>");
        if (!std::filesystem::exists(repoPath)) {
            std::cerr << "Warning: GIT_REPO_PATH does not exist: " << repoPath << std::endl;
            isValid = false;
        }

        // Validate SERVER_PORT
        int port = getInt("SERVER_PORT", 8080);
        if (port <= 0 || port > 65535) {
            std::cerr << "Invalid SERVER_PORT: " << port << ", using default 8080" << std::endl;
            set("SERVER_PORT", "8080");
            isValid = false;
        }

        // Validate LOG_LEVEL
        std::string logLevel = get("LOG_LEVEL", "INFO");
        std::vector<std::string> validLevels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"};
        std::string upperLogLevel = logLevel;
        std::transform(upperLogLevel.begin(), upperLogLevel.end(), upperLogLevel.begin(), ::toupper);

        if (std::find(validLevels.begin(), validLevels.end(), upperLogLevel) == validLevels.end()) {
            std::cerr << "Invalid LOG_LEVEL: " << logLevel << ", using default INFO" << std::endl;
            set("LOG_LEVEL", "INFO");
            isValid = false;
        }

        // Validate LOG_FILE
        std::string logFile = get("LOG_FILE", "/var/log/git-repo-service.log");
        std::filesystem::path logPath(logFile);
        std::filesystem::path logDir = logPath.parent_path();

        if (!std::filesystem::exists(logDir)) {
            std::cerr << "Warning: Log directory does not exist: " << logDir.string() << std::endl;
            try {
                std::filesystem::create_directories(logDir);
                std::cerr << "Created directory: " << logDir.string() << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Failed to create log directory: " << e.what() << std::endl;
                isValid = false;
            }
        }

        // Check if log file is writable
        try {
            std::ofstream test(logFile, std::ios::app);
            if (!test.is_open()) {
                std::cerr << "Warning: LOG_FILE is not writable: " << logFile << std::endl;
                isValid = false;
            }
            test.close();
        } catch (const std::exception& e) {
            std::cerr << "Warning: LOG_FILE is not writable: " << e.what() << std::endl;
            isValid = false;
        }

        return isValid;
    }

    std::string get(const std::string& key, const std::string& defaultValue = "") const {
        auto it = settings_.find(key);
        if (it != settings_.end()) {
            return it->second;
        }
        return defaultValue;
    }

    int getInt(const std::string& key, int defaultValue = 0) const {
        auto it = settings_.find(key);
        if (it != settings_.end()) {
            try {
                return std::stoi(it->second);
            } catch (...) {
                std::cerr << "Warning: Invalid integer value for " << key << ": " << it->second << ", using default " << defaultValue << std::endl;
                return defaultValue;
            }
        }
        return defaultValue;
    }

    bool getBool(const std::string& key, bool defaultValue = false) const {
        auto it = settings_.find(key);
        if (it != settings_.end()) {
            std::string value = it->second;
            std::transform(value.begin(), value.end(), value.begin(), ::tolower);
            return value == "true" || value == "1" || value == "yes" || value == "on";
        }
        return defaultValue;
    }

    void set(const std::string& key, const std::string& value) {
        settings_[key] = value;
    }

    // Dump current configuration (useful for debugging)
    void dumpConfig() const {
        std::cout << "Current Configuration:" << std::endl;
        std::cout << "---------------------" << std::endl;
        for (const auto& [key, value] : settings_) {
            std::cout << key << " = " << value << std::endl;
        }
        std::cout << "---------------------" << std::endl;
    }

private:
    Config() {}
    ~Config() {}

    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;

    std::string loadAndValidateEnvVar(const std::string& name, const std::string& defaultValue,
                                     std::function<bool(const std::string&)> validator) {
        const char* value = std::getenv(name.c_str());
        std::string strValue = value ? value : defaultValue;

        if (!validator(strValue)) {
            std::cerr << "Using default value for " << name << ": " << defaultValue << std::endl;
            strValue = defaultValue;
        }

        settings_[name] = strValue;
        return strValue;
    }

    void loadEnvVar(const std::string& name, const std::string& defaultValue) {
        const char* value = std::getenv(name.c_str());
        settings_[name] = value ? value : defaultValue;
    }

    bool validateConfigSchema(const Json::Value& root) {
        // Check for required fields
        std::vector<std::string> requiredFields = {"GIT_REPO_PATH", "SERVER_PORT", "LOG_LEVEL"};

        for (const auto& field : requiredFields) {
            if (!root.isMember(field)) {
                std::cerr << "Missing required field in config: " << field << std::endl;
                return false;
            }
        }

        // Validate field types
        if (root.isMember("SERVER_PORT") && !root["SERVER_PORT"].isNumeric() && !root["SERVER_PORT"].isString()) {
            std::cerr << "SERVER_PORT must be a number or string" << std::endl;
            return false;
        }

        return true;
    }

    std::unordered_map<std::string, std::string> settings_;
};

#define CONFIG Config::getInstance()
