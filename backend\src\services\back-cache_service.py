from typing import Dict, Any, Optional, Union
import redis
from redis.connection import ConnectionPool
import json
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class CacheService:
    """Redis cache service with connection pooling and monitoring."""
    
    def __init__(self):
        self.pool = None
        self.metrics = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'errors': 0,
            'connection_errors': 0
        }
        self._start_time = datetime.now()
        
    def init_app(self, app):
        """Initialize Redis connection pool."""
        try:
            self.pool = ConnectionPool.from_url(
                url=app.config['REDIS_URL'],
                max_connections=20,
                decode_responses=True
            )
            self._redis = redis.Redis(connection_pool=self.pool)
            logger.info("Redis connection pool initialized")
        except Exception as e:
            self.metrics['connection_errors'] += 1
            logger.error(f"Redis initialization error: {str(e)}", exc_info=True)
            raise
            
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            value = self._redis.get(key)
            if value:
                self.metrics['hits'] += 1
                return json.loads(value)
            self.metrics['misses'] += 1
            return None
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"Cache get error: {str(e)}", exc_info=True)
            return None
            
    def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Set value in cache with optional expiration."""
        try:
            serialized = json.dumps(value)
            self._redis.set(key, serialized, ex=expire)
            self.metrics['sets'] += 1
            return True
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"Cache set error: {str(e)}", exc_info=True)
            return False
            
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            return bool(self._redis.delete(key))
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"Cache delete error: {str(e)}", exc_info=True)
            return False
            
    def get_metrics(self) -> Dict[str, Union[int, float]]:
        """Get cache service metrics."""
        total_ops = self.metrics['hits'] + self.metrics['misses']
        hit_rate = (
            self.metrics['hits'] / total_ops * 100
            if total_ops > 0 else 0
        )
        
        return {
            **self.metrics,
            'hit_rate': round(hit_rate, 2),
            'total_operations': total_ops,
            'uptime_seconds': (
                datetime.now() - self._start_time
            ).total_seconds(),
            'connection_pool_size': (
                len(self.pool._available_connections)
                if self.pool else 0
            )
        }
        
    def clear_metrics(self):
        """Reset metrics counters."""
        self.metrics = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'errors': 0,
            'connection_errors': 0
        }
        self._start_time = datetime.now()
