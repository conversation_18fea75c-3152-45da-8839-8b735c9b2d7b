@echo off
setlocal enabledelayedexpansion

:: Configuration
set "GIT_SERVER=git.chcit.org"
set "SSH_USER=btaylor-admin"
set "REMOTE_DIR=/opt/git-dashboard"
set "REMOTE_BUILD_DIR=/home/<USER>/git-repo-logging"
set "SOURCE_DIR=git-repo-logging"
set "CONFIG_DIR=/etc/git-dashboard"
set "SYSTEMD_DIR=/etc/systemd/system"

:: Main menu
:MENU
cls
echo =============================================
echo Git Repository Logging Agent Deployment Tool
echo =============================================
echo.
echo 1. Check SSH Connection
echo 2. Install Dependencies
echo 3. Deploy Source Code
echo 4. Build Application
echo 5. Install Service
echo 6. Start Service
echo 7. Check Service Status
echo 8. View Logs
echo 9. Setup PostgreSQL Database
echo 10. Configure Storage Tiers
echo 11. Full Deployment (Steps 1-7)
echo.
echo Database Management:
echo 12. Verify PostgreSQL Installation
echo 13. Examine PostgreSQL Schema
echo 14. List PostgreSQL Databases
echo 15. Check Database Schema Version
echo 16. Delete Database
echo 17. List Schemas in Database
echo 18. Delete Schema from Database
echo 19. Uninstall PostgreSQL
echo 20. Delete Database and Schema
echo.
echo 0. Exit
echo.
set /p choice="Enter your choice: "

if "%choice%"=="1" goto MENU_CHECK_SSH
if "%choice%"=="2" goto INSTALL_DEPS
if "%choice%"=="3" goto DEPLOY_CODE
if "%choice%"=="4" goto BUILD_APP
if "%choice%"=="5" goto INSTALL_SERVICE
if "%choice%"=="6" goto START_SERVICE
if "%choice%"=="7" goto CHECK_STATUS
if "%choice%"=="8" goto VIEW_LOGS
if "%choice%"=="9" goto SETUP_DATABASE
if "%choice%"=="10" goto CONFIGURE_STORAGE
if "%choice%"=="11" goto FULL_DEPLOY
if "%choice%"=="12" goto VERIFY_POSTGRESQL
if "%choice%"=="13" goto EXAMINE_SCHEMA
if "%choice%"=="14" goto LIST_DATABASES
if "%choice%"=="15" goto CHECK_SCHEMA_VERSION
if "%choice%"=="16" goto DELETE_DATABASE
if "%choice%"=="17" goto LIST_SCHEMAS
if "%choice%"=="18" goto DELETE_SCHEMA
if "%choice%"=="19" goto UNINSTALL_POSTGRESQL
if "%choice%"=="20" goto DELETE_DATABASE_AND_SCHEMA
if "%choice%"=="0" goto EXIT

echo Invalid choice. Please try again.
pause
goto MENU

:: Menu option for checking SSH - this is different from the subroutine
:MENU_CHECK_SSH
echo.
echo Checking SSH connection to Git server...
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    echo Failed to connect to Git server.
    echo Please check your SSH configuration and ensure the server is reachable.
    pause
    goto MENU
)
echo SSH connection successful.
pause
goto MENU

:: Subroutine for checking SSH - this returns to the caller
:CHECK_SSH
ssh -o BatchMode=yes -o ConnectTimeout=5 %SSH_USER%@%GIT_SERVER% "echo SSH connection successful" > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Failed to connect to Git server.
    echo Please check your SSH configuration and ensure the server is reachable.
    exit /b 1
)
exit /b 0

:INSTALL_DEPS
echo.
echo Checking for missing dependencies...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

set "MISSING_PACKAGES="

:: Check for essential build tools
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q build-essential || echo missing_build_essential"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% build-essential"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q cmake || echo missing_cmake"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% cmake"

:: Check for required libraries
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libboost-all-dev || echo missing_boost"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libboost-all-dev"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libssl-dev || echo missing_ssl"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libssl-dev"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libjsoncpp-dev || echo missing_jsoncpp"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libjsoncpp-dev"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q pkg-config || echo missing_pkgconfig"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% pkg-config"

:: Check for PostgreSQL libraries
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libpq-dev || echo missing_libpq"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libpq-dev"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libpqxx-dev || echo missing_libpqxx"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libpqxx-dev"

:: Check for optional libraries
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libzstd-dev || echo missing_zstd"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libzstd-dev"

:: Check for PostgreSQL server
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l postgresql* | grep -E '^ii\s+postgresql-[0-9]+' > /dev/null || echo missing_postgresql" > temp.txt
set /p pg_installed=<temp.txt
echo Preserving temp.txt for reference
if "%pg_installed%"=="missing_postgresql" set "MISSING_PACKAGES=%MISSING_PACKAGES% postgresql postgresql-contrib"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q libpqxx-dev || echo missing_libpqxx"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% libpqxx-dev"

ssh %SSH_USER%@%GIT_SERVER% "dpkg -l | grep -q zlib1g-dev || echo missing_zlib"
if %ERRORLEVEL% neq 0 set "MISSING_PACKAGES=%MISSING_PACKAGES% zlib1g-dev"

if not "%MISSING_PACKAGES%"=="" (
    echo The following dependencies are missing: %MISSING_PACKAGES%
    set /p install_deps="Install missing dependencies? (y/n): "
    if /i "%install_deps%"=="y" (
        echo Installing dependencies...
        ssh %SSH_USER%@%GIT_SERVER% "sudo apt update && sudo apt install -y%MISSING_PACKAGES%"
        if %ERRORLEVEL% neq 0 (
            echo Failed to install dependencies.
            pause
            goto MENU
        )
        echo Dependencies installed successfully.
    ) else (
        echo Skipping dependency installation.
    )
) else (
    echo All dependencies are already installed.
)

pause
goto MENU

:DEPLOY_CODE
echo.
echo Deploying source code to Git server...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Copy source code directly to the build directory in home
echo Copying source code directly to %REMOTE_BUILD_DIR%...
scp -r %SOURCE_DIR%/* %SSH_USER%@%GIT_SERVER%:%REMOTE_BUILD_DIR%/

echo Note: Files will be compiled in home directory and then moved to production directory with proper permissions.

echo Source code deployed successfully.
pause
goto MENU

:BUILD_APP
echo.
echo Building application on Git server...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Create build directory
echo Creating build directory...
ssh %SSH_USER%@%GIT_SERVER% "mkdir -p %REMOTE_BUILD_DIR%/build"

:: Build the application
echo Building the application...
ssh %SSH_USER%@%GIT_SERVER% "cd %REMOTE_BUILD_DIR%/build && cmake .. -DCMAKE_BUILD_TYPE=Release && make -j4"

if %ERRORLEVEL% neq 0 (
    echo Failed to build application.
    pause
    goto MENU
)

:: Install the application
echo Installing the application...
ssh %SSH_USER%@%GIT_SERVER% "cd %REMOTE_BUILD_DIR%/build && make install"

if %ERRORLEVEL% neq 0 (
    echo Failed to install application.
    pause
    goto MENU
)

:: Ask if user wants to move binaries to production
set /p move_to_prod="Move compiled binaries to production directory? (y/n): "
if /i "%move_to_prod%"=="y" goto MOVE_TO_PRODUCTION

echo Application built and installed successfully.
pause
goto MENU

:MOVE_TO_PRODUCTION
echo.
echo Moving compiled binaries to production directory...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Create all required directories
echo Creating all required directories...
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_DIR%/bin %REMOTE_DIR%/lib %REMOTE_DIR%/config %CONFIG_DIR% /var/log/git-dashboard/logs"

:: Copy binaries to production directory
echo Copying binaries to production directory...
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %REMOTE_BUILD_DIR%/build/git-repo-logging %REMOTE_DIR%/bin/"

:: Copy configuration files
echo Copying configuration files...
ssh %SSH_USER%@%GIT_SERVER% "sudo cp -r %REMOTE_BUILD_DIR%/config/* %REMOTE_DIR%/config/ && sudo cp %REMOTE_BUILD_DIR%/config/logging-config.json %CONFIG_DIR%/ 2>/dev/null || echo 'No logging-config.json found, using defaults'"

:: Copy SQL files
echo Copying SQL files...
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_DIR%/sql && sudo cp -r %REMOTE_BUILD_DIR%/sql/* %REMOTE_DIR%/sql/"

:: Set proper permissions
echo Setting proper permissions...
ssh %SSH_USER%@%GIT_SERVER% "sudo chown -R www-data:www-data %REMOTE_DIR% && sudo chown -R www-data:www-data /var/log/git-dashboard && sudo chmod 755 %REMOTE_DIR%/bin/git-repo-logging"

:: Ask if user wants to set up the database
set /p setup_db="Do you want to set up the database schema? (y/n): "
if /i "%setup_db%"=="y" goto SETUP_DB_SCHEMA

echo Binaries moved to production directory successfully.
pause
goto MENU

:SETUP_DB_SCHEMA
echo.
echo Setting up database schema...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Run the database setup script
echo Running database setup script...
ssh -t %SSH_USER%@%GIT_SERVER% "cd %REMOTE_DIR%/sql && sudo chmod +x setup_database.sh && ./setup_database.sh"

echo Database schema set up successfully.
pause
goto MENU

:INSTALL_SERVICE
echo.
echo Installing systemd service...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Copy systemd service file
echo Copying systemd service file...
ssh %SSH_USER%@%GIT_SERVER% "sudo cp %REMOTE_BUILD_DIR%/systemd/git-repo-logging.service %SYSTEMD_DIR%/ && sudo chown root:root %SYSTEMD_DIR%/git-repo-logging.service && sudo chmod 644 %SYSTEMD_DIR%/git-repo-logging.service"

:: Reload systemd
echo Reloading systemd...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl daemon-reload"

:: Enable the service
echo Enabling the service...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl enable git-repo-logging.service"

echo Service installed successfully.
pause
goto MENU

:START_SERVICE
echo.
echo Starting git-repo-logging service...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Start the service
echo Starting the service...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl start git-repo-logging.service"

if %ERRORLEVEL% neq 0 (
    echo Failed to start service.
    pause
    goto MENU
)

echo Service started successfully.
pause
goto MENU

:CHECK_STATUS
echo.
echo Checking git-repo-logging service status...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check service status
echo Service status:
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status git-repo-logging.service"

pause
goto MENU

:VIEW_LOGS
echo.
echo Viewing git-repo-logging service logs...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: View service logs
echo Service logs:
ssh %SSH_USER%@%GIT_SERVER% "sudo journalctl -u git-repo-logging.service -n 50 --no-pager"

pause
goto MENU

:SETUP_DATABASE
echo.
echo Setting up PostgreSQL database...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check if PostgreSQL is installed
echo Checking if PostgreSQL is installed...

:: Check for any PostgreSQL server package
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-[0-9]*' | grep '^ii' > /dev/null && echo installed || echo missing_postgresql" > temp.txt
set /p pg_installed=<temp.txt
echo Preserving temp.txt for reference

if "%pg_installed%"=="missing_postgresql" (
    echo PostgreSQL server is not installed on the server.
    set /p install_postgres="Install PostgreSQL? (y/n): "
    if /i "%install_postgres%"=="y" (
        echo Installing PostgreSQL...
        ssh %SSH_USER%@%GIT_SERVER% "sudo apt update && sudo apt install -y postgresql postgresql-contrib"
        if %ERRORLEVEL% neq 0 (
            echo Failed to install PostgreSQL.
            pause
            goto MENU
        )
        echo PostgreSQL installed successfully.
    ) else (
        echo Skipping PostgreSQL installation.
        pause
        goto MENU
    )
) else (
    :: Check which PostgreSQL versions are installed
    ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-[0-9]*' | grep '^ii' | awk '{print $2}'" > temp.txt
    type temp.txt
    echo Preserving temp.txt for reference

    :: Check which PostgreSQL is actually running
    echo Checking which PostgreSQL is actually running...
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -t -c 'SELECT version();'" > temp.txt
    set /p pg_running_version=<temp.txt
    echo Preserving temp.txt for reference
    echo PostgreSQL version currently running: %pg_running_version%

    :: Check data directory to determine which installation is active
    echo Checking PostgreSQL data directory...
    ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -t -c 'SHOW data_directory;'" > temp.txt
    set /p pg_data_dir=<temp.txt
    echo Preserving temp.txt for reference
    echo PostgreSQL data directory: %pg_data_dir%
)

:: Check if PostgreSQL service is running
echo Checking if PostgreSQL service is running...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl is-active postgresql >/dev/null 2>&1 && echo running || echo stopped" > temp.txt
set /p pg_status=<temp.txt
echo Preserving temp.txt for reference

if "%pg_status%"=="stopped" (
    echo PostgreSQL service is not running.
    set /p start_postgres="Start PostgreSQL service? (y/n): "
    if /i "%start_postgres%"=="y" (
        echo Starting PostgreSQL service...
        ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl start postgresql"
        if %ERRORLEVEL% neq 0 (
            echo Failed to start PostgreSQL service.
            pause
            goto MENU
        )
        echo PostgreSQL service started successfully.
    ) else (
        echo PostgreSQL service must be running to continue.
        pause
        goto MENU
    )
)

:: Make sure the SQL directory exists on the server
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_DIR%/sql && sudo chown www-data:www-data %REMOTE_DIR%/sql"

:: Copy SQL files to the server
scp "%SOURCE_DIR%\sql\*" %SSH_USER%@%GIT_SERVER%:/tmp/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /tmp/schema.sql /tmp/setup_database.sh %REMOTE_DIR%/sql/ && sudo chmod +x %REMOTE_DIR%/sql/setup_database.sh"

:: Configure database
echo Configuring database...
set /p db_name="Database name (default: git_logging): "
if "%db_name%"=="" set db_name=git_logging

set /p db_user="Database user (default: git_logger): "
if "%db_user%"=="" set db_user=git_logger

set /p db_password="Database password: "
if "%db_password%"=="" (
    echo Password cannot be empty.
    pause
    goto SETUP_DATABASE
)

:: Ask about version checking
set /p skip_version_check="Skip schema version check? (y/n, default: n): "
set skip_version_param=""
if /i "%skip_version_check%"=="y" set skip_version_param="--skip-version-check"

set /p force_update="Force schema update even if version mismatch? (y/n, default: n): "
set force_update_param=""
if /i "%force_update%"=="y" set force_update_param="--force-update"

:: Run the setup script
echo Running database setup script...
ssh %SSH_USER%@%GIT_SERVER% "cd %REMOTE_DIR%/sql && sudo -u postgres ./setup_database.sh --db-name %db_name% --db-user %db_user% --db-password %db_password% %skip_version_param% %force_update_param%"

if %ERRORLEVEL% neq 0 (
    echo Failed to set up database.
    pause
    goto MENU
)

echo Database setup completed successfully.
echo Connection string saved to %REMOTE_DIR%/sql/.env

pause
goto MENU

:CONFIGURE_STORAGE
echo.
echo Configuring storage tiers...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Make sure the config directory exists on the server
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p %REMOTE_DIR%/config && sudo chown www-data:www-data %REMOTE_DIR%/config"

:: Copy config files to the server
scp "%SOURCE_DIR%\config\*" %SSH_USER%@%GIT_SERVER%:/tmp/
ssh %SSH_USER%@%GIT_SERVER% "sudo cp /tmp/storage-config.json %REMOTE_DIR%/config/"

:: Get database connection string
ssh %SSH_USER%@%GIT_SERVER% "if [ -f %REMOTE_DIR%/sql/.env ]; then source %REMOTE_DIR%/sql/.env; echo $DB_CONNECTION_STRING; fi" > temp.txt
set /p DB_CONNECTION_STRING=<temp.txt
echo Preserving temp.txt for reference

if "%DB_CONNECTION_STRING%"=="" (
    echo Database connection string not found.
    echo Please run the database setup first.
    pause
    goto MENU
)

:: Update the storage config with the connection string
ssh %SSH_USER%@%GIT_SERVER% "sudo sed -i 's|\"connection_string\": \".*\"|\"connection_string\": \"%DB_CONNECTION_STRING%\"|g' %REMOTE_DIR%/config/storage-config.json"

:: Create the log directories
ssh %SSH_USER%@%GIT_SERVER% "sudo mkdir -p /var/log/git-dashboard/logs && sudo chown -R www-data:www-data /var/log/git-dashboard"

echo Storage tiers configured successfully.
pause
goto MENU

:VERIFY_POSTGRESQL
echo.
echo Verifying PostgreSQL installation...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Detailed checks for PostgreSQL
echo.
echo 1. Checking ALL installed PostgreSQL packages (exact match):
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql*' | grep '^ii'"

echo.
echo 2. Checking PostgreSQL server packages specifically:
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-[0-9]*' | grep '^ii'"

echo.
echo 3. Checking PostgreSQL client packages:
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-client*' | grep '^ii'"

echo.
echo 4. Checking PostgreSQL service status:
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl status postgresql 2>&1"

echo.
echo 5. Checking PostgreSQL cluster status:
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres pg_lsclusters"

echo.
echo 6. Trying to connect to PostgreSQL:
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'SELECT version();' 2>&1"

echo.
echo 7. Listing PostgreSQL databases:
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -l 2>&1"

echo.
echo 8. Checking PostgreSQL data directories:
ssh %SSH_USER%@%GIT_SERVER% "ls -la /pgsql/data 2>/dev/null || echo 'Directory /pgsql/data not found or not accessible'"
ssh %SSH_USER%@%GIT_SERVER% "ls -la /var/lib/postgresql 2>/dev/null || echo 'Directory /var/lib/postgresql not found or not accessible'"

echo.
echo 9. Checking which PostgreSQL is actually running:
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'SELECT current_setting(\'data_directory\') AS data_dir, current_setting(\'port\') AS port, version();'"

echo.
echo PostgreSQL verification completed.
echo Please review the information above to determine the status of your PostgreSQL installation.
echo.
echo NOTE: If you have multiple PostgreSQL installations, the version shown in
echo       'Checking which PostgreSQL is actually running' indicates which one
echo       is currently active and being used by the system.

pause
goto MENU

:LIST_DATABASES
echo.
echo Listing PostgreSQL databases...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Create a direct script to list databases
echo Creating script to list databases...
ssh %SSH_USER%@%GIT_SERVER% "cat > /tmp/list_databases.sh << 'EOF'
#!/bin/bash

# Check if PostgreSQL is installed
if command -v psql >/dev/null; then
  echo "PostgreSQL is installed"
  echo "PostgreSQL Version:"
  psql --version

  echo "\nListing PostgreSQL databases:"
  sudo -u postgres psql -l
else
  echo "PostgreSQL is not installed or psql command is not in PATH"
  exit 1
fi
EOF"

ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/list_databases.sh"

:: Run the script
echo Running database listing script...
ssh %SSH_USER%@%GIT_SERVER% "/tmp/list_databases.sh"

pause
goto MENU

:CHECK_SCHEMA_VERSION
echo.
echo Checking database schema version...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check PostgreSQL installation and service status
call :CHECK_POSTGRESQL
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Get database name
set /p db_name="Enter database name: "
if "%db_name%"=="" (
    echo Database name cannot be empty.
    pause
    goto MENU
)

:: Check if database exists
echo Checking if database exists...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw %db_name% && echo exists || echo notfound" > temp.txt
set /p db_exists=<temp.txt
echo Preserving temp.txt for reference

if "%db_exists%"=="notfound" (
    echo Database '%db_name%' does not exist.
    pause
    goto MENU
)

:: Check schema version
echo Executing command to check schema version...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -t -c 'SELECT version, description, applied_at FROM schema_version ORDER BY id DESC LIMIT 1;' 2>/dev/null || echo 'Error: Schema version table not found or database does not exist.'"

pause
goto MENU

:DELETE_DATABASE
echo.
echo WARNING: This will permanently delete a database!
echo.

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check PostgreSQL installation and service status
call :CHECK_POSTGRESQL
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: If multiple PostgreSQL versions are installed, ask which one to use
if %pg_version_count% GTR 1 (
    echo.
    echo Multiple PostgreSQL installations detected:
    type pg_versions.txt
    echo.
    echo Current active installation: %pg_data_dir%
    echo.
    set /p use_active="Use the active PostgreSQL installation? (Y/n): "

    if /i "%use_active%"=="n" (
        echo.
        echo Available PostgreSQL data directories:
        ssh %SSH_USER%@%GIT_SERVER% "find /var/lib/postgresql /pgsql -name data -type d 2>/dev/null || echo 'No PostgreSQL data directories found'"
        echo.
        set /p custom_data_dir="Enter the full path to the PostgreSQL data directory to use: "

        if not "%custom_data_dir%"=="" (
            echo Using custom PostgreSQL data directory: %custom_data_dir%
            set pg_data_dir=%custom_data_dir%
        ) else (
            echo No custom data directory specified. Using active installation: %pg_data_dir%
        )
    )
)

:: Get database name
set /p db_name="Enter database name to delete: "
if "%db_name%"=="" (
    echo Database name cannot be empty.
    pause
    goto MENU
)

:: Check if database exists
echo Checking if database exists...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw %db_name% && echo exists || echo notfound" > temp.txt
set /p db_exists=<temp.txt
echo Preserving temp.txt for reference

if "%db_exists%"=="notfound" (
    echo Database '%db_name%' does not exist.
    pause
    goto MENU
)

:: Confirm deletion
set /p confirm="Are you sure you want to delete database '%db_name%'? This cannot be undone. (y/n): "
if /i not "%confirm%"=="y" (
    echo Database deletion cancelled.
    pause
    goto MENU
)

:: Double confirm for safety
set /p confirm2="Type the database name again to confirm deletion: "
if not "%confirm2%"=="%db_name%" (
    echo Database deletion cancelled - names did not match.
    pause
    goto MENU
)

:: Delete database
echo Executing command to delete database...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP DATABASE \"%db_name%\";'"

pause
goto MENU

:LIST_SCHEMAS
echo.
echo Listing schemas in a database...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check PostgreSQL installation and service status
call :CHECK_POSTGRESQL
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: If multiple PostgreSQL versions are installed, ask which one to use
if %pg_version_count% GTR 1 (
    echo.
    echo Multiple PostgreSQL installations detected:
    type pg_versions.txt
    echo.
    echo Current active installation: %pg_data_dir%
    echo.
    set /p use_active="Use the active PostgreSQL installation? (Y/n): "

    if /i "%use_active%"=="n" (
        echo.
        echo Available PostgreSQL data directories:
        ssh %SSH_USER%@%GIT_SERVER% "find /var/lib/postgresql /pgsql -name data -type d 2>/dev/null || echo 'No PostgreSQL data directories found'"
        echo.
        set /p custom_data_dir="Enter the full path to the PostgreSQL data directory to use: "

        if not "%custom_data_dir%"=="" (
            echo Using custom PostgreSQL data directory: %custom_data_dir%
            set pg_data_dir=%custom_data_dir%
        ) else (
            echo No custom data directory specified. Using active installation: %pg_data_dir%
        )
    )
)

:: Get database name
set /p db_name="Enter database name: "
if "%db_name%"=="" (
    echo Database name cannot be empty.
    pause
    goto MENU
)

:: Check if database exists
echo Checking if database exists...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw %db_name% && echo exists || echo notfound" > temp.txt
set /p db_exists=<temp.txt
echo Preserving temp.txt for reference

if "%db_exists%"=="notfound" (
    echo Database '%db_name%' does not exist.
    pause
    goto MENU
)

:: List schemas
echo Executing command to list schemas...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\dn'"

pause
goto MENU

:DELETE_SCHEMA
echo.
echo WARNING: This will permanently delete a schema from a database!
echo.

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check PostgreSQL installation and service status
call :CHECK_POSTGRESQL
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: If multiple PostgreSQL versions are installed, ask which one to use
if %pg_version_count% GTR 1 (
    echo.
    echo Multiple PostgreSQL installations detected:
    type pg_versions.txt
    echo.
    echo Current active installation: %pg_data_dir%
    echo.
    set /p use_active="Use the active PostgreSQL installation? (Y/n): "

    if /i "%use_active%"=="n" (
        echo.
        echo Available PostgreSQL data directories:
        ssh %SSH_USER%@%GIT_SERVER% "find /var/lib/postgresql /pgsql -name data -type d 2>/dev/null || echo 'No PostgreSQL data directories found'"
        echo.
        set /p custom_data_dir="Enter the full path to the PostgreSQL data directory to use: "

        if not "%custom_data_dir%"=="" (
            echo Using custom PostgreSQL data directory: %custom_data_dir%
            set pg_data_dir=%custom_data_dir%
        ) else (
            echo No custom data directory specified. Using active installation: %pg_data_dir%
        )
    )
)

:: Get database name
set /p db_name="Enter database name: "
if "%db_name%"=="" (
    echo Database name cannot be empty.
    pause
    goto MENU
)

:: Check if database exists
echo Checking if database exists...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw %db_name% && echo exists || echo notfound" > temp.txt
set /p db_exists=<temp.txt
echo Preserving temp.txt for reference

if "%db_exists%"=="notfound" (
    echo Database '%db_name%' does not exist.
    pause
    goto MENU
)

:: List schemas for reference
echo Current schemas in database '%db_name%':
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\dn'"

:: Get schema name
set /p schema_name="Enter schema name to delete: "
if "%schema_name%"=="" (
    echo Schema name cannot be empty.
    pause
    goto MENU
)

:: Check if schema exists
echo Checking if schema exists...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -t -c \"SELECT count(*) FROM pg_namespace WHERE nspname = '%schema_name%';\"" > temp.txt
set /p schema_count=<temp.txt
echo Preserving temp.txt for reference

set schema_count=%schema_count: =%
if "%schema_count%"=="0" (
    echo Schema '%schema_name%' does not exist in database '%db_name%'.
    pause
    goto MENU
)

:: Confirm deletion
set /p confirm="Are you sure you want to delete schema '%schema_name%' from database '%db_name%'? This cannot be undone. (y/n): "
if /i not "%confirm%"=="y" (
    echo Schema deletion cancelled.
    pause
    goto MENU
)

:: Double confirm for safety
set /p confirm2="Type the schema name again to confirm deletion: "
if not "%confirm2%"=="%schema_name%" (
    echo Schema deletion cancelled - names did not match.
    pause
    goto MENU
)

:: Delete schema
echo Executing command to delete schema...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c 'DROP SCHEMA \"%schema_name%\" CASCADE;'"

pause
goto MENU

:: Helper function to check PostgreSQL installation and service status
:CHECK_POSTGRESQL
echo Checking if PostgreSQL is installed...

:: Create a comprehensive check script
echo Creating PostgreSQL detection script...
ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'INSTALLED=false' >> /tmp/detect_pg.sh"

:: Check for PostgreSQL packages
ssh %SSH_USER%@%GIT_SERVER% "echo 'if dpkg -l postgresql-[0-9]* 2>/dev/null | grep -q ^ii; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL detected via package manager"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  INSTALLED=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check for psql command
ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL detected via psql command"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  psql --version' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  INSTALLED=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check for PostgreSQL processes
ssh %SSH_USER%@%GIT_SERVER% "echo 'if ps aux | grep -v grep | grep -q postgres; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL detected via running processes"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  INSTALLED=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check for PostgreSQL data directories
ssh %SSH_USER%@%GIT_SERVER% "echo 'if [ -d /pgsql/data ] || [ -d /var/lib/postgresql ] || [ -d /var/lib/postgresql/17 ] || [ -d /etc/postgresql/17 ]; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL detected via data directories"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  INSTALLED=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check for PostgreSQL service
ssh %SSH_USER%@%GIT_SERVER% "echo 'if systemctl list-units | grep -q postgresql; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL detected via systemd service"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  INSTALLED=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check for PostgreSQL 17 specifically
ssh %SSH_USER%@%GIT_SERVER% "echo 'PG17=false' >> /tmp/detect_pg.sh"

:: Check via psql version
ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  if psql --version | grep -q "17"; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "PostgreSQL 17 detected via psql version"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    PG17=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  fi' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check via data directory
ssh %SSH_USER%@%GIT_SERVER% "echo 'if [ -d /var/lib/postgresql/17 ] || [ -d /etc/postgresql/17 ]; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL 17 detected via data directory"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  PG17=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Check via package
ssh %SSH_USER%@%GIT_SERVER% "echo 'if dpkg -l postgresql-17 2>/dev/null | grep -q ^ii; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL 17 detected via package manager"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  PG17=true' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Output final result
ssh %SSH_USER%@%GIT_SERVER% "echo 'if [ "$INSTALLED" = "true" ]; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "installed"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "missing_postgresql"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Output PostgreSQL 17 result
ssh %SSH_USER%@%GIT_SERVER% "echo 'if [ "$PG17" = "true" ]; then' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "pg17_installed"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "pg17_not_installed"' >> /tmp/detect_pg.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/detect_pg.sh"

:: Make the script executable
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/detect_pg.sh"

:: Run the detection script
echo Running PostgreSQL detection script...
ssh %SSH_USER%@%GIT_SERVER% "/tmp/detect_pg.sh" > temp.txt
type temp.txt

:: Check if PostgreSQL is installed
findstr /C:"installed" temp.txt > nul
if %ERRORLEVEL% equ 0 (
    set pg_installed=installed
) else (
    set pg_installed=missing_postgresql
)

:: Check if PostgreSQL 17 is installed
findstr /C:"pg17_installed" temp.txt > nul
if %ERRORLEVEL% equ 0 (
    set pg17_installed=pg17_installed
    echo PostgreSQL 17 is installed.
) else (
    set pg17_installed=pg17_not_installed
)

if "%pg_installed%"=="missing_postgresql" (
    echo PostgreSQL server is not installed on the server.
    echo Please install PostgreSQL first by selecting option 9 from the menu.
    exit /b 1
)

:: Get PostgreSQL version information
echo Getting PostgreSQL version information...
ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "PostgreSQL Version:"' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  psql --version' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "\nInstalled PostgreSQL Packages:"' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  dpkg -l "postgresql*" 2>/dev/null | grep ^ii || echo "No PostgreSQL packages found"' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "\nPostgreSQL Binary Location:"' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  which psql' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "psql command not found"' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/get_pg_info.sh"
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/get_pg_info.sh"

:: Run the script
ssh %SSH_USER%@%GIT_SERVER% "/tmp/get_pg_info.sh" > pg_versions.txt
type pg_versions.txt

:: Count installed versions (set to 1 if psql exists, otherwise count packages)
if "%pg17_installed%"=="pg17_installed" (
    set pg_version_count=1
) else (
    ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-[0-9]*' 2>/dev/null | grep '^ii' | wc -l" > temp.txt
    set /p pg_version_count=<temp.txt
    echo Preserving temp.txt for reference

    if %pg_version_count% equ 0 (
        if "%pg_installed%"=="installed" (
            set pg_version_count=1
        )
    )
)

:: Get PostgreSQL runtime information
echo.
echo Getting PostgreSQL runtime information...
ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "Running PostgreSQL Version:"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  if sudo -u postgres psql -t -c "SELECT version();" 2>/dev/null; then' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "\nPostgreSQL Data Directory:"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    sudo -u postgres psql -t -c "SHOW data_directory;" 2>/dev/null || echo "Unable to determine data directory"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  else' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "Unable to connect to PostgreSQL as postgres user"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "Trying as current user..."' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    if psql -t -c "SELECT version();" 2>/dev/null; then' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      echo "\nPostgreSQL Data Directory:"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      psql -t -c "SHOW data_directory;" 2>/dev/null || echo "Unable to determine data directory"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    else' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      echo "Unable to connect to PostgreSQL"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    fi' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  fi' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  echo "psql command not found"' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/get_pg_runtime.sh"
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/get_pg_runtime.sh"

:: Run the script
ssh %SSH_USER%@%GIT_SERVER% "/tmp/get_pg_runtime.sh" > temp.txt
type temp.txt

:: Extract data directory
ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  # Try as postgres user first' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  DATA_DIR=$(sudo -u postgres psql -t -c "SHOW data_directory;" 2>/dev/null)' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  if [ -z "$DATA_DIR" ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    # Try as current user' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    DATA_DIR=$(psql -t -c "SHOW data_directory;" 2>/dev/null)' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  fi' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  if [ -z "$DATA_DIR" ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    # Check common locations' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    if [ -d /var/lib/postgresql/17/main ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      echo "/var/lib/postgresql/17/main"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    elif [ -d /var/lib/postgresql/16/main ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      echo "/var/lib/postgresql/16/main"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    elif [ -d /pgsql/data ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      echo "/pgsql/data"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    else' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '      echo "Unknown"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    fi' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  else' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "$DATA_DIR"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  fi' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  # Check common locations' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  if [ -d /var/lib/postgresql/17/main ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "/var/lib/postgresql/17/main"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  elif [ -d /var/lib/postgresql/16/main ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "/var/lib/postgresql/16/main"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  elif [ -d /pgsql/data ]; then' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "/pgsql/data"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  else' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '    echo "Unknown"' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo '  fi' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/get_pg_datadir.sh"
ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/get_pg_datadir.sh"

:: Run the script
ssh %SSH_USER%@%GIT_SERVER% "/tmp/get_pg_datadir.sh" > temp.txt
set /p pg_data_dir=<temp.txt
echo Preserving temp.txt for reference
echo PostgreSQL data directory: %pg_data_dir%

:: Determine which installation is active based on data directory
if "%pg_data_dir:~0,11%"=="/pgsql/data" (
    echo ACTIVE INSTALLATION: Custom installation in /pgsql/data (likely PostgreSQL 17)
) else if "%pg_data_dir:~0,24%"=="/var/lib/postgresql/17" (
    echo ACTIVE INSTALLATION: PostgreSQL 17 in standard Ubuntu location
) else if "%pg_data_dir:~0,18%"=="/var/lib/postgresql" (
    echo ACTIVE INSTALLATION: Default Ubuntu package installation in %pg_data_dir%
) else if "%pg_data_dir%"=="Unknown" (
    echo ACTIVE INSTALLATION: Could not determine active installation
) else (
    echo ACTIVE INSTALLATION: Unknown location at %pg_data_dir%
)

echo.
if %pg_version_count% GTR 1 (
    echo WARNING: Multiple PostgreSQL versions detected. The active version is shown above.
    echo          Operations will be performed on the ACTIVE installation unless specified.
)

:: Check if PostgreSQL service is running
echo Checking if PostgreSQL service is running...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl is-active postgresql >/dev/null 2>&1 && echo running || echo stopped" > temp.txt
set /p pg_status=<temp.txt
echo Preserving temp.txt for reference

if "%pg_status%"=="stopped" (
    echo PostgreSQL service is not running.
    set /p start_postgres="Start PostgreSQL service? (y/n): "
    if /i "%start_postgres%"=="y" (
        echo Starting PostgreSQL service...
        ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl start postgresql"
        if %ERRORLEVEL% neq 0 (
            echo Failed to start PostgreSQL service.
            exit /b 1
        )
        echo PostgreSQL service started successfully.
    ) else (
        echo PostgreSQL service must be running to continue.
        exit /b 1
    )
)

exit /b 0

:VERIFY_POSTGRESQL
echo.
echo Verifying PostgreSQL installation...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Create a comprehensive verification script
echo Creating PostgreSQL verification script...
ssh %SSH_USER%@%GIT_SERVER% "cat > /tmp/verify_postgresql.sh << 'EOF'
#!/bin/bash

echo \"=== PostgreSQL Installation Check ===\"

# Check if psql exists
if command -v psql >/dev/null; then
  echo \"PostgreSQL client (psql) is installed\"
  echo \"\\nPostgreSQL Version:\"
  psql --version

  # Check for PostgreSQL 17
  if psql --version | grep -q \"17\"; then
    echo \"\\nPostgreSQL 17 detected via psql version\"
  fi
else
  echo \"PostgreSQL client (psql) is not installed or not in PATH\"
  exit 1
fi

echo \"\\n=== PostgreSQL Packages ===\"
echo \"All PostgreSQL packages:\"
dpkg -l postgresql* 2>/dev/null | grep ^ii || echo \"No PostgreSQL packages found\"

echo \"\\nPostgreSQL 17 package:\"
dpkg -l postgresql-17 2>/dev/null | grep ^ii || echo \"PostgreSQL 17 package not found\"

echo \"\\n=== PostgreSQL Data Directories ===\"
echo \"Standard PostgreSQL 17 data directory:\"
ls -la /var/lib/postgresql/17/main 2>/dev/null || echo \"Directory /var/lib/postgresql/17/main not found\"

echo \"\\nStandard PostgreSQL 17 config directory:\"
ls -la /etc/postgresql/17/main 2>/dev/null || echo \"Directory /etc/postgresql/17/main not found\"

echo \"\\n=== PostgreSQL Service Status ===\"
systemctl status postgresql 2>/dev/null || echo \"PostgreSQL service not found\"

echo \"\\n=== PostgreSQL Processes ===\"
ps aux | grep postgres | grep -v grep || echo \"No PostgreSQL processes found\"

echo \"\\n=== PostgreSQL Databases ===\"
sudo -u postgres psql -l 2>/dev/null || echo \"Failed to list PostgreSQL databases\"

echo \"\\n=== PostgreSQL Runtime Information ===\"
echo \"Data Directory:\"
sudo -u postgres psql -t -c \"SHOW data_directory;\" 2>/dev/null || echo \"Failed to get data directory\"

echo \"\\nConfig File:\"
sudo -u postgres psql -t -c \"SHOW config_file;\" 2>/dev/null || echo \"Failed to get config file\"

echo \"\\nPort:\"
sudo -u postgres psql -t -c \"SHOW port;\" 2>/dev/null || echo \"Failed to get port\"

echo \"\\nVersion:\"
sudo -u postgres psql -t -c \"SELECT version();\" 2>/dev/null || echo \"Failed to get version\"
EOF"

ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/verify_postgresql.sh"

:: Run the verification script
echo Running PostgreSQL verification script...
ssh %SSH_USER%@%GIT_SERVER% "/tmp/verify_postgresql.sh"

pause
goto MENU






    if "%pg17_data%"=="pg17_data_exists" (
        echo PostgreSQL 17 data directory detected at /pgsql/data
    )
    echo.
    echo 9.2. Checking default installation directory (/var/lib/postgresql):
    ssh %SSH_USER%@%GIT_SERVER% "ls -la /var/lib/postgresql 2>/dev/null || echo 'Directory /var/lib/postgresql not found or not accessible'"
    echo.
    echo 9.3. Checking PostgreSQL configuration directories:
    ssh %SSH_USER%@%GIT_SERVER% "ls -la /etc/postgresql/* 2>/dev/null || echo 'No PostgreSQL configuration directories found'"

    echo.
    echo 10. Checking which PostgreSQL is actually running:

    :: Create a temporary script on the server to run the psql command
    echo Creating temporary script on server...
    ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/check_pg.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/check_pg.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  sudo -u postgres psql -c \"SELECT version();\"' >> /tmp/check_pg.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/check_pg.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  echo \"psql command not found\"' >> /tmp/check_pg.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/check_pg.sh"
    ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/check_pg.sh"

    :: Run the script
    ssh %SSH_USER%@%GIT_SERVER% "/tmp/check_pg.sh"

    :: Check if PostgreSQL 17 is running
    echo Creating temporary script to check for PostgreSQL 17...
    ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  if sudo -u postgres psql -c \"SELECT version();\" | grep -q \"17\"; then' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '    echo pg17_running' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  else' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '    echo pg17_not_running' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  fi' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  echo pg17_not_running' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/check_pg17.sh"
    ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/check_pg17.sh"

    :: Run the script
    ssh %SSH_USER%@%GIT_SERVER% "/tmp/check_pg17.sh" > temp.txt
    set /p pg17_running=<temp.txt
    echo Preserving temp.txt for reference

    if "%pg17_running%"=="pg17_running" (
        echo PostgreSQL 17 is the currently running version.
    )

    echo.
    echo 11. Checking PostgreSQL installation details:

    :: Create a temporary script to get PostgreSQL settings
    echo Creating temporary script to get PostgreSQL settings...
    ssh %SSH_USER%@%GIT_SERVER% "echo '#!/bin/bash' > /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'if command -v psql >/dev/null; then' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  echo \"Data Directory:\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  sudo -u postgres psql -t -c \"SHOW data_directory;\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  echo \"Config File:\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  sudo -u postgres psql -t -c \"SHOW config_file;\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  echo \"Port:\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  sudo -u postgres psql -t -c \"SHOW port;\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'else' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo '  echo \"Failed to get PostgreSQL settings\"' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "echo 'fi' >> /tmp/check_pg_settings.sh"
    ssh %SSH_USER%@%GIT_SERVER% "chmod +x /tmp/check_pg_settings.sh"

    :: Run the script
    ssh %SSH_USER%@%GIT_SERVER% "/tmp/check_pg_settings.sh"

    echo.
    echo 12. Checking PostgreSQL socket files:
    ssh %SSH_USER%@%GIT_SERVER% "find /var/run/postgresql -name '.s.PGSQL.*' 2>/dev/null || echo 'No PostgreSQL socket files found in /var/run/postgresql'"
    ssh %SSH_USER%@%GIT_SERVER% "find /tmp -name '.s.PGSQL.*' 2>/dev/null || echo 'No PostgreSQL socket files found in /tmp'"
) else (
    echo.
    echo PostgreSQL is not installed on the server.
    echo To install PostgreSQL, select option 9 from the menu.
)

echo.
echo PostgreSQL verification completed.
echo Please review the information above to determine the status of your PostgreSQL installation.
echo.
echo NOTE: If you have multiple PostgreSQL installations, the version shown in
echo       'Checking which PostgreSQL is actually running' indicates which one
echo       is currently active and being used by the system.

pause
goto MENU

:EXAMINE_SCHEMA
echo.
echo Examining PostgreSQL Schema...

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check PostgreSQL installation and service status
call :CHECK_POSTGRESQL
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: If multiple PostgreSQL versions are installed, ask which one to use
if %pg_version_count% GTR 1 (
    echo.
    echo Multiple PostgreSQL installations detected:
    type pg_versions.txt
    echo.
    echo Current active installation: %pg_data_dir%
    echo.
    set /p use_active="Use the active PostgreSQL installation? (Y/n): "

    if /i "%use_active%"=="n" (
        echo.
        echo Available PostgreSQL data directories:
        ssh %SSH_USER%@%GIT_SERVER% "find /var/lib/postgresql /pgsql -name data -type d 2>/dev/null || echo 'No PostgreSQL data directories found'"
        echo.
        set /p custom_data_dir="Enter the full path to the PostgreSQL data directory to use: "

        if not "%custom_data_dir%"=="" (
            echo Using custom PostgreSQL data directory: %custom_data_dir%
            set pg_data_dir=%custom_data_dir%
        ) else (
            echo No custom data directory specified. Using active installation: %pg_data_dir%
        )
    )
)

echo.
echo 1. Listing all PostgreSQL databases (from %pg_data_dir%):
echo ---------------------------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\l'"
echo.

echo 2. Please enter the database name to examine:
set /p db_name="Database name: "

if "%db_name%"=="" (
    echo No database name provided. Exiting.
    pause
    goto MENU
)

echo.
echo 3. Checking if database exists:
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw %db_name% && echo exists || echo notfound" > temp.txt
set /p db_exists=<temp.txt
echo Preserving temp.txt for reference

if "%db_exists%"=="notfound" (
    echo Database '%db_name%' does not exist.
    pause
    goto MENU
)

echo.
echo 4. Listing schemas in database '%db_name%':
echo ---------------------------------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\dn'"
echo.

echo 5. Listing tables in database '%db_name%':
echo -------------------------------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\dt'"
echo.

echo 6. Detailed table information:
echo --------------------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\d+'"
echo.

echo 7. Database size information:
echo -------------------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c \"SELECT pg_size_pretty(pg_database_size('%db_name%')) AS db_size;\""
echo.

echo 8. Table row counts:
echo ----------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c \"SELECT schemaname, relname, n_live_tup FROM pg_stat_user_tables ORDER BY n_live_tup DESC;\""
echo.

echo 9. Database version information:
echo -----------------------------
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c \"SELECT version();\""
echo.

echo Schema examination completed.

pause
goto MENU

:UNINSTALL_POSTGRESQL
echo.
echo WARNING: This will completely uninstall PostgreSQL from the server!
echo All databases and data will be permanently deleted!
echo.

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Verify PostgreSQL is installed
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql*' | grep -E '^ii' > /dev/null && echo installed || echo not_installed" > temp.txt
set /p pg_installed=<temp.txt
echo Preserving temp.txt for reference

if "%pg_installed%"=="not_installed" (
    echo PostgreSQL is not installed on the server.
    pause
    goto MENU
)

:: Show installed PostgreSQL packages
echo The following PostgreSQL packages are installed:
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql*' | grep '^ii'"
echo.

:: Check for multiple PostgreSQL installations
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-[0-9]*' | grep '^ii' | awk '{print $2, $3}'" > pg_versions.txt
type pg_versions.txt

:: Count installed versions
ssh %SSH_USER%@%GIT_SERVER% "dpkg -l 'postgresql-[0-9]*' | grep '^ii' | wc -l" > temp.txt
set /p pg_version_count=<temp.txt
echo Preserving temp.txt for reference

:: Check data directories
echo.
echo PostgreSQL data directories found:
ssh %SSH_USER%@%GIT_SERVER% "find /var/lib/postgresql /pgsql -name data -type d 2>/dev/null || echo 'No PostgreSQL data directories found'"
echo.

if %pg_version_count% GTR 1 (
    echo WARNING: Multiple PostgreSQL versions detected.
    echo.
    set /p uninstall_all="Uninstall ALL PostgreSQL versions? (y/n): "

    if /i not "%uninstall_all%"=="y" (
        echo.
        echo Available PostgreSQL versions:
        type pg_versions.txt
        echo.
        set /p specific_version="Enter the specific PostgreSQL version to uninstall (e.g., postgresql-17): "

        if not "%specific_version%"=="" (
            echo.
            echo Will uninstall only: %specific_version%
            set uninstall_cmd="sudo apt-get purge -y %specific_version%* && sudo apt-get autoremove -y"
        ) else (
            echo No specific version specified. Will uninstall ALL PostgreSQL versions.
            set uninstall_cmd="sudo apt-get purge -y postgresql* && sudo apt-get autoremove -y"
        )
    ) else (
        echo Will uninstall ALL PostgreSQL versions.
        set uninstall_cmd="sudo apt-get purge -y postgresql* && sudo apt-get autoremove -y"
    )
) else (
    echo Will uninstall PostgreSQL.
    set uninstall_cmd="sudo apt-get purge -y postgresql* && sudo apt-get autoremove -y"
)
echo.

:: Confirm uninstallation
set /p confirm="Are you sure you want to uninstall PostgreSQL? This cannot be undone. (y/n): "
if /i not "%confirm%"=="y" (
    echo PostgreSQL uninstallation cancelled.
    pause
    goto MENU
)

:: Double confirm for safety
set /p confirm2="Type 'UNINSTALL' to confirm PostgreSQL uninstallation: "
if not "%confirm2%"=="UNINSTALL" (
    echo PostgreSQL uninstallation cancelled.
    pause
    goto MENU
)

:: Stop PostgreSQL service
echo Stopping PostgreSQL service...
ssh %SSH_USER%@%GIT_SERVER% "sudo systemctl stop postgresql"

:: Uninstall PostgreSQL
echo Uninstalling PostgreSQL...
ssh %SSH_USER%@%GIT_SERVER% %uninstall_cmd%

:: Remove data directories
echo Removing PostgreSQL data directories...
ssh %SSH_USER%@%GIT_SERVER% "echo SAFETY: Not removing PostgreSQL directories"

echo PostgreSQL has been completely uninstalled from the server.
pause
goto MENU

:DELETE_DATABASE_AND_SCHEMA
echo.
echo WARNING: This will permanently delete a database and all its schemas!
echo.

:: Check SSH connection
call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: Check PostgreSQL installation and service status
call :CHECK_POSTGRESQL
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

:: If multiple PostgreSQL versions are installed, ask which one to use
if %pg_version_count% GTR 1 (
    echo.
    echo Multiple PostgreSQL installations detected:
    type pg_versions.txt
    echo.
    echo Current active installation: %pg_data_dir%
    echo.
    set /p use_active="Use the active PostgreSQL installation? (Y/n): "

    if /i "%use_active%"=="n" (
        echo.
        echo Available PostgreSQL data directories:
        ssh %SSH_USER%@%GIT_SERVER% "find /var/lib/postgresql /pgsql -name data -type d 2>/dev/null || echo 'No PostgreSQL data directories found'"
        echo.
        set /p custom_data_dir="Enter the full path to the PostgreSQL data directory to use: "

        if not "%custom_data_dir%"=="" (
            echo Using custom PostgreSQL data directory: %custom_data_dir%
            set pg_data_dir=%custom_data_dir%
        ) else (
            echo No custom data directory specified. Using active installation: %pg_data_dir%
        )
    )
)

:: List available databases
echo Available databases (from %pg_data_dir%):
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c '\l'"
echo.

:: Get database name
set /p db_name="Enter database name to delete: "
if "%db_name%"=="" (
    echo Database name cannot be empty.
    pause
    goto MENU
)

:: Check if database exists
echo Checking if database exists...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw %db_name% && echo exists || echo notfound" > temp.txt
set /p db_exists=<temp.txt
echo Preserving temp.txt for reference

if "%db_exists%"=="notfound" (
    echo Database '%db_name%' does not exist.
    pause
    goto MENU
)

:: Show database details
echo Database details:
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c \"SELECT pg_size_pretty(pg_database_size('%db_name%')) AS size;\""

:: List schemas in the database
echo Schemas in database '%db_name%':
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\dn'"

:: List tables in the database
echo Tables in database '%db_name%':
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -d %db_name% -c '\dt'"

:: Confirm deletion
set /p confirm="Are you sure you want to delete database '%db_name%' and all its schemas? This cannot be undone. (y/n): "
if /i not "%confirm%"=="y" (
    echo Database deletion cancelled.
    pause
    goto MENU
)

:: Double confirm for safety
set /p confirm2="Type the database name again to confirm deletion: "
if not "%confirm2%"=="%db_name%" (
    echo Database deletion cancelled - names did not match.
    pause
    goto MENU
)

:: Terminate all connections to the database
echo Terminating all connections to database '%db_name%'...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c \"SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%db_name%' AND pid <> pg_backend_pid();\""

:: Delete database
echo Deleting database '%db_name%'...
ssh %SSH_USER%@%GIT_SERVER% "sudo -u postgres psql -c 'DROP DATABASE \"%db_name%\" WITH (FORCE);'"

echo Database '%db_name%' and all its schemas have been permanently deleted.
pause
goto MENU

:FULL_DEPLOY
echo.
echo Running full deployment...

call :CHECK_SSH
if %ERRORLEVEL% neq 0 (
    pause
    goto MENU
)

call :INSTALL_DEPS
call :DEPLOY_CODE
call :BUILD_APP
call :SETUP_DATABASE
call :CONFIGURE_STORAGE
call :INSTALL_SERVICE
call :START_SERVICE
call :CHECK_STATUS

echo Full deployment completed.
pause
goto MENU

:EXIT
echo.
echo Exiting...
exit /b 0

