-- Add indexes for commonly queried columns
CREATE INDEX IF NOT EXISTS idx_improvements_status 
    ON improvements(status);

CREATE INDEX IF NOT EXISTS idx_improvements_project_status 
    ON improvements(project_id, status);

CREATE INDEX IF NOT EXISTS idx_improvements_created_at 
    ON improvements(created_at DESC);

-- Add partial index for active improvements
CREATE INDEX IF NOT EXISTS idx_active_improvements 
    ON improvements(project_id, status, priority) 
    WHERE status IN ('open', 'in_progress');

-- Add GiST index for full text search
CREATE INDEX IF NOT EXISTS idx_improvements_search 
    ON improvements 
    USING gin(to_tsvector('english', description || ' ' || title));

-- Add index for performance monitoring
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp 
    ON performance_metrics(timestamp DESC) 
    INCLUDE (operation_type, duration);