#!/bin/bash
# Master Installation Script for Certificate Sync Fix
# This script runs all component scripts in the correct order
# Run this script as root

# Color codes for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}==============================================${NC}"
echo -e "${BLUE}   Certificate Sync Fix - Master Installer   ${NC}"
echo -e "${BLUE}==============================================${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}This script must be run as root${NC}"
    exit 1
fi

# Get the directory where this script is located
SCRIPT_DIR=$(dirname "$0")
cd "$SCRIPT_DIR" || { echo -e "${RED}Error: Could not change to script directory${NC}"; exit 1; }

# Check if all required component scripts exist
MISSING_SCRIPTS=0

check_script() {
    if [ ! -f "$1" ]; then
        echo -e "${RED}Error: Required script $1 not found${NC}"
        MISSING_SCRIPTS=1
    else
        # Make sure it's executable
        chmod +x "$1"
    fi
}

check_script "cpp-helper-installer.sh"
check_script "update-sync-script.sh"
check_script "update-flask-api.sh"
check_script "update-js-client.sh"

if [ $MISSING_SCRIPTS -eq 1 ]; then
    echo -e "${RED}Error: One or more required scripts are missing.${NC}"
    echo "Make sure all component scripts are in the same directory as this master script."
    exit 1
fi

# Function to run a script and check its exit status
run_script() {
    echo -e "\n${BLUE}==============================================${NC}"
    echo -e "${BLUE}   Running: $1${NC}"
    echo -e "${BLUE}==============================================${NC}"
    
    "./$1"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Script $1 failed${NC}"
        if [ "$2" = "critical" ]; then
            echo -e "${RED}This is a critical error. Stopping installation.${NC}"
            exit 1
        else
            echo -e "${YELLOW}Continuing with the next steps, but some functionality may be missing.${NC}"
        fi
    else
        echo -e "${GREEN}Script $1 completed successfully${NC}"
    fi
}

# Run all component scripts in the correct order
run_script "cpp-helper-installer.sh" "critical"
run_script "update-sync-script.sh" "non-critical"
run_script "update-flask-api.sh" "non-critical"
run_script "update-js-client.sh" "non-critical"

# Final report
echo -e "\n${BLUE}==============================================${NC}"
echo -e "${GREEN}Certificate Sync Fix installation complete!${NC}"
echo -e "${BLUE}==============================================${NC}"
echo -e "${YELLOW}Key components installed:${NC}"
echo "- C++ Helper: /opt/git-dashboard/bin/cert_sync_helper"
echo "- Sync Script: /opt/git-dashboard/sync-certificates.sh"
echo "- Flask API endpoint updated"
echo "- JavaScript client updated"
echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Test the certificate sync using the dashboard button"
echo "2. Verify that the cron job still works correctly"
echo "3. Check logs at /opt/git-dashboard/logs/cert-sync.log if issues occur"
echo -e "\n${GREEN}Thank you for installing the Certificate Sync Fix!${NC}"
