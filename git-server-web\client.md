# Database Service Client Library

This document provides documentation for the Database Service client library, which allows applications to interact with the Database Service.

## Overview

The Database Service client library provides a high-level API for interacting with the Database Service. It handles:

- Connection management
- Authentication
- Request/response serialization
- Error handling
- Retries and circuit breaking

## Client Libraries

The Database Service provides client libraries for multiple languages:

- **C++**: Native C++23 client library
- **Python**: Python client library
- **JavaScript**: Node.js client library

This document focuses on the C++23 client library.

## C++23 Client Library

### Installation

The C++23 client library is included with the Database Service source code in the `client` directory. To use it in your project:

1. Include the client library in your CMake project:

```cmake
# Add the database service client library
add_subdirectory(path/to/database-service/client)

# Link against the client library
target_link_libraries(your_target PRIVATE dbservice_client)
```

2. Import the client module in your code:

```cpp
import dbservice.client;
```

### Basic Usage

```cpp
#include <iostream>
import dbservice.client;

int main() {
    // Create a client configuration
    dbservice::client::ClientConfig config;
    config.serverUrl = "https://git.chcit.org/api/database/v1";
    config.apiKey = "your-api-key";
    
    // Create a client
    auto client = dbservice::client::DatabaseClient::create(config);
    
    try {
        // List databases
        auto databases = client->listDatabases();
        
        std::cout << "Available databases:" << std::endl;
        for (const auto& db : databases) {
            std::cout << "- " << db.name << std::endl;
        }
        
        // Execute a query
        auto result = client->executeQuery("git_dashboard", 
            "SELECT * FROM git_repos.repositories LIMIT 5");
        
        std::cout << "Query results:" << std::endl;
        for (const auto& row : result.rows) {
            std::cout << "- " << row[1].as<std::string>() << std::endl;
        }
    } catch (const dbservice::client::DatabaseError& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

### Asynchronous Usage with Coroutines

```cpp
import dbservice.client;
import std.core;

// Asynchronous function using C++23 coroutines
task<void> listDatabasesAsync() {
    // Create a client configuration
    dbservice::client::ClientConfig config;
    config.serverUrl = "https://git.chcit.org/api/database/v1";
    config.apiKey = "your-api-key";
    
    // Create a client
    auto client = dbservice::client::DatabaseClient::create(config);
    
    try {
        // List databases asynchronously
        auto databases = co_await client->listDatabasesAsync();
        
        std::cout << "Available databases:" << std::endl;
        for (const auto& db : databases) {
            std::cout << "- " << db.name << std::endl;
        }
    } catch (const dbservice::client::DatabaseError& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }
}

int main() {
    // Run the asynchronous function
    auto task = listDatabasesAsync();
    task.wait();
    return 0;
}
```

### Client Configuration

The `ClientConfig` class provides configuration options for the client:

```cpp
struct ClientConfig {
    // Server URL
    std::string serverUrl = "https://git.chcit.org/api/database/v1";
    
    // Authentication
    std::string apiKey;
    std::string jwtToken;
    
    // Connection settings
    int connectionTimeoutMs = 5000;
    int requestTimeoutMs = 30000;
    
    // Retry settings
    int maxRetries = 3;
    int retryDelayMs = 1000;
    bool exponentialBackoff = true;
    
    // Circuit breaker settings
    bool circuitBreakerEnabled = true;
    int circuitBreakerThreshold = 5;
    int circuitBreakerResetTimeMs = 30000;
    
    // SSL settings
    bool sslVerify = true;
    std::string sslCertFile;
    std::string sslKeyFile;
    std::string sslCaFile;
};
```

### Error Handling

The client library throws exceptions for errors:

```cpp
try {
    auto result = client->executeQuery("git_dashboard", 
        "SELECT * FROM git_repos.repositories LIMIT 5");
} catch (const dbservice::client::ConnectionError& e) {
    // Connection error
    std::cerr << "Connection error: " << e.what() << std::endl;
} catch (const dbservice::client::AuthenticationError& e) {
    // Authentication error
    std::cerr << "Authentication error: " << e.what() << std::endl;
} catch (const dbservice::client::QueryError& e) {
    // Query error
    std::cerr << "Query error: " << e.what() << std::endl;
    std::cerr << "SQL state: " << e.sqlState() << std::endl;
} catch (const dbservice::client::DatabaseError& e) {
    // Generic database error
    std::cerr << "Database error: " << e.what() << std::endl;
}
```

### API Reference

#### Database Management

```cpp
// List all databases
std::vector<Database> listDatabases();
task<std::vector<Database>> listDatabasesAsync();

// Create a database
Database createDatabase(const std::string& name, const std::string& owner = "postgres");
task<Database> createDatabaseAsync(const std::string& name, const std::string& owner = "postgres");

// Get database information
Database getDatabase(const std::string& name);
task<Database> getDatabaseAsync(const std::string& name);

// Drop a database
void dropDatabase(const std::string& name);
task<void> dropDatabaseAsync(const std::string& name);
```

#### Schema Management

```cpp
// List all schemas in a database
std::vector<Schema> listSchemas(const std::string& database);
task<std::vector<Schema>> listSchemasAsync(const std::string& database);

// Create a schema
Schema createSchema(const std::string& database, const std::string& name, 
                   const std::string& owner = "postgres");
task<Schema> createSchemaAsync(const std::string& database, const std::string& name, 
                              const std::string& owner = "postgres");

// Get schema information
Schema getSchema(const std::string& database, const std::string& name);
task<Schema> getSchemaAsync(const std::string& database, const std::string& name);

// Drop a schema
void dropSchema(const std::string& database, const std::string& name);
task<void> dropSchemaAsync(const std::string& database, const std::string& name);
```

#### Schema Migration

```cpp
// Get current schema version
SchemaVersion getCurrentVersion(const std::string& database, const std::string& schema);
task<SchemaVersion> getCurrentVersionAsync(const std::string& database, const std::string& schema);

// List available migrations
std::vector<Migration> getAvailableMigrations(const std::string& database, const std::string& schema);
task<std::vector<Migration>> getAvailableMigrationsAsync(const std::string& database, 
                                                        const std::string& schema);

// Apply migration
SchemaVersion applyMigration(const std::string& database, const std::string& schema, 
                            const std::string& targetVersion);
task<SchemaVersion> applyMigrationAsync(const std::string& database, const std::string& schema, 
                                       const std::string& targetVersion);
```

#### Query Execution

```cpp
// Execute a query
QueryResult executeQuery(const std::string& database, const std::string& query, 
                        const std::vector<QueryParameter>& parameters = {});
task<QueryResult> executeQueryAsync(const std::string& database, const std::string& query, 
                                   const std::vector<QueryParameter>& parameters = {});

// Execute a query with a single result
template<typename T>
T executeScalar(const std::string& database, const std::string& query, 
               const std::vector<QueryParameter>& parameters = {});

template<typename T>
task<T> executeScalarAsync(const std::string& database, const std::string& query, 
                          const std::vector<QueryParameter>& parameters = {});

// Execute a non-query command
int executeNonQuery(const std::string& database, const std::string& query, 
                   const std::vector<QueryParameter>& parameters = {});
task<int> executeNonQueryAsync(const std::string& database, const std::string& query, 
                              const std::vector<QueryParameter>& parameters = {});
```

### Data Types

The client library provides classes for working with database objects:

```cpp
// Database information
struct Database {
    std::string name;
    std::string owner;
    std::string encoding;
    std::string size;
    std::string createdAt;
    std::vector<std::string> schemas;
};

// Schema information
struct Schema {
    std::string name;
    std::string owner;
    std::string createdAt;
    int tables;
};

// Schema version information
struct SchemaVersion {
    std::string version;
    std::string description;
    std::string appliedAt;
};

// Migration information
struct Migration {
    std::string version;
    std::string description;
    std::string file;
};

// Query parameter
class QueryParameter {
public:
    // Create parameters of different types
    static QueryParameter fromString(const std::string& value);
    static QueryParameter fromInt(int value);
    static QueryParameter fromDouble(double value);
    static QueryParameter fromBool(bool value);
    static QueryParameter fromNull();
    static QueryParameter fromBinary(const std::vector<uint8_t>& value);
    
    // Get parameter value
    template<typename T>
    T as() const;
};

// Query result
class QueryResult {
public:
    // Get column names
    const std::vector<std::string>& columns() const;
    
    // Get rows
    const std::vector<std::vector<QueryParameter>>& rows() const;
    
    // Get row count
    int rowCount() const;
    
    // Get execution time
    int executionTimeMs() const;
};
```

## Integration Examples

### Git Dashboard Integration

```cpp
#include <iostream>
import dbservice.client;

// Function to get repository information
std::vector<Repository> getRepositories() {
    // Create a client
    dbservice::client::ClientConfig config;
    config.serverUrl = "https://git.chcit.org/api/database/v1";
    config.apiKey = "your-api-key";
    
    auto client = dbservice::client::DatabaseClient::create(config);
    
    // Execute query to get repositories
    auto result = client->executeQuery("git_dashboard", 
        "SELECT id, name, url, description, created_at FROM git_repos.repositories");
    
    // Convert query result to Repository objects
    std::vector<Repository> repositories;
    for (const auto& row : result.rows) {
        Repository repo;
        repo.id = row[0].as<int>();
        repo.name = row[1].as<std::string>();
        repo.url = row[2].as<std::string>();
        repo.description = row[3].as<std::string>();
        repo.createdAt = row[4].as<std::string>();
        repositories.push_back(repo);
    }
    
    return repositories;
}

// Function to get commit history for a repository
std::vector<Commit> getCommits(int repositoryId, int limit = 10) {
    // Create a client
    dbservice::client::ClientConfig config;
    config.serverUrl = "https://git.chcit.org/api/database/v1";
    config.apiKey = "your-api-key";
    
    auto client = dbservice::client::DatabaseClient::create(config);
    
    // Execute query to get commits
    auto result = client->executeQuery("git_dashboard", 
        "SELECT id, hash, author, message, committed_at FROM git_repos.commits "
        "WHERE repository_id = $1 ORDER BY committed_at DESC LIMIT $2",
        {
            QueryParameter::fromInt(repositoryId),
            QueryParameter::fromInt(limit)
        });
    
    // Convert query result to Commit objects
    std::vector<Commit> commits;
    for (const auto& row : result.rows) {
        Commit commit;
        commit.id = row[0].as<int>();
        commit.hash = row[1].as<std::string>();
        commit.author = row[2].as<std::string>();
        commit.message = row[3].as<std::string>();
        commit.committedAt = row[4].as<std::string>();
        commits.push_back(commit);
    }
    
    return commits;
}
```

### Logging Service Integration

```cpp
#include <iostream>
import dbservice.client;

// Function to log a message
void logMessage(const std::string& level, const std::string& message, 
               const std::string& source = "application") {
    // Create a client
    dbservice::client::ClientConfig config;
    config.serverUrl = "https://git.chcit.org/api/database/v1";
    config.apiKey = "your-api-key";
    
    auto client = dbservice::client::DatabaseClient::create(config);
    
    try {
        // Insert log message
        client->executeNonQuery("logging", 
            "INSERT INTO logs.messages (level, message, source, created_at) "
            "VALUES ($1, $2, $3, NOW())",
            {
                QueryParameter::fromString(level),
                QueryParameter::fromString(message),
                QueryParameter::fromString(source)
            });
    } catch (const dbservice::client::DatabaseError& e) {
        // Log to console if database logging fails
        std::cerr << "Failed to log to database: " << e.what() << std::endl;
        std::cerr << level << ": " << message << " [" << source << "]" << std::endl;
    }
}

// Convenience functions for different log levels
void logDebug(const std::string& message, const std::string& source = "application") {
    logMessage("DEBUG", message, source);
}

void logInfo(const std::string& message, const std::string& source = "application") {
    logMessage("INFO", message, source);
}

void logWarning(const std::string& message, const std::string& source = "application") {
    logMessage("WARNING", message, source);
}

void logError(const std::string& message, const std::string& source = "application") {
    logMessage("ERROR", message, source);
}
```

## Advanced Features

### Connection Pooling

The client library automatically manages a connection pool:

```cpp
// Configure connection pool
dbservice::client::ClientConfig config;
config.connectionPoolSize = 10;
config.connectionIdleTimeoutMs = 60000;
config.connectionMaxLifetimeMs = 3600000;

auto client = dbservice::client::DatabaseClient::create(config);
```

### Transactions

The client library supports transactions:

```cpp
// Start a transaction
auto transaction = client->beginTransaction("git_dashboard");

try {
    // Execute multiple queries in a transaction
    transaction->executeNonQuery(
        "INSERT INTO git_repos.repositories (name, url) VALUES ($1, $2)",
        {
            QueryParameter::fromString("new-repo"),
            QueryParameter::fromString("**************:example/new-repo.git")
        });
    
    transaction->executeNonQuery(
        "INSERT INTO git_repos.branches (repository_id, name) VALUES ($1, $2)",
        {
            QueryParameter::fromInt(1),
            QueryParameter::fromString("main")
        });
    
    // Commit the transaction
    transaction->commit();
} catch (const std::exception& e) {
    // Rollback the transaction on error
    transaction->rollback();
    throw;
}
```

### Batch Operations

The client library supports batch operations for improved performance:

```cpp
// Create a batch
auto batch = client->createBatch("git_dashboard");

// Add multiple operations to the batch
batch->add("INSERT INTO git_repos.repositories (name, url) VALUES ($1, $2)",
          {
              QueryParameter::fromString("repo1"),
              QueryParameter::fromString("**************:example/repo1.git")
          });

batch->add("INSERT INTO git_repos.repositories (name, url) VALUES ($1, $2)",
          {
              QueryParameter::fromString("repo2"),
              QueryParameter::fromString("**************:example/repo2.git")
          });

// Execute the batch
auto results = batch->execute();

// Process results
for (const auto& result : results) {
    if (result.isSuccess()) {
        std::cout << "Rows affected: " << result.rowsAffected() << std::endl;
    } else {
        std::cerr << "Error: " << result.errorMessage() << std::endl;
    }
}
```

### Monitoring and Metrics

The client library provides monitoring and metrics:

```cpp
// Get client metrics
auto metrics = client->getMetrics();

std::cout << "Total requests: " << metrics.totalRequests << std::endl;
std::cout << "Successful requests: " << metrics.successfulRequests << std::endl;
std::cout << "Failed requests: " << metrics.failedRequests << std::endl;
std::cout << "Average response time: " << metrics.averageResponseTimeMs << " ms" << std::endl;
std::cout << "Active connections: " << metrics.activeConnections << std::endl;
std::cout << "Idle connections: " << metrics.idleConnections << std::endl;
```

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Check server URL and network connectivity
   - Verify SSL certificate configuration
   - Check firewall settings

2. **Authentication Errors**:
   - Verify API key or JWT token
   - Check token expiration
   - Verify permissions

3. **Query Errors**:
   - Check SQL syntax
   - Verify database and schema names
   - Check parameter types and values

### Logging

Enable client logging for troubleshooting:

```cpp
// Enable debug logging
dbservice::client::ClientConfig config;
config.logLevel = dbservice::client::LogLevel::Debug;
config.logFile = "database_client.log";

auto client = dbservice::client::DatabaseClient::create(config);
```

### Diagnostic Information

Get diagnostic information from the client:

```cpp
// Get diagnostic information
auto diagnostics = client->getDiagnosticInfo();

std::cout << "Client version: " << diagnostics.clientVersion << std::endl;
std::cout << "Server version: " << diagnostics.serverVersion << std::endl;
std::cout << "Connection status: " << diagnostics.connectionStatus << std::endl;
std::cout << "Last request time: " << diagnostics.lastRequestTime << std::endl;
std::cout << "Last error: " << diagnostics.lastError << std::endl;
```

## Related Documentation

- [API Reference](./api.md)
- [Architecture and Design](./architecture-and-design.md)
- [Configuration Validation](./configuration-validation.md)
