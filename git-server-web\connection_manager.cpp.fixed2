#include "database-service/core/connection_manager.hpp"
#include <pqxx/pqxx>
#include <iostream>
#include <stdexcept>
#include <chrono>
#include <thread>

// Import the core module for Task type
import database.core;

namespace dbservice {
namespace core {

ConnectionManager::ConnectionManager(const std::string& connectionString,
                                   size_t poolSize,
                                   bool useSSL)
    : connectionString_(connectionString),
      poolSize_(poolSize),
      useSSL_(useSSL),
      shutdown_(false) {

    // Initialize SSL configuration if enabled
    if (useSSL_) {
        // Default paths for SSL certificates
        sslCertPath_ = "/etc/letsencrypt/live/chcit.org/fullchain.pem";
        sslKeyPath_ = "/etc/letsencrypt/live/chcit.org/privkey.pem";
        sslCaPath_ = "/etc/letsencrypt/live/chcit.org/chain.pem";
    }

    // Initialize connection pool
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        for (size_t i = 0; i < poolSize_; ++i) {
            try {
                auto conn = createConnection();
                availableConnections_.push_back(conn);
            } catch (const std::exception& e) {
                std::cerr << "Error creating connection " << i << ": " << e.what() << std::endl;
                // Continue trying to create other connections
            }
        }

        if (availableConnections_.empty()) {
            throw std::runtime_error("Failed to create any database connections");
        }

        std::cout << "Connection pool initialized with " << availableConnections_.size() 
                  << " connections (requested " << poolSize_ << ")" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error initializing connection pool: " << e.what() << std::endl;
        throw;
    }
}

ConnectionManager::~ConnectionManager() {
    shutdown();
}

std::shared_ptr<pqxx::connection> ConnectionManager::getConnection() {
    std::unique_lock<std::mutex> lock(mutex_);

    // Wait for available connection with timeout
    if (!cv_.wait_for(lock, std::chrono::seconds(30), [this] {
        return !availableConnections_.empty() || shutdown_;
    })) {
        throw std::runtime_error("Timeout waiting for database connection");
    }

    if (shutdown_) {
        throw std::runtime_error("Connection manager is shutting down");
    }

    // Get connection from pool
    auto conn = availableConnections_.back();
    availableConnections_.pop_back();

    // Add to active connections
    activeConnections_[conn.get()] = conn;

    // Return connection with custom deleter to return it to the pool
    return std::shared_ptr<pqxx::connection>(conn.get(),
        [this](pqxx::connection* conn) {
            this->returnConnection(conn);
        });
}

// Implementation of getConnectionAsync
Task<std::shared_ptr<pqxx::connection>> ConnectionManager::getConnectionAsync() {
    try {
        // This is a coroutine function that wraps the synchronous getConnection
        auto connection = getConnection();
        co_return Result<std::shared_ptr<pqxx::connection>>{
            std::move(connection), std::nullopt
        };
    } catch (const std::exception& e) {
        co_return Result<std::shared_ptr<pqxx::connection>>{
            std::nullopt, e.what()
        };
    } catch (...) {
        co_return Result<std::shared_ptr<pqxx::connection>>{
            std::nullopt, "Unknown error getting connection"
        };
    }
}

bool ConnectionManager::isAvailable() {
    try {
        auto conn = getConnection();
        return conn->is_open();
    } catch (const std::exception& e) {
        std::cerr << "Database is not available: " << e.what() << std::endl;
        return false;
    }
}

// Implementation of isAvailableAsync
Task<bool> ConnectionManager::isAvailableAsync() {
    try {
        auto connResult = co_await getConnectionAsync();
        if (connResult.failure()) {
            co_return Result<bool>{false, connResult.error};
        }

        bool is_open = (*connResult)->is_open();
        co_return Result<bool>{is_open, std::nullopt};
    } catch (const std::exception& e) {
        co_return Result<bool>{false, e.what()};
    } catch (...) {
        co_return Result<bool>{false, "Unknown error checking availability"};
    }
}

std::string ConnectionManager::getDatabaseVersion() {
    try {
        return executeWithConnection([](pqxx::connection& conn) {
            pqxx::work txn(conn);
            pqxx::result res = txn.exec("SELECT version()");
            txn.commit();
            return res[0][0].as<std::string>();
        });
    } catch (const std::exception& e) {
        std::cerr << "Error getting database version: " << e.what() << std::endl;
        return "Unknown";
    }
}

// Implementation of getDatabaseVersionAsync
Task<std::string> ConnectionManager::getDatabaseVersionAsync() {
    try {
        auto result = co_await executeWithConnectionAsync([](pqxx::connection& conn) {
            pqxx::work txn(conn);
            pqxx::result res = txn.exec("SELECT version()");
            txn.commit();
            return res[0][0].as<std::string>();
        });

        if (result.failure()) {
            co_return Result<std::string>{"Unknown", result.error};
        }

        co_return Result<std::string>{*result, std::nullopt};
    } catch (const std::exception& e) {
        co_return Result<std::string>{"Unknown", e.what()};
    } catch (...) {
        co_return Result<std::string>{"Unknown", "Unknown error getting database version"};
    }
}

void ConnectionManager::shutdown() {
    std::unique_lock<std::mutex> lock(mutex_);

    if (shutdown_) {
        return;
    }

    shutdown_ = true;
    cv_.notify_all();

    // Close all connections
    availableConnections_.clear();
    activeConnections_.clear();

    std::cout << "Connection manager shut down" << std::endl;
}

std::shared_ptr<pqxx::connection> ConnectionManager::createConnection() {
    try {
        // Build connection string with SSL parameters if enabled
        std::string connStr = buildConnectionString();

        // Create new connection
        auto conn = std::make_shared<pqxx::connection>(connStr);

        // Test connection
        if (!conn->is_open()) {
            throw std::runtime_error("Failed to open database connection");
        }

        return conn;
    } catch (const std::exception& e) {
        std::cerr << "Error creating database connection: " << e.what() << std::endl;
        throw;
    }
}

void ConnectionManager::returnConnection(pqxx::connection* conn) {
    std::unique_lock<std::mutex> lock(mutex_);

    // Find the shared_ptr for this connection
    auto it = activeConnections_.find(conn);
    if (it == activeConnections_.end()) {
        std::cerr << "Attempt to return unknown connection to the pool" << std::endl;
        return;
    }

    // Check if connection is still valid
    if (conn->is_open()) {
        // Return to available pool
        availableConnections_.push_back(it->second);
    } else {
        // Create a new connection to replace the broken one
        try {
            availableConnections_.push_back(createConnection());
        } catch (const std::exception& e) {
            std::cerr << "Failed to replace broken connection: " << e.what() << std::endl;
        }
    }

    // Remove from active connections
    activeConnections_.erase(it);

    // Notify waiting threads
    cv_.notify_one();
}

std::string ConnectionManager::buildConnectionString() {
    // Start with the base connection string
    std::string connStr = connectionString_;

    // Add SSL parameters if enabled
    if (useSSL_) {
        // Check if connection string already has parameters
        if (connStr.find("?") == std::string::npos) {
            connStr += "?";
        } else {
            connStr += "&";
        }

        connStr += "sslmode=verify-full";
        connStr += "&sslcert=" + sslCertPath_;
        connStr += "&sslkey=" + sslKeyPath_;
        connStr += "&sslrootcert=" + sslCaPath_;
    }

    return connStr;
}

} // namespace core
} // namespace dbservice
