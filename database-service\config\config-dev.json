{"database": {"host": "localhost", "port": 5432, "name": "database_service_dev", "user": "postgres", "password": "postgres", "ssl": {"enabled": false, "note": "SSL disabled for development"}, "pool": {"max_connections": 5}}, "api": {"port": 8080, "host": "127.0.0.1", "base_path": "/api", "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowed_headers": ["Content-Type", "Authorization"], "allow_credentials": true, "max_age": 86400}, "ssl": {"enabled": false, "note": "SSL termination handled by Nginx reverse proxy in production"}}, "security": {"enable_authentication": true, "jwt_secret": "development-secret-change-in-production", "token_expiration_seconds": 3600, "refresh_token_expiration_seconds": 86400, "password_hash_algorithm": "bcrypt", "password_hash_cost": 10, "secure_credential_storage": {"enabled": false, "note": "Disabled for development"}}, "logging": {"enabled": true, "level": "debug", "file": "database-service-dev.log", "max_size_mb": 10, "max_files": 3, "console": true}, "schema": {"directory": "schemas", "auto_migrate": true, "version_table": "schema_version"}, "metrics": {"enabled": true, "collection_interval_seconds": 30, "retention_days": 1}}