#include "file_storage.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <algorithm>
#include <jsoncpp/json/json.h>

namespace fs = std::filesystem;
namespace logging {

FileStorage::FileStorage(const std::string& baseDir, bool enableCompression, int compressionAgeDays)
    : baseDir_(baseDir), enableCompression_(enableCompression), compressionAgeDays_(compressionAgeDays) {
    
    std::cout << "Initializing file storage with base directory: " << baseDir 
              << ", compression: " << (enableCompression ? "enabled" : "disabled")
              << ", compression age: " << compressionAgeDays << " days" << std::endl;
    
    // Create base directory if it doesn't exist
    try {
        fs::create_directories(baseDir_);
        
        // Create subdirectories for each category
        fs::create_directories(baseDir_ + "/system");
        fs::create_directories(baseDir_ + "/security");
        fs::create_directories(baseDir_ + "/application");
        fs::create_directories(baseDir_ + "/database");
        fs::create_directories(baseDir_ + "/custom");
        fs::create_directories(baseDir_ + "/metadata");
        
        // Load metadata
        loadMetadata();
    } catch (const std::exception& e) {
        std::cerr << "Error initializing file storage: " << e.what() << std::endl;
    }
}

bool FileStorage::store(const LogEntry& entry) {
    if (!canHandle(entry)) {
        return false;
    }
    
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Get file path for this entry
        std::string filePath = getFilePath(entry);
        
        // Create parent directories if they don't exist
        fs::create_directories(fs::path(filePath).parent_path());
        
        // Open file for appending
        std::ofstream file(filePath, std::ios::app);
        if (!file) {
            std::cerr << "Failed to open file for writing: " << filePath << std::endl;
            return false;
        }
        
        // Write entry to file
        file << entryToString(entry) << std::endl;
        
        // Update metadata
        std::string dateStr = formatDate(entry.timestamp);
        fileStats_[entry.source][dateStr]++;
        
        // Update metadata file periodically
        auto now = fs::file_time_type::clock::now();
        if (lastMetadataUpdate_ + std::chrono::minutes(5) < now) {
            updateMetadata();
            lastMetadataUpdate_ = now;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error storing log entry: " << e.what() << std::endl;
        return false;
    }
}

bool FileStorage::storeBatch(const std::vector<LogEntry>& entries) {
    if (entries.empty()) {
        return true;
    }
    
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Group entries by file path
        std::unordered_map<std::string, std::vector<LogEntry>> fileEntries;
        
        for (const auto& entry : entries) {
            if (canHandle(entry)) {
                std::string filePath = getFilePath(entry);
                fileEntries[filePath].push_back(entry);
                
                // Update metadata
                std::string dateStr = formatDate(entry.timestamp);
                fileStats_[entry.source][dateStr]++;
            }
        }
        
        // Write entries to each file
        for (const auto& [filePath, fileEntriesVec] : fileEntries) {
            // Create parent directories if they don't exist
            fs::create_directories(fs::path(filePath).parent_path());
            
            // Open file for appending
            std::ofstream file(filePath, std::ios::app);
            if (!file) {
                std::cerr << "Failed to open file for writing: " << filePath << std::endl;
                continue;
            }
            
            // Write entries to file
            for (const auto& entry : fileEntriesVec) {
                file << entryToString(entry) << std::endl;
            }
        }
        
        // Update metadata file
        updateMetadata();
        lastMetadataUpdate_ = fs::file_time_type::clock::now();
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error storing log entries in batch: " << e.what() << std::endl;
        return false;
    }
}

std::vector<LogEntry> FileStorage::query(const LogQueryParams& params) {
    std::vector<LogEntry> results;
    
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Get files to search based on query parameters
        std::vector<std::string> filesToSearch = getFilesInDateRange(
            params.source,
            params.startTime,
            params.endTime
        );
        
        // Process each file
        for (const auto& filePath : filesToSearch) {
            // Check if file exists
            if (!fs::exists(filePath)) {
                continue;
            }
            
            // Read lines from file
            std::vector<std::string> lines;
            if (filePath.ends_with(".gz") || filePath.ends_with(".zst")) {
                lines = readLinesFromCompressedFile(filePath);
            } else {
                lines = readLinesFromFile(filePath);
            }
            
            // Extract source from file path
            std::string source = "unknown";
            fs::path path(filePath);
            fs::path parentDir = path.parent_path().filename();
            fs::path grandparentDir = path.parent_path().parent_path().filename();
            
            if (grandparentDir == "system" || grandparentDir == "security" || 
                grandparentDir == "application" || grandparentDir == "database" || 
                grandparentDir == "custom") {
                source = parentDir.string();
            } else if (parentDir == "system" || parentDir == "security" || 
                       parentDir == "application" || parentDir == "database" || 
                       parentDir == "custom") {
                source = path.stem().string();
            }
            
            // Parse each line and check if it matches the query
            for (const auto& line : lines) {
                LogEntry entry = stringToEntry(line, source);
                
                if (matchesQuery(entry, params)) {
                    results.push_back(entry);
                    
                    // Check if we've reached the limit
                    if (params.limit > 0 && results.size() >= static_cast<size_t>(params.limit)) {
                        break;
                    }
                }
            }
            
            // Check if we've reached the limit
            if (params.limit > 0 && results.size() >= static_cast<size_t>(params.limit)) {
                break;
            }
        }
        
        // Sort results by timestamp (newest first)
        std::sort(results.begin(), results.end(),
            [](const LogEntry& a, const LogEntry& b) {
                return a.timestamp > b.timestamp;
            });
        
        // Apply limit if needed
        if (params.limit > 0 && results.size() > static_cast<size_t>(params.limit)) {
            results.resize(params.limit);
        }
        
        return results;
    } catch (const std::exception& e) {
        std::cerr << "Error querying logs: " << e.what() << std::endl;
        return results;
    }
}

StorageStats FileStorage::getStats() {
    StorageStats stats;
    
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Calculate total entries and source counts
        for (const auto& [source, dateCounts] : fileStats_) {
            size_t sourceTotal = 0;
            for (const auto& [date, count] : dateCounts) {
                sourceTotal += count;
            }
            
            stats.bySource[source] = sourceTotal;
            stats.totalEntries += sourceTotal;
        }
        
        // We don't have level counts in the metadata, so we'll estimate
        // based on typical distributions
        stats.byLevel[LogType::INFO] = stats.totalEntries * 0.7;
        stats.byLevel[LogType::WARNING] = stats.totalEntries * 0.15;
        stats.byLevel[LogType::ERROR] = stats.totalEntries * 0.1;
        stats.byLevel[LogType::SUCCESS] = stats.totalEntries * 0.03;
        stats.byLevel[LogType::DEBUG] = stats.totalEntries * 0.02;
        
        // Calculate storage size
        stats.storageSize = 0;
        for (const auto& entry : fs::recursive_directory_iterator(baseDir_)) {
            if (fs::is_regular_file(entry)) {
                stats.storageSize += fs::file_size(entry);
            }
        }
        
        // We don't have exact timestamp information in the metadata,
        // so we'll use the file modification times as an approximation
        std::optional<std::chrono::system_clock::time_point> oldestTime;
        std::optional<std::chrono::system_clock::time_point> newestTime;
        
        for (const auto& entry : fs::recursive_directory_iterator(baseDir_)) {
            if (fs::is_regular_file(entry) && entry.path().extension() != ".json") {
                auto fileTime = fs::last_write_time(entry);
                auto timePoint = std::chrono::file_clock::to_sys(fileTime);
                
                if (!oldestTime || timePoint < *oldestTime) {
                    oldestTime = timePoint;
                }
                
                if (!newestTime || timePoint > *newestTime) {
                    newestTime = timePoint;
                }
            }
        }
        
        stats.oldestEntry = oldestTime;
        stats.newestEntry = newestTime;
        
        return stats;
    } catch (const std::exception& e) {
        std::cerr << "Error getting storage stats: " << e.what() << std::endl;
        return stats;
    }
}

bool FileStorage::prune(std::chrono::system_clock::time_point olderThan) {
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t prunedCount = 0;
        
        // Convert to file_time_type
        auto olderThanFileTime = std::chrono::file_clock::from_sys(olderThan);
        
        // Find files older than the specified time
        for (const auto& entry : fs::recursive_directory_iterator(baseDir_)) {
            if (fs::is_regular_file(entry) && entry.path().extension() != ".json") {
                auto fileTime = fs::last_write_time(entry);
                
                if (fileTime < olderThanFileTime) {
                    // Remove the file
                    fs::remove(entry);
                    prunedCount++;
                }
            }
        }
        
        // Update metadata
        updateMetadata();
        
        std::cout << "Pruned " << prunedCount << " files from file storage" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error pruning logs: " << e.what() << std::endl;
        return false;
    }
}

bool FileStorage::compact() {
    if (!enableCompression_) {
        return true;
    }
    
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t compressedCount = 0;
        auto now = std::chrono::file_clock::now();
        auto cutoff = now - std::chrono::hours(24 * compressionAgeDays_);
        
        // Find files older than the compression age
        for (const auto& entry : fs::recursive_directory_iterator(baseDir_)) {
            if (fs::is_regular_file(entry) && 
                entry.path().extension() != ".json" && 
                entry.path().extension() != ".gz" && 
                entry.path().extension() != ".zst") {
                
                auto fileTime = fs::last_write_time(entry);
                
                if (fileTime < cutoff) {
                    // Compress the file
                    if (compressFile(entry.path().string())) {
                        compressedCount++;
                    }
                }
            }
        }
        
        std::cout << "Compressed " << compressedCount << " files in file storage" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error compacting file storage: " << e.what() << std::endl;
        return false;
    }
}

bool FileStorage::backup(const std::string& backupPath) {
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Create backup directory
        fs::create_directories(backupPath);
        
        // Copy all files to backup directory
        for (const auto& entry : fs::recursive_directory_iterator(baseDir_)) {
            if (fs::is_regular_file(entry)) {
                fs::path relativePath = fs::relative(entry.path(), baseDir_);
                fs::path backupFilePath = fs::path(backupPath) / relativePath;
                
                // Create parent directories if they don't exist
                fs::create_directories(backupFilePath.parent_path());
                
                // Copy the file
                fs::copy_file(entry.path(), backupFilePath, fs::copy_options::overwrite_existing);
            }
        }
        
        std::cout << "File storage backed up to " << backupPath << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error backing up file storage: " << e.what() << std::endl;
        return false;
    }
}

bool FileStorage::canHandle(const LogEntry& entry) const {
    // File storage can handle any log entry
    return true;
}

std::string FileStorage::getFilePath(const LogEntry& entry) const {
    return getFilePath(entry.source, entry.timestamp);
}

std::string FileStorage::getFilePath(const std::string& source, const std::chrono::system_clock::time_point& timestamp) const {
    // Determine category
    std::string category = "custom";
    if (source == "system" || source == "kernel" || source == "systemd") {
        category = "system";
    } else if (source == "auth" || source == "security" || source == "audit") {
        category = "security";
    } else if (source == "nginx" || source == "apache" || source == "dashboard") {
        category = "application";
    } else if (source == "mysql" || source == "postgresql" || source == "mongodb") {
        category = "database";
    }
    
    // Format date
    std::string dateStr = formatDate(timestamp);
    
    // Create path
    return baseDir_ + "/" + category + "/" + source + "/" + dateStr + ".log";
}

std::string FileStorage::formatDate(const std::chrono::system_clock::time_point& timestamp) const {
    auto time = std::chrono::system_clock::to_time_t(timestamp);
    std::tm tm = *std::localtime(&time);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d");
    return oss.str();
}

std::string FileStorage::formatTimestamp(const std::chrono::system_clock::time_point& timestamp) const {
    auto time = std::chrono::system_clock::to_time_t(timestamp);
    std::tm tm = *std::localtime(&time);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    
    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch() % std::chrono::seconds(1)
    );
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return oss.str();
}

std::string FileStorage::entryToString(const LogEntry& entry) const {
    // Format: timestamp|type|source|message|metadata
    std::ostringstream oss;
    
    // Add timestamp
    oss << formatTimestamp(entry.timestamp) << "|";
    
    // Add type
    oss << logTypeToString(entry.type) << "|";
    
    // Add source
    oss << entry.source << "|";
    
    // Add message
    oss << entry.message << "|";
    
    // Add metadata as JSON
    Json::Value metadata;
    
    // Add optional fields
    if (entry.component) metadata["component"] = *entry.component;
    if (entry.unit) metadata["unit"] = *entry.unit;
    if (entry.hostname) metadata["hostname"] = *entry.hostname;
    if (entry.commit) metadata["commit"] = *entry.commit;
    if (entry.author) metadata["author"] = *entry.author;
    if (entry.rawLine) metadata["raw_line"] = *entry.rawLine;
    if (entry.category) metadata["category"] = logCategoryToString(*entry.category);
    
    // Add custom metadata
    for (const auto& [key, value] : entry.metadata) {
        metadata[key] = value;
    }
    
    // Convert to string
    Json::StreamWriterBuilder writer;
    writer["indentation"] = ""; // No indentation for compact output
    oss << Json::writeString(writer, metadata);
    
    return oss.str();
}

LogEntry FileStorage::stringToEntry(const std::string& line, const std::string& source) const {
    LogEntry entry;
    
    // Parse line: timestamp|type|source|message|metadata
    std::istringstream iss(line);
    std::string timestampStr, typeStr, sourceStr, messageStr, metadataStr;
    
    std::getline(iss, timestampStr, '|');
    std::getline(iss, typeStr, '|');
    std::getline(iss, sourceStr, '|');
    std::getline(iss, messageStr, '|');
    std::getline(iss, metadataStr);
    
    // Parse timestamp
    try {
        std::tm tm = {};
        std::istringstream tss(timestampStr);
        tss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
        
        // Parse milliseconds
        double seconds = 0;
        if (timestampStr.find('.') != std::string::npos) {
            seconds = std::stod(timestampStr.substr(timestampStr.find('.')));
        }
        
        auto timeT = std::mktime(&tm);
        auto timePoint = std::chrono::system_clock::from_time_t(timeT);
        timePoint += std::chrono::milliseconds(static_cast<int>(seconds * 1000));
        
        entry.timestamp = timePoint;
    } catch (...) {
        // Use current time if parsing fails
        entry.timestamp = std::chrono::system_clock::now();
    }
    
    // Parse type
    entry.type = logTypeFromString(typeStr);
    
    // Set source
    entry.source = sourceStr.empty() ? source : sourceStr;
    
    // Set message
    entry.message = messageStr;
    
    // Parse metadata
    try {
        Json::Value metadata;
        Json::CharReaderBuilder reader;
        std::string errors;
        
        std::istringstream mss(metadataStr);
        if (Json::parseFromStream(reader, mss, &metadata, &errors)) {
            // Parse optional fields
            if (metadata.isMember("component")) entry.component = metadata["component"].asString();
            if (metadata.isMember("unit")) entry.unit = metadata["unit"].asString();
            if (metadata.isMember("hostname")) entry.hostname = metadata["hostname"].asString();
            if (metadata.isMember("commit")) entry.commit = metadata["commit"].asString();
            if (metadata.isMember("author")) entry.author = metadata["author"].asString();
            if (metadata.isMember("raw_line")) entry.rawLine = metadata["raw_line"].asString();
            if (metadata.isMember("category")) entry.category = logCategoryFromString(metadata["category"].asString());
            
            // Parse custom metadata
            for (const auto& key : metadata.getMemberNames()) {
                if (key != "component" && key != "unit" && key != "hostname" && 
                    key != "commit" && key != "author" && key != "raw_line" && 
                    key != "category") {
                    entry.metadata[key] = metadata[key].asString();
                }
            }
        }
    } catch (...) {
        // Ignore metadata parsing errors
    }
    
    return entry;
}

std::vector<std::string> FileStorage::getFilesInDateRange(
    const std::string& source,
    std::optional<std::chrono::system_clock::time_point> startTime,
    std::optional<std::chrono::system_clock::time_point> endTime) const {
    
    std::vector<std::string> files;
    
    // If no start time, use 30 days ago
    auto start = startTime.value_or(
        std::chrono::system_clock::now() - std::chrono::hours(24 * 30)
    );
    
    // If no end time, use now
    auto end = endTime.value_or(std::chrono::system_clock::now());
    
    // Generate a list of dates in the range
    std::vector<std::chrono::system_clock::time_point> dates;
    auto current = start;
    while (current <= end) {
        dates.push_back(current);
        current += std::chrono::hours(24); // Add one day
    }
    
    // Generate file paths for each date
    for (const auto& date : dates) {
        if (source == "all") {
            // Add files for all sources
            for (const auto& category : {"system", "security", "application", "database", "custom"}) {
                for (const auto& entry : fs::directory_iterator(baseDir_ + "/" + category)) {
                    if (fs::is_directory(entry)) {
                        std::string sourceDir = entry.path().filename().string();
                        std::string filePath = baseDir_ + "/" + category + "/" + sourceDir + "/" + formatDate(date) + ".log";
                        
                        // Check if file exists (uncompressed or compressed)
                        if (fs::exists(filePath)) {
                            files.push_back(filePath);
                        } else if (fs::exists(filePath + ".gz")) {
                            files.push_back(filePath + ".gz");
                        } else if (fs::exists(filePath + ".zst")) {
                            files.push_back(filePath + ".zst");
                        }
                    }
                }
            }
        } else {
            // Add files for the specified source
            for (const auto& category : {"system", "security", "application", "database", "custom"}) {
                std::string filePath = baseDir_ + "/" + category + "/" + source + "/" + formatDate(date) + ".log";
                
                // Check if file exists (uncompressed or compressed)
                if (fs::exists(filePath)) {
                    files.push_back(filePath);
                } else if (fs::exists(filePath + ".gz")) {
                    files.push_back(filePath + ".gz");
                } else if (fs::exists(filePath + ".zst")) {
                    files.push_back(filePath + ".zst");
                }
            }
        }
    }
    
    return files;
}

bool FileStorage::compressFile(const std::string& filePath) {
    // Check if file exists
    if (!fs::exists(filePath)) {
        return false;
    }
    
    // Check if file is already compressed
    if (filePath.ends_with(".gz") || filePath.ends_with(".zst")) {
        return true;
    }
    
    // Compress using zstd if available, otherwise gzip
    std::string compressedPath = filePath + ".zst";
    std::string command = "zstd -q -f " + filePath + " -o " + compressedPath;
    
    int result = system(command.c_str());
    if (result != 0) {
        // Try gzip instead
        compressedPath = filePath + ".gz";
        command = "gzip -f " + filePath;
        result = system(command.c_str());
    }
    
    if (result == 0) {
        // Compression successful, remove original file
        fs::remove(filePath);
        return true;
    }
    
    return false;
}

std::vector<std::string> FileStorage::readLinesFromFile(const std::string& filePath) const {
    std::vector<std::string> lines;
    
    std::ifstream file(filePath);
    if (!file) {
        return lines;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            lines.push_back(line);
        }
    }
    
    return lines;
}

std::vector<std::string> FileStorage::readLinesFromCompressedFile(const std::string& filePath) const {
    std::vector<std::string> lines;
    
    // Use appropriate decompression command
    std::string command;
    if (filePath.ends_with(".gz")) {
        command = "gzip -dc " + filePath;
    } else if (filePath.ends_with(".zst")) {
        command = "zstd -dc " + filePath;
    } else {
        return lines;
    }
    
    // Open pipe to command
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        return lines;
    }
    
    // Read output
    char buffer[4096];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        std::string line(buffer);
        
        // Remove trailing newline
        if (!line.empty() && line.back() == '\n') {
            line.pop_back();
        }
        
        if (!line.empty()) {
            lines.push_back(line);
        }
    }
    
    pclose(pipe);
    return lines;
}

bool FileStorage::matchesQuery(const LogEntry& entry, const LogQueryParams& params) const {
    // Check source filter
    if (params.source != "all" && entry.source != params.source) {
        return false;
    }
    
    // Check level filter
    if (params.level != "all") {
        std::string entryLevel = logTypeToString(entry.type);
        if (entryLevel != params.level) {
            return false;
        }
    }
    
    // Check time range
    if (params.startTime && entry.timestamp < *params.startTime) {
        return false;
    }
    
    if (params.endTime && entry.timestamp > *params.endTime) {
        return false;
    }
    
    // Check search text
    if (!params.searchText.empty()) {
        if (entry.message.find(params.searchText) == std::string::npos) {
            return false;
        }
    }
    
    // Check include sources
    if (!params.includeSources.empty()) {
        bool found = false;
        for (const auto& source : params.includeSources) {
            if (entry.source == source) {
                found = true;
                break;
            }
        }
        
        if (!found) {
            return false;
        }
    }
    
    // Check exclude sources
    for (const auto& source : params.excludeSources) {
        if (entry.source == source) {
            return false;
        }
    }
    
    return true;
}

void FileStorage::updateMetadata() {
    try {
        // Create metadata directory if it doesn't exist
        fs::create_directories(baseDir_ + "/metadata");
        
        // Create JSON object
        Json::Value root;
        
        // Add file stats
        Json::Value statsJson;
        for (const auto& [source, dateCounts] : fileStats_) {
            Json::Value sourceCounts;
            for (const auto& [date, count] : dateCounts) {
                sourceCounts[date] = static_cast<Json::UInt64>(count);
            }
            statsJson[source] = sourceCounts;
        }
        root["file_stats"] = statsJson;
        
        // Add last update time
        root["last_update"] = static_cast<Json::UInt64>(
            std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()
            ).count()
        );
        
        // Write to file
        std::ofstream file(baseDir_ + "/metadata/stats.json");
        if (file) {
            Json::StreamWriterBuilder writer;
            file << Json::writeString(writer, root);
        }
    } catch (const std::exception& e) {
        std::cerr << "Error updating metadata: " << e.what() << std::endl;
    }
}

void FileStorage::loadMetadata() {
    try {
        std::string metadataPath = baseDir_ + "/metadata/stats.json";
        
        // Check if metadata file exists
        if (!fs::exists(metadataPath)) {
            return;
        }
        
        // Read metadata file
        std::ifstream file(metadataPath);
        if (!file) {
            return;
        }
        
        // Parse JSON
        Json::Value root;
        Json::CharReaderBuilder reader;
        std::string errors;
        
        if (Json::parseFromStream(reader, file, &root, &errors)) {
            // Load file stats
            if (root.isMember("file_stats") && root["file_stats"].isObject()) {
                const Json::Value& statsJson = root["file_stats"];
                
                for (const auto& sourceName : statsJson.getMemberNames()) {
                    const Json::Value& sourceCounts = statsJson[sourceName];
                    
                    for (const auto& dateName : sourceCounts.getMemberNames()) {
                        fileStats_[sourceName][dateName] = sourceCounts[dateName].asUInt64();
                    }
                }
            }
            
            // Set last update time
            lastMetadataUpdate_ = fs::file_time_type::clock::now();
        }
    } catch (const std::exception& e) {
        std::cerr << "Error loading metadata: " << e.what() << std::endl;
    }
}

} // namespace logging
