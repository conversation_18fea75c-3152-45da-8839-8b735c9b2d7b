#include "database-service/security/jwt.hpp"
#include "database-service/utils/logger.hpp"
#include <openssl/hmac.h>
#include <openssl/evp.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <format>
#include <stdexcept>
#include <vector>

namespace dbservice::security {

std::string JWT::create(const std::unordered_map<std::string, std::string>& payload, 
                       const std::string& secret,
                       int expiresInSeconds) {
    try {
        // Create header
        std::unordered_map<std::string, std::string> header;
        header["alg"] = "HS256";
        header["typ"] = "JWT";
        
        // Create payload with expiration
        auto payloadWithExp = payload;
        auto now = std::chrono::system_clock::now();
        auto expTime = now + std::chrono::seconds(expiresInSeconds);
        auto expTimeT = std::chrono::system_clock::to_time_t(expTime);
        payloadWithExp["exp"] = std::to_string(expTimeT);
        
        // Add issued at time
        auto issuedAtTimeT = std::chrono::system_clock::to_time_t(now);
        payloadWithExp["iat"] = std::to_string(issuedAtTimeT);
        
        // Encode header and payload
        std::string encodedHeader = base64Encode(serializeJson(header));
        std::string encodedPayload = base64Encode(serializeJson(payloadWithExp));
        
        // Create signature
        std::string signatureInput = encodedHeader + "." + encodedPayload;
        std::string signature = hmacSha256(signatureInput, secret);
        std::string encodedSignature = base64Encode(signature);
        
        // Create token
        return encodedHeader + "." + encodedPayload + "." + encodedSignature;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Error creating JWT token: {}", e.what()));
        return "";
    }
}

bool JWT::verify(const std::string& token, 
                const std::string& secret, 
                std::unordered_map<std::string, std::string>& payload) {
    try {
        // Split token
        std::vector<std::string> parts;
        std::istringstream tokenStream(token);
        std::string part;
        
        while (std::getline(tokenStream, part, '.')) {
            parts.push_back(part);
        }
        
        if (parts.size() != 3) {
            utils::Logger::warning("Invalid JWT token format");
            return false;
        }
        
        // Verify signature
        std::string signatureInput = parts[0] + "." + parts[1];
        std::string expectedSignature = hmacSha256(signatureInput, secret);
        std::string actualSignature = base64Decode(parts[2]);
        
        if (expectedSignature != actualSignature) {
            utils::Logger::warning("Invalid JWT signature");
            return false;
        }
        
        // Decode payload
        std::string decodedPayload = base64Decode(parts[1]);
        payload = parseJson(decodedPayload);
        
        // Check expiration
        if (isExpired(payload)) {
            utils::Logger::warning("JWT token expired");
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Error verifying JWT token: {}", e.what()));
        return false;
    }
}

bool JWT::isExpired(const std::unordered_map<std::string, std::string>& payload) {
    try {
        // Check if payload has expiration
        auto it = payload.find("exp");
        if (it == payload.end()) {
            return false; // No expiration, token is not expired
        }
        
        // Get expiration time
        time_t expTime = std::stoll(it->second);
        
        // Get current time
        auto now = std::chrono::system_clock::now();
        auto nowTimeT = std::chrono::system_clock::to_time_t(now);
        
        // Check if token is expired
        return nowTimeT > expTime;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Error checking JWT expiration: {}", e.what()));
        return true; // Assume expired on error
    }
}

std::string JWT::base64Encode(const std::string& input) {
    BIO* bio = BIO_new(BIO_s_mem());
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO_push(b64, bio);
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);
    
    BIO_write(b64, input.c_str(), static_cast<int>(input.length()));
    BIO_flush(b64);
    
    BUF_MEM* bufferPtr;
    BIO_get_mem_ptr(b64, &bufferPtr);
    
    std::string result(bufferPtr->data, bufferPtr->length);
    
    BIO_free_all(b64);
    
    // Replace characters for URL-safe base64
    std::replace(result.begin(), result.end(), '+', '-');
    std::replace(result.begin(), result.end(), '/', '_');
    result.erase(std::remove(result.begin(), result.end(), '='), result.end());
    
    return result;
}

std::string JWT::base64Decode(const std::string& input) {
    std::string paddedInput = input;
    
    // Replace URL-safe characters
    std::replace(paddedInput.begin(), paddedInput.end(), '-', '+');
    std::replace(paddedInput.begin(), paddedInput.end(), '_', '/');
    
    // Add padding
    switch (paddedInput.length() % 4) {
        case 0: break;
        case 2: paddedInput += "=="; break;
        case 3: paddedInput += "="; break;
        default: throw std::runtime_error("Invalid base64 string");
    }
    
    BIO* bio = BIO_new_mem_buf(paddedInput.c_str(), -1);
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);
    bio = BIO_push(b64, bio);
    
    std::vector<char> buffer(paddedInput.length());
    int decodedLength = BIO_read(bio, buffer.data(), static_cast<int>(buffer.size()));
    
    BIO_free_all(bio);
    
    if (decodedLength <= 0) {
        throw std::runtime_error("Failed to decode base64 string");
    }
    
    return std::string(buffer.data(), decodedLength);
}

std::string JWT::hmacSha256(const std::string& input, const std::string& key) {
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hashLength;
    
    HMAC(EVP_sha256(), key.c_str(), static_cast<int>(key.length()),
         reinterpret_cast<const unsigned char*>(input.c_str()), input.length(),
         hash, &hashLength);
    
    return std::string(reinterpret_cast<char*>(hash), hashLength);
}

std::unordered_map<std::string, std::string> JWT::parseJson(const std::string& json) {
    std::unordered_map<std::string, std::string> result;
    
    // Simple JSON parser for JWT payloads
    // This is not a full JSON parser, but it's sufficient for JWT payloads
    // which are simple key-value pairs
    
    size_t pos = 0;
    if (json[pos] != '{') {
        throw std::runtime_error("Invalid JSON: expected '{'");
    }
    
    pos++;
    
    while (pos < json.length() && json[pos] != '}') {
        // Skip whitespace
        while (pos < json.length() && std::isspace(json[pos])) {
            pos++;
        }
        
        if (pos >= json.length() || json[pos] != '"') {
            throw std::runtime_error("Invalid JSON: expected '\"'");
        }
        
        // Parse key
        pos++;
        size_t keyStart = pos;
        while (pos < json.length() && json[pos] != '"') {
            pos++;
        }
        
        if (pos >= json.length()) {
            throw std::runtime_error("Invalid JSON: unterminated string");
        }
        
        std::string key = json.substr(keyStart, pos - keyStart);
        pos++;
        
        // Skip whitespace and colon
        while (pos < json.length() && (std::isspace(json[pos]) || json[pos] == ':')) {
            pos++;
        }
        
        if (pos >= json.length()) {
            throw std::runtime_error("Invalid JSON: expected value");
        }
        
        // Parse value
        std::string value;
        
        if (json[pos] == '"') {
            // String value
            pos++;
            size_t valueStart = pos;
            while (pos < json.length() && json[pos] != '"') {
                if (json[pos] == '\\' && pos + 1 < json.length()) {
                    pos++; // Skip escape character
                }
                pos++;
            }
            
            if (pos >= json.length()) {
                throw std::runtime_error("Invalid JSON: unterminated string");
            }
            
            value = json.substr(valueStart, pos - valueStart);
            pos++;
        } else if (std::isdigit(json[pos]) || json[pos] == '-') {
            // Number value
            size_t valueStart = pos;
            while (pos < json.length() && (std::isdigit(json[pos]) || json[pos] == '.' || json[pos] == '-' || json[pos] == 'e' || json[pos] == 'E' || json[pos] == '+')) {
                pos++;
            }
            
            value = json.substr(valueStart, pos - valueStart);
        } else if (json.substr(pos, 4) == "true") {
            value = "true";
            pos += 4;
        } else if (json.substr(pos, 5) == "false") {
            value = "false";
            pos += 5;
        } else if (json.substr(pos, 4) == "null") {
            value = "null";
            pos += 4;
        } else {
            throw std::runtime_error("Invalid JSON: unexpected character");
        }
        
        result[key] = value;
        
        // Skip whitespace and comma
        while (pos < json.length() && (std::isspace(json[pos]) || json[pos] == ',')) {
            pos++;
        }
    }
    
    return result;
}

std::string JWT::serializeJson(const std::unordered_map<std::string, std::string>& map) {
    std::ostringstream json;
    json << "{";
    
    bool first = true;
    for (const auto& [key, value] : map) {
        if (!first) {
            json << ",";
        }
        first = false;
        
        json << "\"" << key << "\":";
        
        // Check if value is a number, boolean, or null
        if (value == "true" || value == "false" || value == "null" ||
            (value.length() > 0 && (std::isdigit(value[0]) || value[0] == '-'))) {
            json << value;
        } else {
            json << "\"" << value << "\"";
        }
    }
    
    json << "}";
    return json.str();
}

} // namespace dbservice::security
