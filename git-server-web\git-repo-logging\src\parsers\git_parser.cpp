#include "git_parser.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace logging {

GitParser::GitParser() {
    // Initialize regular expressions for Git log formats
    
    // Commit log format: commit_hash|author|timestamp|message
    commitLogFormat_ = std::regex(
        R"(([a-f0-9]{40})\|([^|]+)\|([^|]+)\|(.*))"
    );
    
    // Operation log format: [timestamp] action: message
    operationLogFormat_ = std::regex(
        R"(\[([^\]]+)\]\s+([^:]+):\s+(.*))"
    );
}

std::optional<LogEntry> GitParser::parse(const std::string& line, const std::string& source) {
    std::smatch matches;
    
    // Try commit log format
    if (std::regex_search(line, matches, commitLogFormat_)) {
        LogEntry entry;
        
        // Parse timestamp
        std::string timestampStr = matches[3].str();
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%a %b %d %H:%M:%S %Y %z");
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set other fields
        entry.commit = matches[1].str();
        entry.author = matches[2].str();
        entry.message = matches[4].str();
        entry.component = "git";
        entry.source = source.empty() ? "git" : source;
        entry.rawLine = line;
        entry.category = LogCategory::APPLICATION;
        entry.type = LogType::INFO; // Commits are generally informational
        
        return entry;
    }
    
    // Try operation log format
    if (std::regex_search(line, matches, operationLogFormat_)) {
        LogEntry entry;
        
        // Parse timestamp
        std::string timestampStr = matches[1].str();
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set other fields
        std::string action = matches[2].str();
        entry.message = matches[3].str();
        entry.component = "git";
        entry.unit = action;
        entry.source = source.empty() ? "git" : source;
        entry.rawLine = line;
        entry.category = LogCategory::APPLICATION;
        
        // Determine log type based on action and content
        if (action.find("error") != std::string::npos || 
            action.find("fatal") != std::string::npos ||
            entry.message.find("failed") != std::string::npos ||
            entry.message.find("error") != std::string::npos) {
            entry.type = LogType::ERROR;
        } else if (action.find("warning") != std::string::npos || 
                   entry.message.find("warning") != std::string::npos) {
            entry.type = LogType::WARNING;
        } else {
            entry.type = LogType::INFO;
        }
        
        return entry;
    }
    
    // No match
    return std::nullopt;
}

bool GitParser::canParse(const std::string& line) {
    // Check if line matches any of the Git log formats
    return std::regex_search(line, commitLogFormat_) ||
           std::regex_search(line, operationLogFormat_);
}

} // namespace logging
