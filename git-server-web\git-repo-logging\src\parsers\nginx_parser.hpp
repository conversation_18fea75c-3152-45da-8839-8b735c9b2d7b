#pragma once
#include "parser_interface.hpp"
#include <regex>

namespace logging {

class NginxParser : public LogParser {
public:
    NginxParser();
    ~NginxParser() = default;
    
    // LogParser interface implementation
    std::optional<LogEntry> parse(const std::string& line, const std::string& source) override;
    bool canParse(const std::string& line) override;
    std::string getName() const override { return "nginx"; }
    std::string getDescription() const override { return "Parser for Nginx log format"; }
    
private:
    // Regular expressions for different Nginx log formats
    std::regex errorLogFormat_;  // For error.log
    std::regex accessLogFormat_; // For access.log
};

} // namespace logging
