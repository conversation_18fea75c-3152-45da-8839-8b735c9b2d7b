#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/config_manager.hpp"
#include <format> // C++20 feature
#include <nlohmann/json.hpp>
#include <regex>
#include <sstream>
#include <thread>
#include <chrono>
#include <algorithm>

// Platform-specific socket includes
#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #define SOCKET_ERROR_CODE WSAGetLastError()
    #define CLOSE_SOCKET closesocket
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <unistd.h>
    #include <arpa/inet.h>
    #include <errno.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define SOCKET_ERROR_CODE errno
    #define CLOSE_SOCKET close
#endif

namespace dbservice::api {

ApiServer::ApiServer(unsigned short port,
                   std::shared_ptr<core::ConnectionManager> connectionManager,
                   std::shared_ptr<security::SecurityManager> securityManager)
    : port_(port),
      connectionManager_(connectionManager),
      securityManager_(securityManager),
      running_(false),
      serverSocket_(INVALID_SOCKET) {

    // Initialize route handlers
    initializeRoutes();

    // Load CORS configuration
    loadCorsConfig();
}

ApiServer::~ApiServer() {
    stop();
}

void ApiServer::start() {
    if (running_) {
        utils::Logger::warning("API server is already running");
        return;
    }

    // Initialize socket library on Windows
    #ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        utils::Logger::error(std::format("Failed to initialize Winsock: {}", SOCKET_ERROR_CODE));
        return;
    }
    #endif

    // Create socket
    serverSocket_ = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket_ == INVALID_SOCKET) {
        utils::Logger::error(std::format("Failed to create socket: {}", SOCKET_ERROR_CODE));
        #ifdef _WIN32
        WSACleanup();
        #endif
        return;
    }

    // Set socket options
    int opt = 1;
    if (setsockopt(serverSocket_, SOL_SOCKET, SO_REUSEADDR, (const char*)&opt, sizeof(opt)) < 0) {
        utils::Logger::error(std::format("Failed to set socket options: {}", SOCKET_ERROR_CODE));
        CLOSE_SOCKET(serverSocket_);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return;
    }

    // Bind socket
    struct sockaddr_in address;
    address.sin_family = AF_INET;
    address.sin_addr.s_addr = INADDR_ANY;
    address.sin_port = htons(port_);

    if (bind(serverSocket_, (struct sockaddr*)&address, sizeof(address)) < 0) {
        utils::Logger::error(std::format("Failed to bind socket: {}", SOCKET_ERROR_CODE));
        CLOSE_SOCKET(serverSocket_);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return;
    }

    // Listen for connections
    if (listen(serverSocket_, 10) < 0) {
        utils::Logger::error(std::format("Failed to listen on socket: {}", SOCKET_ERROR_CODE));
        CLOSE_SOCKET(serverSocket_);
        #ifdef _WIN32
        WSACleanup();
        #endif
        return;
    }

    // Start server thread
    running_ = true;
    serverThread_ = std::thread(&ApiServer::run, this);

    utils::Logger::info(std::format("API server started on port {}", port_));
}

void ApiServer::stop() {
    if (!running_) {
        return;
    }

    running_ = false;

    // Close server socket
    if (serverSocket_ != INVALID_SOCKET) {
        CLOSE_SOCKET(serverSocket_);
        serverSocket_ = INVALID_SOCKET;
    }

    // Wait for server thread to finish
    if (serverThread_.joinable()) {
        serverThread_.join();
    }

    // Cleanup socket library on Windows
    #ifdef _WIN32
    WSACleanup();
    #endif

    utils::Logger::info("API server stopped");
}

bool ApiServer::isRunning() const {
    return running_;
}

unsigned short ApiServer::getPort() const {
    return port_;
}

void ApiServer::configureCors(const CorsConfig& config) {
    corsConfig_ = config;
    utils::Logger::info(std::format("CORS configuration updated: enabled={}", corsConfig_.enabled ? "true" : "false"));
}

void ApiServer::configureSSL(const SSLConfig& config) {
    sslConfig_ = config;
    utils::Logger::info(std::format("SSL configuration updated: enabled={}", sslConfig_.enabled ? "true" : "false"));

    if (sslConfig_.enabled) {
        utils::Logger::info(std::format("SSL certificate: {}", sslConfig_.certPath));
        utils::Logger::info(std::format("SSL key: {}", sslConfig_.keyPath));
    }
}

void ApiServer::loadCorsConfig() {
    auto& configManager = utils::ConfigManager::getInstance();

    corsConfig_.enabled = configManager.getBool("api.cors.enabled", false);

    if (corsConfig_.enabled) {
        // Load allowed origins
        auto originsJson = configManager.getJsonArray("api.cors.allowed_origins");
        if (!originsJson.empty()) {
            for (const auto& origin : originsJson) {
                if (origin.is_string()) {
                    corsConfig_.allowedOrigins.push_back(origin);
                }
            }
        } else {
            // Default to all origins
            corsConfig_.allowedOrigins.push_back("*");
        }

        // Load allowed methods
        auto methodsJson = configManager.getJsonArray("api.cors.allowed_methods");
        if (!methodsJson.empty()) {
            for (const auto& method : methodsJson) {
                if (method.is_string()) {
                    corsConfig_.allowedMethods.push_back(method);
                }
            }
        } else {
            // Default methods
            corsConfig_.allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
        }

        // Load allowed headers
        auto headersJson = configManager.getJsonArray("api.cors.allowed_headers");
        if (!headersJson.empty()) {
            for (const auto& header : headersJson) {
                if (header.is_string()) {
                    corsConfig_.allowedHeaders.push_back(header);
                }
            }
        } else {
            // Default headers
            corsConfig_.allowedHeaders = {"Content-Type", "Authorization"};
        }

        // Load other settings
        corsConfig_.allowCredentials = configManager.getBool("api.cors.allow_credentials", false);
        corsConfig_.maxAge = configManager.getInt("api.cors.max_age", 86400);

        utils::Logger::info(std::format("CORS enabled with {} allowed origins", corsConfig_.allowedOrigins.size()));
    } else {
        utils::Logger::info("CORS is disabled");
    }
}

std::pair<std::unordered_map<std::string, std::string>, std::string> ApiServer::handleRequest(
    const std::string& method,
    const std::string& path,
    const std::unordered_map<std::string, std::string>& headers,
    const std::string& body) {

    std::unordered_map<std::string, std::string> responseHeaders;
    std::string responseBody;

    try {
        // Apply CORS headers
        if (corsConfig_.enabled) {
            applyCorsHeaders(method, headers, responseHeaders);

            // Handle preflight request
            if (method == "OPTIONS") {
                return {responseHeaders, ""};
            }
        }

        // Check authentication
        bool requiresAuth = true;
        if (path == "/api/auth/login" || path == "/api/health") {
            requiresAuth = false;
        }

        if (requiresAuth) {
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                auto [headers, body] = createErrorResponse(401, "Unauthorized", "Authentication required");
                responseHeaders.insert(headers.begin(), headers.end());
                return {responseHeaders, body};
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                auto [headers, body] = createErrorResponse(401, "Unauthorized", "Invalid authentication format");
                responseHeaders.insert(headers.begin(), headers.end());
                return {responseHeaders, body};
            }

            std::string token = authHeader.substr(7);
            if (!securityManager_->validateToken(token)) {
                auto [headers, body] = createErrorResponse(401, "Unauthorized", "Invalid or expired token");
                responseHeaders.insert(headers.begin(), headers.end());
                return {responseHeaders, body};
            }
        }

        // Route request
        std::string result;

        if (method == "GET") {
            auto it = getHandlers_.find(path);
            if (it != getHandlers_.end()) {
                result = it->second(headers, body);
            }
        } else if (method == "POST") {
            auto it = postHandlers_.find(path);
            if (it != postHandlers_.end()) {
                result = it->second(headers, body);
            }
        } else if (method == "PUT") {
            auto it = putHandlers_.find(path);
            if (it != putHandlers_.end()) {
                result = it->second(headers, body);
            }
        } else if (method == "DELETE") {
            auto it = deleteHandlers_.find(path);
            if (it != deleteHandlers_.end()) {
                result = it->second(headers, body);
            }
        } else if (method == "OPTIONS") {
            auto it = optionsHandlers_.find(path);
            if (it != optionsHandlers_.end()) {
                result = it->second(headers, body);
            }
        }

        if (!result.empty()) {
            // Extract headers from the result
            std::istringstream resultStream(result);
            std::string line;

            // Skip status line
            std::getline(resultStream, line);

            // Parse headers
            while (std::getline(resultStream, line) && line != "\r") {
                size_t colonPos = line.find(':');
                if (colonPos != std::string::npos) {
                    std::string name = line.substr(0, colonPos);
                    std::string value = line.substr(colonPos + 1);

                    // Trim whitespace
                    value.erase(0, value.find_first_not_of(" \t"));
                    value.erase(value.find_last_not_of("\r\n") + 1);

                    responseHeaders[name] = value;
                }
            }

            // Extract body
            std::stringstream bodyStream;
            bodyStream << resultStream.rdbuf();
            responseBody = bodyStream.str();

            return {responseHeaders, responseBody};
        }

        auto [headers, body] = createErrorResponse(404, "Not Found", "Endpoint not found");
        responseHeaders.insert(headers.begin(), headers.end());
        return {responseHeaders, body};
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during request handling: {}", e.what()));
        auto [headers, body] = createErrorResponse(500, "Internal Server Error", e.what());
        responseHeaders.insert(headers.begin(), headers.end());
        return {responseHeaders, body};
    }
}

void ApiServer::applyCorsHeaders(
    const std::string& method,
    const std::unordered_map<std::string, std::string>& headers,
    std::unordered_map<std::string, std::string>& responseHeaders) {

    if (!corsConfig_.enabled) {
        return;
    }

    // Handle Origin header
    auto originIt = headers.find("Origin");
    if (originIt != headers.end()) {
        const std::string& origin = originIt->second;

        // Check if origin is allowed
        bool originAllowed = false;

        // Check for wildcard
        if (std::find(corsConfig_.allowedOrigins.begin(), corsConfig_.allowedOrigins.end(), "*") != corsConfig_.allowedOrigins.end()) {
            originAllowed = true;
            responseHeaders["Access-Control-Allow-Origin"] = "*";
        } else {
            // Check specific origins
            for (const auto& allowedOrigin : corsConfig_.allowedOrigins) {
                if (origin == allowedOrigin) {
                    originAllowed = true;
                    responseHeaders["Access-Control-Allow-Origin"] = origin;
                    break;
                }
            }
        }

        if (originAllowed) {
            // Set other CORS headers
            if (corsConfig_.allowCredentials) {
                responseHeaders["Access-Control-Allow-Credentials"] = "true";
            }

            // For preflight requests
            if (method == "OPTIONS") {
                // Allowed methods
                if (!corsConfig_.allowedMethods.empty()) {
                    std::string methodsStr;
                    for (size_t i = 0; i < corsConfig_.allowedMethods.size(); ++i) {
                        if (i > 0) {
                            methodsStr += ", ";
                        }
                        methodsStr += corsConfig_.allowedMethods[i];
                    }
                    responseHeaders["Access-Control-Allow-Methods"] = methodsStr;
                }

                // Allowed headers
                if (!corsConfig_.allowedHeaders.empty()) {
                    std::string headersStr;
                    for (size_t i = 0; i < corsConfig_.allowedHeaders.size(); ++i) {
                        if (i > 0) {
                            headersStr += ", ";
                        }
                        headersStr += corsConfig_.allowedHeaders[i];
                    }
                    responseHeaders["Access-Control-Allow-Headers"] = headersStr;
                }

                // Max age
                responseHeaders["Access-Control-Max-Age"] = std::to_string(corsConfig_.maxAge);
            }
        }
    }
}

void ApiServer::run() {
    while (running_) {
        // Accept connection
        struct sockaddr_in clientAddress;
        socklen_t clientAddressLength = sizeof(clientAddress);
        SOCKET clientSocket = accept(serverSocket_, (struct sockaddr*)&clientAddress, &clientAddressLength);

        if (clientSocket == INVALID_SOCKET) {
            if (running_) {
                utils::Logger::error(std::format("Failed to accept connection: {}", SOCKET_ERROR_CODE));
            }
            continue;
        }

        // Handle connection in a new thread
        std::thread([this, clientSocket, clientAddress]() {
            char clientIp[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &clientAddress.sin_addr, clientIp, INET_ADDRSTRLEN);
            utils::Logger::debug(std::format("Connection accepted from {}", clientIp));

            // Set timeout
            #ifdef _WIN32
            DWORD timeout = 30000; // 30 seconds
            setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
            #else
            struct timeval timeout;
            timeout.tv_sec = 30;
            timeout.tv_usec = 0;
            setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
            #endif

            // Receive request
            std::string request;
            char buffer[4096];
            int bytesRead;

            while ((bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0)) > 0) {
                buffer[bytesRead] = '\0';
                request += buffer;

                // Check if request is complete
                if (request.find("\r\n\r\n") != std::string::npos) {
                    break;
                }
            }

            if (bytesRead < 0) {
                utils::Logger::error(std::format("Failed to receive request: {}", SOCKET_ERROR_CODE));
                CLOSE_SOCKET(clientSocket);
                return;
            }

            // Parse request
            std::string method;
            std::string path;
            std::unordered_map<std::string, std::string> headers;
            std::string body;

            if (!parseRequest(request, method, path, headers, body)) {
                utils::Logger::error("Failed to parse request");
                std::string response = createErrorResponse(400, "Bad Request", "Invalid request format");
                send(clientSocket, response.c_str(), response.length(), 0);
                CLOSE_SOCKET(clientSocket);
                return;
            }

            // Handle request
            auto [responseHeaders, responseBody] = handleRequest(method, path, headers, body);

            // Build response
            std::ostringstream response;

            // Status line (default to 200 OK)
            response << "HTTP/1.1 200 OK\r\n";

            // Headers
            for (const auto& [name, value] : responseHeaders) {
                response << name << ": " << value << "\r\n";
            }

            // Add content length if not present
            if (responseHeaders.find("Content-Length") == responseHeaders.end()) {
                response << "Content-Length: " << responseBody.length() << "\r\n";
            }

            // Add content type if not present
            if (responseHeaders.find("Content-Type") == responseHeaders.end()) {
                response << "Content-Type: application/json\r\n";
            }

            // Add connection close if not present
            if (responseHeaders.find("Connection") == responseHeaders.end()) {
                response << "Connection: close\r\n";
            }

            // End of headers
            response << "\r\n";

            // Body
            response << responseBody;

            // Send response
            std::string responseStr = response.str();
            send(clientSocket, responseStr.c_str(), responseStr.length(), 0);

            // Close connection
            CLOSE_SOCKET(clientSocket);
            utils::Logger::debug(std::format("Connection closed from {}", clientIp));
        }).detach();
    }
}

void ApiServer::initializeRoutes() {
    // CORS preflight handler for all API endpoints
    optionsHandlers_["/api"] = [this](const auto& headers, const auto& body) {
        return "HTTP/1.1 204 No Content\r\n\r\n";
    };

    // Health check
    getHandlers_["/api/health"] = [this](const auto& headers, const auto& body) {
        nlohmann::json response = {
            {"status", "ok"},
            {"version", "1.0.0"},
            {"timestamp", std::chrono::system_clock::now().time_since_epoch().count()}
        };

        return createSuccessResponse(response);
    };

    // Database metrics
    getHandlers_["/api/database/metrics"] = [this](const auto& headers, const auto& body) {
        try {
            // Check admin permission
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            // Only admins can view metrics
            if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
                return createErrorResponse(403, "Forbidden", "Admin permission required");
            }

            // Get database metrics
            auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
            auto metricsJson = dbMetrics.getAllMetrics();

            return createSuccessResponse(metricsJson);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };

    getHandlers_["/api/database/metrics/connection-pool"] = [this](const auto& headers, const auto& body) {
        try {
            // Check admin permission
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            // Only admins can view metrics
            if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
                return createErrorResponse(403, "Forbidden", "Admin permission required");
            }

            // Get connection pool metrics
            auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
            auto metricsJson = dbMetrics.getConnectionPoolMetrics();

            return createSuccessResponse(metricsJson);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };

    getHandlers_["/api/database/metrics/query-performance"] = [this](const auto& headers, const auto& body) {
        try {
            // Check admin permission
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            // Only admins can view metrics
            if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
                return createErrorResponse(403, "Forbidden", "Admin permission required");
            }

            // Get query performance metrics
            auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
            auto metricsJson = dbMetrics.getQueryPerformanceMetrics();

            return createSuccessResponse(metricsJson);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };



    // Authentication
    postHandlers_["/api/auth/login"] = [this](const auto& headers, const auto& body) {
        try {
            nlohmann::json requestJson = nlohmann::json::parse(body);

            if (!requestJson.contains("username") || !requestJson.contains("password")) {
                return createErrorResponse(400, "Bad Request", "Username and password are required");
            }

            std::string username = requestJson["username"];
            std::string password = requestJson["password"];

            security::TokenPair tokens = securityManager_->authenticate(username, password);

            if (tokens.accessToken.empty() || tokens.refreshToken.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid username or password");
            }

            nlohmann::json response = {
                {"access_token", tokens.accessToken},
                {"refresh_token", tokens.refreshToken},
                {"user", username},
                {"token_type", "Bearer"}
            };

            return createSuccessResponse(response);
        } catch (const nlohmann::json::exception& e) {
            return createErrorResponse(400, "Bad Request", "Invalid JSON format");
        }
    };

    // Token refresh
    postHandlers_["/api/auth/refresh"] = [this](const auto& headers, const auto& body) {
        try {
            nlohmann::json requestJson = nlohmann::json::parse(body);

            if (!requestJson.contains("refresh_token")) {
                return createErrorResponse(400, "Bad Request", "Refresh token is required");
            }

            std::string refreshToken = requestJson["refresh_token"];
            security::TokenPair tokens = securityManager_->refreshAccessToken(refreshToken);

            if (tokens.accessToken.empty() || tokens.refreshToken.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired refresh token");
            }

            nlohmann::json response = {
                {"access_token", tokens.accessToken},
                {"refresh_token", tokens.refreshToken},
                {"token_type", "Bearer"}
            };

            return createSuccessResponse(response);
        } catch (const nlohmann::json::exception& e) {
            return createErrorResponse(400, "Bad Request", "Invalid JSON format");
        }
    };

    // User info
    getHandlers_["/api/auth/user"] = [this](const auto& headers, const auto& body) {
        auto it = headers.find("Authorization");
        if (it == headers.end()) {
            return createErrorResponse(401, "Unauthorized", "Authentication required");
        }

        std::string authHeader = it->second;
        if (authHeader.substr(0, 7) != "Bearer ") {
            return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
        }

        std::string token = authHeader.substr(7);
        auto userInfo = securityManager_->getUserInfo(token);

        if (userInfo.empty()) {
            return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
        }

        nlohmann::json response = {
            {"username", userInfo["username"]},
            {"email", userInfo["email"]},
            {"is_admin", userInfo["is_admin"] == "t" || userInfo["is_admin"] == "true"},
            {"created_at", userInfo["created_at"]}
        };

        return createSuccessResponse(response);
    };

    // Logout
    postHandlers_["/api/auth/logout"] = [this](const auto& headers, const auto& body) {
        try {
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            std::string username = userInfo["username"];
            bool success = securityManager_->invalidateTokens(username);

            if (!success) {
                return createErrorResponse(500, "Internal Server Error", "Failed to invalidate tokens");
            }

            nlohmann::json response = {
                {"message", "Logged out successfully"}
            };

            return createSuccessResponse(response);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };

    // Credential management
    postHandlers_["/api/credentials/store"] = [this](const auto& headers, const auto& body) {
        try {
            // Check admin permission
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            // Only admins can manage credentials
            if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
                return createErrorResponse(403, "Forbidden", "Admin permission required");
            }

            // Parse request
            nlohmann::json requestJson = nlohmann::json::parse(body);

            if (!requestJson.contains("key") || !requestJson.contains("value")) {
                return createErrorResponse(400, "Bad Request", "Key and value are required");
            }

            std::string key = requestJson["key"];
            std::string value = requestJson["value"];

            // Store credential
            auto& credentialStore = security::CredentialStore::getInstance();
            bool success = credentialStore.storeCredential(key, value);

            if (!success) {
                return createErrorResponse(500, "Internal Server Error", "Failed to store credential");
            }

            nlohmann::json response = {
                {"message", "Credential stored successfully"}
            };

            return createSuccessResponse(response);
        } catch (const nlohmann::json::exception& e) {
            return createErrorResponse(400, "Bad Request", "Invalid JSON format");
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };

    getHandlers_["/api/credentials/get"] = [this](const auto& headers, const auto& body) {
        try {
            // Check admin permission
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            // Only admins can manage credentials
            if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
                return createErrorResponse(403, "Forbidden", "Admin permission required");
            }

            // Parse query parameters
            auto queryParams = parseQueryParams(headers.at("Query-String"));
            auto keyIt = queryParams.find("key");

            if (keyIt == queryParams.end() || keyIt->second.empty()) {
                return createErrorResponse(400, "Bad Request", "Key parameter is required");
            }

            std::string key = keyIt->second;

            // Get credential
            auto& credentialStore = security::CredentialStore::getInstance();
            std::string value = credentialStore.getCredential(key);

            if (value.empty()) {
                return createErrorResponse(404, "Not Found", "Credential not found");
            }

            nlohmann::json response = {
                {"key", key},
                {"value", value}
            };

            return createSuccessResponse(response);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };

    deleteHandlers_["/api/credentials/remove"] = [this](const auto& headers, const auto& body) {
        try {
            // Check admin permission
            auto it = headers.find("Authorization");
            if (it == headers.end()) {
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }

            std::string authHeader = it->second;
            if (authHeader.substr(0, 7) != "Bearer ") {
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }

            std::string token = authHeader.substr(7);
            auto userInfo = securityManager_->getUserInfo(token);

            if (userInfo.empty()) {
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }

            // Only admins can manage credentials
            if (userInfo["is_admin"] != "t" && userInfo["is_admin"] != "true") {
                return createErrorResponse(403, "Forbidden", "Admin permission required");
            }

            // Parse query parameters
            auto queryParams = parseQueryParams(headers.at("Query-String"));
            auto keyIt = queryParams.find("key");

            if (keyIt == queryParams.end() || keyIt->second.empty()) {
                return createErrorResponse(400, "Bad Request", "Key parameter is required");
            }

            std::string key = keyIt->second;

            // Remove credential
            auto& credentialStore = security::CredentialStore::getInstance();
            bool success = credentialStore.removeCredential(key);

            if (!success) {
                return createErrorResponse(404, "Not Found", "Credential not found");
            }

            nlohmann::json response = {
                {"message", "Credential removed successfully"}
            };

            return createSuccessResponse(response);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "Internal Server Error", e.what());
        }
    };

    // Query execution
    postHandlers_["/api/query"] = [this](const auto& headers, const auto& body) {
        try {
            nlohmann::json requestJson = nlohmann::json::parse(body);

            if (!requestJson.contains("query")) {
                return createErrorResponse(400, "Bad Request", "Query is required");
            }

            std::string query = requestJson["query"];
            std::vector<std::string> params;

            if (requestJson.contains("params") && requestJson["params"].is_array()) {
                for (const auto& param : requestJson["params"]) {
                    params.push_back(param.is_string() ? param.get<std::string>() : param.dump());
                }
            }

            auto result = connectionManager_->executeQuery(query, params);

            nlohmann::json response = {
                {"rows", result.size()},
                {"data", nlohmann::json::array()}
            };

            for (const auto& row : result) {
                response["data"].push_back(row);
            }

            return createSuccessResponse(response);
        } catch (const nlohmann::json::exception& e) {
            return createErrorResponse(400, "Bad Request", "Invalid JSON format");
        }
    };

    // Non-query execution
    postHandlers_["/api/execute"] = [this](const auto& headers, const auto& body) {
        try {
            nlohmann::json requestJson = nlohmann::json::parse(body);

            if (!requestJson.contains("statement")) {
                return createErrorResponse(400, "Bad Request", "Statement is required");
            }

            std::string statement = requestJson["statement"];
            std::vector<std::string> params;

            if (requestJson.contains("params") && requestJson["params"].is_array()) {
                for (const auto& param : requestJson["params"]) {
                    params.push_back(param.is_string() ? param.get<std::string>() : param.dump());
                }
            }

            int result = connectionManager_->executeNonQuery(statement, params);

            if (result < 0) {
                return createErrorResponse(500, "Internal Server Error", "Failed to execute statement");
            }

            nlohmann::json response = {
                {"affected_rows", result}
            };

            return createSuccessResponse(response);
        } catch (const nlohmann::json::exception& e) {
            return createErrorResponse(400, "Bad Request", "Invalid JSON format");
        }
    };
}

bool ApiServer::parseRequest(const std::string& request,
                           std::string& method,
                           std::string& path,
                           std::unordered_map<std::string, std::string>& headers,
                           std::string& body) {
    std::istringstream stream(request);
    std::string line;

    // Parse request line
    if (!std::getline(stream, line)) {
        return false;
    }

    std::regex requestLineRegex("(GET|POST|PUT|DELETE) (\\S+) HTTP/\\d\\.\\d");
    std::smatch match;

    if (!std::regex_search(line, match, requestLineRegex)) {
        return false;
    }

    method = match[1];
    path = match[2];

    // Parse headers
    while (std::getline(stream, line) && line != "\r") {
        size_t colonPos = line.find(':');
        if (colonPos != std::string::npos) {
            std::string name = line.substr(0, colonPos);
            std::string value = line.substr(colonPos + 1);

            // Trim whitespace
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of("\r\n") + 1);

            headers[name] = value;
        }
    }

    // Parse body
    std::string contentLengthStr = headers["Content-Length"];
    if (!contentLengthStr.empty()) {
        size_t contentLength = std::stoul(contentLengthStr);

        if (contentLength > 0) {
            std::vector<char> bodyBuffer(contentLength);
            stream.read(bodyBuffer.data(), contentLength);
            body = std::string(bodyBuffer.data(), contentLength);
        }
    }

    return true;
}

std::string ApiServer::createSuccessResponse(const nlohmann::json& data) {
    nlohmann::json responseJson = {
        {"success", true},
        {"data", data}
    };

    std::string responseBody = responseJson.dump();

    std::ostringstream response;
    response << "HTTP/1.1 200 OK\r\n";
    response << "Content-Type: application/json\r\n";
    response << "Content-Length: " << responseBody.length() << "\r\n";
    response << "Connection: close\r\n";
    response << "\r\n";
    response << responseBody;

    return response.str();
}

std::pair<std::unordered_map<std::string, std::string>, std::string> ApiServer::createSuccessResponseWithHeaders(const nlohmann::json& data) {
    nlohmann::json responseJson = {
        {"success", true},
        {"data", data}
    };

    std::string responseBody = responseJson.dump();

    std::unordered_map<std::string, std::string> headers;
    headers["Content-Type"] = "application/json";
    headers["Content-Length"] = std::to_string(responseBody.length());
    headers["Connection"] = "close";

    return {headers, responseBody};
}

std::pair<std::unordered_map<std::string, std::string>, std::string> ApiServer::createErrorResponse(
    int statusCode, const std::string& statusText, const std::string& message) {

    nlohmann::json responseJson = {
        {"success", false},
        {"error", {
            {"code", statusCode},
            {"message", message}
        }}
    };

    std::string responseBody = responseJson.dump();

    std::unordered_map<std::string, std::string> headers;
    headers["Content-Type"] = "application/json";
    headers["Content-Length"] = std::to_string(responseBody.length());
    headers["Connection"] = "close";
    headers["Status"] = std::to_string(statusCode) + " " + statusText;

    return {headers, responseBody};
}

std::unordered_map<std::string, std::string> ApiServer::parseQueryParams(const std::string& queryString) {
    std::unordered_map<std::string, std::string> params;

    if (queryString.empty()) {
        return params;
    }

    std::istringstream iss(queryString);
    std::string pair;

    while (std::getline(iss, pair, '&')) {
        size_t pos = pair.find('=');
        if (pos != std::string::npos) {
            std::string key = pair.substr(0, pos);
            std::string value = pair.substr(pos + 1);

            // URL decode
            key = urlDecode(key);
            value = urlDecode(value);

            params[key] = value;
        }
    }

    return params;
}

std::string ApiServer::urlDecode(const std::string& input) {
    std::string result;
    result.reserve(input.size());

    for (size_t i = 0; i < input.size(); ++i) {
        if (input[i] == '%' && i + 2 < input.size()) {
            int value;
            std::istringstream iss(input.substr(i + 1, 2));
            if (iss >> std::hex >> value) {
                result += static_cast<char>(value);
                i += 2;
            } else {
                result += input[i];
            }
        } else if (input[i] == '+') {
            result += ' ';
        } else {
            result += input[i];
        }
    }

    return result;
}

} // namespace dbservice::api
