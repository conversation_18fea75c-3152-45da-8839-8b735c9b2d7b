# Git Dashboard Cron Jobs and Server Synchronization Guide

## Overview

This document focuses on critical operational tasks for the Git Dashboard system including:

1. Cron job configuration for metrics collection and maintenance
2. SSL certificate synchronization between servers
3. Proper IP binding configuration
4. Inter-server communication setup

## Cron Job Configuration

### Metrics Collection Cron

The Git Dashboard relies on a cron job to collect system and Git metrics every minute:

```bash
# Edit root's crontab
sudo crontab -e
```

Add the following line to run the metrics collection script every minute:

```cron
* * * * * /opt/git-dashboard/collect-metrics.sh
```

Verify the cron job is properly registered:

```bash
sudo crontab -l | grep collect-metrics
```

### Archiving Historical Metrics

To prevent the metrics file from growing too large, set up a weekly archiving job:

```bash
# Add to root's crontab
0 0 * * 0 find /opt/git-dashboard/data/history -type f -name "metrics_*.json" -mtime +30 -delete
```

This will remove JSON files older than 30 days from the history directory.

### Log Rotation Cron

Ensure log rotation is configured properly by creating a logrotate configuration:

```bash
sudo nano /etc/logrotate.d/git-dashboard
```

Add the following content:

```
/opt/git-dashboard/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload git-dashboard >/dev/null 2>&1 || true
    endscript
}
```

### Health Check Cron

Set up a health check cron job to monitor the application and restart if necessary:

```bash
# Create health check script
sudo nano /opt/git-dashboard/health-check.sh
```

Add the following content:

```bash
#!/bin/bash

LOG_FILE="/opt/git-dashboard/logs/health-check.log"

# Check if API is responsive
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/api/status)

if [ "$API_STATUS" != "200" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] API health check failed with status $API_STATUS. Restarting service..." >> "$LOG_FILE"
    systemctl restart git-dashboard
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Health check passed." >> "$LOG_FILE"
fi

# Check metrics file freshness
METRICS_FILE="/opt/git-dashboard/data/metrics_history.json"
if [ -f "$METRICS_FILE" ]; then
    FILE_AGE=$(($(date +%s) - $(stat -c %Y "$METRICS_FILE")))
    if [ "$FILE_AGE" -gt 300 ]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Metrics file is stale (${FILE_AGE}s old). Running metrics collection..." >> "$LOG_FILE"
        /opt/git-dashboard/collect-metrics.sh
    fi
fi
```

Make the script executable:

```bash
sudo chmod +x /opt/git-dashboard/health-check.sh
```

Add to root's crontab to run every 5 minutes:

```cron
*/5 * * * * /opt/git-dashboard/health-check.sh
```

## SSL Certificate Synchronization

### Setting Up SSH Keys for Server Communication

To synchronize SSL certificates between the main Git server and the dashboard server, first set up SSH key-based authentication:

```bash
# On the dashboard server, generate SSH key
sudo -i
ssh-keygen -t ed25519 -C "<EMAIL>"
```

When prompted, save the key to the default location (/root/.ssh/id_ed25519) and do not set a passphrase for automated processes.

Copy the public key to the Git server:

```bash
# On dashboard server as root
cat ~/.ssh/id_ed25519.pub
```

On the Git server, add this key to the authorized_keys file:

```bash
# On Git server, create a dedicated sync user (if not exists)
sudo adduser git-sync
sudo mkdir -p /home/<USER>/.ssh
sudo nano /home/<USER>/.ssh/authorized_keys
```

Paste the public key and save the file, then set proper permissions:

```bash
# On Git server
sudo chmod 700 /home/<USER>/.ssh
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
sudo chown -R git-sync:git-sync /home/<USER>/.ssh
```

Grant sudo permissions for specific commands to the git-sync user:

```bash
sudo visudo -f /etc/sudoers.d/git-sync
```

Add the following line:

```
git-sync ALL=(ALL) NOPASSWD: /bin/systemctl reload nginx, /bin/tar, /bin/mkdir -p /etc/letsencrypt
```

### Creating SSL Certificate Sync Script

On the dashboard server, create a script to sync SSL certificates:

```bash
sudo nano /usr/local/bin/sync-ssl-certs.sh
```

Add the following content (adjust git-server to your Git server hostname or IP):

```bash
#!/bin/bash

# Log file
LOG_FILE="/opt/git-dashboard/logs/ssl-sync.log"
exec &> >(tee -a "$LOG_FILE")

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting SSL certificate synchronization"

# Define servers and paths
GIT_SERVER="git-server" # Use your Git server's hostname or IP
SSL_DIR="/etc/letsencrypt"
TEMP_DIR="/tmp"
TIMESTAMP=$(date '+%Y%m%d%H%M%S')
BACKUP_FILE="${TEMP_DIR}/letsencrypt-backup-${TIMESTAMP}.tar.gz"

# Create backup of certificates
echo "Creating certificate backup..."
cd "$SSL_DIR"
tar -czf "$BACKUP_FILE" live archive renewal

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to create backup archive"
    exit 1
fi

# Copy to Git server
echo "Copying certificates to Git server..."
scp -i /root/.ssh/id_ed25519 "$BACKUP_FILE" git-sync@${GIT_SERVER}:${TEMP_DIR}/

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to copy backup to Git server"
    rm "$BACKUP_FILE"
    exit 1
fi

# Extract on Git server and reload Nginx
echo "Extracting certificates on Git server..."
ssh -i /root/.ssh/id_ed25519 git-sync@${GIT_SERVER} "sudo mkdir -p ${SSL_DIR} && sudo tar -xzf ${TEMP_DIR}/letsencrypt-backup-${TIMESTAMP}.tar.gz -C ${SSL_DIR}/ && sudo systemctl reload nginx && rm ${TEMP_DIR}/letsencrypt-backup-${TIMESTAMP}.tar.gz"

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to extract certificates on Git server"
else
    echo "SUCCESS: Certificates synchronized successfully"
fi

# Clean up local backup
rm "$BACKUP_FILE"

echo "[$(date '+%Y-%m-%d %H:%M:%S')] SSL certificate synchronization completed"
```

Make the script executable:

```bash
sudo chmod +x /usr/local/bin/sync-ssl-certs.sh
```

### Setting Up Certbot Renewal Hook

To automatically sync certificates after renewal, create a post-renewal hook:

```bash
sudo mkdir -p /etc/letsencrypt/renewal-hooks/post
sudo nano /etc/letsencrypt/renewal-hooks/post/sync-ssl-certs.sh
```

Add the following content:

```bash
#!/bin/bash

# Run the synchronization script after certificate renewal
/usr/local/bin/sync-ssl-certs.sh
```

Make the hook executable:

```bash
sudo chmod +x /etc/letsencrypt/renewal-hooks/post/sync-ssl-certs.sh
```

### Testing SSL Certificate Synchronization

To test the synchronization:

```bash
# Run the sync script manually
sudo /usr/local/bin/sync-ssl-certs.sh

# Check the log
cat /opt/git-dashboard/logs/ssl-sync.log
```

## IP Binding Configuration

### Flask Application Binding

The Flask application binding can be configured in multiple ways:

1. **In Development Mode (app.py)**:
   ```python
   if __name__ == '__main__':
       app.run(host='0.0.0.0', port=8000, debug=True)
   ```

2. **In Gunicorn Configuration (gunicorn_config.py)**:
   ```python
   # Binding
   bind = "0.0.0.0:8000"  # Listen on all interfaces
   ```

3. **In Systemd Service File**:
   ```ini
   [Service]
   ExecStart=/opt/git-dashboard/venv/bin/gunicorn --bind=0.0.0.0:8000 --config /opt/git-dashboard/gunicorn_config.py app:app
   ```

### Environment Variables for IP Configuration

For a more flexible approach, modify the application to use environment variables:

```bash
sudo nano /opt/git-dashboard/app.py
```

Add the following at the top of the file:

```python
import os

# IP binding configuration from environment variables
BIND_IP = os.environ.get('BIND_IP', '0.0.0.0')
BIND_PORT = int(os.environ.get('BIND_PORT', 8000))
```

Then update the run section:

```python
if __name__ == '__main__':
    app.run(host=BIND_IP, port=BIND_PORT, debug=True)
```

Update the systemd service file to pass environment variables:

```bash
sudo nano /etc/systemd/system/git-dashboard.service
```

Add the following in the [Service] section:

```ini
Environment="BIND_IP=0.0.0.0"
Environment="BIND_PORT=8000"
```

Reload systemd and restart the service:

```bash
sudo systemctl daemon-reload
sudo systemctl restart git-dashboard
```

### Internal vs. External Interfaces

For better security, you can bind the Flask application to internal interfaces only and let Nginx handle external connections:

1. **Bind Flask to localhost only**:
   ```python
   bind = "127.0.0.1:8000"  # Listen only on localhost
   ```

2. **Configure Nginx to proxy requests**:
   ```nginx
   location /api/ {
       proxy_pass http://127.0.0.1:8000;
       ...
   }
   ```

## Multi-Server Deployment Configuration

### Master-Slave Configuration

To set up a master-slave configuration where one dashboard server collects metrics from multiple Git servers:

1. **Create an SSH configuration file**:
```bash
sudo nano /root/.ssh/config
```

Add all Git servers:
```
Host git-server-1
    HostName *************
    User git-sync
    IdentityFile /root/.ssh/id_ed25519
    IdentitiesOnly yes

Host git-server-2
    HostName *************
    User git-sync
    IdentityFile /root/.ssh/id_ed25519
    IdentitiesOnly yes
```

2. **Modify the metrics collection script to gather data from multiple servers**:
```bash
sudo nano /opt/git-dashboard/collect-multi-server-metrics.sh
```

```bash
#!/bin/bash

# Configuration
DASHBOARD_DIR="/opt/git-dashboard"
DATA_DIR="$DASHBOARD_DIR/data"
METRICS_FILE="$DATA_DIR/metrics_history.json"
SERVERS=("git-server-1" "git-server-2")

# Initialize metrics object
timestamp=$(date '+%Y-%m-%d %H:%M:%S')
metrics="{\
  \"timestamp\": \"$timestamp\",\
  \"servers\": ["

# Collect metrics from each server
for server in "${SERVERS[@]}"; do
    # Collect remote server metrics via SSH
    remote_metrics=$(ssh "$server" "uptime && df -h / && free -m && git rev-list --all --count")
    
    # Extract metrics from remote output
    load=$(echo "$remote_metrics" | grep -o "load average.*" | cut -d':' -f2 | tr -d ',')
    disk=$(echo "$remote_metrics" | grep -A 1 "Filesystem" | tail -1 | awk '{print $5}')
    memory=$(echo "$remote_metrics" | grep -A 2 "Mem:" | tail -1 | awk '{print $3*100/$2}')
    commits=$(echo "$remote_metrics" | tail -1)
    
    # Add to metrics object
    server_metrics="{\
      \"name\": \"$server\",\
      \"load\": $load,\
      \"disk\": \"$disk\",\
      \"memory\": $memory,\
      \"commits\": $commits\
    }"
    
    # Add comma between servers except for the last one
    if [ "$server" != "${SERVERS[-1]}" ]; then
        server_metrics="$server_metrics,"
    fi
    
    metrics="$metrics$server_metrics"
done

# Complete the metrics object
metrics="$metrics]\
}"

# Write to file
echo "$metrics" > "$METRICS_FILE"
```

### Load Balancing Configuration

For high-availability deployments, configure Nginx as a load balancer:

```bash
sudo nano /etc/nginx/sites-available/git-dashboard-lb.conf
```

```nginx
# Define upstream servers
upstream dashboard_backends {
    server *************:8000 weight=3;
    server *************:8000 weight=1 backup;
    keepalive 32;
}

server {
    listen 80;
    server_name dashboard.yourdomain.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dashboard.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/dashboard.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dashboard.yourdomain.com/privkey.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=63072000";
    
    # Static content
    location / {
        root /opt/git-dashboard;
        index index.html;
    }
    
    # API Load balancing
    location /api/ {
        proxy_pass http://dashboard_backends;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection handling
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
}
```

## Troubleshooting Server Communication Issues

### SSH Connection Issues

If SSH connections fail during synchronization:

```bash
# Test SSH connection with verbose output
ssh -i /root/.ssh/id_ed25519 -v git-sync@git-server

# Check SSH public key format
cat /root/.ssh/id_ed25519.pub

# Verify permissions on SSH directory
ls -la /root/.ssh/
```

Common fixes:

```bash
# Fix SSH key permissions
chmod 600 /root/.ssh/id_ed25519
chmod 644 /root/.ssh/id_ed25519.pub
chmod 700 /root/.ssh

# Check host key verification
ssh-keyscan git-server >> /root/.ssh/known_hosts
```

### SSL Certificate Sync Failures

If SSL synchronization fails:

```bash
# Check if certificates exist
ls -la /etc/letsencrypt/live/

# Test basic file transfer
scp -i /root/.ssh/id_ed25519 /tmp/test.txt git-sync@git-server:/tmp/

# Verify sudo permissions on Git server
ssh -i /root/.ssh/id_ed25519 git-sync@git-server "sudo -l"
```

### Network Connectivity Issues

```bash
# Check if ports are open
telnet git-server 22

# Trace route to server
traceroute git-server

# Check firewall status
sudo ufw status
```
