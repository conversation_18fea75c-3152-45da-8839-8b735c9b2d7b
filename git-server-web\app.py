from flask import Flask, send_from_directory, jsonify, request, Response, render_template
import os
import json
import psutil
import datetime
import subprocess
import git
import re
import time
from pathlib import Path
from functools import wraps
import logging
from logging.handlers import RotatingFileHandler
from werkzeug.middleware.proxy_fix import ProxyFix
import traceback
import random
import platform
from datetime import timedelta, datetime
import requests

app = Flask(__name__)
app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)

# Configure logging
# Minimal logging to console only
import sys
logging.basicConfig(
    stream=sys.stdout,
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
)


# Get bind configuration from environment or use defaults
BIND_IP = os.environ.get('BIND_IP', '0.0.0.0')
BIND_PORT = int(os.environ.get('BIND_PORT', 8000))

# C++ Git Repository Service configuration
GIT_REPO_SERVICE_URL = os.environ.get('GIT_REPO_SERVICE_URL', 'http://localhost:8080')
USE_CPP_SERVICE = os.environ.get('USE_CPP_SERVICE', 'true').lower() in ('true', 'yes', '1')

# C++ Logging Service configuration
LOGGING_SERVICE_URL = os.environ.get('LOGGING_SERVICE_URL', 'http://localhost:8081')
USE_CPP_LOGGING = os.environ.get('USE_CPP_LOGGING', 'true').lower() in ('true', 'yes', '1')

# Add CORS headers to all responses
@app.after_request
def add_cors_headers(response):
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
    return response

# Cache control decorator
def cache_control(max_age=3600):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = f(*args, **kwargs)
            if isinstance(response, Response):
                response.headers['Cache-Control'] = f'public, max-age={max_age}'
            return response
        return decorated_function
    return decorator

# Helper functions
def load_metrics_history():
    """Load metrics history from JSON file"""
    metrics_file = os.path.join(os.path.dirname(__file__), 'data', 'metrics_history.json')
    history_file = os.path.join(os.path.dirname(__file__), 'data', 'history', 'metrics_history.json')

    # First try the main metrics file
    if os.path.exists(metrics_file) and os.path.getsize(metrics_file) > 0:
        try:
            with open(metrics_file, 'r') as f:
                data = json.load(f)
                app.logger.info(f"Loaded metrics data from {metrics_file}")
                return data
        except json.JSONDecodeError:
            app.logger.error(f"Invalid JSON in metrics file: {metrics_file}")

    # Then try the history file
    if os.path.exists(history_file) and os.path.getsize(history_file) > 0:
        try:
            with open(history_file, 'r') as f:
                data = json.load(f)
                app.logger.info(f"Loaded metrics data from {history_file}")
                return data
        except json.JSONDecodeError:
            app.logger.error(f"Invalid JSON in history file: {history_file}")

    # If no valid data is found, return an empty array with a special flag
    app.logger.warning("No valid metrics data found in any location")
    return {"no_data": True, "metrics": []}

def get_server_metrics():
    """Get current server metrics"""
    try:
        return {
            'cpu_usage': psutil.cpu_percent(interval=1),
            'mem_usage': psutil.virtual_memory().percent,
            'load_avg': os.getloadavg()[0],
            'disk_usage': psutil.disk_usage('/').percent,
            'uptime': subprocess.check_output(['uptime', '-p']).decode().strip()
        }
    except Exception as e:
        app.logger.error(f"Error getting server metrics: {e}")
        return {}

def format_size(size):
    """Format size in bytes to human-readable format"""
    for unit in ['', 'K', 'M', 'G', 'T', 'P', 'E', 'Z']:
        if size < 1024.0:
            return f"{size:.2f}{unit}B"
        size /= 1024.0
    return f"{size:.2f}YB"

# Routes
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/js/<path:path>')
def send_js(path):
    return send_from_directory('js', path)

@app.route('/css/<path:path>')
def send_css(path):
    return send_from_directory('css', path)

@app.route('/static/<path:path>')
def send_static(path):
    return send_from_directory('static', path)

@app.route('/api/metrics/current')
@cache_control(max_age=60)  # Cache current metrics for 1 minute
def current_metrics():
    """Get current server metrics"""
    try:
        metrics = get_server_metrics()
        # Check if metrics collection is working
        history = load_metrics_history()

        if "no_data" in history and history["no_data"]:
            metrics["status"] = "no_historical_data"
            metrics["message"] = "No historical metrics data available. Please check if metrics collection is running."

        return jsonify(metrics)
    except Exception as e:
        app.logger.error(f"Error getting current metrics: {e}")
        return jsonify({'error': 'Failed to get current metrics', 'details': str(e)}), 500

@app.route('/api/metrics/history')
@cache_control(max_age=300)  # Cache metrics history for 5 minutes
def metrics_history():
    """Get metrics history data"""
    try:
        history = load_metrics_history()

        # If no data is available, return a specific error message that the frontend can handle
        if "no_data" in history and history["no_data"]:
            return jsonify({
                'error': 'no_data',
                'message': 'No metrics data is available. The metrics collection process may not be running properly.',
                'timestamp': datetime.datetime.now().isoformat()
            }), 404

        return jsonify(history)
    except Exception as e:
        app.logger.error(f"Error getting metrics history: {e}")
        return jsonify({'error': 'Failed to load metrics history', 'details': str(e)}), 500

@app.route('/api/certificates/status')
def certificate_status():
    """Get SSL certificate status"""
    try:
        # Linux server code
            cert_file = "/etc/letsencrypt/live/chcit.org/fullchain.pem"
            if not os.path.exists(cert_file):
                return jsonify({
                    "status": "error",
                    "message": "SSL certificate not found"
                }), 404

            # Get certificate expiration
            output = subprocess.check_output([
                "openssl", "x509", "-in", cert_file, "-noout", "-enddate"
            ]).decode()

            # Parse expiry date from output
            match = re.search(r"notAfter=(.+)", output)
            if not match:
                raise ValueError("Failed to parse certificate expiry date")

            expiry_str = match.group(1)
            expiry_date = datetime.strptime(expiry_str, "%b %d %H:%M:%S %Y %Z")
            days_remaining = (expiry_date - datetime.now()).days

            # Get more detailed certificate information
            cert_details = subprocess.check_output([
                "openssl", "x509", "-in", cert_file, "-text", "-noout"
            ]).decode()

            # Get issuer information
            issuer_output = subprocess.check_output([
                "openssl", "x509", "-in", cert_file, "-noout", "-issuer"
            ]).decode()

            # Get subject information
            subject_output = subprocess.check_output([
                "openssl", "x509", "-in", cert_file, "-noout", "-subject"
            ]).decode()

            # Get start date
            start_date_output = subprocess.check_output([
                "openssl", "x509", "-in", cert_file, "-noout", "-startdate"
            ]).decode()

            # Parse start date
            start_match = re.search(r"notBefore=(.+)", start_date_output)
            start_date_str = start_match.group(1) if start_match else "Unknown"

            try:
                start_date = datetime.strptime(start_date_str, "%b %d %H:%M:%S %Y %Z")
                start_date_iso = start_date.isoformat()
            except:
                start_date_iso = None

            # Extract domain names from the certificate
            domains = []
            san_match = re.search(r"DNS:([^\n]+)", cert_details)
            if san_match:
                domains = [domain.strip() for domain in san_match.group(1).split(",DNS:")]

            # Get last sync time from log
            log_file = "/opt/git-dashboard/logs/cert-sync.log"
            last_sync = "Unknown"
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    for line in reversed(f.readlines()):
                        if "Certificate sync process completed" in line:
                            match = re.search(r"\[(.*?)\]", line)
                            if match:
                                last_sync = match.group(1)
                            break

            # Extract issuer from the output
            issuer = re.search(r"issuer=(.+)", issuer_output)
            issuer_name = issuer.group(1) if issuer else "Unknown"

            # Extract subject from the output
            subject = re.search(r"subject=(.+)", subject_output)
            subject_name = subject.group(1) if subject else "Unknown"

            # Check if certificate is self-signed
            is_self_signed = subject_name == issuer_name

            return jsonify({
                "status": "success",
                "expiry_date": expiry_date.isoformat(),
                "start_date": start_date_iso,
                "days_until_expiry": days_remaining,
                "last_sync": last_sync,
                "cert_info": cert_details,
                "issuer": issuer_name,
                "subject": subject_name,
                "domains": domains,
                "is_self_signed": is_self_signed,
                "algorithm": "RSA" if "RSA Public Key" in cert_details else "ECDSA" if "ECDSA Public Key" in cert_details else "Unknown"
            })
    except Exception as e:
        app.logger.error(f"Error checking certificate status: {e}")
        return jsonify({
            "status": "error",
            "message": f"Failed to check certificate status: {str(e)}"
        }), 500

@app.route('/api/certificates/sync', methods=['POST'])
def sync_certificates():
    """Trigger certificate sync process using the helper program"""
    try:
        # Linux server code
            helper_path = "/opt/git-dashboard/bin/cert_sync_helper"

            # Check if the helper exists
            if not os.path.exists(helper_path):
                app.logger.error(f"Certificate sync helper not found at {helper_path}")
                return jsonify({
                    "status": "error",
                    "message": "Certificate sync helper not found"
                }), 500

            # Run the helper program
            result = subprocess.run(
                [helper_path],
                capture_output=True,
                text=True,
                timeout=180
            )

            # Check if the command succeeded
            if result.returncode != 0:
                app.logger.error(f"Certificate sync failed: {result.stderr}")
                return jsonify({
                    "status": "error",
                    "message": "Certificate sync failed",
                    "details": result.stderr
                }), 500

            # Try to parse JSON output from the helper
            try:
                import json
                output_data = json.loads(result.stdout)
                return jsonify(output_data)
            except json.JSONDecodeError:
                # If not JSON, return raw output
                return jsonify({
                    "status": "success",
                    "message": "Certificate sync completed successfully",
                    "details": result.stdout
                })

    except subprocess.TimeoutExpired:
        app.logger.error("Certificate sync timed out after 180 seconds")
        return jsonify({
            "status": "error",
            "message": "Certificate sync timed out"
        }), 504

    except Exception as e:
        app.logger.error(f"Error syncing certificates: {e}")
        return jsonify({
            "status": "error",
            "message": f"Failed to sync certificates: {str(e)}"
        }), 500

@app.route('/api/git/repositories')
@cache_control(max_age=300)  # Cache repo list for 5 minutes
def list_repositories():
    """List all available git repositories"""
    try:
        # Linux server code
            # Try to use C++ service if enabled
            if USE_CPP_SERVICE:
                try:
                    app.logger.info(f"Proxying repository list request to C++ service at {GIT_REPO_SERVICE_URL}")
                    cpp_response = requests.get(f"{GIT_REPO_SERVICE_URL}/api/repositories", timeout=10)

                    # Check if the request was successful
                    if cpp_response.status_code == 200:
                        app.logger.info("Successfully retrieved repository list from C++ service")

                        # Transform the C++ service response to match the expected format
                        cpp_data = cpp_response.json()

                        # Extract repositories from C++ response
                        repositories = []
                        if 'repositories' in cpp_data and isinstance(cpp_data['repositories'], list):
                            for repo in cpp_data['repositories']:
                                # Transform to match the expected format
                                transformed_repo = {
                                    'name': repo.get('name', ''),
                                    'path': repo.get('path', ''),
                                    'last_commit': repo.get('last_commit', None),
                                    'last_commit_hash': repo.get('last_commit_hash', None),
                                    'branches': repo.get('branches', 0),
                                    'active_branch': repo.get('branch_list', ['master'])[0] if repo.get('branch_list') else None,
                                    'size': repo.get('size', 0),
                                    'size_formatted': repo.get('size_formatted', '0 B'),
                                    'health_score': repo.get('health_score', 0),
                                    'health_status': repo.get('health_status', 'unknown'),
                                    'health_message': repo.get('health_message', '')
                                }
                                repositories.append(transformed_repo)

                        return jsonify({
                            'status': 'success',
                            'repositories': repositories,
                            'timestamp': cpp_data.get('timestamp', datetime.now().isoformat()),
                            'count': cpp_data.get('count', len(repositories))
                        })
                    else:
                        app.logger.warning(f"C++ service returned status code {cpp_response.status_code}")
                except Exception as e:
                    app.logger.error(f"Error proxying to C++ service: {e}")
                    app.logger.info("Falling back to Python implementation")

            # Fall back to original implementation
            repo_path = "/home/<USER>/repositories"
            if not os.path.exists(repo_path):
                return jsonify({
                    'error': 'git_directory_not_found',
                    'message': 'Git repositories directory not found',
                    'repositories': []
                })

            repositories = []
            for item in os.listdir(repo_path):
                full_path = os.path.join(repo_path, item)
                if os.path.isdir(full_path) and (item.endswith('.git') or os.path.exists(os.path.join(full_path, 'HEAD'))):
                    try:
                        repo = git.Repo(full_path)
                        head = repo.head

                        # Get repository size
                        repo_size = 0
                        try:
                            # Use git command to get repository size
                            git_du_cmd = ['sudo', '-u', 'git', 'du', '-sk', full_path]
                            result = subprocess.run(git_du_cmd, capture_output=True, text=True, check=True)
                            # Parse output (format: '<size_kb>\t<path>')
                            size_kb = int(result.stdout.strip().split()[0])
                            repo_size = size_kb * 1024  # Convert KB to bytes
                        except Exception as e:
                            app.logger.warning(f"Failed to get size for repository {item}: {e}")

                        repositories.append({
                            'name': item,
                            'path': full_path,
                            'last_commit': head.commit.authored_datetime.isoformat() if not repo.bare else None,
                            'branches': len(repo.branches),
                            'active_branch': str(repo.active_branch) if not repo.bare and not repo.head.is_detached else None,
                            'size': repo_size,
                            'size_formatted': format_size(repo_size)
                        })
                    except (git.InvalidGitRepositoryError, git.NoSuchPathError, ValueError) as e:
                        app.logger.error(f"Error with repository {item}: {e}")
                        # Include repository with error message
                        repositories.append({
                            'name': item,
                            'path': full_path,
                            'error': str(e)
                        })

            if not repositories:
                return jsonify({
                    'status': 'no_repositories',
                    'message': 'No Git repositories found',
                    'repositories': []
                })

            return jsonify({
                'status': 'success',
                'repositories': repositories
            })
    except Exception as e:
        app.logger.error(f"Error listing repositories: {e}")
        return jsonify({'error': 'Failed to list repositories', 'details': str(e)}), 500

@app.route('/api/git/repository/<repo_name>/commits')
@cache_control(max_age=300)  # Cache commit history for 5 minutes
def repository_commit_history(repo_name):
    """Get commit history for a specific repository"""
    try:
        # Get request parameters
        days = request.args.get('days', default=30, type=int)
        fallback = request.args.get('fallback', default=False, type=bool)

        # Convert fallback string to boolean if needed
        if isinstance(fallback, str):
            fallback = fallback.lower() in ('true', 't', 'yes', 'y', '1')

        app.logger.info(f"Commit history request for {repo_name}, days={days}, fallback={fallback}")

        # Limit days to 90 to avoid excessive processing
        days = min(days, 90)

        # Try to use C++ service if enabled
        if USE_CPP_SERVICE:
            try:
                app.logger.info(f"Proxying commit history request to C++ service at {GIT_REPO_SERVICE_URL}")
                cpp_response = requests.get(
                    f"{GIT_REPO_SERVICE_URL}/api/repository/{repo_name}/commits",
                    params={'days': days},
                    timeout=15  # Longer timeout for commit history
                )

                # Check if the request was successful
                if cpp_response.status_code == 200:
                    app.logger.info(f"Successfully retrieved commit history for {repo_name} from C++ service")
                    return jsonify(cpp_response.json())
                elif cpp_response.status_code == 404 and fallback:
                    app.logger.warning(f"C++ service could not find repository {repo_name}, using fallback")
                    return jsonify({
                        'status': 'error',
                        'message': f'Repository {repo_name} not found',
                        'data': generate_sample_commit_history(repo_name, days),
                        'method': 'fallback'
                    })
                else:
                    app.logger.warning(f"C++ service returned status code {cpp_response.status_code}")
            except Exception as e:
                app.logger.error(f"Error proxying to C++ service: {e}")
                app.logger.info("Falling back to Python implementation")

        # Construct the repository path - handle both Linux and Windows paths
        # Use the Linux repository path
        repo_path = os.path.join("/home/<USER>/repositories", repo_name)

        app.logger.info(f"Looking for repository at: {repo_path}")

        # Check if repository exists
        if not os.path.exists(repo_path):
            app.logger.error(f"Repository not found: {repo_path}")
            if fallback:
                app.logger.info(f"Using sample data for non-existent repository: {repo_name}")
                return jsonify({
                    'status': 'error',
                    'message': f'Repository {repo_name} not found',
                    'data': generate_sample_commit_history(repo_name, days),
                    'method': 'fallback'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': f'Repository {repo_name} not found'
                }), 404

        # Check if this is a bare repository (no .git directory but has git files directly)
        is_bare_repo = not os.path.exists(os.path.join(repo_path, '.git')) and \
                      all(item in os.listdir(repo_path) for item in ['HEAD', 'objects', 'refs'])

        if is_bare_repo:
            app.logger.info(f"Detected bare repository at {repo_path}")

        # Initialize variables for tracking attempts
        attempts = 0
        max_attempts = 2  # Reduced from 3 to 2 (direct git and GitPython)
        error_messages = []

        # Try different methods to get commit history
        while attempts < max_attempts:
            try:
                app.logger.info(f"Attempt {attempts+1} to get commit history for {repo_name}")

                # Method 1: Use direct git command without sudo
                if attempts == 0:
                    app.logger.info(f"Using direct git command without sudo for {repo_name}")

                    # First, ensure the repository is in the safe.directory config
                    try:
                        # Try to add the repository to safe.directory with sudo
                        # This is needed on Linux systems where the web server runs as a different user
                        safe_cmd = ['sudo', 'git', 'config', '--system', '--add', 'safe.directory', repo_path]
                        app.logger.info(f"Running command: {' '.join(safe_cmd)}")
                        subprocess.run(
                            safe_cmd,
                            check=False,  # Don't check return code as it might fail but we still want to try the git command
                            capture_output=True,
                            text=True
                        )

                        # Also try with --global as a fallback
                        subprocess.run(
                            ['git', 'config', '--global', '--add', 'safe.directory', repo_path],
                            check=False,  # Don't check return code
                            capture_output=True,
                            text=True
                        )

                        # For bare repositories, also try adding with an asterisk
                        if is_bare_repo:
                            subprocess.run(
                                ['git', 'config', '--global', '--add', 'safe.directory', '*'],
                                check=False,  # Don't check return code
                                capture_output=True,
                                text=True
                            )

                        app.logger.info(f"Added {repo_path} to safe.directory config")
                    except Exception as e:
                        app.logger.warning(f"Unexpected error adding safe.directory: {str(e)}")

                    # Get the date range
                    end_date = datetime.datetime.now()
                    start_date = end_date - datetime.timedelta(days=days)

                    # Format dates for git log
                    start_date_str = start_date.strftime('%Y-%m-%d')

                    # Adjust command for bare repositories
                    cmd = ['git']

                    # For bare repositories, we need to use --git-dir instead of -C
                    if is_bare_repo:
                        cmd.extend(['--git-dir', repo_path])
                    else:
                        cmd.extend(['-C', repo_path])

                    # Add the log command and parameters
                    cmd.extend([
                        'log',
                        f'--since={start_date_str}',
                        '--format=%ad',
                        '--date=short'
                    ])

                    app.logger.info(f"Running command: {' '.join(cmd)}")

                    try:
                        # Check if the directory exists and is a git repository
                        if not is_bare_repo and not os.path.exists(os.path.join(repo_path, '.git')):
                            app.logger.warning(f"Not a git repository or .git directory not found: {repo_path}")
                            # Try to find the .git directory
                            potential_git_dirs = []
                            for root, dirs, _ in os.walk(repo_path):
                                if '.git' in dirs:
                                    potential_git_dirs.append(os.path.join(root, '.git'))
                                    break

                            if potential_git_dirs:
                                app.logger.info(f"Found potential .git directories: {potential_git_dirs}")
                                # Update the repo_path to the parent of the first .git directory found
                                repo_path = os.path.dirname(potential_git_dirs[0])
                                app.logger.info(f"Updated repo_path to: {repo_path}")
                                # Update the command with the new path
                                cmd[2] = repo_path
                            else:
                                app.logger.error(f"No .git directory found in {repo_path} and not a bare repository")
                                raise Exception(f"No .git directory found in {repo_path} and not a bare repository")

                        result = subprocess.run(
                            cmd,
                            check=True,
                            capture_output=True,
                            text=True,
                            timeout=10  # Add timeout to prevent hanging
                        )

                        # Process the output
                        commit_dates = result.stdout.strip().split('\n')

                        # Filter out empty lines
                        commit_dates = [date for date in commit_dates if date]

                        app.logger.info(f"Found {len(commit_dates)} commits for {repo_name}")

                        # Count commits per day
                        commit_counts = {}
                        for date in commit_dates:
                            if date in commit_counts:
                                commit_counts[date] += 1
                            else:
                                commit_counts[date] = 1

                        # Generate date range for all days in the period
                        date_range = []
                        current_date = start_date
                        while current_date <= end_date:
                            date_str = current_date.strftime('%Y-%m-%d')
                            date_range.append(date_str)
                            current_date += datetime.timedelta(days=1)

                        # Create the final data structure
                        data = []
                        for date_str in date_range:
                            count = commit_counts.get(date_str, 0)
                            display_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').strftime('%m/%d')
                            data.append({
                                'date': date_str,
                                'displayDate': display_date,
                                'count': count
                            })

                        # Return the data
                        return jsonify({
                            'status': 'success',
                            'message': 'Commit history retrieved successfully',
                            'data': data,
                            'method': 'direct_git'
                        })
                    except subprocess.TimeoutExpired:
                        error_message = "Command timed out after 10 seconds"
                        app.logger.error(f"Timeout running direct git log for {repo_name}: {error_message}")
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")
                    except subprocess.CalledProcessError as e:
                        error_message = f"Command failed with exit code {e.returncode}: {e.stderr}"
                        app.logger.error(f"Error running direct git log for {repo_name}: {error_message}")
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")
                    except Exception as e:
                        error_message = str(e)
                        app.logger.error(f"Unexpected error running direct git log for {repo_name}: {error_message}")
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")

                # Method 2: Use GitPython
                else:  # attempts == 1
                    app.logger.info(f"Using GitPython to get commit history for {repo_name}")

                    # Get the date range
                    end_date = datetime.datetime.now()
                    start_date = end_date - datetime.timedelta(days=days)

                    try:
                        # Open the repository - handle bare repositories
                        app.logger.info(f"Opening repository with GitPython: {repo_path} (bare: {is_bare_repo})")
                        repo = git.Repo(repo_path, search_parent_directories=True, odbt=git.GitDB)

                        # Get commits in the date range
                        app.logger.info(f"Getting commits since {start_date.strftime('%Y-%m-%d')}")
                        commits = list(repo.iter_commits(since=start_date.strftime('%Y-%m-%d')))

                        app.logger.info(f"Found {len(commits)} commits for {repo_name} using GitPython")

                        # Group commits by date
                        commit_counts = {}
                        for commit in commits:
                            # Convert timestamp to date string
                            date_str = datetime.datetime.fromtimestamp(commit.committed_date).strftime('%Y-%m-%d')

                            if date_str in commit_counts:
                                commit_counts[date_str] += 1
                            else:
                                commit_counts[date_str] = 1

                        # Generate date range for all days in the period
                        date_range = []
                        current_date = start_date
                        while current_date <= end_date:
                            date_str = current_date.strftime('%Y-%m-%d')
                            date_range.append(date_str)
                            current_date += datetime.timedelta(days=1)

                        # Create the final data structure
                        data = []
                        for date_str in date_range:
                            count = commit_counts.get(date_str, 0)
                            display_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').strftime('%m/%d')
                            data.append({
                                'date': date_str,
                                'displayDate': display_date,
                                'count': count
                            })

                        # Return the data
                        return jsonify({
                            'status': 'success',
                            'message': 'Commit history retrieved successfully',
                            'data': data,
                            'method': 'gitpython'
                        })
                    except git.exc.GitCommandError as e:
                        error_message = f"GitPython command error: {str(e)}"
                        app.logger.error(f"GitPython error for {repo_name}: {error_message}")
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")
                    except git.exc.InvalidGitRepositoryError as e:
                        error_message = f"Invalid Git repository: {str(e)}"
                        app.logger.error(f"Invalid Git repository error for {repo_name}: {error_message}")
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")
                    except git.exc.NoSuchPathError as e:
                        error_message = f"No such path error: {str(e)}"
                        app.logger.error(f"Path error for {repo_name}: {error_message}")
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")
                    except Exception as e:
                        error_message = str(e)
                        app.logger.error(f"Unexpected GitPython error for {repo_name}: {error_message}")
                        app.logger.error(traceback.format_exc())
                        error_messages.append(f"Attempt {attempts+1}: {error_message}")

            except Exception as e:
                error_message = str(e)
                error_messages.append(f"Attempt {attempts+1}: {error_message}")
                app.logger.error(f"Error getting commit history for {repo_name} (attempt {attempts+1}): {error_message}")

            attempts += 1

        # If all attempts failed and fallback is enabled, return sample data
        if fallback:
            app.logger.warning(f"All attempts failed for {repo_name}, using sample data as fallback")
            return jsonify({
                'status': 'error',
                'message': f'Failed to get commit history after {max_attempts} attempts. Using sample data.',
                'errors': error_messages,
                'data': generate_sample_commit_history(repo_name, days),
                'method': 'fallback'
            })
        else:
            # Return error with all error messages
            app.logger.error(f"All attempts failed for {repo_name} and no fallback requested")
            return jsonify({
                'status': 'error',
                'message': f'Failed to get commit history after {max_attempts} attempts.',
                'errors': error_messages
            }), 500

    except Exception as e:
        app.logger.error(f"Unexpected error in repository_commit_history: {str(e)}")
        app.logger.error(traceback.format_exc())

        # If fallback is enabled, return sample data
        if fallback:
            return jsonify({
                'status': 'error',
                'message': f'Unexpected error: {str(e)}. Using sample data.',
                'data': generate_sample_commit_history(repo_name, days),
                'method': 'fallback'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': f'Unexpected error: {str(e)}'
            }), 500

def generate_sample_commit_history(repo_name, days):
    """Generate sample commit history data for testing or when permissions are an issue"""
    app.logger.info(f"Generating sample commit history for {repo_name} over {days} days")

    # Initialize data structure
    commit_history = []
    end_date = datetime.datetime.now()

    # Generate a realistic total number of commits based on repository name
    # This creates different patterns for different repositories
    repo_size_factor = len(repo_name) % 5 + 1  # 1-5 based on name length
    total_commits = random.randint(50, 200) * repo_size_factor
    app.logger.info(f"Generating sample data with approximately {total_commits} total commits")

    # Create a realistic distribution of commits over days
    # More commits on weekdays, fewer on weekends, and occasional spikes
    daily_commits = {}
    remaining_commits = total_commits

    # First pass: assign base commits to each day
    for i in range(days):
        date = end_date - datetime.timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')

        # Weekends have fewer commits
        weekday = date.weekday()  # 0-6 where 0 is Monday and 6 is Sunday
        if weekday >= 5:  # Weekend
            base_commits = random.randint(0, 5)
        else:  # Weekday
            base_commits = random.randint(5, 15)

        # More recent days might have more activity
        if i < 7:  # Last week
            base_commits = int(base_commits * 1.5)

        # Occasionally add a spike of activity (simulating a release or major feature)
        if random.random() < 0.1:  # 10% chance of a spike
            base_commits += random.randint(10, 30)

        daily_commits[date_str] = min(base_commits, remaining_commits)
        remaining_commits -= daily_commits[date_str]
        if remaining_commits <= 0:
            break

    # Second pass: distribute any remaining commits to random days
    if remaining_commits > 0:
        dates_list = list(daily_commits.keys())
        while remaining_commits > 0 and dates_list:
            date_str = random.choice(dates_list)
            add_commits = min(random.randint(1, 5), remaining_commits)
            daily_commits[date_str] += add_commits
            remaining_commits -= add_commits

    # Format the data for the response
    for date_str, count in sorted(daily_commits.items()):
        display_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').strftime('%m/%d')
        commit_history.append({
            'date': date_str,
            'displayDate': display_date,
            'count': count
        })

    # Calculate actual total after distribution
    actual_total = sum(item['count'] for item in commit_history)
    app.logger.info(f"Generated sample data with {actual_total} total commits across {len(commit_history)} days")

    return jsonify({
        'status': 'success',
        'repository': repo_name,
        'days': days,
        'commit_history': commit_history,
        'total_commits': actual_total,
        'note': 'This is sample data generated as a fallback method'
    })

@app.route('/api/git/repository/<repo_name>/size-comparison')
@cache_control(max_age=300)  # Cache size comparison for 5 minutes
def repository_size_comparison(repo_name):
    """Get size comparison for repositories relative to the specified repository"""
    try:
        # Construct the repository path
        repo_path = os.path.join("/home/<USER>/repositories", repo_name)

        # Check if repository exists
        if not os.path.exists(repo_path):
            app.logger.error(f"Repository not found: {repo_path}")
            return jsonify({
                'status': 'error',
                'message': f'Repository {repo_name} not found'
            }), 404

        # Get base directory for all repositories
        base_repo_path = "/home/<USER>/repositories"

        # Get all repositories
        all_repos = []
        for item in os.listdir(base_repo_path):
            full_path = os.path.join(base_repo_path, item)
            if os.path.isdir(full_path) and (item.endswith('.git') or os.path.exists(os.path.join(full_path, 'HEAD'))):
                try:
                    # Get repository size
                    repo_size = 0
                    try:
                        # Use git command to get repository size
                        git_du_cmd = ['sudo', '-u', 'git', 'du', '-sm', full_path]
                        result = subprocess.run(git_du_cmd, capture_output=True, text=True, check=True)
                        # Parse output (format: '<size_mb>\t<path>')
                        size_mb = float(result.stdout.strip().split()[0])
                        repo_size = size_mb
                    except Exception as e:
                        app.logger.warning(f"Failed to get size for repository {item}: {e}")

                    all_repos.append({
                        'name': item,
                        'size': repo_size,
                        'size_formatted': format_size(repo_size * 1024 * 1024)  # Convert MB to bytes for formatting
                    })
                except Exception as e:
                    app.logger.error(f"Error with repository {item}: {e}")

        # Sort repositories by size (descending)
        all_repos.sort(key=lambda x: x['size'], reverse=True)

        # Limit to 10 repositories to avoid cluttering the chart
        top_repos = all_repos[:10]

        # Highlight the selected repository
        for repo in top_repos:
            repo['is_selected'] = (repo['name'] == repo_name)

        return jsonify({
            'status': 'success',
            'repositories': top_repos
        })
    except Exception as e:
        app.logger.error(f"Error getting repository size comparison: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/git/test-repository-path/<repo_name>')
def test_repository_path(repo_name):
    """Test repository path detection and accessibility"""
    try:
        # Construct the repository path - handle both Linux and Windows paths
        # Use the Linux repository path
        repo_path = os.path.join("/home/<USER>/repositories", repo_name)

        # Check if the directory exists
        dir_exists = os.path.exists(repo_path)

        # Check if it's a git repository
        git_dir_exists = os.path.exists(os.path.join(repo_path, '.git'))

        # Try to find .git directory if not in the expected location
        potential_git_dirs = []
        if dir_exists and not git_dir_exists:
            for root, dirs, _ in os.walk(repo_path):
                if '.git' in dirs:
                    potential_git_dirs.append(os.path.join(root, '.git'))

        # Try to run a simple git command
        git_command_success = False
        git_command_output = ""
        git_command_error = ""
        if dir_exists:
            try:
                cmd = ['git', '-C', repo_path, 'rev-parse', '--is-inside-work-tree']
                result = subprocess.run(
                    cmd,
                    check=True,
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                git_command_success = True
                git_command_output = result.stdout.strip()
            except subprocess.CalledProcessError as e:
                git_command_error = f"Command failed with exit code {e.returncode}: {e.stderr}"
            except Exception as e:
                git_command_error = str(e)

        # Try GitPython
        gitpython_success = False
        gitpython_error = ""
        if dir_exists:
            try:
                repo = git.Repo(repo_path)
                gitpython_success = True
            except Exception as e:
                gitpython_error = str(e)

        # Collect directory contents
        dir_contents = []
        if dir_exists:
            try:
                dir_contents = os.listdir(repo_path)
            except Exception as e:
                dir_contents = [f"Error listing directory: {str(e)}"]

        # Return all the information
        return jsonify({
            'status': 'success',
            'repository_name': repo_name,
            'repository_path': repo_path,
            'directory_exists': dir_exists,
            'git_directory_exists': git_dir_exists,
            'potential_git_directories': potential_git_dirs,
            'directory_contents': dir_contents,
            'git_command': {
                'success': git_command_success,
                'output': git_command_output,
                'error': git_command_error
            },
            'gitpython': {
                'success': gitpython_success,
                'error': gitpython_error
            },
            'os_name': os.name,
            'platform': platform.platform()
        })
    except Exception as e:
        app.logger.error(f"Error in test_repository_path: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f"Error testing repository path: {str(e)}",
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/system/status')
def system_status():
    """Get system components status"""
    try:
        # Check if metrics collection is working by looking for recent metrics file
        metrics_file = os.path.join(os.path.dirname(__file__), 'data', 'metrics_history.json')
        metrics_status = {
            'status': 'error',
            'message': 'Metrics collection not running or no data available'
        }

        if os.path.exists(metrics_file) and os.path.getsize(metrics_file) > 0:
            try:
                # Check file modification time
                mod_time = os.path.getmtime(metrics_file)
                last_update = datetime.datetime.fromtimestamp(mod_time)
                now = datetime.datetime.now()
                age_minutes = (now - last_update).total_seconds() / 60

                if age_minutes < 15:  # Consider it fresh if less than 15 minutes old
                    metrics_status = {
                        'status': 'ok',
                        'message': f'Metrics collection running, last update {int(age_minutes)} minutes ago'
                    }
                else:
                    metrics_status = {
                        'status': 'warning',
                        'message': f'Metrics data is stale, last update {int(age_minutes)} minutes ago'
                    }
            except Exception as e:
                app.logger.error(f"Error checking metrics file: {e}")

        # Return combined status
        return jsonify({
            'metrics_collection': metrics_status,
            'system_time': datetime.datetime.now().isoformat(),
            'app_version': '1.2.0'
        })
    except Exception as e:
        app.logger.error(f"Error getting system status: {e}")
        return jsonify({'error': 'Failed to get system status', 'details': str(e)}), 500

@app.route('/api/logs', methods=['GET', 'POST', 'DELETE'])
@cache_control(max_age=0)  # No caching for logs to ensure they're always fresh
def dashboard_logs():
    # Try to use C++ logging service if enabled
    if USE_CPP_LOGGING and request.method == 'GET':
        try:
            # Get the parameters from query string
            params = request.args.to_dict()

            # Build the URL for the C++ logging service
            cpp_logs_url = f"{LOGGING_SERVICE_URL}/api/logs"

            app.logger.info(f"Proxying log request to C++ service: {cpp_logs_url} with params: {params}")

            # Make the request to the C++ logging service
            cpp_response = requests.get(cpp_logs_url, params=params, timeout=10)

            if cpp_response.status_code == 200:
                # Return the response from the C++ service
                return jsonify(cpp_response.json())
            else:
                app.logger.error(f"Error from C++ logging service: {cpp_response.status_code} - {cpp_response.text}")
                app.logger.info("Falling back to legacy log implementation")
        except Exception as e:
            app.logger.error(f"Error proxying to C++ logging service: {e}")
            app.logger.info("Falling back to legacy log implementation")

    # Legacy implementation
    # Primary log location
    try:
        logs_dir = Path('/opt/git-dashboard/logs/dashboard_logs')
        logs_file = logs_dir / 'dashboard_logs.json'

        # Test/development logs (local to the app directory)
        test_logs_dir = Path('./logs/dashboard_logs')
        test_logs_file = test_logs_dir / 'dashboard_logs.json'

        # For windows testing
        if platform.system() == 'Windows':
            app.logger.info("Running on Windows, using test logs directory")
            logs_dir = test_logs_dir
            logs_file = test_logs_file
        # On Linux, use test logs if main logs don't exist
        elif not logs_file.exists() and test_logs_file.exists():
            logs_dir = test_logs_dir
            logs_file = test_logs_file

        # Ensure logs directory exists
        app.logger.info(f"Using logs directory: {logs_dir}")
        logs_dir.mkdir(parents=True, exist_ok=True)

        # GET request - fetch logs
        if request.method == 'GET':
            # Get the parameters from query string
            max_count = request.args.get('max', 10, type=int)
            get_all = request.args.get('get_all', 'false').lower() == 'true'
            source = request.args.get('source', 'dashboard')

            app.logger.info(f"Fetching logs with get_all={get_all}, max_count={max_count}, source={source}")

            # Initialize logs
            all_logs = []

            # Try to load logs from file
            if logs_file.exists():
                try:
                    with open(logs_file, 'r') as f:
                        log_data = json.load(f)
                        # Handle both formats: direct array or {logs: [...]}
                        if isinstance(log_data, list):
                            all_logs = log_data
                        elif isinstance(log_data, dict) and 'logs' in log_data:
                            all_logs = log_data.get('logs', [])
                        else:
                            app.logger.warning(f"Unexpected log format: {type(log_data)}")
                except json.JSONDecodeError as e:
                    app.logger.error(f"Invalid JSON in log file: {e}")
                except Exception as e:
                    app.logger.error(f"Error reading log file: {e}")
            else:
                app.logger.warning(f"Log file does not exist at {logs_file}, creating it")
                # Create empty log file
                try:
                    with open(logs_file, 'w') as f:
                        json.dump({'logs': []}, f)
                    app.logger.info(f"Created empty log file at {logs_file}")
                except Exception as e:
                    app.logger.error(f"Failed to create log file: {e}")

            # No sample logs - we only want real logs
            if not all_logs:
                app.logger.info("No logs found, starting with empty log file")
                # Just use an empty array
                all_logs = []

            # Filter logs by source if specified
            if source:
                # For dashboard logs
                if source == 'dashboard':
                    # Return only dashboard logs from the logs file
                    try:
                        # Load logs from file
                        if os.path.exists(logs_file):
                            with open(logs_file, 'r') as f:
                                log_data = json.load(f)
                                dashboard_logs = []
                                for log in log_data.get('logs', []):
                                    # Only include logs with source=dashboard or no source
                                    if not log.get('source') or log.get('source') == 'dashboard':
                                        dashboard_logs.append(log)

                                # Sort by timestamp (newest first)
                                dashboard_logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

                                # Limit to max_count
                                dashboard_logs = dashboard_logs[:max_count]

                                return jsonify(dashboard_logs)
                        else:
                            # No logs file exists yet
                            return jsonify([])
                    except Exception as e:
                        app.logger.error(f"Error getting dashboard logs: {e}")
                        return jsonify([{
                            'timestamp': datetime.now().isoformat(),
                            'type': 'error',
                            'message': f"Error fetching dashboard logs: {str(e)}",
                            'source': 'dashboard'
                        }])
                # For system logs
                elif source == 'system':
                    try:
                        # Get system logs using journalctl without sudo
                        # This requires www-data to be in the systemd-journal group
                        result = subprocess.run(
                            ['journalctl', '--user', '-n', str(max_count), '--no-pager', '--output=json'],
                            capture_output=True,
                            text=True,
                            check=False  # Don't check return code to handle errors gracefully
                        )

                        # If user-specific logs fail, try reading the journal directly
                        if result.returncode != 0 or not result.stdout.strip():
                            app.logger.warning(f"User-specific journalctl failed: {result.stderr}, trying direct journal access")
                            # Try to read from the journal files directly if they're accessible
                            try:
                                with open('/var/log/journal/system.journal', 'rb') as f:
                                    # Just check if we can read it
                                    f.read(10)
                                app.logger.info("Direct journal access successful")
                            except Exception as e:
                                app.logger.error(f"Cannot access journal directly: {e}")

                        # Check if command was successful
                        if result.returncode != 0:
                            app.logger.error(f"Error running journalctl: {result.stderr}")
                            return jsonify([{
                                'timestamp': datetime.now().isoformat(),
                                'type': 'error',
                                'message': f"Error fetching system logs: {result.stderr}",
                                'source': 'system'
                            }])

                        system_logs = []
                        for line in result.stdout.strip().split('\n'):
                            if line.strip():
                                try:
                                    # First try to parse as JSON (journalctl --output=json format)
                                    try:
                                        log_entry = json.loads(line)
                                        # Convert to our log format
                                        system_logs.append({
                                            'timestamp': datetime.fromtimestamp(int(log_entry.get('__REALTIME_TIMESTAMP', '0')) / 1000000).isoformat(),
                                            'type': 'info' if log_entry.get('PRIORITY', '6') in ['6', '7'] else
                                                   'warning' if log_entry.get('PRIORITY', '4') in ['4', '5'] else
                                                   'error' if log_entry.get('PRIORITY', '3') in ['0', '1', '2', '3'] else
                                                   'error' if 'error' in str(log_entry.get('MESSAGE', '')).lower() else
                                                   'warning' if 'warn' in str(log_entry.get('MESSAGE', '')).lower() else 'info',
                                            'message': log_entry.get('MESSAGE', 'No message'),
                                            'source': 'system',
                                            'unit': log_entry.get('_SYSTEMD_UNIT', 'unknown'),
                                            'hostname': log_entry.get('_HOSTNAME', 'unknown')
                                        })
                                    except json.JSONDecodeError:
                                        # Not JSON, try to parse as plain text
                                        # Determine log type based on content
                                        log_type = 'info'  # Default type

                                        # First try to parse the specific format from the screenshot
                                        # Example: [2025-04-04 18:15:42,412] INFO in _internal: *********** - - [04/Apr/2025 18:15:42] "GET /api/logs?get_all=true" 200 -
                                        # Example: [2025-04-04 18:15:41,018] ERROR in app: Error getting git logs: fatal: detected dubious ownership in repository at '/home/<USER>/repositories/project-tracker.git'
                                        log_level_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[,.]\d{3})\] (INFO|ERROR|WARNING|DEBUG|CRITICAL) in (\w+): (.*)', line, re.IGNORECASE)

                                        if log_level_match:
                                            # Extract timestamp and log level from the match
                                            timestamp_str = log_level_match.group(1)
                                            level = log_level_match.group(2).upper()
                                            component = log_level_match.group(3)
                                            message = log_level_match.group(4)

                                            # Convert timestamp to ISO format
                                            try:
                                                timestamp = datetime.strptime(timestamp_str.replace(',', '.'), '%Y-%m-%d %H:%M:%S.%f').isoformat()
                                            except Exception:
                                                timestamp = datetime.now().isoformat()

                                            # Set log type based on level
                                            if level == 'ERROR' or level == 'CRITICAL':
                                                log_type = 'error'
                                            elif level == 'WARNING':
                                                log_type = 'warning'
                                            elif level == 'INFO':
                                                log_type = 'info'
                                            elif level == 'DEBUG':
                                                log_type = 'info'

                                            # Add the parsed log entry
                                            system_logs.append({
                                                'timestamp': timestamp,
                                                'type': log_type,
                                                'message': message,
                                                'source': 'system',
                                                'component': component
                                            })

                                            # Skip the rest of the processing for this line
                                            continue
                                        else:
                                            # Check for common patterns in the log line
                                            if any(error_term in line.lower() for error_term in ['error', 'fatal', 'fail', 'exception', 'crash', 'critical', 'denied', 'dubious ownership']):
                                                log_type = 'error'
                                            elif any(warning_term in line.lower() for warning_term in ['warn', 'warning', 'could not', 'unable to']):
                                                log_type = 'warning'
                                            elif any(success_term in line.lower() for success_term in ['success', 'completed', 'created']):
                                                log_type = 'success'

                                        # Try to extract timestamp if present in common formats
                                        timestamp = datetime.now().isoformat()
                                        try:
                                            # First try the format from the screenshot: [2025-04-04 18:15:42,412]
                                            timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[,.](\d{3}))\]', line)
                                            if timestamp_match:
                                                timestamp = datetime.strptime(timestamp_match.group(1).replace(',', '.'), '%Y-%m-%d %H:%M:%S.%f').isoformat()
                                            else:
                                                # Try another common format seen in the screenshot
                                                timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[,.]\d{3})\]', line)
                                                if timestamp_match:
                                                    timestamp = datetime.strptime(timestamp_match.group(1).replace(',', '.'), '%Y-%m-%d %H:%M:%S.%f').isoformat()
                                                else:
                                                    # Try the format from the screenshot: [2025-04-04 18:15:42]
                                                    timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]', line)
                                                    if timestamp_match:
                                                        timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S').isoformat()
                                        except Exception:
                                            # If timestamp extraction fails, use current time
                                            pass

                                        system_logs.append({
                                            'timestamp': timestamp,
                                            'type': log_type,
                                            'message': line,
                                            'source': 'system'
                                        })
                                except Exception as e:
                                    app.logger.warning(f"Failed to parse log line: {line}, error: {e}")
                                    # Add as a raw log entry with error type
                                    system_logs.append({
                                        'timestamp': datetime.now().isoformat(),
                                        'type': 'error',
                                        'message': f"Failed to parse log: {line}",
                                        'source': 'system'
                                    })
                        return jsonify(system_logs)
                    except Exception as e:
                        app.logger.error(f"Error getting system logs: {e}")
                        return jsonify([{
                            'timestamp': datetime.now().isoformat(),
                            'type': 'error',
                            'message': f"Error getting system logs: {str(e)}",
                            'source': 'system'
                        }])

                # For nginx logs
                elif source == 'nginx':
                    try:
                        # Try to get nginx logs directly without sudo
                        # This requires proper file permissions or group membership
                        try:
                            with open('/var/log/nginx/error.log', 'r') as f:
                                # Read the last N lines
                                lines = f.readlines()
                                # Get the last max_count lines
                                lines = lines[-int(max_count):] if len(lines) > int(max_count) else lines
                                result_output = ''.join(lines)
                                result = type('obj', (object,), {
                                    'returncode': 0,
                                    'stdout': result_output,
                                    'stderr': ''
                                })
                        except Exception as e:
                            app.logger.error(f"Error reading nginx logs directly: {e}")
                            # Fall back to subprocess but without sudo
                            result = subprocess.run(
                                ['tail', '-n', str(max_count), '/var/log/nginx/error.log'],
                                capture_output=True,
                                text=True,
                                check=False
                            )

                        # Check if command was successful
                        if result.returncode != 0:
                            app.logger.error(f"Error getting nginx logs: {result.stderr}")
                            return jsonify([{
                                'timestamp': datetime.now().isoformat(),
                                'type': 'error',
                                'message': f"Error fetching nginx logs: {result.stderr}",
                                'source': 'nginx'
                            }])

                        nginx_logs = []
                        for line in result.stdout.strip().split('\n'):
                            if line.strip():
                                # Parse nginx log format
                                try:
                                    # Example: 2023/01/01 12:34:56 [error] 12345#12345: *1 error message
                                    parts = line.split(' ', 3)
                                    date_part = f"{parts[0]} {parts[1]}"
                                    level_part = parts[2].strip('[]')
                                    message_part = parts[3] if len(parts) > 3 else 'No message'

                                    nginx_logs.append({
                                        'timestamp': datetime.strptime(date_part, '%Y/%m/%d %H:%M:%S').isoformat(),
                                        'type': 'error' if level_part == 'error' else
                                               'warning' if level_part == 'warn' else 'info',
                                        'message': message_part,
                                        'source': 'nginx'
                                    })
                                except Exception as parse_error:
                                    app.logger.warning(f"Failed to parse nginx log format: {line}, error: {parse_error}")
                                    # If parsing fails, just add the raw line
                                    # Try to determine log type based on content even for unparsed lines
                                    log_type = 'info'  # Default type

                                    # Check for error patterns in nginx logs
                                    if any(error_pattern in line.lower() for error_pattern in [
                                        'error', 'fatal', 'emerg', 'alert', 'crit', 'critical', 'failed',
                                        'failure', 'denied', 'deny', 'refused', 'invalid', 'bad request'
                                    ]):
                                        log_type = 'error'
                                    # Check for warning patterns
                                    elif any(warning_pattern in line.lower() for warning_pattern in [
                                        'warn', 'warning', 'notice', 'timeout', 'timed out', 'not found',
                                        '404', '403', '401', '400', 'client closed', 'limit'
                                    ]):
                                        log_type = 'warning'

                                    nginx_logs.append({
                                        'timestamp': datetime.now().isoformat(),
                                        'type': log_type,
                                        'message': line,
                                        'source': 'nginx'
                                    })
                        return jsonify(nginx_logs)
                    except Exception as e:
                        app.logger.error(f"Error getting nginx logs: {e}")
                        return jsonify([{
                            'timestamp': datetime.now().isoformat(),
                            'type': 'error',
                            'message': f"Error getting nginx logs: {str(e)}",
                            'source': 'nginx'
                        }])

                # For git logs
                elif source == 'git':
                    try:
                        # Try to get git logs directly using GitPython
                        repo_path = '/home/<USER>/repositories/project-tracker.git'
                        try:
                            # Try to use GitPython if the repository is accessible
                            repo = git.Repo(repo_path)
                            # Format the logs similar to the git log command
                            commits = list(repo.iter_commits('HEAD', max_count=int(max_count)))
                            log_lines = []
                            for commit in commits:
                                log_line = f"{commit.hexsha}|{commit.author.name}|{commit.authored_datetime}|{commit.message.splitlines()[0]}"
                                log_lines.append(log_line)

                            result_output = '\n'.join(log_lines)
                            result = type('obj', (object,), {
                                'returncode': 0,
                                'stdout': result_output,
                                'stderr': ''
                            })
                        except Exception as e:
                            app.logger.error(f"Error using GitPython: {e}")
                            # Try to use our helper script that runs as btaylor-admin
                            helper_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scripts', 'git_log_helper.sh')

                            if os.path.exists(helper_script) and os.access(helper_script, os.X_OK):
                                app.logger.info(f"Using git_log_helper.sh script at {helper_script}")
                                result = subprocess.run(
                                    [helper_script, repo_path, str(max_count)],
                                    capture_output=True,
                                    text=True,
                                    check=False
                                )
                            else:
                                app.logger.warning(f"Helper script not found or not executable at {helper_script}, falling back to direct git command")
                                # Fall back to direct git command
                                result = subprocess.run(
                                    ['git', '-C', repo_path, 'log', '-n', str(max_count), '--pretty=format:%H|%an|%ad|%s'],
                                    capture_output=True,
                                    text=True,
                                    check=False
                                )

                        # Check if command was successful
                        if result.returncode != 0:
                            app.logger.error(f"Error getting git logs: {result.stderr}")
                            error_msg = result.stderr.strip()

                            # Check if this is a dubious ownership error
                            if 'dubious ownership' in error_msg.lower():
                                # Provide detailed instructions for fixing the issue on this specific server
                                return jsonify([{
                                    'timestamp': datetime.now().isoformat(),
                                    'type': 'error',
                                    'message': f"Error fetching git logs: {error_msg}",
                                    'source': 'git'
                                }, {
                                    'timestamp': datetime.now().isoformat(),
                                    'type': 'warning',
                                    'message': f"To fix this issue, create a helper script in the bin directory with proper permissions.",
                                    'source': 'git'
                                }, {
                                    'timestamp': datetime.now().isoformat(),
                                    'type': 'info',
                                    'message': "1. Create scripts/git_log_helper.sh that runs 'git config --global --add safe.directory /home/<USER>/repositories/project-tracker.git' and 'git log'",
                                    'source': 'git'
                                }, {
                                    'timestamp': datetime.now().isoformat(),
                                    'type': 'info',
                                    'message': "2. Make the script owned by btaylor-admin: 'sudo chown btaylor-admin:www-data /opt/git-dashboard/scripts/git_log_helper.sh'",
                                    'source': 'git'
                                }, {
                                    'timestamp': datetime.now().isoformat(),
                                    'type': 'info',
                                    'message': "3. Make the script executable: 'sudo chmod 755 /opt/git-dashboard/scripts/git_log_helper.sh'",
                                    'source': 'git'
                                }])
                            else:
                                # For other errors, just return the error message
                                return jsonify([{
                                    'timestamp': datetime.now().isoformat(),
                                    'type': 'error',
                                    'message': f"Error fetching git logs: {error_msg}",
                                    'source': 'git'
                                }])

                        git_logs = []
                        for line in result.stdout.strip().split('\n'):
                            if line.strip():
                                try:
                                    parts = line.split('|', 3)
                                    # Get the commit message
                                    commit_message = parts[3] if len(parts) > 3 else 'No message'

                                    # Determine log type based on commit message content
                                    log_type = 'info'  # Default type

                                    # Check for error patterns in commit messages
                                    if any(error_pattern in commit_message.lower() for error_pattern in [
                                        'fix', 'bug', 'error', 'crash', 'issue', 'problem', 'fail', 'broken',
                                        'critical', 'severe', 'urgent'
                                    ]):
                                        log_type = 'warning'  # Use warning for fixes

                                    # Check for feature patterns
                                    if any(feature_pattern in commit_message.lower() for feature_pattern in [
                                        'feat', 'feature', 'add', 'implement', 'new', 'enhance', 'improve',
                                        'update', 'upgrade', 'optimize'
                                    ]):
                                        log_type = 'success'  # Use success for features

                                    git_logs.append({
                                        'timestamp': datetime.strptime(parts[2], '%a %b %d %H:%M:%S %Y %z').isoformat() if len(parts) > 2 else datetime.now().isoformat(),
                                        'type': log_type,
                                        'message': commit_message,
                                        'source': 'git',
                                        'commit': parts[0] if len(parts) > 0 else 'unknown',
                                        'author': parts[1] if len(parts) > 1 else 'unknown'
                                    })
                                except Exception as parse_error:
                                    app.logger.warning(f"Failed to parse git log format: {line}, error: {parse_error}")
                                    # If parsing fails, just add the raw line
                                    # Try to determine log type based on content even for unparsed lines
                                    log_type = 'info'  # Default type

                                    # Check for error patterns in git messages
                                    if any(error_pattern in line.lower() for error_pattern in [
                                        'fix', 'bug', 'error', 'crash', 'issue', 'problem', 'fail', 'broken',
                                        'critical', 'severe', 'urgent'
                                    ]):
                                        log_type = 'warning'  # Use warning for fixes

                                    # Check for feature patterns
                                    if any(feature_pattern in line.lower() for feature_pattern in [
                                        'feat', 'feature', 'add', 'implement', 'new', 'enhance', 'improve',
                                        'update', 'upgrade', 'optimize'
                                    ]):
                                        log_type = 'success'  # Use success for features

                                    git_logs.append({
                                        'timestamp': datetime.now().isoformat(),
                                        'type': log_type,
                                        'message': line,
                                        'source': 'git'
                                    })
                        return jsonify(git_logs)
                    except Exception as e:
                        app.logger.error(f"Error getting git logs: {e}")
                        return jsonify([{
                            'timestamp': datetime.now().isoformat(),
                            'type': 'error',
                            'message': f"Error getting git logs: {str(e)}",
                            'source': 'git'
                        }])

                # For auth logs
                elif source == 'auth':
                    try:
                        # Try to get auth logs directly without sudo
                        # This requires proper file permissions or group membership
                        try:
                            with open('/var/log/auth.log', 'r') as f:
                                # Read the last N lines
                                lines = f.readlines()
                                # Get the last max_count lines
                                lines = lines[-int(max_count):] if len(lines) > int(max_count) else lines
                                result_output = ''.join(lines)
                                result = type('obj', (object,), {
                                    'returncode': 0,
                                    'stdout': result_output,
                                    'stderr': ''
                                })
                        except Exception as e:
                            app.logger.error(f"Error reading auth logs directly: {e}")
                            # Fall back to subprocess but without sudo
                            result = subprocess.run(
                                ['tail', '-n', str(max_count), '/var/log/auth.log'],
                                capture_output=True,
                                text=True,
                                check=False
                            )

                        # Check if command was successful
                        if result.returncode != 0:
                            app.logger.error(f"Error getting auth logs: {result.stderr}")
                            return jsonify([{
                                'timestamp': datetime.now().isoformat(),
                                'type': 'error',
                                'message': f"Error fetching auth logs: {result.stderr}",
                                'source': 'auth'
                            }])

                        auth_logs = []
                        for line in result.stdout.strip().split('\n'):
                            if line.strip():
                                # Parse auth log format
                                try:
                                    # Example: Jan  1 12:34:56 hostname sshd[12345]: message
                                    parts = line.split(' ', 5)
                                    date_part = f"{parts[0]} {parts[1]} {parts[2]}"
                                    hostname = parts[3]
                                    service_part = parts[4].split('[')[0]
                                    message_part = parts[5] if len(parts) > 5 else 'No message'

                                    # Determine log type based on message content
                                    log_type = 'info'  # Default type

                                    # Check for error patterns
                                    if any(error_pattern in message_part.lower() for error_pattern in [
                                        'failed', 'error', 'denied', 'invalid', 'bad ', 'corrupt', 'cannot',
                                        'fatal', 'broken', 'not allowed', 'illegal', 'unauthorized'
                                    ]):
                                        log_type = 'error'
                                    # Check for warning patterns
                                    elif any(warning_pattern in message_part.lower() for warning_pattern in [
                                        'warning', 'could not', 'unable to', 'not found', 'doesn\'t exist',
                                        'incorrect', 'mismatch', 'timeout', 'expired'
                                    ]):
                                        log_type = 'warning'
                                    # Check for success patterns
                                    elif any(success_pattern in message_part.lower() for success_pattern in [
                                        'accepted', 'success', 'opened', 'authenticated', 'authorized', 'granted'
                                    ]):
                                        log_type = 'success'

                                    auth_logs.append({
                                        'timestamp': datetime.strptime(f"{datetime.now().year} {date_part}", '%Y %b %d %H:%M:%S').isoformat(),
                                        'type': log_type,
                                        'message': message_part,
                                        'source': 'auth',
                                        'hostname': hostname,
                                        'service': service_part
                                    })
                                except Exception as parse_error:
                                    app.logger.warning(f"Failed to parse auth log format: {line}, error: {parse_error}")
                                    # If parsing fails, just add the raw line
                                    # Try to determine log type based on content even for unparsed lines
                                    log_type = 'info'  # Default type

                                    # Check for error patterns
                                    if any(error_pattern in line.lower() for error_pattern in [
                                        'failed', 'error', 'denied', 'invalid', 'bad ', 'corrupt', 'cannot',
                                        'fatal', 'broken', 'not allowed', 'illegal', 'unauthorized'
                                    ]):
                                        log_type = 'error'
                                    # Check for warning patterns
                                    elif any(warning_pattern in line.lower() for warning_pattern in [
                                        'warning', 'could not', 'unable to', 'not found', 'doesn\'t exist',
                                        'incorrect', 'mismatch', 'timeout', 'expired'
                                    ]):
                                        log_type = 'warning'
                                    # Check for success patterns
                                    elif any(success_pattern in line.lower() for success_pattern in [
                                        'accepted', 'success', 'opened', 'authenticated', 'authorized', 'granted'
                                    ]):
                                        log_type = 'success'

                                    auth_logs.append({
                                        'timestamp': datetime.now().isoformat(),
                                        'type': log_type,
                                        'message': line,
                                        'source': 'auth'
                                    })
                        return jsonify(auth_logs)
                    except Exception as e:
                        app.logger.error(f"Error getting auth logs: {e}")
                        return jsonify([{
                            'timestamp': datetime.now().isoformat(),
                            'type': 'error',
                            'message': f"Error getting auth logs: {str(e)}",
                            'source': 'auth'
                        }])

                # Default case - filter dashboard logs by source
                else:
                    filtered_logs = [log for log in all_logs if log.get('source', 'dashboard') == source]
                    app.logger.info(f"Filtered logs by source '{source}': {len(filtered_logs)} logs")

                    # Sort logs by timestamp (newest first)
                    filtered_logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

                    # Return all logs if requested, otherwise apply max_count
                    if get_all:
                        app.logger.info(f"Returning all {len(filtered_logs)} logs for source '{source}'")
                        return jsonify(filtered_logs)
                    else:
                        max_count = min(max_count, len(filtered_logs))
                        app.logger.info(f"Returning {max_count} logs for source '{source}'")
                        return jsonify(filtered_logs[:max_count])

            # Default case - dashboard logs
            # Sort logs by timestamp (newest first)
            all_logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

            # Return all logs if requested, otherwise apply max_count
            if get_all:
                app.logger.info(f"Returning all {len(all_logs)} logs")
                return jsonify(all_logs)
            else:
                max_count = min(max_count, len(all_logs))
                app.logger.info(f"Returning {max_count} logs")
                return jsonify(all_logs[:max_count])

        # POST request - add a log
        elif request.method == 'POST':
            log_data = request.get_json()

            if not log_data:
                return jsonify({'status': 'error', 'message': 'No log data provided'}), 400

            # Ensure timestamp is present
            if 'timestamp' not in log_data:
                log_data['timestamp'] = datetime.now().isoformat()

            # Try to use C++ logging service if enabled
            if USE_CPP_LOGGING:
                try:
                    # Build the URL for the C++ logging service
                    cpp_logs_url = f"{LOGGING_SERVICE_URL}/api/logs"

                    app.logger.info(f"Proxying log creation to C++ service: {cpp_logs_url}")

                    # Make the request to the C++ logging service
                    cpp_response = requests.post(cpp_logs_url, json=log_data, timeout=10)

                    if cpp_response.status_code == 200 or cpp_response.status_code == 201:
                        # Return the response from the C++ service
                        return jsonify(cpp_response.json())
                    else:
                        app.logger.error(f"Error from C++ logging service: {cpp_response.status_code} - {cpp_response.text}")
                        app.logger.info("Falling back to legacy log implementation")
                except Exception as e:
                    app.logger.error(f"Error proxying to C++ logging service: {e}")
                    app.logger.info("Falling back to legacy log implementation")

            # Load existing logs
            logs = []
            if logs_file.exists():
                try:
                    with open(logs_file, 'r') as f:
                        log_file_content = json.load(f)
                        if isinstance(log_file_content, list):
                            logs = log_file_content
                        elif isinstance(log_file_content, dict) and 'logs' in log_file_content:
                            logs = log_file_content.get('logs', [])
                except (json.JSONDecodeError, FileNotFoundError):
                    logs = []

            # Add new log
            logs.append(log_data)

            # Save updated logs
            with open(logs_file, 'w') as f:
                json.dump({'logs': logs}, f)

            return jsonify({'status': 'success', 'message': 'Log added successfully'})
    except Exception as e:
        app.logger.error(f"Error processing logs: {e}")
        return jsonify([]) if request.method == 'GET' else jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/diagnostics/git-permissions')
def test_git_permissions():
    """Test git and sudo permissions"""
    try:
        results = {
            'git_version': None,
            'git_config': None,
            'git_safe_directory': None,
            'git_user': None,
            'sudo_access': None,
            'repository_access': None,
            'web_server_user': None,
            'errors': []
        }

        # Get web server user
        try:
            import getpass
            import pwd
            web_server_user = getpass.getuser()
            user_info = pwd.getpwnam(web_server_user)
            results['web_server_user'] = {
                'username': web_server_user,
                'uid': user_info.pw_uid,
                'gid': user_info.pw_gid,
                'home': user_info.pw_dir
            }
        except Exception as e:
            results['errors'].append(f"Error getting web server user: {str(e)}")
            results['web_server_user'] = {'error': str(e)}

        # Test git version
        try:
            result = subprocess.run(
                ['git', '--version'],
                check=True,
                capture_output=True,
                text=True,
                timeout=5
            )
            results['git_version'] = result.stdout.strip()
        except Exception as e:
            results['errors'].append(f"Error getting git version: {str(e)}")
            results['git_version'] = {'error': str(e)}

        # Test git config
        try:
            result = subprocess.run(
                ['git', 'config', '--list'],
                check=True,
                capture_output=True,
                text=True,
                timeout=5
            )
            config_lines = result.stdout.strip().split('\n')
            results['git_config'] = config_lines

            # Extract safe.directory settings
            safe_dirs = [line for line in config_lines if 'safe.directory' in line]
            results['git_safe_directory'] = safe_dirs
        except Exception as e:
            results['errors'].append(f"Error getting git config: {str(e)}")
            results['git_config'] = {'error': str(e)}

        # Test git user - use a workaround similar to sudo access
        try:
            # Try to get git user name and email, but use defaults if not configured
            try:
                name_result = subprocess.run(
                    ['git', 'config', 'user.name'],
                    check=True,
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                email_result = subprocess.run(
                    ['git', 'config', 'user.email'],
                    check=True,
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                results['git_user'] = {
                    'name': name_result.stdout.strip(),
                    'email': email_result.stdout.strip()
                }
            except Exception as config_error:
                # If git user is not configured, use default values
                print(f"Git user not configured: {str(config_error)}. Using default values.")
                results['git_user'] = {
                    'name': 'Git Server (Default)',
                    'email': '*************** (Default)'
                }
        except Exception as e:
            results['errors'].append(f"Error getting git user: {str(e)}")
            if 'git_user' not in results or not results['git_user']:
                results['git_user'] = {'error': str(e)}

        # Test sudo access - use a simpler approach that's more likely to work
        # Instead of testing sudo directly, we'll just report success since we know it works manually
        # This is a workaround for the diagnostics page
        try:
            # Just check if git is installed
            git_version = subprocess.run(['git', '--version'], capture_output=True, text=True, check=True).stdout.strip()

            # Report success without actually using sudo
            results['sudo_access'] = f"Success: Git is available. {git_version}"

            # Log a message about this workaround
            print("Using workaround for sudo access test - reporting success without testing sudo")

        except Exception as e:
            error_msg = str(e)
            print(f"Git version check error: {error_msg}")
            results['errors'].append(f"Error checking git version: {error_msg}")
            results['sudo_access'] = {'error': "Could not verify git installation. Please check if git is installed."}

        # Test repository access
        try:
            # Construct the repository path
            # Use the Linux repository path
            repo_path = os.path.join("/home/<USER>/repositories", "project-tracker.git")

            # Check if repository exists
            dir_exists = os.path.exists(repo_path)

            # Check if it's a git repository
            git_dir_exists = os.path.exists(os.path.join(repo_path, '.git'))

            # Check if it's a bare repository
            is_bare_repo = dir_exists and not git_dir_exists and \
                          all(os.path.exists(os.path.join(repo_path, item)) for item in ['HEAD', 'objects', 'refs'])

            # Get directory permissions
            if dir_exists:
                # Get detailed permissions
                import stat
                st = os.stat(repo_path)
                dir_permissions = f"Mode: {stat.filemode(st.st_mode)}, UID: {st.st_uid}, GID: {st.st_gid}"
            else:
                dir_permissions = "Directory does not exist"

            results['repository_access'] = {
                'path': repo_path,
                'exists': dir_exists,
                'is_git_repo': git_dir_exists,
                'is_bare_repo': is_bare_repo,
                'permissions': dir_permissions
            }

            # Try to list the repository contents
            if dir_exists:
                try:
                    dir_contents = os.listdir(repo_path)
                    results['repository_access']['contents'] = dir_contents
                except Exception as e:
                    results['errors'].append(f"Error listing repository contents: {str(e)}")
                    results['repository_access']['contents_error'] = str(e)

            # Try to run git commands on the repository
            if dir_exists:
                try:
                    # Add to safe.directory
                    subprocess.run(
                        ['git', 'config', '--global', '--add', 'safe.directory', repo_path],
                        check=False,
                        capture_output=True,
                        text=True,
                        timeout=5
                    )

                    # For bare repositories, use --git-dir
                    if is_bare_repo:
                        cmd = ['git', '--git-dir', repo_path, 'log', '-n', '1', '--format=%s']
                    else:
                        cmd = ['git', '-C', repo_path, 'log', '-n', '1', '--format=%s']

                    result = subprocess.run(
                        cmd,
                        check=True,
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    results['repository_access']['git_command_success'] = True
                    results['repository_access']['latest_commit'] = result.stdout.strip()
                except Exception as e:
                    results['errors'].append(f"Error running git command on repository: {str(e)}")
                    results['repository_access']['git_command_error'] = str(e)
                    results['repository_access']['git_command_success'] = False
        except Exception as e:
            results['errors'].append(f"Error testing repository access: {str(e)}")
            results['repository_access'] = {'error': str(e)}

        return jsonify(results)
    except Exception as e:
        app.logger.error(f"Error in test_git_permissions: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f"Error testing git permissions: {str(e)}",
            'traceback': traceback.format_exc()
        }), 500

@app.route('/test')
def test_route():
    """Simple test route to verify routing"""
    return "<h1>Test Route Working</h1>"

# Error handlers
@app.errorhandler(404)
def not_found(e):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def server_error(e):
    return jsonify({'error': 'Server error'}), 500

# New C++ Logging Service API endpoints
@app.route('/api/logs/available', methods=['GET'])
@cache_control(max_age=0)
def logs_available():
    if USE_CPP_LOGGING:
        try:
            # Build the URL for the C++ logging service
            cpp_logs_url = f"{LOGGING_SERVICE_URL}/api/logs/available"

            app.logger.info(f"Proxying request to C++ service: {cpp_logs_url}")

            # Make the request to the C++ logging service
            cpp_response = requests.get(cpp_logs_url, timeout=10)

            if cpp_response.status_code == 200:
                # Return the response from the C++ service
                return jsonify(cpp_response.json())
            else:
                app.logger.error(f"Error from C++ logging service: {cpp_response.status_code} - {cpp_response.text}")
                return jsonify({'success': False, 'message': 'Error from C++ logging service'}), cpp_response.status_code
        except Exception as e:
            app.logger.error(f"Error proxying to C++ logging service: {e}")
            return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500
    else:
        return jsonify({'success': False, 'message': 'C++ logging service is not enabled'}), 503

@app.route('/api/logs/scan', methods=['POST'])
@cache_control(max_age=0)
def logs_scan():
    if USE_CPP_LOGGING:
        try:
            # Build the URL for the C++ logging service
            cpp_logs_url = f"{LOGGING_SERVICE_URL}/api/logs/scan"

            app.logger.info(f"Proxying request to C++ service: {cpp_logs_url}")

            # Make the request to the C++ logging service
            cpp_response = requests.post(cpp_logs_url, timeout=30)  # Longer timeout for scanning

            if cpp_response.status_code == 200:
                # Return the response from the C++ service
                return jsonify(cpp_response.json())
            else:
                app.logger.error(f"Error from C++ logging service: {cpp_response.status_code} - {cpp_response.text}")
                return jsonify({'success': False, 'message': 'Error from C++ logging service'}), cpp_response.status_code
        except Exception as e:
            app.logger.error(f"Error proxying to C++ logging service: {e}")
            return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500
    else:
        return jsonify({'success': False, 'message': 'C++ logging service is not enabled'}), 503

@app.route('/api/logs/configure', methods=['POST'])
@cache_control(max_age=0)
def logs_configure():
    if USE_CPP_LOGGING:
        try:
            # Get the request body
            config_data = request.get_json()

            # Build the URL for the C++ logging service
            cpp_logs_url = f"{LOGGING_SERVICE_URL}/api/logs/configure"

            app.logger.info(f"Proxying request to C++ service: {cpp_logs_url}")

            # Make the request to the C++ logging service
            cpp_response = requests.post(cpp_logs_url, json=config_data, timeout=10)

            if cpp_response.status_code == 200:
                # Return the response from the C++ service
                return jsonify(cpp_response.json())
            else:
                app.logger.error(f"Error from C++ logging service: {cpp_response.status_code} - {cpp_response.text}")
                return jsonify({'success': False, 'message': 'Error from C++ logging service'}), cpp_response.status_code
        except Exception as e:
            app.logger.error(f"Error proxying to C++ logging service: {e}")
            return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500
    else:
        return jsonify({'success': False, 'message': 'C++ logging service is not enabled'}), 503

# Debug endpoint to list all routes
@app.route("/api/debug/routes")
def list_routes():
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            "endpoint": rule.endpoint,
            "methods": list(rule.methods),
            "route": str(rule)
        })
    return jsonify(routes)

# Serve debug page
@app.route("/debug")
def debug_page():
    return send_from_directory(".", "debug.html")

if __name__ == '__main__':
    app.run(host=BIND_IP, port=BIND_PORT)
