#include <iostream>
#include <string>
#include <boost/asio.hpp>
#include <boost/beast.hpp>

namespace beast = boost::beast;
namespace http = beast::http;
namespace asio = boost::asio;
using tcp = asio::ip::tcp;

int main() {
    try {
        // Set up the connection
        asio::io_context ioc;
        tcp::resolver resolver(ioc);
        beast::tcp_stream stream(ioc);
        
        // Look up the domain name
        auto const results = resolver.resolve("localhost", "8080");
        
        // Connect to the server
        stream.connect(results);
        
        // Set up an HTTP GET request
        http::request<http::string_body> req{http::verb::get, "/api/repositories", 11};
        req.set(http::field::host, "localhost");
        req.set(http::field::user_agent, "Beast");
        
        // Send the request
        http::write(stream, req);
        
        // Receive the response
        beast::flat_buffer buffer;
        http::response<http::string_body> res;
        http::read(stream, buffer, res);
        
        // Print the response
        std::cout << "Status: " << res.result_int() << std::endl;
        std::cout << "Content-Type: " << res[http::field::content_type] << std::endl;
        std::cout << "Body: " << res.body() << std::endl;
        
        // Gracefully close the socket
        beast::error_code ec;
        stream.socket().shutdown(tcp::socket::shutdown_both, ec);
        
        if(ec && ec != beast::errc::not_connected) {
            throw beast::system_error{ec};
        }
        
    } catch(std::exception const& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }
    
    return EXIT_SUCCESS;
}
