# Project Tracker API Overview

## Overview
This document provides comprehensive documentation for the Project Tracker API endpoints, authentication, and usage.

## Authentication
All API endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Authentication Endpoints

#### POST /api/auth/login
Authenticate user and receive JW<PERSON> token.

**Request:**
```json
{
    "username": "string",
    "password": "string"
}
```

**Response:**
```json
{
    "token": "string",
    "user": {
        "id": "integer",
        "username": "string",
        "roles": ["string"]
    }
}
```

## Projects

### GET /api/projects
List all projects.

**Response:**
```json
[
    {
        "id": "integer",
        "name": "string",
        "description": "string",
        "repository_url": "string",
        "improvements_count": "integer",
        "categories_count": "integer"
    }
]
```

### POST /api/projects
Create a new project.

**Request:**
```json
{
    "name": "string",
    "description": "string",
    "repository_url": "string"
}
```

### GET /api/projects/{project_id}/categories
List categories for a project.

**Response:**
```json
[
    {
        "id": "integer",
        "name": "string",
        "description": "string",
        "improvements_count": "integer"
    }
]
```

### GET /api/projects/{project_id}/improvements
List improvements for a project.

**Response:**
```json
[
    {
        "id": "integer",
        "title": "string",
        "description": "string",
        "status": "string",
        "priority": "string",
        "category_name": "string",
        "subtasks": [
            {
                "description": "string",
                "completed": "boolean"
            }
        ]
    }
]
```

## Error Responses
All endpoints may return the following error responses:

- 400 Bad Request: Invalid input
- 401 Unauthorized: Missing or invalid authentication
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 500 Internal Server Error: Server error

Example error response:
```json
{
    "error": "string",
    "details": "string" // Optional
}
```

## Rate Limiting
API requests are limited to:
- 100 requests per minute for authenticated users
- 30 requests per minute for unauthenticated requests

## Caching
Responses from GET endpoints are cached for 5 minutes by default.

## Related Documentation

- [Documentation Index](docs/DOCUMENTATION-INDEX.md)
