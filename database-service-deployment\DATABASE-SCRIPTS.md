# DATABASE-SCRIPTS.md

This document provides an overview of the Database Service supporting files and scripts.

## Overview

This directory contains supporting files and scripts for the Database Service, including deployment scripts, configuration templates, database schema files, and migration scripts.

## Directory Structure

- scripts/: Deployment and utility scripts
  - deploy-database-service.bat: Main deployment script
  - setup-certificate-access.sh: <PERSON>ript to set up certificate access on the server
  - Other utility scripts

- config/: Configuration templates
  - config.ini: Main configuration template

- schemas/: Database schema files
  - initial_schema.sql: Initial database schema

- migrations/: Schema migration files
  - git_dashboard_1.0.0_to_1.1.0.sql: Example migration

## Deployment Process

The deployment process consists of the following steps:

1. **Server Readiness Check**: Verify that the server has all required dependencies
2. **Build**: Build the Database Service from source
3. **Deploy**: Copy the executable and supporting files to the server
4. **Certificate Access Setup**: Configure access to SSL certificates
5. **Service Setup**: Create and start the systemd service

## Configuration

The configuration templates in the config/ directory provide a starting point for configuring the Database Service. The configuration options include:

- Database connection settings
- API server settings
- Schema management settings
- Security settings
- Logging settings
- Certificate access settings

## Schema Management

The schema files in the schemas/ directory define the initial database schema for the Database Service. The migration files in the migrations/ directory define schema changes for upgrading from one version to another.

## Related Directories

- **Source Code**: D:\Augment\project-tracker\database-service
- **Documentation**: D:\Augment\project-tracker\docs\database-service

## Documentation

For comprehensive documentation, see the [Database Service Documentation Index](../docs/database-service/Database Service Documentation Index.md).
