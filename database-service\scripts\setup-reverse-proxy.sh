#!/bin/bash

# Database Service Reverse Proxy Setup Script
# Configures Nginx for SSL termination and reverse proxy

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NGINX_CONF_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
SERVICE_NAME="database-service"
DOMAIN="database-service.chcit.org"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message"
            ;;
    esac
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log "ERROR" "This script must be run as root"
        exit 1
    fi
}

# Check if Nginx is installed
check_nginx() {
    if ! command -v nginx &> /dev/null; then
        log "ERROR" "Nginx is not installed. Please install Nginx first."
        exit 1
    fi
    
    log "INFO" "Nginx is installed: $(nginx -v 2>&1)"
}

# Check if SSL certificates exist
check_certificates() {
    local cert_path="/etc/letsencrypt/live/chcit.org/fullchain.pem"
    local key_path="/etc/letsencrypt/live/chcit.org/privkey.pem"
    local chain_path="/etc/letsencrypt/live/chcit.org/chain.pem"
    
    if [[ ! -f "$cert_path" ]]; then
        log "WARN" "SSL certificate not found at $cert_path"
        log "WARN" "You may need to obtain SSL certificates first"
        return 1
    fi
    
    if [[ ! -f "$key_path" ]]; then
        log "WARN" "SSL private key not found at $key_path"
        return 1
    fi
    
    if [[ ! -f "$chain_path" ]]; then
        log "WARN" "SSL chain certificate not found at $chain_path"
        return 1
    fi
    
    log "INFO" "SSL certificates found and accessible"
    return 0
}

# Install Nginx configuration
install_nginx_config() {
    local source_config="$PROJECT_ROOT/nginx/database-service.conf"
    local target_config="$NGINX_CONF_DIR/$SERVICE_NAME"
    
    if [[ ! -f "$source_config" ]]; then
        log "ERROR" "Nginx configuration file not found at $source_config"
        exit 1
    fi
    
    log "INFO" "Installing Nginx configuration"
    cp "$source_config" "$target_config"
    
    # Set proper permissions
    chmod 644 "$target_config"
    chown root:root "$target_config"
    
    log "INFO" "Nginx configuration installed to $target_config"
}

# Enable Nginx site
enable_nginx_site() {
    local config_file="$NGINX_CONF_DIR/$SERVICE_NAME"
    local enabled_link="$NGINX_ENABLED_DIR/$SERVICE_NAME"
    
    if [[ -L "$enabled_link" ]]; then
        log "INFO" "Nginx site already enabled"
        return 0
    fi
    
    log "INFO" "Enabling Nginx site"
    ln -s "$config_file" "$enabled_link"
    
    log "INFO" "Nginx site enabled"
}

# Test Nginx configuration
test_nginx_config() {
    log "INFO" "Testing Nginx configuration"
    
    if nginx -t; then
        log "INFO" "Nginx configuration test passed"
        return 0
    else
        log "ERROR" "Nginx configuration test failed"
        return 1
    fi
}

# Reload Nginx
reload_nginx() {
    log "INFO" "Reloading Nginx"
    
    if systemctl reload nginx; then
        log "INFO" "Nginx reloaded successfully"
    else
        log "ERROR" "Failed to reload Nginx"
        return 1
    fi
}

# Create log directories
create_log_directories() {
    local log_dir="/var/log/nginx"
    
    if [[ ! -d "$log_dir" ]]; then
        log "INFO" "Creating Nginx log directory"
        mkdir -p "$log_dir"
        chown www-data:adm "$log_dir"
        chmod 755 "$log_dir"
    fi
    
    log "INFO" "Log directories ready"
}

# Setup logrotate for database service logs
setup_logrotate() {
    local logrotate_config="/etc/logrotate.d/database-service-nginx"
    
    log "INFO" "Setting up log rotation"
    
    cat > "$logrotate_config" << 'EOF'
/var/log/nginx/database-service.*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    sharedscripts
    prerotate
        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \
            run-parts /etc/logrotate.d/httpd-prerotate; \
        fi
    endscript
    postrotate
        invoke-rc.d nginx rotate >/dev/null 2>&1
    endscript
}
EOF
    
    chmod 644 "$logrotate_config"
    log "INFO" "Log rotation configured"
}

# Check if database service is running
check_database_service() {
    local service_port=8080
    
    if netstat -tuln | grep -q ":$service_port "; then
        log "INFO" "Database service appears to be running on port $service_port"
        return 0
    else
        log "WARN" "Database service does not appear to be running on port $service_port"
        log "WARN" "Make sure to start the database service before testing"
        return 1
    fi
}

# Display configuration summary
show_summary() {
    log "INFO" "=== Configuration Summary ==="
    log "INFO" "Service: Database Service"
    log "INFO" "Domain: $DOMAIN"
    log "INFO" "Backend: http://127.0.0.1:8080"
    log "INFO" "SSL: Enabled (certificates from Let's Encrypt)"
    log "INFO" "Rate Limiting: Enabled"
    log "INFO" "Security Headers: Enabled"
    log "INFO" "Compression: Enabled"
    log "INFO" "=== Next Steps ==="
    log "INFO" "1. Start the database service: systemctl start database-service"
    log "INFO" "2. Test the configuration: curl -k https://$DOMAIN/api/health"
    log "INFO" "3. Check logs: tail -f /var/log/nginx/database-service.access.log"
}

# Main execution
main() {
    log "INFO" "Starting Database Service Reverse Proxy Setup"
    
    # Checks
    check_root
    check_nginx
    
    # Setup
    create_log_directories
    install_nginx_config
    enable_nginx_site
    setup_logrotate
    
    # Test configuration
    if test_nginx_config; then
        reload_nginx
        log "INFO" "Reverse proxy setup completed successfully"
    else
        log "ERROR" "Configuration test failed. Please check the configuration."
        exit 1
    fi
    
    # Final checks and summary
    check_certificates || log "WARN" "SSL certificates not found - HTTPS will not work"
    check_database_service || log "WARN" "Database service not running"
    
    show_summary
    
    log "INFO" "Setup complete!"
}

# Run main function
main "$@"
