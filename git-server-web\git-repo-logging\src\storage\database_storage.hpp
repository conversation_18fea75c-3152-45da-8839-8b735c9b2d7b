#pragma once
#include "storage_types.hpp"
#include <memory>
#include <string>
#include <mutex>
#include <chrono>
#include <atomic>
#include <thread>

// Forward declaration for pqxx classes to avoid including the full header
namespace pqxx {
    class connection;
    class result;
    class row;
}

namespace logging {

// Circuit breaker pattern implementation to prevent cascading failures
class CircuitBreaker {
public:
    enum State { CLOSED, OPEN, HALF_OPEN };

    CircuitBreaker(int failureThreshold = 3, int resetTimeoutSeconds = 60)
        : state_(CLOSED), failures_(0), failureThreshold_(failureThreshold),
          resetTimeout_(std::chrono::seconds(resetTimeoutSeconds)),
          lastFailureTime_(std::chrono::steady_clock::now()) {}

    bool allowRequest() {
        auto now = std::chrono::steady_clock::now();

        switch (state_) {
            case CLOSED:
                return true;
            case OPEN:
                if (now - lastFailureTime_ > resetTimeout_) {
                    std::cout << "Circuit half-open, allowing test request" << std::endl;
                    state_ = HALF_OPEN;
                    return true;
                }
                return false;
            case HALF_OPEN:
                return true;
        }

        return false;
    }

    void recordSuccess() {
        if (state_ == HALF_OPEN) {
            std::cout << "Circuit closed after successful request" << std::endl;
            state_ = CLOSED;
            failures_ = 0;
        }
    }

    void recordFailure() {
        lastFailureTime_ = std::chrono::steady_clock::now();

        if (state_ == HALF_OPEN) {
            std::cout << "Circuit opened after failed test request" << std::endl;
            state_ = OPEN;
            return;
        }

        if (state_ == CLOSED) {
            failures_++;
            if (failures_ >= failureThreshold_) {
                std::cout << "Circuit opened after " << failures_ << " consecutive failures" << std::endl;
                state_ = OPEN;
            }
        }
    }

    State getState() const {
        return state_;
    }

    std::string getStateString() const {
        switch (state_) {
            case CLOSED: return "CLOSED";
            case OPEN: return "OPEN";
            case HALF_OPEN: return "HALF_OPEN";
            default: return "UNKNOWN";
        }
    }

private:
    State state_;
    int failures_;
    int failureThreshold_;
    std::chrono::seconds resetTimeout_;
    std::chrono::steady_clock::time_point lastFailureTime_;
};

class DatabaseStorage : public StorageTier {
public:
    DatabaseStorage(const std::string& connectionString, bool structuredOnly);
    virtual ~DatabaseStorage();

    // StorageTier interface implementation
    bool store(const LogEntry& entry) override;
    bool storeBatch(const std::vector<LogEntry>& entries) override;
    std::vector<LogEntry> query(const LogQueryParams& params) override;
    StorageStats getStats() override;
    bool prune(std::chrono::system_clock::time_point olderThan) override;
    bool compact() override;
    bool backup(const std::string& backupPath) override;
    bool canHandle(const LogEntry& entry) const override;
    std::string getName() const override { return "Database"; }
    int getPriority() const override { return 2; }

    // Database-specific methods
    bool initialize();
    bool isConnected() const;
    bool reconnect(int maxAttempts = 3, int delaySeconds = 5);
    CircuitBreaker::State getCircuitState() const { return circuitBreaker_.getState(); }
    std::string getCircuitStateString() const { return circuitBreaker_.getStateString(); }

private:
    // Helper methods
    bool createTables();
    bool createIndexes();
    std::string buildQueryString(const LogQueryParams& params);
    LogEntry rowToLogEntry(const pqxx::row& row);
    std::string logEntryToJson(const LogEntry& entry);
    LogEntry jsonToLogEntry(const std::string& json);

    std::string connectionString_;
    bool structuredOnly_;
    std::unique_ptr<pqxx::connection> connection_;
    std::mutex mutex_;
    CircuitBreaker circuitBreaker_;
    std::atomic<bool> reconnecting_{false};
    std::chrono::steady_clock::time_point lastReconnectAttempt_;
    std::chrono::seconds reconnectCooldown_{30}; // Wait 30 seconds between reconnection attempts
};

} // namespace logging
