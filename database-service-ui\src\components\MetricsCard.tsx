import React from 'react';
import './MetricsCard.css';

interface MetricsCardProps {
  title: string;
  metrics: Record<string, any>;
  loading?: boolean;
  error?: string;
}

const MetricsCard: React.FC<MetricsCardProps> = ({ title, metrics, loading, error }) => {
  return (
    <div className="metrics-card card">
      <h2 className="metrics-card-title">{title}</h2>
      
      {loading && <div className="metrics-loading">Loading metrics...</div>}
      
      {error && <div className="metrics-error alert alert-danger">{error}</div>}
      
      {!loading && !error && (
        <div className="metrics-content">
          {Object.keys(metrics).length === 0 ? (
            <p>No metrics available</p>
          ) : (
            <table className="metrics-table">
              <tbody>
                {Object.entries(metrics).map(([key, value]) => (
                  <tr key={key}>
                    <td className="metric-name">{key.replace(/_/g, ' ')}</td>
                    <td className="metric-value">
                      {typeof value === 'object' ? (
                        <pre>{JSON.stringify(value, null, 2)}</pre>
                      ) : (
                        value
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      )}
    </div>
  );
};

export default MetricsCard;
