# Database Service UI Documentation

The Database Service UI is a web-based interface for managing and monitoring the Database Service. It provides a user-friendly way to view database metrics, manage credentials, and configure service settings.

## Features

- **Authentication**: Secure login with JWT-based authentication
- **Dashboard**: Overview of database service metrics and quick access to features
- **Metrics Visualization**: Detailed view of database-specific metrics
- **Credential Management**: Secure storage and retrieval of credentials
- **Service Configuration**: Configure service settings through a user interface

## Technology Stack

- **React**: Frontend library for building user interfaces
- **TypeScript**: Type-safe JavaScript
- **React Router**: Client-side routing
- **Axios**: HTTP client for API requests
- **Chart.js**: Data visualization library

## Architecture

The UI follows a component-based architecture with the following structure:

- **Components**: Reusable UI components
- **Pages**: Top-level page components
- **Services**: API service layer for communication with the backend
- **Utils**: Utility functions and helpers

## Pages

### Login Page

The login page allows users to authenticate with the Database Service. It uses JWT-based authentication with access and refresh tokens.

### Dashboard

The dashboard provides an overview of the database service metrics and quick access to features. It displays key metrics such as:

- Connection pool utilization
- Query performance statistics
- Transaction metrics
- Authentication success/failure rates

### Metrics Page

The metrics page provides a detailed view of database-specific metrics. It includes:

- Connection pool metrics
- Query performance metrics for different query types
- Transaction metrics
- Authentication metrics

### Credentials Page

The credentials page allows administrators to securely store and retrieve credentials used by the Database Service. All credentials are encrypted using AES-256 encryption.

### Settings Page

The settings page allows administrators to configure the Database Service. It includes settings for:

- API configuration (port, SSL, CORS)
- Security settings (authentication, token expiration)
- Database settings (connection pool)

## API Integration

The UI communicates with the Database Service API using the following endpoints:

### Authentication

- `POST /api/auth/login`: Authenticate user and get tokens
- `POST /api/auth/refresh`: Refresh access token
- `POST /api/auth/logout`: Logout and invalidate tokens
- `GET /api/auth/user`: Get user info

### Metrics

- `GET /api/database/metrics`: Get all database metrics
- `GET /api/database/metrics/connection-pool`: Get connection pool metrics
- `GET /api/database/metrics/query-performance`: Get query performance metrics

### Credentials

- `POST /api/credentials/store`: Store a credential securely
- `GET /api/credentials/get`: Get a stored credential
- `DELETE /api/credentials/remove`: Remove a stored credential

## Security

The UI implements several security features:

- **JWT Authentication**: Secure authentication with access and refresh tokens
- **Protected Routes**: Routes that require authentication or admin privileges
- **Token Refresh**: Automatic refresh of access tokens
- **Secure Credential Storage**: Credentials are encrypted before storage

## Deployment

### Prerequisites

- Node.js 14.x or higher
- npm 6.x or higher

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Configure the API URL in `.env`:
   ```
   REACT_APP_API_URL=http://localhost:8080/api
   ```
4. Build the application:
   ```
   npm run build
   ```
5. Deploy the built files to a web server

### Development

To run the application in development mode:

```
npm start
```

The application will be available at http://localhost:3000.

## User Roles

The UI supports two user roles:

- **Regular Users**: Can view the dashboard and metrics
- **Administrators**: Can manage credentials and configure service settings

## Customization

The UI can be customized by modifying the following files:

- **src/index.css**: Global CSS variables for colors and styling
- **src/components/Header.tsx**: Navigation menu
- **src/components/Footer.tsx**: Footer content
