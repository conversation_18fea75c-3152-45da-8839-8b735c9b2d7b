# Ubuntu Server Configuration Guide for Git Dashboard

## Overview

This document provides Ubuntu-specific configuration details for the Git Dashboard system. It includes system-level settings, security configurations, and optimization tips specifically for Ubuntu Server 24.04 LTS or newer.

## System Requirements

- Ubuntu Server 24.04 LTS or newer
- Minimum 1GB RAM (2GB recommended)
- At least 10GB free disk space
- Network connectivity

## Basic Server Setup

### Update System Packages

```bash
# Update package lists
sudo apt update

# Upgrade installed packages
sudo apt upgrade -y

# Install essential packages
sudo apt install -y build-essential curl wget gnupg lsb-release ca-certificates apt-transport-https software-properties-common
```

### Configure Hostname

```bash
# Set hostname
sudo hostnamectl set-hostname git-dashboard

# Update /etc/hosts
sudo nano /etc/hosts
```

Add the following line to `/etc/hosts`:
```
********* git-dashboard
```

### Set Up Automatic Updates

Install and configure unattended upgrades for security patches:

```bash
sudo apt install -y unattended-upgrades apt-listchanges
sudo dpkg-reconfigure -plow unattended-upgrades
```

Edit the configuration file:

```bash
sudo nano /etc/apt/apt.conf.d/50unattended-upgrades
```

Ensure the following lines are uncommented:

```
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}";
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};
```

## User and Group Management

### Create Service User (Alternative to Root)

If you prefer not to use root for the Git Dashboard:

```bash
# Create git-dashboard user
sudo adduser --system --group git-dashboard

# Add to www-data group for Nginx integration
sudo usermod -aG www-data git-dashboard

# Set ownership of files
sudo chown -R git-dashboard:git-dashboard /opt/git-dashboard

# Allow sudo for specific commands
sudo visudo -f /etc/sudoers.d/git-dashboard
```

Add the following to the sudoers file:

```
git-dashboard ALL=(ALL) NOPASSWD: /bin/systemctl restart nginx, /bin/systemctl reload nginx
```

## Firewall Configuration

### UFW (Uncomplicated Firewall)

```bash
# Install UFW if not already installed
sudo apt install -y ufw

# Set default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (always do this before enabling UFW)
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Enable UFW
sudo ufw --force enable

# Check status
sudo ufw status verbose
```

### IP Tables (Alternative to UFW)

If you prefer using iptables directly:

```bash
# Install iptables-persistent
sudo apt install -y iptables-persistent

# Create rules
sudo nano /etc/iptables/rules.v4
```

Basic iptables rules:

```
*filter
:INPUT DROP [0:0]
:FORWARD DROP [0:0]
:OUTPUT ACCEPT [0:0]

# Allow established connections
-A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# Allow loopback
-A INPUT -i lo -j ACCEPT

# Allow SSH
-A INPUT -p tcp --dport 22 -j ACCEPT

# Allow HTTP/HTTPS
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -p tcp --dport 443 -j ACCEPT

# ICMP (ping)
-A INPUT -p icmp --icmp-type echo-request -j ACCEPT

COMMIT
```

Apply the rules:

```bash
sudo iptables-restore < /etc/iptables/rules.v4
```

## System Optimization

### TCP Optimizations

Edit the sysctl configuration file:

```bash
sudo nano /etc/sysctl.conf
```

Add or modify the following settings:

```
# Increase system file descriptor limit
fs.file-max = 100000

# Increase TCP max connections
net.core.somaxconn = 65535

# Increase TCP performance parameters
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 4096
net.ipv4.tcp_slow_start_after_idle = 0

# Reuse TIME_WAIT sockets
net.ipv4.tcp_tw_reuse = 1

# Set reasonable TCP keepalive parameters
net.ipv4.tcp_keepalive_time = 60
net.ipv4.tcp_keepalive_intvl = 10
net.ipv4.tcp_keepalive_probes = 6
```

Apply the changes:

```bash
sudo sysctl -p
```

### I/O Scheduler Optimization

For SSD drives, change the I/O scheduler to 'none' or 'mq-deadline':

```bash
# Check current scheduler
cat /sys/block/sda/queue/scheduler

# Set scheduler temporarily
echo 'none' | sudo tee /sys/block/sda/queue/scheduler
```

For permanent change, edit the GRUB configuration:

```bash
sudo nano /etc/default/grub
```

Modify the `GRUB_CMDLINE_LINUX` line to add the scheduler parameter:

```
GRUB_CMDLINE_LINUX="elevator=none"
```

Update GRUB:

```bash
sudo update-grub
```

## Time Synchronization

Ensure accurate time for logging and metrics collection:

```bash
# Install chrony
sudo apt install -y chrony

# Configure NTP servers
sudo nano /etc/chrony/chrony.conf
```

Add reliable NTP servers:

```
pool pool.ntp.org iburst
pool time.ubuntu.com iburst
```

Restart the service:

```bash
sudo systemctl restart chrony
```

Verify synchronization:

```bash
chronyc sources
```

## System Monitoring

### Setting Up Node Exporter for Prometheus (Optional)

For advanced monitoring with Prometheus:

```bash
# Create user
sudo useradd --no-create-home --shell /bin/false node_exporter

# Download and install Node Exporter
cd /tmp
curl -LO https://github.com/prometheus/node_exporter/releases/download/v1.5.0/node_exporter-1.5.0.linux-amd64.tar.gz
tar xvf node_exporter-1.5.0.linux-amd64.tar.gz
sudo cp node_exporter-1.5.0.linux-amd64/node_exporter /usr/local/bin/
sudo chown node_exporter:node_exporter /usr/local/bin/node_exporter
```

Create a systemd service file:

```bash
sudo nano /etc/systemd/system/node_exporter.service
```

Add the following content:

```ini
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
```

Start and enable the service:

```bash
sudo systemctl daemon-reload
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

## Daily Maintenance Cron Jobs

Create a daily maintenance script:

```bash
sudo nano /opt/git-dashboard/maintenance.sh
```

Add the following content:

```bash
#!/bin/bash

# Log file
LOG_FILE="/opt/git-dashboard/logs/maintenance.log"

# Redirect stdout and stderr to log file
exec > >(tee -a "$LOG_FILE") 2>&1

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting daily maintenance"

# Cleanup old logs
echo "Cleaning up old logs..."
find /opt/git-dashboard/logs/ -name "*.log.*" -type f -mtime +30 -delete

# Backup configuration
echo "Backing up configuration..."
BACKUP_DIR="/opt/git-dashboard/backups"
mkdir -p "$BACKUP_DIR"
cp /etc/nginx/sites-available/git-dashboard.conf "$BACKUP_DIR/git-dashboard-nginx-$(date +%Y%m%d).conf"
cp /etc/systemd/system/git-dashboard.service "$BACKUP_DIR/git-dashboard-systemd-$(date +%Y%m%d).service"
cp /opt/git-dashboard/app.py "$BACKUP_DIR/app-$(date +%Y%m%d).py"
cp /opt/git-dashboard/collect-metrics.sh "$BACKUP_DIR/collect-metrics-$(date +%Y%m%d).sh"

# Cleanup old backups
find "$BACKUP_DIR" -type f -mtime +30 -delete

# Check services
echo "Checking services status..."
systemctl is-active --quiet nginx && echo "Nginx: Running" || echo "Nginx: Failed"
systemctl is-active --quiet git-dashboard && echo "Git Dashboard: Running" || echo "Git Dashboard: Failed"

# Run app health check
echo "Health check..."
curl -s http://localhost:8000/api/status | grep -q '"status":"ok"' && echo "API Health Check: OK" || echo "API Health Check: Failed"

# Check disk space
echo "Checking disk space..."
df -h / | awk 'NR==2 {print "Disk Usage: " $5}'  
if [ $(df / | awk 'NR==2 {print $5}' | tr -d '%') -gt 90 ]; then
    echo "WARNING: Disk usage above 90%"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Maintenance complete"
```

Make the script executable:

```bash
sudo chmod +x /opt/git-dashboard/maintenance.sh
```

Add to crontab to run daily at 3 AM:

```bash
sudo crontab -e
```

Add the following line:

```
0 3 * * * /opt/git-dashboard/maintenance.sh
```

## Log Management

### Setting Up Logrotate

Create a logrotate configuration for the dashboard:

```bash
sudo nano /etc/logrotate.d/git-dashboard
```

Add the following content:

```
/opt/git-dashboard/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload git-dashboard >/dev/null 2>&1 || true
    endscript
}

/var/log/nginx/git-dashboard.*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data adm
    sharedscripts
    postrotate
        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \
            run-parts /etc/logrotate.d/httpd-prerotate; \
        fi; \
        if [ -f /var/run/nginx.pid ]; then \
            kill -USR1 `cat /var/run/nginx.pid`; \
        fi;
    endscript
}
```

## System Security Hardening

### SSH Hardening

Edit the SSH configuration:

```bash
sudo nano /etc/ssh/sshd_config
```

Apply these security settings:

```
# Disable root login
PermitRootLogin no

# Use public key authentication
PubkeyAuthentication yes
PasswordAuthentication no

# Limit authentication attempts
MaxAuthTries 3

# Use strong algorithms
Ciphers <EMAIL>,<EMAIL>
KexAlgorithms curve25519-sha256,<EMAIL>
MACs <EMAIL>,<EMAIL>

# Set login grace time
LoginGraceTime 30

# Disable empty passwords
PermitEmptyPasswords no

# Enable strict mode
StrictModes yes

# Disable challenge-response authentication
ChallengeResponseAuthentication no

# Disable X11 forwarding
X11Forwarding no
```

Restart SSH service:

```bash
sudo systemctl restart ssh
```

### Fail2Ban Setup

Install and configure Fail2Ban to protect against brute force attacks:

```bash
sudo apt install -y fail2ban
```

Create a custom jail for SSH and Nginx:

```bash
sudo nano /etc/fail2ban/jail.local
```

Add the following content:

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
bannedports = ssh,http,https

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
port = http,https
filter = nginx-http-auth
logpath = /var/log/nginx/git-dashboard.error.log

[nginx-botsearch]
enabled = true
port = http,https
filter = nginx-botsearch
logpath = /var/log/nginx/git-dashboard.access.log
```

Restart Fail2Ban:

```bash
sudo systemctl restart fail2ban
```

### AppArmor Configuration

Use AppArmor to restrict application access:

```bash
# Install AppArmor utils
sudo apt install -y apparmor-utils

# Create AppArmor profile for Git Dashboard
sudo nano /etc/apparmor.d/opt.git-dashboard.app
```

Add a basic profile:

```
#include <tunables/global>

profile opt-git-dashboard {
  #include <abstractions/base>
  #include <abstractions/python>
  #include <abstractions/nameservice>
  
  # Dashboard directory
  /opt/git-dashboard/** r,
  /opt/git-dashboard/data/** rw,
  /opt/git-dashboard/logs/** rw,
  
  # Allow reading Git repositories
  /home/<USER>/**.git/ r,
  /home/<USER>/**.git/** r,
  
  # System access needed for metrics
  /proc/loadavg r,
  /proc/uptime r,
  /proc/meminfo r,
  /proc/stat r,
  /sys/devices/system/cpu/ r,
  /sys/devices/system/cpu/** r,
  
  # Python interpreter
  /usr/bin/python3.10 rix,
  /opt/git-dashboard/venv/bin/python3.10 rix,
  /opt/git-dashboard/venv/bin/gunicorn rix,
}
```

Load the profile:

```bash
sudo apparmor_parser -r /etc/apparmor.d/opt.git-dashboard.app
```

## SSL Certificate Automation

### Certbot Auto-renewal

Ensure Certbot auto-renewal is enabled:

```bash
sudo systemctl status certbot.timer
```

If not active, enable it:

```bash
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer
```

Test renewal process:

```bash
sudo certbot renew --dry-run
```

### Custom SSL Certificate Renewal Hook

Create a custom hook to run after certificate renewal:

```bash
sudo mkdir -p /etc/letsencrypt/renewal-hooks/post
sudo nano /etc/letsencrypt/renewal-hooks/post/git-dashboard.sh
```

Add the following content:

```bash
#!/bin/bash

# Reload Nginx to apply new certificates
systemctl reload nginx

# Log the renewal
echo "[$(date '+%Y-%m-%d %H:%M:%S')] SSL certificates renewed successfully" >> /opt/git-dashboard/logs/ssl-renewal.log

# Sync certificates to Git server if needed
if [ -f /usr/local/bin/sync-ssl-certs.sh ]; then
    /usr/local/bin/sync-ssl-certs.sh
fi
```

Make the script executable:

```bash
sudo chmod +x /etc/letsencrypt/renewal-hooks/post/git-dashboard.sh
```

## Troubleshooting Ubuntu-Specific Issues

### Common Issues and Solutions

1. **Package Installation Failures**
   ```bash
   # Fix broken packages
   sudo apt --fix-broken install
   
   # Update package repository
   sudo apt update
   ```

2. **Service Not Starting**
   ```bash
   # Check systemd journal
   sudo journalctl -xe
   
   # Check specific service logs
   sudo journalctl -u git-dashboard
   ```

3. **Permission Issues**
   ```bash
   # Fix permissions for data directory
   sudo find /opt/git-dashboard/data -type d -exec chmod 755 {} \;
   sudo find /opt/git-dashboard/data -type f -exec chmod 644 {} \;
   ```

4. **UFW Blocking Access**
   ```bash
   # Check UFW status
   sudo ufw status
   
   # Allow specific ports
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   ```

5. **System Resource Issues**
   ```bash
   # Check system resources
   free -h
   df -h
   top
   
   # Clear system cache if necessary
   sudo sync; echo 3 | sudo tee /proc/sys/vm/drop_caches
   ```
