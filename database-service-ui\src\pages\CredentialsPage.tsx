import React, { useState } from 'react';
import CredentialForm from '../components/CredentialForm';
import CredentialList from '../components/CredentialList';
import './CredentialsPage.css';

const CredentialsPage: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  const handleCredentialSaved = () => {
    // Trigger a refresh of the credential list
    setRefreshTrigger(prev => prev + 1);
  };
  
  return (
    <div className="credentials-page">
      <div className="container">
        <h1 className="page-title">Credential Management</h1>
        
        <div className="credentials-description card">
          <h2>Secure Credential Storage</h2>
          <p>
            This page allows you to securely store and manage credentials used by the Database Service.
            All credentials are encrypted using AES-256 encryption before being stored.
          </p>
          <p>
            <strong>Note:</strong> Only administrators can view and manage credentials.
          </p>
        </div>
        
        <div className="credentials-layout">
          <div className="credentials-form-container">
            <CredentialForm onCredentialSaved={handleCredentialSaved} />
          </div>
          
          <div className="credentials-list-container">
            <CredentialList refreshTrigger={refreshTrigger} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CredentialsPage;
