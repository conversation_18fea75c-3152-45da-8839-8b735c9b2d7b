# Database Service Troubleshooting and Maintenance

This document provides comprehensive troubleshooting procedures and maintenance guidelines for the Database Service and UI integration.

## Troubleshooting Guide

### Common Integration Issues

#### 1. CORS Errors

**Symptoms:**
- Browser console shows CORS policy errors
- API requests fail with network errors
- Preflight OPTIONS requests failing

**Diagnosis:**
```bash
# Test CORS preflight request
curl -H "Origin: https://git.chcit.org" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://localhost:8080/api/health

# Check response headers
curl -I -H "Origin: https://git.chcit.org" \
     http://localhost:8080/api/health
```

**Solutions:**
```cpp
// Backend: Verify CORS configuration
{
  "api": {
    "cors": {
      "enabled": true,
      "allowed_origins": ["https://git.chcit.org", "http://localhost:3000"],
      "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      "allowed_headers": ["Content-Type", "Authorization"],
      "allow_credentials": true
    }
  }
}
```

```nginx
# Nginx: Add CORS headers if needed
location /database-api/ {
    add_header Access-Control-Allow-Origin "https://git.chcit.org" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
    add_header Access-Control-Allow-Credentials "true" always;
    
    if ($request_method = 'OPTIONS') {
        return 204;
    }
    
    # ... rest of configuration
}
```

#### 2. Authentication Failures

**Symptoms:**
- Login requests return 401 Unauthorized
- Token refresh fails
- Protected routes redirect to login

**Diagnosis:**
```bash
# Test authentication endpoint directly
curl -X POST http://localhost:8080/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"password"}' \
     -v

# Check JWT token validity
echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | base64 -d

# Verify database user exists
sudo -u postgres psql -d gitdashboard -c "SELECT * FROM users WHERE username='admin';"
```

**Solutions:**
```bash
# Check database service logs
sudo journalctl -u database-service -f

# Verify JWT secret configuration
sudo grep jwt_secret /opt/database-service/etc/database-service/config.json

# Reset admin password if needed
sudo -u postgres psql -d gitdashboard -c "
UPDATE users SET password_hash = crypt('newpassword', gen_salt('bf')) 
WHERE username = 'admin';"

# Restart service with new configuration
sudo systemctl restart database-service
```

#### 3. Database Connection Issues

**Symptoms:**
- Health check fails with database errors
- Connection pool exhaustion
- SSL connection failures

**Diagnosis:**
```bash
# Test PostgreSQL connection directly
psql -h localhost -U database_service_user -d gitdashboard

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-14-main.log

# Verify SSL certificates
sudo ls -la /etc/letsencrypt/live/chcit.org/
sudo openssl x509 -in /etc/letsencrypt/live/chcit.org/fullchain.pem -text -noout

# Check connection pool status
curl http://localhost:8080/api/database/metrics | jq .connection_pool
```

**Solutions:**
```sql
-- Create database user if missing
CREATE USER database_service_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE gitdashboard TO database_service_user;
GRANT USAGE ON SCHEMA public TO database_service_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO database_service_user;
```

```bash
# Fix SSL certificate permissions
sudo chown database-service:database-service /etc/letsencrypt/live/chcit.org/fullchain.pem
sudo chmod 644 /etc/letsencrypt/live/chcit.org/fullchain.pem

# Adjust connection pool settings
sudo nano /opt/database-service/etc/database-service/config.json
# Increase max_connections or reduce connection timeout
```

#### 4. Nginx Proxy Issues

**Symptoms:**
- 502 Bad Gateway errors
- Requests not reaching backend
- SSL certificate errors

**Diagnosis:**
```bash
# Test Nginx configuration
sudo nginx -t

# Check if backend is running
curl http://127.0.0.1:8080/api/health

# Verify proxy configuration
sudo grep -A 20 "location /database-api" /etc/nginx/sites-available/git.chcit.org

# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log

# Test SSL certificate
openssl s_client -connect git.chcit.org:443 -servername git.chcit.org
```

**Solutions:**
```bash
# Restart backend service
sudo systemctl restart database-service

# Reload Nginx configuration
sudo systemctl reload nginx

# Check upstream backend configuration
sudo grep -A 5 "upstream database_service_backend" /etc/nginx/sites-available/git.chcit.org

# Verify firewall rules
sudo ufw status
sudo netstat -tuln | grep 8080
```

### Performance Issues

#### 1. Slow API Responses

**Investigation:**
```bash
# Monitor database service performance
top -p $(pgrep database-service)
htop -p $(pgrep database-service)

# Check database query performance
sudo -u postgres psql -d gitdashboard -c "
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"

# Monitor connection pool metrics
watch -n 5 'curl -s http://localhost:8080/api/database/metrics | jq .connection_pool'

# Check system resources
iostat -x 1
free -h
df -h
```

**Solutions:**
```sql
-- Optimize slow queries
EXPLAIN ANALYZE SELECT * FROM large_table WHERE condition;

-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_table_column ON table_name(column_name);

-- Update table statistics
ANALYZE table_name;
```

```json
// Adjust configuration for better performance
{
  "database": {
    "pool": {
      "max_connections": 30,
      "connection_timeout": 10,
      "idle_timeout": 300
    }
  },
  "api": {
    "request_timeout": 30,
    "max_concurrent_requests": 100
  }
}
```

#### 2. High Memory Usage

**Investigation:**
```bash
# Check memory usage by component
ps aux | grep database-service
sudo pmap -x $(pgrep database-service)

# Monitor memory over time
sar -r 1 60

# Check for memory leaks
valgrind --tool=memcheck --leak-check=full ./database-service --config config.json
```

**Solutions:**
```bash
# Reduce connection pool size
sudo nano /opt/database-service/etc/database-service/config.json
# Set lower max_connections value

# Adjust PostgreSQL memory settings
sudo nano /etc/postgresql/14/main/postgresql.conf
# shared_buffers = 256MB
# work_mem = 4MB
# maintenance_work_mem = 64MB

# Restart services
sudo systemctl restart postgresql
sudo systemctl restart database-service
```

#### 3. High CPU Usage

**Investigation:**
```bash
# Profile CPU usage
perf top -p $(pgrep database-service)
strace -p $(pgrep database-service) -c

# Check for busy loops
gdb -p $(pgrep database-service)
(gdb) thread apply all bt

# Monitor database CPU usage
sudo -u postgres psql -d gitdashboard -c "
SELECT query, calls, total_time, mean_time, cpu_time_percent
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```

**Solutions:**
```cpp
// Optimize hot code paths
// Add connection pooling
// Implement query caching
// Use prepared statements
```

### Network Issues

#### 1. Connection Timeouts

**Diagnosis:**
```bash
# Test network connectivity
telnet git.chcit.org 443
nc -zv git.chcit.org 443

# Check DNS resolution
nslookup git.chcit.org
dig git.chcit.org

# Monitor network traffic
sudo tcpdump -i any port 8080
sudo netstat -an | grep 8080
```

**Solutions:**
```nginx
# Increase timeout values in Nginx
location /database-api/ {
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    proxy_buffering off;
}
```

```json
// Increase timeouts in backend
{
  "api": {
    "connection_timeout": 60,
    "request_timeout": 120,
    "keepalive_timeout": 300
  }
}
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### 1. Daily Tasks

**Automated Health Checks:**
```bash
#!/bin/bash
# /opt/database-service/scripts/daily-health-check.sh

LOG_FILE="/var/log/database-service/health-check.log"
DATE=$(date -Iseconds)

echo "[$DATE] Starting daily health check" >> "$LOG_FILE"

# Check service status
if ! systemctl is-active --quiet database-service; then
    echo "[$DATE] ERROR: Database service is not running" >> "$LOG_FILE"
    systemctl restart database-service
    sleep 10
fi

# Check API health
if ! curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
    echo "[$DATE] ERROR: API health check failed" >> "$LOG_FILE"
fi

# Check database connectivity
if ! sudo -u postgres psql -d gitdashboard -c "SELECT 1;" > /dev/null 2>&1; then
    echo "[$DATE] ERROR: Database connectivity check failed" >> "$LOG_FILE"
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "[$DATE] WARNING: Disk usage is ${DISK_USAGE}%" >> "$LOG_FILE"
fi

# Check memory usage
MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
if (( $(echo "$MEM_USAGE > 90" | bc -l) )); then
    echo "[$DATE] WARNING: Memory usage is ${MEM_USAGE}%" >> "$LOG_FILE"
fi

echo "[$DATE] Daily health check completed" >> "$LOG_FILE"
```

#### 2. Weekly Tasks

**Database Maintenance:**
```bash
#!/bin/bash
# /opt/database-service/scripts/weekly-maintenance.sh

LOG_FILE="/var/log/database-service/maintenance.log"
DATE=$(date -Iseconds)

echo "[$DATE] Starting weekly maintenance" >> "$LOG_FILE"

# Update database statistics
sudo -u postgres psql -d gitdashboard -c "ANALYZE;" >> "$LOG_FILE" 2>&1

# Vacuum database
sudo -u postgres psql -d gitdashboard -c "VACUUM ANALYZE;" >> "$LOG_FILE" 2>&1

# Check database size
DB_SIZE=$(sudo -u postgres psql -d gitdashboard -t -c "SELECT pg_size_pretty(pg_database_size('gitdashboard'));")
echo "[$DATE] Database size: $DB_SIZE" >> "$LOG_FILE"

# Reindex if needed
sudo -u postgres psql -d gitdashboard -c "REINDEX DATABASE gitdashboard;" >> "$LOG_FILE" 2>&1

# Clean old log files
find /var/log/database-service/ -name "*.log" -mtime +30 -delete

echo "[$DATE] Weekly maintenance completed" >> "$LOG_FILE"
```

#### 3. Monthly Tasks

**Security Updates:**
```bash
#!/bin/bash
# /opt/database-service/scripts/monthly-security-update.sh

# Update system packages
sudo apt update
sudo apt list --upgradable

# Check for security updates
sudo unattended-upgrades --dry-run

# Update SSL certificates
sudo certbot renew --dry-run

# Audit database permissions
sudo -u postgres psql -d gitdashboard -c "
SELECT grantee, privilege_type, table_name 
FROM information_schema.role_table_grants 
WHERE table_schema = 'public';"

# Check for unused database connections
sudo -u postgres psql -d gitdashboard -c "
SELECT pid, usename, application_name, client_addr, state, state_change
FROM pg_stat_activity 
WHERE state = 'idle' AND state_change < NOW() - INTERVAL '1 hour';"
```

### Backup Procedures

#### 1. Database Backup

**Automated Backup Script:**
```bash
#!/bin/bash
# /opt/database-service/scripts/backup-database.sh

BACKUP_DIR="/opt/database-service/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/gitdashboard_$DATE.sql"
LOG_FILE="/var/log/database-service/backup.log"

mkdir -p "$BACKUP_DIR"

echo "[$(date -Iseconds)] Starting database backup" >> "$LOG_FILE"

# Create database backup
if sudo -u postgres pg_dump gitdashboard > "$BACKUP_FILE"; then
    echo "[$(date -Iseconds)] Database backup created: $BACKUP_FILE" >> "$LOG_FILE"
    
    # Compress backup
    gzip "$BACKUP_FILE"
    echo "[$(date -Iseconds)] Backup compressed: ${BACKUP_FILE}.gz" >> "$LOG_FILE"
    
    # Remove backups older than 30 days
    find "$BACKUP_DIR" -name "*.sql.gz" -mtime +30 -delete
    echo "[$(date -Iseconds)] Old backups cleaned up" >> "$LOG_FILE"
    
    # Verify backup integrity
    if gunzip -t "${BACKUP_FILE}.gz"; then
        echo "[$(date -Iseconds)] Backup integrity verified" >> "$LOG_FILE"
    else
        echo "[$(date -Iseconds)] ERROR: Backup integrity check failed" >> "$LOG_FILE"
    fi
else
    echo "[$(date -Iseconds)] ERROR: Database backup failed" >> "$LOG_FILE"
    exit 1
fi

echo "[$(date -Iseconds)] Database backup completed successfully" >> "$LOG_FILE"
```

#### 2. Configuration Backup

```bash
#!/bin/bash
# /opt/database-service/scripts/backup-config.sh

BACKUP_DIR="/opt/database-service/backups"
DATE=$(date +%Y%m%d)
CONFIG_BACKUP="$BACKUP_DIR/config_$DATE.tar.gz"

# Create configuration backup
sudo tar -czf "$CONFIG_BACKUP" \
    /opt/database-service/etc/database-service/ \
    /etc/nginx/sites-available/git.chcit.org \
    /var/www/html/database-ui/ \
    /etc/systemd/system/database-service.service

echo "Configuration backup created: $CONFIG_BACKUP"

# Keep only last 7 configuration backups
find "$BACKUP_DIR" -name "config_*.tar.gz" -mtime +7 -delete
```

### Monitoring Setup

#### 1. Service Monitoring

**Systemd Service Monitoring:**
```bash
#!/bin/bash
# /opt/database-service/scripts/monitor-service.sh

SERVICE_NAME="database-service"
LOG_FILE="/var/log/database-service/monitor.log"

if ! systemctl is-active --quiet "$SERVICE_NAME"; then
    echo "[$(date -Iseconds)] $SERVICE_NAME is down, attempting restart" >> "$LOG_FILE"
    
    systemctl restart "$SERVICE_NAME"
    sleep 10
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        echo "[$(date -Iseconds)] $SERVICE_NAME restarted successfully" >> "$LOG_FILE"
    else
        echo "[$(date -Iseconds)] Failed to restart $SERVICE_NAME" >> "$LOG_FILE"
        # Send alert email
        echo "Database service failed to restart on $(hostname)" | \
            mail -s "Database Service Alert" <EMAIL>
    fi
fi

# Check API responsiveness
if ! curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
    echo "[$(date -Iseconds)] API health check failed" >> "$LOG_FILE"
fi
```

#### 2. Performance Monitoring

**Metrics Collection:**
```bash
#!/bin/bash
# /opt/database-service/scripts/collect-metrics.sh

METRICS_FILE="/var/log/database-service/metrics.log"
TIMESTAMP=$(date -Iseconds)

# Collect system metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')

# Collect service metrics
if SERVICE_METRICS=$(curl -s http://localhost:8080/api/database/metrics); then
    ACTIVE_CONN=$(echo "$SERVICE_METRICS" | jq -r '.connection_pool.active // 0')
    TOTAL_QUERIES=$(echo "$SERVICE_METRICS" | jq -r '.queries.total // 0')
    AVG_RESPONSE=$(echo "$SERVICE_METRICS" | jq -r '.queries.avg_response_time // 0')
else
    ACTIVE_CONN="N/A"
    TOTAL_QUERIES="N/A"
    AVG_RESPONSE="N/A"
fi

# Log metrics in CSV format
echo "$TIMESTAMP,$CPU_USAGE,$MEM_USAGE,$DISK_USAGE,$ACTIVE_CONN,$TOTAL_QUERIES,$AVG_RESPONSE" >> "$METRICS_FILE"

# Rotate metrics log if it gets too large
if [ $(wc -l < "$METRICS_FILE") -gt 10000 ]; then
    tail -5000 "$METRICS_FILE" > "${METRICS_FILE}.tmp"
    mv "${METRICS_FILE}.tmp" "$METRICS_FILE"
fi
```

### Disaster Recovery

#### 1. Service Recovery Procedure

```bash
#!/bin/bash
# /opt/database-service/scripts/disaster-recovery.sh

BACKUP_DIR="/opt/database-service/backups"
LOG_FILE="/var/log/database-service/recovery.log"

echo "[$(date -Iseconds)] Starting disaster recovery procedure" >> "$LOG_FILE"

# Stop services
sudo systemctl stop database-service
sudo systemctl stop nginx

# Restore configuration from latest backup
LATEST_CONFIG=$(ls -t "$BACKUP_DIR"/config_*.tar.gz | head -1)
if [ -n "$LATEST_CONFIG" ]; then
    echo "[$(date -Iseconds)] Restoring configuration from $LATEST_CONFIG" >> "$LOG_FILE"
    sudo tar -xzf "$LATEST_CONFIG" -C /
fi

# Restore database from latest backup
LATEST_DB_BACKUP=$(ls -t "$BACKUP_DIR"/gitdashboard_*.sql.gz | head -1)
if [ -n "$LATEST_DB_BACKUP" ]; then
    echo "[$(date -Iseconds)] Restoring database from $LATEST_DB_BACKUP" >> "$LOG_FILE"
    
    # Drop and recreate database
    sudo -u postgres dropdb gitdashboard
    sudo -u postgres createdb gitdashboard
    
    # Restore data
    gunzip -c "$LATEST_DB_BACKUP" | sudo -u postgres psql gitdashboard
fi

# Restart services
sudo systemctl start database-service
sudo systemctl start nginx

# Verify functionality
sleep 10
if curl -f https://git.chcit.org/database-api/health > /dev/null 2>&1; then
    echo "[$(date -Iseconds)] Disaster recovery completed successfully" >> "$LOG_FILE"
else
    echo "[$(date -Iseconds)] ERROR: Service verification failed after recovery" >> "$LOG_FILE"
fi
```

This comprehensive troubleshooting and maintenance guide provides all the necessary procedures for maintaining a healthy Database Service and UI integration in production environments.
