# Edit Project Settings Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Edit project settings function
function Edit-ProjectSettings {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Edit Project Settings                   " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host 
    
    # Ensure deployment_scripts property exists
    if (-not (Get-Member -InputObject $Config.project -Name "deployment_scripts" -MemberType Properties)) {
        $Config.project | Add-Member -MemberType NoteProperty -Name "deployment_scripts" -Value "D:\Augment\project-tracker\database-service-scripts\scripts\deployment"
    }
    
    # Ensure source_dir is correct
    if ($Config.project.source_dir -like "*\config" -or $Config.project.source_dir -like "*\deployment*") {
        $Config.project.source_dir = "D:\Augment\project-tracker\database-service"
        Save-Configuration
    }
    
    Write-Host "Current Project Settings:" -ForegroundColor Yellow
    Write-Host "  Name: $($Config.project.name)" -ForegroundColor White
    Write-Host "  Description: $($Config.project.description)" -ForegroundColor White
    Write-Host "  Source Code: $($Config.project.source_dir)" -ForegroundColor White
    Write-Host "  Deployment Scripts: $($Config.project.deployment_scripts)" -ForegroundColor White
    Write-Host "  Install Directory: $($Config.project.install_dir)" -ForegroundColor White
    Write-Host 
    
    Write-Host "[1] Edit Project Name" -ForegroundColor White
    Write-Host "[2] Edit Project Description" -ForegroundColor White
    Write-Host "[3] Edit Source Code" -ForegroundColor White
    Write-Host "[4] Edit Deployment Scripts" -ForegroundColor White
    Write-Host "[5] Edit Install Directory" -ForegroundColor White
    Write-Host "[6] Back to Edit Configuration" -ForegroundColor White
    
    $choice = Read-Host "`nSelect an option (1-6)"
    
    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new project name"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $Config.project.name = $newValue
                if (Save-Configuration) {
                    Write-Host "Project name updated to: $newValue" -ForegroundColor Green
                    Start-Sleep -Seconds 1
                }
            }
            Edit-ProjectSettings
        }
        "2" {
            $newValue = Read-Host "Enter new project description"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $Config.project.description = $newValue
                Save-Configuration
            }
            Edit-ProjectSettings
        }
        "3" {
            $newValue = Read-Host "Enter new source code directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $Config.project.source_dir = $newValue
                Save-Configuration
            }
            Edit-ProjectSettings
        }
        "4" {
            $newValue = Read-Host "Enter new deployment scripts directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $Config.project.deployment_scripts = $newValue
                Save-Configuration
            }
            Edit-ProjectSettings
        }
        "5" {
            $newValue = Read-Host "Enter new install directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $Config.project.install_dir = $newValue
                Save-Configuration
            }
            Edit-ProjectSettings
        }
        "6" { 
            # Return to Edit Configuration
            & "$PSScriptRoot\Manage-DeploymentConfigurations.ps1"
        }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Edit-ProjectSettings
        }
    }
}

# Run the function
Edit-ProjectSettings
