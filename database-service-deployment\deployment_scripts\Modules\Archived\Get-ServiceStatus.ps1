# Get Service Status Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Get service status function
function Get-ServiceStatus {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Service Status                         " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host 
    
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "SSH configuration is not set up. Please configure SSH settings first." -ForegroundColor Red
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    
    # Get service name
    $serviceName = $Config.service.name
    
    # Check if the service is installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    
    if ($serviceExists -eq 0) {
        Write-Host "Service $serviceName is not installed." -ForegroundColor Red
        Write-Host "Please install the service first." -ForegroundColor Yellow
        Wait-ForUser
        & "$PSScriptRoot\Install-Service.ps1"
        return
    }
    
    try {
        # Get service status
        Write-Host "Checking status of service $serviceName..." -ForegroundColor Yellow
        $statusCmd = "systemctl status $serviceName"
        $status = Invoke-RemoteCommand -Command $statusCmd
        
        Write-Host 
        Write-Host "Service Status:" -ForegroundColor Cyan
        Write-Host $status -ForegroundColor Gray
        
        # Get service logs
        Write-Host 
        Write-Host "Recent Service Logs:" -ForegroundColor Cyan
        $logsCmd = "journalctl -u $serviceName -n 20 --no-pager"
        $logs = Invoke-RemoteCommand -Command $logsCmd
        Write-Host $logs -ForegroundColor Gray
        
        # Check if PostgreSQL is running
        Write-Host 
        Write-Host "PostgreSQL Status:" -ForegroundColor Cyan
        $pgStatusCmd = "systemctl status postgresql"
        $pgStatus = Invoke-RemoteCommand -Command $pgStatusCmd
        Write-Host $pgStatus -ForegroundColor Gray
    } catch {
        Write-Host "Error checking service status: $_" -ForegroundColor Red
    }
    
    Write-Host 
    Write-Host "Press any key to return to the main menu..."
    Wait-ForUser
    
    Show-MainMenu
}

# Run the function
Get-ServiceStatus
