import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { isAuthenticated, isAdmin } from './utils/auth';
import Header from './components/Header';
import Footer from './components/Footer';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import MetricsPage from './pages/MetricsPage';
import CredentialsPage from './pages/CredentialsPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';
import './App.css';

// Protected route component
const ProtectedRoute: React.FC<{ element: React.ReactNode; adminOnly?: boolean }> = ({ 
  element, 
  adminOnly = false 
}) => {
  const authenticated = isAuthenticated();
  const admin = isAdmin();
  
  if (!authenticated) {
    return <Navigate to="/login" />;
  }
  
  if (adminOnly && !admin) {
    return <Navigate to="/dashboard" />;
  }
  
  return <>{element}</>;
};

const App: React.FC = () => {
  return (
    <div className="app">
      <Header />
      
      <main className="main-content">
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          
          <Route 
            path="/" 
            element={<Navigate to="/dashboard" />} 
          />
          
          <Route 
            path="/dashboard" 
            element={<ProtectedRoute element={<DashboardPage />} />} 
          />
          
          <Route 
            path="/metrics" 
            element={<ProtectedRoute element={<MetricsPage />} />} 
          />
          
          <Route 
            path="/credentials" 
            element={<ProtectedRoute element={<CredentialsPage />} adminOnly={true} />} 
          />
          
          <Route 
            path="/settings" 
            element={<ProtectedRoute element={<SettingsPage />} adminOnly={true} />} 
          />
          
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </main>
      
      <Footer />
    </div>
  );
};

export default App;
