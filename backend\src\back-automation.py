import re
from typing import Dict, List, Optional
from contextlib import contextmanager
from datetime import datetime, timedelta
import logging
from git_integration import GitIntegration
from templates import ProjectTemplate, CommitTracker
from psycopg2 import Error as PostgresError
from redis.exceptions import RedisError
from custom_exceptions import GitOperationError

@contextmanager
def database_operation():
    """Context manager for database operations with proper error handling"""
    try:
        yield
    except PostgresError as e:
        logger.error(f"Database error: {str(e)}", exc_info=True)
        raise DatabaseError(f"Database operation failed: {str(e)}")
    except RedisError as e:
        logger.error(f"Cache error: {str(e)}", exc_info=True)
        raise CacheError(f"Cache operation failed: {str(e)}")
    except Exception as e:
        logger.critical(f"Unexpected error: {str(e)}", exc_info=True)
        raise

class AutomationManager:
    def __init__(self, project_config: Dict, git_integration: GitIntegration):
        self.config = project_config
        self.git = git_integration
        self.setup_logging()
    
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('automation.log'),
                logging.StreamHandler()
            ]
        )
    
    def analyze_recent_activity(self, days: int = 7) -> Dict:
        """Analyze recent project activity and generate insights."""
        since = datetime.now() - timedelta(days=days)
        commits = self.git.get_latest_commits(since.strftime("%Y-%m-%d"))
        
        analysis = {
            "total_commits": len(commits),
            "active_files": set(),
            "category_changes": {},
            "severity_distribution": {
                "critical": 0,
                "high": 0,
                "medium": 0,
                "low": 0
            },
            "top_contributors": {},
            "potential_risks": [],
            "suggested_reviews": []
        }
        
        for commit in commits:
            # Track active files
            analysis["active_files"].update(commit["affected_files"])
            
            # Track category changes
            category = commit.get("category", "Unknown")
            analysis["category_changes"][category] = analysis["category_changes"].get(category, 0) + 1
            
            # Track severity distribution
            severity = commit.get("severity", "medium")
            analysis["severity_distribution"][severity] += 1
            
            # Track contributors
            analysis["top_contributors"][commit["author"]] = analysis["top_contributors"].get(commit["author"], 0) + 1
            
            # Identify potential risks
            impact = self.git.analyze_commit_impact(commit["hash"])
            if impact.get("impact_level") == "high":
                analysis["potential_risks"].append({
                    "commit": commit["hash"],
                    "message": commit["message"],
                    "impact": impact
                })
            
            # Suggest code reviews
            if len(commit["affected_files"]) > 5 or "security" in commit["message"].lower():
                analysis["suggested_reviews"].append({
                    "commit": commit["hash"],
                    "message": commit["message"],
                    "files": commit["affected_files"]
                })
        
        # Convert sets to lists for JSON serialization
        analysis["active_files"] = list(analysis["active_files"])
        
        return analysis
    
    def suggest_improvements(self, analysis: Dict) -> List[Dict]:
        """Generate improvement suggestions with proper error handling."""
        try:
            with database_operation():
                suggestions = []
                file_activity = self._calculate_file_activity(analysis)
                
                high_activity_files = {
                    f: c for f, c in file_activity.items() 
                    if c > self.config.HIGH_ACTIVITY_THRESHOLD
                }
                
                if high_activity_files:
                    suggestions.append(self._create_refactor_suggestion(high_activity_files))
                
                return suggestions
                
        except GitOperationError as e:
            logger.error(f"Git operation failed: {str(e)}")
            return []
    
    def generate_weekly_report(self) -> Dict:
        """Generate a weekly activity report."""
        analysis = self.analyze_recent_activity(days=7)
        suggestions = self.suggest_improvements(analysis)
        
        return {
            "period": {
                "start": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d"),
                "end": datetime.now().strftime("%Y-%m-%d")
            },
            "activity_summary": {
                "total_commits": analysis["total_commits"],
                "active_files": len(analysis["active_files"]),
                "top_categories": dict(sorted(
                    analysis["category_changes"].items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]),
                "severity_distribution": analysis["severity_distribution"]
            },
            "contributors": dict(sorted(
                analysis["top_contributors"].items(),
                key=lambda x: x[1],
                reverse=True
            )),
            "potential_risks": analysis["potential_risks"],
            "suggested_reviews": analysis["suggested_reviews"],
            "improvement_suggestions": suggestions
        }
    
    def auto_categorize_improvements(self) -> List[Dict]:
        """Auto-categorize recent improvements based on commit patterns."""
        commits = self.git.get_latest_commits(since="1 week ago")
        categorized = []
        
        for commit in commits:
            # Skip already categorized commits
            if commit.get("category"):
                continue
            
            # Auto-detect category and severity
            category = ProjectTemplate.suggest_category(
                commit["message"],
                commit["affected_files"]
            )
            severity = ProjectTemplate.detect_severity(commit["message"])
            
            categorized.append({
                "commit": commit["hash"],
                "message": commit["message"],
                "suggested_category": category,
                "suggested_severity": severity,
                "confidence": "high" if category != "General" else "medium"
            })
        
        return categorized
    
    def detect_related_improvements(self, improvement_id: str) -> List[Dict]:
        """Find improvements that might be related to a given improvement."""
        # Get the improvement details (assuming they're stored somewhere)
        # This is a placeholder - you'd need to implement the actual retrieval
        improvement = {"id": improvement_id, "title": "", "description": "", "files": []}
        
        related = []
        commits = self.git.get_latest_commits(since="30 days ago")
        
        for commit in commits:
            # Check for file overlap
            if any(f in improvement["files"] for f in commit["affected_files"]):
                related.append({
                    "commit": commit["hash"],
                    "message": commit["message"],
                    "relation_type": "file_overlap",
                    "confidence": "high"
                })
                continue
            
            # Check for semantic similarity in commit messages
            # This is a simple check - you might want to use more sophisticated NLP
            if any(word in commit["message"].lower() 
                   for word in improvement["description"].lower().split()):
                related.append({
                    "commit": commit["hash"],
                    "message": commit["message"],
                    "relation_type": "semantic_similarity",
                    "confidence": "medium"
                })
        
        return related
