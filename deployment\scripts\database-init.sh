#!/bin/bash
set -euo pipefail

# Source common functions and configurations
source "$(dirname "$0")/common.sh"
source "$(dirname "$0")/db-config.sh"

# Constants
readonly BACKUP_DIR="/opt/project-tracker/backups"
readonly SCHEMA_DIR="/opt/project-tracker/database/schemas"
readonly MIGRATION_DIR="/opt/project-tracker/database/migrations"

# Logging setup
setup_logging() {
    local log_dir="/var/log/project-tracker"
    local log_file="${log_dir}/db-init-$(date +%Y%m%d-%H%M%S).log"
    
    mkdir -p "${log_dir}"
    exec 1> >(tee -a "${log_file}")
    exec 2> >(tee -a "${log_file}" >&2)
    
    log_info "Database initialization started"
}

# Database backup
backup_database() {
    local backup_file="${BACKUP_DIR}/backup-$(date +%Y%m%d-%H%M%S).sql"
    
    log_info "Creating database backup"
    mkdir -p "${BACKUP_DIR}"
    
    if pg_dump "${DB_NAME}" > "${backup_file}"; then
        log_info "Backup created successfully: ${backup_file}"
    else
        log_error "Backup failed"
        return 1
    fi
}

# Schema validation
validate_schema() {
    local schema_file="${1}"
    
    log_info "Validating schema: ${schema_file}"
    
    if ! psql -v ON_ERROR_STOP=1 -f "${schema_file}" "${DB_NAME}" --dry-run; then
        log_error "Schema validation failed: ${schema_file}"
        return 1
    fi
}

# Apply migrations
apply_migrations() {
    local migration_files=("${MIGRATION_DIR}"/*.sql)
    
    log_info "Applying migrations"
    
    for migration in "${migration_files[@]}"; do
        log_info "Applying migration: ${migration}"
        
        if ! psql -v ON_ERROR_STOP=1 -f "${migration}" "${DB_NAME}"; then
            log_error "Migration failed: ${migration}"
            return 1
        fi
    done
}

# Verify database
verify_database() {
    log_info "Verifying database integrity"
    
    # Check connection
    if ! psql -c "\l" "${DB_NAME}"; then
        log_error "Database connection failed"
        return 1
    fi
    
    # Check tables
    local required_tables=("users" "projects" "tasks" "categories")
    for table in "${required_tables[@]}"; do
        if ! psql -c "\dt ${table}" "${DB_NAME}" >/dev/null 2>&1; then
            log_error "Required table missing: ${table}"
            return 1
        fi
    done
}

# Main function
main() {
    setup_logging
    
    # Check if running as postgres user
    if [[ "$(whoami)" != "postgres" ]]; then
        log_error "This script must be run as postgres user"
        exit 1
    fi
    
    # Create backup before making changes
    backup_database || exit 1
    
    # Validate and apply schema
    validate_schema "${SCHEMA_DIR}/base_schema.sql" || exit 1
    
    # Apply migrations
    apply_migrations || exit 1
    
    # Verify database
    verify_database || exit 1
    
    log_info "Database initialization completed successfully"
}

# Execute main function
main "$@"
