# Load user and permission settings from requirements.json
function Import-Requirements {
    param(
        [Parameter(Position = 0, Mandatory = $false)]
        [switch]$Silent
    )

    try {
        $requirementsPath = Join-Path -Path $PSScriptRoot -ChildPath "requirements.json"

        if (-not (Test-Path $requirementsPath)) {
            if (-not $Silent) {
                Write-Host "Requirements file not found at: $requirementsPath" -ForegroundColor Red
            }
            return $null
        }

        $requirementsJson = Get-Content -Path $requirementsPath -Raw | ConvertFrom-Json
        return $requirementsJson
    } catch {
        if (-not $Silent) {
            Write-Host "Error loading requirements: $_" -ForegroundColor Red
        }
        return $null
    }
}

# Change environment
function Change-Environment {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Change Environment                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Display current configuration if available
    Write-Host "Current Configuration:" -ForegroundColor Yellow
    if ($null -ne $Config -and $null -ne $Config.project -and $null -ne $DeploymentEnvironment) {
        Write-Host "Project: $($Config.project.name)" -ForegroundColor White
        Write-Host "Environment: $DeploymentEnvironment" -ForegroundColor White
        Write-Host "Server: $($Config.ssh.host)" -ForegroundColor White
        Write-Host "Install Directory: $($Config.project.install_dir)" -ForegroundColor White
    } else {
        Write-Host "No configuration currently loaded." -ForegroundColor White
    }
    Write-Host

    # Check if config directory exists
    if (-not (Test-Path -Path $ConfigDir)) {
        Write-Host "No configuration directory found. Creating one now..." -ForegroundColor Yellow
        try {
            New-Item -Path $ConfigDir -ItemType Directory | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return
        }
    }

    # Get available config files
    $configFiles = Get-ChildItem -Path $ConfigDir -Filter "*.json" | Select-Object -ExpandProperty Name

    if ($configFiles.Count -eq 0) {
        Write-Host "No configuration files found. Please create a new configuration." -ForegroundColor Yellow
        Write-Host
        $environment = Read-Host "Environment name (development, testing, production)"
        Create-DefaultConfig -Environment $environment
        $script:DeploymentEnvironment = $environment
        return
    }

    # Display available environments
    Write-Host "Menu Options:" -ForegroundColor Yellow

    $menu = @{}
    $index = 1

    # Add option to create a new configuration
    Write-Host "[$index] Create New Configuration" -ForegroundColor White
    $menu[$index] = "new"
    $index++

    # Section for saved configurations
    Write-Host
    Write-Host "Available Configurations:" -ForegroundColor Yellow
    $configIndex = $index

    foreach ($file in $configFiles) {
        # Extract both project name and environment from filename (project-environment.json)
        if ($file -match '(.+)-(.+)\.json$') {
            $projectName = $matches[1]
            $env = $matches[2]
            $menu[$index] = @{
                file = $file
                environment = $env
                project = $projectName
            }

            # Check if this configuration has a "True" project name issue
            $configFile = Join-Path -Path $ConfigDir -ChildPath $file
            $displayName = $projectName

            try {
                $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                if ($configContent.project.name -eq $true) {
                    # If the project name is "True", fix it now but don't output a message
                    $configContent.project.name = $projectName
                    $configContent | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
                    $displayName = $projectName
                } else {
                    $displayName = $configContent.project.name
                }

                # Simply show the filename
                Write-Host "[$index] $file" -ForegroundColor White
            } catch {
                Write-Host "[$index] $file" -ForegroundColor White
                Write-Host "    Warning: Could not read configuration file properly" -ForegroundColor Yellow
            }

            $index++
        }
    }

    # Add option to back to main menu (at the end)
    Write-Host
    Write-Host "[$index] Return to Main Menu" -ForegroundColor White
    $menu[$index] = "back"

    Write-Host
    $choice = Read-Host "Select an option"

    # Make sure we treat the choice as a string for consistent comparison
    $choice = $choice.ToString()

    [int]$choiceNum = 0
    $validInt = [int]::TryParse($choice, [ref]$choiceNum)

    if ($validInt -and $menu.ContainsKey($choiceNum)) {
        $selected = $menu[$choiceNum]

        if ($selected -eq "back") {
            Return-ToMainMenu
            return
        }
        elseif ($selected -eq "new") {
            # Create new configuration
            $environment = Read-Host "Environment name (development, testing, production)"
            Create-DefaultConfig -Environment $environment
            $script:DeploymentEnvironment = $environment
            return
        }
        else {
            # Load an existing configuration
            $configFile = Join-Path -Path $ConfigDir -ChildPath $selected.file
            try {
                $config = Get-Content -Path $configFile -Raw | ConvertFrom-Json

                # Convert from JSON to PowerShell hashtable
                $script:Config = @{}
                $config.PSObject.Properties | ForEach-Object {
                    $name = $_.Name
                    $value = $_.Value

                    if ($value -is [System.Management.Automation.PSCustomObject]) {
                        $valueHash = @{}
                        $value.PSObject.Properties | ForEach-Object {
                            $valueHash[$_.Name] = $_.Value
                        }
                        $script:Config[$name] = $valueHash
                    } else {
                        $script:Config[$name] = $value
                    }
                }

                $script:DeploymentEnvironment = $selected.environment
                Write-Host "`nLoaded configuration: $($selected.file)" -ForegroundColor Green

                # Check for invalid "True" project name and fix it
                if ($Config.project.name -eq $true) {
                    Write-Host "Detected invalid project name. Fixing configuration..." -ForegroundColor Yellow
                    $Config.project.name = $selected.project

                    # Save the fixed configuration
                    $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
                    Write-Host "Fixed configuration saved." -ForegroundColor Green
                }

                # Pause to show the message
                Pause-Script
            } catch {
                Write-Host "Error loading configuration: $_" -ForegroundColor Red
                Pause-Script
            }
        }
    } else {
        Write-Host "Invalid option. Please try again." -ForegroundColor Red
        Start-Sleep -Seconds 1
        Change-Environment
    }
}

# Create default configuration
function Create-DefaultConfig {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Environment
    )

    # Load requirements from JSON
    $requirements = Import-Requirements

    if ($null -eq $requirements) {
        Write-Host "Could not load requirements.json. Using default values." -ForegroundColor Yellow
        $serviceUser = "database-service"
        $serviceGroup = "database-service"
        $installDir = "/opt/database-service"
        $projectName = "database-service"
        $projectDesc = "Database Service for Project Tracker"
    } else {
        # Get user/group from requirements
        $serviceUser = $requirements.users.service_user.name
        $serviceGroup = $requirements.users.service_user.group
        $installDir = $requirements.directories.install_dir.path
        $projectName = $requirements.project_name -or "database-service"
        $projectDesc = $requirements.project_description -or "Database Service for Project Tracker"
        Write-Host "Loaded service user/group from requirements.json: $serviceUser - $serviceGroup" -ForegroundColor Green
    }

    # Prompt for project selection
    Write-Host "Select project type:" -ForegroundColor Yellow
    Write-Host "[1] Database Service" -ForegroundColor White
    Write-Host "[2] Git Dashboard" -ForegroundColor White
    Write-Host "[3] Custom Project" -ForegroundColor White

    $choice = Read-Host "Select project type (1-3)"

    switch ($choice) {
        "1" {
            $projectName = "database-service"
            $projectDesc = "Database Service for Project Tracker"
            $serviceUser = "database-service"
            $serviceGroup = "database-service"
            $installDir = "/opt/database-service"
            $dbName = "database_service"
        }
        "2" {
            $projectName = "git-dashboard"
            $projectDesc = "Git Dashboard Service"
            $serviceUser = "git-dashboard"
            $serviceGroup = "git-dashboard"
            $installDir = "/opt/git-dashboard"
            $dbName = "git_dashboard"
        }
        "3" {
            $projectName = Read-Host "Project name"
            if ([string]::IsNullOrWhiteSpace($projectName)) {
                $projectName = "custom-service"
            }

            $projectDesc = Read-Host "Project description"
            if ([string]::IsNullOrWhiteSpace($projectDesc)) {
                $projectDesc = "$projectName Service"
            }

            $serviceUser = Read-Host "Service user"
            if ([string]::IsNullOrWhiteSpace($serviceUser)) {
                $serviceUser = $projectName
            }

            $serviceGroup = Read-Host "Service group"
            if ([string]::IsNullOrWhiteSpace($serviceGroup)) {
                $serviceGroup = $projectName
            }

            $installDir = Read-Host "Install directory"
            if ([string]::IsNullOrWhiteSpace($installDir)) {
                $installDir = "/opt/$projectName"
            }

            $dbName = Read-Host "Database name"
            if ([string]::IsNullOrWhiteSpace($dbName)) {
                $dbName = $projectName.Replace("-", "_")
            }
        }
        default {
            Write-Host "Invalid choice. Using default Database Service settings." -ForegroundColor Yellow
            $projectName = "database-service"
            $projectDesc = "Database Service for Project Tracker"
            $dbName = "database_service"
        }
    }

    # Automatically detect SSH key path
    $sshKeyPath = Find-SSHKey
    if ([string]::IsNullOrEmpty($sshKeyPath)) {
        $sshKeyPath = Join-Path -Path $HOME -ChildPath ".ssh\id_rsa"
        Write-Host "No SSH key found, using default path: $sshKeyPath" -ForegroundColor Yellow
    }

    # Create default configuration
    $defaultConfig = @{
        environment = $Environment
        project = @{
            name = $projectName
            version = "1.0.0"
            build_dir = "build"
            install_dir = $installDir
            description = $projectDesc
        }
        ssh = @{
            host = "git.chcit.org"
            username = "btaylor-admin"
            port = 22
            key_path = $sshKeyPath
            sudo_no_passwd = "true"
        }
        db = @{
            host = "localhost"
            port = 5432
            name = $dbName
            user = "postgres"
            admin_db = "postgres"
        }
        service = @{
            name = $projectName
            description = $projectDesc
            user = $serviceUser
            group = $serviceGroup
        }
    }

    $script:Config = $defaultConfig

    # Create the configuration file
    if (-not (Test-Path -Path $ConfigDir)) {
        try {
            New-Item -Path $ConfigDir -ItemType Directory | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return
        }
    }

    # Use project name in the config filename
    $configFile = Join-Path -Path $ConfigDir -ChildPath "$($defaultConfig.project.name)-$Environment.json"
    $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8

    Write-Host "`nCreated default configuration at: $configFile" -ForegroundColor Green
    Write-Host "Please update this file with your specific settings if needed." -ForegroundColor Yellow
}

# Create a new service configuration
function New-ServiceConfig {
    param(
        [Parameter(Position = 0, Mandatory = $true)]
        [string]$Environment
    )

    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Create New Configuration                 " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue

    # Create config directory if it doesn't exist
    if (-not (Test-Path -Path $ConfigDir)) {
        try {
            New-Item -Path $ConfigDir -ItemType Directory | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return
        }
    }

    Write-Host "Environment: $Environment" -ForegroundColor Green

    # Load requirements from JSON file
    $requirements = Import-Requirements

    if ($null -eq $requirements) {
        Write-Host "Could not load requirements.json. Using default values." -ForegroundColor Yellow
        $serviceUser = "database-service"
        $serviceGroup = "database-service"
        $installDir = "/opt/database-service"
        $defaultProjectName = "database-service"
    } else {
        # Get user/group from requirements
        $serviceUser = $requirements.users.service_user.name
        $serviceGroup = $requirements.users.service_user.group
        $installDir = $requirements.directories.install_dir.path
        $defaultProjectName = $requirements.project_name -or "database-service"
        Write-Host "Loaded service user/group from requirements.json: $serviceUser - $serviceGroup" -ForegroundColor Green
    }

    # Prompt for project name
    $projectName = Read-Host "Project name [$defaultProjectName]"
    if ([string]::IsNullOrWhiteSpace($projectName)) { $projectName = $defaultProjectName }

    # Prompt for SSH configuration values
    Write-Host "`nPlease provide SSH configuration for $Environment environment:" -ForegroundColor Cyan

    # Get SSH server
    $defaultHost = "git.chcit.org"
    $sshHost = Read-Host "SSH Server hostname [$defaultHost]"
    if ([string]::IsNullOrWhiteSpace($sshHost)) { $sshHost = $defaultHost }

    # Get SSH username
    $defaultUser = "btaylor-admin"
    $sshUser = Read-Host "SSH Username [$defaultUser]"
    if ([string]::IsNullOrWhiteSpace($sshUser)) { $sshUser = $defaultUser }

    # Get SSH port
    $defaultPort = 22
    $sshPortStr = Read-Host "SSH Port [$defaultPort]"
    $sshPort = if ([string]::IsNullOrWhiteSpace($sshPortStr)) { $defaultPort } else { [int]$sshPortStr }

    # Get SSH key path - detect automatically first
    $detectedKeyPath = Find-SSHKey
    $defaultKeyPath = if ([string]::IsNullOrEmpty($detectedKeyPath)) {
        Join-Path -Path $HOME -ChildPath ".ssh\id_rsa"
    } else {
        $detectedKeyPath
    }

    $sshKeyPath = Read-Host "SSH Key path [$defaultKeyPath]"
    if ([string]::IsNullOrWhiteSpace($sshKeyPath)) { $sshKeyPath = $defaultKeyPath }

    # Create configuration object
    $defaultConfig = @{
        environment = $Environment
        project = @{
            name = $projectName
            version = "1.0.0"
            build_dir = "build"
            install_dir = $installDir
        }
        ssh = @{
            host = $sshHost
            username = $sshUser
            port = $sshPort
            key_path = $sshKeyPath
            sudo_no_passwd = "true"
        }
        db = @{
            host = "localhost"
            port = 5432
            name = "database_service"
            user = "postgres"
            admin_db = "postgres"
        }
        service = @{
            name = "database-service"
            description = "Database Service for Project Tracker"
            user = $serviceUser
            group = $serviceGroup
        }
    }

    $script:Config = $defaultConfig

    # Save configuration to file with project name prefix
    if (-not (Test-Path -Path $ConfigDir)) {
        try {
            New-Item -Path $ConfigDir -ItemType Directory | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return
        }
    }

    $configFile = Join-Path -Path $ConfigDir -ChildPath "$projectName-$Environment.json"
    $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8

    Write-Host "`nConfiguration created and saved to $configFile" -ForegroundColor Green
}

# Show configuration
function Show-Configuration {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Configuration Details                    " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "Environment: " -ForegroundColor Cyan -NoNewline
    switch ($DeploymentEnvironment) {
        "production" { Write-Host "PRODUCTION" -ForegroundColor Red }
        "testing" { Write-Host "TESTING" -ForegroundColor Yellow }
        "development" { Write-Host "DEVELOPMENT" -ForegroundColor Green }
        default { Write-Host $DeploymentEnvironment -ForegroundColor White }
    }

    # Check for empty SSH key path and attempt to detect it if empty
    if ($null -ne $Config -and $null -ne $Config.ssh -and [string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        Write-Host "`nDetecting SSH key path..." -ForegroundColor Yellow
        $detectedKeyPath = Find-SSHKey
        if (-not [string]::IsNullOrEmpty($detectedKeyPath)) {
            $Config.ssh.key_path = $detectedKeyPath
            Write-Host "Found SSH key at: $detectedKeyPath" -ForegroundColor Green

            # Save the updated configuration
            if (-not (Test-Path -Path $ConfigDir)) {
                try {
                    New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
                } catch {
                    Write-Host "`nFailed to create configuration directory: $_" -ForegroundColor Red
                    Pause-Script
                    return
                }
            }

            try {
                $configFile = Join-Path -Path $ConfigDir -ChildPath "$($Config.project.name)-$DeploymentEnvironment.json"
                $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
                Write-Host "Updated configuration with detected SSH key path" -ForegroundColor Green
            } catch {
                Write-Host "Failed to save configuration: $_" -ForegroundColor Red
            }
        }
    }

    # Display SSH configuration
    Write-Host "`nSSH Configuration:" -ForegroundColor Yellow
    Write-Host "  Host: $($Config.ssh.host)" -ForegroundColor White
    Write-Host "  Username: $($Config.ssh.username)" -ForegroundColor White
    Write-Host "  Port: $($Config.ssh.port)" -ForegroundColor White
    Write-Host "  Key Path: $($Config.ssh.key_path)" -ForegroundColor White
    Write-Host "  Passwordless Sudo: $($Config.ssh.sudo_no_passwd)" -ForegroundColor White

    # Display project configuration
    Write-Host "`nProject Configuration:" -ForegroundColor Yellow
    Write-Host "  Name: $($Config.project.name)" -ForegroundColor White
    Write-Host "  Version: $($Config.project.version)" -ForegroundColor White
    Write-Host "  Build Directory: $($Config.project.build_dir)" -ForegroundColor White
    Write-Host "  Install Directory: $($Config.project.install_dir)" -ForegroundColor White
    Write-Host "  Description: $($Config.project.description)" -ForegroundColor White

    # Display database configuration
    Write-Host "`nDatabase Configuration:" -ForegroundColor Yellow
    Write-Host "  Host: $($Config.db.host)" -ForegroundColor White
    Write-Host "  Port: $($Config.db.port)" -ForegroundColor White
    Write-Host "  Name: $($Config.db.name)" -ForegroundColor White
    Write-Host "  User: $($Config.db.user)" -ForegroundColor White
    Write-Host "  Admin Database: $($Config.db.admin_db)" -ForegroundColor White

    # Display service configuration
    Write-Host "`nService Configuration:" -ForegroundColor Yellow
    Write-Host "  Name: $($Config.service.name)" -ForegroundColor White
    Write-Host "  Description: $($Config.service.description)" -ForegroundColor White
    Write-Host "  User: $($Config.service.user)" -ForegroundColor White
    Write-Host "  Group: $($Config.service.group)" -ForegroundColor White

    Write-Host
    Write-Host "[1] Edit Configuration" -ForegroundColor White
    Write-Host "[2] Back to Main Menu" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-2)"

    switch ($choice) {
        "1" { Edit-Configuration }
        "2" { Return-ToMainMenu }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Show-Configuration
        }
    }
}

# Edit configuration
function Edit-Configuration {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "                Edit Configuration                     " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "[1] Basic Project Settings" -ForegroundColor White
    Write-Host "[2] Server Configuration" -ForegroundColor White
    Write-Host "[3] Database Configuration" -ForegroundColor White
    Write-Host "[4] Service User Configuration" -ForegroundColor White
    Write-Host "[5] Save Configuration" -ForegroundColor Green
    Write-Host "[6] Back to Configuration Menu" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-6)"

    switch ($choice) {
        "1" { Edit-ProjectSettings }
        "2" { Edit-ServerSettings }
        "3" { Edit-DatabaseSettings }
        "4" { Edit-ServiceSettings }
        "5" {
            # Save the configuration
            if (-not (Test-Path -Path $ConfigDir)) {
                try {
                    New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
                } catch {
                    Write-Host "`nFailed to create configuration directory: $_" -ForegroundColor Red
                    Pause-Script
                    return
                }
            }

            try {
                $configFile = Join-Path -Path $ConfigDir -ChildPath "$($Config.project.name)-$DeploymentEnvironment.json"
                $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8

                Write-Host "`nConfiguration saved successfully to $configFile." -ForegroundColor Green
            } catch {
                Write-Host "`nFailed to save configuration: $_" -ForegroundColor Red
            }

            Pause-Script
            Edit-Configuration
        }
        "6" { Return-ToMainMenu }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Edit-Configuration
        }
    }
}

# Edit project settings
function Edit-ProjectSettings {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Edit Project Settings                    " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "Current Project Settings:" -ForegroundColor Yellow
    Write-Host "  Name: $($Config.project.name)" -ForegroundColor White
    Write-Host "  Version: $($Config.project.version)" -ForegroundColor White
    Write-Host "  Build Directory: $($Config.project.build_dir)" -ForegroundColor White
    Write-Host "  Install Directory: $($Config.project.install_dir)" -ForegroundColor White
    Write-Host "  Description: $($Config.project.description)" -ForegroundColor White
    Write-Host

    # Edit project name
    $projectName = Read-Host "Project name [$($Config.project.name)]"
    if (-not [string]::IsNullOrWhiteSpace($projectName)) {
        $Config.project.name = $projectName
    }

    # Edit project version
    $projectVersion = Read-Host "Project version [$($Config.project.version)]"
    if (-not [string]::IsNullOrWhiteSpace($projectVersion)) {
        $Config.project.version = $projectVersion
    }

    # Edit build directory
    $buildDir = Read-Host "Build directory [$($Config.project.build_dir)]"
    if (-not [string]::IsNullOrWhiteSpace($buildDir)) {
        $Config.project.build_dir = $buildDir
    }

    # Edit install directory
    $installDir = Read-Host "Install directory [$($Config.project.install_dir)]"
    if (-not [string]::IsNullOrWhiteSpace($installDir)) {
        $Config.project.install_dir = $installDir
    }

    # Edit project description
    $projectDescription = Read-Host "Project description [$($Config.project.description)]"
    if (-not [string]::IsNullOrWhiteSpace($projectDescription)) {
        $Config.project.description = $projectDescription
    }

    Write-Host "`nProject settings updated" -ForegroundColor Green
    Pause-Script
    Edit-Configuration
}

# Edit server settings
function Edit-ServerSettings {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Edit Server Settings                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # List of known servers
    $knownServers = @(
        @{name="Git Dashboard Server"; host="git.chcit.org"; description="Git server and dashboard"},
        @{name="Project Tracker Server"; host="project-tracker.chcit.org"; description="Main application server"}
    )

    Write-Host "Current Server Settings:" -ForegroundColor Yellow
    Write-Host "  Host: $($Config.ssh.host)" -ForegroundColor White
    Write-Host "  Username: $($Config.ssh.username)" -ForegroundColor White
    Write-Host "  Port: $($Config.ssh.port)" -ForegroundColor White
    Write-Host "  Key Path: $($Config.ssh.key_path)" -ForegroundColor White
    Write-Host "  Passwordless Sudo: $($Config.ssh.sudo_no_passwd)" -ForegroundColor White

    # Show known servers
    Write-Host "Known Servers:" -ForegroundColor Yellow
    for ($i=0; $i -lt $knownServers.Count; $i++) {
        Write-Host "  [$($i+1)] $($knownServers[$i].name) - $($knownServers[$i].host)" -ForegroundColor White
    }
    Write-Host "  [C] Custom Server" -ForegroundColor White

    $choice = Read-Host "`nSelect a server or 'C' for custom"

    if ($choice -match '^\d+$' -and [int]$choice -ge 1 -and [int]$choice -le $knownServers.Count) {
        $selectedServer = $knownServers[[int]$choice-1]
        $Config.ssh.host = $selectedServer.host
        Write-Host "Selected $($selectedServer.name) ($($selectedServer.host))" -ForegroundColor Green
    } elseif ($choice -eq "C" -or $choice -eq "c") {
        # Custom server entry
        $sshHost = Read-Host "SSH Server hostname [$($Config.ssh.host)]"
        if (-not [string]::IsNullOrWhiteSpace($sshHost)) {
            $Config.ssh.host = $sshHost
        }
    } else {
        Write-Host "Invalid selection. Using existing server settings." -ForegroundColor Yellow
    }

    # Edit username
    $sshUser = Read-Host "SSH Username [$($Config.ssh.username)]"
    if (-not [string]::IsNullOrWhiteSpace($sshUser)) {
        $Config.ssh.username = $sshUser
    }

    # Edit port
    $sshPortStr = Read-Host "SSH Port [$($Config.ssh.port)]"
    if (-not [string]::IsNullOrWhiteSpace($sshPortStr) -and $sshPortStr -match '^\d+$') {
        $Config.ssh.port = [int]$sshPortStr
    }

    # Edit key path
    $sshKeyPath = Read-Host "SSH Key path [$($Config.ssh.key_path)]"
    if (-not [string]::IsNullOrWhiteSpace($sshKeyPath)) {
        # Verify the key exists
        try {
            if (Test-Path -Path $sshKeyPath) {
                $Config.ssh.key_path = $sshKeyPath
            } else {
                Write-Host "Warning: SSH key path does not exist. Using existing path." -ForegroundColor Yellow
            }
        } catch {
            Write-Host "Error checking SSH key path: $_" -ForegroundColor Red
        }
    } else {
        # If empty, attempt to auto-detect
        $detectedKeyPath = Find-SSHKey
        if (-not [string]::IsNullOrEmpty($detectedKeyPath)) {
            $Config.ssh.key_path = $detectedKeyPath
            Write-Host "Found SSH key at: $detectedKeyPath" -ForegroundColor Green
        }
    }

    # Edit sudo setting
    $sudoNoPasswd = Read-Host "Passwordless Sudo (true/false) [$($Config.ssh.sudo_no_passwd)]"
    if ($sudoNoPasswd -eq "true" -or $sudoNoPasswd -eq "false") {
        $Config.ssh.sudo_no_passwd = $sudoNoPasswd
    }

    Write-Host "`nServer settings updated" -ForegroundColor Green
    Pause-Script
    Edit-Configuration
}

# Edit database settings
function Edit-DatabaseSettings {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "             Edit Database Settings                    " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Define known database configurations
    $knownDatabases = @(
        @{name="Git Dashboard DB"; host="localhost"; port=5432; dbname="git_dashboard"; user="postgres"; admin_db="postgres"; description="Git Dashboard PostgreSQL Database"},
        @{name="Project Tracker DB"; host="localhost"; port=5432; dbname="project_tracker"; user="postgres"; admin_db="postgres"; description="Project Tracker PostgreSQL Database"}
    )

    Write-Host "Current Database Settings:" -ForegroundColor Yellow
    Write-Host "  Host: $($Config.db.host)" -ForegroundColor White
    Write-Host "  Port: $($Config.db.port)" -ForegroundColor White
    Write-Host "  Name: $($Config.db.name)" -ForegroundColor White
    Write-Host "  User: $($Config.db.user)" -ForegroundColor White
    Write-Host "  Admin Database: $($Config.db.admin_db)" -ForegroundColor White
    Write-Host

    # Show known databases
    Write-Host "Known Database Configurations:" -ForegroundColor Yellow
    for ($i=0; $i -lt $knownDatabases.Count; $i++) {
        Write-Host "  [$($i+1)] $($knownDatabases[$i].name) - $($knownDatabases[$i].description)" -ForegroundColor White
    }
    Write-Host "  [C] Custom Database" -ForegroundColor White

    $choice = Read-Host "`nSelect a database configuration or 'C' for custom"

    if ($choice -match '^\d+$' -and [int]$choice -ge 1 -and [int]$choice -le $knownDatabases.Count) {
        $selectedDB = $knownDatabases[[int]$choice-1]
        $Config.db.host = $selectedDB.host
        $Config.db.port = $selectedDB.port
        $Config.db.name = $selectedDB.dbname
        $Config.db.user = $selectedDB.user
        $Config.db.admin_db = $selectedDB.admin_db
        Write-Host "Selected $($selectedDB.name)" -ForegroundColor Green
    } elseif ($choice -eq "C" -or $choice -eq "c") {
        # Custom database entry
        $dbHost = Read-Host "Database Host [$($Config.db.host)]"
        if (-not [string]::IsNullOrWhiteSpace($dbHost)) {
            $Config.db.host = $dbHost
        }

        $dbPortStr = Read-Host "Database Port [$($Config.db.port)]"
        if (-not [string]::IsNullOrWhiteSpace($dbPortStr) -and $dbPortStr -match '^\d+$') {
            $Config.db.port = [int]$dbPortStr
        }

        $dbName = Read-Host "Database Name [$($Config.db.name)]"
        if (-not [string]::IsNullOrWhiteSpace($dbName)) {
            $Config.db.name = $dbName
        }

        $dbUser = Read-Host "Database User [$($Config.db.user)]"
        if (-not [string]::IsNullOrWhiteSpace($dbUser)) {
            $Config.db.user = $dbUser
        }

        $adminDB = Read-Host "Admin Database [$($Config.db.admin_db)]"
        if (-not [string]::IsNullOrWhiteSpace($adminDB)) {
            $Config.db.admin_db = $adminDB
        }
    } else {
        Write-Host "Invalid selection. Using existing database settings." -ForegroundColor Yellow
    }

    Write-Host "`nDatabase settings updated" -ForegroundColor Green
    Pause-Script
    Edit-Configuration
}

# Edit service settings
function Edit-ServiceSettings {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "             Edit Service Settings                     " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Define known service configurations
    $knownServices = @(
        @{name="Git Dashboard"; service_name="git-dashboard"; user="git-dashboard"; group="git-dashboard"; description="Git Dashboard Service"},
        @{name="Database Service"; service_name="database-service"; user="database-service"; group="database-service"; description="Database Service for Project Tracker"}
    )

    Write-Host "Current Service Settings:" -ForegroundColor Yellow
    Write-Host "  Name: $($Config.service.name)" -ForegroundColor White
    Write-Host "  Description: $($Config.service.description)" -ForegroundColor White
    Write-Host "  User: $($Config.service.user)" -ForegroundColor White
    Write-Host "  Group: $($Config.service.group)" -ForegroundColor White
    Write-Host

    # Show known services
    Write-Host "Known Service Configurations:" -ForegroundColor Yellow
    for ($i=0; $i -lt $knownServices.Count; $i++) {
        Write-Host "  [$($i+1)] $($knownServices[$i].name) - $($knownServices[$i].description)" -ForegroundColor White
    }
    Write-Host "  [C] Custom Service" -ForegroundColor White

    $choice = Read-Host "`nSelect a service configuration or 'C' for custom"

    if ($choice -match '^\d+$' -and [int]$choice -ge 1 -and [int]$choice -le $knownServices.Count) {
        $selectedService = $knownServices[[int]$choice-1]
        $Config.service.name = $selectedService.service_name
        $Config.service.description = $selectedService.description
        $Config.service.user = $selectedService.user
        $Config.service.group = $selectedService.group
        Write-Host "Selected $($selectedService.name)" -ForegroundColor Green
    } elseif ($choice -eq "C" -or $choice -eq "c") {
        # Custom service entry
        $serviceName = Read-Host "Service Name [$($Config.service.name)]"
        if (-not [string]::IsNullOrWhiteSpace($serviceName)) {
            $Config.service.name = $serviceName
        }

        $serviceDesc = Read-Host "Service Description [$($Config.service.description)]"
        if (-not [string]::IsNullOrWhiteSpace($serviceDesc)) {
            $Config.service.description = $serviceDesc
        }

        $serviceUser = Read-Host "Service User [$($Config.service.user)]"
        if (-not [string]::IsNullOrWhiteSpace($serviceUser)) {
            $Config.service.user = $serviceUser
        }

        $serviceGroup = Read-Host "Service Group [$($Config.service.group)]"
        if (-not [string]::IsNullOrWhiteSpace($serviceGroup)) {
            $Config.service.group = $serviceGroup
        }
    } else {
        Write-Host "Invalid selection. Using existing service settings." -ForegroundColor Yellow
    }

    Write-Host "`nService settings updated" -ForegroundColor Green
    Pause-Script
    Edit-Configuration
}

# SSH Configuration and testing submenu
function Configure-SSH {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "             SSH Configuration and Testing             " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Display current SSH settings if available
    if ($null -ne $Config -and $null -ne $Config.ssh) {
        Write-Host "Current SSH Configuration:" -ForegroundColor Yellow
        Write-Host "Host: $($Config.ssh.host)" -ForegroundColor Cyan
        Write-Host "Username: $($Config.ssh.username)" -ForegroundColor Cyan
        Write-Host "Port: $($Config.ssh.port)" -ForegroundColor Cyan

        if ([string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
            Write-Host "Key Path: Not configured" -ForegroundColor Red
        } else {
            $keyStatus = if (Test-Path -Path $Config.ssh.key_path) { "Found" } else { "Not Found" }
            Write-Host "Key Path: $($Config.ssh.key_path) ($keyStatus)" -ForegroundColor White
        }
    } else {
        Write-Host "No SSH configuration loaded." -ForegroundColor Red
    }

    Write-Host
    Write-Host "[1] Configure SSH Settings" -ForegroundColor White
    Write-Host "[2] Test SSH Connection" -ForegroundColor White
    Write-Host "[3] Manage SSH Keys" -ForegroundColor White
    Write-Host "[4] Return to Main Menu" -ForegroundColor White

    Write-Host
    $choice = Read-Host "Select an option (1-4)"

    # Check if the choice is a valid integer
    [int]$choiceNum = 0
    $validInt = [int]::TryParse($choice, [ref]$choiceNum)

    if ($validInt) {
        switch ($choiceNum) {
            1 { Configure-SSHSettings }
            2 { Test-SSHConnection }
            3 { Set-SSHKeys }
            4 { Return-ToMainMenu }
            default {
                Write-Host "Invalid option. Please try again." -ForegroundColor Red
                Start-Sleep -Seconds 1
                Configure-SSH
            }
        }
    } else {
        Write-Host "Invalid option. Please try again." -ForegroundColor Red
        Start-Sleep -Seconds 1
        Configure-SSH
    }
}

# Configure SSH Settings
function Configure-SSHSettings {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Configure SSH Settings                 " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if we have a configuration loaded
    if ($null -eq $Config) {
        Write-Host "No configuration loaded. Please set up an environment first." -ForegroundColor Red
        Pause-Script
        return
    }

    # Initialize SSH config if not present
    if ($null -eq $Config.ssh) {
        $Config.ssh = @{}
    }

    # Get current values or defaults
    $sshHost = if ($Config.ssh.host) { $Config.ssh.host } else { "" }
    $sshUser = if ($Config.ssh.username) { $Config.ssh.username } else { "btaylor-admin" }
    $sshPort = if ($Config.ssh.port) { $Config.ssh.port } else { 22 }
    $sshKeyPath = if ($Config.ssh.key_path) { $Config.ssh.key_path } else { "" }

    # Display CHCIT servers for selection
    Write-Host "Known CHCIT Servers:" -ForegroundColor Yellow
    Write-Host "[1] git.chcit.org (***********)" -ForegroundColor White
    Write-Host "[2] project-tracker.chcit.org (***********)" -ForegroundColor White
    Write-Host "[3] Custom Server" -ForegroundColor White

    $serverChoice = Read-Host "Select server or enter number (1-3)"

    switch ($serverChoice) {
        "1" { $sshHost = "git.chcit.org" }
        "2" { $sshHost = "project-tracker.chcit.org" }
        "3" { $sshHost = Read-Host "Enter SSH host address" }
        default {
            if (-not [string]::IsNullOrWhiteSpace($serverChoice)) {
                $sshHost = $serverChoice
            } else {
                $sshHost = Read-Host "Enter SSH host address"
            }
        }
    }

    # Get username with suggestion
    Write-Host
    Write-Host "Suggested username for CHCIT servers: btaylor-admin" -ForegroundColor Yellow
    $userInput = Read-Host "Enter SSH username [$sshUser]"
    if (-not [string]::IsNullOrWhiteSpace($userInput)) {
        $sshUser = $userInput
    }

    # Get port
    $portInput = Read-Host "Enter SSH port [$sshPort]"
    if (-not [string]::IsNullOrWhiteSpace($portInput)) {
        $sshPort = [int]$portInput
    }

    # Handle SSH key path
    Write-Host
    Write-Host "SSH Key Options:" -ForegroundColor Yellow
    Write-Host "[1] Detect SSH keys" -ForegroundColor White
    Write-Host "[2] Enter Custom Key Path" -ForegroundColor White
    Write-Host "[3] Create a new key" -ForegroundColor White
    $keyOption = Read-Host "Select option (1-3)"

    switch ($keyOption) {
        "1" {
            $detectedKey = Find-SSHKey
            if ($detectedKey) {
                Write-Host "Found SSH key: $detectedKey" -ForegroundColor Green
                $sshKeyPath = $detectedKey
            } else {
                Write-Host "No SSH keys found. Would you like to create one?" -ForegroundColor Yellow
                $createKey = Read-Host "Create new SSH key? (y/n)"
                if ($createKey -eq "y") {
                    New-SSHKey
                    $sshKeyPath = Find-SSHKey
                }
            }
        }
        "2" {
            $keyInput = Read-Host "Enter SSH key path [$sshKeyPath]"
            if (-not [string]::IsNullOrWhiteSpace($keyInput)) {
                $sshKeyPath = $keyInput
            }
        }
        "3" {
            New-SSHKey
            $sshKeyPath = Find-SSHKey
        }
        default {
            Write-Host "Invalid option. Using existing path." -ForegroundColor Yellow
        }
    }

    # Update configuration
    $Config.ssh.host = $sshHost
    $Config.ssh.username = $sshUser
    $Config.ssh.port = $sshPort
    $Config.ssh.key_path = $sshKeyPath

    # Save configuration
    if ($Config.project -and $Config.project.name -and $DeploymentEnvironment) {
        if (-not (Test-Path -Path $ConfigDir)) {
            try {
                New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
            } catch {
                Write-Host "`nFailed to create configuration directory: $_" -ForegroundColor Red
                Pause-Script
                return
            }
        }

        try {
            $configFile = Join-Path -Path $ConfigDir -ChildPath "$($Config.project.name)-$DeploymentEnvironment.json"
            $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8

            Write-Host "`nSSH configuration saved successfully to $configFile." -ForegroundColor Green
        } catch {
            Write-Host "`nFailed to save configuration: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "`nConfiguration updated but not saved. No project or environment set." -ForegroundColor Yellow
    }

    # Ask if user wants to test the connection
    Write-Host
    $testConnection = Read-Host "Would you like to test the SSH connection now? (y/n)"
    if ($testConnection -eq "y") {
        Test-SSHConnection
    } else {
        Pause-Script
    }
}

# Helper function to extract package names from requirements.json
function Extract-RequiredPackages {
    param (
        [Parameter(Mandatory = $true)]
        [object]$Requirements
    )

    $packages = @()
    $buildTools = @()
    $libraries = @()
    $pythonPackages = @()
    $serviceUsers = @()
    $directories = @()

    # Extract compiler packages
    if ($Requirements.compiler) {
        foreach ($compiler in $Requirements.compiler.PSObject.Properties) {
            if ($compiler.Value.package) {
                $packages += $compiler.Value.package
            }
        }
    }

    # Extract build tool packages
    if ($Requirements.build_tools) {
        foreach ($tool in $Requirements.build_tools.PSObject.Properties) {
            if ($tool.Value.package) {
                $buildTools += $tool.Value.package
            }
        }
    }

    # Extract library packages
    if ($Requirements.libraries) {
        foreach ($lib in $Requirements.libraries.PSObject.Properties) {
            if ($lib.Value.package) {
                $libraries += $lib.Value.package
            }
        }
    }

    # Extract system packages
    if ($Requirements.system) {
        foreach ($sys in $Requirements.system.PSObject.Properties) {
            if ($sys.Value.package) {
                $packages += $sys.Value.package
            }
        }
    }

    # Extract service users
    if ($Requirements.users) {
        foreach ($user in $Requirements.users.PSObject.Properties) {
            if ($user.Value.name) {
                $info = @{
                    name = $user.Value.name
                    group = $user.Value.group
                }
                $serviceUsers += $info
            }
        }
    }

    # Extract directory information
    if ($Requirements.directories) {
        foreach ($dir in $Requirements.directories.PSObject.Properties) {
            if ($dir.Value.path) {
                $info = @{
                    path = $dir.Value.path
                    owner = if ($dir.Value.permission) { $dir.Value.permission.Split(':')[0] } else { $null }
                    group = if ($dir.Value.permission) { $dir.Value.permission.Split(':')[1] } else { $null }
                    permissions = $dir.Value.mode
                }
                $directories += $info
            }
        }
    }

    return @{
        packages = $packages
        build_tools = $buildTools
        libraries = $libraries
        python_packages = $pythonPackages
        users = $serviceUsers
        directories = $directories
    }
}

# Helper function to generate default dependencies based on project type
function Get-DefaultDependencies {
    param (
        [Parameter(Mandatory = $false)]
        [string]$ProjectType = "database-service"
    )

    $defaultDependencies = @{}

    # Default packages for database-service project on Ubuntu
    if ($ProjectType -eq "database-service") {
        $defaultDependencies = @{
            packages = @("postgresql", "postgresql-contrib", "nginx")
            build_tools = @("build-essential", "cmake")
            libraries = @("libpq-dev", "libboost-all-dev", "libssl-dev", "nlohmann-json3-dev")
            python_packages = @()
            users = @(
                @{
                    name = "database-service"
                    group = "database-service"
                }
            )
            directories = @(
                @{
                    path = "/opt/database-service"
                    owner = "database-service"
                    group = "database-service"
                    permissions = "755"
                },
                @{
                    path = "/opt/database-service/logs"
                    owner = "database-service"
                    group = "database-service"
                    permissions = "775"
                }
            )
        }
    }
    # Could add other project types here in the future

    return $defaultDependencies
}

# Helper function to scan source code for dependencies
function Scan-SourceForDependencies {
    param (
        [Parameter(Mandatory = $true)]
        [string]$SourceRoot
    )

    Write-Host "Scanning source code for dependencies in $SourceRoot..." -ForegroundColor Yellow

    # Initialize the dependency collections
    $detectedDependencies = @{
        packages = @()
        build_tools = @()
        libraries = @()
        python_packages = @()
        users = @()
        directories = @()
    }

    # Dependencies mapping based on patterns found in code
    $dependencyPatterns = @{
        "<pqxx/" = @{type = "libraries"; name = "libpq-dev"}
        "<postgresql/" = @{type = "libraries"; name = "libpq-dev"}
        "<boost/" = @{type = "libraries"; name = "libboost-all-dev"}
        "<boost/asio" = @{type = "libraries"; name = "libboost-system-dev"}
        "<boost/beast" = @{type = "libraries"; name = "libboost-system-dev"}
        "<boost/program_options" = @{type = "libraries"; name = "libboost-program-options-dev"}
        "<openssl/" = @{type = "libraries"; name = "libssl-dev"}
        "<nlohmann/json" = @{type = "libraries"; name = "nlohmann-json3-dev"}
        "#include.*<json" = @{type = "libraries"; name = "nlohmann-json3-dev"}
        "<curl/curl.h>" = @{type = "libraries"; name = "libcurl4-openssl-dev"}
        "<thread>" = @{type = "libraries"; name = "libpthread-stubs0-dev"}
        "<coroutine>" = @{type = "libraries"; name = "libstdc++-10-dev"}
        "import.*<coroutine>" = @{type = "libraries"; name = "libstdc++-10-dev"}
        "<filesystem>" = @{type = "libraries"; name = "libstdc++-10-dev"}
    }

    # Scan header includes
    $cppFiles = Get-ChildItem -Path $SourceRoot -Include @("*.cpp", "*.h", "*.hpp", "*.cc") -Recurse -ErrorAction SilentlyContinue

    if ($cppFiles.Count -gt 0) {
        Write-Host "  Scanning $($cppFiles.Count) C++ source files for dependencies..." -ForegroundColor Green

        foreach ($file in $cppFiles) {
            $content = Get-Content -Path $file.FullName -Raw -ErrorAction SilentlyContinue

            if ($content) {
                foreach ($pattern in $dependencyPatterns.Keys) {
                    if ($content -match $pattern) {
                        $dependency = $dependencyPatterns[$pattern]
                        $depType = $dependency.type
                        $depName = $dependency.name

                        if (-not $detectedDependencies[$depType].Contains($depName)) {
                            Write-Host "  Detected dependency: $depName in $($file.Name)" -ForegroundColor Green
                            $detectedDependencies[$depType] += $depName
                        }
                    }
                }
            }
        }
    }

    # Check for CMake usage
    $cmakeFiles = Get-ChildItem -Path $SourceRoot -Filter "CMakeLists.txt" -Recurse -ErrorAction SilentlyContinue
    if ($cmakeFiles.Count -gt 0) {
        Write-Host "  Detected CMake build system" -ForegroundColor Green
        $detectedDependencies.build_tools += "cmake"
    }

    # Always add build-essential for C++ projects
    $detectedDependencies.build_tools += "build-essential"

    # For modern C++ with modules support, add specific GCC version
    $detectedDependencies.packages += "g++-10"

    # Add PostgreSQL client and development libraries for database connections
    $detectedDependencies.packages += "postgresql-client"
    $detectedDependencies.libraries += "libpqxx-dev"

    # Check for database usage indicators
    if ($detectedDependencies.libraries -contains "libpq-dev") {
        if (-not $detectedDependencies.packages.Contains("postgresql")) {
            $detectedDependencies.packages += "postgresql"
            $detectedDependencies.packages += "postgresql-contrib"
        }
    }

    # Add service user for database components
    if ($detectedDependencies.packages -contains "postgresql") {
        $projectName = (Split-Path -Path $SourceRoot -Leaf).ToLower()
        if (-not $projectName -or $projectName -eq "scripts") {
            $projectName = (Split-Path -Path (Split-Path -Path $SourceRoot -Parent) -Leaf).ToLower()
        }

        # Sanitize projectName for use as a service name
        $projectName = $projectName -replace "[^a-zA-Z0-9-]", "-"

        Write-Host "  Adding service user: $projectName" -ForegroundColor Green
        $detectedDependencies.users += @{
            name = $projectName
            group = $projectName
        }

        # Add standard service directories
        $detectedDependencies.directories += @{
            path = "/opt/$projectName"
            owner = $projectName
            group = $projectName
            permissions = "755"
        }

        $detectedDependencies.directories += @{
            path = "/opt/$projectName/logs"
            owner = $projectName
            group = $projectName
            permissions = "775"
        }
    }

    # Deduplicate entries
    $detectedDependencies.packages = $detectedDependencies.packages | Select-Object -Unique
    $detectedDependencies.build_tools = $detectedDependencies.build_tools | Select-Object -Unique
    $detectedDependencies.libraries = $detectedDependencies.libraries | Select-Object -Unique

    Write-Host "Dependency scanning complete!" -ForegroundColor Green
    Write-Host "Found $($detectedDependencies.packages.Count) system packages, $($detectedDependencies.build_tools.Count) build tools, and $($detectedDependencies.libraries.Count) libraries." -ForegroundColor Green

    return $detectedDependencies
}

# Install dependencies on remote server
function Install-Dependencies {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Install Dependencies                   " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if (-not $Config -or -not $Config.ssh) {
        Write-Host "No SSH configuration loaded. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "Checking SSH connection..." -ForegroundColor Yellow
    $sshTest = Connect-RemoteServer

    if (-not $sshTest) {
        Write-Host "SSH connection failed. Cannot install dependencies." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "SSH connection successful." -ForegroundColor Green

    # Read requirements.json to get dependencies
    $requirementsPath = Join-Path -Path $PSScriptRoot -ChildPath "requirements.json"
    if (-not (Test-Path -Path $requirementsPath)) {
        Write-Host "Requirements file not found at: $requirementsPath" -ForegroundColor Red
        Write-Host

        # Present a menu to search for requirements.json or return to main menu
        Write-Host "Options:" -ForegroundColor Yellow
        Write-Host "[1] Search source code directory for requirements.json" -ForegroundColor White
        Write-Host "[2] Scan source code for dependencies" -ForegroundColor White
        Write-Host "[3] Return to main menu" -ForegroundColor White
        Write-Host

        $choice = Read-Host "Select an option (1-3)"

        switch ($choice) {
            "1" {
                # Get the parent directory of the script directory (source code root)
                $sourceRoot = Split-Path -Path $PSScriptRoot -Parent
                Write-Host "Searching for requirements.json in $sourceRoot..." -ForegroundColor Yellow

                # Use Get-ChildItem to search for requirements.json recursively
                $foundFiles = Get-ChildItem -Path $sourceRoot -Filter "requirements.json" -Recurse -ErrorAction SilentlyContinue

                if ($foundFiles.Count -gt 0) {
                    Write-Host "Found $($foundFiles.Count) requirements.json files:" -ForegroundColor Green

                    # Display the found files with indices
                    for ($i = 0; $i -lt $foundFiles.Count; $i++) {
                        Write-Host "[$($i+1)] $($foundFiles[$i].FullName)" -ForegroundColor White
                    }

                    Write-Host "[0] Cancel" -ForegroundColor White
                    Write-Host

                    $fileChoice = Read-Host "Select a file to use (0-$($foundFiles.Count))"

                    if ($fileChoice -match '^\d+$' -and [int]$fileChoice -gt 0 -and [int]$fileChoice -le $foundFiles.Count) {
                        $selectedFile = $foundFiles[[int]$fileChoice - 1].FullName
                        Write-Host "Using requirements file: $selectedFile" -ForegroundColor Green

                        # Copy the file to the script directory for future use
                        $targetPath = Join-Path -Path $PSScriptRoot -ChildPath "requirements.json"
                        Copy-Item -Path $selectedFile -Destination $targetPath -Force
                        Write-Host "Copied requirements file to script directory for future use." -ForegroundColor Green
                    } else {
                        Write-Host "Operation cancelled. Returning to main menu." -ForegroundColor Yellow
                        Return-ToMainMenu
                        return
                    }
                } else {
                    Write-Host "No requirements.json files found in the source code directory." -ForegroundColor Red
                    Return-ToMainMenu
                    return
                }
            }
            "2" {
                # Get the parent directory of the script directory (source code root)
                $sourceRoot = Split-Path -Path $PSScriptRoot -Parent

                # Check if the source directory exists
                if (-not (Test-Path -Path $sourceRoot)) {
                    Write-Host "Source directory not found at: $sourceRoot" -ForegroundColor Red
                    Return-ToMainMenu
                    return
                }

                Write-Host "Scanning source code for dependencies instead of using a requirements file..." -ForegroundColor Yellow

                # Scan source code for dependencies
                $requirements = Scan-SourceForDependencies -SourceRoot $sourceRoot

                # Create a new requirements.json file for future use
                try {
                    # Basic structure for requirements.json
                    $defaultRequirements = @{
                        compiler = @{
                            gcc = @{
                                minimum_version = "9.0.0"
                                required = $true
                                description = "GCC compiler with C++17 support"
                                package = "g++"
                            }
                        }
                        build_tools = @{}
                        libraries = @{}
                        system = @{}
                        directories = @{}
                        users = @{}
                    }

                    # Add detected build tools
                    foreach ($tool in $requirements.build_tools) {
                        $toolName = $tool -replace '-', '_'
                        $defaultRequirements.build_tools[$toolName] = @{
                            required = $true
                            description = "Detected build tool: $tool"
                            package = $tool
                        }
                    }

                    # Add detected libraries
                    foreach ($lib in $requirements.libraries) {
                        $libName = ($lib -replace 'lib', '' -replace '-dev', '' -replace '-', '_')
                        $defaultRequirements.libraries[$libName] = @{
                            required = $true
                            description = "Detected library: $lib"
                            package = $lib
                        }
                    }

                    # Add detected system packages
                    foreach ($pkg in $requirements.packages) {
                        $pkgName = $pkg -replace '-', '_'
                        $defaultRequirements.system[$pkgName] = @{
                            required = $true
                            description = "Detected system package: $pkg"
                            package = $pkg
                        }
                    }

                    # Add detected users
                    foreach ($user in $requirements.users) {
                        $userName = $user.name
                        $defaultRequirements.users["${userName}_user"] = @{
                            name = $userName
                            group = $user.group
                        }
                    }

                    # Add detected directories
                    foreach ($dir in $requirements.directories) {
                        $dirName = ($dir.path -replace '/', '_' -replace '^_opt_', '' -replace '^_', '')
                        $defaultRequirements.directories[$dirName] = @{
                            path = $dir.path
                            permission = "$($dir.owner):$($dir.group)"
                            mode = $dir.permissions
                        }
                    }

                    # Convert to JSON and save
                    $defaultRequirementsJson = $defaultRequirements | ConvertTo-Json -Depth 10
                    $targetPath = Join-Path -Path $PSScriptRoot -ChildPath "requirements.json"
                    Set-Content -Path $targetPath -Value $defaultRequirementsJson

                    Write-Host "Created requirements.json file from source code analysis at: $targetPath" -ForegroundColor Green
                }
                catch {
                    Write-Host "Warning: Failed to create requirements.json file: $_" -ForegroundColor Yellow
                    Write-Host "Continuing with in-memory dependency analysis." -ForegroundColor Yellow
                }
            }
            default {
                Write-Host "Returning to main menu." -ForegroundColor Yellow
                Return-ToMainMenu
                return
            }
        }
    }

    try {
        if ($choice -eq "2") {
            # We already generated the requirements in the previous step
            # Just continue with the existing $requirements variable
        } else {
            $requirementsJson = Get-Content -Path $requirementsPath -Raw | ConvertFrom-Json
            $requirements = Extract-RequiredPackages -Requirements $requirementsJson
        }

        # Display all dependencies that will be installed
        Write-Host "`nThe following dependencies will be installed on $($Config.ssh.host):" -ForegroundColor Yellow

        # Display packages
        Write-Host "`nSystem Packages:" -ForegroundColor Cyan
        if ($requirements.packages -and $requirements.packages.Count -gt 0) {
            foreach ($package in $requirements.packages) {
                Write-Host "  - $package" -ForegroundColor White
            }
        } else {
            Write-Host "  None specified" -ForegroundColor White
        }

        # Display build tools
        Write-Host "`nBuild Tools:" -ForegroundColor Cyan
        if ($requirements.build_tools -and $requirements.build_tools.Count -gt 0) {
            foreach ($tool in $requirements.build_tools) {
                Write-Host "  - $tool" -ForegroundColor White
            }
        } else {
            Write-Host "  None specified" -ForegroundColor White
        }

        # Display libraries
        Write-Host "`nLibraries:" -ForegroundColor Cyan
        if ($requirements.libraries -and $requirements.libraries.Count -gt 0) {
            foreach ($lib in $requirements.libraries) {
                Write-Host "  - $lib" -ForegroundColor White
            }
        } else {
            Write-Host "  None specified" -ForegroundColor White
        }

        # Display Python packages if any
        if ($requirements.python_packages -and $requirements.python_packages.Count -gt 0) {
            Write-Host "`nPython Packages:" -ForegroundColor Cyan
            foreach ($pyPackage in $requirements.python_packages) {
                Write-Host "  - $pyPackage" -ForegroundColor White
            }
        }

        # Display user and permissions if any
        if ($requirements.users -and $requirements.users.Count -gt 0) {
            Write-Host "`nService User/Group:" -ForegroundColor Cyan
            foreach ($user in $requirements.users) {
                Write-Host "  - User: $($user.name), Group: $($user.group)" -ForegroundColor White
            }
        }

        # Display directory permissions if any
        if ($requirements.directories -and $requirements.directories.Count -gt 0) {
            Write-Host "`nDirectory Permissions:" -ForegroundColor Cyan
            foreach ($dir in $requirements.directories) {
                $permInfo = "$($dir.path)"
                if ($dir.owner) { $permInfo += " (Owner: $($dir.owner)" }
                if ($dir.group) { $permInfo += ", Group: $($dir.group)" }
                if ($dir.permissions) { $permInfo += ", Permissions: $($dir.permissions)" }
                if ($dir.owner) { $permInfo += ")" }
                Write-Host "  - $permInfo" -ForegroundColor White
            }
        }

        # Ask for confirmation
        Write-Host
        $confirm = Read-Host "Proceed with installation? (y/n)"

        if ($confirm -ne "y") {
            Write-Host "Installation cancelled. Returning to main menu." -ForegroundColor Yellow
            Return-ToMainMenu
            return
        }

        # Proceed with installation
        Write-Host "Starting dependency installation..." -ForegroundColor Yellow

        # Update package repositories
        Write-Host "Updating package repositories..." -ForegroundColor Yellow
        Invoke-RemoteCommand -Command "sudo apt-get update"

        # Install system packages
        if ($requirements.packages -and $requirements.packages.Count -gt 0) {
            $packageList = $requirements.packages -join " "
            Write-Host "Installing system packages..." -ForegroundColor Yellow
            Invoke-RemoteCommand -Command "sudo apt-get install -y $packageList"
        }

        # Install build tools
        if ($requirements.build_tools -and $requirements.build_tools.Count -gt 0) {
            $toolsList = $requirements.build_tools -join " "
            Write-Host "Installing build tools..." -ForegroundColor Yellow
            Invoke-RemoteCommand -Command "sudo apt-get install -y $toolsList"
        }

        # Install libraries
        if ($requirements.libraries -and $requirements.libraries.Count -gt 0) {
            $libsList = $requirements.libraries -join " "
            Write-Host "Installing libraries..." -ForegroundColor Yellow
            Invoke-RemoteCommand -Command "sudo apt-get install -y $libsList"
        }

        # Install Python packages if any
        if ($requirements.python_packages -and $requirements.python_packages.Count -gt 0) {
            $pythonPackageList = $requirements.python_packages -join " "
            Write-Host "Installing Python packages..." -ForegroundColor Yellow
            Invoke-RemoteCommand -Command "sudo pip3 install $pythonPackageList"
        }

        # Create service user and group if needed
        if ($requirements.users -and $requirements.users.Count -gt 0) {
            Write-Host "Setting up service users and groups..." -ForegroundColor Yellow

            foreach ($userInfo in $requirements.users) {
                $username = $userInfo.name
                $group = $userInfo.group

                # Check if group exists
                $groupExists = Invoke-RemoteCommand -Command "getent group $group > /dev/null 2>&1 && echo 'exists' || echo 'not exists'"
                if ($groupExists -ne "exists") {
                    Invoke-RemoteCommand -Command "sudo groupadd $group"
                }

                # Check if user exists
                $userExists = Invoke-RemoteCommand -Command "id -u $username > /dev/null 2>&1 && echo 'exists' || echo 'not exists'"
                if ($userExists -ne "exists") {
                    Invoke-RemoteCommand -Command "sudo useradd -m -g $group $username"
                }
            }
        }

        # Set up directory permissions
        if ($requirements.directories -and $requirements.directories.Count -gt 0) {
            Write-Host "Setting up directory permissions..." -ForegroundColor Yellow

            foreach ($dir in $requirements.directories) {
                # Create directory if it doesn't exist
                Invoke-RemoteCommand -Command "sudo mkdir -p $($dir.path)"

                # Set ownership if owner is specified
                if ($dir.owner) {
                    $chownCmd = "sudo chown $($dir.owner)"
                    if ($dir.group) { $chownCmd += ":$($dir.group)" }
                    $chownCmd += " $($dir.path)"
                    Invoke-RemoteCommand -Command $chownCmd
                }

                # Set permissions if specified
                if ($dir.permissions) {
                    Invoke-RemoteCommand -Command "sudo chmod $($dir.permissions) $($dir.path)"
                }
            }
        }

        Write-Host "`nDependency installation completed successfully." -ForegroundColor Green
    }
    catch {
        Write-Host "Error installing dependencies: $_" -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Build project using the Build-Project module
function Build-Project {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "                Build Project                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if (-not $Config -or -not $Config.ssh -or -not $Config.project) {
        Write-Host "Configuration not loaded or incomplete. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Import the required modules
    try {
        # Import Test-SSHConnection module
        $modulePath = Join-Path -Path $PSScriptRoot -ChildPath "Modules\Test-SSHConnection.psm1"
        if (Test-Path -Path $modulePath) {
            Import-Module -Name $modulePath -Force -ErrorAction Stop
            Write-Host "Successfully imported Test-SSHConnection module" -ForegroundColor Green
        } else {
            Write-Host "Test-SSHConnection module not found at: $modulePath" -ForegroundColor Red
            Return-ToMainMenu
            return
        }

        # Import Build-Project module
        $buildModulePath = Join-Path -Path $PSScriptRoot -ChildPath "Modules\Build-Project.psm1"
        if (Test-Path -Path $buildModulePath) {
            Import-Module -Name $buildModulePath -Force -ErrorAction Stop
            Write-Host "Successfully imported Build-Project module" -ForegroundColor Green
        } else {
            Write-Host "Build-Project module not found at: $buildModulePath" -ForegroundColor Red
            Return-ToMainMenu
            return
        }
    } catch {
        Write-Host "Error importing required modules: $_" -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # First check SSH connection using the imported module
    Write-Host "Testing SSH connection..." -ForegroundColor Yellow
    $connected = Test-SSHConnection -HostName $Config.ssh.host -User $Config.ssh.username -Port $Config.ssh.port -KeyPath $Config.ssh.key_path

    if (-not $connected) {
        Write-Host "Failed to connect to remote server. Please check your SSH settings." -ForegroundColor Red
        Return-ToMainMenu
        return
    }
    Write-Host "SSH connection successful." -ForegroundColor Green
    Write-Host

    # Set the script-level configuration for the module to use
    $script:Config = $Config
    $script:Environment = $DeploymentEnvironment

    # Call the Build-Project function from the module
    try {
        # Call the module's Build-Project function
        # This will handle all the build steps including:
        # - Creating build directories
        # - Copying source files
        # - Running CMake
        # - Building the project
        # - Creating deployment package
        Write-Host "Calling Build-Project module..." -ForegroundColor Cyan
        Build-Project
    }
    catch {
        Write-Host "Error during build process: $_" -ForegroundColor Red
        Write-Host $_.ScriptStackTrace -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Install service (placeholder)
function Install-Service {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Install Service                         " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "This function would install the Database Service." -ForegroundColor Yellow
    Write-Host "Currently implemented as a placeholder." -ForegroundColor Yellow

    Return-ToMainMenu
}

# Start database service (placeholder)
function Start-DatabaseService {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "             Start Database Service                   " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "This function would start the Database Service." -ForegroundColor Yellow
    Write-Host "Currently implemented as a placeholder." -ForegroundColor Yellow

    Return-ToMainMenu
}

# Initialize database (placeholder)
function Initialize-Database {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "             Initialize Database                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "This function would initialize the database schema." -ForegroundColor Yellow
    Write-Host "Currently implemented as a placeholder." -ForegroundColor Yellow

    Return-ToMainMenu
}

# Get service status
function Get-ServiceStatus {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Service Status                          " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if ($null -eq $Config -or $null -eq $Config.ssh -or $null -eq $Config.service) {
        Write-Host "Configuration not loaded or incomplete. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "Checking service status for $($Config.service.name)..." -ForegroundColor Yellow

    # First test SSH connection
    if (-not (Connect-RemoteServer -Silent)) {
        Write-Host "Failed to connect to remote server. Please check your SSH settings." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "SSH connection successful." -ForegroundColor Green

    try {
        # Check service status using systemctl
        $statusCommand = "sudo systemctl status $($Config.service.name)"
        $output = Invoke-RemoteCommand $statusCommand

        # Parse the output to determine service status
        if ($output -match "Active:\s+(active|running)") {
            Write-Host "`nService Status: RUNNING" -ForegroundColor Green
        } elseif ($output -match "Active:\s+inactive") {
            Write-Host "`nService Status: STOPPED" -ForegroundColor Yellow
        } elseif ($output -match "Active:\s+failed") {
            Write-Host "`nService Status: FAILED" -ForegroundColor Red
        } else {
            Write-Host "`nService Status: UNKNOWN" -ForegroundColor Gray
        }

        # Extract uptime if the service is running
        if ($output -match "Active:\s+active\s+\(running\)\s+since\s+(.+);") {
            Write-Host "Running since: $($matches[1])" -ForegroundColor White
        }

        # Check recent log entries
        Write-Host
        Write-Host "Recent log entries:" -ForegroundColor Cyan
        $logCommand = "sudo journalctl -u $($Config.service.name) -n 5 --no-pager"
        $logs = Invoke-RemoteCommand $logCommand

        Write-Host "$logs" -ForegroundColor Gray

        # Show memory usage and other stats if available
        Write-Host
        Write-Host "Resource usage:" -ForegroundColor Cyan
        $resourceCommand = "sudo systemctl show $($Config.service.name) --property=MainPID,MemoryCurrent,TasksCurrent"
        $resources = Invoke-RemoteCommand $resourceCommand

        # Parse resource info
        $memInfo = [PSCustomObject]@{}
        foreach ($line in $resources.Split("`n")) {
            if ($line -match "([^=]+)=(.+)") {
                $key = $matches[1].Trim()
                $value = $matches[2].Trim()
                Add-Member -InputObject $memInfo -MemberType NoteProperty -Name $key -Value $value -Force
            }
        }

        # Display formatted resource information
        Write-Host "Process ID: $($memInfo.MainPID)" -ForegroundColor White

        # Convert memory to MB if available
        if ($memInfo.MemoryCurrent -and $memInfo.MemoryCurrent -ne "[not set]") {
            $memoryMB = [math]::Round([long]$memInfo.MemoryCurrent / 1MB, 2)
            Write-Host "Memory Usage: $memoryMB MB" -ForegroundColor White
        }

        if ($memInfo.TasksCurrent -and $memInfo.TasksCurrent -ne "[not set]") {
            Write-Host "Tasks: $($memInfo.TasksCurrent)" -ForegroundColor White
        }
    } catch {
        Write-Host "Error checking service status: $_" -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Run custom command
function Run-CustomCommand {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Run Custom Command                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if (-not $Config -or -not $Config.ssh) {
        Write-Host "No SSH configuration loaded. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "Checking SSH connection..." -ForegroundColor Yellow
    $sshTest = Connect-RemoteServer

    if (-not $sshTest) {
        Write-Host "SSH connection failed. Cannot run custom commands." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "SSH connection successful." -ForegroundColor Green

    # Get custom command from user
    $customCommand = Read-Host "Enter the command to run on $($Config.ssh.host)"

    if ([string]::IsNullOrWhiteSpace($customCommand)) {
        Write-Host "No command provided. Returning to menu." -ForegroundColor Yellow
        Return-ToMainMenu
        return
    }

    # Confirm command execution
    Write-Host "About to run: $customCommand" -ForegroundColor Yellow
    $confirm = Read-Host "Proceed? (y/n)"

    if ($confirm -ne "y") {
        Write-Host "Command execution cancelled." -ForegroundColor Yellow
        Return-ToMainMenu
        return
    }

    # Execute the command
    Write-Host "Executing command..." -ForegroundColor Yellow
    $result = Invoke-RemoteCommand -Command $customCommand

    Write-Host
    Write-Host "Command output:" -ForegroundColor Cyan
    Write-Host $result

    Return-ToMainMenu
}

# Start full deployment (placeholder)
function Start-FullDeployment {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "             Full Deployment                          " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "This function would perform a full deployment of the Database Service." -ForegroundColor Yellow
    Write-Host "It would execute the following steps in sequence:" -ForegroundColor Yellow
    Write-Host "1. Check server readiness" -ForegroundColor White
    Write-Host "2. Install dependencies" -ForegroundColor White
    Write-Host "3. Build project" -ForegroundColor White
    Write-Host "4. Install service" -ForegroundColor White
    Write-Host "5. Initialize database" -ForegroundColor White
    Write-Host "6. Start service" -ForegroundColor White
    Write-Host "7. Verify service status" -ForegroundColor White

    Write-Host
    Write-Host "Currently implemented as a placeholder." -ForegroundColor Yellow

    Return-ToMainMenu
}

# Exit the script
function Exit-Script {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "                 Exiting Script                        " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host
    Write-Host "Thank you for using the Database Service Deployment Script." -ForegroundColor White
    exit 0
}

# Start the script
function Start-Script {
    # Create necessary directories if they don't exist
    if (-not (Test-Path -Path $PSScriptRoot)) {
        New-Item -Path $PSScriptRoot -ItemType Directory -Force | Out-Null
    }

    $script:ConfigDir = Join-Path -Path $PSScriptRoot -ChildPath "config"
    $script:LogDir = Join-Path -Path $PSScriptRoot -ChildPath "logs"

    if (-not (Test-Path -Path $ConfigDir)) {
        try {
            New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return
        }
    }

    if (-not (Test-Path -Path $LogDir)) {
        try {
            New-Item -Path $LogDir -ItemType Directory -Force | Out-Null
        } catch {
            Write-Host "Failed to create log directory: $_" -ForegroundColor Red
            return
        }
    }

    # Load configuration if available
    $configFile = Join-Path -Path $ConfigDir -ChildPath "database-service-$Environment.json"
    if (Test-Path $configFile) {
        try {
            $script:Config = Get-Content -Path $configFile -Raw | ConvertFrom-Json
            Write-Host "Loaded configuration from $configFile" -ForegroundColor Green
        } catch {
            Write-Host "Error loading configuration: $_" -ForegroundColor Red
            $script:Config = $null
        }
    } else {
        Write-Host "No configuration found for environment: $Environment" -ForegroundColor Yellow
        $script:Config = $null
    }
}

# Show main menu
function Show-MainMenu {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "       Database Service Deployment Script Menu         " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "Environment: " -ForegroundColor Cyan -NoNewline
    switch ($Environment) {
        "production" { Write-Host "PRODUCTION" -ForegroundColor Red }
        "staging" { Write-Host "STAGING" -ForegroundColor Yellow }
        "development" { Write-Host "DEVELOPMENT" -ForegroundColor Green }
        default { Write-Host $Environment -ForegroundColor White }
    }

    Write-Host
    Write-Host "[1] Test SSH Connection" -ForegroundColor White
    Write-Host "[2] Manage SSH Keys" -ForegroundColor White
    Write-Host "[3] Change Environment" -ForegroundColor White
    Write-Host "[4] Show Configuration" -ForegroundColor White
    Write-Host "[5] Check Server Readiness" -ForegroundColor White
    Write-Host "[6] Install Dependencies" -ForegroundColor White
    Write-Host "[7] Build Project" -ForegroundColor White
    Write-Host "[8] Install Service" -ForegroundColor White
    Write-Host "[9] Start Database Service" -ForegroundColor White
    Write-Host "[10] Initialize Database" -ForegroundColor White
    Write-Host "[11] Check Service Status" -ForegroundColor White
    Write-Host "[12] Run Custom Command" -ForegroundColor White
    Write-Host "[13] Exit" -ForegroundColor White

    Write-Host
    $choice = Read-Host "Select an option (1-13)"

    switch ($choice) {
        "1" { Test-SSHConnection }
        "2" { Set-SSHKeys }
        "3" { Change-Environment }
        "4" { Show-Configuration }
        "5" { Test-ServerReadiness }
        "6" { Install-Dependencies }
        "7" { Build-Project }
        "8" { Install-Service }
        "9" { Start-DatabaseService }
        "10" { Initialize-Database }
        "11" { Get-ServiceStatus }
        "12" { Run-CustomCommand }
        "13" { Exit-Script }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Show-MainMenu
        }
    }
}

# Configure Environment
function Configure-Environment {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "            Environment Configuration                 " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "Current environment: " -ForegroundColor Cyan -NoNewline
    switch ($Environment) {
        "production" { Write-Host "PRODUCTION" -ForegroundColor Red }
        "staging" { Write-Host "STAGING" -ForegroundColor Yellow }
        "development" { Write-Host "DEVELOPMENT" -ForegroundColor Green }
        default { Write-Host $Environment -ForegroundColor White }
    }

    # Display current configuration if available
    if ($null -ne $Config -and $null -ne $Config.project -and $null -ne $DeploymentEnvironment) {
        Write-Host "Current Configuration:" -ForegroundColor Yellow
        Write-Host "Project: $($Config.project.name)" -ForegroundColor White
        Write-Host "Environment: $DeploymentEnvironment" -ForegroundColor White
        Write-Host "Server: $($Config.ssh.host)" -ForegroundColor White
        Write-Host "Install Directory: $($Config.project.install_dir)" -ForegroundColor White
    } else {
        Write-Host "No configuration currently loaded." -ForegroundColor White
    }
    Write-Host

    # Check if config directory exists
    if (-not (Test-Path -Path $ConfigDir)) {
        Write-Host "No configuration directory found. Creating one now..." -ForegroundColor Yellow
        try {
            New-Item -Path $ConfigDir -ItemType Directory | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return
        }
    }

    # Get available config files
    $configFiles = Get-ChildItem -Path $ConfigDir -Filter "*.json" | Select-Object -ExpandProperty Name

    if ($configFiles.Count -eq 0) {
        Write-Host "No configuration files found. Please create a new configuration." -ForegroundColor Yellow
        Write-Host
        $environment = Read-Host "Environment name (development, testing, production)"
        Create-DefaultConfig -Environment $environment
        $script:DeploymentEnvironment = $environment
        return
    }

    # Display available environments
    Write-Host "Menu Options:" -ForegroundColor Yellow

    $menu = @{}
    $index = 1

    # Add option to create a new configuration
    Write-Host "[$index] Create New Configuration" -ForegroundColor White
    $menu[$index] = "new"
    $index++

    # Section for saved configurations
    Write-Host
    Write-Host "Available Configurations:" -ForegroundColor Yellow
    $configIndex = $index

    foreach ($file in $configFiles) {
        # Extract both project name and environment from filename (project-environment.json)
        if ($file -match '(.+)-(.+)\.json$') {
            $projectName = $matches[1]
            $env = $matches[2]
            $menu[$index] = @{
                file = $file
                environment = $env
                project = $projectName
            }

            # Check if this configuration has a "True" project name issue
            $configFile = Join-Path -Path $ConfigDir -ChildPath $file
            $displayName = $projectName

            try {
                $configContent = Get-Content -Path $configFile -Raw | ConvertFrom-Json
                if ($configContent.project.name -eq $true) {
                    # If the project name is "True", fix it now but don't output a message
                    $configContent.project.name = $projectName
                    $configContent | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
                    $displayName = $projectName
                } else {
                    $displayName = $configContent.project.name
                }

                # Simply show the filename
                Write-Host "[$index] $file" -ForegroundColor White
            } catch {
                Write-Host "[$index] $file" -ForegroundColor White
                Write-Host "    Warning: Could not read configuration file properly" -ForegroundColor Yellow
            }

            $index++
        }
    }

    # Add option to back to main menu (at the end)
    Write-Host
    Write-Host "[$index] Return to Main Menu" -ForegroundColor White
    $menu[$index] = "back"

    Write-Host
    $choice = Read-Host "Select an option"

    # Make sure we treat the choice as a string for consistent comparison
    $choice = $choice.ToString()

    [int]$choiceNum = 0
    $validInt = [int]::TryParse($choice, [ref]$choiceNum)

    if ($validInt -and $menu.ContainsKey($choiceNum)) {
        $selected = $menu[$choiceNum]

        if ($selected -eq "back") {
            Return-ToMainMenu
            return
        }
        elseif ($selected -eq "new") {
            # Create new configuration
            $environment = Read-Host "Environment name (development, testing, production)"
            Create-DefaultConfig -Environment $environment
            $script:DeploymentEnvironment = $environment
            return
        }
        else {
            # Load an existing configuration
            $configFile = Join-Path -Path $ConfigDir -ChildPath $selected.file
            try {
                $config = Get-Content -Path $configFile -Raw | ConvertFrom-Json

                # Convert from JSON to PowerShell hashtable
                $script:Config = @{}
                $config.PSObject.Properties | ForEach-Object {
                    $name = $_.Name
                    $value = $_.Value

                    if ($value -is [System.Management.Automation.PSCustomObject]) {
                        $valueHash = @{}
                        $value.PSObject.Properties | ForEach-Object {
                            $valueHash[$_.Name] = $_.Value
                        }
                        $script:Config[$name] = $valueHash
                    } else {
                        $script:Config[$name] = $value
                    }
                }

                $script:DeploymentEnvironment = $selected.environment
                Write-Host "`nLoaded configuration: $($selected.file)" -ForegroundColor Green

                # Check for invalid "True" project name and fix it
                if ($Config.project.name -eq $true) {
                    Write-Host "Detected invalid project name. Fixing configuration..." -ForegroundColor Yellow
                    $Config.project.name = $selected.project

                    # Save the fixed configuration
                    $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
                    Write-Host "Fixed configuration saved." -ForegroundColor Green
                }

                # Pause to show the message
                Pause-Script
            } catch {
                Write-Host "Error loading configuration: $_" -ForegroundColor Red
                Pause-Script
            }
        }
    } else {
        Write-Host "Invalid option. Please try again." -ForegroundColor Red
        Start-Sleep -Seconds 1
        Configure-Environment
    }
}

# Change Environment
function Change-Environment {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Change Environment                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "Current environment: " -ForegroundColor Cyan -NoNewline
    switch ($Environment) {
        "production" { Write-Host "PRODUCTION" -ForegroundColor Red }
        "staging" { Write-Host "STAGING" -ForegroundColor Yellow }
        "development" { Write-Host "DEVELOPMENT" -ForegroundColor Green }
        default { Write-Host $Environment -ForegroundColor White }
    }

    Write-Host
    Write-Host "Available environments:" -ForegroundColor White
    Write-Host "[1] Development" -ForegroundColor Green
    Write-Host "[2] Staging" -ForegroundColor Yellow
    Write-Host "[3] Production" -ForegroundColor Red
    Write-Host "[4] Return to Configuration Menu" -ForegroundColor White

    Write-Host
    $choice = Read-Host "Select an environment (1-4)"

    switch ($choice) {
        "1" {
            $script:Environment = "development"
            Write-Host "Environment changed to DEVELOPMENT" -ForegroundColor Green
            Start-Script # Reload configuration for the new environment
            Pause-Script
            Configure-Environment
        }
        "2" {
            $script:Environment = "staging"
            Write-Host "Environment changed to STAGING" -ForegroundColor Yellow
            Start-Script # Reload configuration for the new environment
            Pause-Script
            Configure-Environment
        }
        "3" {
            $script:Environment = "production"
            Write-Host "Warning: You are switching to PRODUCTION environment!" -ForegroundColor Red
            $confirm = Read-Host "Are you sure? (y/n)"
            if ($confirm -eq "y") {
                $script:Environment = "production"
                Write-Host "Environment changed to PRODUCTION" -ForegroundColor Red
                Start-Script # Reload configuration for the new environment
                Pause-Script
            }
            Configure-Environment
        }
        "4" { Return-ToMainMenu }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Change-Environment
        }
    }
}

# Create new configuration
function Create-Configuration {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "              Create New Configuration                 " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    Write-Host "Creating configuration for environment: " -ForegroundColor Cyan -NoNewline
    switch ($Environment) {
        "production" { Write-Host "PRODUCTION" -ForegroundColor Red }
        "staging" { Write-Host "STAGING" -ForegroundColor Yellow }
        "development" { Write-Host "DEVELOPMENT" -ForegroundColor Green }
        default { Write-Host $Environment -ForegroundColor White }
    }

    # Initialize configuration with default values
    $script:Config = @{
        project = @{
            name = "database-service"
            version = "1.0.0"
            build_dir = "/tmp/database-service-build"
            install_dir = "/opt/database-service"
            description = "Database Service for Project Tracker"
        }
        ssh = @{
            host = "project-tracker.chcit.org"
            username = "btaylor-admin"
            port = 22
            key_path = ""
            sudo_no_passwd = $false
        }
        database = @{
            host = "localhost"
            port = 5432
            name = "project_tracker"
            username = "pt_service"
            password = ""
            require_ssl = $true
        }
        service = @{
            name = "database-service"
            description = "Database Service for Project Tracker"
            user = "pt_service"
            group = "pt_service"
            port = 8080
            log_dir = "/var/log/database-service"
            config_dir = "/etc/database-service"
        }
    }

    # Try to detect SSH key
    Write-Host "`nAttempting to detect SSH key..." -ForegroundColor Yellow
    $detectedKeyPath = Find-SSHKey

    if (-not [string]::IsNullOrWhiteSpace($detectedKeyPath)) {
        Write-Host "SSH key detected: $detectedKeyPath" -ForegroundColor Green
        $Config.ssh.key_path = $detectedKeyPath
    } else {
        Write-Host "Could not detect SSH key. Please set it manually." -ForegroundColor Yellow
    }

    # Prompt for important values
    Write-Host "`nPlease enter the following configuration values (or press Enter to use defaults):" -ForegroundColor White

    # Project configuration
    $projectName = Read-Host "Project name [database-service]"
    if (-not [string]::IsNullOrWhiteSpace($projectName)) {
        $Config.project.name = $projectName
    }

    # Server configuration
    Write-Host "`nServer Configuration:" -ForegroundColor Yellow
    $serverHost = Read-Host "Server hostname [project-tracker.chcit.org]"
    if (-not [string]::IsNullOrWhiteSpace($serverHost)) {
        $Config.ssh.host = $serverHost
    }

    $sshUser = Read-Host "SSH username [btaylor-admin]"
    if (-not [string]::IsNullOrWhiteSpace($sshUser)) {
        $Config.ssh.username = $sshUser
    }

    $sshKeyPath = Read-Host "SSH Key path [$($Config.ssh.key_path)]"
    if (-not [string]::IsNullOrWhiteSpace($sshKeyPath)) {
        $Config.ssh.key_path = $sshKeyPath
    }

    # Database configuration
    Write-Host "`nDatabase Configuration:" -ForegroundColor Yellow
    $dbName = Read-Host "Database name [project_tracker]"
    if (-not [string]::IsNullOrWhiteSpace($dbName)) {
        $Config.database.name = $dbName
    }

    # Service configuration
    Write-Host "`nService Configuration:" -ForegroundColor Yellow
    $servicePort = Read-Host "Service port [8080]"
    if (-not [string]::IsNullOrWhiteSpace($servicePort) -and $servicePort -match '^\d+$') {
        $Config.service.port = [int]$servicePort
    }

    # Save the configuration
    if ($Config.project -and $Config.project.name -and $DeploymentEnvironment) {
        if (-not (Test-Path -Path $ConfigDir)) {
            try {
                New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
            } catch {
                Write-Host "`nFailed to create configuration directory: $_" -ForegroundColor Red
                Pause-Script
                return
            }
        }

        try {
            $configFile = Join-Path -Path $ConfigDir -ChildPath "$($Config.project.name)-$DeploymentEnvironment.json"
            $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8

            Write-Host "`nSSH configuration saved successfully to $configFile." -ForegroundColor Green
        } catch {
            Write-Host "`nFailed to save configuration: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "`nConfiguration updated but not saved. No project or environment set." -ForegroundColor Yellow
    }

    # Ask if user wants to test the connection
    Write-Host
    $testConnection = Read-Host "Would you like to test the SSH connection now? (y/n)"
    if ($testConnection -eq "y") {
        Test-SSHConnection
    } else {
        Pause-Script
    }
}

# Test server readiness
function Test-ServerReadiness {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Server Readiness Check                 " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "Configuration not loaded or incomplete. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    Write-Host "Checking server readiness for $($Config.ssh.host)..." -ForegroundColor Yellow

    # First test SSH connection
    Write-Host "Testing SSH connection..." -ForegroundColor Yellow
    if (-not (Connect-RemoteServer -Silent)) {
        Write-Host "Failed to connect to remote server. Please check your SSH settings." -ForegroundColor Red
        Return-ToMainMenu
        return
    } else {
        Write-Host "SSH connection successful." -ForegroundColor Green
    }

    Write-Host
    Write-Host "Checking server requirements..." -ForegroundColor Yellow

    # Define the checks to perform
    $checks = @(
        @{ Name = "Operating System"; Command = "cat /etc/os-release | grep PRETTY_NAME"; Validation = { param($output) $output -match "Ubuntu" } },
        @{ Name = "Disk Space"; Command = "df -h / | grep -v Filesystem"; Validation = { param($output) if ($output -match "([0-9]+)%") { [int]$used = $matches[1]; $used -lt 90 } else { $false } } },
        @{ Name = "Memory"; Command = "free -m | grep Mem:"; Validation = { param($output) if ($output -match "Mem:\s+([0-9]+)") { [int]$total = $matches[1]; $total -ge 1024 } else { $false } } },
        @{ Name = "PostgreSQL"; Command = "command -v psql || echo 'PostgreSQL not installed'"; Validation = { param($output) $output -notmatch "not installed" } },
        @{ Name = "Build Tools"; Command = "command -v gcc g++ make cmake || echo 'Build tools missing'"; Validation = { param($output) $output -notmatch "missing" } },
        @{ Name = "Systemd"; Command = "systemctl --version | head -n 1"; Validation = { param($output) $output -match "systemd" } }
    )

    $allPassed = $true

    # Run each check
    foreach ($check in $checks) {
        try {
            Write-Host "  Checking $($check.Name)..." -NoNewline
            $output = Invoke-RemoteCommand $check.Command -Silent
            $passed = & $check.Validation $output

            if ($passed) {
                Write-Host "PASS" -ForegroundColor Green
                Write-Host "    $output" -ForegroundColor Gray
            } else {
                Write-Host "FAIL" -ForegroundColor Red
                Write-Host "    $output" -ForegroundColor Gray
                $allPassed = $false
            }
        } catch {
            Write-Host "ERROR" -ForegroundColor Red
            Write-Host "    Error running check: $_" -ForegroundColor Gray
            $allPassed = $false
        }
    }

    Write-Host
    # Check installation directory
    if ($Config.project -and $Config.project.install_dir) {
        Write-Host "Checking installation directory $($Config.project.install_dir)..." -NoNewline
        $checkDirCmd = 'sudo test -d "$($Config.project.install_dir)" && echo "EXISTS" || echo "NOT_EXISTS"'
        $dirExists = Invoke-RemoteCommand $checkDirCmd -Silent

        if ($dirExists -match "EXISTS") {
            Write-Host "EXISTS" -ForegroundColor Green

            # Check directory permissions
            $checkPermsCmd = 'sudo stat -c "%U:%G" "$($Config.project.install_dir)"'
            $dirPerms = Invoke-RemoteCommand $checkPermsCmd -Silent
            Write-Host "    Owner/Group: $dirPerms" -ForegroundColor Gray

            # Check if we can write to the directory
            $writeTestCmd = 'sudo touch "$($Config.project.install_dir)/.write_test" && sudo rm "$($Config.project.install_dir)/.write_test" && echo "WRITABLE" || echo "NOT_WRITABLE"'
            $writableTest = Invoke-RemoteCommand $writeTestCmd -Silent

            if ($writableTest -match "WRITABLE") {
                Write-Host "    Directory is writable." -ForegroundColor Green
            } else {
                Write-Host "    Directory is not writable!" -ForegroundColor Red
                $allPassed = $false
            }
        } else {
            Write-Host "DOES NOT EXIST" -ForegroundColor Yellow
            Write-Host "    Directory will be created during installation." -ForegroundColor Gray
        }
    } else {
        Write-Host "Installation directory not configured!" -ForegroundColor Red
        $allPassed = $false
    }

    Write-Host
    # Summary
    if ($allPassed) {
        Write-Host "Server readiness check: " -NoNewline
        Write-Host "PASSED" -ForegroundColor Green
        Write-Host "The server meets all requirements for deployment." -ForegroundColor Green
    } else {
        Write-Host "Server readiness check: " -NoNewline
        Write-Host "FAILED" -ForegroundColor Red
        Write-Host "The server does not meet all requirements. Please address the issues before deployment." -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Test SSH connection
function Test-SSHConnection {
    Clear-Host

    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Test SSH Connection                     " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "No SSH configuration loaded. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Display current SSH settings
    Write-Host "Current SSH Settings:" -ForegroundColor Yellow
    Write-Host "Host: $($Config.ssh.host)" -ForegroundColor Cyan
    Write-Host "Username: $($Config.ssh.username)" -ForegroundColor Cyan
    Write-Host "Port: $($Config.ssh.port)" -ForegroundColor Cyan
    Write-Host "Key path: $($Config.ssh.key_path)" -ForegroundColor Cyan
    Write-Host

    # If SSH key path is empty, try to detect it
    if ([string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        Write-Host "SSH key path is not configured. Attempting to detect SSH key..." -ForegroundColor Yellow
        $detectedKeyPath = Find-SSHKey
        if (-not [string]::IsNullOrWhiteSpace($detectedKeyPath)) {
            $Config.ssh.key_path = $detectedKeyPath
            Write-Host "Found SSH key at: $detectedKeyPath" -ForegroundColor Green

            # Update the configuration file
            if (-not (Test-Path -Path $ConfigDir)) {
                try {
                    New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
                } catch {
                    Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
                    Return-ToMainMenu
                    return
                }
            }

            try {
                $configFile = Join-Path -Path $ConfigDir -ChildPath "$($Config.project.name)-$DeploymentEnvironment.json"
                $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
                Write-Host "Updated configuration with detected SSH key path" -ForegroundColor Green
            } catch {
                Write-Host "Failed to save configuration: $_" -ForegroundColor Red
            }
        } else {
            Write-Host "No SSH keys found. Please configure an SSH key path." -ForegroundColor Red
            Return-ToMainMenu
            return
        }
    }

    # Test connection
    Write-Host "Testing SSH connection..." -ForegroundColor Yellow

    # Make sure we have all required SSH parameters before testing
    if ([string]::IsNullOrWhiteSpace($Config.ssh.host) -or
        [string]::IsNullOrWhiteSpace($Config.ssh.username) -or
        $null -eq $Config.ssh.port -or
        [string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        Write-Host "SSH connection parameters are not fully configured." -ForegroundColor Red
        Write-Host "Please check your SSH settings and ensure all fields are filled." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Check if the SSH key file exists
    if (-not (Test-Path -Path $Config.ssh.key_path)) {
        Write-Host "SSH key not found at $($Config.ssh.key_path)" -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Attempt SSH connection
    try {
        $sshArgs = @(
            "-i", "$($Config.ssh.key_path)",
            "-p", "$($Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($Config.ssh.username)@$($Config.ssh.host)",
            "echo 'SSH connection successful'"
        )

        $output = & ssh @sshArgs 2>&1
        $exitCode = $LASTEXITCODE

        if ($exitCode -eq 0 -and $output -like "*SSH connection successful*") {
            Write-Host "`nConnection successful!" -ForegroundColor Green
        } else {
            Write-Host "`nConnection failed." -ForegroundColor Red
            Write-Host "SSH command output: $output" -ForegroundColor Red
            Write-Host "Please check your SSH settings and try again." -ForegroundColor Red
        }
    } catch {
        Write-Host "`nConnection failed." -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
        Write-Host "Please check your SSH settings and try again." -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Helper function to return to the main menu
function Return-ToMainMenu {
    Write-Host "`nPress Enter to return to the main menu..." -ForegroundColor Cyan
    Read-Host | Out-Null
    Show-MainMenu
}

# Helper function to pause script execution
function Pause-Script {
    Write-Host "`nPress Enter to continue..." -ForegroundColor Cyan
    Read-Host | Out-Null
}

# Helper function to connect to the remote server via SSH
function Connect-RemoteServer {
    param(
        [Parameter(Position = 0, Mandatory = $false)]
        [switch]$Silent
    )

    if ($null -eq $Config -or $null -eq $Config.ssh) {
        if (-not $Silent) {
            Write-Host "No SSH configuration loaded. Please set up your environment first." -ForegroundColor Red
        }
        return $false
    }

    if ([string]::IsNullOrWhiteSpace($Config.ssh.host) -or
        [string]::IsNullOrWhiteSpace($Config.ssh.username) -or
        $null -eq $Config.ssh.port -or
        [string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        if (-not $Silent) {
            Write-Host "SSH connection parameters are not fully configured." -ForegroundColor Red
            Write-Host "Please check your SSH settings and ensure all fields are filled." -ForegroundColor Red
        }
        return $false
    }

    # Check if the SSH key file exists
    if (-not (Test-Path -Path $Config.ssh.key_path)) {
        if (-not $Silent) {
            Write-Host "SSH key not found at $($Config.ssh.key_path)" -ForegroundColor Red
        }
        return $false
    }

    # Attempt SSH connection
    try {
        $sshArgs = @(
            "-i", "$($Config.ssh.key_path)",
            "-p", "$($Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($Config.ssh.username)@$($Config.ssh.host)",
            "echo 'CONNECTION_OK'"
        )

        $output = & ssh @sshArgs 2>&1
        $exitCode = $LASTEXITCODE

        if ($exitCode -eq 0 -and $output -like "*CONNECTION_OK*") {
            if (-not $Silent) {
                Write-Host "Connection successful!" -ForegroundColor Green
            }
            return $true
        } else {
            if (-not $Silent) {
                Write-Host "Connection failed." -ForegroundColor Red
                Write-Host "SSH command output: $output" -ForegroundColor Red
            }
            return $false
        }
    } catch {
        if (-not $Silent) {
            Write-Host "Connection failed." -ForegroundColor Red
            Write-Host "Error: $_" -ForegroundColor Red
        }
        return $false
    }
}

# Helper function to execute commands on the remote server
function Invoke-RemoteCommand {
    param(
        [Parameter(Position = 0, Mandatory = $true)]
        [string]$Command,

        [Parameter(Position = 1, Mandatory = $false)]
        [switch]$Silent,

        [Parameter(Position = 2, Mandatory = $false)]
        [switch]$AsSudo
    )

    if (-not (Connect-RemoteServer -Silent)) {
        if (-not $Silent) {
            Write-Host "Failed to connect to remote server. Command not executed." -ForegroundColor Red
        }
        return $null
    }

    try {
        $sudoPrefix = ""
        if ($AsSudo) {
            if ($Config.ssh.sudo_no_passwd) {
                $sudoPrefix = "sudo "
            } else {
                if (-not $Silent) {
                    Write-Host "Sudo without password is not enabled for this connection." -ForegroundColor Yellow
                    Write-Host "Some operations may fail if they require elevated privileges." -ForegroundColor Yellow
                }
            }
        }

        # Properly escape the command for SSH
        $escapedCommand = $Command -replace '"', '\"'

        $sshArgs = @(
            "-i", "$($Config.ssh.key_path)",
            "-p", "$($Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "$($Config.ssh.username)@$($Config.ssh.host)",
            "${sudoPrefix}${escapedCommand}"
        )

        $output = & ssh @sshArgs 2>&1
        $exitCode = $LASTEXITCODE

        if ($exitCode -ne 0 -and -not $Silent) {
            Write-Host "Command execution failed with exit code $exitCode" -ForegroundColor Red
            Write-Host "Command: ${sudoPrefix}${escapedCommand}" -ForegroundColor Red
        }

        return $output
    } catch {
        if (-not $Silent) {
            Write-Host "Error executing command: $_" -ForegroundColor Red
            Write-Host "Command: $Command" -ForegroundColor Red
        }
        return $null
    }
}

# Helper function to execute commands on the remote server and return true/false based on success
function Test-RemoteCommand {
    param(
        [Parameter(Position = 0, Mandatory = $true)]
        [string]$Command,

        [Parameter(Position = 1, Mandatory = $false)]
        [switch]$Silent,

        [Parameter(Position = 2, Mandatory = $false)]
        [switch]$AsSudo
    )

    $output = Invoke-RemoteCommand -Command $Command -Silent:$Silent -AsSudo:$AsSudo
    $exitCode = $LASTEXITCODE

    if ($null -eq $output -or $exitCode -ne 0) {
        return $false
    }

    return $true
}

# Function to manage SSH keys
function Set-SSHKeys {
    Clear-Host

    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "                  Manage SSH Keys                     " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if configuration is loaded
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "No SSH configuration loaded. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Display current SSH key information
    Write-Host "Current SSH Key Configuration:" -ForegroundColor Yellow

    # Check if the key path is configured
    if ([string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        Write-Host "No SSH key path configured." -ForegroundColor Red
    } else {
        Write-Host "Key path: $($Config.ssh.key_path)" -ForegroundColor White
        $sshKeyExists = Test-Path -Path $Config.ssh.key_path

        if ($sshKeyExists) {
            Write-Host "Status: Key file exists" -ForegroundColor Green

            # Display public key info if available
            $pubKeyPath = "$($Config.ssh.key_path).pub"
            if (Test-Path -Path $pubKeyPath) {
                $pubKeyContent = Get-Content -Path $pubKeyPath -Raw
                if (-not [string]::IsNullOrWhiteSpace($pubKeyContent)) {
                    $pubKeyParts = $pubKeyContent.Split(' ')
                    if ($pubKeyParts.Length -ge 3) {
                        Write-Host "Key type: $($pubKeyParts[0])" -ForegroundColor White
                        Write-Host "Key comment: $($pubKeyParts[2])" -ForegroundColor White
                    }
                }
            } else {
                Write-Host "Note: Public key file not found ($pubKeyPath)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "Status: Key file NOT found" -ForegroundColor Red
        }
    }

    Write-Host
    Write-Host "SSH Key Management Options:" -ForegroundColor Yellow
    Write-Host "[1] Set SSH Key Path" -ForegroundColor White
    Write-Host "[2] Generate New SSH Key" -ForegroundColor White
    Write-Host "[3] Test SSH Key" -ForegroundColor White
    Write-Host "[4] Deploy Public Key to Server" -ForegroundColor White
    Write-Host "[5] Back to Main Menu" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-5)"

    switch ($choice) {
        "1" { Set-SSHKeyPath }
        "2" { Generate-SSHKey }
        "3" { Test-SSHConnection }
        "4" { Deploy-SSHPublicKey }
        "5" { Show-MainMenu }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            Set-SSHKeys
        }
    }
}

# Set the SSH key path
function Set-SSHKeyPath {
    Clear-Host

    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "                  Set SSH Key Path                     " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Display current key path
    if (-not [string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        Write-Host "Current key path: $($Config.ssh.key_path)" -ForegroundColor Yellow
    } else {
        Write-Host "No SSH key path currently configured." -ForegroundColor Yellow
    }

    # First try to detect existing keys
    $detectedKeyPath = Find-SSHKey
    if (-not [string]::IsNullOrWhiteSpace($detectedKeyPath)) {
        Write-Host "`nDetected SSH key at: $detectedKeyPath" -ForegroundColor Green
        $useDetected = Read-Host "Use this key? (y/n)"

        if ($useDetected.ToLower() -eq "y") {
            $Config.ssh.key_path = $detectedKeyPath
            Write-Host "Key path set to: $detectedKeyPath" -ForegroundColor Green

            # Save configuration
            Save-Configuration
            Return-ToMainMenu
            return
        }
    }

    # Manually specify a key path
    Write-Host "`nEnter the path to your SSH private key:" -ForegroundColor White
    $keyPath = Read-Host "SSH key path"

    if ([string]::IsNullOrWhiteSpace($keyPath)) {
        Write-Host "No path provided. Operation cancelled." -ForegroundColor Yellow
        Return-ToMainMenu
        return
    }

    # Validate the path
    if (Test-Path -Path $keyPath) {
        $Config.ssh.key_path = $keyPath
        Write-Host "Key path set to: $keyPath" -ForegroundColor Green

        # Save configuration
        Save-Configuration
    } else {
        Write-Host "The specified file does not exist: $keyPath" -ForegroundColor Red
        $createEmpty = Read-Host "Create directory structure for this path? (y/n)"

        if ($createEmpty.ToLower() -eq "y") {
            try {
                $keyDir = Split-Path -Path $keyPath -Parent
                if (-not (Test-Path -Path $keyDir)) {
                    New-Item -Path $keyDir -ItemType Directory -Force | Out-Null
                }

                $Config.ssh.key_path = $keyPath
                Write-Host "Key path set to: $keyPath (Note: key file does not exist yet)" -ForegroundColor Yellow

                # Save configuration
                Save-Configuration
            } catch {
                Write-Host "Error creating directory structure: $_" -ForegroundColor Red
            }
        }
    }

    Return-ToMainMenu
}

# Generate a new SSH key
function Generate-SSHKey {
    Clear-Host

    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "                 Generate SSH Key                      " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Check if ssh-keygen is available
    try {
        $sshKeygenVersion = & ssh-keygen -V 2>&1

        if ($LASTEXITCODE -ne 0) {
            Write-Host "ssh-keygen is not available on this system." -ForegroundColor Red
            Write-Host "Please install OpenSSH or ensure it's in your PATH." -ForegroundColor Red
            Return-ToMainMenu
            return
        }
    } catch {
        Write-Host "ssh-keygen is not available on this system." -ForegroundColor Red
        Write-Host "Please install OpenSSH or ensure it's in your PATH." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Prompt for key location
    Write-Host "Default key location is: $HOME\.ssh\id_rsa" -ForegroundColor Yellow
    $useDefault = Read-Host "Use default location? (y/n)"

    $keyPath = ""
    if ($useDefault.ToLower() -eq "y") {
        $keyPath = Join-Path -Path $HOME -ChildPath ".ssh\id_rsa"

        # Create .ssh directory if it doesn't exist
        $sshDir = Join-Path -Path $HOME -ChildPath ".ssh"
        if (-not (Test-Path -Path $sshDir)) {
            try {
                New-Item -Path $sshDir -ItemType Directory -Force | Out-Null
                Write-Host "Created SSH directory: $sshDir" -ForegroundColor Green
            } catch {
                Write-Host "Failed to create SSH directory: $_" -ForegroundColor Red
                Return-ToMainMenu
                return
            }
        }
    } else {
        $keyPath = Read-Host "Enter the path for the new SSH key"

        if ([string]::IsNullOrWhiteSpace($keyPath)) {
            Write-Host "No path provided. Operation cancelled." -ForegroundColor Yellow
            Return-ToMainMenu
            return
        }

        # Create parent directory if it doesn't exist
        $keyDir = Split-Path -Path $keyPath -Parent
        if (-not (Test-Path -Path $keyDir)) {
            try {
                New-Item -Path $keyDir -ItemType Directory -Force | Out-Null
                Write-Host "Created directory: $keyDir" -ForegroundColor Green
            } catch {
                Write-Host "Failed to create directory: $_" -ForegroundColor Red
                Return-ToMainMenu
                return
            }
        }
    }

    # Check if key already exists
    if (Test-Path -Path $keyPath) {
        $overwrite = Read-Host "Key already exists at $keyPath. Overwrite? (y/n)"

        if ($overwrite.ToLower() -ne "y") {
            Write-Host "Operation cancelled." -ForegroundColor Yellow
            Return-ToMainMenu
            return
        }
    }

    # Prompt for key comment (usually email)
    $comment = Read-Host "Enter a comment for the key (e.g., your email)"
    if ([string]::IsNullOrWhiteSpace($comment)) {
        $comment = "$($env:USERNAME)@$($env:COMPUTERNAME)"
    }

    # Generate the key
    Write-Host "`nGenerating SSH key..." -ForegroundColor Yellow

    try {
        # Use -N for empty passphrase for automation purposes
        # Replace with passphrase prompt for more security if needed
        & ssh-keygen -t rsa -b 4096 -C $comment -f $keyPath -N '""'

        if ($LASTEXITCODE -eq 0) {
            Write-Host "SSH key successfully generated at: $keyPath" -ForegroundColor Green

            # Update configuration with the new key path
            $Config.ssh.key_path = $keyPath
            Save-Configuration

            # Display the public key
            $pubKeyPath = "$keyPath.pub"
            if (Test-Path -Path $pubKeyPath) {
                $pubKey = Get-Content -Path $pubKeyPath -Raw
                Write-Host "`nPublic Key:" -ForegroundColor Yellow
                Write-Host $pubKey -ForegroundColor Cyan

                Write-Host "`nDeploy this key to the remote server?" -ForegroundColor Yellow
                $deployKey = Read-Host "(y/n)"

                if ($deployKey.ToLower() -eq "y") {
                    Deploy-SSHPublicKey
                    return
                }
            }
        } else {
            Write-Host "Failed to generate SSH key." -ForegroundColor Red
        }
    } catch {
        Write-Host "Error generating SSH key: $_" -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Deploy the public key to the remote server
function Deploy-SSHPublicKey {
    Clear-Host

    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Deploy SSH Public Key                   " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host

    # Verify SSH configuration
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "No SSH configuration loaded. Please set up your environment first." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    if ([string]::IsNullOrWhiteSpace($Config.ssh.host) -or
        [string]::IsNullOrWhiteSpace($Config.ssh.username)) {
        Write-Host "SSH host or username not configured." -ForegroundColor Red
        Write-Host "Please update your configuration with valid SSH settings." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Verify key path
    if ([string]::IsNullOrWhiteSpace($Config.ssh.key_path)) {
        Write-Host "No SSH key path configured." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Check if public key exists
    $pubKeyPath = "$($Config.ssh.key_path).pub"
    if (-not (Test-Path -Path $pubKeyPath)) {
        Write-Host "Public key not found at: $pubKeyPath" -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Read the public key
    $pubKey = Get-Content -Path $pubKeyPath -Raw
    if ([string]::IsNullOrWhiteSpace($pubKey)) {
        Write-Host "Public key file is empty." -ForegroundColor Red
        Return-ToMainMenu
        return
    }

    # Display connection information
    Write-Host "Will deploy public key to:" -ForegroundColor Yellow
    Write-Host "Host: $($Config.ssh.host)" -ForegroundColor White
    Write-Host "Username: $($Config.ssh.username)" -ForegroundColor White
    Write-Host "Port: $($Config.ssh.port)" -ForegroundColor White
    Write-Host

    # Prompt for credentials if not using SSH key already
    $password = Read-Host "Enter password for $($Config.ssh.username)@$($Config.ssh.host)" -AsSecureString

    if ($null -eq $password) {
        Write-Host "No password provided. Operation cancelled." -ForegroundColor Yellow
        Return-ToMainMenu
        return
    }

    # Convert the secure string to plain text for SSH usage
    try {
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($password)
        $plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
    } finally {
        if ($BSTR -ne [IntPtr]::Zero) {
            [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
        }
    }

    Write-Host "Deploying public key..." -ForegroundColor Yellow

    try {
        # First, create the .ssh directory if it doesn't exist
        # Then append our key to authorized_keys
        # Finally, fix permissions
        $remoteCommands = @(
            "mkdir -p ~/.ssh",
            "chmod 700 ~/.ssh",
            "echo '$pubKey' >> ~/.ssh/authorized_keys",
            "chmod 600 ~/.ssh/authorized_keys"
        )

        $remoteScript = $remoteCommands -join "; "

        # Use sshpass to pass the password
        # Note: This requires sshpass to be installed or use another method
        # This is a simplified example; in production, consider using a more secure approach
        $process = New-Object System.Diagnostics.Process
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "ssh"
        $startInfo.Arguments = "-o StrictHostKeyChecking=accept-new -p $($Config.ssh.port) $($Config.ssh.username)@$($Config.ssh.host) '$remoteScript'"
        $startInfo.RedirectStandardInput = $true
        $startInfo.RedirectStandardOutput = $true
        $startInfo.RedirectStandardError = $true
        $startInfo.UseShellExecute = $false
        $process.StartInfo = $startInfo

        $process.Start() | Out-Null
        $process.StandardInput.WriteLine($plainPassword)
        $process.StandardInput.Close()

        $output = $process.StandardOutput.ReadToEnd()
        $errorOutput = $process.StandardError.ReadToEnd()
        $process.WaitForExit()

        if ($process.ExitCode -eq 0) {
            Write-Host "Public key successfully deployed!" -ForegroundColor Green

            # Test the connection using the key
            Write-Host "`nTesting SSH connection with the deployed key..." -ForegroundColor Yellow
            Test-SSHConnection
            return
        } else {
            Write-Host "Failed to deploy public key. Exit code: $($process.ExitCode)" -ForegroundColor Red
            if (-not [string]::IsNullOrWhiteSpace($errorOutput)) {
                Write-Host "Error: $errorOutput" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "Error deploying public key: $_" -ForegroundColor Red
    }

    Return-ToMainMenu
}

# Helper function to find SSH key
function Find-SSHKey {
    # Common locations for SSH keys
    $possiblePaths = @(
        (Join-Path -Path $HOME -ChildPath ".ssh\id_rsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_dsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_ecdsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_ed25519")
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path -Path $path) {
            return $path
        }
    }

    return $null
}

# Helper function to save configuration
function Save-Configuration {
    # Create config directory if needed
    if (-not (Test-Path -Path $ConfigDir)) {
        try {
            New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
        } catch {
            Write-Host "Failed to create configuration directory: $_" -ForegroundColor Red
            return $false
        }
    }

    try {
        # Ensure we have a project name and environment
        if ($null -ne $Config -and $Config.project -and $Config.project.name -and -not [string]::IsNullOrWhiteSpace($DeploymentEnvironment)) {
            $configFile = Join-Path -Path $ConfigDir -ChildPath "$($Config.project.name)-$DeploymentEnvironment.json"
            $Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configFile -Encoding UTF8
            Write-Host "Configuration saved to: $configFile" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Cannot save configuration: missing project name or environment." -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error saving configuration: $_" -ForegroundColor Red
        return $false
    }
}

Start-Script
Show-MainMenu
