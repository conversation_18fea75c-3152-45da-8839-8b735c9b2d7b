/**
 * Git Size Comparison Chart Module
 * Creates visualizations comparing repository sizes
 */
const GitSizeComparisonChart = {
    // Module configuration
    config: {
        chartId: 'size-comparison-chart',
        containerId: 'size-chart-container',
        loadingId: 'size-chart-loading',
        errorId: 'size-chart-error',
        messageId: 'size-chart-message',
        maxRepos: 10, // Maximum number of repositories to show in chart
        colors: {
            background: [
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 99, 132, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
                'rgba(255, 159, 64, 0.5)',
                'rgba(199, 199, 199, 0.5)',
                'rgba(83, 102, 255, 0.5)',
                'rgba(40, 159, 64, 0.5)',
                'rgba(210, 199, 199, 0.5)'
            ],
            border: [
                'rgba(54, 162, 235, 1)',
                'rgba(255, 99, 132, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)',
                'rgba(199, 199, 199, 1)',
                'rgba(83, 102, 255, 1)',
                'rgba(40, 159, 64, 1)',
                'rgba(210, 199, 199, 1)'
            ]
        }
    },
    
    // Module state
    state: {
        chart: null
    },
    
    /**
     * Initialize the chart module
     * @returns {Object} This module for chaining
     */
    init() {
        console.log('GitSizeComparisonChart.init() - Initialization complete');
        return this;
    },
    
    /**
     * Clean up chart instance
     */
    cleanupChart() {
        if (this.state.chart) {
            this.state.chart.destroy();
            this.state.chart = null;
            console.log('Chart instance cleaned up');
        }
    },
    
    /**
     * Initialize chart with repository data
     * @param {Array} repositories - Array of repository objects
     * @param {string} selectedRepo - Name of the selected repository
     */
    initChart(repositories, selectedRepo) {
        console.log(`initChart called with ${repositories.length} repositories`);
        this.renderChart(repositories, selectedRepo);
    },
    
    /**
     * Render the size comparison chart
     * @param {Array} repositories - Array of repository objects
     * @param {string} selectedRepo - Name of the selected repository
     */
    renderChart(repositories, selectedRepo) {
        try {
            // Get elements
            const container = document.getElementById(this.config.containerId);
            const canvas = document.getElementById(this.config.chartId);
            const loading = document.getElementById(this.config.loadingId);
            const error = document.getElementById(this.config.errorId);
            
            if (!container || !canvas) {
                console.error('Chart container elements not found');
                return;
            }
            
            // Reset UI state
            if (loading) loading.style.display = 'flex';
            if (error) error.style.display = 'none';
            if (canvas) canvas.style.display = 'none';
            
            // Clean up existing chart
            this.cleanupChart();
            
            // Check if we have repositories
            if (!repositories || !Array.isArray(repositories) || repositories.length === 0) {
                if (loading) loading.style.display = 'none';
                if (error) {
                    error.textContent = 'No repositories found';
                    error.style.display = 'block';
                }
                return;
            }
            
            // Process data for chart
            const chartData = this.processRepositoriesForSizeChart(repositories, selectedRepo);
            
            // Create chart
            this.createChart(canvas, chartData);
            
            // Hide loading, show canvas
            if (loading) loading.style.display = 'none';
            if (canvas) canvas.style.display = 'block';
            
        } catch (error) {
            console.error('Error rendering size comparison chart:', error);
            
            // Show error message
            const loading = document.getElementById(this.config.loadingId);
            const errorElement = document.getElementById(this.config.errorId);
            const canvas = document.getElementById(this.config.chartId);
            
            if (loading) loading.style.display = 'none';
            if (canvas) canvas.style.display = 'none';
            
            if (errorElement) {
                errorElement.textContent = error.message || 'Failed to load size comparison chart';
                errorElement.style.display = 'block';
            }
        }
    },
    
    /**
     * Process repositories data for size chart
     * @param {Array} repositories - Array of repository objects
     * @param {string} selectedRepo - Name of the selected repository
     * @returns {Object} Chart data
     */
    processRepositoriesForSizeChart(repositories, selectedRepo) {
        // Sort repositories by size (descending)
        const sortedRepos = [...repositories].sort((a, b) => (b.size || 0) - (a.size || 0));
        
        // Take top N repositories
        const topRepos = sortedRepos.slice(0, this.config.maxRepos);
        
        // Prepare data for chart
        const labels = [];
        const sizes = [];
        const backgroundColors = [];
        const borderColors = [];
        
        // Process each repository
        topRepos.forEach((repo, index) => {
            labels.push(repo.name);
            sizes.push(repo.size || 0);
            
            // Highlight selected repository
            if (repo.name === selectedRepo) {
                backgroundColors.push('rgba(255, 99, 132, 0.5)');
                borderColors.push('rgba(255, 99, 132, 1)');
            } else {
                const colorIndex = index % this.config.colors.background.length;
                backgroundColors.push(this.config.colors.background[colorIndex]);
                borderColors.push(this.config.colors.border[colorIndex]);
            }
        });
        
        return {
            labels,
            datasets: [{
                label: 'Repository Size (bytes)',
                data: sizes,
                backgroundColor: backgroundColors,
                borderColor: borderColors,
                borderWidth: 1
            }]
        };
    },
    
    /**
     * Create the chart with the provided data
     * @param {HTMLCanvasElement} canvas - Canvas element
     * @param {Object} chartData - Data for the chart
     */
    createChart(canvas, chartData) {
        if (!canvas) {
            console.error('Canvas element not found');
            return;
        }
        
        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not available');
            return;
        }
        
        // Clean up existing chart
        this.cleanupChart();
        
        // Create new chart
        this.state.chart = new Chart(canvas, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y', // Horizontal bar chart
                scales: {
                    x: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                // Format bytes to human-readable format
                                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                                if (value === 0) return '0 Bytes';
                                const i = Math.floor(Math.log(value) / Math.log(1024));
                                return parseFloat((value / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
                            }
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Repository Size Comparison'
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                // Format bytes to human-readable format
                                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                                if (value === 0) return '0 Bytes';
                                const i = Math.floor(Math.log(value) / Math.log(1024));
                                return parseFloat((value / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
                            }
                        }
                    }
                }
            }
        });
        
        console.log('Size comparison chart created');
    }
};

// Expose the module globally
window.GitSizeComparisonChart = GitSizeComparisonChart;
