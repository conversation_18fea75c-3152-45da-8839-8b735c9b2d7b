/**
 * Git Summary View Module
 * Displays summary statistics for repositories
 */
const GitSummaryView = {
    // Module state
    state: {
        container: null,
        initialized: false
    },
    
    /**
     * Initialize the summary view module
     * @returns {Object} This module for chaining
     */
    init() {
        console.log('GitSummaryView.init() - Starting initialization');
        
        // Create summary container
        this.createSummaryContainer();
        
        // Mark as initialized
        this.state.initialized = true;
        
        console.log('GitSummaryView.init() - Initialization complete');
        return this;
    },
    
    /**
     * Create the summary container
     */
    createSummaryContainer() {
        // Get the parent container
        const gitRepoSection = document.getElementById('git-repo-section');
        if (!gitRepoSection) {
            console.error('Git repository section not found');
            return;
        }
        
        // Check if the summary container already exists
        const existingSummary = document.getElementById('git-summary-row');
        if (existingSummary) {
            console.log('Summary container already exists, using existing one');
            this.state.container = existingSummary;
            return;
        }
        
        // Create the container div for the summary
        const summaryRow = document.createElement('div');
        summaryRow.className = 'row mb-4';
        summaryRow.id = 'git-summary-row';
        
        // Create the summary cards
        summaryRow.innerHTML = `
            <!-- Repository Count Card -->
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-code-branch fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Total Repositories</h5>
                        <h2 id="summary-repo-count" class="display-4">0</h2>
                    </div>
                </div>
            </div>
            
            <!-- Total Size Card -->
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-2x text-success mb-2"></i>
                        <h5 class="card-title">Total Size</h5>
                        <h2 id="summary-total-size" class="display-4">0 KB</h2>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity Card -->
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Recent Commits</h5>
                        <h2 id="summary-recent-commits" class="display-4">0</h2>
                        <small class="text-muted">Last 24 hours</small>
                    </div>
                </div>
            </div>
            
            <!-- Health Status Card -->
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-heartbeat fa-2x text-danger mb-2"></i>
                        <h5 class="card-title">Overall Health</h5>
                        <div class="progress mb-2">
                            <div id="summary-health-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <p id="summary-health-message" class="mb-0">No repositories</p>
                    </div>
                </div>
            </div>
        `;
        
        // Add the summary row to the parent container
        gitRepoSection.prepend(summaryRow);
        
        // Store reference to the container
        this.state.container = summaryRow;
        
        console.log('Summary container created');
    },
    
    /**
     * Update the summary view with repository data
     * @param {Array} repositories - Array of repository objects
     */
    updateSummaryView(repositories) {
        if (!this.state.initialized) {
            console.error('Summary view not initialized');
            return;
        }
        
        if (!repositories || !Array.isArray(repositories)) {
            console.error('Invalid repositories data');
            return;
        }
        
        // Update repository count with animation
        const repoCountElement = document.getElementById('summary-repo-count');
        if (repoCountElement) {
            const currentValue = parseInt(repoCountElement.textContent) || 0;
            const newValue = repositories.length;
            
            if (currentValue !== newValue) {
                // Add pulsing animation
                repoCountElement.classList.add('pulsing');
                
                // Update the value
                repoCountElement.textContent = newValue;
                
                // Remove animation after a delay
                setTimeout(() => {
                    repoCountElement.classList.remove('pulsing');
                }, 1000);
            }
        }
        
        // Update total size with animation
        const totalSizeElement = document.getElementById('summary-total-size');
        if (totalSizeElement) {
            const totalSize = repositories.reduce((sum, repo) => sum + (repo.size || 0), 0);
            const formattedSize = this.formatSize(totalSize);
            
            if (totalSizeElement.textContent !== formattedSize) {
                // Add pulsing animation
                totalSizeElement.classList.add('pulsing');
                
                // Update the value
                totalSizeElement.textContent = formattedSize;
                
                // Remove animation after a delay
                setTimeout(() => {
                    totalSizeElement.classList.remove('pulsing');
                }, 1000);
            }
        }
        
        // Update recent commits with animation
        const recentCommitsElement = document.getElementById('summary-recent-commits');
        if (recentCommitsElement) {
            const recentCommits = this.calculateRecentCommits(repositories);
            const currentValue = parseInt(recentCommitsElement.textContent) || 0;
            
            if (currentValue !== recentCommits) {
                // Add pulsing animation
                recentCommitsElement.classList.add('pulsing');
                
                // Update the value
                recentCommitsElement.textContent = recentCommits;
                
                // Remove animation after a delay
                setTimeout(() => {
                    recentCommitsElement.classList.remove('pulsing');
                }, 1000);
            }
        }
        
        // Update health status
        this.updateHealthStatus(repositories);
    },
    
    /**
     * Calculate number of recent commits (within 24 hours)
     * @param {Array} repositories - Array of repository objects
     * @returns {number} Count of recent commits
     */
    calculateRecentCommits(repositories) {
        // Count recent commits based on repository data
        let recentCommits = 0;
        
        // Get current time and 24 hours ago time
        const now = new Date();
        const oneDayAgo = new Date(now);
        oneDayAgo.setHours(now.getHours() - 24);
        
        // Check each repository for recent commits
        repositories.forEach(repo => {
            if (repo.last_commit) {
                const lastCommitDate = new Date(repo.last_commit);
                if (lastCommitDate >= oneDayAgo) {
                    // If the repository has been updated in the last 24 hours
                    // count it as 1 recent commit
                    recentCommits += 1;
                }
            }
        });
        
        return recentCommits;
    },
    
    /**
     * Update health status based on repository data
     * @param {Array} repositories - Array of repository objects
     */
    updateHealthStatus(repositories) {
        const healthProgressElement = document.getElementById('summary-health-progress');
        const healthMessageElement = document.getElementById('summary-health-message');
        
        if (!healthProgressElement || !healthMessageElement) {
            return;
        }
        
        if (repositories.length === 0) {
            healthProgressElement.style.width = '0%';
            healthProgressElement.setAttribute('aria-valuenow', '0');
            healthProgressElement.className = 'progress-bar bg-secondary';
            healthMessageElement.textContent = 'No repositories';
            return;
        }
        
        // Calculate average health score
        let totalScore = 0;
        let activeRepos = 0;
        
        repositories.forEach(repo => {
            if (repo.health && repo.health.score !== undefined) {
                totalScore += repo.health.score;
                activeRepos++;
            }
        });
        
        const averageScore = activeRepos > 0 ? Math.round(totalScore / activeRepos) : 0;
        
        // Get current score from the progress bar
        const currentScore = parseInt(healthProgressElement.getAttribute('aria-valuenow')) || 0;
        
        // Check if the score has changed
        if (currentScore !== averageScore) {
            // Add animation class
            healthProgressElement.classList.add('pulsing');
            
            // Update progress bar with animation
            healthProgressElement.style.transition = 'width 0.5s ease-in-out';
            healthProgressElement.style.width = `${averageScore}%`;
            healthProgressElement.setAttribute('aria-valuenow', averageScore);
            
            // Set color and message based on average score
            let colorClass = 'bg-secondary';
            let message = 'Unknown health status';
            
            if (averageScore >= 80) {
                colorClass = 'bg-success';
                message = 'Excellent - Active development';
            } else if (averageScore >= 60) {
                colorClass = 'bg-info';
                message = 'Good - Regular updates';
            } else if (averageScore >= 40) {
                colorClass = 'bg-warning';
                message = 'Fair - Occasional updates';
            } else if (averageScore > 0) {
                colorClass = 'bg-danger';
                message = 'Poor - Infrequent updates';
            }
            
            healthProgressElement.className = `progress-bar ${colorClass}`;
            healthMessageElement.textContent = message;
            
            // Remove animation class after transition completes
            setTimeout(() => {
                healthProgressElement.classList.remove('pulsing');
                healthProgressElement.style.transition = '';
            }, 1000);
        }
    },
    
    /**
     * Format size in bytes to human-readable format
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    formatSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Expose the module globally
window.GitSummaryView = GitSummaryView;
