# Database Service Advanced Integration

This document covers advanced integration topics, security, monitoring, troubleshooting, and maintenance procedures for the Database Service and UI integration.

## Security Integration

### JWT Token Security

**Backend Token Generation:**
```cpp
// C++ JWT token creation
class SecurityManager {
    TokenPair generateTokens(const std::string& username) {
        auto now = std::chrono::system_clock::now();
        auto accessExpiry = now + std::chrono::seconds(accessTokenExpiration_);
        auto refreshExpiry = now + std::chrono::seconds(refreshTokenExpiration_);
        
        nlohmann::json accessPayload = {
            {"sub", username},
            {"iat", std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count()},
            {"exp", std::chrono::duration_cast<std::chrono::seconds>(accessExpiry.time_since_epoch()).count()},
            {"type", "access"}
        };
        
        return {
            .accessToken = jwt::create()
                .set_issuer("database-service")
                .set_payload_claim("data", accessPayload)
                .sign(jwt::algorithm::hs256{jwtSecret_}),
            .refreshToken = generateRefreshToken(username, refreshExpiry)
        };
    }
};
```

**Frontend Token Storage:**
```typescript
// Secure token storage
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'db_access_token';
  private static readonly REFRESH_TOKEN_KEY = 'db_refresh_token';
  
  static setTokens(accessToken: string, refreshToken: string): void {
    // Use httpOnly cookies in production
    if (process.env.NODE_ENV === 'production') {
      document.cookie = `${this.ACCESS_TOKEN_KEY}=${accessToken}; Secure; HttpOnly; SameSite=Strict`;
      document.cookie = `${this.REFRESH_TOKEN_KEY}=${refreshToken}; Secure; HttpOnly; SameSite=Strict`;
    } else {
      sessionStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
  }
}
```

### CORS Configuration

**Backend CORS Setup:**
```cpp
// C++ CORS configuration
struct CorsConfig {
    bool enabled = true;
    std::vector<std::string> allowedOrigins = {
        "https://git.chcit.org",
        "http://localhost:3000"  // Development only
    };
    std::vector<std::string> allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
    std::vector<std::string> allowedHeaders = {"Content-Type", "Authorization"};
    bool allowCredentials = true;
    int maxAge = 86400;
};
```

**Frontend Request Configuration:**
```typescript
// TypeScript fetch configuration
const apiRequest = async (url: string, options: RequestInit = {}) => {
  const defaultOptions: RequestInit = {
    credentials: 'include',  // Include cookies
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };
  
  return fetch(url, { ...defaultOptions, ...options });
};
```

### SSL/TLS Integration

**Certificate Management:**
- Let's Encrypt certificates managed by Nginx
- Automatic renewal via certbot
- Database service uses client certificates for PostgreSQL connections

**Security Headers (Nginx):**
```nginx
# Security headers applied to all database service requests
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

## Monitoring and Metrics

### Backend Metrics Collection

**C++ Metrics Implementation:**
```cpp
class DatabaseMetrics {
    struct ConnectionPoolMetrics {
        std::atomic<size_t> activeConnections{0};
        std::atomic<size_t> idleConnections{0};
        std::atomic<size_t> totalConnections{0};
        std::atomic<size_t> waitingRequests{0};
    };
    
    struct QueryMetrics {
        std::atomic<uint64_t> totalQueries{0};
        std::atomic<uint64_t> successfulQueries{0};
        std::atomic<uint64_t> failedQueries{0};
        std::atomic<double> averageResponseTime{0.0};
    };
    
    nlohmann::json exportMetrics() const {
        return {
            {"connection_pool", {
                {"active", connectionPool_.activeConnections.load()},
                {"idle", connectionPool_.idleConnections.load()},
                {"total", connectionPool_.totalConnections.load()},
                {"waiting", connectionPool_.waitingRequests.load()}
            }},
            {"queries", {
                {"total", query_.totalQueries.load()},
                {"successful", query_.successfulQueries.load()},
                {"failed", query_.failedQueries.load()},
                {"avg_response_time", query_.averageResponseTime.load()}
            }},
            {"timestamp", std::chrono::system_clock::now()}
        };
    }
};
```

**Frontend Metrics Visualization:**
```typescript
// React component for metrics display
interface MetricsData {
  connection_pool: {
    active: number;
    idle: number;
    total: number;
    waiting: number;
  };
  queries: {
    total: number;
    successful: number;
    failed: number;
    avg_response_time: number;
  };
  timestamp: string;
}

const MetricsChart: React.FC = () => {
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/database-api/database/metrics');
        const data = await response.json();
        setMetrics(data);
      } catch (error) {
        console.error('Failed to fetch metrics:', error);
      }
    };
    
    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="metrics-dashboard">
      {metrics && (
        <>
          <ConnectionPoolChart data={metrics.connection_pool} />
          <QueryMetricsChart data={metrics.queries} />
        </>
      )}
    </div>
  );
};
```

### Health Monitoring

**Backend Health Checks:**
```cpp
class HealthMonitor {
    struct HealthStatus {
        bool databaseConnected;
        bool apiServerRunning;
        bool certificatesValid;
        double cpuUsage;
        double memoryUsage;
        std::string status; // "healthy", "degraded", "unhealthy"
    };
    
    HealthStatus checkHealth() {
        HealthStatus status;
        status.databaseConnected = testDatabaseConnection();
        status.apiServerRunning = true; // If we're responding, API is running
        status.certificatesValid = validateCertificates();
        status.cpuUsage = getCpuUsage();
        status.memoryUsage = getMemoryUsage();
        
        if (status.databaseConnected && status.certificatesValid && 
            status.cpuUsage < 80.0 && status.memoryUsage < 90.0) {
            status.status = "healthy";
        } else if (status.databaseConnected) {
            status.status = "degraded";
        } else {
            status.status = "unhealthy";
        }
        
        return status;
    }
};
```

**Frontend Health Display:**
```typescript
const HealthIndicator: React.FC = () => {
  const [health, setHealth] = useState<HealthStatus | null>(null);
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#28a745';
      case 'degraded': return '#ffc107';
      case 'unhealthy': return '#dc3545';
      default: return '#6c757d';
    }
  };
  
  return (
    <div className="health-indicator">
      <div 
        className="status-dot" 
        style={{ backgroundColor: getStatusColor(health?.status || 'unknown') }}
      />
      <span>{health?.status || 'Unknown'}</span>
    </div>
  );
};
```

## Performance Optimization

### Backend Optimization

**Connection Pool Tuning:**
```cpp
// Optimal connection pool configuration
struct ConnectionPoolConfig {
    size_t minConnections = 5;      // Minimum idle connections
    size_t maxConnections = 20;     // Maximum total connections
    size_t maxWaitTime = 30;        // Max wait time in seconds
    size_t idleTimeout = 300;       // Idle connection timeout
    size_t connectionLifetime = 3600; // Max connection lifetime
};
```

**Query Optimization:**
```cpp
// Prepared statement caching
class QueryCache {
    std::unordered_map<std::string, std::shared_ptr<PreparedStatement>> cache_;
    std::mutex cacheMutex_;
    
public:
    std::shared_ptr<PreparedStatement> getOrCreate(const std::string& query) {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        
        auto it = cache_.find(query);
        if (it != cache_.end()) {
            return it->second;
        }
        
        auto stmt = std::make_shared<PreparedStatement>(query);
        cache_[query] = stmt;
        return stmt;
    }
};
```

### Frontend Optimization

**Component Optimization:**
```typescript
// Memoized components for better performance
const MetricsDisplay = React.memo<MetricsProps>(({ metrics }) => {
  return (
    <div className="metrics-container">
      {/* Render metrics */}
    </div>
  );
});

// Debounced API calls
const useDebouncedApi = (apiCall: () => Promise<any>, delay: number) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const debouncedCall = useCallback(
    debounce(async () => {
      setLoading(true);
      try {
        const result = await apiCall();
        setData(result);
      } finally {
        setLoading(false);
      }
    }, delay),
    [apiCall, delay]
  );
  
  return { data, loading, call: debouncedCall };
};
```

**Bundle Optimization:**
```typescript
// Code splitting for better loading performance
const DatabaseManagement = lazy(() => import('./pages/DatabaseManagement'));
const MetricsPage = lazy(() => import('./pages/MetricsPage'));
const CredentialsPage = lazy(() => import('./pages/CredentialsPage'));

const App: React.FC = () => {
  return (
    <Router>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/databases" element={<DatabaseManagement />} />
          <Route path="/metrics" element={<MetricsPage />} />
          <Route path="/credentials" element={<CredentialsPage />} />
        </Routes>
      </Suspense>
    </Router>
  );
};
```
