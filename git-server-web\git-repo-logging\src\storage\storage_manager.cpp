#include "storage_manager.hpp"
#include "memory_storage.hpp"
#include "database_storage.hpp"
#include "file_storage.hpp"
#include <algorithm>
#include <fstream>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <chrono>
#include <thread>
#include <filesystem>

namespace logging {

StorageManager::StorageManager() {
    // Default constructor
}

bool StorageManager::initialize(const std::string& configPath) {
    try {
        // Load configuration from JSON file
        std::ifstream configFile(configPath);
        if (!configFile.is_open()) {
            std::cerr << "Failed to open config file: " << configPath << std::endl;
            return false;
        }

        Json::Value config;
        Json::CharReaderBuilder reader;
        std::string errors;

        if (!Json::parseFromStream(reader, configFile, &config, &errors)) {
            std::cerr << "Failed to parse config file: " << errors << std::endl;
            return false;
        }

        // Clear existing tiers
        std::lock_guard<std::mutex> lock(mutex_);
        tiers_.clear();

        // Initialize memory tier if enabled
        if (config["tiers"]["memory"]["enabled"].asBool()) {
            size_t maxEntries = config["tiers"]["memory"]["max_entries"].asUInt64();
            int maxAgeMinutes = config["tiers"]["memory"]["max_age_minutes"].asInt();

            auto memoryTier = std::make_shared<MemoryStorage>(
                maxEntries,
                std::chrono::minutes(maxAgeMinutes)
            );

            tiers_.push_back(memoryTier);
            std::cout << "Memory tier initialized" << std::endl;
        }

        // Initialize database tier if enabled
        if (config["tiers"]["database"]["enabled"].asBool()) {
            std::string connectionString = config["tiers"]["database"]["connection_string"].asString();

            // Check for environment variable if connection string is empty
            if (connectionString.empty()) {
                const char* connStrEnv = std::getenv("DB_CONNECTION_STRING");
                if (connStrEnv) {
                    connectionString = connStrEnv;
                    std::cout << "Using database connection string from environment variable" << std::endl;
                }
            }

            bool structuredOnly = config["tiers"]["database"]["structured_only"].asBool();

            auto databaseTier = std::make_shared<DatabaseStorage>(
                connectionString,
                structuredOnly
            );

            tiers_.push_back(databaseTier);
            std::cout << "Database tier initialized with connection string: " << connectionString << std::endl;
        }

        // Initialize file tier if enabled
        if (config["tiers"]["file"]["enabled"].asBool()) {
            std::string baseDir = config["tiers"]["file"]["base_directory"].asString();
            bool enableCompression = config["tiers"]["file"]["compression"].asBool();
            int compressionAgeDays = config["tiers"]["file"]["compression_age_days"].asInt();

            auto fileTier = std::make_shared<FileStorage>(
                baseDir,
                enableCompression,
                compressionAgeDays
            );

            tiers_.push_back(fileTier);
            std::cout << "File tier initialized" << std::endl;
        }

        // Sort tiers by priority
        std::sort(tiers_.begin(), tiers_.end(),
            [](const std::shared_ptr<StorageTier>& a, const std::shared_ptr<StorageTier>& b) {
                return a->getPriority() < b->getPriority();
            });

        std::cout << "Storage manager initialized with " << tiers_.size() << " tiers" << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error initializing storage manager: " << e.what() << std::endl;
        return false;
    }
}

bool StorageManager::store(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(mutex_);

    // Find the appropriate tier for this entry
    auto tier = findTierForEntry(entry);

    if (!tier) {
        std::cerr << "No suitable storage tier found for log entry" << std::endl;
        return false;
    }

    // If we're using the database tier, store the entry directly
    auto dbTier = std::dynamic_pointer_cast<DatabaseStorage>(tier);
    if (dbTier) {
        return dbTier->store(entry);
    }

    // If we're using the file tier and have unstructured data, convert to JSON first
    auto fileTier = std::dynamic_pointer_cast<FileStorage>(tier);
    if (fileTier && entry.rawLine && !entry.rawLine->empty()) {
        // Create a copy with JSON-formatted data
        LogEntry jsonEntry = entry;
        jsonEntry.message = convertToJson(entry);
        jsonEntry.rawLine = std::nullopt; // Clear raw line since we've processed it

        return fileTier->store(jsonEntry);
    }

    // Otherwise, store the entry as-is
    return tier->store(entry);
}

bool StorageManager::storeBatch(const std::vector<LogEntry>& entries) {
    if (entries.empty()) {
        return true;
    }

    std::lock_guard<std::mutex> lock(mutex_);

    // Group entries by appropriate tier
    std::unordered_map<std::shared_ptr<StorageTier>, std::vector<LogEntry>> tierEntries;

    // Track database and file tiers separately for special handling
    std::shared_ptr<DatabaseStorage> dbTier = nullptr;
    std::shared_ptr<FileStorage> fileTier = nullptr;
    std::vector<LogEntry> dbEntries;
    std::vector<LogEntry> fileEntries;
    std::vector<LogEntry> jsonFileEntries; // For unstructured logs converted to JSON

    for (const auto& entry : entries) {
        auto tier = findTierForEntry(entry);
        if (!tier) {
            std::cerr << "No suitable storage tier found for log entry" << std::endl;
            continue;
        }

        // Check if this is a database or file tier
        auto currentDbTier = std::dynamic_pointer_cast<DatabaseStorage>(tier);
        auto currentFileTier = std::dynamic_pointer_cast<FileStorage>(tier);

        if (currentDbTier) {
            // Store in database tier
            dbTier = currentDbTier;
            dbEntries.push_back(entry);
        } else if (currentFileTier && entry.rawLine && !entry.rawLine->empty()) {
            // Convert unstructured logs to JSON for file tier
            fileTier = currentFileTier;

            // Create a copy with JSON-formatted data
            LogEntry jsonEntry = entry;
            jsonEntry.message = convertToJson(entry);
            jsonEntry.rawLine = std::nullopt; // Clear raw line since we've processed it

            jsonFileEntries.push_back(jsonEntry);
        } else if (currentFileTier) {
            // Store structured logs in file tier as-is
            fileTier = currentFileTier;
            fileEntries.push_back(entry);
        } else {
            // Other tiers (memory, etc.)
            tierEntries[tier].push_back(entry);
        }
    }

    // Store entries in each tier
    bool success = true;

    // Store in database tier
    if (dbTier && !dbEntries.empty()) {
        if (!dbTier->storeBatch(dbEntries)) {
            success = false;
        }
    }

    // Store in file tier
    if (fileTier) {
        // Store regular entries
        if (!fileEntries.empty() && !fileTier->storeBatch(fileEntries)) {
            success = false;
        }

        // Store JSON-converted entries
        if (!jsonFileEntries.empty() && !fileTier->storeBatch(jsonFileEntries)) {
            success = false;
        }
    }

    // Store in other tiers
    for (const auto& [tier, tierEntriesVec] : tierEntries) {
        if (!tier->storeBatch(tierEntriesVec)) {
            success = false;
        }
    }

    return success;
}

std::vector<LogEntry> StorageManager::query(const LogQueryParams& params) {
    std::lock_guard<std::mutex> lock(mutex_);

    std::vector<LogEntry> results;

    // Query each tier
    for (const auto& tier : tiers_) {
        auto tierResults = tier->query(params);

        // Add results to the combined results
        results.insert(results.end(), tierResults.begin(), tierResults.end());

        // Check if we've reached the limit
        if (params.limit > 0 && results.size() >= static_cast<size_t>(params.limit)) {
            results.resize(params.limit);
            break;
        }
    }

    // Sort results by timestamp (newest first)
    std::sort(results.begin(), results.end(),
        [](const LogEntry& a, const LogEntry& b) {
            return a.timestamp > b.timestamp;
        });

    // Apply limit if needed
    if (params.limit > 0 && results.size() > static_cast<size_t>(params.limit)) {
        results.resize(params.limit);
    }

    return results;
}

StorageStats StorageManager::getStats() {
    std::lock_guard<std::mutex> lock(mutex_);

    StorageStats combinedStats;

    // Combine stats from all tiers
    for (const auto& tier : tiers_) {
        auto tierStats = tier->getStats();

        // Add to total entries
        combinedStats.totalEntries += tierStats.totalEntries;

        // Add to source counts
        for (const auto& [source, count] : tierStats.bySource) {
            combinedStats.bySource[source] += count;
        }

        // Add to level counts
        for (const auto& [level, count] : tierStats.byLevel) {
            combinedStats.byLevel[level] += count;
        }

        // Update oldest entry if needed
        if (tierStats.oldestEntry && (!combinedStats.oldestEntry || *tierStats.oldestEntry < *combinedStats.oldestEntry)) {
            combinedStats.oldestEntry = tierStats.oldestEntry;
        }

        // Update newest entry if needed
        if (tierStats.newestEntry && (!combinedStats.newestEntry || *tierStats.newestEntry > *combinedStats.newestEntry)) {
            combinedStats.newestEntry = tierStats.newestEntry;
        }

        // Add to storage size
        combinedStats.storageSize += tierStats.storageSize;
    }

    return combinedStats;
}

bool StorageManager::prune(std::chrono::system_clock::time_point olderThan) {
    std::lock_guard<std::mutex> lock(mutex_);

    bool success = true;

    // Prune each tier
    for (const auto& tier : tiers_) {
        if (!tier->prune(olderThan)) {
            success = false;
        }
    }

    return success;
}

bool StorageManager::compact() {
    std::lock_guard<std::mutex> lock(mutex_);

    bool success = true;

    // Compact each tier
    for (const auto& tier : tiers_) {
        if (!tier->compact()) {
            success = false;
        }
    }

    return success;
}

bool StorageManager::backup(const std::string& backupPath) {
    std::lock_guard<std::mutex> lock(mutex_);

    bool success = true;

    // Backup each tier
    for (size_t i = 0; i < tiers_.size(); ++i) {
        std::string tierBackupPath = backupPath + "_" + tiers_[i]->getName();
        if (!tiers_[i]->backup(tierBackupPath)) {
            success = false;
        }
    }

    return success;
}

void StorageManager::addTier(std::shared_ptr<StorageTier> tier) {
    std::lock_guard<std::mutex> lock(mutex_);

    tiers_.push_back(tier);

    // Re-sort tiers by priority
    std::sort(tiers_.begin(), tiers_.end(),
        [](const std::shared_ptr<StorageTier>& a, const std::shared_ptr<StorageTier>& b) {
            return a->getPriority() < b->getPriority();
        });
}

std::vector<std::shared_ptr<StorageTier>> StorageManager::getTiers() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return tiers_;
}

std::shared_ptr<StorageTier> StorageManager::findTierForEntry(const LogEntry& entry) const {
    // First, try to find a connected database tier
    auto dbTier = findConnectedDatabaseTier();

    // If we have a connected database tier and it can handle the entry, use it
    if (dbTier && dbTier->canHandle(entry)) {
        return dbTier;
    }

    // If the entry is unstructured and we have a database tier, convert to JSON and try again
    if (dbTier && entry.rawLine && !entry.rawLine->empty()) {
        // Create a copy of the entry with JSON-formatted data
        LogEntry jsonEntry = entry;
        jsonEntry.message = convertToJson(entry);
        jsonEntry.rawLine = std::nullopt; // Clear raw line since we've processed it

        // Try database tier with JSON-formatted entry
        if (dbTier->canHandle(jsonEntry)) {
            return dbTier;
        }
    }

    // If database is not available or can't handle the entry, try memory tier
    for (const auto& tier : tiers_) {
        if (tier->getName() == "Memory" && tier->canHandle(entry)) {
            return tier;
        }
    }

    // As a last resort, if database is not connected, use file tier
    if (!dbTier || !dbTier->isConnected()) {
        for (const auto& tier : tiers_) {
            if (tier->getName() == "File" && tier->canHandle(entry)) {
                return tier;
            }
        }
    }

    return nullptr;
}

std::shared_ptr<DatabaseStorage> StorageManager::findConnectedDatabaseTier() const {
    for (const auto& tier : tiers_) {
        auto dbTier = std::dynamic_pointer_cast<DatabaseStorage>(tier);
        if (dbTier && dbTier->isConnected() &&
            dbTier->getCircuitState() != CircuitBreaker::OPEN) {
            return dbTier;
        }
    }
    return nullptr;
}

std::string StorageManager::convertToJson(const LogEntry& entry) const {
    Json::Value root;

    // Add basic fields
    root["timestamp"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
        entry.timestamp.time_since_epoch()).count());
    root["source"] = entry.source;
    root["message"] = entry.message;

    // Add raw line
    if (entry.rawLine) {
        root["raw_data"] = *entry.rawLine;
    }

    // Add optional fields
    if (entry.component) root["component"] = *entry.component;
    if (entry.unit) root["unit"] = *entry.unit;
    if (entry.hostname) root["hostname"] = *entry.hostname;
    if (entry.commit) root["commit"] = *entry.commit;
    if (entry.author) root["author"] = *entry.author;

    // Add metadata
    for (const auto& [key, value] : entry.metadata) {
        root[key] = value;
    }

    // Convert to string
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}

} // namespace logging
