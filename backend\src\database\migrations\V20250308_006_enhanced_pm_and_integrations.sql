-- Enhanced Project Management and Integrations Migration
-- Version: V20250308_006
-- Description: Adds advanced project management features and external tool integrations

-- Project Templates Table
CREATE TABLE IF NOT EXISTS project_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    structure JSONB NOT NULL, -- Template structure including categories, milestones, etc.
    git_settings JSONB, -- Default git configuration
    created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Project Time Tracking
CREATE TABLE IF NOT EXISTS time_entries (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    improvement_id INTEGER REFERENCES improvements(id) ON DELETE CASCADE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    description TEXT,
    category VARCHAR(50), -- 'development', 'review', 'testing', etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- External Tool Integrations
CREATE TABLE IF NOT EXISTS external_integrations (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    tool_type VARCHAR(50) NOT NULL, -- 'jira', 'github', 'gitlab', 'slack', etc.
    config JSONB NOT NULL, -- Configuration including API keys (encrypted), webhooks, etc.
    sync_enabled BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Integration Sync History
CREATE TABLE IF NOT EXISTS integration_sync_history (
    id SERIAL PRIMARY KEY,
    integration_id INTEGER REFERENCES external_integrations(id) ON DELETE CASCADE,
    sync_type VARCHAR(50) NOT NULL, -- 'full', 'incremental'
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'running', -- 'running', 'success', 'failed'
    items_processed INTEGER DEFAULT 0,
    error_details TEXT
);

-- Project Dependencies
CREATE TABLE IF NOT EXISTS project_dependencies (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    depends_on_project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    dependency_type VARCHAR(50) NOT NULL, -- 'blocks', 'requires', 'relates_to'
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, depends_on_project_id)
);

-- Project Metrics
CREATE TABLE IF NOT EXISTS project_metrics (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    metrics_data JSONB NOT NULL, -- Store various project metrics
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, metric_date)
);

-- Automated Workflows
CREATE TABLE IF NOT EXISTS automated_workflows (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL, -- 'on_git_push', 'on_improvement_update', etc.
    trigger_conditions JSONB, -- Conditions that trigger the workflow
    actions JSONB NOT NULL, -- Actions to perform
    is_active BOOLEAN DEFAULT true,
    last_run_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Workflow Execution History
CREATE TABLE IF NOT EXISTS workflow_executions (
    id SERIAL PRIMARY KEY,
    workflow_id INTEGER REFERENCES automated_workflows(id) ON DELETE CASCADE,
    trigger_event JSONB, -- Event that triggered the workflow
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'running', -- 'running', 'success', 'failed'
    results JSONB, -- Results of the workflow execution
    error_details TEXT
);

-- Project Reports
CREATE TABLE IF NOT EXISTS project_reports (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    report_type VARCHAR(50) NOT NULL, -- 'progress', 'git_activity', 'time_tracking', etc.
    config JSONB NOT NULL, -- Report configuration
    schedule VARCHAR(50), -- 'daily', 'weekly', 'monthly', null for on-demand
    last_generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add integration columns to existing tables
ALTER TABLE improvements
ADD COLUMN IF NOT EXISTS external_id JSONB, -- Store IDs from external systems
ADD COLUMN IF NOT EXISTS workflow_data JSONB; -- Store workflow-related data

-- Indexes
CREATE INDEX idx_time_entries_improvement ON time_entries(improvement_id);
CREATE INDEX idx_time_entries_user ON time_entries(user_id);
CREATE INDEX idx_external_integrations_project ON external_integrations(project_id);
CREATE INDEX idx_project_metrics_date ON project_metrics(project_id, metric_date);
CREATE INDEX idx_automated_workflows_project ON automated_workflows(project_id);
CREATE INDEX idx_workflow_executions_workflow ON workflow_executions(workflow_id);
CREATE INDEX idx_project_reports_project ON project_reports(project_id);

-- Update triggers
CREATE TRIGGER update_project_templates_updated_at
    BEFORE UPDATE ON project_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_external_integrations_updated_at
    BEFORE UPDATE ON external_integrations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_automated_workflows_updated_at
    BEFORE UPDATE ON automated_workflows
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_project_reports_updated_at
    BEFORE UPDATE ON project_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();
