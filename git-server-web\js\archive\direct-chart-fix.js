/**
 * Direct Chart Fix
 * This script directly manipulates the DOM to create a chart
 */

// Wait for page to load
document.addEventListener('DOMContentLoaded', function() {
    console.log('[DirectFix] Script loaded');
    
    // Add a button to the page
    const button = document.createElement('button');
    button.textContent = 'Fix Chart';
    button.style.position = 'fixed';
    button.style.top = '10px';
    button.style.right = '10px';
    button.style.zIndex = '9999';
    button.style.padding = '5px 10px';
    button.style.backgroundColor = '#4CAF50';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '4px';
    button.style.cursor = 'pointer';
    
    // Add click event
    button.addEventListener('click', fixChart);
    
    // Add to page
    document.body.appendChild(button);
    console.log('[DirectFix] Button added to page');
    
    // Also expose the function globally
    window.fixChart = fixChart;
    
    // Listen for modal shown events
    document.addEventListener('shown.bs.modal', function(event) {
        // Check if this is the repository modal
        if (event.target.id === 'repositoryDetailsModal') {
            console.log('[DirectFix] Repository modal shown, auto-fixing chart');
            // Wait a moment for the modal to fully render
            setTimeout(fixChart, 500);
        }
    });
});

function fixChart() {
    console.log('[DirectFix] Fixing chart...');
    
    try {
        // First, check if modal exists
        const modal = document.getElementById('repositoryDetailsModal');
        if (!modal) {
            console.error('[DirectFix] Repository modal not found');
            alert('Repository modal not found. Please open a repository first.');
            return;
        }
        
        // Find the modal body
        const modalBody = modal.querySelector('.modal-body');
        if (!modalBody) {
            console.error('[DirectFix] Modal body not found');
            alert('Modal body not found');
            return;
        }
        
        // Find the repository name
        const repoNameElement = document.getElementById('repo-name');
        if (!repoNameElement) {
            console.error('[DirectFix] Repository name element not found');
            alert('Repository name not found');
            return;
        }
        
        const repoName = repoNameElement.textContent.trim();
        console.log(`[DirectFix] Repository name: ${repoName}`);
        
        // Remove any existing chart section
        const existingSection = document.getElementById('direct-fix-chart-section');
        if (existingSection) {
            existingSection.remove();
            console.log('[DirectFix] Removed existing chart section');
        }
        
        // Create a new section for our chart
        const chartSection = document.createElement('div');
        chartSection.id = 'direct-fix-chart-section';
        chartSection.className = 'border-bottom p-4';
        chartSection.innerHTML = `
            <h6>Commit History (Direct Fix)</h6>
            <div id="direct-chart-container" style="height: 300px; position: relative;">
                <canvas id="direct-chart"></canvas>
            </div>
        `;
        
        // Add the chart section to the modal body
        modalBody.appendChild(chartSection);
        console.log('[DirectFix] Chart section added to modal');
        
        // Create sample data for the chart
        const data = {
            labels: ['Mar 4', 'Mar 11', 'Mar 18', 'Mar 25', 'Apr 1'],
            datasets: [{
                label: 'Commits',
                data: [5, 12, 87, 13, 8],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };
        
        // Create the chart
        const canvas = document.getElementById('direct-chart');
        if (!canvas) {
            console.error('[DirectFix] Canvas element not found');
            alert('Canvas element not found');
            return;
        }
        
        // Check if Chart.js is available
        if (typeof Chart === 'undefined') {
            console.error('[DirectFix] Chart.js is not available');
            alert('Chart.js is not available');
            return;
        }
        
        // Destroy existing chart if it exists
        if (window.directFixChart) {
            window.directFixChart.destroy();
            window.directFixChart = null;
        }
        
        // Set canvas dimensions
        const parentWidth = canvas.parentElement.clientWidth || 300;
        canvas.width = parentWidth;
        canvas.height = 300;
        
        // Create chart
        window.directFixChart = new Chart(canvas, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Commit History (Last 30 Days)'
                    },
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        console.log('[DirectFix] Chart created successfully');
        
    } catch (error) {
        console.error('[DirectFix] Error fixing chart:', error);
        alert(`Error fixing chart: ${error.message}`);
    }
}
