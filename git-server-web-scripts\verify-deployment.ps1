# Verify deployment of Git Repository components

# Configuration
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"

Write-Host "Verifying deployment of Git Repository components..." -ForegroundColor Yellow

# Verify all JavaScript files are present
Write-Host "Checking JavaScript files..." -ForegroundColor Cyan
$jsFiles = @(
    "git-utils.js",
    "git-commit-history-chart.js",
    "git-size-comparison-chart.js",
    "git-repository-list.js",
    "git-repository-modal.js",
    "git-repository-manager.js"
)

$allFilesExist = $true
foreach ($file in $jsFiles) {
    $output = ssh "${RemoteUser}@${RemoteHost}" "test -f ${RemotePath}/js/${file} && echo 'exists' || echo 'missing'"
    if ($output -eq "exists") {
        Write-Host "  - ${file}: " -NoNewline
        Write-Host "OK" -ForegroundColor Green
    } else {
        Write-Host "  - ${file}: " -NoNewline
        Write-Host "MISSING" -ForegroundColor Red
        $allFilesExist = $false
    }
}

# Verify index.html has been updated
Write-Host "\nChecking HTML files..." -ForegroundColor Cyan
$htmlResult = ssh "${RemoteUser}@${RemoteHost}" "grep -q 'git-utils.js' ${RemotePath}/index.html && echo 'updated' || echo 'not-updated'"
if ($htmlResult -eq "updated") {
    Write-Host "  - index.html: " -NoNewline
    Write-Host "UPDATED" -ForegroundColor Green
} else {
    Write-Host "  - index.html: " -NoNewline
    Write-Host "NOT UPDATED" -ForegroundColor Red
    $allFilesExist = $false
}

# Verify CSS file is present
Write-Host "\nChecking CSS files..." -ForegroundColor Cyan
$cssResult = ssh "${RemoteUser}@${RemoteHost}" "test -f ${RemotePath}/css/repositories.css && echo 'exists' || echo 'missing'"
if ($cssResult -eq "exists") {
    Write-Host "  - repositories.css: " -NoNewline
    Write-Host "OK" -ForegroundColor Green
} else {
    Write-Host "  - repositories.css: " -NoNewline
    Write-Host "MISSING" -ForegroundColor Red
    $allFilesExist = $false
}

# Overall status
Write-Host "\nDeployment verification result: " -NoNewline
if ($allFilesExist) {
    Write-Host "SUCCESS" -ForegroundColor Green
    Write-Host "All Git Repository components have been successfully deployed!" -ForegroundColor Green
} else {
    Write-Host "INCOMPLETE" -ForegroundColor Red
    Write-Host "Some Git Repository components are missing. Please review the deployment logs." -ForegroundColor Red
}
