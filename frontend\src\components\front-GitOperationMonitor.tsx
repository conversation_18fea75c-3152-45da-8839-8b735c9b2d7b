import React from 'react';

interface GitOperation {
  id: string;
  type: 'clone' | 'pull' | 'push' | 'fetch';
  repository: string;
  timestamp: string;
  duration: number;
  status: 'success' | 'failure' | 'in-progress';
  user: string;
}

interface GitOperationMonitorProps {
  operations: GitOperation[];
}

const GitOperationMonitor: React.FC<GitOperationMonitorProps> = ({ operations }) => {
  return (
    <div className="git-operation-monitor">
      <h2>Git Operations Performance</h2>
      <table className="operations-table">
        <thead>
          <tr>
            <th>Operation</th>
            <th>Repository</th>
            <th>User</th>
            <th>Timestamp</th>
            <th>Duration (ms)</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          {operations.length === 0 ? (
            <tr>
              <td colSpan={6}>No git operations recorded.</td>
            </tr>
          ) : (
            operations.map((op) => (
              <tr key={op.id} className={`status-${op.status}`}>
                <td>{op.type}</td>
                <td>{op.repository}</td>
                <td>{op.user}</td>
                <td>{op.timestamp}</td>
                <td>{op.duration}</td>
                <td>{op.status}</td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default GitOperationMonitor;
