#!/usr/bin/env python3
"""
Project Tracker Windows Build Environment Validator
Version: 1.0.0

Validates the Windows build server environment for Project Tracker deployment package preparation.
This script ensures the build server is properly configured to create deployment packages.

Requirements:
- Windows Server 2022 or Windows 10/11
- PowerShell 7+ for deployment scripts
"""

import os
import sys
import subprocess
import platform
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple

class BuildEnvironmentValidator:
    def __init__(self):
        self.results = {
            "build_tools": {"status": "pending", "details": {}},
            "package_structure": {"status": "pending", "details": {}},
            "storage": {"status": "pending", "details": {}}
        }
        self.required_space_gb = 10  # Required free space in GB
        self.deployment_dir = Path(__file__).resolve().parent.parent

    def check_build_tools(self) -> None:
        """Validate required build tools"""
        try:
            # Check PowerShell
            try:
                ps_version = subprocess.run(
                    ["pwsh", "-Command", "$PSVersionTable.PSVersion.ToString()"],
                    capture_output=True,
                    text=True,
                    check=True
                ).stdout.strip()
                self.results["build_tools"]["details"]["powershell"] = {
                    "version": ps_version,
                    "valid": True
                }
            except subprocess.CalledProcessError:
                self.results["build_tools"]["details"]["powershell"] = {
                    "version": "not found",
                    "valid": False
                }

            # Overall status
            self.results["build_tools"]["status"] = "ok" if self.results["build_tools"]["details"]["powershell"]["valid"] else "error"
        except Exception as e:
            self.results["build_tools"]["status"] = "error"
            self.results["build_tools"]["details"]["error"] = str(e)

    def check_package_structure(self) -> None:
        """Validate deployment package structure"""
        try:
            required_files = [
                "README.md",
                "DEPLOYMENT.md",
                "scripts/configure-server.sh",
                "scripts/deploy.sh",
                "scripts/auto-fix.py",
                "scripts/validate-deployment.py",
                "scripts/validate-configuration.py",
                "scripts/security-hardening.sh",
                "scripts/ssl-setup.sh",
                "scripts/backup-cron.sh"
            ]
            
            missing_files = []
            for file_path in required_files:
                full_path = self.deployment_dir / file_path
                if not full_path.exists():
                    missing_files.append(file_path)

            # Check file encodings (should be UTF-8 with LF)
            encoding_issues = []
            for file_path in required_files:
                full_path = self.deployment_dir / file_path
                if full_path.exists():
                    try:
                        with open(full_path, 'rb') as f:
                            content = f.read()
                            if b'\r\n' in content:
                                encoding_issues.append(f"{file_path} (CRLF line endings)")
                    except Exception as e:
                        encoding_issues.append(f"{file_path} (error: {str(e)})")

            self.results["package_structure"]["details"].update({
                "missing_files": missing_files,
                "encoding_issues": encoding_issues
            })
            self.results["package_structure"]["status"] = "ok" if not (missing_files or encoding_issues) else "error"
        except Exception as e:
            self.results["package_structure"]["status"] = "error"
            self.results["package_structure"]["details"]["error"] = str(e)

    def check_storage_requirements(self) -> None:
        """Validate storage space and permissions"""
        try:
            # Check available space
            free_gb = psutil.disk_usage(self.deployment_dir).free / (1024**3)
            space_sufficient = free_gb >= self.required_space_gb

            # Check directory permissions
            try:
                test_file = self.deployment_dir / ".write_test"
                test_file.touch()
                test_file.unlink()
                write_access = True
            except (OSError, PermissionError):
                write_access = False

            self.results["storage"]["details"].update({
                "free_space_gb": f"{free_gb:.2f}",
                "space_sufficient": space_sufficient,
                "write_access": write_access
            })
            
            self.results["storage"]["status"] = "ok" if (space_sufficient and write_access) else "error"
        except Exception as e:
            self.results["storage"]["status"] = "error"
            self.results["storage"]["details"]["error"] = str(e)

    def validate_environment(self) -> Dict:
        """Run all validation checks"""
        print("🔍 Starting Windows Build Environment Validation...")
        
        checks = [
            (self.check_build_tools, "Build Tools"),
            (self.check_package_structure, "Package Structure"),
            (self.check_storage_requirements, "Storage Requirements")
        ]

        for check_func, check_name in checks:
            print(f"\n🔍 Validating {check_name}...")
            check_func()
            status = self.results[check_name.lower().replace(" ", "_")]["status"]
            print(f"{'✅' if status == 'ok' else '❌' if status == 'error' else '⚠️'} {check_name}: {status}")

        return self.results

    def print_report(self) -> None:
        """Print a detailed validation report"""
        print("\n=== Windows Build Environment Validation Report ===")
        
        for category, data in self.results.items():
            print(f"\n{category.upper()} Status: {data['status']}")
            if isinstance(data.get('details'), dict):
                for key, value in data['details'].items():
                    print(f"  {key}: {value}")

def main():
    """Main entry point for build environment validation"""
    if platform.system() != "Windows":
        print("❌ Error: This script must be run on Windows")
        sys.exit(1)

    validator = BuildEnvironmentValidator()
    results = validator.validate_environment()
    validator.print_report()
    
    # Exit with error if any critical checks fail
    sys.exit(0 if all(
        data["status"] == "ok" 
        for data in results.values()
    ) else 1)

if __name__ == "__main__":
    main()
