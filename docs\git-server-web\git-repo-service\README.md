# Git Repository Service Documentation

This directory contains comprehensive documentation for the Git Repository Service, a C++23 application for managing Git repositories and providing repository statistics.

## Overview

The Git Repository Service is a high-performance C++23 application that provides:

- Git repository management
- Repository statistics and metrics
- User activity tracking
- Integration with the Database Service for data storage

## Key Features

- **High Performance**: Optimized C++23 implementation for fast repository operations
- **Database Integration**: Uses the Database Service for data storage
- **RESTful API**: HTTP API for repository operations
- **Security**: SSL/TLS encryption and authentication
- **JSON Support**: Full support for JSON data storage
- **Metrics Collection**: Comprehensive repository metrics and statistics

## Architecture

The Git Repository Service follows a modular architecture:

- **Core**: Repository management and Git operations
- **API**: RESTful API for repository operations
- **Database**: Integration with the Database Service
- **Security**: Authentication and authorization
- **Metrics**: Repository metrics and statistics collection

## API Reference

The Git Repository Service exposes a RESTful API for repository operations:

- **GET /api/repositories**: List all repositories
- **GET /api/repositories/{id}**: Get repository details
- **POST /api/repositories**: Create a new repository
- **PUT /api/repositories/{id}**: Update repository details
- **DELETE /api/repositories/{id}**: Delete a repository
- **GET /api/repositories/{id}/commits**: Get repository commits
- **GET /api/repositories/{id}/branches**: Get repository branches
- **GET /api/repositories/{id}/tags**: Get repository tags
- **GET /api/repositories/{id}/stats**: Get repository statistics

## Database Schema

The Git Repository Service uses the `git_repo_db` database with the following schemas:

- **public**: Core repository data
  - `repositories`: Repository information
  - `commits`: Commit history
  - `branches`: Branch information
  - `tags`: Tag information
  - `users`: User information

- **metadata**: Repository metadata
  - `repository_stats`: Repository statistics
  - `user_activity`: User activity tracking
  - `repository_metadata`: Additional repository metadata

## Integration with Other Components

The Git Repository Service integrates with:

- **Database Service**: For data storage and retrieval
- **Logging Service**: For logging and monitoring
- **Git Dashboard**: For web interface integration

## Deployment

The Git Repository Service is deployed to `/opt/git-dashboard/git-repo-service/` with:

- **Binary**: `/opt/git-dashboard/git-repo-service/bin/git-repo-service`
- **Configuration**: `/opt/git-dashboard/git-repo-service/config/config.json`
- **Systemd Service**: `/etc/systemd/system/git-repo-service.service`
- **Log Files**: `/opt/git-dashboard/git-repo-service/logs/git-repo-service.log`

## See Also

- [Database Service Documentation](../../database-service/overview.md)
- [Logging Service Documentation](../git-repo-logging/README.md)
- [C++23 Applications Integration Guide](../../CPP23-APPLICATIONS-INTEGRATION.md)
- [Project Tracker Documentation Index](../../DOCUMENTATION-INDEX.md)

