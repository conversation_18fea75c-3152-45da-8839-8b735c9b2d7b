{"compiler": {"gcc": {"minimum_version": "14.0.0", "required": true, "description": "GCC 14 compiler with C++23 support (required for modules and coroutines)", "package": "g++-14", "binary": "g++-14", "version_command": "g++-14 --version | head -n1 | awk '{print $4}'"}}, "build_tools": {"cmake": {"minimum_version": "3.20.0", "required": true, "description": "CMake build system", "package": "cmake", "binary": "cmake", "version_command": "cmake --version | head -n1 | awk '{print $3}'"}, "build_essential": {"required": true, "description": "Build tools (make, etc.)", "package": "build-essential", "check_command": "dpkg -s build-essential"}}, "libraries": {"boost": {"minimum_version": "1.74.0", "required": true, "description": "Boost C++ libraries (system, program_options components required)", "package": "libboost-all-dev", "check_command": "dpkg -l | grep libboost", "version_command": "dpkg -s libboost-dev | grep 'Version' | awk '{print $2}' | cut -d'-' -f1"}, "postgresql_client": {"minimum_version": "17.0", "required": true, "description": "PostgreSQL client development libraries", "package": "libpq-dev", "check_command": "dpkg -s libpq-dev", "version_command": "pg_config --version | awk '{print $2}'"}, "pqxx": {"minimum_version": "7.0.0", "required": true, "description": "C++ client API for PostgreSQL", "package": "libpqxx-dev", "check_command": "dpkg -s libpqxx-dev", "header_check": "find /usr -name pqxx -type d | grep include"}, "openssl": {"minimum_version": "1.1.1", "required": true, "description": "OpenSSL development libraries", "package": "libssl-dev", "check_command": "dpkg -s libssl-dev", "version_command": "openssl version | awk '{print $2}'"}, "nlohmann_json": {"minimum_version": "3.9.0", "required": true, "description": "JSON for Modern C++", "package": "nlohmann-json3-dev", "check_command": "dpkg -s n<PERSON><PERSON>-json3-dev", "header_check": "find /usr -name json.hpp | grep -q nlohmann && echo 'found' || echo 'missing'"}}, "system": {"postgresql_server": {"minimum_version": "17.0", "required": true, "description": "PostgreSQL database server", "service": "postgresql", "package": "postgresql", "binary": "psql", "version_command": "psql --version | head -n1 | awk '{print $3}'", "service_check": "systemctl status postgresql"}}, "directories": {"install_dir": {"path": "/opt/database-service", "permission": "database-service:database-service", "mode": "775"}, "bin_dir": {"path": "/opt/database-service/bin", "permission": "database-service:database-service", "mode": "775"}, "config_dir": {"path": "/opt/database-service/config", "permission": "database-service:database-service", "mode": "775"}, "lib_dir": {"path": "/opt/database-service/lib", "permission": "database-service:database-service", "mode": "775"}, "log_dir": {"path": "/opt/database-service/logs", "permission": "database-service:database-service", "mode": "775"}, "sql_dir": {"path": "/opt/database-service/sql", "permission": "database-service:database-service", "mode": "775"}, "systemd_dir": {"path": "/etc/systemd/system", "mode": "644"}, "ssl_cert_dir": {"path": "/etc/letsencrypt/live/git.chcit.org", "files": ["fullchain.pem", "privkey.pem", "chain.pem"], "check": "test -d /etc/letsencrypt/live/git.chcit.org && test -f /etc/letsencrypt/live/git.chcit.org/fullchain.pem"}, "build_dir": {"path": "/home/<USER>/database-service-build", "permission": "btaylor-admin:btaylor-admin", "mode": "775"}}, "users": {"service_user": {"name": "database-service", "group": "database-service", "check": "id database-service || echo 'User not found'"}}, "ports": {"service_port": {"port": 8080, "check": "sudo netstat -tuln | grep -w 8080 || echo 'Port 8080 is available'"}, "postgresql_port": {"port": 5432, "check": "sudo netstat -tuln | grep -w 5432 || echo 'WARNING: PostgreSQL port 5432 not listening'"}}, "databases": {"database_service": {"name": "database_service", "user": "postgres", "host": "localhost", "port": 5432, "check": "sudo -u postgres psql -c \"SELECT 1 FROM pg_database WHERE datname = 'database_service'\" | grep -q 1 && echo 'Database exists' || echo 'Database does not exist'", "create": "sudo -u postgres createdb database_service"}}, "services": {"database_service": {"name": "database-service", "description": "C++23 Database Service for Git Dashboard", "check": "systemctl list-unit-files | grep -q database-service && systemctl is-active database-service || echo 'Service not installed'"}}, "system_requirements": {"cpu_cores": {"minimum": 2, "recommended": 4, "check": "cat /proc/cpuinfo | grep processor | wc -l"}, "memory": {"minimum": "2G", "recommended": "4G", "check": "free -h | grep Mem | awk '{print $2}'"}, "disk_space": {"minimum": "5G", "check": "df -h / | grep / | awk '{print $4}'"}}}