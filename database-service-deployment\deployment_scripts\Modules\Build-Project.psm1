# Build-Project.psm1 - Module for building the database service project

<#
.SYNOPSIS
    Provides functionality for building the database service project.

.DESCRIPTION
    This module handles the build process for the database service project,
    including source code synchronization, remote build, and deployment.
    Based on the original batch script functionality.

.NOTES
    File Name      : Build-Project.psm1
    Author         : Database Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Write-Host "SSHManager module not loaded. Attempting to load..." -ForegroundColor Yellow
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
        Write-Host "SSHManager module loaded successfully." -ForegroundColor Green
    }
} catch {
    Write-Log -Message "Error loading required modules: $_" -Level "Error" -Component "Build-Project"
}

# Helper function to invoke remote commands
function Invoke-RemoteCommand {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Command,

        [Parameter(Mandatory = $false)]
        [switch]$Silent,

        [Parameter(Mandatory = $false)]
        [int]$Timeout = 60 # Default timeout in seconds
    )

    # Use global config for SSH parameters
    $Config = $global:Config
    $sshHost = $Config.ssh.host
    $sshUser = $Config.ssh.username
    $sshPort = $Config.ssh.port
    $sshKeyPath = $Config.ssh.local_key_path
    if ([string]::IsNullOrEmpty($sshKeyPath) -and $Config.ssh.PSObject.Properties.Name -contains "key_path") {
        $sshKeyPath = $Config.ssh.key_path
    }

    # Debug output for SSH parameters
    if (-not $Silent) {
        Write-Host "[Invoke-RemoteCommand] SSH Host: $sshHost" -ForegroundColor Yellow
        Write-Host "[Invoke-RemoteCommand] SSH User: $sshUser" -ForegroundColor Yellow
        Write-Host "[Invoke-RemoteCommand] SSH Port: $sshPort" -ForegroundColor Yellow
        Write-Host "[Invoke-RemoteCommand] SSH Key Path: $sshKeyPath" -ForegroundColor Yellow
    }

    if ([string]::IsNullOrEmpty($sshHost) -or [string]::IsNullOrEmpty($sshUser) -or [string]::IsNullOrEmpty($sshKeyPath)) {
        Write-Host "[Invoke-RemoteCommand] Error: Missing SSH parameters!" -ForegroundColor Red
        Write-Host "host: $sshHost" -ForegroundColor Red
        Write-Host "username: $sshUser" -ForegroundColor Red
        Write-Host "key path: $sshKeyPath" -ForegroundColor Red
        return $null
    }

    # Import SSHManager module if available
    try {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
    } catch {
        if (-not $Silent) {
            Write-Host "Warning: SSHManager module not available, using direct SSH command" -ForegroundColor Yellow
        }
    }

    # Check if SSHManager module is available
    if (Get-Module -Name "SSHManager") {
        # Use the Invoke-SSHCommand function from SSHManager module
        try {
            $result = Invoke-SSHCommand -Command $Command -HostName $sshHost -User $sshUser -Port $sshPort -KeyPath $sshKeyPath -Silent:$Silent -Timeout $Timeout
            if ($result.Success) {
                return $result.Output
            } else {
                if (-not $Silent) {
                    Write-Host "Error executing remote command: $($result.Error)" -ForegroundColor Red
                }
                return $null
            }
        } catch {
            if (-not $Silent) {
                Write-Host "Exception during remote command execution: $_" -ForegroundColor Red
            }
            return $null
        }
    } else {
        # Fallback to direct SSH command
        $sshArgs = @(
            "-i", $sshKeyPath,
            "-p", $sshPort,
            "-o", "StrictHostKeyChecking=no",
            "-o", "BatchMode=yes",
            "-o", "ConnectTimeout=10",
            "{0}@{1}" -f $sshUser, $sshHost,
            $Command
        )

        $tempOutputFile = [System.IO.Path]::GetTempFileName()
        $tempErrorFile = [System.IO.Path]::GetTempFileName()

        try {
            # Execute SSH command with timeout
            $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -NoNewWindow -PassThru -RedirectStandardOutput $tempOutputFile -RedirectStandardError $tempErrorFile
            $completed = $process.WaitForExit($Timeout * 1000)

            if (-not $completed) {
                try { $process.Kill() } catch { }
                if (-not $Silent) {
                    Write-Host "Command execution timed out after $Timeout seconds." -ForegroundColor Red
                }
                return $null
            }

            # Get command output
            $output = Get-Content -Path $tempOutputFile -Raw -ErrorAction SilentlyContinue
            $errorOutput = Get-Content -Path $tempErrorFile -Raw -ErrorAction SilentlyContinue

            # Clean up temporary files
            Remove-Item -Path $tempOutputFile -Force -ErrorAction SilentlyContinue
            Remove-Item -Path $tempErrorFile -Force -ErrorAction SilentlyContinue

            if ($process.ExitCode -eq 0) {
                return $output
            } else {
                if (-not $Silent) {
                    Write-Host "SSH command failed with exit code: $($process.ExitCode)" -ForegroundColor Red
                    if (-not [string]::IsNullOrWhiteSpace($errorOutput)) {
                        Write-Host "Error details: $errorOutput" -ForegroundColor Red
                    }
                }
                return $null
            }

            return $output
        } catch {
            if (-not $Silent) {
                Write-Host "Exception during remote command execution: $_" -ForegroundColor Red
            }
            return $null
        } finally {
            # Clean up temporary files
            Remove-Item -Path $tempOutputFile -Force -ErrorAction SilentlyContinue
            Remove-Item -Path $tempErrorFile -Force -ErrorAction SilentlyContinue
        }
    }
}

function Build-Project {
    [CmdletBinding()]
    param()

    # Log start of Build-Project using centralized logger
    Write-Log -Message "Build-Project started." -Level "Info" -Component "Build-Project"

    # Unify config to global scope for cross-module access
    if ($null -eq $global:Config -or $null -eq $global:Config.project) {
        Write-Log -Message "Configuration not loaded, attempting to load..." -Level "UI" -ForegroundColor Yellow
        $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
        if (Test-Path $configDir) {
            $configFile = Get-ChildItem -Path $configDir -Filter "database-service-*.json" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($null -ne $configFile) {
                try {
                    $configContent = Get-Content -Path $configFile.FullName -Raw -ErrorAction Stop
                    $global:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
                    Write-Log -Message "Successfully loaded configuration from $($configFile.FullName)" -Level "UI" -ForegroundColor Green
                } catch {
                    Write-Log -Message "Error loading configuration: $_" -Level "UI" -ForegroundColor Red
                    Write-Log -Message "Error loading configuration: $_" -Level "Error" -Component "Build-Project"
                    Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
                    Read-Host | Out-Null
                    return
                }
            } else {
                Write-Log -Message "Configuration file not found: $configFile" -Level "UI" -ForegroundColor Red
                Write-Log -Message "Configuration file not found: $configFile" -Level "Error" -Component "Build-Project"
                Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
                Read-Host | Out-Null
                return
            }
        } else {
            Write-Log -Message "Configuration directory not found: $configDir" -Level "UI" -ForegroundColor Red
            Write-Log -Message "Configuration directory not found: $configDir" -Level "Error" -Component "Build-Project"
            Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
            Read-Host | Out-Null
            return
        }
    }

    # Use global config for the rest of the function
    $Config = $global:Config

    # Display current configuration
    Write-Log -Message "Current Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "Project: $($Config.project.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "Environment: $($Config.environment)" -Level "UI" -ForegroundColor White
    Write-Log -Message "Server: $($Config.ssh.host)" -Level "UI" -ForegroundColor White

    $sourceDir = $null
    if ($Config.project.PSObject.Properties.Name -contains "local_source_dir") {
        $sourceDir = $Config.project.local_source_dir
        Write-Log -Message "Source Directory: $sourceDir" -Level "UI" -ForegroundColor White
    } elseif ($Config.project.PSObject.Properties.Name -contains "source_dir") {
        $sourceDir = $Config.project.source_dir
        Write-Log -Message "Source Directory: $sourceDir" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "Source directory not found in configuration." -Level "UI" -ForegroundColor Yellow
    }

    $remoteBuildDir = $Config.build_dir
    $sshKeyPath = $Config.ssh.local_key_path
    if ([string]::IsNullOrEmpty($sshKeyPath) -and $Config.ssh.PSObject.Properties.Name -contains "key_path") {
        $sshKeyPath = $Config.ssh.key_path
    }

    # Debug SSH connection details
    Write-Log -Message "SSH Host: $($Config.ssh.host)" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "SSH User: $($Config.ssh.username)" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "SSH Port: $($Config.ssh.port)" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "SSH Key Path: $sshKeyPath" -Level "UI" -ForegroundColor Yellow

    # Check for missing SSH parameters
    if ([string]::IsNullOrEmpty($Config.ssh.host) -or [string]::IsNullOrEmpty($Config.ssh.username) -or [string]::IsNullOrEmpty($sshKeyPath)) {
        Write-Log -Message "Error: One or more SSH parameters are missing from configuration!" -Level "UI" -ForegroundColor Red
        Write-Log -Message "host: $($Config.ssh.host)" -Level "UI" -ForegroundColor Red
        Write-Log -Message "username: $($Config.ssh.username)" -Level "UI" -ForegroundColor Red
        Write-Log -Message "key path: $sshKeyPath" -Level "UI" -ForegroundColor Red
        Write-Log -Message "Error: Missing SSH parameters! host=$($Config.ssh.host) user=$($Config.ssh.username) key=$sshKeyPath" -Level "Error" -Component "Build-Project"
        Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
        Read-Host | Out-Null
        return
    }

    # Actual build process starts here
    Write-Log -Message "Starting build process..." -Level "UI" -ForegroundColor Cyan
    
    # 1. Verify source directory exists
    if (-not (Test-Path $sourceDir)) {
        Write-Log -Message "Error: Source directory does not exist: $sourceDir" -Level "UI" -ForegroundColor Red
        Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
        Read-Host | Out-Null
        return
    }
    Write-Log -Message "Source directory verified: $sourceDir" -Level "UI" -ForegroundColor Green
    
    # 2. Test SSH connection
    Write-Log -Message "Testing SSH connection to $($Config.ssh.host)..." -Level "UI" -ForegroundColor Cyan
    $testResult = Invoke-RemoteCommand -Command "echo 'SSH connection successful'" -Silent:$false
    if ($null -eq $testResult) {
        Write-Log -Message "Error: SSH connection test failed. Cannot proceed with build." -Level "UI" -ForegroundColor Red
        Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
        Read-Host | Out-Null
        return
    }
    Write-Log -Message "SSH connection successful." -Level "UI" -ForegroundColor Green
    
    # 3. Create remote build directory if it doesn't exist
    $remoteBuildDir = $Config.project.remote_build_dir
    if ([string]::IsNullOrEmpty($remoteBuildDir)) {
        $remoteBuildDir = "/home/<USER>/database-service-build"
        Write-Log -Message "Remote build directory not specified in config. Using default: $remoteBuildDir" -Level "UI" -ForegroundColor Yellow
    }
    
    Write-Log -Message "Creating remote build directory: $remoteBuildDir" -Level "UI" -ForegroundColor Cyan
    $mkdirResult = Invoke-RemoteCommand -Command "mkdir -p $remoteBuildDir" -Silent:$false
    if ($null -eq $mkdirResult) {
        Write-Log -Message "Warning: Could not create remote build directory. Build may fail." -Level "UI" -ForegroundColor Yellow
    } else {
        Write-Log -Message "Remote build directory created/verified." -Level "UI" -ForegroundColor Green
    }
    
    # 4. Synchronize source code to remote server
    Write-Log -Message "Synchronizing source code to remote server..." -Level "UI" -ForegroundColor Cyan
    
    # Windows-compatible file synchronization using SCP
    try {
        # Use SCP for direct file transfer (since we're on Windows and rsync is typically not available)
        Write-Log -Message "Using SCP for direct file transfer on Windows..." -Level "UI" -ForegroundColor Cyan
        
        # Ensure source directory path ends with trailing backslash
        $sourceDirWithSlash = $sourceDir.TrimEnd('\') + "\"  # Ensure trailing backslash
        
        # Directly transfer files using SCP
        $scpProcess = Start-Process -FilePath "scp" -ArgumentList "-i", "`"$sshKeyPath`"", "-P", "$($Config.ssh.port)", "-r", "`"$sourceDirWithSlash*`"", "$($Config.ssh.username)@$($Config.ssh.host):$remoteBuildDir/" -NoNewWindow -PassThru -Wait
        
        if ($scpProcess.ExitCode -ne 0) {
            Write-Log -Message "Error: SCP file transfer failed. Cannot proceed with build." -Level "UI" -ForegroundColor Red
            return $false
        }
        
        Write-Log -Message "Direct file transfer completed." -Level "UI" -ForegroundColor Green
        Write-Log -Message "Source code synchronized successfully." -Level "UI" -ForegroundColor Green
    }
    catch {
        Write-Log -Message "Error during file transfer: $_" -Level "UI" -ForegroundColor Red
        Write-Log -Message "Could not synchronize source code. Build cannot proceed." -Level "UI" -ForegroundColor Red
        return $false
    }
    
    # 5. Run remote build commands
    Write-Log -Message "Running build commands on remote server..." -Level "UI" -ForegroundColor Cyan
    
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $remoteLogFilename = "build-$timestamp.log"
    $cmakeGenerator = 'Ninja'
    $cmakeArgs = @(
        "-G", $cmakeGenerator,
        "-DCMAKE_BUILD_TYPE=Release"
    )
    $env:CC = "clang"
    $env:CXX = "clang++"
    $cmakeCommand = "cd $remoteBuildDir && mkdir -p build && cd build && CC=clang CXX=clang++ cmake -G Ninja -DCMAKE_BUILD_TYPE=Release .. > $remoteLogFilename 2>&1 ; ninja >> $remoteLogFilename 2>&1"

    # --- RUN REMOTE BUILD ---
    $buildResult = Invoke-RemoteCommand -Command $cmakeCommand -Silent:$false

    if ($null -eq $buildResult -or [string]::IsNullOrEmpty($buildResult)) {
        Write-Log -Message "Build process failed or returned no output." -Level "UI" -ForegroundColor Red
    } else {
        Write-Log -Message "Build Output:" -Level "UI" -ForegroundColor Cyan
        Write-Log -Message $buildResult -Level "UI" -ForegroundColor White
    }

    $localLogDir = "D:\Augment\project-tracker\database-service-deployment\deployment_logs"
    $localLogPath = "$localLogDir\$remoteLogFilename"
    $remoteLogPath = "$remoteBuildDir/build/$remoteLogFilename"

    # Ensure local log directory exists
    if (-not (Test-Path $localLogDir)) {
        New-Item -ItemType Directory -Path $localLogDir | Out-Null
    }

    # Compose SCP command
    $scpExe = "scp"
    $scpArgs = @(
        "-i", $sshKeyPath,
        "-P", $Config.ssh.port,
        "-o", "StrictHostKeyChecking=no",
        "-o", "BatchMode=yes",
        "-o", "UserKnownHostsFile=/dev/null",
        "$($Config.ssh.username)@$($Config.ssh.host):$remoteLogPath",
        $localLogPath
    )

    # --- SCP LOG FILE COPY ---
    $scpResult = Start-Process -FilePath $scpExe -ArgumentList $scpArgs -Wait -NoNewWindow -PassThru

    if ($scpResult.ExitCode -eq 0) {
        Write-Log -Message "Build log copied successfully to $localLogPath" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "Failed to copy build log from remote server." -Level "UI" -ForegroundColor Red
    }

    # Log successful completion
    Write-Log -Message "Build-Project completed successfully." -Level "Info" -Component "Build-Project"
    
    # Add pause before returning to main menu
    Write-Log -Message "Press Enter to return to the main menu..." -Level "UI" -ForegroundColor Yellow
    Read-Host | Out-Null
    
    return $true
}

# Export the main function
Export-ModuleMember -Function Build-Project
