#pragma once
#include <string>
#include <unordered_map>
#include <mutex>
#include <memory>
#include <chrono>
#include <vector>
#include <atomic>
#include <thread>

namespace dbservice::metrics {

/**
 * @struct MetricValue
 * @brief Value of a metric with timestamp
 */
struct MetricValue {
    double value;
    std::chrono::system_clock::time_point timestamp;
};

/**
 * @class MetricsCollector
 * @brief Collects and stores metrics for the database service
 */
class MetricsCollector {
public:
    /**
     * @brief Get the singleton instance
     * @return Reference to the singleton instance
     */
    static MetricsCollector& getInstance();
    
    /**
     * @brief Initialize the metrics collector
     * @param enabled Whether metrics collection is enabled
     * @param collectionIntervalSeconds Interval between metrics collections in seconds
     * @param retentionDays Number of days to retain metrics
     * @return True if initialization was successful
     */
    bool initialize(bool enabled, int collectionIntervalSeconds, int retentionDays);
    
    /**
     * @brief Start metrics collection
     */
    void start();
    
    /**
     * @brief Stop metrics collection
     */
    void stop();
    
    /**
     * @brief Record a metric
     * @param name Metric name
     * @param value Metric value
     */
    void recordMetric(const std::string& name, double value);
    
    /**
     * @brief Get metrics for a specific name
     * @param name Metric name
     * @param startTime Start time for metrics
     * @param endTime End time for metrics
     * @return Vector of metric values
     */
    std::vector<MetricValue> getMetrics(const std::string& name, 
                                      const std::chrono::system_clock::time_point& startTime,
                                      const std::chrono::system_clock::time_point& endTime);
    
    /**
     * @brief Get all metric names
     * @return Vector of metric names
     */
    std::vector<std::string> getMetricNames();
    
    /**
     * @brief Get the latest value for a metric
     * @param name Metric name
     * @return Latest metric value or nullptr if not found
     */
    std::shared_ptr<MetricValue> getLatestMetric(const std::string& name);
    
private:
    /**
     * @brief Constructor
     */
    MetricsCollector();
    
    /**
     * @brief Destructor
     */
    ~MetricsCollector();
    
    /**
     * @brief Collect system metrics
     */
    void collectSystemMetrics();
    
    /**
     * @brief Clean up old metrics
     */
    void cleanupOldMetrics();
    
    /**
     * @brief Run the metrics collection loop
     */
    void run();
    
    std::unordered_map<std::string, std::vector<MetricValue>> metrics_;
    std::mutex mutex_;
    bool enabled_;
    int collectionIntervalSeconds_;
    int retentionDays_;
    std::atomic<bool> running_;
    std::thread collectionThread_;
};

} // namespace dbservice::metrics
