#pragma once
#include "../models/log_entry.hpp"
#include <string>
#include <vector>
#include <unordered_map>
#include <filesystem>
#include <memory>
#include <mutex>
#include <functional>

namespace logging {

// Structure to hold information about a discovered log file
struct LogFileInfo {
    std::string path;           // Full path to the log file
    std::string name;           // Display name for the log
    std::string category;       // Category (system, security, application, etc.)
    std::string parser;         // Parser to use for this log
    bool binary;                // Whether the log is binary or text
    bool enabled;               // Whether the log is enabled for collection
    std::filesystem::file_time_type lastModified; // Last modification time
    size_t size;                // File size in bytes
};

class LogDiscoveryService {
public:
    LogDiscoveryService(const std::string& logSourcesPath);
    ~LogDiscoveryService();

    // Scan for available log files
    bool scanLogSources();

    // Get discovered log files
    std::vector<LogFileInfo> getDiscoveredLogs() const;

    // Get logs by category
    std::vector<LogFileInfo> getLogsByCategory(const std::string& category) const;

    // Enable/disable a log file
    bool setLogEnabled(const std::string& path, bool enabled);

    // Save configuration
    bool saveConfiguration(const std::string& configPath);

    // Load configuration
    bool loadConfiguration(const std::string& configPath);

private:
    // Detect log type based on content
    std::string detectLogType(const std::string& path);

    // Categorize log file based on path and content
    std::string categorizeLogFile(const std::string& path, const std::string& content);

    // Determine appropriate parser for log file
    std::string determineParser(const std::string& path, const std::string& content);

    // Check if file is binary
    bool isBinaryFile(const std::string& path);

    // Sample file content
    std::string sampleFileContent(const std::string& path, size_t maxBytes = 4096);

    // Known log files patterns
    struct LogPattern {
        std::string namePattern;    // Regex pattern for file name
        std::string contentPattern;  // Regex pattern for content
        std::string category;       // Category for this pattern
        std::string parser;         // Parser to use
    };

    // Initialize known log patterns
    void initializeLogPatterns();

    std::string logSourcesPath_;
    std::vector<LogFileInfo> discoveredLogs_;
    std::vector<LogPattern> logPatterns_;
    mutable std::mutex mutex_;
};

} // namespace logging
