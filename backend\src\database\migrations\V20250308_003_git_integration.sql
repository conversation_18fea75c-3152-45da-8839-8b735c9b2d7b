-- Git Integration Schema Migration
-- Version: V20250308_003
-- Description: Adds tables for Git repository tracking and metrics

-- Git Repositories Table
CREATE TABLE IF NOT EXISTS git_repositories (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    local_path VARCHAR(512) NOT NULL,
    remote_url VARCHAR(512),
    default_branch VARCHAR(100) DEFAULT 'main',
    last_fetch_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Git Branches Table
CREATE TABLE IF NOT EXISTS git_branches (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    last_commit_hash VARCHAR(40),
    last_commit_message TEXT,
    last_commit_author VA<PERSON>HA<PERSON>(255),
    last_commit_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(repository_id, name)
);

-- Git Operations Table (for tracking git operations and their performance)
CREATE TABLE IF NOT EXISTS git_operations (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    operation_type VARCHAR(50) NOT NULL, -- e.g., 'fetch', 'pull', 'push', 'commit'
    branch_name VARCHAR(255),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'success', 'failed'
    error_message TEXT,
    performance_metrics JSONB -- Store metrics like execution time, objects transferred, etc.
);

-- Git Metrics Table (for storing repository metrics)
CREATE TABLE IF NOT EXISTS git_metrics (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    commits_count INTEGER DEFAULT 0,
    lines_added INTEGER DEFAULT 0,
    lines_deleted INTEGER DEFAULT 0,
    files_changed INTEGER DEFAULT 0,
    contributors_count INTEGER DEFAULT 0,
    branches_count INTEGER DEFAULT 0,
    metrics_data JSONB, -- Additional metrics in JSON format
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Git Hooks Table (for managing repository webhooks)
CREATE TABLE IF NOT EXISTS git_hooks (
    id SERIAL PRIMARY KEY,
    repository_id INTEGER REFERENCES git_repositories(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- e.g., 'push', 'pull_request', 'commit'
    endpoint_url VARCHAR(512) NOT NULL,
    secret_token VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Project Git Integration Settings
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS git_settings JSONB DEFAULT '{
    "auto_fetch": true,
    "fetch_interval": 300,
    "track_metrics": true,
    "notify_on_push": true,
    "notify_on_pull_request": true
}'::jsonb;

-- Indexes
CREATE INDEX idx_git_repositories_project_id ON git_repositories(project_id);
CREATE INDEX idx_git_branches_repository_id ON git_branches(repository_id);
CREATE INDEX idx_git_operations_repository_id ON git_operations(repository_id);
CREATE INDEX idx_git_metrics_repository_date ON git_metrics(repository_id, metric_date);
CREATE INDEX idx_git_hooks_repository_id ON git_hooks(repository_id);

-- Update triggers
CREATE TRIGGER update_git_repositories_updated_at
    BEFORE UPDATE ON git_repositories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_git_branches_updated_at
    BEFORE UPDATE ON git_branches
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_git_hooks_updated_at
    BEFORE UPDATE ON git_hooks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();
