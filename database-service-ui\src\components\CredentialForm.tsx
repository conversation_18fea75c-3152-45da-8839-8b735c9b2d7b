import React, { useState } from 'react';
import { credentialsAPI } from '../services/api';
import './CredentialForm.css';

interface CredentialFormProps {
  onCredentialSaved?: () => void;
}

const CredentialForm: React.FC<CredentialFormProps> = ({ onCredentialSaved }) => {
  const [key, setKey] = useState('');
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!key.trim()) {
      setError('Key is required');
      return;
    }
    
    if (!value.trim()) {
      setError('Value is required');
      return;
    }
    
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await credentialsAPI.storeCredential(key, value);
      
      if (response.data.success) {
        setSuccess('Credential stored successfully');
        setKey('');
        setValue('');
        
        if (onCredentialSaved) {
          onCredentialSaved();
        }
      } else {
        setError(response.data.error?.message || 'Failed to store credential');
      }
    } catch (err: any) {
      setError(err.response?.data?.error?.message || err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="credential-form card">
      <h2>Store Credential</h2>
      
      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="credential-key">Key</label>
          <input
            type="text"
            id="credential-key"
            className="form-control"
            value={key}
            onChange={(e) => setKey(e.target.value)}
            disabled={loading}
            placeholder="Enter credential key"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="credential-value">Value</label>
          <input
            type="text"
            id="credential-value"
            className="form-control"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            disabled={loading}
            placeholder="Enter credential value"
          />
        </div>
        
        <button type="submit" className="btn btn-primary" disabled={loading}>
          {loading ? 'Storing...' : 'Store Credential'}
        </button>
      </form>
    </div>
  );
};

export default CredentialForm;
