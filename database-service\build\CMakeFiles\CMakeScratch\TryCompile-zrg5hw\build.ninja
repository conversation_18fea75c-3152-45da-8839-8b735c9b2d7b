# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: CMAKE_TRY_COMPILE
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/Augment/project-tracker/database-service/build/CMakeFiles/CMakeScratch/TryCompile-zrg5hw/
# =============================================================================
# Object build statements for EXECUTABLE target cmTC_0ee64


#############################################
# Order-only phony target for cmTC_0ee64

build cmake_object_order_depends_target_cmTC_0ee64: phony || .

build CMakeFiles/cmTC_0ee64.dir/src.cxx.obj: CXX_COMPILER__cmTC_0ee64_unscanned_ D$:/Augment/project-tracker/database-service/build/CMakeFiles/CMakeScratch/TryCompile-zrg5hw/src.cxx || cmake_object_order_depends_target_cmTC_0ee64
  DEFINES = -DHAVE_STD_FORMAT
  DEP_FILE = CMakeFiles\cmTC_0ee64.dir\src.cxx.obj.d
  FLAGS = -std=c++23
  OBJECT_DIR = CMakeFiles\cmTC_0ee64.dir
  OBJECT_FILE_DIR = CMakeFiles\cmTC_0ee64.dir


# =============================================================================
# Link build statements for EXECUTABLE target cmTC_0ee64


#############################################
# Link the executable cmTC_0ee64.exe

build cmTC_0ee64.exe: CXX_EXECUTABLE_LINKER__cmTC_0ee64_ CMakeFiles/cmTC_0ee64.dir/src.cxx.obj
  LINK_LIBRARIES = -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\cmTC_0ee64.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = cmTC_0ee64.exe
  TARGET_IMPLIB = libcmTC_0ee64.dll.a
  TARGET_PDB = cmTC_0ee64.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\Augment\project-tracker\database-service\build\CMakeFiles\CMakeScratch\TryCompile-zrg5hw && D:\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\Augment\project-tracker\database-service\build\CMakeFiles\CMakeScratch\TryCompile-zrg5hw && D:\mingw64\bin\cmake.exe --regenerate-during-build -SD:\Augment\project-tracker\database-service\build\CMakeFiles\CMakeScratch\TryCompile-zrg5hw -BD:\Augment\project-tracker\database-service\build\CMakeFiles\CMakeScratch\TryCompile-zrg5hw"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build cmTC_0ee64: phony cmTC_0ee64.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/Augment/project-tracker/database-service/build/CMakeFiles/CMakeScratch/TryCompile-zrg5hw

build codegen: phony

# =============================================================================

#############################################
# Folder: D:/Augment/project-tracker/database-service/build/CMakeFiles/CMakeScratch/TryCompile-zrg5hw

build all: phony cmTC_0ee64.exe

# =============================================================================
# Built-in targets


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
