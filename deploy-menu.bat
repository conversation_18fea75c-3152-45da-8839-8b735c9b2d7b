@echo off
setlocal enabledelayedexpansion

REM ======================================================================
REM Project Tracker Master Deployment Menu
REM
REM This script provides a menu-driven interface for deploying all
REM components of the Project Tracker ecosystem.
REM ======================================================================

:menu
cls
echo ========================================================
echo Project Tracker Master Deployment Menu
echo ========================================================
echo.
echo  1. Deploy Database Service
echo  2. Deploy Git Repository Service
echo  3. Deploy Logging Service
echo  4. Deploy All Services
echo  5. Setup All Databases
echo  6. Check All Services Status
echo  7. View All Logs
echo  8. Exit
echo.
echo ========================================================
echo.

set /p choice=Enter your choice (1-8):

if "%choice%"=="1" goto database_service
if "%choice%"=="2" goto git_repo_service
if "%choice%"=="3" goto logging_service
if "%choice%"=="4" goto deploy_all
if "%choice%"=="5" goto setup_all_db
if "%choice%"=="6" goto check_all
if "%choice%"=="7" goto view_all_logs
if "%choice%"=="8" goto exit

echo Invalid choice. Please try again.
timeout /t 2 >nul
goto menu

:database_service
cls
echo Launching Database Service Deployment Menu...
cd /d "D:\Augment\project-tracker\database-service\scripts"
call deploy-menu.bat
cd /d "D:\Augment\project-tracker"
goto menu

:git_repo_service
cls
echo Launching Git Repository Service Deployment Menu...
cd /d "D:\Augment\project-tracker\git-server-web\git-repo-service\scripts"
call deploy-menu.bat
cd /d "D:\Augment\project-tracker"
goto menu

:logging_service
cls
echo Launching Logging Service Deployment Menu...
cd /d "D:\Augment\project-tracker\git-server-web\git-repo-logging\scripts"
call deploy-menu.bat
cd /d "D:\Augment\project-tracker"
goto menu

:deploy_all
cls
echo ========================================================
echo Deploy All Services
echo ========================================================
echo.
set /p server_name=Enter server name:

if "%server_name%"=="" (
    echo Server name cannot be empty.
    timeout /t 2 >nul
    goto deploy_all
)

echo.
echo Deploying all services to %server_name%...
echo.

echo Step 1/3: Deploying Database Service...
cd /d "D:\Augment\project-tracker\database-service\scripts"
call deploy-database-service.bat %server_name%
cd /d "D:\Augment\project-tracker"

echo.
echo Step 2/3: Deploying Git Repository Service...
cd /d "D:\Augment\project-tracker\git-server-web\git-repo-service\scripts"
call deploy-git-repo-service.bat %server_name%
cd /d "D:\Augment\project-tracker"

echo.
echo Step 3/3: Deploying Logging Service...
cd /d "D:\Augment\project-tracker\git-server-web\git-repo-logging\scripts"
call deploy-git-repo-logging.bat %server_name%
cd /d "D:\Augment\project-tracker"

echo.
echo All services deployed successfully!
echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:setup_all_db
cls
echo ========================================================
echo Setup All Databases
echo ========================================================
echo.
set /p server_name=Enter server name:

if "%server_name%"=="" (
    echo Server name cannot be empty.
    timeout /t 2 >nul
    goto setup_all_db
)

echo.
echo Setting up all databases on %server_name%...
echo.

echo Step 1/3: Setting up Central Management Database...
cd /d "D:\Augment\project-tracker\database-service\scripts"
call setup-database.bat %server_name% database_service
cd /d "D:\Augment\project-tracker"

echo.
echo Step 2/3: Setting up Git Repository Database...
cd /d "D:\Augment\project-tracker\database-service\scripts"
call setup-database.bat %server_name% git_repo_db
cd /d "D:\Augment\project-tracker"

echo.
echo Step 3/3: Setting up Logging Database...
cd /d "D:\Augment\project-tracker\database-service\scripts"
call setup-database.bat %server_name% logging_db
cd /d "D:\Augment\project-tracker"

echo.
echo All databases set up successfully!
echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:check_all
cls
echo ========================================================
echo Check All Services Status
echo ========================================================
echo.
set /p server_name=Enter server name:

if "%server_name%"=="" (
    echo Server name cannot be empty.
    timeout /t 2 >nul
    goto check_all
)

echo.
echo Checking status of all services on %server_name%...
echo.

echo Database Service Status:
echo ----------------------
ssh btaylor-admin@%server_name% "sudo systemctl status database-service | head -n 20"

echo.
echo Git Repository Service Status:
echo ---------------------------
ssh btaylor-admin@%server_name% "sudo systemctl status git-repo-service | head -n 20"

echo.
echo Logging Service Status:
echo --------------------
ssh btaylor-admin@%server_name% "sudo systemctl status git-repo-logging | head -n 20"

echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:view_all_logs
cls
echo ========================================================
echo View All Logs
echo ========================================================
echo.
set /p server_name=Enter server name:

if "%server_name%"=="" (
    echo Server name cannot be empty.
    timeout /t 2 >nul
    goto view_all_logs
)

echo.
echo Select log to view:
echo  1. Database Service Logs
echo  2. Git Repository Service Logs
echo  3. Logging Service Logs
echo  4. All Services Logs (last 20 lines each)
echo  5. Back to Main Menu
echo.

set /p log_choice=Enter your choice (1-5):

if "%log_choice%"=="1" (
    echo.
    echo Viewing Database Service logs on %server_name%...
    echo.
    ssh btaylor-admin@%server_name% "sudo tail -n 100 /opt/git-dashboard/logs/database-service.log"
) else if "%log_choice%"=="2" (
    echo.
    echo Viewing Git Repository Service logs on %server_name%...
    echo.
    ssh btaylor-admin@%server_name% "sudo tail -n 100 /opt/git-dashboard/logs/git-repo-service.log"
) else if "%log_choice%"=="3" (
    echo.
    echo Viewing Logging Service logs on %server_name%...
    echo.
    ssh btaylor-admin@%server_name% "sudo tail -n 100 /opt/git-dashboard/logs/git-repo-logging.log"
) else if "%log_choice%"=="4" (
    echo.
    echo Viewing all services logs on %server_name%...
    echo.
    echo Database Service Logs:
    echo --------------------
    ssh btaylor-admin@%server_name% "sudo tail -n 20 /opt/git-dashboard/logs/database-service.log"

    echo.
    echo Git Repository Service Logs:
    echo -------------------------
    ssh btaylor-admin@%server_name% "sudo tail -n 20 /opt/git-dashboard/logs/git-repo-service.log"

    echo.
    echo Logging Service Logs:
    echo ------------------
    ssh btaylor-admin@%server_name% "sudo tail -n 20 /opt/git-dashboard/logs/git-repo-logging.log"
) else if "%log_choice%"=="5" (
    goto menu
) else (
    echo Invalid choice. Please try again.
    timeout /t 2 >nul
    goto view_all_logs
)

echo.
echo Press any key to return to the log viewing menu...
pause >nul
goto view_all_logs

:exit
echo Exiting...
exit /b 0

endlocal
