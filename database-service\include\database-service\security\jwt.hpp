#pragma once
#include <string>
#include <unordered_map>
#include <chrono>
#include <vector>

namespace dbservice::security {

/**
 * @class JWT
 * @brief JSON Web Token implementation
 */
class JWT {
public:
    /**
     * @brief Create a JWT token
     * @param payload Token payload
     * @param secret Secret key
     * @param expiresInSeconds Token expiration time in seconds
     * @return JWT token
     */
    static std::string create(const std::unordered_map<std::string, std::string>& payload, 
                             const std::string& secret,
                             int expiresInSeconds);
    
    /**
     * @brief Verify a JWT token
     * @param token JWT token
     * @param secret Secret key
     * @param payload Output parameter for token payload
     * @return True if token is valid
     */
    static bool verify(const std::string& token, 
                      const std::string& secret, 
                      std::unordered_map<std::string, std::string>& payload);
    
    /**
     * @brief Check if a token is expired
     * @param payload Token payload
     * @return True if token is expired
     */
    static bool isExpired(const std::unordered_map<std::string, std::string>& payload);
    
private:
    /**
     * @brief Base64 encode a string
     * @param input String to encode
     * @return Base64 encoded string
     */
    static std::string base64Encode(const std::string& input);
    
    /**
     * @brief Base64 decode a string
     * @param input String to decode
     * @return Base64 decoded string
     */
    static std::string base64Decode(const std::string& input);
    
    /**
     * @brief Create HMAC-SHA256 signature
     * @param input String to sign
     * @param key Key to use for signing
     * @return Signature
     */
    static std::string hmacSha256(const std::string& input, const std::string& key);
    
    /**
     * @brief Parse a JSON string
     * @param json JSON string
     * @return Map of key-value pairs
     */
    static std::unordered_map<std::string, std::string> parseJson(const std::string& json);
    
    /**
     * @brief Serialize a map to JSON
     * @param map Map to serialize
     * @return JSON string
     */
    static std::string serializeJson(const std::unordered_map<std::string, std::string>& map);
};

} // namespace dbservice::security
