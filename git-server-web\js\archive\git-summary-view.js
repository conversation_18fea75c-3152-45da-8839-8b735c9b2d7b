/**
 * Git Repository Summary Module
 * Handles the summary view of git repositories in the Repository tab
 */
const GitSummaryView = {
    // Configuration
    config: {
        endpoint: '/api/git/repositories',
        refreshInterval: 60000 // 60 seconds
    },
    
    // Application state
    state: {
        repositoriesData: null,
        lastUpdated: null,
        status: 'loading',
        refreshTimer: null,
        isInitialized: false
    },
    
    /**
     * Initialize the Git Summary component
     */
    init() {
        console.log('Initializing Git Summary View...');
        
        if (this.state.isInitialized) {
            console.warn('Git Summary View already initialized');
            return this;
        }
        
        // Create summary container if it doesn't exist
        this.createSummaryContainer();
        
        // Fetch initial data
        this.fetchRepositoriesData();
        
        // Set up auto-refresh
        this.state.refreshTimer = setInterval(() => {
            this.fetchRepositoriesData();
        }, this.config.refreshInterval);
        
        // Log initialization
        if (window.DashboardLogger) {
            window.DashboardLogger.addLog('info', 'Git Summary View initialized', 'git');
        }
        
        this.state.isInitialized = true;
        return this;
    },
    
    /**
     * Create the summary container in the Repository tab
     */
    createSummaryContainer() {
        // Get the repository tab container
        const repoContainer = document.getElementById('git-repository-container');
        if (!repoContainer) {
            console.error('Repository container not found');
            return;
        }
        
        // Create summary container if it doesn't exist
        let summaryContainer = document.getElementById('git-summary-container');
        if (!summaryContainer) {
            summaryContainer = document.createElement('div');
            summaryContainer.id = 'git-summary-container';
            summaryContainer.className = 'mb-4';
            repoContainer.prepend(summaryContainer);
        }
        
        // Create initial content with stats cards
        summaryContainer.innerHTML = `
            <div class="row mb-3">
                <div class="col-12">
                    <h5 class="border-bottom pb-2">Repository Summary</h5>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="metric-value" id="summary-total-repos">--</div>
                            <div class="metric-label">Total Repositories</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="metric-value" id="summary-total-size">--</div>
                            <div class="metric-label">Total Size</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="metric-value" id="summary-recent-commits">--</div>
                            <div class="metric-label">Recent Commits (24h)</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="metric-value" id="summary-largest-repo">--</div>
                            <div class="metric-label">Largest Repository</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">Recent Activity</h6>
                        </div>
                        <div class="card-body">
                            <div id="summary-recent-activity" class="activity-list">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span class="ms-2">Loading activity...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">Health Status</h6>
                        </div>
                        <div class="card-body">
                            <div id="summary-health-status" class="health-status-list">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span class="ms-2">Loading health status...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },
    
    /**
     * Update module status
     * @param {string} status - New status (loading, success, error)
     */
    updateStatus(status) {
        this.state.status = status;
        
        // Update UI indicators if they exist
        const gitStatusIndicator = document.getElementById('repo-list-status-indicator');
        if (gitStatusIndicator) {
            gitStatusIndicator.className = 'status-indicator ' + status;
        }
    },
    
    /**
     * Fetch Git repositories data from API
     * @returns {Promise<void>}
     */
    async fetchRepositoriesData() {
        console.log('Fetching Git repositories data for summary view');
        this.updateStatus('loading');
        
        try {
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('info', 'Fetching Git repositories for summary view', 'git');
            }
            
            const response = await fetch(this.config.endpoint);
            if (!response.ok) {
                const errorMsg = `Failed to fetch repositories: ${response.status}`;
                if (window.DashboardLogger) {
                    window.DashboardLogger.addLog('error', errorMsg, 'git');
                }
                throw new Error(errorMsg);
            }
            
            const data = await response.json();
            console.log('Repository data for summary:', data);
            this.state.repositoriesData = data;
            
            this.state.lastUpdated = new Date();
            this.updateStatus('success');
            
            // Update the summary view with the new data
            this.updateSummaryView();
            
            // Return the data for potential further processing
            return data;
            
        } catch (error) {
            console.error('Error fetching repositories for summary:', error);
            if (window.DashboardLogger) {
                window.DashboardLogger.addLog('error', `Error fetching repositories for summary: ${error.message}`, 'git');
            }
            this.updateStatus('error');
            
            // Show error message in summary view
            this.showErrorInSummary(error.message);
            
            throw error;
        }
    },
    
    /**
     * Show error message in summary view
     * @param {string} message - Error message to display
     */
    showErrorInSummary(message) {
        const summaryContainer = document.getElementById('git-summary-container');
        if (!summaryContainer) return;
        
        summaryContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Error loading repository data:</strong> ${message}
                <button class="btn btn-sm btn-outline-danger ms-3" id="retry-summary-btn">
                    <i class="fas fa-sync me-1"></i> Retry
                </button>
            </div>
        `;
        
        // Add event listener to retry button
        const retryBtn = document.getElementById('retry-summary-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.fetchRepositoriesData();
            });
        }
    },
    
    /**
     * Update summary view with repository data
     */
    updateSummaryView() {
        if (!this.state.repositoriesData) {
            console.warn('No repository data available for summary');
            return;
        }
        
        const repositories = this.state.repositoriesData.repositories || [];
        
        // Calculate summary stats
        const totalRepos = repositories.length;
        let recentCommits = 0;
        let largestRepo = { name: 'None', size: 0, size_formatted: '0B' };
        let totalSize = 0;
        
        // Get current date for recent commits (last 24 hours)
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        
        // Collect repositories with health issues
        const healthIssues = [];
        
        // Collect recent activity
        const recentActivity = [];
        
        repositories.forEach(repo => {
            // Count recent commits
            if (repo.last_commit) {
                const commitDate = new Date(repo.last_commit);
                
                // Add to recent activity list
                if (commitDate > oneDayAgo) {
                    recentCommits++;
                    recentActivity.push({
                        type: 'commit',
                        repo: repo.name,
                        date: commitDate,
                        message: 'Recent commit activity'
                    });
                }
            }
            
            // Find largest repository
            const repoSize = this.parseSize(repo.size_formatted || '0B');
            if (repoSize > this.parseSize(largestRepo.size_formatted)) {
                largestRepo = repo;
            }
            
            // Add to total size
            totalSize += this.parseSize(repo.size_formatted || '0B') * 1024 * 1024; // Convert MB to bytes
            
            // Check health status
            const healthStatus = this.getRepositoryHealth(repo);
            if (healthStatus.status !== 'good') {
                healthIssues.push({
                    repo: repo.name,
                    status: healthStatus.status,
                    message: healthStatus.message
                });
            }
        });
        
        // Format total size
        const totalSizeFormatted = this.formatBytes(totalSize);
        
        // Update summary metrics in UI
        document.getElementById('summary-total-repos').textContent = totalRepos;
        document.getElementById('summary-recent-commits').textContent = recentCommits;
        document.getElementById('summary-largest-repo').textContent = 
            largestRepo.name !== 'None' ? `${largestRepo.name} (${largestRepo.size_formatted})` : 'None';
        document.getElementById('summary-total-size').textContent = totalSizeFormatted;
        
        // Update recent activity
        this.updateRecentActivity(recentActivity);
        
        // Update health status
        this.updateHealthStatus(healthIssues);
    },
    
    /**
     * Update recent activity section
     * @param {Array} activities - List of recent activities
     */
    updateRecentActivity(activities) {
        const activityContainer = document.getElementById('summary-recent-activity');
        if (!activityContainer) return;
        
        if (activities.length === 0) {
            activityContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    No recent activity in the past 24 hours
                </div>
            `;
            return;
        }
        
        // Sort activities by date (newest first)
        activities.sort((a, b) => b.date - a.date);
        
        // Limit to 5 most recent activities
        const recentActivities = activities.slice(0, 5);
        
        let activityHtml = '<ul class="list-group">';
        
        recentActivities.forEach(activity => {
            const time = activity.date.toLocaleTimeString();
            activityHtml += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-code-branch text-primary me-2"></i>
                        <strong>${activity.repo}</strong>
                    </div>
                    <span class="badge bg-primary rounded-pill">${time}</span>
                </li>
            `;
        });
        
        activityHtml += '</ul>';
        activityContainer.innerHTML = activityHtml;
    },
    
    /**
     * Update health status section
     * @param {Array} healthIssues - List of repositories with health issues
     */
    updateHealthStatus(healthIssues) {
        const healthContainer = document.getElementById('summary-health-status');
        if (!healthContainer) return;
        
        if (healthIssues.length === 0) {
            healthContainer.innerHTML = `
                <div class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>
                    All repositories are healthy
                </div>
            `;
            return;
        }
        
        let healthHtml = '<ul class="list-group">';
        
        healthIssues.forEach(issue => {
            let badgeClass = 'bg-warning';
            if (issue.status === 'poor') badgeClass = 'bg-danger';
            
            healthHtml += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <strong>${issue.repo}</strong>
                    </div>
                    <span class="badge ${badgeClass} rounded-pill">${issue.status}</span>
                </li>
            `;
        });
        
        healthHtml += '</ul>';
        healthContainer.innerHTML = healthHtml;
    },
    
    /**
     * Get repository health status
     * @param {Object} repo - Repository object
     * @returns {Object} Health status object
     */
    getRepositoryHealth(repo) {
        if (!repo.last_commit) {
            return {
                status: 'unknown',
                message: 'No commit history available'
            };
        }
        
        const lastCommitDate = new Date(repo.last_commit);
        const now = new Date();
        const daysSinceLastCommit = Math.floor((now - lastCommitDate) / (1000 * 60 * 60 * 24));
        
        if (daysSinceLastCommit <= 7) {
            return {
                status: 'good',
                message: 'Active repository'
            };
        } else if (daysSinceLastCommit <= 30) {
            return {
                status: 'fair',
                message: 'Moderately active repository'
            };
        } else {
            return {
                status: 'poor',
                message: 'Inactive repository'
            };
        }
    },
    
    /**
     * Parse size string to bytes
     * @param {string} sizeStr - Size string (e.g., "1.5 MB", "800 KB")
     * @returns {number} Size in MB
     */
    parseSize(sizeStr) {
        if (!sizeStr || typeof sizeStr !== 'string') return 0;
        
        // Handle empty strings or non-string values
        sizeStr = sizeStr.trim();
        if (!sizeStr) return 0;
        
        // Try to match the pattern: number followed by unit (KB, MB, GB, TB)
        const match = sizeStr.match(/^([\d.]+)\s*(B|KB|MB|GB|TB)$/i);
        if (!match) {
            console.warn(`Could not parse size string: ${sizeStr}`);
            return 0;
        }
        
        const [, size, unit] = match;
        const value = parseFloat(size);
        
        if (isNaN(value)) {
            console.warn(`Invalid size value: ${size}`);
            return 0;
        }
        
        // Convert to MB based on unit
        switch (unit.toUpperCase()) {
            case 'B':
                return value / (1024 * 1024);
            case 'KB':
                return value / 1024;
            case 'MB':
                return value;
            case 'GB':
                return value * 1024;
            case 'TB':
                return value * 1024 * 1024;
            default:
                console.warn(`Unknown size unit: ${unit}`);
                return 0;
        }
    },
    
    /**
     * Format bytes to human-readable size
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        
        return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Will be initialized via index.html
});
