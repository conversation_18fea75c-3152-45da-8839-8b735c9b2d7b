# Backend Development Setup Guide

## Local Development Environment

### Prerequisites

- Python 3.12 or higher
- PostgreSQL 16+ installed locally
- Redis 5.0.1+ installed locally

### Initial Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd project-tracker
   ```

2. Create and activate a virtual environment:
   ```bash
   cd backend
   python -m venv venv
   
   # On Linux/Mac
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

5. Set up local database:
   ```bash
   # Create PostgreSQL database
   createdb project_tracker
   
   # Apply database schema
   python back-main.py db init
   python back-main.py db migrate
   python back-main.py db upgrade
   ```

### Development Database

We've included a script to set up a local development database with sample data:

```bash
# Set up development database with sample data
python back-main.py setup_dev_db
```

This will create:
- Sample users with different roles
- Example projects with metrics
- Test git operations with performance data

### Development Workflow

#### Running the Development Server

```bash
# Start the Flask development server
python app.py run

# Start the WebSocket server
python monitoring.py run_websocket
```

#### Code Quality Tools

We use pre-commit hooks for code quality checks:

```bash
# Install pre-commit hooks
pip install pre-commit
pre-commit install

# Run linting manually
pre-commit run --all-files
```

#### Testing

```bash
# Run all tests
python -m pytest

# Run tests with coverage
python -m pytest --cov=app

# Generate coverage report
python -m pytest --cov=app --cov-report=html
```

### Database Migrations

```bash
# Create a new migration after model changes
python back-main.py db migrate -m "description of changes"

# Apply migrations
python back-main.py db upgrade

# Revert last migration
python back-main.py db downgrade
```

### Connecting to Frontend Services

During local development:
- CORS is enabled for frontend connections
- WebSocket server accepts connections from the frontend development server

### Building for Production

```bash
# Create production-ready package
python setup.py bdist_wheel
```

### Troubleshooting

#### Common Issues

1. **Database connection errors**:
   - Verify PostgreSQL is running
   - Check database credentials in `.env`
   - Ensure database exists

2. **Redis connection errors**:
   - Verify Redis server is running
   - Check Redis connection string in `.env`

3. **Module import errors**:
   - Ensure virtual environment is activated
   - Check that all dependencies are installed
