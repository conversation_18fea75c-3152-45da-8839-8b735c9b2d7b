#!/bin/bash
# Script to set up certificate access for the database service

# Configuration
CERT_DIR="/etc/letsencrypt/archive/chcit.org"
CERT_GROUP="ssl-cert"
DB_SERVICE_USER="database-service"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "This script must be run as root"
  exit 1
fi

# Check if certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
  echo "Certificate directory $CERT_DIR does not exist"
  exit 1
fi

# Check if certificate group exists
getent group "$CERT_GROUP" > /dev/null
if [ $? -ne 0 ]; then
  echo "Creating certificate group $CERT_GROUP"
  groupadd "$CERT_GROUP"
fi

# Add database service user to certificate group
usermod -a -G "$CERT_GROUP" "$DB_SERVICE_USER"
if [ $? -ne 0 ]; then
  echo "Failed to add user $DB_SERVICE_USER to group $CERT_GROUP"
  exit 1
fi

# Set permissions on certificate directory
echo "Setting permissions on certificate directory"
chmod 750 "$CERT_DIR"
chmod 640 "$CERT_DIR"/*.pem
chgrp -R "$CERT_GROUP" "$CERT_DIR"

# Also check and set permissions for the live directory
LIVE_CERT_DIR="/etc/letsencrypt/live/chcit.org"
if [ -d "$LIVE_CERT_DIR" ]; then
  echo "Setting permissions on live certificate directory"
  chmod 750 "$LIVE_CERT_DIR"
  chmod 640 "$LIVE_CERT_DIR"/*.pem 2>/dev/null || true
  chgrp -R "$CERT_GROUP" "$LIVE_CERT_DIR"
fi

# Set permissions on the parent directories to ensure access
chmod 755 /etc/letsencrypt
chmod 750 /etc/letsencrypt/archive
chmod 750 /etc/letsencrypt/live
chgrp "$CERT_GROUP" /etc/letsencrypt/archive
chgrp "$CERT_GROUP" /etc/letsencrypt/live

echo "Certificate access setup complete"
echo "User $DB_SERVICE_USER can now access certificates in $CERT_DIR and $LIVE_CERT_DIR"
