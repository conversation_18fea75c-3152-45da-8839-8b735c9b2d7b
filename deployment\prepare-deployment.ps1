#!/usr/bin/env pwsh
# Project Tracker Deployment Script
# This script prepares deployment packages for the Project Tracker application with multi-server architecture for both frontend and backend components.
# Logging Configuration
$LogFile = Join-Path -Path (Get-Location) -ChildPath "prepare-deployment.log"
$ProgressFile = Join-Path -Path (Get-Location) -ChildPath "deployment-progress.log"

# Deployment Directories
$DEPLOY_DIR = Join-Path -Path (Get-Location) -ChildPath "project-tracker-deploy"
$FRONTEND_DEPLOY_DIR = Join-Path -Path $DEPLOY_DIR -ChildPath "frontend"
$BACKEND_DEPLOY_DIR = Join-Path -Path $DEPLOY_DIR -ChildPath "backend"

# Add parameter validation and error handling
param (
    [Parameter(Mandatory=$true)]
    [string]$DeploymentEnvironment,
    
    [Parameter(Mandatory=$false)]
    [string]$BackupLocation
)

# Use structured configuration instead of arrays
$DeploymentConfig = @{
    Scripts = @{
        Backend = @{
            Required = @(
                "../backend/scripts/ssl-setup.sh",
                "../backend/scripts/verify-nginx-modules.sh"
            )
            Optional = @(
                "../backend/scripts/setup-code-quality.sh",
                "../backend/scripts/monitor-performance.sh"
            )
        }
        Frontend = @{
            Required = @(
                "../frontend/scripts/build.sh",
                "../frontend/scripts/validate.sh"
            )
        }
    }
    ConfigFiles = @{
        Backend = @{
            Nginx = "../backend/config/nginx.conf"
            Service = "../backend/config/back-server.service"
            Performance = "../backend/config/performance-dashboard.json"
        }
    }
}

# Add validation function
function Test-RequiredFiles {
    param($Files)
    foreach ($file in $Files) {
        if (-not (Test-Path $file)) {
            throw "Required file not found: $file"
        }
    }
}

# Logging Function
function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [ValidateSet("INFO", "WARNING", "ERROR", "PROGRESS")]
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    # Console output with color
    switch ($Level) {
        "INFO" { Write-Host $Message -ForegroundColor Green }
        "WARNING" { Write-Host $Message -ForegroundColor Yellow }
        "ERROR" { Write-Host $Message -ForegroundColor Red }
        "PROGRESS" { Write-Host $Message -ForegroundColor Blue }
    }
    
    # Log to file
    Add-Content -Path $LogFile -Value $LogEntry
}

# Progress Tracking Function
function Update-Progress {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Step,
        
        [Parameter(Mandatory=$true)]
        [string]$Status
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $ProgressEntry = "[$Timestamp] $Step : $Status"
    
    Write-Log -Message "$Step : $Status" -Level "PROGRESS"
    Add-Content -Path $ProgressFile -Value $ProgressEntry
}

# Validate Prerequisites
function Test-Prerequisites {
    Write-Log -Message "Validating deployment prerequisites..."
    Update-Progress -Step "Prerequisite Check" -Status "Starting validation"
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 7) {
        Write-Log -Message "PowerShell 7+ is required" -Level "ERROR"
        throw "Unsupported PowerShell version"
    }
    
    # Check available disk space
    $Drive = (Get-Location).Drive
    $FreeSpace = (Get-PSDrive $Drive.Name).Free / 1GB
    
    Write-Log -Message "Available Disk Space: $FreeSpace GB"
    
    if ($FreeSpace -lt 5) {
        Write-Log -Message "Insufficient disk space" -Level "ERROR"
        throw "Minimum 5GB free space required"
    }
    
    # Check Node.js version
    try {
        $NodeVersion = node -v
        Write-Log -Message "Node.js version: $NodeVersion"
    } catch {
        Write-Log -Message "Node.js not found" -Level "ERROR"
        throw "Node.js is required for frontend build"
    }
    
    # Check npm version
    try {
        $NpmVersion = npm -v
        Write-Log -Message "npm version: $NpmVersion"
    } catch {
        Write-Log -Message "npm not found" -Level "ERROR"
        throw "npm is required for frontend build"
    }
    
    # Check Python version
    try {
        $PythonVersion = python --version
        Write-Log -Message "Python version: $PythonVersion"
    } catch {
        Write-Log -Message "Python not found" -Level "ERROR"
        throw "Python is required for backend build"
    }
    
    Update-Progress -Step "Prerequisite Check" -Status "Validation completed successfully"
}

# Validate PostgreSQL
function Test-PostgreSQL {
    Write-Host "Validating PostgreSQL connection..."
    try {
        # Add logic to check PostgreSQL connection
        $PostgreSQLConnection = New-Object System.Data.SqlClient.SqlConnection("Server=tcp:localhost,5432;Database=project-tracker;User Id=********;Password=********;")
        $PostgreSQLConnection.Open()
        $PostgreSQLConnection.Close()
    } catch {
        throw "PostgreSQL validation failed: $_"
    }
}

# Validate Redis
function Test-Redis {
    Write-Host "Validating Redis connection..."
    try {
        # Add logic to check Redis connection
        $RedisConnection = New-Object System.Net.Sockets.TcpClient("localhost", 6379)
        $RedisConnection.Close()
    } catch {
        throw "Redis validation failed: $_"
    }
}

# Frontend Scripts
$FrontendScripts = @(
    "../frontend/scripts/configure-frontend.sh",
    "../frontend/scripts/validate-frontend.sh"
)

# Backend Scripts
$BackendScripts = @(
    "../backend/scripts/configure-backend.sh",
    "../backend/scripts/validate-backend.sh",
    "../backend/scripts/backup-cron.sh",
    "../backend/scripts/security-hardening.sh",
    "../backend/scripts/ssl-setup.sh",
    "../backend/scripts/verify-nginx-modules.sh",
    "../backend/scripts/setup-code-quality.sh",
    "../backend/scripts/monitor-performance.sh"
)

# Frontend Config Files
$FrontendConfigFiles = @(
    "../frontend/config/nginx.conf",
    "../frontend/config/front-server.service"
)

# Backend Config Files
$BackendConfigFiles = @(
    "../backend/config/nginx.conf",
    "../backend/config/back-server.service",
    "../backend/config/performance-dashboard.json",
    "../backend/config/nginx-modules.conf",
    "../backend/config/.pre-commit-config.yaml",
    "../backend/config/.flake8",
    "../backend/config/.eslintrc.json",
    "../backend/config/.prettierrc"
)

# Runtime Directories
$RuntimeDirs = @(
    "logs",
    "data"
)

# Create deployment directory
Write-Log -Message "Creating deployment directory: $DEPLOY_DIR"
Update-Progress -Step "Deployment Preparation" -Status "Creating deployment directory"

try {
    # Validate prerequisites
    Test-Prerequisites
    
    # Validate PostgreSQL
    Test-PostgreSQL
    
    # Validate Redis
    Test-Redis
    
    # Remove existing deployment directory if it exists
    if (Test-Path $DEPLOY_DIR) {
        Write-Log -Message "Removing existing deployment directory"
        Remove-Item -Path $DEPLOY_DIR -Recurse -Force
    }
    
    # Create main deployment directory
    New-Item -ItemType Directory -Force -Path $DEPLOY_DIR | Out-Null
    
    # Create frontend and backend deployment directories
    New-Item -ItemType Directory -Force -Path $FRONTEND_DEPLOY_DIR | Out-Null
    New-Item -ItemType Directory -Force -Path $BACKEND_DEPLOY_DIR | Out-Null
    
    # Create config directories
    New-Item -ItemType Directory -Force -Path "$FRONTEND_DEPLOY_DIR/config" | Out-Null
    New-Item -ItemType Directory -Force -Path "$BACKEND_DEPLOY_DIR/config" | Out-Null
    
    # Create scripts directories
    New-Item -ItemType Directory -Force -Path "$FRONTEND_DEPLOY_DIR/scripts" | Out-Null
    New-Item -ItemType Directory -Force -Path "$BACKEND_DEPLOY_DIR/scripts" | Out-Null
    
    # Copy frontend scripts
    foreach ($script in $FrontendScripts) {
        Write-Log -Message "Copying $script"
        Update-Progress -Step "File Copy" -Status "Copying $script"
        $targetPath = Join-Path -Path $FRONTEND_DEPLOY_DIR -ChildPath "scripts/$(Split-Path -Leaf $script)"
        Copy-Item -Path $script -Destination $targetPath -ErrorAction SilentlyContinue
    }
    
    # Copy backend scripts
    foreach ($script in $BackendScripts) {
        Write-Log -Message "Copying $script"
        Update-Progress -Step "File Copy" -Status "Copying $script"
        $targetPath = Join-Path -Path $BACKEND_DEPLOY_DIR -ChildPath "scripts/$(Split-Path -Leaf $script)"
        Copy-Item -Path $script -Destination $targetPath -ErrorAction SilentlyContinue
    }
    
    # Copy frontend configuration files
    foreach ($config in $FrontendConfigFiles) {
        Write-Log -Message "Copying $config"
        Update-Progress -Step "File Copy" -Status "Copying $config"
        $targetPath = Join-Path -Path $FRONTEND_DEPLOY_DIR -ChildPath "config/$(Split-Path -Leaf $config)"
        Copy-Item -Path $config -Destination $targetPath -ErrorAction SilentlyContinue
    }
    
    # Copy backend configuration files
    foreach ($config in $BackendConfigFiles) {
        Write-Log -Message "Copying $config"
        Update-Progress -Step "File Copy" -Status "Copying $config"
        $targetPath = Join-Path -Path $BACKEND_DEPLOY_DIR -ChildPath "config/$(Split-Path -Leaf $config)"
        Copy-Item -Path $config -Destination $targetPath -ErrorAction SilentlyContinue
    }
    
    # Create runtime directories for both frontend and backend
    foreach ($dir in $RuntimeDirs) {
        Write-Log -Message "Creating directory: $dir"
        Update-Progress -Step "Directory Creation" -Status "Creating $dir"
        New-Item -ItemType Directory -Force -Path "$FRONTEND_DEPLOY_DIR/$dir" | Out-Null
        New-Item -ItemType Directory -Force -Path "$BACKEND_DEPLOY_DIR/$dir" | Out-Null
    }
    
    # Package Python backend
    Write-Host "Packaging Python backend..."
    if (Test-Path "../backend/back-requirements.txt") {
        Copy-Item "../backend/back-requirements.txt" -Destination "$BACKEND_DEPLOY_DIR/back-requirements.txt" -Force
    } else {
        throw "Backend back-requirements.txt not found"
    }
    
    # Copy backend source files
    Write-Host "Copying backend source files..."
    New-Item -Path "$BACKEND_DEPLOY_DIR/src" -ItemType Directory -Force
    Copy-Item "../backend/src/*" -Destination "$BACKEND_DEPLOY_DIR/src" -Recurse -Force
    
    # Build frontend
    Write-Host "Building frontend..."
    Push-Location "../frontend"
    try {
        npm install --package-lock-only front-package.json
        npm run build
        if ($LASTEXITCODE -ne 0) { throw "Frontend build failed" }
        
        # Copy built frontend
        New-Item -Path "$FRONTEND_DEPLOY_DIR/build" -ItemType Directory -Force
        Copy-Item "build/*" -Destination "$FRONTEND_DEPLOY_DIR/build" -Recurse -Force
    } finally {
        Pop-Location
    }
    
    # Create deployment instructions for frontend
    $FRONTEND_DEPLOY_INSTRUCTIONS = @'
# Frontend Server Deployment

## Package Contents

1. Scripts:
   - configure-frontend.sh - Sets up frontend server environment
   - validate-frontend.sh - Validates frontend deployment

2. Configuration:
   - nginx.conf - Web server configuration with Brotli module
   - front-server.service - Systemd service file

3. Build:
   - build/ - React/TypeScript frontend build

4. Runtime Directories:
   - logs/ - Application logs
   - data/ - Application data

## Deployment Steps

1. Copy files to server:
   ```bash
   scp -r project-tracker-deploy/frontend user@your-frontend-server:/tmp/
   ```

2. Run the configuration script:
   ```bash
   cd /tmp/frontend
   sudo -S su - root << "EOF"
   bash configure-frontend.sh
   EOF
   ```

3. Verify deployment:
   ```bash
   bash validate-frontend.sh
   ```
'@
    
    # Create deployment instructions for backend
    $BACKEND_DEPLOY_INSTRUCTIONS = @'
# Backend Server Deployment

## Package Contents

1. Scripts:
   - configure-backend.sh - Sets up backend server environment
   - validate-backend.sh - Validates backend deployment
   - backup-cron.sh - Configures automated backups
   - security-hardening.sh - Applies security configurations
   - ssl-setup.sh - Configures SSL certificates
   - monitor-performance.sh - Monitors performance metrics
   - verify-nginx-modules.sh - Validates Nginx modules
   - setup-code-quality.sh - Sets up code quality tools

2. Configuration:
   - nginx.conf - Web server configuration with Brotli module
   - back-server.service - Systemd service file
   - performance-dashboard.json - Performance dashboard configuration
   - nginx-modules.conf - Nginx module-specific configuration
   - .pre-commit-config.yaml - Pre-commit configuration
   - .flake8 - Flake8 configuration
   - .eslintrc.json - ESLint configuration
   - .prettierrc - Prettier configuration

3. Source Code:
   - src/ - Application source code

4. Runtime Directories:
   - logs/ - Application logs
   - data/ - Application data

## Deployment Steps

1. Copy files to server:
   ```bash
   scp -r project-tracker-deploy/backend user@your-backend-server:/tmp/
   ```

2. Run the configuration script:
   ```bash
   cd /tmp/backend
   sudo -S su - root << "EOF"
   bash configure-backend.sh
   EOF
   ```

3. Verify deployment:
   ```bash
   bash validate-backend.sh
   ```

4. Verify Nginx modules:
   ```bash
   bash verify-nginx-modules.sh
   ```
'@
    
    # Write deployment instructions to files
    Set-Content -Path "$FRONTEND_DEPLOY_DIR/README.md" -Value $FRONTEND_DEPLOY_INSTRUCTIONS
    Set-Content -Path "$BACKEND_DEPLOY_DIR/README.md" -Value $BACKEND_DEPLOY_INSTRUCTIONS
    
    # Create overall deployment instructions
    $DEPLOY_INSTRUCTIONS = @'
# Project Tracker Multi-Server Deployment

This package contains deployment files for both frontend and backend servers.

## Frontend Server

See `frontend/README.md` for frontend server deployment instructions.

## Backend Server

See `backend/README.md` for backend server deployment instructions.

## Multi-Server Architecture

The Project Tracker uses a multi-server architecture with:

1. **Frontend Server** - Dedicated Ubuntu server for React/TypeScript frontend
   - Serves static assets
   - Handles frontend monitoring
   - Manages WebSocket client connections

2. **Backend Server** - Dedicated Ubuntu server for Flask/Python backend
   - Provides REST APIs
   - Manages database connections
   - Handles WebSocket server operations
   - Processes business logic

This separation provides better scalability, resource isolation, and deployment flexibility.
'@
    
    # Write overall deployment instructions
    Set-Content -Path "$DEPLOY_DIR/README.md" -Value $DEPLOY_INSTRUCTIONS
    
    # Create deployment package
    $DeploymentPackage = "project-tracker-deploy.zip"
    Compress-Archive -Path $DEPLOY_DIR -DestinationPath $DeploymentPackage -Force
    
    Write-Log -Message "Deployment package created successfully: $DeploymentPackage"
    Update-Progress -Step "Deployment Preparation" -Status "Completed"
    
    Write-Host "Deployment package created successfully: $DeploymentPackage" -ForegroundColor Green
} catch {
    Write-Log -Message "Error: $_" -Level "ERROR"
    Update-Progress -Step "Deployment Preparation" -Status "Failed: $_"
    
    Write-Host "Deployment preparation failed: $_" -ForegroundColor Red
    exit 1
}
