/**
 * Git Repository Size Comparison Chart Module
 * Handles the visualization of repository size comparisons
 */
const GitSizeComparisonChart = {
    // Chart-specific state
    state: {
        chart: null,
        loading: false,
        error: null
    },
    
    // Configuration
    config: {
        chartId: 'size-comparison-chart',
        colors: {
            background: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)'
            ],
            border: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)'
            ]
        },
        maxRepos: 5  // Maximum number of repositories to show in comparison
    },
    
    /**
     * Initialize the chart module
     * @returns {Object} This module for chaining
     */
    init() {
        // Nothing to initialize here
        return this;
    },
    
    /**
     * Clean up chart instance
     */
    cleanupChart() {
        if (this.state.chart) {
            GitUtils.cleanupChartInstance(this.state.chart);
            this.state.chart = null;
        }
    },
    
    /**
     * Render size comparison chart for the repositories
     * @param {Array} repositories - Array of repository objects
     * @param {string} selectedRepoName - Currently selected repository name
     * @returns {void}
     */
    renderChart(repositories, selectedRepoName) {
        // Get the canvas element
        const canvas = document.getElementById(this.config.chartId);
        if (!canvas) {
            console.error('Size comparison chart canvas not found');
            return;
        }
        
        // Clean up any existing chart
        this.cleanupChart();
        
        // Show loading state
        this.updateStatus('loading', '');
        
        try {
            // Process repository data for chart
            const chartData = this.processRepositoriesForSizeChart(repositories, selectedRepoName);
            
            // Check if we have data to display
            if (!chartData || !chartData.labels || chartData.labels.length === 0) {
                this.updateStatus('error', 'No size data available');
                return;
            }
            
            // Create chart configuration
            const config = {
                type: 'bar',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: 'Repository Size (MB)',
                        data: chartData.data,
                        backgroundColor: this.getBackgroundColors(chartData.labels.length, chartData.selectedIndex),
                        borderColor: this.getBorderColors(chartData.labels.length, chartData.selectedIndex),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Size (MB)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Repository'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Repository Size Comparison'
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const sizeInMB = context.raw;
                                    return `Size: ${sizeInMB.toFixed(2)} MB`;
                                }
                            }
                        }
                    }
                }
            };
            
            // Create chart
            this.state.chart = new Chart(canvas, config);
            
            // Update status to success
            this.updateStatus('success', '');
        } catch (error) {
            console.error('Error rendering size comparison chart:', error);
            this.updateStatus('error', 'Failed to render size comparison chart');
        }
    },
    
    /**
     * Update chart status and UI elements
     * @param {string} status - Status (loading, success, error)
     * @param {string} message - Status message
     */
    updateStatus(status, message) {
        this.state.loading = status === 'loading';
        this.state.error = status === 'error' ? message : null;
        
        // Update UI elements
        const chartContainer = document.getElementById('size-comparison-container');
        const loadingElement = document.getElementById('size-chart-loading');
        const errorElement = document.getElementById('size-chart-error');
        const chartElement = document.getElementById(this.config.chartId);
        
        if (chartContainer && loadingElement && errorElement && chartElement) {
            // Handle loading state
            if (status === 'loading') {
                loadingElement.style.display = 'flex';
                errorElement.style.display = 'none';
                chartElement.style.display = 'none';
            }
            // Handle error state
            else if (status === 'error') {
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
                errorElement.textContent = message || 'Failed to load size comparison';
                chartElement.style.display = 'none';
            }
            // Handle success state
            else {
                loadingElement.style.display = 'none';
                errorElement.style.display = 'none';
                chartElement.style.display = 'block';
            }
        }
    },
    
    /**
     * Process repositories for size comparison chart
     * @param {Array} repositories - Array of repository objects
     * @param {string} selectedRepoName - Name of the selected repository
     * @returns {Object} Processed data for chart
     */
    processRepositoriesForSizeChart(repositories, selectedRepoName) {
        if (!repositories || !Array.isArray(repositories) || repositories.length === 0) {
            return { labels: [], data: [], selectedIndex: -1 };
        }
        
        // Parse sizes and create array of { name, size, sizeMB } objects
        const reposWithSize = repositories.map(repo => {
            const sizeMB = GitUtils.parseSize(repo.size);
            return {
                name: repo.name,
                size: repo.size,
                sizeMB: sizeMB
            };
        });
        
        // Sort by size (descending)
        reposWithSize.sort((a, b) => b.sizeMB - a.sizeMB);
        
        // Find selected repository
        let selectedRepo = reposWithSize.find(repo => repo.name === selectedRepoName);
        let selectedIndex = selectedRepo ? reposWithSize.indexOf(selectedRepo) : -1;
        
        // Limit to largest repositories plus selected repository
        let reposToShow = reposWithSize.slice(0, this.config.maxRepos);
        
        // If selected repository is not in top N, replace the smallest with it
        if (selectedRepo && selectedIndex >= this.config.maxRepos) {
            reposToShow[this.config.maxRepos - 1] = selectedRepo;
            selectedIndex = this.config.maxRepos - 1;
        }
        
        // Extract labels and data
        const labels = reposToShow.map(repo => repo.name);
        const data = reposToShow.map(repo => repo.sizeMB);
        
        // Find new selectedIndex in the filtered list
        if (selectedRepo) {
            selectedIndex = labels.indexOf(selectedRepo.name);
        }
        
        return { labels, data, selectedIndex };
    },
    
    /**
     * Get background colors for chart bars
     * @param {number} count - Number of bars
     * @param {number} selectedIndex - Index of selected repository
     * @returns {Array} Array of background colors
     */
    getBackgroundColors(count, selectedIndex) {
        const colors = [];
        
        for (let i = 0; i < count; i++) {
            if (i === selectedIndex) {
                // Highlight selected repository with a different color
                colors.push('rgba(255, 159, 64, 0.5)');
            } else {
                // Use color from the predefined color array, cycling if needed
                colors.push(this.config.colors.background[i % this.config.colors.background.length]);
            }
        }
        
        return colors;
    },
    
    /**
     * Get border colors for chart bars
     * @param {number} count - Number of bars
     * @param {number} selectedIndex - Index of selected repository
     * @returns {Array} Array of border colors
     */
    getBorderColors(count, selectedIndex) {
        const colors = [];
        
        for (let i = 0; i < count; i++) {
            if (i === selectedIndex) {
                // Highlight selected repository with a different color
                colors.push('rgba(255, 159, 64, 1)');
            } else {
                // Use color from the predefined color array, cycling if needed
                colors.push(this.config.colors.border[i % this.config.colors.border.length]);
            }
        }
        
        return colors;
    }
};

// Expose the module globally
window.GitSizeComparisonChart = GitSizeComparisonChart;

