param()

# Suppress the PSScriptAnalyzer warning for 'date' command in bash scripts
[Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingCmdletAliases', '', Scope='Script', Target='*')]

 

# Git Repository Service Deployment Tool

# Configuration
$GIT_SERVER = "git.chcit.org"
$SSH_USER = "btaylor-admin"
$SOURCE_DIR = "$PSScriptRoot\git-repo-service"
$REMOTE_DIR = "/opt/git-dashboard/git-repo-service"
$REMOTE_BIN_DIR = "/opt/git-dashboard/bin"
$REMOTE_SYSTEMD_DIR = "/opt/git-dashboard/systemd"
$REPO_PATH = "/home/<USER>/repositories"

# Create local log directory if it doesn't exist
$LOCAL_LOG_DIR = "$PSScriptRoot\logs"
if (-not (Test-Path $LOCAL_LOG_DIR)) {
    New-Item -ItemType Directory -Path $LOCAL_LOG_DIR -Force | Out-Null
}

# Function to test SSH connection
function Test-SSHConnection {
    Write-Host ""
    Write-Host "Testing SSH connection to $GIT_SERVER..." -ForegroundColor Cyan
    
    ssh -o BatchMode=yes -o ConnectTimeout=5 "${SSH_USER}@${GIT_SERVER}" "echo Connection successful"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to connect to Git server." -ForegroundColor Red
        Write-Host "Please ensure that:"
        Write-Host "  1. The server is reachable"
        Write-Host "  2. SSH key authentication is set up"
        Write-Host "  3. The username is correct"
        return $false
    }
    
    Write-Host "SSH connection successful." -ForegroundColor Green
    return $true
}

function Install-Dependencies {
    Write-Host ""
    Write-Host "Installing dependencies on Git server..." -ForegroundColor Cyan
    
    # Create the installation script
    $installScript = @"
#!/bin/bash

# Update package lists
sudo apt update

# Check for installed packages
echo "Checking installed packages..."
INSTALLED_PKGS=()
MISSING_PKGS=()

PKGS=("build-essential" "cmake" "libgit2-dev" "libssl-dev" "libjsoncpp-dev" "libboost-all-dev" "libcurl4-openssl-dev")

for pkg in "${PKGS[@]}"; do
    if dpkg -s $pkg >/dev/null 2>&1; then
        INSTALLED_PKGS+=($pkg)
    else
        MISSING_PKGS+=($pkg)
    fi
done

# Display installed packages
echo "Installed packages:"
for pkg in "${INSTALLED_PKGS[@]}"; do
    echo "  - $pkg"
done

# Display missing packages
echo "Missing packages:"
if [ ${#MISSING_PKGS[@]} -eq 0 ]; then
    echo "  None - All dependencies are installed."
else
    for pkg in "${MISSING_PKGS[@]}"; do
        echo "  - $pkg"
    done
fi

# Special check for JsonCpp header
echo "Checking for JsonCpp header file..."
JSON_HEADER_PATH=$(find /usr -name json.h | grep jsoncpp | head -1)
if [ -n "$JSON_HEADER_PATH" ]; then
    echo "[OK] JsonCpp header found at: $JSON_HEADER_PATH"
else
    echo "[MISSING] JsonCpp header"
    if [[ " ${MISSING_PKGS[*]} " == *" libjsoncpp-dev "* ]]; then
        echo "JsonCpp package is missing. Will be installed."
    else
        echo "JsonCpp package is installed but header is missing. Will reinstall."
        MISSING_PKGS+=("libjsoncpp-dev")
    fi
fi
    
# Ask for confirmation to install missing packages
if [ ${#MISSING_PKGS[@]} -gt 0 ]; then
    echo "Installing missing packages..."
    sudo apt install -y "${MISSING_PKGS[@]}"
fi
"@
    
    # Create a temporary script file
    $tempScriptPath = "$env:TEMP\install_deps.sh"
    $installScript | Out-File -FilePath $tempScriptPath -Encoding ASCII
    
    # Upload the script to the server
    scp $tempScriptPath "${SSH_USER}@${GIT_SERVER}:/tmp/install_deps.sh"
    
    # Execute the script
    ssh "${SSH_USER}@${GIT_SERVER}" "chmod +x /tmp/install_deps.sh && /tmp/install_deps.sh && rm /tmp/install_deps.sh"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install dependencies." -ForegroundColor Red
        return $false
    }
    
    Write-Host "Dependencies installed successfully." -ForegroundColor Green
    return $true
}

function Deploy-SourceCode {
    Write-Host ""
    Write-Host "Deploying source code to Git server..." -ForegroundColor Cyan
    
    # Create a temporary directory for the source code
    $tempDir = "~/temp_git_repo_service"
    
    # Create the deployment script
    $deployScript = @"
#!/bin/bash

# Create temporary directory
sudo mkdir -p $REMOTE_DIR
sudo cp -r $tempDir/* $REMOTE_DIR/
sudo chown -R www-data:www-data $REMOTE_DIR
rm -rf $tempDir

echo "Source code deployed successfully."
"@
    
    # Create a temporary script file
    $tempScriptPath = "$env:TEMP\deploy_code.sh"
    $deployScript | Out-File -FilePath $tempScriptPath -Encoding ASCII
    
    # Copy source files to the server's temporary directory
    Write-Host "Copying source files to Git server..." -ForegroundColor Cyan
    ssh "${SSH_USER}@${GIT_SERVER}" "mkdir -p $tempDir"
    scp -r "${SOURCE_DIR}/*" "${SSH_USER}@${GIT_SERVER}:${tempDir}/"
    
    # Upload the script to the server
    scp $tempScriptPath "${SSH_USER}@${GIT_SERVER}:/tmp/deploy_code.sh"
    
    # Execute the script
    ssh "${SSH_USER}@${GIT_SERVER}" "chmod +x /tmp/deploy_code.sh && /tmp/deploy_code.sh && rm /tmp/deploy_code.sh"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to deploy source code." -ForegroundColor Red
        return $false
    }
    
    Write-Host "Source code deployed successfully." -ForegroundColor Green
    return $true
}

function Build-Application {
    Write-Host ""
    Write-Host "Building application on Git server..." -ForegroundColor Cyan
    
    # Create timestamp for log file
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $logFile = "build-log-$timestamp.txt"
    $remoteLogPath = "/tmp/$logFile"
    $localLogDir = $LOCAL_LOG_DIR
    
    # Ensure local log directory exists
    if (-not (Test-Path $localLogDir)) {
        New-Item -Path $localLogDir -ItemType Directory -Force | Out-Null
    }
    
    # Run build with detailed logging
    Write-Host "Running CMake with detailed logging..." -ForegroundColor Cyan
    
    # Create build script with success/failure markers
    $buildScript = @"
#!/bin/bash

# Navigate to the application directory
cd $REMOTE_DIR

# Create build directory
sudo -u www-data mkdir -p build
cd build

# Run CMake with verbose output
sudo -u www-data cmake -DCMAKE_VERBOSE_MAKEFILE=ON .. > $remoteLogPath 2>&1
CMAKE_RESULT=\$?

# Check if JsonCpp header exists
JSON_HEADER_PATH=$(find /usr -name json.h | grep jsoncpp | head -1)
if [ -z "$JSON_HEADER_PATH" ]; then
    echo 'BUILD_FAILED: Missing JsonCpp header file. Please run dependency installation first.' >> $remoteLogPath
    exit 1
fi

echo "Using JsonCpp header at: $JSON_HEADER_PATH" >> $remoteLogPath

# Run make with verbose output if CMake was successful
if [ \$CMAKE_RESULT -eq 0 ]; then
    sudo -u www-data make -j\$(nproc) VERBOSE=1 >> $remoteLogPath 2>&1
    MAKE_RESULT=\$?
    
    if [ \$MAKE_RESULT -eq 0 ]; then
        echo 'BUILD_SUCCESS' >> $remoteLogPath
        exit 0
    else
        echo 'BUILD_FAILED: Make process failed' >> $remoteLogPath
        exit 1
    fi
else
    echo 'BUILD_FAILED: CMake configuration failed' >> $remoteLogPath
    exit 1
fi
"@
    
    # Create a temporary script file
    $tempScriptPath = "$env:TEMP\build_app.sh"
    $buildScript | Out-File -FilePath $tempScriptPath -Encoding ASCII
    
    # Upload the script to the server
    Write-Host "Uploading build script to server..." -ForegroundColor Cyan
    scp $tempScriptPath "${SSH_USER}@${GIT_SERVER}:/tmp/build_app.sh"
    
    # Execute the script
    ssh "${SSH_USER}@${GIT_SERVER}" "chmod +x /tmp/build_app.sh && /tmp/build_app.sh"
    $buildExitCode = $LASTEXITCODE
    
    # Clean up the temporary script
    ssh "${SSH_USER}@${GIT_SERVER}" "rm /tmp/build_app.sh"
    
    # Copy log file back to local machine
    Write-Host "Copying build log to local machine..." -ForegroundColor Cyan
    scp "${SSH_USER}@${GIT_SERVER}:${remoteLogPath}" "${localLogDir}\${logFile}"
    
    # Check if build was successful by looking for success marker in the log file
    $buildSuccess = Select-String -Path "${localLogDir}\${logFile}" -Pattern "BUILD_SUCCESS" -Quiet
    
    if (-not $buildSuccess -or $buildExitCode -ne 0) {
        Write-Host "Failed to build application. Check log file: ${localLogDir}\${logFile}" -ForegroundColor Red
        return $false
    }
    
    # Copy executable to bin directory
    Write-Host "Copying executable to bin directory..." -ForegroundColor Cyan
    ssh "${SSH_USER}@${GIT_SERVER}" "sudo mkdir -p $REMOTE_BIN_DIR && sudo cp $REMOTE_DIR/build/git-repo-service $REMOTE_BIN_DIR/ && sudo chown git:git $REMOTE_BIN_DIR/git-repo-service && sudo chmod 755 $REMOTE_BIN_DIR/git-repo-service"
    Write-Host "Executable copied to bin directory." -ForegroundColor Green
    
    # Display log file location
    Write-Host "Application built successfully." -ForegroundColor Green
    Write-Host "Build log saved to: ${localLogDir}\${logFile}" -ForegroundColor Cyan
    return $true
}

function Install-Service {
    Write-Host "\nInstalling Git Repository Service..." -ForegroundColor Cyan
    
    # Check if service is already installed
    $serviceStatus = ssh "${SSH_USER}@${GIT_SERVER}" "systemctl is-enabled git-repo-service 2>/dev/null || echo 'not-installed'"
    
    if ($serviceStatus -eq "not-installed") {
        Write-Host "Service is not installed" -ForegroundColor Yellow
    } else {
        Write-Host "Service is already installed" -ForegroundColor Cyan
    }
    
    $installService = Read-Host "Install/update service? (y/n)"
    if ($installService.ToLower() -ne "y") {
        Write-Host "Service installation skipped." -ForegroundColor Yellow
        return $true
    }
    
    # Copy the systemd service file
    Write-Host "Installing systemd service file..." -ForegroundColor Cyan
    scp "$PSScriptRoot\systemd\git-repo-service.service" "${SSH_USER}@${GIT_SERVER}:/tmp/git-repo-service.service"
    ssh "${SSH_USER}@${GIT_SERVER}" "sudo mkdir -p $REMOTE_SYSTEMD_DIR && sudo cp /tmp/git-repo-service.service $REMOTE_SYSTEMD_DIR/ && sudo mv /tmp/git-repo-service.service /etc/systemd/system/ && sudo chown root:root /etc/systemd/system/git-repo-service.service && sudo chmod 644 /etc/systemd/system/git-repo-service.service && sudo systemctl daemon-reload"
    
    # Make sure the executable is in the bin directory
    $execCheck = ssh "${SSH_USER}@${GIT_SERVER}" "if [ ! -f $REMOTE_BIN_DIR/git-repo-service ]; then echo 'missing'; else echo 'found'; fi"
    
    if ($execCheck -eq "missing") {
        Write-Host "Error: Executable not found in bin directory. Please build the application first." -ForegroundColor Red
        return $false
    }
    
    # Add www-data to git group if not already added
    ssh "${SSH_USER}@${GIT_SERVER}" "sudo usermod -a -G git www-data"
    
    # Enable and start the service
    ssh "${SSH_USER}@${GIT_SERVER}" "sudo systemctl enable git-repo-service && sudo systemctl restart git-repo-service"
    
    Write-Host "Service installed successfully." -ForegroundColor Green
    return $true
}

function Start-GitService {
    Write-Host ""
    Write-Host "Checking service status..."
    ssh "${SSH_USER}@${GIT_SERVER}" "systemctl is-active --quiet git-repo-service && echo 'Service is running' || echo 'Service is not running'"
    
    $startService = Read-Host "Start/restart service? (y/n)"
    if ($startService -eq "y") {
        Write-Host "Starting service on Git server..."
        ssh "${SSH_USER}@${GIT_SERVER}" "sudo systemctl enable git-repo-service && sudo systemctl restart git-repo-service"
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Failed to start service." -ForegroundColor Red
            return $false
        }
        Write-Host "Service started successfully." -ForegroundColor Green
    } else {
        Write-Host "Skipping service start." -ForegroundColor Yellow
    }
    
    return $true
}

function Test-ServiceStatus {
    Write-Host "Checking service status..." -ForegroundColor Cyan
    $statusOutput = ssh "${SSH_USER}@${GIT_SERVER}" "sudo systemctl status git-repo-service"
    
    if ($statusOutput -match "Active: active \(running\)") {
        Write-Host "Service is running" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Service is not running" -ForegroundColor Yellow
        return $false
    }
}

function Invoke-VersionCheck {
    param (
        [string]$RemoteCommand
    )
    
    # This function is used to suppress the 'date' command warning
    # by wrapping the SSH command execution
    ssh "${SSH_USER}@${GIT_SERVER}" $RemoteCommand
}

function Get-CompilerVersions {
    Write-Host "Checking compiler versions on Git server..." -ForegroundColor Cyan
    
    # Create timestamp for log file
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $logFile = "versions-log-$timestamp.txt"
    $localLogDir = $LOCAL_LOG_DIR
    
    # Ensure local log directory exists
    if (-not (Test-Path $localLogDir)) {
        New-Item -Path $localLogDir -ItemType Directory -Force | Out-Null
    }
    
    # Run version check commands on server
    $output = ssh "${SSH_USER}@${GIT_SERVER}" @"
    echo "===== Compiler and Library Versions ====="
    echo "System: \$(uname -a)"
    echo "\nC++ Compiler:"
    g++ --version | head -n 1
    echo "\nCMake:"
    cmake --version | head -n 1
    echo "\nPkg-config:"
    pkg-config --version
    echo "\nLibgit2:"
    pkg-config --modversion libgit2
    echo "\nOpenSSL:"
    openssl version
    echo "\nBoost:"
    dpkg -l | grep libboost | head -n 1
    echo "\nJsonCpp:"
    dpkg -l | grep libjsoncpp | head -n 1
    echo "\nCURL:"
    curl --version | head -n 1
    echo "\nGit:"
    git --version
    echo "\n===== JsonCpp Header Location ====="
    find /usr/include -name json.h | grep -i json
    echo "\n===== End of Version Check ====="
"@
    
    # Save output to log file
    $output | Out-File -FilePath "$localLogDir\$logFile" -Encoding utf8
    
    # Display output
    Write-Host "\nVersion information:" -ForegroundColor Cyan
    $output | ForEach-Object { Write-Host $_ }
    Write-Host "\nVersion information saved to: $localLogDir\$logFile" -ForegroundColor Green
    
    return $true
}

function Start-FullDeployment {
    Write-Host ""
    Write-Host "Starting full deployment process..." -ForegroundColor Cyan
    
    if (-not (Test-SSHConnection)) {
        Write-Host "SSH connection failed. Aborting deployment." -ForegroundColor Red
        return $false
    }
    
    if (-not (Install-Dependencies)) {
        Write-Host "Failed to install dependencies. Aborting deployment." -ForegroundColor Red
        return $false
    }
    
    if (-not (Deploy-SourceCode)) {
        Write-Host "Failed to deploy source code. Aborting deployment." -ForegroundColor Red
        return $false
    }
    
    if (-not (Build-Application)) {
        Write-Host "Failed to build application. Aborting deployment." -ForegroundColor Red
        return $false
    }
    
    Write-Host "Installing systemd service file..." -ForegroundColor Cyan
    scp "$PSScriptRoot\systemd\git-repo-service.service" "${SSH_USER}@${GIT_SERVER}:/tmp/git-repo-service.service"
    ssh "${SSH_USER}@${GIT_SERVER}" "sudo mkdir -p $REMOTE_SYSTEMD_DIR && sudo cp /tmp/git-repo-service.service $REMOTE_SYSTEMD_DIR/ && sudo mv /tmp/git-repo-service.service /etc/systemd/system/ && sudo chown root:root /etc/systemd/system/git-repo-service.service && sudo chmod 644 /etc/systemd/system/git-repo-service.service && sudo systemctl daemon-reload"
    Write-Host "Systemd service file installed." -ForegroundColor Green
    
    if (-not (Install-Service)) {
        Write-Host "Failed to install service. Aborting deployment." -ForegroundColor Red
        return $false
    }
    
    if (-not (Start-GitService)) {
        Write-Host "Failed to start service. Aborting deployment." -ForegroundColor Red
        return $false
    }
    
    if (-not (Test-ServiceStatus)) {
        Write-Host "Service is not running. Deployment may have issues." -ForegroundColor Yellow
    } else {
        Write-Host "Full deployment completed successfully!" -ForegroundColor Green
    }
    
    return $true
}

# Main script execution
do {
    Clear-Host
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host "Git Repository Service Deployment Tool" -ForegroundColor Cyan
    Write-Host "=============================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Choose an option:" -ForegroundColor Yellow
    Write-Host "1. Check SSH Connection" -ForegroundColor White
    Write-Host "2. Install Dependencies on Git Server" -ForegroundColor White
    Write-Host "3. Deploy Source Code" -ForegroundColor White
    Write-Host "4. Build Application" -ForegroundColor White
    Write-Host "5. Install Service" -ForegroundColor White
    Write-Host "6. Start Service" -ForegroundColor White
    Write-Host "7. Check Service Status" -ForegroundColor White
    Write-Host "8. Full Deployment (Steps 1-7)" -ForegroundColor White
    Write-Host "9. Check Installed Versions" -ForegroundColor White
    Write-Host "10. Exit" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-10)"
    
    switch ($choice) {
        "1" { Test-SSHConnection; pause }
        "2" { Install-Dependencies; pause }
        "3" { Deploy-SourceCode; pause }
        "4" { Build-Application; pause }
        "5" { Install-Service; pause }
        "6" { Start-GitService; pause }
        "7" { Test-ServiceStatus; pause }
        "8" { Start-FullDeployment; pause }
        "9" { Get-CompilerVersions; pause }
        "10" { Write-Host "Exiting deployment tool..." -ForegroundColor Cyan; exit }
        default { Write-Host "Invalid choice. Please try again." -ForegroundColor Red; pause }
    }
} while ($true)

function pause {
    Write-Host "Press any key to continue..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
