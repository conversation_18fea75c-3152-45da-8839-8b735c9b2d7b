# Git Dashboard Backup and Restore Guide

## Overview

This document provides comprehensive instructions for backing up and restoring the Git Dashboard system. Regular backups are essential for disaster recovery, system migration, and configuration management.

## Backup Strategy

### What to Back Up

1. **Application Files**
   - Python application code in `/opt/git-dashboard/`
   - Static web assets and frontend code
   - Custom scripts and configuration files

2. **Configuration Files**
   - Nginx configuration in `/etc/nginx/sites-available/git-dashboard.conf`
   - Systemd service file in `/etc/systemd/system/git-dashboard.service`
   - Environment variables and `.env` files

3. **Data**
   - Metrics data in `/opt/git-dashboard/data/`
   - Historical metrics in `/opt/git-dashboard/data/history/`

4. **SSL Certificates**
   - Let's Encrypt certificates in `/etc/letsencrypt/`

5. **Logs**
   - Application logs in `/opt/git-dashboard/logs/`
   - Nginx logs in `/var/log/nginx/git-dashboard.*`

## Backup Procedures

### Creating a Full System Backup Script

Create a comprehensive backup script:

```bash
sudo nano /usr/local/bin/backup-git-dashboard.sh
```

Add the following content:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/opt/backups/git-dashboard"
TIMESTAMP=$(date +%Y%m%d-%H%M)
LOG_FILE="$BACKUP_DIR/backup-$TIMESTAMP.log"

# Create backup directory
mkdir -p "$BACKUP_DIR"
mkdir -p "$BACKUP_DIR/logs"

# Start logging
exec > >(tee -a "$LOG_FILE") 2>&1

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting Git Dashboard backup"

# Function to backup with error handling
backup_component() {
    local name=$1
    local source=$2
    local destination="$BACKUP_DIR/$TIMESTAMP-$name.tar.gz"
    
    echo "Backing up $name from $source to $destination"
    
    if [ ! -e "$source" ]; then
        echo "WARNING: Source path $source does not exist. Skipping."
        return 1
    fi
    
    tar czf "$destination" -C "$(dirname "$source")" "$(basename "$source")" 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to backup $name"
        return 1
    else
        echo "Successfully backed up $name ($(du -h "$destination" | cut -f1))"
        return 0
    fi
}

# Backup application files
backup_component "app" "/opt/git-dashboard"

# Backup Nginx configuration
backup_component "nginx-config" "/etc/nginx/sites-available/git-dashboard.conf"

# Backup systemd service file
backup_component "systemd-service" "/etc/systemd/system/git-dashboard.service"

# Backup SSL certificates
backup_component "letsencrypt" "/etc/letsencrypt"

# Backup only recent logs (last 7 days)
find /opt/git-dashboard/logs -name "*.log*" -mtime -7 -exec tar czf "$BACKUP_DIR/$TIMESTAMP-recent-logs.tar.gz" {} \;

# Backup nginx logs
find /var/log/nginx -name "git-dashboard.*.log*" -mtime -7 -exec tar czf "$BACKUP_DIR/$TIMESTAMP-nginx-logs.tar.gz" {} \;

# Create a manifest file
echo "Git Dashboard Backup - $TIMESTAMP" > "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
echo "=========================================" >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
echo "System Information:" >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
echo "Ubuntu Version: $(lsb_release -d | cut -f2)" >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
echo "Python Version: $(python3 --version)" >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
echo "Nginx Version: $(nginx -v 2>&1 | cut -d'/' -f2)" >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
echo "\nBackup Contents:" >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"
ls -lh "$BACKUP_DIR/$TIMESTAMP"* | awk '{print $5 "\t" $9}' >> "$BACKUP_DIR/$TIMESTAMP-manifest.txt"

# Clean up old backups (keep last 10)
ls -tr "$BACKUP_DIR"/*-app.tar.gz | head -n -10 | xargs -r rm
find "$BACKUP_DIR" -name "*-manifest.txt" -mtime +30 -delete

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup completed"
```

Make the script executable:

```bash
sudo chmod +x /usr/local/bin/backup-git-dashboard.sh
```

### Setting Up Automated Backups

```bash
sudo crontab -e
```

Add a weekly backup schedule (runs Sundays at 2:00 AM):

```cron
0 2 * * 0 /usr/local/bin/backup-git-dashboard.sh
```

### Database Backup (If Added in Future)

If you add a database to your Git Dashboard in the future, include this in the backup script:

```bash
# Backup PostgreSQL database
if command -v psql > /dev/null; then
    echo "Backing up PostgreSQL database"
    pg_dump -U git_dashboard -d git_dashboard_db | gzip > "$BACKUP_DIR/$TIMESTAMP-database.sql.gz"
fi

# Backup MySQL/MariaDB database
if command -v mysqldump > /dev/null; then
    echo "Backing up MySQL database"
    mysqldump -u git_dashboard --password=YOUR_PASSWORD git_dashboard_db | gzip > "$BACKUP_DIR/$TIMESTAMP-database.sql.gz"
fi
```

## Restore Procedures

### Full System Restore

Create a restore script:

```bash
sudo nano /usr/local/bin/restore-git-dashboard.sh
```

Add the following content:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/opt/backups/git-dashboard"
LOG_FILE="$BACKUP_DIR/restore.log"

# Start logging
exec > >(tee -a "$LOG_FILE") 2>&1

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting Git Dashboard restore"

# Check if backup exists
if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup-timestamp>"
    echo "Available backups:"
    ls -1 "$BACKUP_DIR" | grep -o "[0-9]\{8\}-[0-9]\{4\}" | sort -u
    exit 1
fi

TIMESTAMP=$1

# Function to restore with error handling
restore_component() {
    local name=$1
    local destination=$2
    local backup_file="$BACKUP_DIR/$TIMESTAMP-$name.tar.gz"
    
    echo "Restoring $name from $backup_file to $destination"
    
    if [ ! -f "$backup_file" ]; then
        echo "ERROR: Backup file $backup_file does not exist"
        return 1
    fi
    
    # Create destination directory if it doesn't exist
    sudo mkdir -p "$(dirname "$destination")"
    
    # Extract the backup
    sudo tar xzf "$backup_file" -C "$(dirname "$destination")"
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to restore $name"
        return 1
    else
        echo "Successfully restored $name"
        return 0
    fi
}

# Stop services before restore
echo "Stopping services..."
sudo systemctl stop git-dashboard nginx

# Restore application files
restore_component "app" "/opt/git-dashboard"

# Restore Nginx configuration
restore_component "nginx-config" "/etc/nginx/sites-available/git-dashboard.conf"
sudo ln -sf /etc/nginx/sites-available/git-dashboard.conf /etc/nginx/sites-enabled/

# Restore systemd service file
restore_component "systemd-service" "/etc/systemd/system/git-dashboard.service"

# Restore SSL certificates
restore_component "letsencrypt" "/etc/letsencrypt"

# Set correct permissions
echo "Setting correct permissions..."
sudo chown -R root:root /opt/git-dashboard
sudo chmod -R 755 /opt/git-dashboard
sudo find /opt/git-dashboard -type f -exec chmod 644 {} \;
sudo chmod 755 /opt/git-dashboard/app.py /opt/git-dashboard/collect-metrics.sh

# Verify and restart services
echo "Reloading systemd and restarting services..."
sudo systemctl daemon-reload
sudo nginx -t

if [ $? -eq 0 ]; then
    sudo systemctl start nginx
    sudo systemctl start git-dashboard
    echo "Services restarted successfully"
    
    # Verify services are running
    sleep 5
    sudo systemctl status nginx git-dashboard --no-pager
else
    echo "ERROR: Nginx configuration test failed"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Restore completed"
```

Make the script executable:

```bash
sudo chmod +x /usr/local/bin/restore-git-dashboard.sh
```

### Using the Restore Script

```bash
# List available backups
sudo /usr/local/bin/restore-git-dashboard.sh

# Restore a specific backup
sudo /usr/local/bin/restore-git-dashboard.sh 20250312-1530
```

## Off-Site Backup Storage

### Copying Backups to Remote Server

Create a script to transfer backups to a remote server:

```bash
sudo nano /usr/local/bin/transfer-backups.sh
```

Add the following content:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/opt/backups/git-dashboard"
REMOTE_SERVER="backup-server.example.com"
REMOTE_USER="backup-user"
REMOTE_DIR="/path/to/backups/git-dashboard"
LOG_FILE="$BACKUP_DIR/transfer.log"

# Start logging
exec > >(tee -a "$LOG_FILE") 2>&1

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting backup transfer"

# Find the latest backup
LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/*-app.tar.gz | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "ERROR: No backups found in $BACKUP_DIR"
    exit 1
fi

BACKUP_TIMESTAMP=$(basename "$LATEST_BACKUP" | cut -d'-' -f1-2)
echo "Transferring backup set: $BACKUP_TIMESTAMP"

# Create directory on remote server
ssh "$REMOTE_USER@$REMOTE_SERVER" "mkdir -p $REMOTE_DIR"

# Transfer all files from the latest backup set
find "$BACKUP_DIR" -name "$BACKUP_TIMESTAMP-*" -exec scp {} "$REMOTE_USER@$REMOTE_SERVER:$REMOTE_DIR/" \;

if [ $? -eq 0 ]; then
    echo "Successfully transferred backup to remote server"
else
    echo "ERROR: Failed to transfer backup to remote server"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup transfer completed"
```

Make the script executable and schedule it after backups:

```bash
sudo chmod +x /usr/local/bin/transfer-backups.sh
```

Add to crontab to run 30 minutes after the backup:

```cron
30 2 * * 0 /usr/local/bin/transfer-backups.sh
```

## Testing Backups

### Backup Verification Script

Create a script to verify backup integrity:

```bash
sudo nano /usr/local/bin/verify-backups.sh
```

Add the following content:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/opt/backups/git-dashboard"
LOG_FILE="$BACKUP_DIR/verification.log"
TEST_DIR="/tmp/git-dashboard-backup-test"

# Start logging
exec > >(tee -a "$LOG_FILE") 2>&1

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting backup verification"

# Find the latest backup
LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/*-app.tar.gz | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "ERROR: No backups found in $BACKUP_DIR"
    exit 1
fi

BACKUP_TIMESTAMP=$(basename "$LATEST_BACKUP" | cut -d'-' -f1-2)
echo "Verifying backup set: $BACKUP_TIMESTAMP"

# Create test directory
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR"

# Check each backup file
find "$BACKUP_DIR" -name "$BACKUP_TIMESTAMP-*.tar.gz" | while read backup_file; do
    name=$(basename "$backup_file" | cut -d'-' -f3- | sed 's/.tar.gz//')
    echo "Testing $name backup"
    
    # Test extraction
    tar tzf "$backup_file" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "$name backup is valid"
    else
        echo "ERROR: $name backup is corrupt"
    fi
    
    # Extract a sample file for further testing
    mkdir -p "$TEST_DIR/$name"
    tar xzf "$backup_file" -C "$TEST_DIR/$name" --strip-components=1 "$(tar tzf "$backup_file" | head -n1)" 2>/dev/null
done

# Clean up
rm -rf "$TEST_DIR"

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup verification completed"
```

Make the script executable and schedule regular testing:

```bash
sudo chmod +x /usr/local/bin/verify-backups.sh
```

Add to crontab to run weekly after backups are complete:

```cron
0 3 * * 0 /usr/local/bin/verify-backups.sh
```

## Appendix: Disaster Recovery

### Complete System Rebuild Procedure

In case of catastrophic failure, follow these steps to restore the Git Dashboard on a new server:

1. **Install Base System**
   ```bash
   sudo apt update && sudo apt upgrade -y
   sudo apt install -y python3.10 python3.10-venv python3-pip nginx git supervisor ufw
   ```

2. **Restore Application**
   ```bash
   # Restore latest app backup from backup server
   scp <EMAIL>:/path/to/backups/git-dashboard/latest-*-app.tar.gz /tmp/
   
   # Extract to proper location
   sudo tar xzf /tmp/latest-*-app.tar.gz -C /
   ```

3. **Restore Configuration**
   ```bash
   # Restore Nginx and systemd config
   scp <EMAIL>:/path/to/backups/git-dashboard/latest-*-nginx-config.tar.gz /tmp/
   scp <EMAIL>:/path/to/backups/git-dashboard/latest-*-systemd-service.tar.gz /tmp/
   
   sudo tar xzf /tmp/latest-*-nginx-config.tar.gz -C /
   sudo tar xzf /tmp/latest-*-systemd-service.tar.gz -C /
   
   # Create symbolic link
   sudo ln -sf /etc/nginx/sites-available/git-dashboard.conf /etc/nginx/sites-enabled/
   ```

4. **Restore SSL Certificates**
   ```bash
   scp <EMAIL>:/path/to/backups/git-dashboard/latest-*-letsencrypt.tar.gz /tmp/
   sudo tar xzf /tmp/latest-*-letsencrypt.tar.gz -C /
   ```

5. **Verify and Start Services**
   ```bash
   sudo systemctl daemon-reload
   sudo nginx -t
   sudo systemctl start nginx
   sudo systemctl start git-dashboard
   ```
