# Project Tracker Nginx Configuration
# Version: 1.0.0
# Last Updated: 2025-03-05

# Main server configuration
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name project-tracker.chcit.org;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/project-tracker.chcit.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/project-tracker.chcit.org/privkey.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/letsencrypt/live/project-tracker.chcit.org/chain.pem;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self' wss://project-tracker.chcit.org";

    # Logging
    access_log /var/log/nginx/project-tracker.access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/project-tracker.error.log warn;

    # Root Directory
    root /opt/project-tracker/static;
    index index.html;

    # Frontend Routes
    location / {
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, no-transform";
    }

    # API Routes
    location /api {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket Support
    location /ws {
        proxy_pass http://127.0.0.1:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket Timeouts
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }

    # Static Files
    location /static/ {
        alias /opt/project-tracker/static/;
        expires 1d;
        add_header Cache-Control "public, no-transform";
        access_log off;

        # Brotli Compression
        brotli on;
        brotli_comp_level 6;
        brotli_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    }

    # Media Files
    location /media/ {
        alias /opt/project-tracker/media/;
        expires 1d;
        add_header Cache-Control "public, no-transform";
        access_log off;

        # Image Optimization
        image_filter_jpeg_quality 85;
        image_filter_webp_quality 80;
        image_filter_buffer 10M;
    }

    # Error Pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /opt/project-tracker/static;
    }
}
