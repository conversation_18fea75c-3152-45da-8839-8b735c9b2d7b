# 5.4 Activity Feed

*Component Documentation*  
*Last Updated: March 9, 2025*  
*Implementation Status: Completed* ✅

## Table of Contents
1. [Overview](#overview)
2. [Implementation Details](#implementation-details)
3. [Component Status](#component-status)
4. [Architecture](#architecture)
5. [Integration Points](#integration-points)
6. [Performance Considerations](#performance-considerations)
7. [Security Aspects](#security-aspects)
8. [Future Enhancements](#future-enhancements)

## Overview

The Activity Feed component provides real-time tracking and display of all project-related activities, ensuring users stay informed about changes and updates across the Project Tracker application.

### Purpose and Objectives

- **Activity Tracking**: Log all project-related activities
- **Real-time Updates**: Deliver instant activity notifications
- **User Engagement**: Keep users informed of relevant changes
- **Change History**: Maintain comprehensive activity history
- **Filtered Views**: Provide context-specific activity views

### Key Features

- **Activity Logging**: Comprehensive activity tracking
- **Real-time Updates**: Instant activity notifications
- **Filtered Feeds**: Context-specific activity views
- **User Notifications**: Personalized activity alerts
- **Activity Search**: Historical activity lookup
- **Feed Aggregation**: Grouped activity display
- **Read Status**: Activity read/unread tracking
- **Activity Categories**: Organized activity types

### Relation to Project Tracker

The Activity Feed component is essential for maintaining transparency and providing real-time updates about project changes, improvement status updates, and system notifications across the Project Tracker application.

## Implementation Details

### Technology Stack

- **Database**: PostgreSQL for activity storage
- **Cache**: Redis for real-time feed delivery
- **WebSocket**: Real-time activity updates
- **Queue**: Redis pub/sub for event handling
- **Search**: PostgreSQL full-text search

### Implementation Example

```python
# activity_service.py
from datetime import datetime
from typing import List, Optional, Dict
import json
from enum import Enum

class ActivityType(Enum):
    PROJECT_CREATE = "project_create"
    PROJECT_UPDATE = "project_update"
    IMPROVEMENT_CREATE = "improvement_create"
    IMPROVEMENT_UPDATE = "improvement_update"
    GIT_OPERATION = "git_operation"
    SYSTEM_EVENT = "system_event"

class ActivityService:
    def __init__(self, db_pool, redis_client, websocket_manager):
        self.db = db_pool
        self.redis = redis_client
        self.ws = websocket_manager
        
    async def log_activity(self, 
                          activity_type: ActivityType,
                          entity_type: str,
                          entity_id: int,
                          user_id: int,
                          details: dict) -> int:
        """Log a new activity and notify interested users"""
        try:
            # Create activity record
            async with self.db.transaction():
                activity_id = await self.db.fetchval("""
                    INSERT INTO activity_logs 
                    (entity_type, entity_id, user_id, activity_type, details, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING id
                """, entity_type, entity_id, user_id, 
                    activity_type.value, json.dumps(details),
                    datetime.utcnow())
                
                # Add to Redis activity feed
                await self._add_to_feeds(activity_id, activity_type, 
                                       entity_type, entity_id, details)
                
                # Notify interested users
                await self._notify_users(activity_type, entity_type, 
                                      entity_id, details)
                
                return activity_id
        except Exception as e:
            self.logger.error(f"Activity log error: {str(e)}")
            raise
    
    async def _add_to_feeds(self, 
                           activity_id: int,
                           activity_type: ActivityType,
                           entity_type: str,
                           entity_id: int,
                           details: dict):
        """Add activity to relevant Redis feeds"""
        activity_data = {
            'id': activity_id,
            'type': activity_type.value,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'details': details,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Add to global feed
        await self.redis.lpush('activity:feed:global', 
                             json.dumps(activity_data))
        await self.redis.ltrim('activity:feed:global', 0, 999)
        
        # Add to entity feed
        entity_key = f'activity:feed:{entity_type}:{entity_id}'
        await self.redis.lpush(entity_key, json.dumps(activity_data))
        await self.redis.ltrim(entity_key, 0, 99)
        
        # Add to interested users' feeds
        interested_users = await self._get_interested_users(
            entity_type, entity_id)
        for user_id in interested_users:
            user_key = f'activity:feed:user:{user_id}'
            await self.redis.lpush(user_key, json.dumps(activity_data))
            await self.redis.ltrim(user_key, 0, 99)
    
    async def _notify_users(self,
                           activity_type: ActivityType,
                           entity_type: str,
                           entity_id: int,
                           details: dict):
        """Notify interested users via WebSocket"""
        interested_users = await self._get_interested_users(
            entity_type, entity_id)
        
        notification = {
            'type': 'activity_update',
            'activity_type': activity_type.value,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'details': details,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self.ws.broadcast_to_users(interested_users, notification)
    
    async def get_user_feed(self, 
                           user_id: int,
                           page: int = 0,
                           page_size: int = 20) -> List[Dict]:
        """Get paginated user activity feed"""
        # Try Redis cache first
        cache_key = f'activity:feed:user:{user_id}'
        start = page * page_size
        end = start + page_size - 1
        
        try:
            cached = await self.redis.lrange(cache_key, start, end)
            if cached:
                return [json.loads(item) for item in cached]
        except Exception:
            pass
        
        # Fallback to database
        activities = await self.db.fetch("""
            SELECT al.*, u.username
            FROM activity_logs al
            JOIN users u ON al.user_id = u.id
            WHERE al.entity_type IN (
                SELECT entity_type FROM user_subscriptions
                WHERE user_id = $1
            )
            ORDER BY al.created_at DESC
            LIMIT $2 OFFSET $3
        """, user_id, page_size, start)
        
        return [dict(a) for a in activities]
    
    async def get_entity_feed(self,
                             entity_type: str,
                             entity_id: int,
                             page: int = 0,
                             page_size: int = 20) -> List[Dict]:
        """Get paginated entity activity feed"""
        cache_key = f'activity:feed:{entity_type}:{entity_id}'
        start = page * page_size
        end = start + page_size - 1
        
        try:
            cached = await self.redis.lrange(cache_key, start, end)
            if cached:
                return [json.loads(item) for item in cached]
        except Exception:
            pass
        
        activities = await self.db.fetch("""
            SELECT al.*, u.username
            FROM activity_logs al
            JOIN users u ON al.user_id = u.id
            WHERE al.entity_type = $1 AND al.entity_id = $2
            ORDER BY al.created_at DESC
            LIMIT $3 OFFSET $4
        """, entity_type, entity_id, page_size, start)
        
        return [dict(a) for a in activities]
```

### Frontend Integration

```typescript
// activity-feed.component.ts
interface Activity {
    id: number;
    type: string;
    entity_type: string;
    entity_id: number;
    details: any;
    timestamp: string;
    username: string;
}

@Component({
    selector: 'app-activity-feed',
    template: `
        <div class="activity-feed">
            <div *ngFor="let activity of activities"
                 class="activity-item"
                 [class.unread]="!isRead(activity.id)">
                <div class="activity-header">
                    <span class="username">{{activity.username}}</span>
                    <span class="timestamp">{{activity.timestamp | timeAgo}}</span>
                </div>
                <div class="activity-content">
                    {{formatActivity(activity)}}
                </div>
            </div>
            <div *ngIf="loading" class="loading-spinner">
                Loading...
            </div>
            <button *ngIf="hasMore" 
                    (click)="loadMore()"
                    class="load-more">
                Load More
            </button>
        </div>
    `
})
export class ActivityFeedComponent implements OnInit {
    activities: Activity[] = [];
    page = 0;
    pageSize = 20;
    loading = false;
    hasMore = true;
    
    constructor(
        private activityService: ActivityService,
        private websocketService: WebSocketService
    ) {}
    
    ngOnInit() {
        this.loadActivities();
        this.subscribeToUpdates();
    }
    
    async loadActivities() {
        this.loading = true;
        try {
            const newActivities = await this.activityService
                .getUserFeed(this.page, this.pageSize);
            this.activities.push(...newActivities);
            this.hasMore = newActivities.length === this.pageSize;
            this.page++;
        } finally {
            this.loading = false;
        }
    }
    
    subscribeToUpdates() {
        this.websocketService.on('activity_update', (activity) => {
            this.activities.unshift(activity);
            if (this.activities.length > 100) {
                this.activities.pop();
            }
        });
    }
    
    formatActivity(activity: Activity): string {
        switch (activity.type) {
            case 'project_create':
                return `Created project "${activity.details.name}"`;
            case 'project_update':
                return `Updated project "${activity.details.name}"`;
            case 'improvement_create':
                return `Created improvement "${activity.details.title}"`;
            case 'improvement_update':
                return `Updated improvement status to "${activity.details.status}"`;
            default:
                return `Performed ${activity.type}`;
        }
    }
    
    isRead(activityId: number): boolean {
        return this.activityService.isActivityRead(activityId);
    }
    
    loadMore() {
        this.loadActivities();
    }
}
```

## Component Status

| Status | Feature | Description | Implementation Details |
|--------|---------|-------------|------------------------|
| ✅ Done | Activity Logging | Comprehensive tracking | Database and Redis integration |
| ✅ Done | Real-time Updates | Instant notifications | WebSocket delivery |
| ✅ Done | Filtered Feeds | Context-specific views | User and entity feeds |
| ✅ Done | User Notifications | Personalized alerts | Real-time delivery |
| ✅ Done | Activity Search | Historical lookup | Full-text search |
| ✅ Done | Feed Aggregation | Grouped display | Smart aggregation |

## Architecture

The Activity Feed architecture follows an event-driven pattern:

```
Activity Event
    ↓
Activity Service
    ↓
Database Storage
    ↓
Redis Cache
    ↓
WebSocket Notification
    ↓
Client Update
```

## Integration Points

- **Database Layer**: Activity storage
- **Redis Cache**: Real-time feed delivery
- **WebSocket**: Live notifications
- **Frontend**: Activity display
- **User System**: Activity targeting

## Performance Considerations

- **Caching Strategy**: Redis-based feed caching
- **Pagination**: Efficient feed loading
- **Feed Pruning**: Automatic old activity cleanup
- **Query Optimization**: Efficient activity retrieval
- **Memory Usage**: Smart cache management

## Security Aspects

- **Access Control**: Activity visibility rules
- **Data Validation**: Activity input validation
- **Rate Limiting**: Activity creation limits
- **Data Privacy**: Sensitive data filtering
- **Audit Trail**: Activity tracking

## Future Enhancements

- **Smart Aggregation**: Intelligent activity grouping
- **Custom Filters**: User-defined feed filters
- **Advanced Search**: Enhanced activity search
- **Export**: Activity data export
- **Analytics**: Activity pattern analysis
