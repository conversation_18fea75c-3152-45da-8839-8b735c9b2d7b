# SSH Key Management Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force

# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Main SSH Keys Management function
function Set-SSHKeys {
    Clear-Host

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               SSH Key Management                     " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Display current SSH key configuration if available
    $sshKeyPath = $null

    # Check for different possible key path property names
    if ($null -ne $Config -and $null -ne $Config.ssh) {
        if ($Config.ssh.PSObject.Properties.Name -contains "local_key_path") {
            $sshKeyPath = $Config.ssh.local_key_path
        } elseif ($Config.ssh.PSObject.Properties.Name -contains "key_path") {
            $sshKeyPath = $Config.ssh.key_path
        }
    }

    if (-not [string]::IsNullOrWhiteSpace($sshKeyPath)) {
        Write-Log -Message "Current SSH Key: $sshKeyPath" -Level "UI" -ForegroundColor White

        # Check if the key file exists
        if (Test-Path $sshKeyPath) {
            Write-Log -Message "Status: Key file exists" -Level "UI" -ForegroundColor Green
        } else {
            Write-Log -Message "Status: Key file does not exist" -Level "UI" -ForegroundColor Red
        }
    } else {
        Write-Log -Message "No SSH key configured." -Level "UI" -ForegroundColor Yellow
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "[1] Set SSH Key Path" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Generate New SSH Key" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Test SSH Connection" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Deploy Public Key to Server" -Level "UI" -ForegroundColor White
    Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
    Write-Log -Message "[0] Back to Main Menu" -Level "UI" -ForegroundColor White

    # Disable UI Mode before reading input
    Disable-UIMode

    $choice = Read-Host "`nSelect an option (0-4)"

    switch ($choice) {
        "1" { Set-SSHKeyPath }
        "2" { New-SSHKey }
        "3" { & "$PSScriptRoot\Test-SSHConnection.ps1" }
        "4" { Deploy-SSHPublicKey }
        "0" { Show-MainMenu }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Warning" -Component "SSH"
            Start-Sleep -Seconds 1
            Set-SSHKeys
        }
    }
}

# Set the SSH key path
function Set-SSHKeyPath {
    Clear-Host

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Set SSH Key Path                       " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after menu display
    Disable-UIMode

    # Try to find an existing SSH key
    $foundKey = Find-SSHKey

    if ($null -ne $foundKey) {
        Write-Log -Message "Found existing SSH key: $foundKey" -Level "Success" -Component "SSH"
        $useExisting = Read-Host "Use this key? (y/n)"

        if ($useExisting -eq "y") {
            if ($null -eq $Config) {
                $script:Config = @{}
            }

            if ($null -eq $Config.ssh) {
                $Config.ssh = @{}
            }

            # Use local_key_path for consistency with existing configuration
            $Config.ssh.local_key_path = $foundKey
            Save-Configuration

            Write-Log -Message "SSH key path set to: $foundKey" -Level "Success" -Component "SSH"
            Wait-ForUser
            Set-SSHKeys
            return
        }
    }

    Write-Log -Message "Please enter the path to your SSH private key file." -Level "Info" -Component "SSH"
    $keyPath = Read-Host "SSH key path"

    if (-not (Test-Path $keyPath)) {
        Write-Log -Message "The specified file does not exist." -Level "Error" -Component "SSH"
        $createNew = Read-Host "Would you like to generate a new SSH key? (y/n)"

        if ($createNew -eq "y") {
            New-SSHKey
            return
        } else {
            Write-Log -Message "Operation cancelled." -Level "Warning" -Component "SSH"
            Wait-ForUser
            Set-SSHKeys
            return
        }
    }

    # Update configuration
    if ($null -eq $Config) {
        $script:Config = @{}
    }

    if ($null -eq $Config.ssh) {
        $Config.ssh = @{}
    }

    $Config.ssh.key_path = $keyPath
    Save-Configuration

    Write-Log -Message "SSH key path set to: $keyPath" -Level "Success" -Component "SSH"
    Wait-ForUser
    Set-SSHKeys
}

# Function to generate SSH key
function New-SSHKey {
    param (
        [Parameter(Mandatory=$true)]
        [string]$KeyPath,
        [Parameter(Mandatory=$false)]
        [string]$Comment = "",
        [Parameter(Mandatory=$false)]
        [switch]$NoPassphrase
    )
    Clear-Host

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Generate New SSH Key                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after menu display
    Disable-UIMode

    # Check if ssh-keygen is available
    try {
        $null = & ssh-keygen -V
    } catch {
        Write-Log -Message "ssh-keygen is not available. Please install OpenSSH." -Level "Error" -Component "SSH"
        Wait-ForUser
        Set-SSHKeys
        return
    }

    # Enable UI Mode for key type selection
    Enable-UIMode

    Write-Log -Message "Select key type:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "[1] RSA (2048 bits)" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] RSA (4096 bits)" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Ed25519 (recommended)" -Level "UI" -ForegroundColor White

    # Disable UI Mode before reading input
    Disable-UIMode

    $keyType = Read-Host "Select key type (1-3)"

    $keyTypeStr = ""
    $keyBits = 0

    switch ($keyType) {
        "1" {
            $keyTypeStr = "rsa"
            $keyBits = 2048
        }
        "2" {
            $keyTypeStr = "rsa"
            $keyBits = 4096
        }
        "3" {
            $keyTypeStr = "ed25519"
        }
        default {
            Write-Log -Message "Invalid option. Using Ed25519 as default." -Level "Warning" -Component "SSH"
            $keyTypeStr = "ed25519"
        }
    }

    # Ask for key location if not provided
    if ([string]::IsNullOrWhiteSpace($KeyPath)) {
        Write-Log -Message "Enter the path where you want to save the key:" -Level "Info" -Component "SSH"
        Write-Log -Message "(Default: $HOME\.ssh\id_$keyTypeStr)" -Level "Info" -Component "SSH"
        $KeyPath = Read-Host "Key path"

        if ([string]::IsNullOrWhiteSpace($KeyPath)) {
            $KeyPath = "$HOME\.ssh\id_$keyTypeStr"
        }

        if (Test-Path $KeyPath) {
            Write-Log -Message "Warning: The file already exists and will be overwritten." -Level "Warning" -Component "SSH"
            $confirm = Read-Host "Continue? (y/n)"

            if ($confirm -ne "y") {
                Write-Log -Message "Operation cancelled." -Level "Warning" -Component "SSH"
                Wait-ForUser
                Set-SSHKeys
                return
            }
        }
    }

    # Generate key
    Write-Log -Message "Generating SSH key..." -Level "Info" -Component "SSH"

    try {
        $email = Read-Host "Enter your email (for key comment)"

        if ([string]::IsNullOrWhiteSpace($email)) {
            $email = "<EMAIL>"
        }

        $sshKeygenArgs = "-t $keyTypeStr"

        if ($keyTypeStr -eq "rsa") {
            $sshKeygenArgs += " -b $keyBits"
        }

        $sshKeygenArgs += " -f `"$KeyPath`" -C `"$email`""

        if ($NoPassphrase) {
            $sshKeygenArgs += " -N `"`""
        }

        # Ensure .ssh directory exists
        $sshDir = Split-Path -Path $KeyPath -Parent
        if (-not (Test-Path $sshDir)) {
            New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
        }

        # Run ssh-keygen
        $process = Start-Process -FilePath "ssh-keygen" -ArgumentList $sshKeygenArgs -NoNewWindow -Wait -PassThru

        if ($process.ExitCode -eq 0) {
            Write-Log -Message "SSH key generated successfully!" -Level "Success" -Component "SSH"
            Write-Log -Message "Private key: $KeyPath" -Level "Info" -Component "SSH"
            Write-Log -Message "Public key: $KeyPath.pub" -Level "Info" -Component "SSH"

            # Update configuration
            if ($null -eq $Config) {
                $script:Config = @{}
            }

            if ($null -eq $Config.ssh) {
                $Config.ssh = @{}
            }

            # Use local_key_path for consistency with existing configuration
            $Config.ssh.local_key_path = $KeyPath
            Save-Configuration

            $deployKey = Read-Host "Would you like to deploy this key to the server? (y/n)"

            if ($deployKey -eq "y") {
                Deploy-SSHPublicKey
                return
            }
        } else {
            Write-Log -Message "Failed to generate SSH key." -Level "Error" -Component "SSH"
        }
    } catch {
        Write-Log -Message "Error generating SSH key: $_" -Level "Error" -Component "SSH"
    }

    Wait-ForUser
    Set-SSHKeys
}

# Deploy the public key to the remote server
function Deploy-SSHPublicKey {
    Clear-Host

    # Enable UI Mode for menu display
    Enable-UIMode

    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Deploy SSH Public Key                  " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode after menu display
    Disable-UIMode

    # Get SSH key path from configuration
    $sshKeyPath = $null

    # Check for different possible key path property names
    if ($null -ne $Config -and $null -ne $Config.ssh) {
        if ($Config.ssh.PSObject.Properties.Name -contains "local_key_path") {
            $sshKeyPath = $Config.ssh.local_key_path
        } elseif ($Config.ssh.PSObject.Properties.Name -contains "key_path") {
            $sshKeyPath = $Config.ssh.key_path
        }
    }

    if ([string]::IsNullOrWhiteSpace($sshKeyPath)) {
        Write-Log -Message "SSH key path is not configured." -Level "Error" -Component "SSH"
        Write-Log -Message "Please set the SSH key path first." -Level "Warning" -Component "SSH"
        Wait-ForUser
        Set-SSHKeys
        return
    }

    if ($null -eq $Config.ssh.host -or $null -eq $Config.ssh.port -or $null -eq $Config.ssh.username) {
        Write-Log -Message "SSH connection details are not configured." -Level "Error" -Component "SSH"
        Write-Log -Message "Please configure SSH settings first." -Level "Warning" -Component "SSH"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }

    # $sshKeyPath is already set above
    $sshHost = $Config.ssh.host
    $sshPort = $Config.ssh.port
    $sshUser = $Config.ssh.username

    # Check if the public key file exists
    $pubKeyPath = "$sshKeyPath.pub"

    if (-not (Test-Path $pubKeyPath)) {
        Write-Log -Message "Public key file not found: $pubKeyPath" -Level "Error" -Component "SSH"
        Wait-ForUser
        Set-SSHKeys
        return
    }

    # Read the public key content
    $pubKey = Get-Content -Path $pubKeyPath -Raw

    # Enable UI Mode for connection details
    Enable-UIMode

    Write-Log -Message "Deploying public key to $sshHost..." -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Host: $sshHost" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $sshPort" -Level "UI" -ForegroundColor White
    Write-Log -Message "  User: $sshUser" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Public Key: $pubKeyPath" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    # Disable UI Mode before reading input
    Disable-UIMode

    Write-Log -Message "You'll need to enter your password for the remote server." -Level "Info" -Component "SSH"
    $password = Read-Host "Enter password" -AsSecureString
    $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

    if ([string]::IsNullOrWhiteSpace($plainPassword)) {
        Write-Log -Message "Password cannot be empty." -Level "Error" -Component "SSH"
        Wait-ForUser
        Set-SSHKeys
        return
    }

    Write-Log -Message "Deploying public key..." -Level "Info" -Component "SSH"

    try {
        # First, create the .ssh directory if it doesn't exist
        # Then append our key to authorized_keys
        # Finally, fix permissions
        $remoteCommands = @(
            "mkdir -p ~/.ssh",
            "chmod 700 ~/.ssh",
            "echo '$pubKey' >> ~/.ssh/authorized_keys",
            "chmod 600 ~/.ssh/authorized_keys"
        )

        $remoteScript = $remoteCommands -join "; "

        # Use sshpass to pass the password
        # Note: This requires sshpass to be installed or use another method
        # This is a simplified example; in production, consider using a more secure approach
        $process = New-Object System.Diagnostics.Process
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "ssh"
        $startInfo.Arguments = "-o StrictHostKeyChecking=accept-new -p $($Config.ssh.port) $($Config.ssh.username)@$($Config.ssh.host) '$remoteScript'"
        $startInfo.RedirectStandardInput = $true
        $startInfo.RedirectStandardOutput = $true
        $startInfo.RedirectStandardError = $true
        $startInfo.UseShellExecute = $false
        $process.StartInfo = $startInfo

        $process.Start() | Out-Null
        $process.StandardInput.WriteLine($plainPassword)
        $process.StandardInput.Close()

        $errorOutput = $process.StandardError.ReadToEnd()
        $process.WaitForExit()

        if ($process.ExitCode -eq 0) {
            Write-Log -Message "Public key successfully deployed!" -Level "Success" -Component "SSH"

            # Test the connection using the key
            Write-Log -Message "`nTesting SSH connection with the deployed key..." -Level "Info" -Component "SSH"
            & "$PSScriptRoot\Test-SSHConnection.ps1"
            return
        } else {
            Write-Log -Message "Failed to deploy public key. Exit code: $($process.ExitCode)" -Level "Error" -Component "SSH"
            if (-not [string]::IsNullOrWhiteSpace($errorOutput)) {
                Write-Log -Message "Error: $errorOutput" -Level "Error" -Component "SSH"
            }
        }
    } catch {
        Write-Log -Message "Error deploying public key: $_" -Level "Error" -Component "SSH"
    }

    Show-MainMenu
}

# Helper function to find SSH key
function Find-SSHKey {
    # Common locations for SSH keys
    $possiblePaths = @(
        (Join-Path -Path $HOME -ChildPath ".ssh\id_rsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_dsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_ecdsa"),
        (Join-Path -Path $HOME -ChildPath ".ssh\id_ed25519")
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path -Path $path) {
            return $path
        }
    }

    return $null
}

# Run the function
Set-SSHKeys
