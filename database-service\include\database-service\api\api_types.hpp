#pragma once
#include <string>
#include <unordered_map>
#include <nlohmann/json.hpp>

namespace dbservice::api {

/**
 * @enum ApiErrorType
 * @brief Enum for different API error types
 */
enum class ApiErrorType {
    Unknown,
    WinsockInit,
    SocketCreation,
    SocketOption,
    SocketBind,
    SocketListen,
    RequestParsing,
    ResponseCreation
};

/**
 * @struct ApiError
 * @brief Represents an API error with a type, message, and code
 */
struct ApiError {
    ApiErrorType type;
    std::string message;
    int code = 0; // Optional error code (e.g., errno)
};

/**
 * @struct Response
 * @brief Represents an API response
 */
struct Response {
    std::unordered_map<std::string, std::string> headers;
    std::string body;
};

/**
 * @struct ParsedRequest
 * @brief Represents a parsed HTTP request
 */
struct ParsedRequest {
    std::string method;
    std::string path;
    std::unordered_map<std::string, std::string> headers;
    std::string body;
};

} // namespace dbservice::api
