#include "logging_service.hpp"
#include "../api/api_server.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <regex>
#include <chrono>
#include <thread>
#include <filesystem>
#include <jsoncpp/json/json.h>

namespace fs = std::filesystem;

// Utility methods
std::string LoggingService::formatTimestamp(const std::chrono::system_clock::time_point& time) {
    // Convert to time_t
    auto timeT = std::chrono::system_clock::to_time_t(time);
    
    // Convert to tm struct
    std::tm tm = *std::localtime(&timeT);
    
    // Format as ISO 8601
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%dT%H:%M:%S");
    
    // Add milliseconds
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        time.time_since_epoch() % std::chrono::seconds(1)
    );
    oss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    // Add timezone offset
    oss << std::put_time(&tm, "%z");
    
    return oss.str();
}

std::chrono::system_clock::time_point LoggingService::parseTimestamp(const std::string& timestamp) {
    // Parse ISO 8601 timestamp
    std::tm tm = {};
    std::istringstream ss(timestamp);
    
    // Try different formats
    if (timestamp.find('T') != std::string::npos) {
        // ISO 8601 format: 2023-05-20T10:15:30.123Z
        ss >> std::get_time(&tm, "%Y-%m-%dT%H:%M:%S");
    } else if (timestamp.find('-') != std::string::npos) {
        // Date format: 2023-05-20 10:15:30
        ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
    } else {
        // Fallback format: 20/05/2023 10:15:30
        ss >> std::get_time(&tm, "%d/%m/%Y %H:%M:%S");
    }
    
    // Convert to time_point
    auto timeT = std::mktime(&tm);
    return std::chrono::system_clock::from_time_t(timeT);
}

std::string LoggingService::getLogTypeString(LogType type) {
    switch (type) {
        case LogType::INFO:
            return "info";
        case LogType::WARNING:
            return "warning";
        case LogType::ERROR:
            return "error";
        case LogType::SUCCESS:
            return "success";
        case LogType::DEBUG:
            return "debug";
        default:
            return "info";
    }
}

LogType LoggingService::parseLogType(const std::string& typeStr) {
    if (typeStr == "error") {
        return LogType::ERROR;
    } else if (typeStr == "warning") {
        return LogType::WARNING;
    } else if (typeStr == "success") {
        return LogType::SUCCESS;
    } else if (typeStr == "debug") {
        return LogType::DEBUG;
    } else {
        return LogType::INFO;
    }
}

// File operations
void LoggingService::saveLogsToFile() {
    try {
        std::lock_guard<std::mutex> lock(logsMutex_);
        
        // Create JSON object
        Json::Value root;
        root["logs"] = Json::Value(Json::arrayValue);
        
        // Add logs
        for (const auto& entry : logs_) {
            Json::Value logJson;
            logJson["timestamp"] = formatTimestamp(entry.timestamp);
            logJson["type"] = getLogTypeString(entry.type);
            logJson["message"] = entry.message;
            logJson["source"] = entry.source;
            
            // Add optional fields if present
            if (entry.component) {
                logJson["component"] = *entry.component;
            }
            
            if (entry.unit) {
                logJson["unit"] = *entry.unit;
            }
            
            if (entry.hostname) {
                logJson["hostname"] = *entry.hostname;
            }
            
            if (entry.commit) {
                logJson["commit"] = *entry.commit;
            }
            
            if (entry.author) {
                logJson["author"] = *entry.author;
            }
            
            // Add metadata
            for (const auto& [key, value] : entry.metadata) {
                logJson[key] = value;
            }
            
            root["logs"].append(logJson);
        }
        
        // Write to file
        std::ofstream file(logFile_);
        if (!file.is_open()) {
            std::cerr << "Failed to open log file for writing: " << logFile_ << std::endl;
            return;
        }
        
        // Write JSON
        Json::StreamWriterBuilder writer;
        file << Json::writeString(writer, root);
        
        std::cout << "Saved " << logs_.size() << " logs to file: " << logFile_ << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving logs to file: " << e.what() << std::endl;
    }
}

void LoggingService::loadLogsFromFile() {
    try {
        // Check if log file exists
        if (!fs::exists(logFile_)) {
            std::cout << "Log file does not exist: " << logFile_ << std::endl;
            return;
        }
        
        // Open log file
        std::ifstream file(logFile_);
        if (!file.is_open()) {
            std::cerr << "Failed to open log file for reading: " << logFile_ << std::endl;
            return;
        }
        
        // Parse JSON
        Json::Value root;
        Json::CharReaderBuilder reader;
        std::string errors;
        
        if (!Json::parseFromStream(reader, file, &root, &errors)) {
            std::cerr << "Failed to parse log file: " << errors << std::endl;
            return;
        }
        
        // Clear existing logs
        std::lock_guard<std::mutex> lock(logsMutex_);
        logs_.clear();
        
        // Load logs
        if (root.isMember("logs") && root["logs"].isArray()) {
            for (const auto& logJson : root["logs"]) {
                try {
                    // Parse timestamp
                    std::string timestampStr = logJson["timestamp"].asString();
                    auto timestamp = parseTimestamp(timestampStr);
                    
                    // Parse type
                    std::string typeStr = logJson["type"].asString();
                    auto type = parseLogType(typeStr);
                    
                    // Parse message and source
                    std::string message = logJson["message"].asString();
                    std::string source = logJson["source"].asString();
                    
                    // Create log entry
                    LogEntry entry(timestamp, type, message, source);
                    
                    // Add optional fields if present
                    if (logJson.isMember("component")) {
                        entry.component = logJson["component"].asString();
                    }
                    
                    if (logJson.isMember("unit")) {
                        entry.unit = logJson["unit"].asString();
                    }
                    
                    if (logJson.isMember("hostname")) {
                        entry.hostname = logJson["hostname"].asString();
                    }
                    
                    if (logJson.isMember("commit")) {
                        entry.commit = logJson["commit"].asString();
                    }
                    
                    if (logJson.isMember("author")) {
                        entry.author = logJson["author"].asString();
                    }
                    
                    // Add metadata
                    for (const auto& key : logJson.getMemberNames()) {
                        if (key != "timestamp" && key != "type" && key != "message" && key != "source" &&
                            key != "component" && key != "unit" && key != "hostname" && key != "commit" && key != "author") {
                            entry.metadata[key] = logJson[key].asString();
                        }
                    }
                    
                    // Add to logs
                    logs_.push_back(entry);
                    
                } catch (const std::exception& e) {
                    std::cerr << "Error parsing log entry: " << e.what() << std::endl;
                }
            }
        }
        
        std::cout << "Loaded " << logs_.size() << " logs from file: " << logFile_ << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading logs from file: " << e.what() << std::endl;
    }
}

void LoggingService::pruneOldLogs() {
    try {
        std::lock_guard<std::mutex> lock(logsMutex_);
        
        // Check if we need to prune
        if (logs_.size() <= maxLogSize_) {
            return;
        }
        
        // Calculate cutoff time
        auto now = std::chrono::system_clock::now();
        auto cutoff = now - std::chrono::hours(24 * logRetentionDays_);
        
        // Remove logs older than cutoff
        logs_.erase(
            std::remove_if(logs_.begin(), logs_.end(), [&](const LogEntry& entry) {
                return entry.timestamp < cutoff;
            }),
            logs_.end()
        );
        
        std::cout << "Pruned logs, new size: " << logs_.size() << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error pruning old logs: " << e.what() << std::endl;
    }
}
