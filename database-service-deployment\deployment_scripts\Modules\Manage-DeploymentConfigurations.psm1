# Manage Deployment Configurations Module

# Import the central Configuration module first
if (-not (Get-Module -Name "Configuration")) {
    Import-Module -Name "$PSScriptRoot\Configuration.psm1" -DisableNameChecking -ErrorAction SilentlyContinue
}

# Import common module
if (-not (Get-Module -Name "Common")) {
    Import-Module -Name "$PSScriptRoot\..\Common.psm1" -DisableNameChecking -ErrorAction SilentlyContinue
}

# Import Logger module
if (-not (Get-Module -Name "Logger")) {
    Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -DisableNameChecking -ErrorAction SilentlyContinue
}

function Show-DeploymentConfigurations {
    Clear-Host
    # Enable UI Mode for menu display
    if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
        Enable-UIMode
    }
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "            Manage Deployment Configurations            " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host " "

    # Get the current configuration using the central configuration module
    $config = Get-Configuration

    # If no configuration loaded, try to load it directly
    if ($null -eq $config) {
        # Determine environment - use existing or default to development
        $env = "development"
        if ($null -ne $env:DeploymentEnvironment) {
            $env = $env:DeploymentEnvironment
        }

        # Direct path to config file
        $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
        $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($env.ToLower()).json"

        Write-Host "Attempting to load configuration from $configPath..." -ForegroundColor Yellow

        if (Test-Path -Path $configPath) {
            Import-Configuration -ConfigFile $configPath | Out-Null
            # Get the configuration again after loading
            $config = Get-Configuration
        }
    }

    # If still no configuration loaded, inform the user and provide options
    if ($null -eq $config -or $null -eq $config.project) {
        Write-Host "No configuration loaded. Would you like to:" -ForegroundColor Yellow
        Write-Host "[1] Set Environment First" -ForegroundColor White
        Write-Host "[2] Create a Default Configuration" -ForegroundColor White
        Write-Host "[0] Back to Main Menu" -ForegroundColor White

        $configChoice = Read-Host "`nSelect an option (0-2)"

        switch ($configChoice) {
            "1" {
                if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
                    Disable-UIMode
                }
                if (Get-Command -Name Set-Environment -ErrorAction SilentlyContinue) {
                    Set-Environment
                    # After environment is set, reload this menu
                    Show-DeploymentConfigurations
                } else {
                    Write-Host "Set-Environment command not available." -ForegroundColor Red
                    if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
                        Wait-ForUser
                    } else {
                        Read-Host "Press Enter to continue..."
                    }
                }
                return
            }
            "2" {
                if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
                    Disable-UIMode
                }
                # Create default configuration
                $env = "development"
                if ($null -ne $env:DeploymentEnvironment) {
                    $env = $env:DeploymentEnvironment
                }

                try {
                    # Create new basic configuration
                    $newConfig = @{
                        project = @{
                            name = "database-service"
                            description = "database-service for $env environment"
                            local_source_dir = "D:\Augment\project-tracker\database-service"
                            remote_install_dir = "/opt/database-service"
                            remote_build_dir = "/home/<USER>/database-service-build"
                        }
                        ssh = @{
                            host = "git.chcit.org"
                            port = 22
                            username = "btaylor-admin"
                            local_key_path = "C:\Users\<USER>\.ssh\id_rsa"
                        }
                        service = @{
                            name = "database-service"
                            user = "database-service"
                            group = "database-service"
                        }
                        database = @{
                            name = "database_service"
                            username = "database_service"
                            port = 5432
                        }
                        version = @{
                            number = 1
                            created = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
                            updated = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
                        }
                    }

                    # Create config directory if it doesn't exist
                    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
                    if (-not (Test-Path -Path $configDir)) {
                        New-Item -Path $configDir -ItemType Directory -Force | Out-Null
                    }

                    # Save the new configuration
                    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($env.ToLower()).json"
                    $newConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8

                    # Load the configuration using our centralized module
                    Import-Configuration -ConfigFile $configPath | Out-Null

                    Write-Host "Default configuration created and loaded." -ForegroundColor Green
                    Start-Sleep -Seconds 1
                    # Refresh the menu
                    Show-DeploymentConfigurations
                    return
                } catch {
                    Write-Host "Error creating default configuration: $_" -ForegroundColor Red
                    Wait-ForUser
                    return
                }
            }
            "0" {
                Disable-UIMode
                if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
                    Show-MainMenu
                }
                return
            }
            default {
                Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
                Start-Sleep -Seconds 1
                Disable-UIMode
                if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
                    Show-MainMenu
                }
                return
            }
        }
    }

    # Now we have a valid configuration, show the menu
    Write-Log -Message "Current Configuration:" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message "  Project: $($config.project.name)" -Level "UI" -ForegroundColor White

    # Get environment from the centralized module
    $env = $script:Environment
    if ($null -ne $env:DeploymentEnvironment) {
        $env = $env:DeploymentEnvironment
    }

    Write-Log -Message "  Environment: $env" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Server: $($config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "[1] View Configuration Details" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Export Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Import Exported Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "---------------------------------------" -Level "UI" -ForegroundColor Gray
    Write-Log -Message "[0] Back to Main Menu" -Level "UI" -ForegroundColor White
    $choice = Read-Host "`nSelect an option (0-4)"
    # Disable UI Mode after menu display
    Disable-UIMode

    switch ($choice) {
        "1" { Show-ConfigDetails }
        "2" { Edit-Configuration }
        "3" { Export-Config }
        "4" { Import-Config }
        "0" { Show-MainMenu }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Show-DeploymentConfigurations
        }
    }
}

# Create a proper function with an approved verb
function Show-DeploymentConfigurationsUI {
    # Simply call the Show-DeploymentConfigurations function
    Show-DeploymentConfigurations
}

# Create an alias for backward compatibility
New-Alias -Name "Manage-DeploymentConfigurations" -Value Show-DeploymentConfigurationsUI -Force

# Create alias for backward compatibility
if (-not (Get-Alias -Name "ConfigManager" -ErrorAction SilentlyContinue)) {
    New-Alias -Name "ConfigManager" -Value Show-DeploymentConfigurations -Scope Global
}

# Show configuration details function
function Show-ConfigDetails {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Configuration Details                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Get the configuration from the centralized module
    $config = Get-Configuration

    # Check if we have a valid configuration
    if ($null -eq $config) {
        Write-Log -Message "No configuration loaded." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    # Get current environment
    $env = $script:Environment
    if ($null -ne $env:DeploymentEnvironment) {
        $env = $env:DeploymentEnvironment
    }

    # Display project details
    Write-Log -Message "Project Details:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Name: $($config.project.name)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Description: $($config.project.description)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Environment: $env" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Local Source Directory: $($config.project.local_source_dir)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Remote Install Directory: $($config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Remote Build Directory: $($config.project.remote_build_dir)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    # Display SSH details
    Write-Log -Message "SSH Details:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Host: $($config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $($config.ssh.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Username: $($config.ssh.username)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Local Key Path: $($config.ssh.local_key_path)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    # Display database details if they exist
    if ($null -ne $config.database) {
        Write-Log -Message "Database Details:" -Level "UI" -ForegroundColor Yellow
        Write-Log -Message "  Database Name: $($config.database.name)" -Level "UI" -ForegroundColor White
        if ($null -ne $config.database.username) {
            Write-Log -Message "  Username: $($config.database.username)" -Level "UI" -ForegroundColor White
        }
        if ($null -ne $config.database.port) {
            Write-Log -Message "  Port: $($config.database.port)" -Level "UI" -ForegroundColor White
        }
        Write-Log -Message " " -Level "UI"
    }

    # Display service details if they exist
    if ($null -ne $config.service) {
        Write-Log -Message "Service Details:" -Level "UI" -ForegroundColor Yellow
        Write-Log -Message "  Service Name: $($config.service.name)" -Level "UI" -ForegroundColor White
        if ($null -ne $config.service.user) {
            Write-Log -Message "  Service User: $($config.service.user)" -Level "UI" -ForegroundColor White
        }
        if ($null -ne $config.service.group) {
            Write-Log -Message "  Service Group: $($config.service.group)" -Level "UI" -ForegroundColor White
        }
        Write-Log -Message " " -Level "UI"
    }

    # Display version details
    if ($null -ne $config.version) {
        Write-Log -Message "Version Details:" -Level "UI" -ForegroundColor Yellow
        Write-Log -Message "  Version Number: $($config.version.number)" -Level "UI" -ForegroundColor White
        if ($null -ne $config.version.created) {
            Write-Log -Message "  Created: $($config.version.created)" -Level "UI" -ForegroundColor White
        }
        if ($null -ne $config.version.updated) {
            Write-Log -Message "  Last Updated: $($config.version.updated)" -Level "UI" -ForegroundColor White
        }
        Write-Log -Message " " -Level "UI"
    }

    # Display config file path
    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($env.ToLower()).json"
    Write-Log -Message "Configuration File: $configPath" -Level "UI" -ForegroundColor Cyan
    Write-Log -Message " " -Level "UI"

    # Return option
    Write-Log -Message "Press Enter to return to Configuration Manager..." -Level "UI" -ForegroundColor Green
    Read-Host | Out-Null
    # Disable UI Mode after display
    Disable-UIMode
    Show-DeploymentConfigurations
}

# Edit configuration function
function Edit-Configuration {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "                  Edit Configuration                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Get the configuration from the centralized module
    $config = Get-Configuration

    # If no configuration loaded, try to load it
    if ($null -eq $config) {
        # Use the environment variable if set
        $env = "development"
        if ($null -ne $env:DeploymentEnvironment) {
            $env = $env:DeploymentEnvironment
        }

        # Try to load the configuration
        $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
        $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($env.ToLower()).json"

        if (Test-Path -Path $configPath) {
            Import-Configuration -ConfigFile $configPath | Out-Null
            # Get the configuration again after loading
            $config = Get-Configuration
        }
    }

    # If still no configuration loaded, inform the user
    if ($null -eq $config) {
        Write-Log -Message "No configuration loaded." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    Write-Log -Message "[1] Edit Project Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Server Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Database Settings" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Show Raw Configuration" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Standardize All Configurations" -Level "UI" -ForegroundColor Green
    Write-Log -Message "[0] Back to Configuration Manager" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (0-5)"
    # Disable UI Mode after menu display
    Disable-UIMode

    switch ($choice) {
        "1" {
            # Call Edit-ProjectSettings with proper error handling
            try {
                if (Get-Command -Name Edit-ProjectSettings -ErrorAction SilentlyContinue) {
                    Edit-ProjectSettings
                } else {
                    Write-Host "Error: Edit-ProjectSettings function not found" -ForegroundColor Red
                    Start-Sleep -Seconds 2
                    Edit-Configuration
                }
            } catch {
                Write-Host "Error calling Edit-ProjectSettings: $_" -ForegroundColor Red
                Start-Sleep -Seconds 2
                Edit-Configuration
            }
        }
        "2" { Edit-ServerSettings }
        "3" { Edit-DatabaseSettings }
        "4" { Show-RawConfig }
        "5" {
            if (Get-Command -Name Update-ConfigurationFiles -ErrorAction SilentlyContinue) {
                Update-ConfigurationFiles
                Start-Sleep -Seconds 1
                Show-DeploymentConfigurations
            } else {
                Write-Host "Standardization module not found." -ForegroundColor Red
                Start-Sleep -Seconds 2
                Edit-Configuration
            }
        }
        "0" { Show-DeploymentConfigurations }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-Configuration
        }
    }
}

# Show Raw Configuration
function Show-RawConfig {
    Clear-Host
    Write-Host "Raw Configuration:" -ForegroundColor Cyan
    Write-Host "=====================================" -ForegroundColor Cyan

    # Get the configuration from the centralized module
    $config = Get-Configuration

    # Check if we have a valid configuration
    if ($null -eq $config) {
        Write-Host "No configuration loaded." -ForegroundColor Red
        Wait-ForUser
        Edit-Configuration
        return
    }

    # Convert configuration to pretty JSON and display
    $jsonConfig = $config | ConvertTo-Json -Depth 10
    Write-Log -Message $jsonConfig -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    # Return option
    Write-Log -Message "Press Enter to return to Edit Configuration..." -Level "UI" -ForegroundColor Green
    Read-Host | Out-Null
    # Disable UI Mode after display
    Disable-UIMode
    Edit-Configuration
}

# Edit Server Settings function
function Edit-ServerSettings {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Server Settings                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Get the configuration from the centralized module
    $config = Get-Configuration

    # Check if we have a valid configuration
    if ($null -eq $config -or $null -eq $config.ssh) {
        Write-Log -Message "No SSH configuration loaded." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Edit-Configuration
        return
    }

    # Show current settings
    Write-Log -Message "Current Server Settings:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Host: $($config.ssh.host)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Port: $($config.ssh.port)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Username: $($config.ssh.username)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Local Key Path: $($config.ssh.local_key_path)" -Level "UI" -ForegroundColor White
    Write-Log -Message " " -Level "UI"

    # Options
    Write-Log -Message "[1] Edit Host" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Port" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Username" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Local Key Path" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Test SSH Connection" -Level "UI" -ForegroundColor Green
    Write-Log -Message "[0] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (0-5)"
    # Disable UI Mode after menu display
    Disable-UIMode

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new host"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $config.ssh.host = $newValue
                try {
                    Save-Configuration
                    Write-Host "Host updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ServerSettings
        }
        "2" {
            $newValue = Read-Host "Enter new port"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                try {
                    $portNum = [int]$newValue
                    $config.ssh.port = $portNum
                    Save-Configuration
                    Write-Host "Port updated to: $portNum" -ForegroundColor Green
                } catch {
                    Write-Host "Error: Please enter a valid port number" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ServerSettings
        }
        "3" {
            $newValue = Read-Host "Enter new username"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $config.ssh.username = $newValue
                try {
                    Save-Configuration
                    Write-Host "Username updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ServerSettings
        }
        "4" {
            $newValue = Read-Host "Enter new local key path"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $config.ssh.local_key_path = $newValue
                try {
                    Save-Configuration
                    Write-Host "Local key path updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ServerSettings
        }
        "5" {
            Write-Host "Testing SSH connection to $($config.ssh.host)..." -ForegroundColor Yellow
            try {
                # Get a reference to the centralized Test-SSHConnection function
                if (Get-Command -Name Test-SSHConnection -ErrorAction SilentlyContinue) {
                    $result = Test-SSHConnection
                    if ($result) {
                        Write-Host "Connection successful!" -ForegroundColor Green
                    } else {
                        Write-Host "Connection failed." -ForegroundColor Red
                    }
                } else {
                    Write-Host "Test-SSHConnection function not found." -ForegroundColor Red
                }
            } catch {
                Write-Host "Error testing connection: $_" -ForegroundColor Red
            }
            Start-Sleep -Seconds 2
            Edit-ServerSettings
        }
        "0" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-ServerSettings
        }
    }
}

# Edit Database Settings function
function Edit-DatabaseSettings {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "              Edit Database Settings                  " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Get the configuration from the centralized module
    $config = Get-Configuration

    # Check if we have a valid configuration
    if ($null -eq $config) {
        Write-Log -Message "No configuration loaded." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Edit-Configuration
        return
    }

    # Initialize database section if it doesn't exist
    if ($null -eq $config.database) {
        $config.database = @{
            name = "database_service"
            username = "database_service"
            port = 5432
        }
        # Save this default configuration
        try {
            Save-Configuration
            Write-Host "Created default database configuration." -ForegroundColor Green
            Start-Sleep -Seconds 1
        } catch {
            Write-Host "Error saving default database configuration: $_" -ForegroundColor Red
            Start-Sleep -Seconds 2
            Edit-Configuration
            return
        }
    }

    # Show current settings
    Write-Log -Message "Current Database Settings:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Database Name: $($config.database.name)" -Level "UI" -ForegroundColor White
    if ($null -ne $config.database.username) {
        Write-Log -Message "  Username: $($config.database.username)" -Level "UI" -ForegroundColor White
    }
    if ($null -ne $config.database.host) {
        Write-Log -Message "  Host: $($config.database.host)" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "  Host: Not set (using localhost)" -Level "UI" -ForegroundColor Gray
    }
    if ($null -ne $config.database.port) {
        Write-Log -Message "  Port: $($config.database.port)" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "  Port: Not set (using default)" -Level "UI" -ForegroundColor Gray
    }
    Write-Log -Message " " -Level "UI"

    # Options
    Write-Log -Message "[1] Edit Database Name" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Username" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Host" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Port" -Level "UI" -ForegroundColor White
    Write-Log -Message "[0] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (0-4)"
    # Disable UI Mode after menu display
    Disable-UIMode

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new database name"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $config.database.name = $newValue
                try {
                    Save-Configuration
                    Write-Host "Database name updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-DatabaseSettings
        }
        "2" {
            $newValue = Read-Host "Enter new username"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $config.database.username = $newValue
                try {
                    Save-Configuration
                    Write-Host "Username updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-DatabaseSettings
        }
        "3" {
            $newValue = Read-Host "Enter database host (leave empty for localhost)"
            # Even empty string is valid here (means localhost)
            $config.database.host = $newValue
            try {
                Save-Configuration
                if ([string]::IsNullOrWhiteSpace($newValue)) {
                    Write-Host "Host set to default (localhost)" -ForegroundColor Green
                } else {
                    Write-Host "Host updated to: $newValue" -ForegroundColor Green
                }
            } catch {
                Write-Host "Error saving configuration: $_" -ForegroundColor Red
            }
            Start-Sleep -Seconds 1
            Edit-DatabaseSettings
        }
        "4" {
            $newValue = Read-Host "Enter database port number"
            if (-not [string]::IsNullOrWhiteSpace($newValue) -and $newValue -match "^\d+$") {
                $config.database.port = [int]$newValue
                try {
                    Save-Configuration
                    Write-Host "Port updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-DatabaseSettings
        }
        "0" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-DatabaseSettings
        }
    }
}

# Export configuration function
function Export-Config {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Export Configuration                    " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host ""

    # Get the configuration from the centralized module
    $config = Get-Configuration

    # Check if we have a valid configuration
    if ($null -eq $config) {
        Write-Host "No configuration loaded." -ForegroundColor Red
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    # Get current environment
    $env = $script:Environment
    if ($null -ne $env:DeploymentEnvironment) {
        $env = $env:DeploymentEnvironment
    }

    # Ask user for the export file location
    $defaultPath = Join-Path -Path $env:USERPROFILE -ChildPath "Desktop\database-service-$($env.ToLower())-export.json"
    $exportPath = Read-Host "Enter the export file path [$defaultPath]"

    if ([string]::IsNullOrWhiteSpace($exportPath)) {
        $exportPath = $defaultPath
    }

    try {
        # Add export metadata
        $exportConfig = $config.PSObject.Copy()
        if ($null -eq $exportConfig.export) {
            $exportConfig | Add-Member -MemberType NoteProperty -Name "export" -Value @{}
        }
        $exportConfig.export.timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $exportConfig.export.environment = $env
        $exportConfig.export.machine = $env:COMPUTERNAME
        $exportConfig.export.user = $env:USERNAME

        # Ensure the directory exists
        $exportDir = Split-Path -Path $exportPath -Parent
        if (-not (Test-Path -Path $exportDir)) {
            New-Item -Path $exportDir -ItemType Directory -Force | Out-Null
        }

        # Export to JSON
        $exportConfig | ConvertTo-Json -Depth 10 | Set-Content -Path $exportPath -Encoding UTF8
        Write-Host "Configuration exported to: $exportPath" -ForegroundColor Green
    } catch {
        Write-Host "Error exporting configuration: $_" -ForegroundColor Red
    }

    Wait-ForUser
    Show-DeploymentConfigurations
}

# Import configuration function
function Import-Config {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Import Configuration                    " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host ""

    # Ask user for the import file location
    $defaultPath = Join-Path -Path $env:USERPROFILE -ChildPath "Desktop\database-service-export.json"
    $importPath = Read-Host "Enter the import file path [$defaultPath]"

    if ([string]::IsNullOrWhiteSpace($importPath)) {
        $importPath = $defaultPath
    }

    # Verify the file exists
    if (-not (Test-Path -Path $importPath)) {
        Write-Host "Import file not found: $importPath" -ForegroundColor Red
        Wait-ForUser
        Show-DeploymentConfigurations
        return
    }

    try {
        # Import the configuration using our centralized function
        if (Import-Configuration -ConfigFile $importPath) {
            # Get the imported configuration
            $config = Get-Configuration

            # Display import information
            Write-Host "Configuration imported successfully:" -ForegroundColor Green
            Write-Host "  Project: $($config.project.name)" -ForegroundColor White

            # Show export metadata if available
            if ($null -ne $config.export) {
                Write-Host "  Export Information:" -ForegroundColor Yellow
                if ($null -ne $config.export.timestamp) {
                    Write-Host "    Timestamp: $($config.export.timestamp)" -ForegroundColor White
                }
                if ($null -ne $config.export.environment) {
                    Write-Host "    Environment: $($config.export.environment)" -ForegroundColor White
                }
                if ($null -ne $config.export.machine) {
                    Write-Host "    Machine: $($config.export.machine)" -ForegroundColor White
                }
                if ($null -ne $config.export.user) {
                    Write-Host "    User: $($config.export.user)" -ForegroundColor White
                }
            }

            # Ask if user wants to save this as the current configuration
            $saveChoice = Read-Host "Do you want to save this as your current configuration? (Y/N)"
            if ($saveChoice -eq "Y" -or $saveChoice -eq "y") {
                # Get current environment
                $env = $script:Environment
                if ($null -ne $env:DeploymentEnvironment) {
                    $env = $env:DeploymentEnvironment
                }

                # Save the configuration
                Save-Configuration
                Write-Host "Configuration saved as the current $env configuration." -ForegroundColor Green
            }
        } else {
            Write-Host "Failed to import configuration." -ForegroundColor Red
        }
    } catch {
        Write-Host "Error importing configuration: $_" -ForegroundColor Red
    }

    Wait-ForUser
    Show-DeploymentConfigurations
}

# Edit Project Settings function
function Edit-ProjectSettings {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Edit Project Settings                   " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"

    # Ensure we have a valid configuration
    if ($null -eq $script:Config -or $null -eq $script:Config.project) {
        Write-Log -Message "No configuration loaded." -Level "UI" -ForegroundColor Red
        Wait-ForUser
        Edit-Configuration
        return
    }

    Write-Log -Message "Current Project Settings:" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "  Name: $($script:Config.project.name)" -Level "UI" -ForegroundColor White
    if ($script:Config.project.description) {
        Write-Log -Message "  Description: $($script:Config.project.description)" -Level "UI" -ForegroundColor White
    }
    Write-Log -Message "  Source Directory: $($script:Config.project.local_source_dir)" -Level "UI" -ForegroundColor White
    Write-Log -Message "  Install Directory: $($script:Config.project.remote_install_dir)" -Level "UI" -ForegroundColor White
    if ($script:Config.project.remote_build_dir) {
        Write-Log -Message "  Build Directory: $($script:Config.project.remote_build_dir)" -Level "UI" -ForegroundColor White
    }
    Write-Log -Message " " -Level "UI"

    Write-Log -Message "[1] Edit Project Name" -Level "UI" -ForegroundColor White
    Write-Log -Message "[2] Edit Project Description" -Level "UI" -ForegroundColor White
    Write-Log -Message "[3] Edit Source Directory" -Level "UI" -ForegroundColor White
    Write-Log -Message "[4] Edit Install Directory" -Level "UI" -ForegroundColor White
    Write-Log -Message "[5] Edit Build Directory" -Level "UI" -ForegroundColor White
    Write-Log -Message "[6] Back to Edit Configuration" -Level "UI" -ForegroundColor White

    $choice = Read-Host "`nSelect an option (1-6)"
    # Disable UI Mode after menu display
    Disable-UIMode

    switch ($choice) {
        "1" {
            $newValue = Read-Host "Enter new project name"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.name = $newValue
                $global:Config.project.name = $newValue
                try {
                    # Direct save
                    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
                    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($script:Environment.ToLower()).json"
                    $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
                    Write-Host "Project name updated to: $newValue" -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ProjectSettings
        }
        "2" {
            $newValue = Read-Host "Enter new project description"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.description = $newValue
                $global:Config.project.description = $newValue
                try {
                    # Direct save
                    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
                    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($script:Environment.ToLower()).json"
                    $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
                    Write-Host "Project description updated." -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ProjectSettings
        }
        "3" {
            $newValue = Read-Host "Enter new source directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.local_source_dir = $newValue
                $global:Config.project.local_source_dir = $newValue
                try {
                    # Direct save
                    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
                    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($script:Environment.ToLower()).json"
                    $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
                    Write-Host "Source directory updated." -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ProjectSettings
        }
        "4" {
            $newValue = Read-Host "Enter new install directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.remote_install_dir = $newValue
                $global:Config.project.remote_install_dir = $newValue
                try {
                    # Direct save
                    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
                    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($script:Environment.ToLower()).json"
                    $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
                    Write-Host "Install directory updated." -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ProjectSettings
        }
        "5" {
            $newValue = Read-Host "Enter new build directory"
            if (-not [string]::IsNullOrWhiteSpace($newValue)) {
                $script:Config.project.remote_build_dir = $newValue
                $global:Config.project.remote_build_dir = $newValue
                try {
                    # Direct save
                    $configDir = Join-Path -Path $PSScriptRoot -ChildPath "..\config"
                    $configPath = Join-Path -Path $configDir -ChildPath "database-service-$($script:Environment.ToLower()).json"
                    $script:Config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
                    Write-Host "Build directory updated." -ForegroundColor Green
                } catch {
                    Write-Host "Error saving configuration: $_" -ForegroundColor Red
                }
                Start-Sleep -Seconds 1
            }
            Edit-ProjectSettings
        }
        "6" { Edit-Configuration }
        default {
            Write-Log -Message "Invalid option. Please try again." -Level "Error" -Component "Config"
            Start-Sleep -Seconds 1
            Edit-ProjectSettings
        }
    }
}

# Export both functions and any aliases
Export-ModuleMember -Function Show-DeploymentConfigurations, Show-DeploymentConfigurationsUI, Update-ConfigurationPaths, Show-ConfigDetails, Edit-Configuration, Export-Config, Import-Config, Edit-ProjectSettings, Edit-ServerSettings, Edit-DatabaseSettings
Export-ModuleMember -Alias ConfigManager, Manage-DeploymentConfigurations
