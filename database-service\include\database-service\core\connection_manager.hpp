#pragma once
#include <string>
#include <memory>
#include <deque>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <optional>
#include <chrono>
#include <expected>
#include <span>
#include <format>

namespace dbservice::core {

// Forward declaration
class Connection;

/**
 * @enum SSLMode
 * @brief SSL connection verification modes
 */
enum class SSLMode {
    Disable,       // SSL is disabled
    Allow,         // Use SSL if available, but don't verify
    Prefer,        // Try SSL first, fall back to non-SSL
    Require,       // Require SSL, but don't verify certificate
    VerifyCa,      // Verify that the server certificate is issued by a trusted CA
    VerifyFull     // Verify that the server certificate is issued by a trusted CA and the server hostname matches
};

/**
 * @struct SSLConfig
 * @brief SSL configuration options
 */
struct SSLConfig {
    bool enabled = false;
    SSLMode mode = SSLMode::VerifyFull;
    std::string certPath;
    std::string keyPath;
    std::string caPath;
    std::string crlPath;
    bool rejectExpired = true;
};

/**
 * @class ConnectionManager
 * @brief Manages database connections
 */
class ConnectionManager {
public:
    /**
     * @brief Constructor
     * @param connectionString Database connection string
     * @param maxConnections Maximum number of connections
     * @param sslConfig SSL configuration
     */
    ConnectionManager(const std::string& connectionString, size_t maxConnections, const SSLConfig& sslConfig);

    /**
     * @brief Constructor with simplified SSL configuration
     * @param connectionString Database connection string
     * @param maxConnections Maximum number of connections
     * @param useSSL Whether to use SSL
     */
    ConnectionManager(const std::string& connectionString, size_t maxConnections, bool useSSL);

    /**
     * @brief Destructor
     */
    ~ConnectionManager();

    /**
     * @brief Get a connection from the pool
     * @return Connection
     */
    std::expected<std::shared_ptr<Connection>, std::string> getConnection();

    /**
     * @brief Return a connection to the pool
     * @param connection Connection to return
     */
    void returnConnection(std::shared_ptr<Connection> connection);

    /**
     * @brief Execute a query
     * @param query Query to execute
     * @param params Query parameters
     * @return Query result or error message
     */
    std::expected<std::vector<std::vector<std::string>>, std::string> executeQuery(const std::string& query, const std::vector<std::string>& params = {});

    /**
     * @brief Execute a query with a callback
     * @param query Query to execute
     * @param callback Callback to execute for each row
     * @param params Query parameters
     * @return Success or error message
     */
    std::expected<void, std::string> executeQueryWithCallback(const std::string& query, std::function<void(std::span<const std::string>)> callback, const std::vector<std::string>& params = {});

    /**
     * @brief Execute a non-query statement
     * @param statement Statement to execute
     * @param params Statement parameters
     * @return Number of rows affected or error message
     */
    std::expected<int, std::string> executeNonQuery(const std::string& statement, const std::vector<std::string>& params = {});

    /**
     * @brief Begin a transaction
     * @return Transaction object or error message
     */
    std::expected<std::shared_ptr<class Transaction>, std::string> beginTransaction();

    /**
     * @brief Shutdown the connection manager
     */
    void shutdown();

    /**
     * @brief Get the SSL configuration
     * @return SSL configuration
     */
    const SSLConfig& getSSLConfig() const;

    /**
     * @brief Get the number of active connections
     * @return Number of active connections
     */
    size_t getActiveConnectionCount() const;

    /**
     * @brief Get the number of idle connections
     * @return Number of idle connections
     */
    size_t getIdleConnectionCount() const;

    /**
     * @brief Get the number of waiting connection requests
     * @return Number of waiting connection requests
     */
    size_t getWaitingConnectionCount() const;

    /**
     * @brief Get the maximum number of connections
     * @return Maximum number of connections
     */
    size_t getMaxConnections() const;

    /**
     * @brief Update database metrics
     */
    void updateMetrics();

private:
    /**
     * @brief Create a new connection
     * @return New connection
     */
    std::shared_ptr<Connection> createConnection();

    /**
     * @brief Build connection string with SSL parameters
     * @return Connection string with SSL parameters
     */
    std::string buildConnectionString() const;

    /**
     * @brief Convert SSLMode to string
     * @param mode SSL mode
     * @return String representation of SSL mode
     */
    static std::string sslModeToString(SSLMode mode);

    std::string connectionString_;
    size_t maxConnections_;
    SSLConfig sslConfig_;
        std::deque<std::shared_ptr<Connection>> connections_;
    mutable std::mutex mutex_;
    std::condition_variable cv_;
    std::chrono::milliseconds connectionTimeout_{5000}; // Default timeout for acquiring a connection
    bool shutdown_;

    // Metrics tracking
    size_t activeConnections_;
    size_t waitingConnections_;
    std::chrono::steady_clock::time_point lastMetricsUpdate_;
};

class ManagedConnection {
public:
    explicit ManagedConnection(ConnectionManager& manager);
    ~ManagedConnection();

    ManagedConnection(ManagedConnection&& other) noexcept;
    ManagedConnection& operator=(ManagedConnection&& other) noexcept;

    ManagedConnection(const ManagedConnection&) = delete;
    ManagedConnection& operator=(const ManagedConnection&) = delete;

    Connection* operator->() const;
    std::shared_ptr<Connection> get() const;
    explicit operator bool() const; // Checks if connection is valid

    // Allows checking the error if connection acquisition failed
    const std::string* getError() const; 

private:
    ConnectionManager* manager_; // Non-owning pointer to the manager
    std::expected<std::shared_ptr<Connection>, std::string> connection_expected_;
    bool moved_from_; // To prevent returning connection in destructor if moved
};

} // namespace dbservice::core
