#include "database-service/api/route_controller.hpp"
#include "database-service/utils/logger.hpp"
#include <nlohmann/json.hpp>

namespace dbservice::api {

RouteController::RouteController(
    std::shared_ptr<core::ConnectionManager> connectionManager,
    std::shared_ptr<security::SecurityManager> securityManager)
    : connectionManager_(std::move(connectionManager)),
      securityManager_(std::move(securityManager)) {}

void RouteController::registerRoutes(ApiServer& server) {
    // Health check endpoint
    server.addRoute("GET", "/api/health", [this](const ParsedRequest& request) {
        return handleHealthCheck(request);
    });

    utils::Logger::info("Registered health check route");
}

std::expected<Response, std::string> RouteController::handleHealthCheck(const ParsedRequest& request) {
    nlohmann::json response_body;
    response_body["status"] = "ok";

    Response response;
    response.headers["Content-Type"] = "application/json";
    response.body = response_body.dump();

    return response;
}

} // namespace dbservice::api
