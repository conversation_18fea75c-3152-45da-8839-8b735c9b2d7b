# Logging Service API

This document describes the API endpoints provided by the C++23 Logging Service.

## Overview

The Logging Service provides a RESTful API for retrieving and managing logs, as well as monitoring the health of the service. All API endpoints return JSON responses.

## Base URL

All API endpoints are relative to the base URL of the Logging Service:

```
http://<server>:<port>/
```

By default, the service runs on port 8000.

## Authentication

Currently, the API does not require authentication. It is designed to be used behind a reverse proxy (nginx) that handles authentication and SSL termination.

## API Endpoints

### Log Retrieval

#### Get Logs

Retrieves logs based on specified filters.

- **URL**: `/api/logs`
- **Method**: GET
- **Query Parameters**:
  - `source` (optional): Filter logs by source (e.g., "git", "nginx", "system")
  - `level` (optional): Filter logs by level (e.g., "info", "warning", "error")
  - `count` (optional): Maximum number of logs to return
  - `all` (optional): If "true", returns all logs regardless of count limit

- **Response**:
  ```json
  {
    "logs": [
      {
        "timestamp": "2023-10-30T12:34:56Z",
        "source": "git",
        "level": "info",
        "message": "Repository created: example.git",
        "metadata": {
          "user": "admin",
          "ip": "*************"
        }
      },
      ...
    ],
    "total": 42,
    "returned": 10
  }
  ```

#### Get Log Sources

Retrieves available log sources.

- **URL**: `/api/logs/sources`
- **Method**: GET
- **Response**:
  ```json
  {
    "sources": ["git", "nginx", "system", "auth", "dashboard"]
  }
  ```

#### Get Log Statistics

Retrieves statistics about logs.

- **URL**: `/api/logs/stats`
- **Method**: GET
- **Response**:
  ```json
  {
    "total": 1024,
    "by_source": {
      "git": 512,
      "nginx": 256,
      "system": 128,
      "auth": 64,
      "dashboard": 64
    },
    "by_level": {
      "info": 768,
      "warning": 128,
      "error": 64,
      "debug": 64
    },
    "oldest": "2023-10-01T00:00:00Z",
    "newest": "2023-10-30T23:59:59Z"
  }
  ```

### Log Management

#### Get Available Logs

Retrieves information about available log files.

- **URL**: `/api/logs/available`
- **Method**: GET
- **Response**:
  ```json
  {
    "available": [
      {
        "path": "/var/log/syslog",
        "size": 1048576,
        "last_modified": "2023-10-30T12:34:56Z"
      },
      ...
    ]
  }
  ```

#### Scan for Logs

Triggers a scan for new log files.

- **URL**: `/api/logs/scan`
- **Method**: POST
- **Response**:
  ```json
  {
    "success": true,
    "found": 3,
    "new": 1
  }
  ```

#### Configure Logs

Configures log sources and parsing rules.

- **URL**: `/api/logs/configure`
- **Method**: POST
- **Request Body**:
  ```json
  {
    "sources": [
      {
        "name": "git",
        "path": "/var/log/git",
        "enabled": true,
        "parser": "git"
      },
      ...
    ]
  }
  ```
- **Response**:
  ```json
  {
    "success": true
  }
  ```

### Health Monitoring

#### Health Check

Retrieves the health status of the Logging Service.

- **URL**: `/health`
- **Method**: GET
- **Response**:
  ```json
  {
    "status": "ok",
    "version": "1.0.0",
    "timestamp": "**********",
    "storage": {
      "database": "connected",
      "database_circuit": "CLOSED"
    },
    "uptime_seconds": 3600
  }
  ```

- **Response Fields**:
  - `status`: Overall service status ("ok" or "error")
  - `version`: Service version number
  - `timestamp`: Current server timestamp (Unix epoch)
  - `storage.database`: Database connection status ("connected" or "disconnected")
  - `storage.database_circuit`: Circuit breaker state ("CLOSED", "OPEN", or "HALF-OPEN")
  - `uptime_seconds`: Service uptime in seconds

### WebSocket API

The Logging Service also provides a WebSocket API for real-time log updates.

#### Log Stream

- **URL**: `/api/logs/stream`
- **Protocol**: WebSocket
- **Messages**:
  - **Subscribe**:
    ```json
    {
      "action": "subscribe",
      "source": "git",
      "level": "info"
    }
    ```
  - **Unsubscribe**:
    ```json
    {
      "action": "unsubscribe"
    }
    ```
  - **Log Update** (server to client):
    ```json
    {
      "timestamp": "2023-10-30T12:34:56Z",
      "source": "git",
      "level": "info",
      "message": "Repository created: example.git",
      "metadata": {
        "user": "admin",
        "ip": "*************"
      }
    }
    ```

## Error Handling

All API endpoints return appropriate HTTP status codes:

- **200 OK**: Request successful
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

Error responses include a JSON body with an error message:

```json
{
  "error": "Invalid source parameter"
}
```

## Rate Limiting

Currently, the API does not implement rate limiting. However, it is recommended to implement rate limiting at the reverse proxy level.

## Versioning

The current API version is v1. The version is not included in the URL path but is returned in the health check response.

## Examples

### Curl Examples

#### Get Logs
```bash
curl -X GET "http://localhost:8000/api/logs?source=git&level=error&count=10"
```

#### Get Log Sources
```bash
curl -X GET "http://localhost:8000/api/logs/sources"
```

#### Health Check
```bash
curl -X GET "http://localhost:8000/health"
```

### JavaScript Examples

#### Get Logs
```javascript
fetch('/api/logs?source=git&level=error&count=10')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

#### Health Check
```javascript
fetch('/health')
  .then(response => response.json())
  .then(health => {
    console.log('Service status:', health.status);
    console.log('Database connected:', health.storage.database === 'connected');
    console.log('Circuit breaker state:', health.storage.database_circuit);
  })
  .catch(error => console.error('Error:', error));
```

## Conclusion

The Logging Service API provides comprehensive endpoints for retrieving and managing logs, as well as monitoring the health of the service. By using these endpoints, clients can access log data, configure log sources, and monitor the operational status of the service.
