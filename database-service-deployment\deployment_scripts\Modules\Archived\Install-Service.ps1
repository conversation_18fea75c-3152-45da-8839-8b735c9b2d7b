# Install Service Module

# Import common module
Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force
# Import Logger module
Import-Module -Name "$PSScriptRoot\Logger.psm1" -Force

# Install service function
function Install-Service {
    Clear-Host
    Write-Log -Message "========== Install Service ==========" -Level "Info" -Component "Service"
    Write-Log -Message "               Install Service                        " -Level "Info" -Component "Service"
    Write-Log -Message "========== Install Service ==========" -Level "Info" -Component "Service"
    Write-Log -Message "" -Level "Info" -Component "Service"
    
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Service"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    
    # Check if the installation directory exists
    $installDir = $Config.project.install_dir
    $checkDirCmd = "test -d $installDir && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $dirExists = Invoke-RemoteCommand -Command $checkDirCmd -Silent
    
    if ($dirExists -ne "EXISTS") {
        Write-Log -Message "Installation directory does not exist: $installDir" -Level "Error" -Component "Service"
        Write-Log -Message "Please build the project first." -Level "Warning" -Component "Service"
        Wait-ForUser
        & "$PSScriptRoot\Build-Project.ps1"
        return
    }
    
    # Check if the service is already installed
    $serviceName = $Config.service.name
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    
    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName is already installed." -Level "Warning" -Component "Service"
        $reinstall = Read-Host "Do you want to reinstall the service? (y/n)"
        
        if ($reinstall -ne "y") {
            Write-Log -Message "Installation cancelled." -Level "Warning" -Component "Service"
            Show-MainMenu
            return
        }
        
        # Stop the service before reinstalling
        Write-Log -Message "Stopping existing service..." -Level "Info" -Component "Service"
        $stopCmd = "sudo systemctl stop $serviceName"
        Invoke-RemoteCommand -Command $stopCmd
    }
    
    # Create the service user and group if they don't exist
    $serviceUser = $Config.service.user
    $serviceGroup = $Config.service.group
    
    Write-Log -Message "Checking if service user and group exist..." -Level "Info" -Component "Service"
    
    # Check if the group exists
    $checkGroupCmd = "getent group $serviceGroup > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $groupExists = Invoke-RemoteCommand -Command $checkGroupCmd -Silent
    
    if ($groupExists -ne "EXISTS") {
        Write-Log -Message "Creating service group: $serviceGroup" -Level "Info" -Component "Service"
        $createGroupCmd = "sudo groupadd $serviceGroup"
        Invoke-RemoteCommand -Command $createGroupCmd
    } else {
        Write-Log -Message "Service group $serviceGroup already exists." -Level "Info" -Component "Service"
    }
    
    # Check if the user exists
    $checkUserCmd = "id -u $serviceUser > /dev/null 2>&1 && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    
    if ($userExists -ne "EXISTS") {
        Write-Log -Message "Creating service user: $serviceUser" -Level "Info" -Component "Service"
        $createUserCmd = "sudo useradd -r -g $serviceGroup -d $installDir -s /bin/false $serviceUser"
        Invoke-RemoteCommand -Command $createUserCmd
    } else {
        Write-Log -Message "Service user $serviceUser already exists." -Level "Info" -Component "Service"
    }
    
    # Set permissions on the installation directory
    Write-Log -Message "Setting permissions on installation directory..." -Level "Info" -Component "Service"
    $setPermCmd = "sudo chown -R $serviceUser:$serviceGroup $installDir"
    Invoke-RemoteCommand -Command $setPermCmd
    
    # Create the systemd service file
    Write-Log -Message "Creating systemd service file..." -Level "Info" -Component "Service"
    
    $serviceDescription = $Config.service.description
    $serviceFile = @"
[Unit]
Description=$serviceDescription
After=network.target postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceGroup
WorkingDirectory=$installDir
ExecStart=$installDir/bin/database-service
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
"@
    
    # Create a temporary file for the service definition
    $tempServiceFile = Join-Path -Path $env:TEMP -ChildPath "$serviceName.service"
    $serviceFile | Set-Content -Path $tempServiceFile -Encoding UTF8
    
    # Upload the service file to the server
    $scpArgs = "-i `"$($Config.ssh.key_path)`" -P $($Config.ssh.port) `"$tempServiceFile`" $($Config.ssh.username)@$($Config.ssh.host):/tmp/"
    
    try {
        $process = Start-Process -FilePath "scp" -ArgumentList $scpArgs -NoNewWindow -PassThru -Wait
        
        if ($process.ExitCode -eq 0) {
            Write-Log -Message "Service file uploaded successfully!" -Level "Info" -Component "Service"
        } else {
            Write-Log -Message "Failed to upload service file. Exit code: $($process.ExitCode)" -Level "Error" -Component "Service"
            Wait-ForUser
            Show-MainMenu
            return
        }
    } catch {
        Write-Log -Message "Error uploading service file: $_" -Level "Error" -Component "Service"
        Wait-ForUser
        Show-MainMenu
        return
    }
    
    # Move the service file to the systemd directory and reload systemd
    Write-Log -Message "Installing service..." -Level "Info" -Component "Service"
    $installServiceCmd = @"
sudo mv /tmp/$serviceName.service /etc/systemd/system/ && \
sudo systemctl daemon-reload && \
sudo systemctl enable $serviceName
"@
    Invoke-RemoteCommand -Command $installServiceCmd
    
    # Verify the service was installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    
    if ($serviceExists -gt 0) {
        Write-Log -Message "Service $serviceName installed successfully!" -Level "Info" -Component "Service"
    } else {
        Write-Log -Message "Failed to install service $serviceName." -Level "Error" -Component "Service"
    }
    
    Show-MainMenu
}

# Run the function
Install-Service
