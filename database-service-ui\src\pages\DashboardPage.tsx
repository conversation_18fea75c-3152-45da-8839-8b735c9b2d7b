import React, { useState, useEffect } from 'react';
import { metricsAPI } from '../services/api';
import { formatPercent, formatNumber } from '../utils/formatters';
import { getCurrentUser } from '../utils/auth';
import './DashboardPage.css';

const DashboardPage: React.FC = () => {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const user = getCurrentUser();
  
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await metricsAPI.getAllMetrics();
        
        if (response.data.success) {
          setMetrics(response.data.data);
        } else {
          setError(response.data.error?.message || 'Failed to load metrics');
        }
      } catch (err: any) {
        setError(err.response?.data?.error?.message || err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMetrics();
    
    // Refresh metrics every 30 seconds
    const intervalId = setInterval(fetchMetrics, 30000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  return (
    <div className="dashboard-page">
      <div className="container">
        <h1 className="page-title">Dashboard</h1>
        
        <div className="welcome-card card">
          <h2>Welcome, {user?.username || 'User'}</h2>
          <p>This is the Database Service management dashboard. Here you can monitor the database service performance and manage its configuration.</p>
        </div>
        
        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading dashboard data...</p>
          </div>
        ) : error ? (
          <div className="alert alert-danger">{error}</div>
        ) : (
          <>
            <div className="metrics-summary">
              <div className="metrics-summary-card card">
                <h3>Connection Pool</h3>
                <div className="metric-value">
                  <span className="metric-number">{metrics?.connection_pool?.active_connections || 0}</span>
                  <span className="metric-label">Active Connections</span>
                </div>
                <div className="metric-progress">
                  <div 
                    className="metric-progress-bar" 
                    style={{ 
                      width: `${metrics?.connection_pool?.connection_utilization * 100 || 0}%`,
                      backgroundColor: metrics?.connection_pool?.connection_utilization > 0.8 ? 'var(--error-color)' : 'var(--primary-color)'
                    }}
                  ></div>
                </div>
                <div className="metric-details">
                  {formatPercent(metrics?.connection_pool?.connection_utilization || 0)} of {metrics?.connection_pool?.max_connections || 0} connections used
                </div>
              </div>
              
              <div className="metrics-summary-card card">
                <h3>Query Performance</h3>
                <div className="metric-value">
                  <span className="metric-number">
                    {metrics?.query_performance?.SELECT?.avg_time_ms?.toFixed(2) || '0.00'}
                  </span>
                  <span className="metric-label">Avg. SELECT Time (ms)</span>
                </div>
                <div className="metric-details">
                  {formatNumber(metrics?.query_performance?.SELECT?.count || 0)} queries executed
                </div>
                <div className="metric-details">
                  {formatPercent(metrics?.query_performance?.SELECT?.error_rate || 0)} error rate
                </div>
              </div>
              
              <div className="metrics-summary-card card">
                <h3>Transactions</h3>
                <div className="metric-value">
                  <span className="metric-number">{metrics?.transactions?.total_count || 0}</span>
                  <span className="metric-label">Total Transactions</span>
                </div>
                <div className="metric-details">
                  {formatPercent(metrics?.transactions?.commit_rate || 0)} commit rate
                </div>
                <div className="metric-details">
                  {metrics?.transactions?.avg_transaction_time_ms?.toFixed(2) || '0.00'} ms avg. duration
                </div>
              </div>
              
              <div className="metrics-summary-card card">
                <h3>Authentication</h3>
                <div className="metric-value">
                  <span className="metric-number">{metrics?.authentication?.total_count || 0}</span>
                  <span className="metric-label">Auth Attempts</span>
                </div>
                <div className="metric-details">
                  {formatPercent(metrics?.authentication?.success_rate || 0)} success rate
                </div>
                <div className="metric-details">
                  {metrics?.authentication?.failure_count || 0} failed attempts
                </div>
              </div>
            </div>
            
            <div className="dashboard-actions card">
              <h3>Quick Actions</h3>
              <div className="action-buttons">
                <a href="/metrics" className="btn btn-primary">View Detailed Metrics</a>
                <a href="/credentials" className="btn btn-secondary">Manage Credentials</a>
                <a href="/settings" className="btn btn-secondary">Service Settings</a>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DashboardPage;
