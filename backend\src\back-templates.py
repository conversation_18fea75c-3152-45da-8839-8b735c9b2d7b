from typing import Dict, List, Optional
import json
import os
import re
from datetime import datetime

class ProjectTemplate:
    DEFAULT_CATEGORIES = [
        "Configuration Management",
        "Frontend Architecture",
        "Communication Systems",
        "Authentication",
        "Error Handling",
        "Performance Optimization"
    ]
    
    SEVERITY_PATTERNS = {
        "critical": [
            r"security",
            r"vulnerability",
            r"crash",
            r"data.*loss",
            r"breach",
            r"deadlock",
            r"race.*condition",
            r"memory.*leak"
        ],
        "high": [
            r"performance.*impact",
            r"memory.*leak",
            r"deadlock",
            r"race.*condition",
            r"api.*break",
            r"backward.*compatibility",
            r"data.*corruption"
        ],
        "medium": [
            r"feature",
            r"enhancement",
            r"refactor",
            r"optimization",
            r"usability",
            r"maintainability"
        ],
        "low": [
            r"typo",
            r"documentation",
            r"style",
            r"cleanup",
            r"formatting",
            r"comment"
        ]
    }
    
    DEFAULT_TEMPLATES = {
        "RMM Backend": {
            "description": "Remote Monitoring and Management Backend System",
            "categories": DEFAULT_CATEGORIES + ["API Design", "Database Optimization"],
            "commit_patterns": [
                r"^feat\(.*\):",
                r"^fix\(.*\):",
                r"^refactor:",
                r"^perf:",
                r"^security:"
            ],
            "tracked_files": [
                "*.cpp",
                "*.h",
                "*.py",
                "CMakeLists.txt",
                "config/*.json"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Frontend System": {
            "description": "Web-based Frontend Interface",
            "categories": DEFAULT_CATEGORIES + ["UI Components", "State Management"],
            "commit_patterns": [
                r"^feat\(.*\):",
                r"^fix\(.*\):",
                r"^style:",
                r"^refactor:",
                r"^perf:"
            ],
            "tracked_files": [
                "*.ts",
                "*.tsx",
                "*.css",
                "*.scss",
                "package.json"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Authentication Service": {
            "description": "Authentication and Authorization System",
            "categories": DEFAULT_CATEGORIES + ["Security", "Token Management", "Access Control"],
            "commit_patterns": [
                r"^feat\(auth\):",
                r"^fix\(auth\):",
                r"^security:",
                r"^refactor:",
                r"^perf:"
            ],
            "tracked_files": [
                "auth/*.py",
                "auth/*.cpp",
                "auth/*.h",
                "config/auth*.json"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Database Service": {
            "description": "Database Management and Optimization Service",
            "categories": DEFAULT_CATEGORIES + ["Query Optimization", "Schema Management", "Data Migration"],
            "commit_patterns": [
                r"^feat\(db\):",
                r"^fix\(db\):",
                r"^perf\(db\):",
                r"^schema:",
                r"^migrate:"
            ],
            "tracked_files": [
                "db/*.sql",
                "db/*.py",
                "migrations/*.sql",
                "config/db*.json"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "API Gateway": {
            "description": "API Gateway and Service Mesh",
            "categories": DEFAULT_CATEGORIES + ["Route Management", "Load Balancing", "Service Discovery"],
            "commit_patterns": [
                r"^feat\(gateway\):",
                r"^fix\(gateway\):",
                r"^route:",
                r"^proxy:",
                r"^balance:"
            ],
            "tracked_files": [
                "gateway/*.yaml",
                "gateway/*.json",
                "routes/*.yaml",
                "config/gateway*.yaml"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Monitoring System": {
            "description": "System Monitoring and Alerting",
            "categories": DEFAULT_CATEGORIES + ["Metrics Collection", "Alert Rules", "Dashboard Design"],
            "commit_patterns": [
                r"^feat\(monitor\):",
                r"^fix\(monitor\):",
                r"^alert:",
                r"^metric:",
                r"^dashboard:"
            ],
            "tracked_files": [
                "monitoring/*.yaml",
                "alerts/*.yaml",
                "dashboards/*.json",
                "config/monitoring*.yaml"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "DevOps Pipeline": {
            "description": "CI/CD and Infrastructure as Code",
            "categories": DEFAULT_CATEGORIES + ["Pipeline Configuration", "Infrastructure", "Deployment"],
            "commit_patterns": [
                r"^feat\(ci\):",
                r"^fix\(ci\):",
                r"^pipeline:",
                r"^deploy:",
                r"^infra:"
            ],
            "tracked_files": [
                "*.yaml",
                "*.tf",
                "Dockerfile*",
                "scripts/*.sh"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Machine Learning Service": {
            "description": "ML Model Training and Inference Service",
            "categories": DEFAULT_CATEGORIES + ["Model Training", "Data Pipeline", "Model Deployment"],
            "commit_patterns": [
                r"^feat\(ml\):",
                r"^fix\(ml\):",
                r"^model:",
                r"^train:",
                r"^data:"
            ],
            "tracked_files": [
                "ml/*.py",
                "models/*.h5",
                "data/*.csv",
                "config/ml*.yaml"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Message Queue Service": {
            "description": "Message Queue and Event Processing System",
            "categories": DEFAULT_CATEGORIES + ["Queue Management", "Event Processing", "Message Routing"],
            "commit_patterns": [
                r"^feat\(queue\):",
                r"^fix\(queue\):",
                r"^event:",
                r"^route:",
                r"^process:"
            ],
            "tracked_files": [
                "queue/*.py",
                "events/*.py",
                "processors/*.py",
                "config/queue*.yaml"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        },
        "Cache Service": {
            "description": "Distributed Caching and State Management",
            "categories": DEFAULT_CATEGORIES + ["Cache Strategy", "State Management", "Invalidation"],
            "commit_patterns": [
                r"^feat\(cache\):",
                r"^fix\(cache\):",
                r"^state:",
                r"^invalidate:",
                r"^store:"
            ],
            "tracked_files": [
                "cache/*.py",
                "state/*.py",
                "store/*.py",
                "config/cache*.yaml"
            ],
            "improvement_id_format": "%Y-%m-%d-%03d",
            "version_pattern": r"\d+\.\d+\.\d+",
            "auto_categorize": True,
            "auto_assign_severity": True
        }
    }
    
    @classmethod
    def get_template(cls, template_name: str) -> Optional[Dict]:
        """Get a project template by name."""
        return cls.DEFAULT_TEMPLATES.get(template_name)
    
    @classmethod
    def list_templates(cls) -> List[str]:
        """List all available template names."""
        return list(cls.DEFAULT_TEMPLATES.keys())
    
    @classmethod
    def get_categories(cls, template_name: Optional[str] = None) -> List[str]:
        """Get categories for a template, or default categories if no template specified."""
        if template_name and template_name in cls.DEFAULT_TEMPLATES:
            return cls.DEFAULT_TEMPLATES[template_name]["categories"]
        return cls.DEFAULT_CATEGORIES
    
    @classmethod
    def create_from_template(cls, template_name: str, project_name: str, 
                           repository_url: Optional[str] = None) -> Dict:
        """Create a new project configuration from a template."""
        template = cls.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        return {
            "name": project_name,
            "description": template["description"],
            "repository_url": repository_url,
            "categories": template["categories"],
            "commit_patterns": template["commit_patterns"],
            "tracked_files": template["tracked_files"],
            "improvement_id_format": template["improvement_id_format"],
            "version_pattern": template["version_pattern"],
            "auto_categorize": template.get("auto_categorize", True),
            "auto_assign_severity": template.get("auto_assign_severity", True)
        }

    @classmethod
    def detect_severity(cls, message: str) -> str:
        """Detect severity level from commit message."""
        message = message.lower()
        for severity, patterns in cls.SEVERITY_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, message):
                    return severity
        return "medium"  # Default severity

    @classmethod
    def suggest_category(cls, message: str, files: List[str]) -> str:
        """Suggest a category based on commit message and affected files."""
        message = message.lower()
        
        # Define category patterns
        patterns = {
            "Security": [r"security", r"auth", r"permission", r"vulnerability"],
            "Performance Optimization": [r"performance", r"optimize", r"speed", r"memory"],
            "Error Handling": [r"error", r"exception", r"catch", r"handle"],
            "Configuration Management": [r"config", r"setting", r"env", r"parameter"],
            "Frontend Architecture": [r"ui", r"component", r"style", r"react", r"vue"],
            "Communication Systems": [r"api", r"websocket", r"http", r"protocol"],
            "Database Optimization": [r"database", r"query", r"schema", r"sql"],
            "Infrastructure": [r"docker", r"kubernetes", r"deploy", r"pipeline"],
            "Machine Learning": [r"model", r"train", r"predict", r"inference"],
            "Message Queue": [r"queue", r"event", r"message", r"broker"],
            "Caching": [r"cache", r"redis", r"memcached", r"state"],
            "Testing": [r"test", r"spec", r"coverage", r"mock"]
        }
        
        # Check message against patterns
        for category, category_patterns in patterns.items():
            for pattern in category_patterns:
                if re.search(pattern, message):
                    return category
        
        # Check files if no category found from message
        file_patterns = {
            "Frontend Architecture": [r"\.tsx?$", r"\.jsx?$", r"\.css$", r"\.scss$"],
            "Configuration Management": [r"\.yaml$", r"\.json$", r"\.conf$"],
            "Database Optimization": [r"\.sql$", r"migrations/"],
            "Infrastructure": [r"Dockerfile", r"\.tf$", r"\.yaml$"],
            "Machine Learning": [r"\.h5$", r"\.pkl$", r"model", r"train"],
            "Message Queue": [r"queue", r"event", r"processor"],
            "Caching": [r"cache", r"state", r"store"]
        }
        
        for file in files:
            for category, patterns in file_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, file):
                        return category
        
        return "General"  # Default category

class CommitTracker:
    def __init__(self, project_config: Dict):
        self.config = project_config
        
    def parse_commit_message(self, message: str, files: List[str] = None) -> Dict:
        """Parse a commit message to extract improvement details."""
        # Previous implementation...
        result = {
            "type": None,
            "version": None,
            "affected_files": [],
            "severity": None,
            "category": None
        }
        
        # Extract basic info
        for pattern in self.config["commit_patterns"]:
            if re.match(pattern, message):
                result["type"] = pattern.strip("^").split("(")[0]
                break
        
        # Extract version
        version_match = re.search(self.config["version_pattern"], message)
        if version_match:
            result["version"] = version_match.group(0)
        
        # Get affected files
        if files:
            result["affected_files"] = [
                f for f in files
                if any(self._matches_glob(f, pattern) for pattern in self.config["tracked_files"])
            ]
        
        # Auto-assign severity and category if enabled
        if self.config.get("auto_assign_severity"):
            result["severity"] = ProjectTemplate.detect_severity(message)
        
        if self.config.get("auto_categorize"):
            result["category"] = ProjectTemplate.suggest_category(message, files or [])
        
        return result
    
    def _matches_glob(self, filename: str, pattern: str) -> bool:
        """Check if a filename matches a glob pattern."""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)
    
    def generate_improvement_id(self) -> str:
        """Generate a new improvement ID based on the configured format."""
        return datetime.now().strftime(self.config["improvement_id_format"])
