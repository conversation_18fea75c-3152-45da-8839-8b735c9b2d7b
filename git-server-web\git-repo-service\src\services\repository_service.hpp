#pragma once
#include "../models/repository.hpp"
#include <string>
#include <vector>
#include <memory>
#include <git2.h>

class RepositoryService {
public:
    RepositoryService(const std::string& basePath);
    
    // API endpoint handlers
    std::string getAllRepositories();
    std::string getCommitHistory(const std::string& repoName, int days = 30);
    std::string getRepositoryDetails(const std::string& repoName);
    
private:
    std::string basePath_;
    
    // Repository scanning and processing
    Repository scanRepository(const std::string& path);
    uint64_t calculateDirectorySize(const std::string& path);
    std::vector<std::string> getBranches(const std::string& path);
    std::vector<std::string> getBranches(git_repository* repo);
    std::string getLastCommitDate(const std::string& path);
    
    // Health calculation
    double calculateHealth(const Repository& repo);
    std::pair<std::string, std::string> getHealthStatus(double health);
    
    // Utility functions
    std::string formatSize(uint64_t sizeInBytes);
    uint64_t parseSize(const std::string& sizeStr);
};
