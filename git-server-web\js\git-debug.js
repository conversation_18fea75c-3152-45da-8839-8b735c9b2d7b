/**
 * Git Functionality Debug
 * This script specifically debugs the Git Repository components
 */

const GitDebug = {
    init() {
        console.log('Initializing Git Component Debug...');

        // Create the debug card only if it doesn't exist already
        if (!document.getElementById('git-debug-card')) {
            this.createDebugCard();
        }

        // Add event listener for Run Diagnostics button (always do this to ensure it works)
        const runButton = document.getElementById('run-git-diagnostics');
        if (runButton) {
            // Remove any existing event listeners
            runButton.replaceWith(runButton.cloneNode(true));

            // Get the fresh button and add the event listener
            const freshButton = document.getElementById('run-git-diagnostics');
            if (freshButton) {
                console.log('Adding event listener to Run Diagnostics button');
                freshButton.addEventListener('click', () => {
                    console.log('Run Diagnostics button clicked');
                    this.run();
                });
            }
        }
    },

    createDebugCard() {
        const container = document.getElementById('troubleshooting-container');
        if (!container) {
            console.error('Troubleshooting container not found');
            return;
        }

        // Create a column for the Git debug card
        const column = document.createElement('div');
        column.className = 'col-md-6'; // Removed mb-4 to fix spacing
        column.id = 'git-debug-card';

        // Create the card
        column.innerHTML = `
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-code-branch me-2"></i>Git Component Diagnostics
                </div>
                <div class="card-body overflow-auto">
                    <button id="run-git-diagnostics" class="btn btn-primary mb-3">
                        <i class="fas fa-play me-2"></i>Run Diagnostics
                    </button>

                    <div id="git-debug-output" class="mt-3">
                        <div class="alert alert-info">Click the button above to run Git component diagnostics.</div>
                    </div>

                    <div id="module-check" class="debug-section mb-3"></div>
                    <div id="api-test" class="debug-section mb-3"></div>
                    <div id="init-check" class="debug-section mb-3"></div>
                    <div id="permission-check" class="debug-section"></div>
                </div>
            </div>
        `;

        // Add to container
        container.appendChild(column);
    },

    run() {
        console.log('=== Git Component Debug Started ===');

        // Show running status
        const output = document.getElementById('git-debug-output');
        if (output) {
            output.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>Running diagnostics...</span>
                </div>
            `;
        }

        // Check if all modules are defined
        this.checkModuleDefinitions();

        // Try to call API directly
        this.testGitAPI();

        // Check initialization sequence
        this.checkInitializationSequence();

        // Test Git permissions - Check if the endpoint exists first
        this.testGitAPIEndpointExists('/api/git/repositories')
            .then(exists => {
                if (exists) {
                    this.testGitPermissions();
                } else {
                    this.updateSection(
                        'permission-check',
                        `<h5>Git Permissions Test</h5>
                        <div class="alert alert-warning">Skipped - Git API not available</div>`
                    );
                }
            });

        // Update output status
        if (output) {
            output.innerHTML = `<div class="alert alert-success">Diagnostics completed. See results below.</div>`;
        }
    },

    async testGitAPIEndpointExists(endpoint) {
        try {
            const response = await fetch(endpoint, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    },

    checkModuleDefinitions() {
        console.log('\n--- Module Definition Check ---');
        const modules = [
            'GitUtils',
            'GitCommitHistoryChart',
            'GitSizeComparisonChart',
            'GitRepositoryList',
            'GitRepositoryModal',
            'GitRepositoryManager'
        ];

        let allDefined = true;
        let results = '<h5>Module Definition Check</h5><ul>';

        modules.forEach(module => {
            const isDefined = typeof window[module] !== 'undefined';
            console.log(`${module}: ${isDefined ? '✓ Defined' : '✗ MISSING'}`);
            results += `<li>${module}: <span class="${isDefined ? 'text-success' : 'text-danger'}">${isDefined ? '✓ Defined' : '✗ MISSING'}</span></li>`;
            if (!isDefined) allDefined = false;

            // If GitCommitHistoryChart is missing, try to fix it
            if (module === 'GitCommitHistoryChart' && !isDefined) {
                console.warn('Attempting to fix missing GitCommitHistoryChart module');
                try {
                    // Create a minimal implementation
                    window.GitCommitHistoryChart = {
                        config: {
                            chartId: 'commit-history-chart',
                            containerId: 'commit-history-container',
                            loadingId: 'commit-chart-loading',
                            errorId: 'commit-chart-error',
                            messageId: 'commit-chart-message',
                            days: 30,
                            colors: {
                                background: 'rgba(54, 162, 235, 0.5)',
                                border: 'rgba(54, 162, 235, 1)'
                            }
                        },
                        state: { chart: null },
                        init() {
                            console.log('GitCommitHistoryChart.init() - Debug initialization');
                            return this;
                        },
                        initChart() {}
                    };
                    console.log('Created emergency GitCommitHistoryChart implementation');
                    results += `<li class="text-warning">Created emergency GitCommitHistoryChart implementation</li>`;
                } catch (error) {
                    console.error('Failed to create emergency implementation:', error);
                }
            }
        });

        results += '</ul>';

        if (!allDefined) {
            console.error('Some Git modules are missing! Check script loading order and paths.');
            results += '<div class="alert alert-danger">Some Git modules are missing! Check script loading order and paths.</div>';
        } else {
            results += '<div class="alert alert-success">All Git modules are properly defined.</div>';
        }

        this.updateSection('module-check', results);
    },

    async testGitAPI() {
        console.log('\n--- Git API Test ---');
        let results = '<h5>Git API Test</h5>';

        try {
            console.log('Fetching repositories data...');
            results += '<p>Testing /api/git/repositories...</p>';

            const response = await fetch('/api/git/repositories');

            if (!response.ok) {
                throw new Error(`API returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            console.log('API Response:', data);

            if (data.status === 'success') {
                console.log(`✓ API returned ${data.repositories.length} repositories`);
                results += `<div class="alert alert-success">✓ API returned ${data.repositories.length} repositories</div>`;
                results += '<div class="card mb-3"><div class="card-header">Response Data</div><div class="card-body"><pre class="mb-0" style="max-height: 200px; overflow-y: auto;">' +
                          JSON.stringify(data, null, 2) + '</pre></div></div>';

                // Try to fetch commit history for the first repository
                if (data.repositories.length > 0) {
                    const firstRepo = data.repositories[0].name;
                    console.log(`Testing commit history for ${firstRepo}...`);
                    results += `<p>Testing commit history for ${firstRepo}...</p>`;

                    const historyResponse = await fetch(`/api/git/repository/${firstRepo}/commits?days=30`);
                    const historyData = await historyResponse.json();

                    console.log('Commit History Response:', historyData);
                    results += '<div class="card mb-3"><div class="card-header">Commit History Response</div><div class="card-body"><pre class="mb-0" style="max-height: 200px; overflow-y: auto;">' +
                              JSON.stringify(historyData, null, 2) + '</pre></div></div>';
                }
            } else {
                console.error('✗ API returned an error:', data);
                results += `<div class="alert alert-danger">✗ API returned an error</div>`;
                results += '<div class="card mb-3"><div class="card-header">Error Response</div><div class="card-body"><pre class="mb-0" style="max-height: 200px; overflow-y: auto;">' +
                          JSON.stringify(data, null, 2) + '</pre></div></div>';
            }
        } catch (error) {
            console.error('✗ Error testing Git API:', error);
            results += `<div class="alert alert-danger">✗ Error testing Git API: ${error.message}</div>`;
        }

        this.updateSection('api-test', results);
    },


	checkInitializationSequence() {
        console.log('\n--- Initialization Sequence Check ---');
        let results = '<h5>Initialization Sequence Check</h5>';

        if (typeof GitRepositoryManager === 'undefined') {
            console.error('GitRepositoryManager not defined, cannot check initialization');
            results += '<div class="alert alert-danger">GitRepositoryManager not defined, cannot check initialization</div>';
            this.updateSection('init-check', results);
            return;
        }

        console.log('GitRepositoryManager state:', GitRepositoryManager.state);
        results += '<p>Current GitRepositoryManager state:</p>';
        results += '<div class="card mb-3"><div class="card-header">State Object</div><div class="card-body"><pre class="mb-0" style="max-height: 200px; overflow-y: auto;">' +
                  JSON.stringify(GitRepositoryManager.state, null, 2) + '</pre></div></div>';

        // Try to manually initialize
        console.log('Attempting manual initialization...');
        results += '<p>Attempting manual initialization...</p>';
        try {
            GitRepositoryManager.init();
            console.log('Manual initialization completed');
            results += '<div class="alert alert-success">Manual initialization completed successfully</div>';
        } catch (error) {
            console.error('Error during manual initialization:', error);
            results += `<div class="alert alert-danger">Error during manual initialization: ${error.message}</div>`;
        }

        this.updateSection('init-check', results);
    },

    async testGitPermissions() {
        console.log('\n--- Git Permissions Test ---');
        let results = '<h5>Git Permissions Test</h5>';

        try {
            console.log('Checking Git permissions...');
            results += '<p>Checking Git permissions...</p>';

            // Try to fetch repositories as this is a good permission test,
            // even without a dedicated permissions endpoint
            const response = await fetch('/api/git/repositories');

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Repositories Response (for permission check):', data);

            // If we successfully got repositories, then permissions are likely good
            if (data.status === 'success' && data.repositories) {
                results += `<div class="alert alert-success">✓ Git permissions appear to be working - successfully fetched repositories</div>`;
                results += '<div class="card mb-3"><div class="card-header">Repositories Data</div><div class="card-body"><pre class="mb-0" style="max-height: 200px; overflow-y: auto;">' +
                          JSON.stringify(data.repositories.slice(0, 3), null, 2) + '</pre></div></div>';
            } else {
                console.error('✗ Git permissions may be an issue - repositories endpoint returned unexpected format:', data);
                results += `<div class="alert alert-warning">⚠️ Git permissions may be an issue - repository endpoint returned unexpected format</div>`;
                results += '<div class="card mb-3"><div class="card-header">Response</div><div class="card-body"><pre class="mb-0" style="max-height: 200px; overflow-y: auto;">' +
                          JSON.stringify(data, null, 2) + '</pre></div></div>';
            }
        } catch (error) {
            console.error('✗ Error testing Git permissions:', error);
            results += `<div class="alert alert-danger">✗ Git permissions error: ${error.message}</div>`;
            results += `<p>The app might not have sufficient permissions to access Git repositories. Check:</p>
                       <ul>
                         <li>sudoers configuration allows www-data user access to git commands</li>
                         <li>Git repositories path is accessible to the application</li>
                         <li>Backend flask app has correct file paths configured</li>
                       </ul>`;
        }

        this.updateSection('permission-check', results);
    },

    updateSection(sectionId, content) {
        const sectionElement = document.getElementById(sectionId);
        if (sectionElement) {
            sectionElement.innerHTML = content;
        }
    },
};

// Run debug when the page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('Git Debug script loaded');
    setTimeout(() => {
        // Check if we're on the right tab
        const troubleshootingContainer = document.getElementById('troubleshooting-container');
        if (troubleshootingContainer) {
            GitDebug.init();
        }
    }, 1000); // Wait 1 second for everything to load
});
