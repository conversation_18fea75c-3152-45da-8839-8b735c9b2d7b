cmake_minimum_required(VERSION 3.20)

# Find GTest
find_package(GTest QUIET)
if(NOT GTest_FOUND)
    message(STATUS "GTest not found, tests will not be built")
    return()
endif()

# Add test executable
add_executable(database-service-tests
    main.cpp
    connection_test.cpp
    schema_manager_test.cpp
    security_manager_test.cpp
    api_server_test.cpp
)

# Link libraries
target_link_libraries(database-service-tests
    PRIVATE
        GTest::GTest
        GTest::Main
        ${Boost_LIBRARIES}
        ${PostgreSQL_LIBRARIES}
        ${OPENSSL_LIBRARIES}
        ${CURL_LIBRARIES}
        Threads::Threads
)

# Add pqxx library
if(pqxx_FOUND)
    target_link_libraries(database-service-tests PRIVATE pqxx::pqxx)
else()
    target_link_libraries(database-service-tests PRIVATE pqxx)
endif()

# Add tests
add_test(NAME database-service-tests COMMAND database-service-tests)
