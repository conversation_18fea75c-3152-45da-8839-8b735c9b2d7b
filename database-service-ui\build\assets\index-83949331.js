function e(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var n={exports:{}},r={},a={exports:{}},l={},i=Symbol.for("react.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),g=Symbol.iterator;var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},b=Object.assign,x={};function w(e,t,n){this.props=e,this.context=t,this.refs=x,this.updater=n||y}function k(){}function S(e,t,n){this.props=e,this.context=t,this.refs=x,this.updater=n||y}w.prototype.isReactComponent={},w.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},w.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},k.prototype=w.prototype;var E=S.prototype=new k;E.constructor=S,b(E,w.prototype),E.isPureReactComponent=!0;var j=Array.isArray,C=Object.prototype.hasOwnProperty,N={current:null},_={key:!0,ref:!0,__self:!0,__source:!0};function P(e,t,n){var r,a={},l=null,o=null;if(null!=t)for(r in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(l=""+t.key),t)C.call(t,r)&&!_.hasOwnProperty(r)&&(a[r]=t[r]);var s=arguments.length-2;if(1===s)a.children=n;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];a.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===a[r]&&(a[r]=s[r]);return{$$typeof:i,type:e,key:l,ref:o,props:a,_owner:N.current}}function T(e){return"object"==typeof e&&null!==e&&e.$$typeof===i}var L=/\/+/g;function z(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function R(e,t,n,r,a){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case i:case o:s=!0}}if(s)return a=a(s=e),e=""===r?"."+z(s,0):r,j(a)?(n="",null!=e&&(n=e.replace(L,"$&/")+"/"),R(a,t,n,"",(function(e){return e}))):null!=a&&(T(a)&&(a=function(e,t){return{$$typeof:i,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,n+(!a.key||s&&s.key===a.key?"":(""+a.key).replace(L,"$&/")+"/")+e)),t.push(a)),1;if(s=0,r=""===r?".":r+":",j(e))for(var u=0;u<e.length;u++){var c=r+z(l=e[u],u);s+=R(l,t,n,c,a)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=g&&e[g]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),u=0;!(l=e.next()).done;)s+=R(l=l.value,t,n,c=r+z(l,u++),a);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function O(e,t,n){if(null==e)return e;var r=[],a=0;return R(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function D(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var F={current:null},M={transition:null},I={ReactCurrentDispatcher:F,ReactCurrentBatchConfig:M,ReactCurrentOwner:N};function U(){throw Error("act(...) is not supported in production builds of React.")}l.Children={map:O,forEach:function(e,t,n){O(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return O(e,(function(){t++})),t},toArray:function(e){return O(e,(function(e){return e}))||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},l.Component=w,l.Fragment=s,l.Profiler=c,l.PureComponent=S,l.StrictMode=u,l.Suspense=h,l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,l.act=U,l.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=b({},e.props),a=e.key,l=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,o=N.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)C.call(t,u)&&!_.hasOwnProperty(u)&&(r[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)r.children=n;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];r.children=s}return{$$typeof:i,type:e.type,key:a,ref:l,props:r,_owner:o}},l.createContext=function(e){return(e={$$typeof:f,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:d,_context:e},e.Consumer=e},l.createElement=P,l.createFactory=function(e){var t=P.bind(null,e);return t.type=e,t},l.createRef=function(){return{current:null}},l.forwardRef=function(e){return{$$typeof:p,render:e}},l.isValidElement=T,l.lazy=function(e){return{$$typeof:v,_payload:{_status:-1,_result:e},_init:D}},l.memo=function(e,t){return{$$typeof:m,type:e,compare:void 0===t?null:t}},l.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},l.unstable_act=U,l.useCallback=function(e,t){return F.current.useCallback(e,t)},l.useContext=function(e){return F.current.useContext(e)},l.useDebugValue=function(){},l.useDeferredValue=function(e){return F.current.useDeferredValue(e)},l.useEffect=function(e,t){return F.current.useEffect(e,t)},l.useId=function(){return F.current.useId()},l.useImperativeHandle=function(e,t,n){return F.current.useImperativeHandle(e,t,n)},l.useInsertionEffect=function(e,t){return F.current.useInsertionEffect(e,t)},l.useLayoutEffect=function(e,t){return F.current.useLayoutEffect(e,t)},l.useMemo=function(e,t){return F.current.useMemo(e,t)},l.useReducer=function(e,t,n){return F.current.useReducer(e,t,n)},l.useRef=function(e){return F.current.useRef(e)},l.useState=function(e){return F.current.useState(e)},l.useSyncExternalStore=function(e,t,n){return F.current.useSyncExternalStore(e,t,n)},l.useTransition=function(){return F.current.useTransition()},l.version="18.3.1",a.exports=l;var A=a.exports;const $=t(A),B=e({__proto__:null,default:$},[A]);
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var V=A,W=Symbol.for("react.element"),H=Symbol.for("react.fragment"),Q=Object.prototype.hasOwnProperty,q=V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,K={key:!0,ref:!0,__self:!0,__source:!0};function Y(e,t,n){var r,a={},l=null,i=null;for(r in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(i=t.ref),t)Q.call(t,r)&&!K.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:W,type:e,key:l,ref:i,props:a,_owner:q.current}}r.Fragment=H,r.jsx=Y,r.jsxs=Y,n.exports=r;var G=n.exports,X={},J={exports:{}},Z={},ee={exports:{}},te={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,l=e[r];if(!(0<a(l,t)))break e;e[r]=t,e[n]=l,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,l=e.length,i=l>>>1;r<i;){var o=2*(r+1)-1,s=e[o],u=o+1,c=e[u];if(0>a(s,n))u<l&&0>a(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[o]=n,r=o);else{if(!(u<l&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var l=performance;e.unstable_now=function(){return l.now()}}else{var i=Date,o=i.now();e.unstable_now=function(){return i.now()-o}}var s=[],u=[],c=1,d=null,f=3,p=!1,h=!1,m=!1,v="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var a=n(u);null!==a;){if(null===a.callback)r(u);else{if(!(a.startTime<=e))break;r(u),a.sortIndex=a.expirationTime,t(s,a)}a=n(u)}}function x(e){if(m=!1,b(e),!h)if(null!==n(s))h=!0,z(w);else{var t=n(u);null!==t&&R(x,t.startTime-e)}}function w(t,a){h=!1,m&&(m=!1,g(j),j=-1),p=!0;var l=f;try{for(b(a),d=n(s);null!==d&&(!(d.expirationTime>a)||t&&!_());){var i=d.callback;if("function"==typeof i){d.callback=null,f=d.priorityLevel;var o=i(d.expirationTime<=a);a=e.unstable_now(),"function"==typeof o?d.callback=o:d===n(s)&&r(s),b(a)}else r(s);d=n(s)}if(null!==d)var c=!0;else{var v=n(u);null!==v&&R(x,v.startTime-a),c=!1}return c}finally{d=null,f=l,p=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,S=!1,E=null,j=-1,C=5,N=-1;function _(){return!(e.unstable_now()-N<C)}function P(){if(null!==E){var t=e.unstable_now();N=t;var n=!0;try{n=E(!0,t)}finally{n?k():(S=!1,E=null)}}else S=!1}if("function"==typeof y)k=function(){y(P)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,L=T.port2;T.port1.onmessage=P,k=function(){L.postMessage(null)}}else k=function(){v(P,0)};function z(e){E=e,S||(S=!0,k())}function R(t,n){j=v((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){h||p||(h=!0,z(w))},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,a,l){var i=e.unstable_now();switch("object"==typeof l&&null!==l?l="number"==typeof(l=l.delay)&&0<l?i+l:i:l=i,r){case 1:var o=-1;break;case 2:o=250;break;case 5:o=**********;break;case 4:o=1e4;break;default:o=5e3}return r={id:c++,callback:a,priorityLevel:r,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>i?(r.sortIndex=l,t(u,r),null===n(s)&&r===n(u)&&(m?(g(j),j=-1):m=!0,R(x,l-i))):(r.sortIndex=o,t(s,r),h||p||(h=!0,z(w))),r},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(te),ee.exports=te;var ne=ee.exports,re=A,ae=ne;
/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function le(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ie=new Set,oe={};function se(e,t){ue(e,t),ue(e+"Capture",t)}function ue(e,t){for(oe[e]=t,e=0;e<t.length;e++)ie.add(t[e])}var ce=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),de=Object.prototype.hasOwnProperty,fe=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,pe={},he={};function me(e,t,n,r,a,l,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=i}var ve={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){ve[e]=new me(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];ve[t]=new me(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){ve[e]=new me(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){ve[e]=new me(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){ve[e]=new me(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){ve[e]=new me(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){ve[e]=new me(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){ve[e]=new me(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){ve[e]=new me(e,5,!1,e.toLowerCase(),null,!1,!1)}));var ge=/[\-:]([a-z])/g;function ye(e){return e[1].toUpperCase()}function be(e,t,n,r){var a=ve.hasOwnProperty(t)?ve[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!de.call(he,e)||!de.call(pe,e)&&(fe.test(e)?he[e]=!0:(pe[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(ge,ye);ve[t]=new me(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(ge,ye);ve[t]=new me(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(ge,ye);ve[t]=new me(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){ve[e]=new me(e,1,!1,e.toLowerCase(),null,!1,!1)})),ve.xlinkHref=new me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){ve[e]=new me(e,1,!1,e.toLowerCase(),null,!0,!0)}));var xe=re.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,we=Symbol.for("react.element"),ke=Symbol.for("react.portal"),Se=Symbol.for("react.fragment"),Ee=Symbol.for("react.strict_mode"),je=Symbol.for("react.profiler"),Ce=Symbol.for("react.provider"),Ne=Symbol.for("react.context"),_e=Symbol.for("react.forward_ref"),Pe=Symbol.for("react.suspense"),Te=Symbol.for("react.suspense_list"),Le=Symbol.for("react.memo"),ze=Symbol.for("react.lazy"),Re=Symbol.for("react.offscreen"),Oe=Symbol.iterator;function De(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=Oe&&e[Oe]||e["@@iterator"])?e:null}var Fe,Me=Object.assign;function Ie(e){if(void 0===Fe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Fe=t&&t[1]||""}return"\n"+Fe+e}var Ue=!1;function Ae(e,t){if(!e||Ue)return"";Ue=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"==typeof u.stack){for(var a=u.stack.split("\n"),l=r.stack.split("\n"),i=a.length-1,o=l.length-1;1<=i&&0<=o&&a[i]!==l[o];)o--;for(;1<=i&&0<=o;i--,o--)if(a[i]!==l[o]){if(1!==i||1!==o)do{if(i--,0>--o||a[i]!==l[o]){var s="\n"+a[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=o);break}}}finally{Ue=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ie(e):""}function $e(e){switch(e.tag){case 5:return Ie(e.type);case 16:return Ie("Lazy");case 13:return Ie("Suspense");case 19:return Ie("SuspenseList");case 0:case 2:case 15:return e=Ae(e.type,!1);case 11:return e=Ae(e.type.render,!1);case 1:return e=Ae(e.type,!0);default:return""}}function Be(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case Se:return"Fragment";case ke:return"Portal";case je:return"Profiler";case Ee:return"StrictMode";case Pe:return"Suspense";case Te:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Ne:return(e.displayName||"Context")+".Consumer";case Ce:return(e._context.displayName||"Context")+".Provider";case _e:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case Le:return null!==(t=e.displayName||null)?t:Be(e.type)||"Memo";case ze:t=e._payload,e=e._init;try{return Be(e(t))}catch(n){}}return null}function Ve(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Be(t);case 8:return t===Ee?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function We(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function He(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Qe(e){e._valueTracker||(e._valueTracker=function(e){var t=He(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function qe(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=He(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Ke(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ye(e,t){var n=t.checked;return Me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Ge(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=We(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Xe(e,t){null!=(t=t.checked)&&be(e,"checked",t,!1)}function Je(e,t){Xe(e,t);var n=We(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?et(e,t.type,n):t.hasOwnProperty("defaultValue")&&et(e,t.type,We(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Ze(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function et(e,t,n){"number"===t&&Ke(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var tt=Array.isArray;function nt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+We(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function rt(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(le(91));return Me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function at(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(le(92));if(tt(n)){if(1<n.length)throw Error(le(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:We(n)}}function lt(e,t){var n=We(t.value),r=We(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function it(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ot(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function st(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ot(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ut,ct,dt=(ct=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ut=ut||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ut.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ct(e,t)}))}:ct);function ft(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ht=["Webkit","ms","Moz","O"];function mt(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pt.hasOwnProperty(e)&&pt[e]?(""+t).trim():t+"px"}function vt(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=mt(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pt).forEach((function(e){ht.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pt[t]=pt[e]}))}));var gt=Me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yt(e,t){if(t){if(gt[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(le(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(le(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(le(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(le(62))}}function bt(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xt=null;function wt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var kt=null,St=null,Et=null;function jt(e){if(e=bl(e)){if("function"!=typeof kt)throw Error(le(280));var t=e.stateNode;t&&(t=wl(t),kt(e.stateNode,e.type,t))}}function Ct(e){St?Et?Et.push(e):Et=[e]:St=e}function Nt(){if(St){var e=St,t=Et;if(Et=St=null,jt(e),t)for(e=0;e<t.length;e++)jt(t[e])}}function _t(e,t){return e(t)}function Pt(){}var Tt=!1;function Lt(e,t,n){if(Tt)return e(t,n);Tt=!0;try{return _t(e,t,n)}finally{Tt=!1,(null!==St||null!==Et)&&(Pt(),Nt())}}function zt(e,t){var n=e.stateNode;if(null===n)return null;var r=wl(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(le(231,t,typeof n));return n}var Rt=!1;if(ce)try{var Ot={};Object.defineProperty(Ot,"passive",{get:function(){Rt=!0}}),window.addEventListener("test",Ot,Ot),window.removeEventListener("test",Ot,Ot)}catch(ct){Rt=!1}function Dt(e,t,n,r,a,l,i,o,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ft=!1,Mt=null,It=!1,Ut=null,At={onError:function(e){Ft=!0,Mt=e}};function $t(e,t,n,r,a,l,i,o,s){Ft=!1,Mt=null,Dt.apply(At,arguments)}function Bt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Vt(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Wt(e){if(Bt(e)!==e)throw Error(le(188))}function Ht(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Bt(e)))throw Error(le(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return Wt(a),e;if(l===r)return Wt(a),t;l=l.sibling}throw Error(le(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,o=a.child;o;){if(o===n){i=!0,n=a,r=l;break}if(o===r){i=!0,r=a,n=l;break}o=o.sibling}if(!i){for(o=l.child;o;){if(o===n){i=!0,n=l,r=a;break}if(o===r){i=!0,r=l,n=a;break}o=o.sibling}if(!i)throw Error(le(189))}}if(n.alternate!==r)throw Error(le(190))}if(3!==n.tag)throw Error(le(188));return n.stateNode.current===n?e:t}(e))?Qt(e):null}function Qt(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qt(e);if(null!==t)return t;e=e.sibling}return null}var qt=ae.unstable_scheduleCallback,Kt=ae.unstable_cancelCallback,Yt=ae.unstable_shouldYield,Gt=ae.unstable_requestPaint,Xt=ae.unstable_now,Jt=ae.unstable_getCurrentPriorityLevel,Zt=ae.unstable_ImmediatePriority,en=ae.unstable_UserBlockingPriority,tn=ae.unstable_NormalPriority,nn=ae.unstable_LowPriority,rn=ae.unstable_IdlePriority,an=null,ln=null;var on=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(sn(e)/un|0)|0},sn=Math.log,un=Math.LN2;var cn=64,dn=4194304;function fn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pn(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,i=268435455&n;if(0!==i){var o=i&~a;0!==o?r=fn(o):0!==(l&=i)&&(r=fn(l))}else 0!==(i=n&~a)?r=fn(i):0!==l&&(r=fn(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&4194240&l))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-on(t)),r|=e[n],t&=~a;return r}function hn(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mn(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function vn(){var e=cn;return!(4194240&(cn<<=1))&&(cn=64),e}function gn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yn(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-on(t)]=n}function bn(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-on(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var xn=0;function wn(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var kn,Sn,En,jn,Cn,Nn=!1,_n=[],Pn=null,Tn=null,Ln=null,zn=new Map,Rn=new Map,On=[],Dn="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fn(e,t){switch(e){case"focusin":case"focusout":Pn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Ln=null;break;case"pointerover":case"pointerout":zn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rn.delete(t.pointerId)}}function Mn(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=bl(t))&&Sn(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function In(e){var t=yl(e.target);if(null!==t){var n=Bt(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Vt(n)))return e.blockedOn=t,void Cn(e.priority,(function(){En(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Un(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gn(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bl(n))&&Sn(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xt=r,n.target.dispatchEvent(r),xt=null,t.shift()}return!0}function An(e,t,n){Un(e)&&n.delete(t)}function $n(){Nn=!1,null!==Pn&&Un(Pn)&&(Pn=null),null!==Tn&&Un(Tn)&&(Tn=null),null!==Ln&&Un(Ln)&&(Ln=null),zn.forEach(An),Rn.forEach(An)}function Bn(e,t){e.blockedOn===t&&(e.blockedOn=null,Nn||(Nn=!0,ae.unstable_scheduleCallback(ae.unstable_NormalPriority,$n)))}function Vn(e){function t(t){return Bn(t,e)}if(0<_n.length){Bn(_n[0],e);for(var n=1;n<_n.length;n++){var r=_n[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pn&&Bn(Pn,e),null!==Tn&&Bn(Tn,e),null!==Ln&&Bn(Ln,e),zn.forEach(t),Rn.forEach(t),n=0;n<On.length;n++)(r=On[n]).blockedOn===e&&(r.blockedOn=null);for(;0<On.length&&null===(n=On[0]).blockedOn;)In(n),null===n.blockedOn&&On.shift()}var Wn=xe.ReactCurrentBatchConfig,Hn=!0;function Qn(e,t,n,r){var a=xn,l=Wn.transition;Wn.transition=null;try{xn=1,Kn(e,t,n,r)}finally{xn=a,Wn.transition=l}}function qn(e,t,n,r){var a=xn,l=Wn.transition;Wn.transition=null;try{xn=4,Kn(e,t,n,r)}finally{xn=a,Wn.transition=l}}function Kn(e,t,n,r){if(Hn){var a=Gn(e,t,n,r);if(null===a)Wa(e,t,r,Yn,n),Fn(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Pn=Mn(Pn,e,t,n,r,a),!0;case"dragenter":return Tn=Mn(Tn,e,t,n,r,a),!0;case"mouseover":return Ln=Mn(Ln,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return zn.set(l,Mn(zn.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Rn.set(l,Mn(Rn.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Fn(e,r),4&t&&-1<Dn.indexOf(e)){for(;null!==a;){var l=bl(a);if(null!==l&&kn(l),null===(l=Gn(e,t,n,r))&&Wa(e,t,r,Yn,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Wa(e,t,r,null,n)}}var Yn=null;function Gn(e,t,n,r){if(Yn=null,null!==(e=yl(e=wt(r))))if(null===(t=Bt(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Vt(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Yn=e,null}function Xn(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Jt()){case Zt:return 1;case en:return 4;case tn:case nn:return 16;case rn:return 536870912;default:return 16}default:return 16}}var Jn=null,Zn=null,er=null;function tr(){if(er)return er;var e,t,n=Zn,r=n.length,a="value"in Jn?Jn.value:Jn.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[l-t];t++);return er=a.slice(e,1<t?1-t:void 0)}function nr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function rr(){return!0}function ar(){return!1}function lr(e){function t(t,n,r,a,l){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?rr:ar,this.isPropagationStopped=ar,this}return Me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=rr)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=rr)},persist:function(){},isPersistent:rr}),t}var ir,or,sr,ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cr=lr(ur),dr=Me({},ur,{view:0,detail:0}),fr=lr(dr),pr=Me({},dr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jr,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sr&&(sr&&"mousemove"===e.type?(ir=e.screenX-sr.screenX,or=e.screenY-sr.screenY):or=ir=0,sr=e),ir)},movementY:function(e){return"movementY"in e?e.movementY:or}}),hr=lr(pr),mr=lr(Me({},pr,{dataTransfer:0})),vr=lr(Me({},dr,{relatedTarget:0})),gr=lr(Me({},ur,{animationName:0,elapsedTime:0,pseudoElement:0})),yr=Me({},ur,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),br=lr(yr),xr=lr(Me({},ur,{data:0})),wr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sr={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Er(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sr[e])&&!!t[e]}function jr(){return Er}var Cr=Me({},dr,{key:function(e){if(e.key){var t=wr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=nr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kr[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jr,charCode:function(e){return"keypress"===e.type?nr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?nr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nr=lr(Cr),_r=lr(Me({},pr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pr=lr(Me({},dr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jr})),Tr=lr(Me({},ur,{propertyName:0,elapsedTime:0,pseudoElement:0})),Lr=Me({},pr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),zr=lr(Lr),Rr=[9,13,27,32],Or=ce&&"CompositionEvent"in window,Dr=null;ce&&"documentMode"in document&&(Dr=document.documentMode);var Fr=ce&&"TextEvent"in window&&!Dr,Mr=ce&&(!Or||Dr&&8<Dr&&11>=Dr),Ir=String.fromCharCode(32),Ur=!1;function Ar(e,t){switch(e){case"keyup":return-1!==Rr.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $r(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Br=!1;var Vr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vr[e.type]:"textarea"===t}function Hr(e,t,n,r){Ct(r),0<(t=Qa(t,"onChange")).length&&(n=new cr("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qr=null,qr=null;function Kr(e){Ia(e,0)}function Yr(e){if(qe(xl(e)))return e}function Gr(e,t){if("change"===e)return t}var Xr=!1;if(ce){var Jr;if(ce){var Zr="oninput"in document;if(!Zr){var ea=document.createElement("div");ea.setAttribute("oninput","return;"),Zr="function"==typeof ea.oninput}Jr=Zr}else Jr=!1;Xr=Jr&&(!document.documentMode||9<document.documentMode)}function ta(){Qr&&(Qr.detachEvent("onpropertychange",na),qr=Qr=null)}function na(e){if("value"===e.propertyName&&Yr(qr)){var t=[];Hr(t,qr,e,wt(e)),Lt(Kr,t)}}function ra(e,t,n){"focusin"===e?(ta(),qr=n,(Qr=t).attachEvent("onpropertychange",na)):"focusout"===e&&ta()}function aa(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yr(qr)}function la(e,t){if("click"===e)return Yr(t)}function ia(e,t){if("input"===e||"change"===e)return Yr(t)}var oa="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function sa(e,t){if(oa(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!de.call(t,a)||!oa(e[a],t[a]))return!1}return!0}function ua(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ca(e,t){var n,r=ua(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ua(r)}}function da(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?da(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fa(){for(var e=window,t=Ke();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Ke((e=t.contentWindow).document)}return t}function pa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function ha(e){var t=fa(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&da(n.ownerDocument.documentElement,n)){if(null!==r&&pa(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=ca(n,l);var i=ca(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ma=ce&&"documentMode"in document&&11>=document.documentMode,va=null,ga=null,ya=null,ba=!1;function xa(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ba||null==va||va!==Ke(r)||("selectionStart"in(r=va)&&pa(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ya&&sa(ya,r)||(ya=r,0<(r=Qa(ga,"onSelect")).length&&(t=new cr("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=va)))}function wa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ka={animationend:wa("Animation","AnimationEnd"),animationiteration:wa("Animation","AnimationIteration"),animationstart:wa("Animation","AnimationStart"),transitionend:wa("Transition","TransitionEnd")},Sa={},Ea={};function ja(e){if(Sa[e])return Sa[e];if(!ka[e])return e;var t,n=ka[e];for(t in n)if(n.hasOwnProperty(t)&&t in Ea)return Sa[e]=n[t];return e}ce&&(Ea=document.createElement("div").style,"AnimationEvent"in window||(delete ka.animationend.animation,delete ka.animationiteration.animation,delete ka.animationstart.animation),"TransitionEvent"in window||delete ka.transitionend.transition);var Ca=ja("animationend"),Na=ja("animationiteration"),_a=ja("animationstart"),Pa=ja("transitionend"),Ta=new Map,La="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function za(e,t){Ta.set(e,t),se(t,[e])}for(var Ra=0;Ra<La.length;Ra++){var Oa=La[Ra];za(Oa.toLowerCase(),"on"+(Oa[0].toUpperCase()+Oa.slice(1)))}za(Ca,"onAnimationEnd"),za(Na,"onAnimationIteration"),za(_a,"onAnimationStart"),za("dblclick","onDoubleClick"),za("focusin","onFocus"),za("focusout","onBlur"),za(Pa,"onTransitionEnd"),ue("onMouseEnter",["mouseout","mouseover"]),ue("onMouseLeave",["mouseout","mouseover"]),ue("onPointerEnter",["pointerout","pointerover"]),ue("onPointerLeave",["pointerout","pointerover"]),se("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),se("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),se("onBeforeInput",["compositionend","keypress","textInput","paste"]),se("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),se("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),se("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Da="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fa=new Set("cancel close invalid load scroll toggle".split(" ").concat(Da));function Ma(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,i,o,s){if($t.apply(this,arguments),Ft){if(!Ft)throw Error(le(198));var u=Mt;Ft=!1,Mt=null,It||(It=!0,Ut=u)}}(r,t,void 0,e),e.currentTarget=null}function Ia(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],s=o.instance,u=o.currentTarget;if(o=o.listener,s!==l&&a.isPropagationStopped())break e;Ma(a,o,u),l=s}else for(i=0;i<r.length;i++){if(s=(o=r[i]).instance,u=o.currentTarget,o=o.listener,s!==l&&a.isPropagationStopped())break e;Ma(a,o,u),l=s}}}if(It)throw e=Ut,It=!1,Ut=null,e}function Ua(e,t){var n=t[ml];void 0===n&&(n=t[ml]=new Set);var r=e+"__bubble";n.has(r)||(Va(t,e,2,!1),n.add(r))}function Aa(e,t,n){var r=0;t&&(r|=4),Va(n,e,r,t)}var $a="_reactListening"+Math.random().toString(36).slice(2);function Ba(e){if(!e[$a]){e[$a]=!0,ie.forEach((function(t){"selectionchange"!==t&&(Fa.has(t)||Aa(t,!1,e),Aa(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$a]||(t[$a]=!0,Aa("selectionchange",!1,t))}}function Va(e,t,n,r){switch(Xn(t)){case 1:var a=Qn;break;case 4:a=qn;break;default:a=Kn}n=a.bind(null,t,n,e),a=void 0,!Rt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Wa(e,t,n,r,a){var l=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a||8===o.nodeType&&o.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;i=i.return}for(;null!==o;){if(null===(i=yl(o)))return;if(5===(s=i.tag)||6===s){r=l=i;continue e}o=o.parentNode}}r=r.return}Lt((function(){var r=l,a=wt(n),i=[];e:{var o=Ta.get(e);if(void 0!==o){var s=cr,u=e;switch(e){case"keypress":if(0===nr(n))break e;case"keydown":case"keyup":s=Nr;break;case"focusin":u="focus",s=vr;break;case"focusout":u="blur",s=vr;break;case"beforeblur":case"afterblur":s=vr;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mr;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pr;break;case Ca:case Na:case _a:s=gr;break;case Pa:s=Tr;break;case"scroll":s=fr;break;case"wheel":s=zr;break;case"copy":case"cut":case"paste":s=br;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=_r}var c=!!(4&t),d=!c&&"scroll"===e,f=c?null!==o?o+"Capture":null:o;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=zt(h,f))&&c.push(Ha(h,m,p)))),d)break;h=h.return}0<c.length&&(o=new s(o,u,null,n,a),i.push({event:o,listeners:c}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===xt||!(u=n.relatedTarget||n.fromElement)||!yl(u)&&!u[hl])&&(s||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?yl(u):null)&&(u!==(d=Bt(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hr,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=_r,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?o:xl(s),p=null==u?o:xl(u),(o=new c(m,h+"leave",s,n,a)).target=d,o.relatedTarget=p,m=null,yl(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=qa(p))h++;for(p=0,m=f;m;m=qa(m))p++;for(;0<h-p;)c=qa(c),h--;for(;0<p-h;)f=qa(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=qa(c),f=qa(f)}c=null}else c=null;null!==s&&Ka(i,o,s,c,!1),null!==u&&null!==d&&Ka(i,d,u,c,!0)}if("select"===(s=(o=r?xl(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===s&&"file"===o.type)var v=Gr;else if(Wr(o))if(Xr)v=ia;else{v=aa;var g=ra}else(s=o.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(v=la);switch(v&&(v=v(e,r))?Hr(i,v,n,a):(g&&g(e,o,r),"focusout"===e&&(g=o._wrapperState)&&g.controlled&&"number"===o.type&&et(o,"number",o.value)),g=r?xl(r):window,e){case"focusin":(Wr(g)||"true"===g.contentEditable)&&(va=g,ga=r,ya=null);break;case"focusout":ya=ga=va=null;break;case"mousedown":ba=!0;break;case"contextmenu":case"mouseup":case"dragend":ba=!1,xa(i,n,a);break;case"selectionchange":if(ma)break;case"keydown":case"keyup":xa(i,n,a)}var y;if(Or)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Br?Ar(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Mr&&"ko"!==n.locale&&(Br||"onCompositionStart"!==b?"onCompositionEnd"===b&&Br&&(y=tr()):(Zn="value"in(Jn=a)?Jn.value:Jn.textContent,Br=!0)),0<(g=Qa(r,b)).length&&(b=new xr(b,e,null,n,a),i.push({event:b,listeners:g}),y?b.data=y:null!==(y=$r(n))&&(b.data=y))),(y=Fr?function(e,t){switch(e){case"compositionend":return $r(t);case"keypress":return 32!==t.which?null:(Ur=!0,Ir);case"textInput":return(e=t.data)===Ir&&Ur?null:e;default:return null}}(e,n):function(e,t){if(Br)return"compositionend"===e||!Or&&Ar(e,t)?(e=tr(),er=Zn=Jn=null,Br=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mr&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Qa(r,"onBeforeInput")).length&&(a=new xr("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=y))}Ia(i,t)}))}function Ha(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qa(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=zt(e,n))&&r.unshift(Ha(e,l,a)),null!=(l=zt(e,t))&&r.push(Ha(e,l,a))),e=e.return}return r}function qa(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Ka(e,t,n,r,a){for(var l=t._reactName,i=[];null!==n&&n!==r;){var o=n,s=o.alternate,u=o.stateNode;if(null!==s&&s===r)break;5===o.tag&&null!==u&&(o=u,a?null!=(s=zt(n,l))&&i.unshift(Ha(n,s,o)):a||null!=(s=zt(n,l))&&i.push(Ha(n,s,o))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Ya=/\r\n?/g,Ga=/\u0000|\uFFFD/g;function Xa(e){return("string"==typeof e?e:""+e).replace(Ya,"\n").replace(Ga,"")}function Ja(e,t,n){if(t=Xa(t),Xa(e)!==t&&n)throw Error(le(425))}function Za(){}var el=null,tl=null;function nl(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var rl="function"==typeof setTimeout?setTimeout:void 0,al="function"==typeof clearTimeout?clearTimeout:void 0,ll="function"==typeof Promise?Promise:void 0,il="function"==typeof queueMicrotask?queueMicrotask:void 0!==ll?function(e){return ll.resolve(null).then(e).catch(ol)}:rl;function ol(e){setTimeout((function(){throw e}))}function sl(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Vn(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Vn(t)}function ul(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function cl(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var dl=Math.random().toString(36).slice(2),fl="__reactFiber$"+dl,pl="__reactProps$"+dl,hl="__reactContainer$"+dl,ml="__reactEvents$"+dl,vl="__reactListeners$"+dl,gl="__reactHandles$"+dl;function yl(e){var t=e[fl];if(t)return t;for(var n=e.parentNode;n;){if(t=n[hl]||n[fl]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=cl(e);null!==e;){if(n=e[fl])return n;e=cl(e)}return t}n=(e=n).parentNode}return null}function bl(e){return!(e=e[fl]||e[hl])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xl(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(le(33))}function wl(e){return e[pl]||null}var kl=[],Sl=-1;function El(e){return{current:e}}function jl(e){0>Sl||(e.current=kl[Sl],kl[Sl]=null,Sl--)}function Cl(e,t){Sl++,kl[Sl]=e.current,e.current=t}var Nl={},_l=El(Nl),Pl=El(!1),Tl=Nl;function Ll(e,t){var n=e.type.contextTypes;if(!n)return Nl;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function zl(e){return null!=(e=e.childContextTypes)}function Rl(){jl(Pl),jl(_l)}function Ol(e,t,n){if(_l.current!==Nl)throw Error(le(168));Cl(_l,t),Cl(Pl,n)}function Dl(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(le(108,Ve(e)||"Unknown",a));return Me({},n,r)}function Fl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Nl,Tl=_l.current,Cl(_l,e),Cl(Pl,Pl.current),!0}function Ml(e,t,n){var r=e.stateNode;if(!r)throw Error(le(169));n?(e=Dl(e,t,Tl),r.__reactInternalMemoizedMergedChildContext=e,jl(Pl),jl(_l),Cl(_l,e)):jl(Pl),Cl(Pl,n)}var Il=null,Ul=!1,Al=!1;function $l(e){null===Il?Il=[e]:Il.push(e)}function Bl(){if(!Al&&null!==Il){Al=!0;var e=0,t=xn;try{var n=Il;for(xn=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Il=null,Ul=!1}catch(a){throw null!==Il&&(Il=Il.slice(e+1)),qt(Zt,Bl),a}finally{xn=t,Al=!1}}return null}var Vl=[],Wl=0,Hl=null,Ql=0,ql=[],Kl=0,Yl=null,Gl=1,Xl="";function Jl(e,t){Vl[Wl++]=Ql,Vl[Wl++]=Hl,Hl=e,Ql=t}function Zl(e,t,n){ql[Kl++]=Gl,ql[Kl++]=Xl,ql[Kl++]=Yl,Yl=e;var r=Gl;e=Xl;var a=32-on(r)-1;r&=~(1<<a),n+=1;var l=32-on(t)+a;if(30<l){var i=a-a%5;l=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Gl=1<<32-on(t)+a|n<<a|r,Xl=l+e}else Gl=1<<l|n<<a|r,Xl=e}function ei(e){null!==e.return&&(Jl(e,1),Zl(e,1,0))}function ti(e){for(;e===Hl;)Hl=Vl[--Wl],Vl[Wl]=null,Ql=Vl[--Wl],Vl[Wl]=null;for(;e===Yl;)Yl=ql[--Kl],ql[Kl]=null,Xl=ql[--Kl],ql[Kl]=null,Gl=ql[--Kl],ql[Kl]=null}var ni=null,ri=null,ai=!1,li=null;function ii(e,t){var n=Lc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function oi(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,ri=ul(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,ri=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Yl?{id:Gl,overflow:Xl}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ni=e,ri=null,!0);default:return!1}}function si(e){return!(!(1&e.mode)||128&e.flags)}function ui(e){if(ai){var t=ri;if(t){var n=t;if(!oi(e,t)){if(si(e))throw Error(le(418));t=ul(n.nextSibling);var r=ni;t&&oi(e,t)?ii(r,n):(e.flags=-4097&e.flags|2,ai=!1,ni=e)}}else{if(si(e))throw Error(le(418));e.flags=-4097&e.flags|2,ai=!1,ni=e}}}function ci(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function di(e){if(e!==ni)return!1;if(!ai)return ci(e),ai=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!nl(e.type,e.memoizedProps)),t&&(t=ri)){if(si(e))throw fi(),Error(le(418));for(;t;)ii(e,t),t=ul(t.nextSibling)}if(ci(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(le(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ri=ul(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ri=null}}else ri=ni?ul(e.stateNode.nextSibling):null;return!0}function fi(){for(var e=ri;e;)e=ul(e.nextSibling)}function pi(){ri=ni=null,ai=!1}function hi(e){null===li?li=[e]:li.push(e)}var mi=xe.ReactCurrentBatchConfig;function vi(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(le(309));var r=n.stateNode}if(!r)throw Error(le(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===l?t.ref:((t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e})._stringRef=l,t)}if("string"!=typeof e)throw Error(le(284));if(!n._owner)throw Error(le(290,e))}return e}function gi(e,t){throw e=Object.prototype.toString.call(t),Error(le(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yi(e){return(0,e._init)(e._payload)}function bi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Rc(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=Mc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function s(e,t,n,r){var l=n.type;return l===Se?c(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===ze&&yi(l)===t.type)?((r=a(t,n.props)).ref=vi(e,t,n),r.return=e,r):((r=Oc(n.type,n.key,n.props,null,e.mode,r)).ref=vi(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ic(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function c(e,t,n,r,l){return null===t||7!==t.tag?((t=Dc(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Mc(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case we:return(n=Oc(t.type,t.key,t.props,null,e.mode,n)).ref=vi(e,null,t),n.return=e,n;case ke:return(t=Ic(t,e.mode,n)).return=e,t;case ze:return d(e,(0,t._init)(t._payload),n)}if(tt(t)||De(t))return(t=Dc(t,e.mode,n,null)).return=e,t;gi(e,t)}return null}function f(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:o(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case we:return n.key===a?s(e,t,n,r):null;case ke:return n.key===a?u(e,t,n,r):null;case ze:return f(e,t,(a=n._init)(n._payload),r)}if(tt(n)||De(n))return null!==a?null:c(e,t,n,r,null);gi(e,n)}return null}function p(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case we:return s(t,e=e.get(null===r.key?n:r.key)||null,r,a);case ke:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case ze:return p(e,t,n,(0,r._init)(r._payload),a)}if(tt(r)||De(r))return c(t,e=e.get(n)||null,r,a,null);gi(t,r)}return null}return function o(s,u,c,h){if("object"==typeof c&&null!==c&&c.type===Se&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case we:e:{for(var m=c.key,v=u;null!==v;){if(v.key===m){if((m=c.type)===Se){if(7===v.tag){n(s,v.sibling),(u=a(v,c.props.children)).return=s,s=u;break e}}else if(v.elementType===m||"object"==typeof m&&null!==m&&m.$$typeof===ze&&yi(m)===v.type){n(s,v.sibling),(u=a(v,c.props)).ref=vi(s,v,c),u.return=s,s=u;break e}n(s,v);break}t(s,v),v=v.sibling}c.type===Se?((u=Dc(c.props.children,s.mode,h,c.key)).return=s,s=u):((h=Oc(c.type,c.key,c.props,null,s.mode,h)).ref=vi(s,u,c),h.return=s,s=h)}return i(s);case ke:e:{for(v=c.key;null!==u;){if(u.key===v){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(u=a(u,c.children||[])).return=s,s=u;break e}n(s,u);break}t(s,u),u=u.sibling}(u=Ic(c,s.mode,h)).return=s,s=u}return i(s);case ze:return o(s,u,(v=c._init)(c._payload),h)}if(tt(c))return function(a,i,o,s){for(var u=null,c=null,h=i,m=i=0,v=null;null!==h&&m<o.length;m++){h.index>m?(v=h,h=null):v=h.sibling;var g=f(a,h,o[m],s);if(null===g){null===h&&(h=v);break}e&&h&&null===g.alternate&&t(a,h),i=l(g,i,m),null===c?u=g:c.sibling=g,c=g,h=v}if(m===o.length)return n(a,h),ai&&Jl(a,m),u;if(null===h){for(;m<o.length;m++)null!==(h=d(a,o[m],s))&&(i=l(h,i,m),null===c?u=h:c.sibling=h,c=h);return ai&&Jl(a,m),u}for(h=r(a,h);m<o.length;m++)null!==(v=p(h,a,m,o[m],s))&&(e&&null!==v.alternate&&h.delete(null===v.key?m:v.key),i=l(v,i,m),null===c?u=v:c.sibling=v,c=v);return e&&h.forEach((function(e){return t(a,e)})),ai&&Jl(a,m),u}(s,u,c,h);if(De(c))return function(a,i,o,s){var u=De(o);if("function"!=typeof u)throw Error(le(150));if(null==(o=u.call(o)))throw Error(le(151));for(var c=u=null,h=i,m=i=0,v=null,g=o.next();null!==h&&!g.done;m++,g=o.next()){h.index>m?(v=h,h=null):v=h.sibling;var y=f(a,h,g.value,s);if(null===y){null===h&&(h=v);break}e&&h&&null===y.alternate&&t(a,h),i=l(y,i,m),null===c?u=y:c.sibling=y,c=y,h=v}if(g.done)return n(a,h),ai&&Jl(a,m),u;if(null===h){for(;!g.done;m++,g=o.next())null!==(g=d(a,g.value,s))&&(i=l(g,i,m),null===c?u=g:c.sibling=g,c=g);return ai&&Jl(a,m),u}for(h=r(a,h);!g.done;m++,g=o.next())null!==(g=p(h,a,m,g.value,s))&&(e&&null!==g.alternate&&h.delete(null===g.key?m:g.key),i=l(g,i,m),null===c?u=g:c.sibling=g,c=g);return e&&h.forEach((function(e){return t(a,e)})),ai&&Jl(a,m),u}(s,u,c,h);gi(s,c)}return"string"==typeof c&&""!==c||"number"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(u=a(u,c)).return=s,s=u):(n(s,u),(u=Mc(c,s.mode,h)).return=s,s=u),i(s)):n(s,u)}}var xi=bi(!0),wi=bi(!1),ki=El(null),Si=null,Ei=null,ji=null;function Ci(){ji=Ei=Si=null}function Ni(e){var t=ki.current;jl(ki),e._currentValue=t}function _i(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pi(e,t){Si=e,ji=Ei=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function Ti(e){var t=e._currentValue;if(ji!==e)if(e={context:e,memoizedValue:t,next:null},null===Ei){if(null===Si)throw Error(le(308));Ei=e,Si.dependencies={lanes:0,firstContext:e}}else Ei=Ei.next=e;return t}var Li=null;function zi(e){null===Li?Li=[e]:Li.push(e)}function Ri(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,zi(t)):(n.next=a.next,a.next=n),t.interleaved=n,Oi(e,r)}function Oi(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Di=!1;function Fi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Mi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ii(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ui(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&_u){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Oi(e,n)}return null===(a=r.interleaved)?(t.next=t,zi(r)):(t.next=a.next,a.next=t),r.interleaved=t,Oi(e,n)}function Ai(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bn(e,n)}}function $i(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=i:l=l.next=i,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Bi(e,t,n,r){var a=e.updateQueue;Di=!1;var l=a.firstBaseUpdate,i=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,u=s.next;s.next=null,null===i?l=u:i.next=u,i=s;var c=e.alternate;null!==c&&((o=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===o?c.firstBaseUpdate=u:o.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(i=0,c=u=s=null,o=l;;){var f=o.lane,p=o.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var h=e,m=o;switch(f=t,p=n,m.tag){case 1:if("function"==typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null==(f="function"==typeof(h=m.payload)?h.call(p,d,f):h))break e;d=Me({},d,f);break e;case 2:Di=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[o]:f.push(o))}else p={eventTime:p,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=f;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(f=o).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Fu|=i,e.lanes=i,e.memoizedState=d}}function Vi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(le(191,a));a.call(r)}}}var Wi={},Hi=El(Wi),Qi=El(Wi),qi=El(Wi);function Ki(e){if(e===Wi)throw Error(le(174));return e}function Yi(e,t){switch(Cl(qi,t),Cl(Qi,e),Cl(Hi,Wi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:st(null,"");break;default:t=st(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}jl(Hi),Cl(Hi,t)}function Gi(){jl(Hi),jl(Qi),jl(qi)}function Xi(e){Ki(qi.current);var t=Ki(Hi.current),n=st(t,e.type);t!==n&&(Cl(Qi,e),Cl(Hi,n))}function Ji(e){Qi.current===e&&(jl(Hi),jl(Qi))}var Zi=El(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=xe.ReactCurrentDispatcher,ao=xe.ReactCurrentBatchConfig,lo=0,io=null,oo=null,so=null,uo=!1,co=!1,fo=0,po=0;function ho(){throw Error(le(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!oa(e[n],t[n]))return!1;return!0}function vo(e,t,n,r,a,l){if(lo=l,io=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Zo:es,e=n(r,a),co){l=0;do{if(co=!1,fo=0,25<=l)throw Error(le(301));l+=1,so=oo=null,t.updateQueue=null,ro.current=ts,e=n(r,a)}while(co)}if(ro.current=Jo,t=null!==oo&&null!==oo.next,lo=0,so=oo=io=null,uo=!1,t)throw Error(le(300));return e}function go(){var e=0!==fo;return fo=0,e}function yo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?io.memoizedState=so=e:so=so.next=e,so}function bo(){if(null===oo){var e=io.alternate;e=null!==e?e.memoizedState:null}else e=oo.next;var t=null===so?io.memoizedState:so.next;if(null!==t)so=t,oo=e;else{if(null===e)throw Error(le(310));e={memoizedState:(oo=e).memoizedState,baseState:oo.baseState,baseQueue:oo.baseQueue,queue:oo.queue,next:null},null===so?io.memoizedState=so=e:so=so.next=e}return so}function xo(e,t){return"function"==typeof t?t(e):t}function wo(e){var t=bo(),n=t.queue;if(null===n)throw Error(le(311));n.lastRenderedReducer=e;var r=oo,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var o=i=null,s=null,u=l;do{var c=u.lane;if((lo&c)===c)null!==s&&(s=s.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===s?(o=s=d,i=r):s=s.next=d,io.lanes|=c,Fu|=c}u=u.next}while(null!==u&&u!==l);null===s?i=r:s.next=o,oa(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,io.lanes|=l,Fu|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ko(e){var t=bo(),n=t.queue;if(null===n)throw Error(le(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);oa(l,t.memoizedState)||(bs=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function So(){}function Eo(e,t){var n=io,r=bo(),a=t(),l=!oa(r.memoizedState,a);if(l&&(r.memoizedState=a,bs=!0),r=r.queue,Fo(No.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,Lo(9,Co.bind(null,n,r,a,t),void 0,null),null===Pu)throw Error(le(349));30&lo||jo(n,t,a)}return a}function jo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=io.updateQueue)?(t={lastEffect:null,stores:null},io.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Co(e,t,n,r){t.value=n,t.getSnapshot=r,_o(t)&&Po(e)}function No(e,t,n){return n((function(){_o(t)&&Po(e)}))}function _o(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!oa(e,n)}catch(r){return!0}}function Po(e){var t=Oi(e,1);null!==t&&nc(t,e,1,-1)}function To(e){var t=yo();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ko.bind(null,io,e),[t.memoizedState,e]}function Lo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=io.updateQueue)?(t={lastEffect:null,stores:null},io.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function zo(){return bo().memoizedState}function Ro(e,t,n,r){var a=yo();io.flags|=e,a.memoizedState=Lo(1|t,n,void 0,void 0===r?null:r)}function Oo(e,t,n,r){var a=bo();r=void 0===r?null:r;var l=void 0;if(null!==oo){var i=oo.memoizedState;if(l=i.destroy,null!==r&&mo(r,i.deps))return void(a.memoizedState=Lo(t,n,l,r))}io.flags|=e,a.memoizedState=Lo(1|t,n,l,r)}function Do(e,t){return Ro(8390656,8,e,t)}function Fo(e,t){return Oo(2048,8,e,t)}function Mo(e,t){return Oo(4,2,e,t)}function Io(e,t){return Oo(4,4,e,t)}function Uo(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ao(e,t,n){return n=null!=n?n.concat([e]):null,Oo(4,4,Uo.bind(null,t,e),n)}function $o(){}function Bo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wo(e,t,n){return 21&lo?(oa(n,t)||(n=vn(),io.lanes|=n,Fu|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n)}function Ho(e,t){var n=xn;xn=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{xn=n,ao.transition=r}}function Qo(){return bo().memoizedState}function qo(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yo(e))Go(t,n);else if(null!==(n=Ri(e,t,n,r))){nc(n,e,r,ec()),Xo(n,t,r)}}function Ko(e,t,n){var r=tc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yo(e))Go(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var i=t.lastRenderedState,o=l(i,n);if(a.hasEagerState=!0,a.eagerState=o,oa(o,i)){var s=t.interleaved;return null===s?(a.next=a,zi(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Ri(e,t,a,r))&&(nc(n,e,r,a=ec()),Xo(n,t,r))}}function Yo(e){var t=e.alternate;return e===io||null!==t&&t===io}function Go(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xo(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bn(e,n)}}var Jo={readContext:Ti,useCallback:ho,useContext:ho,useEffect:ho,useImperativeHandle:ho,useInsertionEffect:ho,useLayoutEffect:ho,useMemo:ho,useReducer:ho,useRef:ho,useState:ho,useDebugValue:ho,useDeferredValue:ho,useTransition:ho,useMutableSource:ho,useSyncExternalStore:ho,useId:ho,unstable_isNewReconciler:!1},Zo={readContext:Ti,useCallback:function(e,t){return yo().memoizedState=[e,void 0===t?null:t],e},useContext:Ti,useEffect:Do,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Ro(4194308,4,Uo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ro(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ro(4,2,e,t)},useMemo:function(e,t){var n=yo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qo.bind(null,io,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yo().memoizedState=e},useState:To,useDebugValue:$o,useDeferredValue:function(e){return yo().memoizedState=e},useTransition:function(){var e=To(!1),t=e[0];return e=Ho.bind(null,e[1]),yo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=io,a=yo();if(ai){if(void 0===n)throw Error(le(407));n=n()}else{if(n=t(),null===Pu)throw Error(le(349));30&lo||jo(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,Do(No.bind(null,r,l,e),[e]),r.flags|=2048,Lo(9,Co.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=yo(),t=Pu.identifierPrefix;if(ai){var n=Xl;t=":"+t+"R"+(n=(Gl&~(1<<32-on(Gl)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=po++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Ti,useCallback:Bo,useContext:Ti,useEffect:Fo,useImperativeHandle:Ao,useInsertionEffect:Mo,useLayoutEffect:Io,useMemo:Vo,useReducer:wo,useRef:zo,useState:function(){return wo(xo)},useDebugValue:$o,useDeferredValue:function(e){return Wo(bo(),oo.memoizedState,e)},useTransition:function(){return[wo(xo)[0],bo().memoizedState]},useMutableSource:So,useSyncExternalStore:Eo,useId:Qo,unstable_isNewReconciler:!1},ts={readContext:Ti,useCallback:Bo,useContext:Ti,useEffect:Fo,useImperativeHandle:Ao,useInsertionEffect:Mo,useLayoutEffect:Io,useMemo:Vo,useReducer:ko,useRef:zo,useState:function(){return ko(xo)},useDebugValue:$o,useDeferredValue:function(e){var t=bo();return null===oo?t.memoizedState=e:Wo(t,oo.memoizedState,e)},useTransition:function(){return[ko(xo)[0],bo().memoizedState]},useMutableSource:So,useSyncExternalStore:Eo,useId:Qo,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=Me({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:Me({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return!!(e=e._reactInternals)&&Bt(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Ii(r,a);l.payload=t,null!=n&&(l.callback=n),null!==(t=Ui(e,l,a))&&(nc(t,e,a,r),Ai(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),a=tc(e),l=Ii(r,a);l.tag=1,l.payload=t,null!=n&&(l.callback=n),null!==(t=Ui(e,l,a))&&(nc(t,e,a,r),Ai(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),a=Ii(n,r);a.tag=2,null!=t&&(a.callback=t),null!==(t=Ui(e,a,r))&&(nc(t,e,r,n),Ai(t,e,r))}};function ls(e,t,n,r,a,l,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,i):!t.prototype||!t.prototype.isPureReactComponent||(!sa(n,r)||!sa(a,l))}function is(e,t,n){var r=!1,a=Nl,l=t.contextType;return"object"==typeof l&&null!==l?l=Ti(l):(a=zl(t)?Tl:_l.current,l=(r=null!=(r=t.contextTypes))?Ll(e,a):Nl),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function os(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function ss(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fi(e);var l=t.contextType;"object"==typeof l&&null!==l?a.context=Ti(l):(l=zl(t)?Tl:_l.current,a.context=Ll(e,l)),a.state=e.memoizedState,"function"==typeof(l=t.getDerivedStateFromProps)&&(rs(e,t,l,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&as.enqueueReplaceState(a,a.state,null),Bi(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=$e(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fs="function"==typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=Ii(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wu||(Wu=!0,Hu=r),ds(0,t)},n}function hs(e,t,n){(n=Ii(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ds(0,t)}}var l=e.stateNode;return null!==l&&"function"==typeof l.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!=typeof r&&(null===Qu?Qu=new Set([this]):Qu.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=jc.bind(null,e,t,n),t.then(e,e))}function vs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function gs(e,t,n,r,a){return 1&e.mode?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ii(-1,1)).tag=2,Ui(n,t,1))),n.lanes|=1),e)}var ys=xe.ReactCurrentOwner,bs=!1;function xs(e,t,n,r){t.child=null===e?wi(t,null,n,r):xi(t,e.child,n,r)}function ws(e,t,n,r,a){n=n.render;var l=t.ref;return Pi(t,a),r=vo(e,t,n,r,l,a),n=go(),null===e||bs?(ai&&n&&ei(t),t.flags|=1,xs(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function ks(e,t,n,r,a){if(null===e){var l=n.type;return"function"!=typeof l||zc(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Oc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ss(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var i=l.memoizedProps;if((n=null!==(n=n.compare)?n:sa)(i,r)&&e.ref===t.ref)return Ws(e,t,a)}return t.flags|=1,(e=Rc(l,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sa(l,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Ws(e,t,a);131072&e.flags&&(bs=!0)}}return Cs(e,t,n,r,a)}function Es(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Cl(Ru,zu),zu|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Cl(Ru,zu),zu|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Cl(Ru,zu),zu|=n;else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Cl(Ru,zu),zu|=r;return xs(e,t,a,n),t.child}function js(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Cs(e,t,n,r,a){var l=zl(n)?Tl:_l.current;return l=Ll(t,l),Pi(t,a),n=vo(e,t,n,r,l,a),r=go(),null===e||bs?(ai&&r&&ei(t),t.flags|=1,xs(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function Ns(e,t,n,r,a){if(zl(n)){var l=!0;Fl(t)}else l=!1;if(Pi(t,a),null===t.stateNode)Vs(e,t),is(t,n,r),ss(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,o=t.memoizedProps;i.props=o;var s=i.context,u=n.contextType;"object"==typeof u&&null!==u?u=Ti(u):u=Ll(t,u=zl(n)?Tl:_l.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof i.getSnapshotBeforeUpdate;d||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(o!==r||s!==u)&&os(t,i,r,u),Di=!1;var f=t.memoizedState;i.state=f,Bi(t,r,i,a),s=t.memoizedState,o!==r||f!==s||Pl.current||Di?("function"==typeof c&&(rs(t,n,c,r),s=t.memoizedState),(o=Di||ls(t,n,o,r,f,s,u))?(d||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=u,r=o):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Mi(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:ns(t.type,o),i.props=u,d=t.pendingProps,f=i.context,"object"==typeof(s=n.contextType)&&null!==s?s=Ti(s):s=Ll(t,s=zl(n)?Tl:_l.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(o!==d||f!==s)&&os(t,i,r,s),Di=!1,f=t.memoizedState,i.state=f,Bi(t,r,i,a);var h=t.memoizedState;o!==d||f!==h||Pl.current||Di?("function"==typeof p&&(rs(t,n,p,r),h=t.memoizedState),(u=Di||ls(t,n,u,r,f,h,s)||!1)?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,s),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,s)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=s,r=u):("function"!=typeof i.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return _s(e,t,n,r,l,a)}function _s(e,t,n,r,a,l){js(e,t);var i=!!(128&t.flags);if(!r&&!i)return a&&Ml(t,n,!1),Ws(e,t,l);r=t.stateNode,ys.current=t;var o=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=xi(t,e.child,null,l),t.child=xi(t,null,o,l)):xs(e,t,o,l),t.memoizedState=r.state,a&&Ml(t,n,!0),t.child}function Ps(e){var t=e.stateNode;t.pendingContext?Ol(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ol(0,t.context,!1),Yi(e,t.containerInfo)}function Ts(e,t,n,r,a){return pi(),hi(a),t.flags|=256,xs(e,t,n,r),t.child}var Ls,zs,Rs,Os,Ds={dehydrated:null,treeContext:null,retryLane:0};function Fs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ms(e,t,n){var r,a=t.pendingProps,l=Zi.current,i=!1,o=!!(128&t.flags);if((r=o)||(r=(null===e||null!==e.memoizedState)&&!!(2&l)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),Cl(Zi,1&l),null===e)return ui(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=a.children,e=a.fallback,i?(a=t.mode,i=t.child,o={mode:"hidden",children:o},1&a||null===i?i=Fc(o,a,0,null):(i.childLanes=0,i.pendingProps=o),e=Dc(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Fs(n),t.memoizedState=Ds,e):Is(t,o));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,i){if(n)return 256&t.flags?(t.flags&=-257,Us(e,t,i,r=cs(Error(le(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Fc({mode:"visible",children:r.children},a,0,null),(l=Dc(l,a,i,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,1&t.mode&&xi(t,e.child,null,i),t.child.memoizedState=Fs(i),t.memoizedState=Ds,l);if(!(1&t.mode))return Us(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var o=r.dgst;return r=o,Us(e,t,i,r=cs(l=Error(le(419)),r,void 0))}if(o=0!==(i&e.childLanes),bs||o){if(null!==(r=Pu)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==l.retryLane&&(l.retryLane=a,Oi(e,a),nc(r,e,a,-1))}return mc(),Us(e,t,i,r=cs(Error(le(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Nc.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,ri=ul(a.nextSibling),ni=t,ai=!0,li=null,null!==e&&(ql[Kl++]=Gl,ql[Kl++]=Xl,ql[Kl++]=Yl,Gl=e.id,Xl=e.overflow,Yl=t),t=Is(t,r.children),t.flags|=4096,t)}(e,t,o,a,r,l,n);if(i){i=a.fallback,o=t.mode,r=(l=e.child).sibling;var s={mode:"hidden",children:a.children};return 1&o||t.child===l?(a=Rc(l,s)).subtreeFlags=14680064&l.subtreeFlags:((a=t.child).childLanes=0,a.pendingProps=s,t.deletions=null),null!==r?i=Rc(r,i):(i=Dc(i,o,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,o=null===(o=e.child.memoizedState)?Fs(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Ds,a}return e=(i=e.child).sibling,a=Rc(i,{mode:"visible",children:a.children}),!(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Is(e,t){return(t=Fc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Us(e,t,n,r){return null!==r&&hi(r),xi(t,e.child,null,n),(e=Is(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function As(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),_i(e.return,t,n)}function $s(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Bs(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(xs(e,t,r.children,n),2&(r=Zi.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&As(e,n,t);else if(19===e.tag)As(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Cl(Zi,r),1&t.mode)switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$s(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$s(t,!0,n,null,l);break;case"together":$s(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Vs(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fu|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(le(153));if(null!==t.child){for(n=Rc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Rc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Hs(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qs(e,t,n){var r=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qs(t),null;case 1:case 17:return zl(t.type)&&Rl(),Qs(t),null;case 3:return r=t.stateNode,Gi(),jl(Pl),jl(_l),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(di(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==li&&(ic(li),li=null))),zs(e,t),Qs(t),null;case 5:Ji(t);var a=Ki(qi.current);if(n=t.type,null!==e&&null!=t.stateNode)Rs(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(le(166));return Qs(t),null}if(e=Ki(Hi.current),di(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[fl]=t,r[pl]=l,e=!!(1&t.mode),n){case"dialog":Ua("cancel",r),Ua("close",r);break;case"iframe":case"object":case"embed":Ua("load",r);break;case"video":case"audio":for(a=0;a<Da.length;a++)Ua(Da[a],r);break;case"source":Ua("error",r);break;case"img":case"image":case"link":Ua("error",r),Ua("load",r);break;case"details":Ua("toggle",r);break;case"input":Ge(r,l),Ua("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Ua("invalid",r);break;case"textarea":at(r,l),Ua("invalid",r)}for(var i in yt(n,l),a=null,l)if(l.hasOwnProperty(i)){var o=l[i];"children"===i?"string"==typeof o?r.textContent!==o&&(!0!==l.suppressHydrationWarning&&Ja(r.textContent,o,e),a=["children",o]):"number"==typeof o&&r.textContent!==""+o&&(!0!==l.suppressHydrationWarning&&Ja(r.textContent,o,e),a=["children",""+o]):oe.hasOwnProperty(i)&&null!=o&&"onScroll"===i&&Ua("scroll",r)}switch(n){case"input":Qe(r),Ze(r,l,!0);break;case"textarea":Qe(r),it(r);break;case"select":case"option":break;default:"function"==typeof l.onClick&&(r.onclick=Za)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{i=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ot(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),"select"===n&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[fl]=t,e[pl]=r,Ls(e,t,!1,!1),t.stateNode=e;e:{switch(i=bt(n,r),n){case"dialog":Ua("cancel",e),Ua("close",e),a=r;break;case"iframe":case"object":case"embed":Ua("load",e),a=r;break;case"video":case"audio":for(a=0;a<Da.length;a++)Ua(Da[a],e);a=r;break;case"source":Ua("error",e),a=r;break;case"img":case"image":case"link":Ua("error",e),Ua("load",e),a=r;break;case"details":Ua("toggle",e),a=r;break;case"input":Ge(e,r),a=Ye(e,r),Ua("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=Me({},r,{value:void 0}),Ua("invalid",e);break;case"textarea":at(e,r),a=rt(e,r),Ua("invalid",e)}for(l in yt(n,a),o=a)if(o.hasOwnProperty(l)){var s=o[l];"style"===l?vt(e,s):"dangerouslySetInnerHTML"===l?null!=(s=s?s.__html:void 0)&&dt(e,s):"children"===l?"string"==typeof s?("textarea"!==n||""!==s)&&ft(e,s):"number"==typeof s&&ft(e,""+s):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(oe.hasOwnProperty(l)?null!=s&&"onScroll"===l&&Ua("scroll",e):null!=s&&be(e,l,s,i))}switch(n){case"input":Qe(e),Ze(e,r,!1);break;case"textarea":Qe(e),it(e);break;case"option":null!=r.value&&e.setAttribute("value",""+We(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?nt(e,!!r.multiple,l,!1):null!=r.defaultValue&&nt(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=Za)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Qs(t),null;case 6:if(e&&null!=t.stateNode)Os(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(le(166));if(n=Ki(qi.current),Ki(Hi.current),di(t)){if(r=t.stateNode,n=t.memoizedProps,r[fl]=t,(l=r.nodeValue!==n)&&null!==(e=ni))switch(e.tag){case 3:Ja(r.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Ja(r.nodeValue,n,!!(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fl]=t,t.stateNode=r}return Qs(t),null;case 13:if(jl(Zi),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ai&&null!==ri&&1&t.mode&&!(128&t.flags))fi(),pi(),t.flags|=98560,l=!1;else if(l=di(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(le(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(le(317));l[fl]=t}else pi(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qs(t),l=!1}else null!==li&&(ic(li),li=null),l=!0;if(!l)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,1&t.mode&&(null===e||1&Zi.current?0===Ou&&(Ou=3):mc())),null!==t.updateQueue&&(t.flags|=4),Qs(t),null);case 4:return Gi(),zs(e,t),null===e&&Ba(t.stateNode.containerInfo),Qs(t),null;case 10:return Ni(t.type._context),Qs(t),null;case 19:if(jl(Zi),null===(l=t.memoizedState))return Qs(t),null;if(r=!!(128&t.flags),null===(i=l.rendering))if(r)Hs(l,!1);else{if(0!==Ou||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(i=eo(e))){for(t.flags|=128,Hs(l,!1),null!==(r=i.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(i=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=i.childLanes,l.lanes=i.lanes,l.child=i.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=i.memoizedProps,l.memoizedState=i.memoizedState,l.updateQueue=i.updateQueue,l.type=i.type,e=i.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Cl(Zi,1&Zi.current|2),t.child}e=e.sibling}null!==l.tail&&Xt()>Bu&&(t.flags|=128,r=!0,Hs(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(i))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Hs(l,!0),null===l.tail&&"hidden"===l.tailMode&&!i.alternate&&!ai)return Qs(t),null}else 2*Xt()-l.renderingStartTime>Bu&&1073741824!==n&&(t.flags|=128,r=!0,Hs(l,!1),t.lanes=4194304);l.isBackwards?(i.sibling=t.child,t.child=i):(null!==(n=l.last)?n.sibling=i:t.child=i,l.last=i)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Xt(),t.sibling=null,n=Zi.current,Cl(Zi,r?1&n|2:1&n),t):(Qs(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&1&t.mode?!!(1073741824&zu)&&(Qs(t),6&t.subtreeFlags&&(t.flags|=8192)):Qs(t),null;case 24:case 25:return null}throw Error(le(156,t.tag))}function Ks(e,t){switch(ti(t),t.tag){case 1:return zl(t.type)&&Rl(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Gi(),jl(Pl),jl(_l),no(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Ji(t),null;case 13:if(jl(Zi),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(le(340));pi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return jl(Zi),null;case 4:return Gi(),null;case 10:return Ni(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Ls=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},zs=function(){},Rs=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Ki(Hi.current);var l,i=null;switch(n){case"input":a=Ye(e,a),r=Ye(e,r),i=[];break;case"select":a=Me({},a,{value:void 0}),r=Me({},r,{value:void 0}),i=[];break;case"textarea":a=rt(e,a),r=rt(e,r),i=[];break;default:"function"!=typeof a.onClick&&"function"==typeof r.onClick&&(e.onclick=Za)}for(u in yt(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var o=a[u];for(l in o)o.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(oe.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var s=r[u];if(o=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&s!==o&&(null!=s||null!=o))if("style"===u)if(o){for(l in o)!o.hasOwnProperty(l)||s&&s.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in s)s.hasOwnProperty(l)&&o[l]!==s[l]&&(n||(n={}),n[l]=s[l])}else n||(i||(i=[]),i.push(u,n)),n=s;else"dangerouslySetInnerHTML"===u?(s=s?s.__html:void 0,o=o?o.__html:void 0,null!=s&&o!==s&&(i=i||[]).push(u,s)):"children"===u?"string"!=typeof s&&"number"!=typeof s||(i=i||[]).push(u,""+s):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(oe.hasOwnProperty(u)?(null!=s&&"onScroll"===u&&Ua("scroll",e),i||o===s||(i=[])):(i=i||[]).push(u,s))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},Os=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ys=!1,Gs=!1,Xs="function"==typeof WeakSet?WeakSet:Set,Js=null;function Zs(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(r){Ec(e,t,r)}else n.current=null}function eu(e,t,n){try{n()}catch(r){Ec(e,t,r)}}var tu=!1;function nu(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&eu(t,n,l)}a=a.next}while(a!==r)}}function ru(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function au(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function lu(e){var t=e.alternate;null!==t&&(e.alternate=null,lu(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fl],delete t[pl],delete t[ml],delete t[vl],delete t[gl])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function iu(e){return 5===e.tag||3===e.tag||4===e.tag}function ou(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||iu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function su(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Za));else if(4!==r&&null!==(e=e.child))for(su(e,t,n),e=e.sibling;null!==e;)su(e,t,n),e=e.sibling}function uu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(uu(e,t,n),e=e.sibling;null!==e;)uu(e,t,n),e=e.sibling}var cu=null,du=!1;function fu(e,t,n){for(n=n.child;null!==n;)pu(e,t,n),n=n.sibling}function pu(e,t,n){if(ln&&"function"==typeof ln.onCommitFiberUnmount)try{ln.onCommitFiberUnmount(an,n)}catch(o){}switch(n.tag){case 5:Gs||Zs(n,t);case 6:var r=cu,a=du;cu=null,fu(e,t,n),du=a,null!==(cu=r)&&(du?(e=cu,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cu.removeChild(n.stateNode));break;case 18:null!==cu&&(du?(e=cu,n=n.stateNode,8===e.nodeType?sl(e.parentNode,n):1===e.nodeType&&sl(e,n),Vn(e)):sl(cu,n.stateNode));break;case 4:r=cu,a=du,cu=n.stateNode.containerInfo,du=!0,fu(e,t,n),cu=r,du=a;break;case 0:case 11:case 14:case 15:if(!Gs&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,i=l.destroy;l=l.tag,void 0!==i&&(2&l||4&l)&&eu(n,t,i),a=a.next}while(a!==r)}fu(e,t,n);break;case 1:if(!Gs&&(Zs(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){Ec(n,t,o)}fu(e,t,n);break;case 21:fu(e,t,n);break;case 22:1&n.mode?(Gs=(r=Gs)||null!==n.memoizedState,fu(e,t,n),Gs=r):fu(e,t,n);break;default:fu(e,t,n)}}function hu(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xs),t.forEach((function(t){var r=_c.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function mu(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 5:cu=o.stateNode,du=!1;break e;case 3:case 4:cu=o.stateNode.containerInfo,du=!0;break e}o=o.return}if(null===cu)throw Error(le(160));pu(l,i,a),cu=null,du=!1;var s=a.alternate;null!==s&&(s.return=null),a.return=null}catch(u){Ec(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vu(t,e),t=t.sibling}function vu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mu(t,e),gu(e),4&r){try{nu(3,e,e.return),ru(3,e)}catch(m){Ec(e,e.return,m)}try{nu(5,e,e.return)}catch(m){Ec(e,e.return,m)}}break;case 1:mu(t,e),gu(e),512&r&&null!==n&&Zs(n,n.return);break;case 5:if(mu(t,e),gu(e),512&r&&null!==n&&Zs(n,n.return),32&e.flags){var a=e.stateNode;try{ft(a,"")}catch(m){Ec(e,e.return,m)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,o=e.type,s=e.updateQueue;if(e.updateQueue=null,null!==s)try{"input"===o&&"radio"===l.type&&null!=l.name&&Xe(a,l),bt(o,i);var u=bt(o,l);for(i=0;i<s.length;i+=2){var c=s[i],d=s[i+1];"style"===c?vt(a,d):"dangerouslySetInnerHTML"===c?dt(a,d):"children"===c?ft(a,d):be(a,c,d,u)}switch(o){case"input":Je(a,l);break;case"textarea":lt(a,l);break;case"select":var f=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var p=l.value;null!=p?nt(a,!!l.multiple,p,!1):f!==!!l.multiple&&(null!=l.defaultValue?nt(a,!!l.multiple,l.defaultValue,!0):nt(a,!!l.multiple,l.multiple?[]:"",!1))}a[pl]=l}catch(m){Ec(e,e.return,m)}}break;case 6:if(mu(t,e),gu(e),4&r){if(null===e.stateNode)throw Error(le(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(m){Ec(e,e.return,m)}}break;case 3:if(mu(t,e),gu(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vn(t.containerInfo)}catch(m){Ec(e,e.return,m)}break;case 4:default:mu(t,e),gu(e);break;case 13:mu(t,e),gu(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||($u=Xt())),4&r&&hu(e);break;case 22:if(c=null!==n&&null!==n.memoizedState,1&e.mode?(Gs=(u=Gs)||c,mu(t,e),Gs=u):mu(t,e),gu(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!c&&1&e.mode)for(Js=e,c=e.child;null!==c;){for(d=Js=c;null!==Js;){switch(p=(f=Js).child,f.tag){case 0:case 11:case 14:case 15:nu(4,f,f.return);break;case 1:Zs(f,f.return);var h=f.stateNode;if("function"==typeof h.componentWillUnmount){r=f,n=f.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(m){Ec(r,n,m)}}break;case 5:Zs(f,f.return);break;case 22:if(null!==f.memoizedState){wu(d);continue}}null!==p?(p.return=f,Js=p):wu(d)}c=c.sibling}e:for(c=null,d=e;;){if(5===d.tag){if(null===c){c=d;try{a=d.stateNode,u?"function"==typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(o=d.stateNode,i=null!=(s=d.memoizedProps.style)&&s.hasOwnProperty("display")?s.display:null,o.style.display=mt("display",i))}catch(m){Ec(e,e.return,m)}}}else if(6===d.tag){if(null===c)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(m){Ec(e,e.return,m)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===e)&&null!==d.child){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;null===d.sibling;){if(null===d.return||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:mu(t,e),gu(e),4&r&&hu(e);case 21:}}function gu(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(iu(n)){var r=n;break e}n=n.return}throw Error(le(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(ft(a,""),r.flags&=-33),uu(e,ou(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;su(e,ou(e),l);break;default:throw Error(le(161))}}catch(i){Ec(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yu(e,t,n){Js=e,bu(e)}function bu(e,t,n){for(var r=!!(1&e.mode);null!==Js;){var a=Js,l=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Ys;if(!i){var o=a.alternate,s=null!==o&&null!==o.memoizedState||Gs;o=Ys;var u=Gs;if(Ys=i,(Gs=s)&&!u)for(Js=a;null!==Js;)s=(i=Js).child,22===i.tag&&null!==i.memoizedState?ku(a):null!==s?(s.return=i,Js=s):ku(a);for(;null!==l;)Js=l,bu(l),l=l.sibling;Js=a,Ys=o,Gs=u}xu(e)}else 8772&a.subtreeFlags&&null!==l?(l.return=a,Js=l):xu(e)}}function xu(e){for(;null!==Js;){var t=Js;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Gs||ru(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Gs)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Vi(t,l,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vi(t,i,n)}break;case 5:var o=t.stateNode;if(null===n&&4&t.flags){n=o;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var c=u.memoizedState;if(null!==c){var d=c.dehydrated;null!==d&&Vn(d)}}}break;default:throw Error(le(163))}Gs||512&t.flags&&au(t)}catch(f){Ec(t,t.return,f)}}if(t===e){Js=null;break}if(null!==(n=t.sibling)){n.return=t.return,Js=n;break}Js=t.return}}function wu(e){for(;null!==Js;){var t=Js;if(t===e){Js=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Js=n;break}Js=t.return}}function ku(e){for(;null!==Js;){var t=Js;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ru(4,t)}catch(s){Ec(t,n,s)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Ec(t,a,s)}}var l=t.return;try{au(t)}catch(s){Ec(t,l,s)}break;case 5:var i=t.return;try{au(t)}catch(s){Ec(t,i,s)}}}catch(s){Ec(t,t.return,s)}if(t===e){Js=null;break}var o=t.sibling;if(null!==o){o.return=t.return,Js=o;break}Js=t.return}}var Su,Eu=Math.ceil,ju=xe.ReactCurrentDispatcher,Cu=xe.ReactCurrentOwner,Nu=xe.ReactCurrentBatchConfig,_u=0,Pu=null,Tu=null,Lu=0,zu=0,Ru=El(0),Ou=0,Du=null,Fu=0,Mu=0,Iu=0,Uu=null,Au=null,$u=0,Bu=1/0,Vu=null,Wu=!1,Hu=null,Qu=null,qu=!1,Ku=null,Yu=0,Gu=0,Xu=null,Ju=-1,Zu=0;function ec(){return 6&_u?Xt():-1!==Ju?Ju:Ju=Xt()}function tc(e){return 1&e.mode?2&_u&&0!==Lu?Lu&-Lu:null!==mi.transition?(0===Zu&&(Zu=vn()),Zu):0!==(e=xn)?e:e=void 0===(e=window.event)?16:Xn(e.type):1}function nc(e,t,n,r){if(50<Gu)throw Gu=0,Xu=null,Error(le(185));yn(e,n,r),2&_u&&e===Pu||(e===Pu&&(!(2&_u)&&(Mu|=n),4===Ou&&oc(e,Lu)),rc(e,r),1===n&&0===_u&&!(1&t.mode)&&(Bu=Xt()+500,Ul&&Bl()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var i=31-on(l),o=1<<i,s=a[i];-1===s?0!==(o&n)&&0===(o&r)||(a[i]=hn(o,t)):s<=t&&(e.expiredLanes|=o),l&=~o}}(e,t);var r=pn(e,e===Pu?Lu:0);if(0===r)null!==n&&Kt(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Kt(n),1===t)0===e.tag?function(e){Ul=!0,$l(e)}(sc.bind(null,e)):$l(sc.bind(null,e)),il((function(){!(6&_u)&&Bl()})),n=null;else{switch(wn(r)){case 1:n=Zt;break;case 4:n=en;break;case 16:default:n=tn;break;case 536870912:n=rn}n=Pc(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Ju=-1,Zu=0,6&_u)throw Error(le(327));var n=e.callbackNode;if(kc()&&e.callbackNode!==n)return null;var r=pn(e,e===Pu?Lu:0);if(0===r)return null;if(30&r||0!==(r&e.expiredLanes)||t)t=vc(e,r);else{t=r;var a=_u;_u|=2;var l=hc();for(Pu===e&&Lu===t||(Vu=null,Bu=Xt()+500,fc(e,t));;)try{yc();break}catch(o){pc(e,o)}Ci(),ju.current=l,_u=a,null!==Tu?t=0:(Pu=null,Lu=0,t=Ou)}if(0!==t){if(2===t&&(0!==(a=mn(e))&&(r=a,t=lc(e,a))),1===t)throw n=Du,fc(e,0),oc(e,r),rc(e,Xt()),n;if(6===t)oc(e,r);else{if(a=e.current.alternate,!(30&r||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!oa(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)||(t=vc(e,r),2===t&&(l=mn(e),0!==l&&(r=l,t=lc(e,l))),1!==t)))throw n=Du,fc(e,0),oc(e,r),rc(e,Xt()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(le(345));case 2:case 5:wc(e,Au,Vu);break;case 3:if(oc(e,r),(130023424&r)===r&&10<(t=$u+500-Xt())){if(0!==pn(e,0))break;if(((a=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=rl(wc.bind(null,e,Au,Vu),t);break}wc(e,Au,Vu);break;case 4:if(oc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-on(r);l=1<<i,(i=t[i])>a&&(a=i),r&=~l}if(r=a,10<(r=(120>(r=Xt()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Eu(r/1960))-r)){e.timeoutHandle=rl(wc.bind(null,e,Au,Vu),r);break}wc(e,Au,Vu);break;default:throw Error(le(329))}}}return rc(e,Xt()),e.callbackNode===n?ac.bind(null,e):null}function lc(e,t){var n=Uu;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=vc(e,t))&&(t=Au,Au=n,null!==t&&ic(t)),e}function ic(e){null===Au?Au=e:Au.push.apply(Au,e)}function oc(e,t){for(t&=~Iu,t&=~Mu,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-on(t),r=1<<n;e[n]=-1,t&=~r}}function sc(e){if(6&_u)throw Error(le(327));kc();var t=pn(e,0);if(!(1&t))return rc(e,Xt()),null;var n=vc(e,t);if(0!==e.tag&&2===n){var r=mn(e);0!==r&&(t=r,n=lc(e,r))}if(1===n)throw n=Du,fc(e,0),oc(e,t),rc(e,Xt()),n;if(6===n)throw Error(le(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Au,Vu),rc(e,Xt()),null}function uc(e,t){var n=_u;_u|=1;try{return e(t)}finally{0===(_u=n)&&(Bu=Xt()+500,Ul&&Bl())}}function cc(e){null!==Ku&&0===Ku.tag&&!(6&_u)&&kc();var t=_u;_u|=1;var n=Nu.transition,r=xn;try{if(Nu.transition=null,xn=1,e)return e()}finally{xn=r,Nu.transition=n,!(6&(_u=t))&&Bl()}}function dc(){zu=Ru.current,jl(Ru)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,al(n)),null!==Tu)for(n=Tu.return;null!==n;){var r=n;switch(ti(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Rl();break;case 3:Gi(),jl(Pl),jl(_l),no();break;case 5:Ji(r);break;case 4:Gi();break;case 13:case 19:jl(Zi);break;case 10:Ni(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Pu=e,Tu=e=Rc(e.current,null),Lu=zu=t,Ou=0,Du=null,Iu=Mu=Fu=0,Au=Uu=null,null!==Li){for(t=0;t<Li.length;t++)if(null!==(r=(n=Li[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var i=l.next;l.next=a,r.next=i}n.pending=r}Li=null}return e}function pc(e,t){for(;;){var n=Tu;try{if(Ci(),ro.current=Jo,uo){for(var r=io.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(lo=0,so=oo=io=null,co=!1,fo=0,Cu.current=null,null===n||null===n.return){Ou=1,Du=t,Tu=null;break}e:{var l=e,i=n.return,o=n,s=t;if(t=Lu,o.flags|=32768,null!==s&&"object"==typeof s&&"function"==typeof s.then){var u=s,c=o,d=c.tag;if(!(1&c.mode||0!==d&&11!==d&&15!==d)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var p=vs(i);if(null!==p){p.flags&=-257,gs(p,i,o,0,t),1&p.mode&&ms(l,u,t),s=u;var h=(t=p).updateQueue;if(null===h){var m=new Set;m.add(s),t.updateQueue=m}else h.add(s);break e}if(!(1&t)){ms(l,u,t),mc();break e}s=Error(le(426))}else if(ai&&1&o.mode){var v=vs(i);if(null!==v){!(65536&v.flags)&&(v.flags|=256),gs(v,i,o,0,t),hi(us(s,o));break e}}l=s=us(s,o),4!==Ou&&(Ou=2),null===Uu?Uu=[l]:Uu.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,$i(l,ps(0,s,t));break e;case 1:o=s;var g=l.type,y=l.stateNode;if(!(128&l.flags||"function"!=typeof g.getDerivedStateFromError&&(null===y||"function"!=typeof y.componentDidCatch||null!==Qu&&Qu.has(y)))){l.flags|=65536,t&=-t,l.lanes|=t,$i(l,hs(l,o,t));break e}}l=l.return}while(null!==l)}xc(n)}catch(b){t=b,Tu===n&&null!==n&&(Tu=n=n.return);continue}break}}function hc(){var e=ju.current;return ju.current=Jo,null===e?Jo:e}function mc(){0!==Ou&&3!==Ou&&2!==Ou||(Ou=4),null===Pu||!(268435455&Fu)&&!(268435455&Mu)||oc(Pu,Lu)}function vc(e,t){var n=_u;_u|=2;var r=hc();for(Pu===e&&Lu===t||(Vu=null,fc(e,t));;)try{gc();break}catch(a){pc(e,a)}if(Ci(),_u=n,ju.current=r,null!==Tu)throw Error(le(261));return Pu=null,Lu=0,Ou}function gc(){for(;null!==Tu;)bc(Tu)}function yc(){for(;null!==Tu&&!Yt();)bc(Tu)}function bc(e){var t=Su(e.alternate,e,zu);e.memoizedProps=e.pendingProps,null===t?xc(e):Tu=t,Cu.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ks(n,t)))return n.flags&=32767,void(Tu=n);if(null===e)return Ou=6,void(Tu=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=qs(n,t,zu)))return void(Tu=n);if(null!==(t=t.sibling))return void(Tu=t);Tu=t=e}while(null!==t);0===Ou&&(Ou=5)}function wc(e,t,n){var r=xn,a=Nu.transition;try{Nu.transition=null,xn=1,function(e,t,n,r){do{kc()}while(null!==Ku);if(6&_u)throw Error(le(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(le(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-on(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,l),e===Pu&&(Tu=Pu=null,Lu=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||qu||(qu=!0,Pc(tn,(function(){return kc(),null}))),l=!!(15990&n.flags),!!(15990&n.subtreeFlags)||l){l=Nu.transition,Nu.transition=null;var i=xn;xn=1;var o=_u;_u|=4,Cu.current=null,function(e,t){if(el=Hn,pa(e=fa())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(x){n=null;break e}var i=0,o=-1,s=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var p;d!==n||0!==a&&3!==d.nodeType||(o=i+a),d!==l||0!==r&&3!==d.nodeType||(s=i+r),3===d.nodeType&&(i+=d.nodeValue.length),null!==(p=d.firstChild);)f=d,d=p;for(;;){if(d===e)break t;if(f===n&&++u===a&&(o=i),f===l&&++c===r&&(s=i),null!==(p=d.nextSibling))break;f=(d=f).parentNode}d=p}n=-1===o||-1===s?null:{start:o,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(tl={focusedElem:e,selectionRange:n},Hn=!1,Js=t;null!==Js;)if(e=(t=Js).child,1028&t.subtreeFlags&&null!==e)e.return=t,Js=e;else for(;null!==Js;){t=Js;try{var h=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var m=h.memoizedProps,v=h.memoizedState,g=t.stateNode,y=g.getSnapshotBeforeUpdate(t.elementType===t.type?m:ns(t.type,m),v);g.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(le(163))}}catch(x){Ec(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Js=e;break}Js=t.return}h=tu,tu=!1}(e,n),vu(n,e),ha(tl),Hn=!!el,tl=el=null,e.current=n,yu(n),Gt(),_u=o,xn=i,Nu.transition=l}else e.current=n;if(qu&&(qu=!1,Ku=e,Yu=a),l=e.pendingLanes,0===l&&(Qu=null),function(e){if(ln&&"function"==typeof ln.onCommitFiberRoot)try{ln.onCommitFiberRoot(an,e,void 0,!(128&~e.current.flags))}catch(t){}}(n.stateNode),rc(e,Xt()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Wu)throw Wu=!1,e=Hu,Hu=null,e;!!(1&Yu)&&0!==e.tag&&kc(),l=e.pendingLanes,1&l?e===Xu?Gu++:(Gu=0,Xu=e):Gu=0,Bl()}(e,t,n,r)}finally{Nu.transition=a,xn=r}return null}function kc(){if(null!==Ku){var e=wn(Yu),t=Nu.transition,n=xn;try{if(Nu.transition=null,xn=16>e?16:e,null===Ku)var r=!1;else{if(e=Ku,Ku=null,Yu=0,6&_u)throw Error(le(331));var a=_u;for(_u|=4,Js=e.current;null!==Js;){var l=Js,i=l.child;if(16&Js.flags){var o=l.deletions;if(null!==o){for(var s=0;s<o.length;s++){var u=o[s];for(Js=u;null!==Js;){var c=Js;switch(c.tag){case 0:case 11:case 15:nu(8,c,l)}var d=c.child;if(null!==d)d.return=c,Js=d;else for(;null!==Js;){var f=(c=Js).sibling,p=c.return;if(lu(c),c===u){Js=null;break}if(null!==f){f.return=p,Js=f;break}Js=p}}}var h=l.alternate;if(null!==h){var m=h.child;if(null!==m){h.child=null;do{var v=m.sibling;m.sibling=null,m=v}while(null!==m)}}Js=l}}if(2064&l.subtreeFlags&&null!==i)i.return=l,Js=i;else e:for(;null!==Js;){if(2048&(l=Js).flags)switch(l.tag){case 0:case 11:case 15:nu(9,l,l.return)}var g=l.sibling;if(null!==g){g.return=l.return,Js=g;break e}Js=l.return}}var y=e.current;for(Js=y;null!==Js;){var b=(i=Js).child;if(2064&i.subtreeFlags&&null!==b)b.return=i,Js=b;else e:for(i=y;null!==Js;){if(2048&(o=Js).flags)try{switch(o.tag){case 0:case 11:case 15:ru(9,o)}}catch(w){Ec(o,o.return,w)}if(o===i){Js=null;break e}var x=o.sibling;if(null!==x){x.return=o.return,Js=x;break e}Js=o.return}}if(_u=a,Bl(),ln&&"function"==typeof ln.onPostCommitFiberRoot)try{ln.onPostCommitFiberRoot(an,e)}catch(w){}r=!0}return r}finally{xn=n,Nu.transition=t}}return!1}function Sc(e,t,n){e=Ui(e,t=ps(0,t=us(n,t),1),1),t=ec(),null!==e&&(yn(e,1,t),rc(e,t))}function Ec(e,t,n){if(3===e.tag)Sc(e,e,n);else for(;null!==t;){if(3===t.tag){Sc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Qu||!Qu.has(r))){t=Ui(t,e=hs(t,e=us(n,e),1),1),e=ec(),null!==t&&(yn(t,1,e),rc(t,e));break}}t=t.return}}function jc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Pu===e&&(Lu&n)===n&&(4===Ou||3===Ou&&(130023424&Lu)===Lu&&500>Xt()-$u?fc(e,0):Iu|=n),rc(e,t)}function Cc(e,t){0===t&&(1&e.mode?(t=dn,!(130023424&(dn<<=1))&&(dn=4194304)):t=1);var n=ec();null!==(e=Oi(e,t))&&(yn(e,t,n),rc(e,n))}function Nc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cc(e,n)}function _c(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(le(314))}null!==r&&r.delete(t),Cc(e,n)}function Pc(e,t){return qt(e,t)}function Tc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lc(e,t,n,r){return new Tc(e,t,n,r)}function zc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Rc(e,t){var n=e.alternate;return null===n?((n=Lc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Oc(e,t,n,r,a,l){var i=2;if(r=e,"function"==typeof e)zc(e)&&(i=1);else if("string"==typeof e)i=5;else e:switch(e){case Se:return Dc(n.children,a,l,t);case Ee:i=8,a|=8;break;case je:return(e=Lc(12,n,t,2|a)).elementType=je,e.lanes=l,e;case Pe:return(e=Lc(13,n,t,a)).elementType=Pe,e.lanes=l,e;case Te:return(e=Lc(19,n,t,a)).elementType=Te,e.lanes=l,e;case Re:return Fc(n,a,l,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case Ce:i=10;break e;case Ne:i=9;break e;case _e:i=11;break e;case Le:i=14;break e;case ze:i=16,r=null;break e}throw Error(le(130,null==e?e:typeof e,""))}return(t=Lc(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Dc(e,t,n,r){return(e=Lc(7,e,r,t)).lanes=n,e}function Fc(e,t,n,r){return(e=Lc(22,e,r,t)).elementType=Re,e.lanes=n,e.stateNode={isHidden:!1},e}function Mc(e,t,n){return(e=Lc(6,e,null,t)).lanes=n,e}function Ic(e,t,n){return(t=Lc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gn(0),this.expirationTimes=gn(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gn(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Ac(e,t,n,r,a,l,i,o,s){return e=new Uc(e,t,n,o,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Lc(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fi(l),e}function $c(e){if(!e)return Nl;e:{if(Bt(e=e._reactInternals)!==e||1!==e.tag)throw Error(le(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(zl(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(le(171))}if(1===e.tag){var n=e.type;if(zl(n))return Dl(e,n,t)}return t}function Bc(e,t,n,r,a,l,i,o,s){return(e=Ac(n,r,!0,e,0,l,0,o,s)).context=$c(null),n=e.current,(l=Ii(r=ec(),a=tc(n))).callback=null!=t?t:null,Ui(n,l,a),e.current.lanes=a,yn(e,a,r),rc(e,r),e}function Vc(e,t,n,r){var a=t.current,l=ec(),i=tc(a);return n=$c(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ii(l,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ui(a,t,i))&&(nc(e,a,i,l),Ai(e,a,i)),i}function Wc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Qc(e,t){Hc(e,t),(e=e.alternate)&&Hc(e,t)}Su=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pl.current)bs=!0;else{if(0===(e.lanes&n)&&!(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Ps(t),pi();break;case 5:Xi(t);break;case 1:zl(t.type)&&Fl(t);break;case 4:Yi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Cl(ki,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Cl(Zi,1&Zi.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ms(e,t,n):(Cl(Zi,1&Zi.current),null!==(e=Ws(e,t,n))?e.sibling:null);Cl(Zi,1&Zi.current);break;case 19:if(r=0!==(n&t.childLanes),128&e.flags){if(r)return Bs(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Cl(Zi,Zi.current),r)break;return null;case 22:case 23:return t.lanes=0,Es(e,t,n)}return Ws(e,t,n)}(e,t,n);bs=!!(131072&e.flags)}else bs=!1,ai&&1048576&t.flags&&Zl(t,Ql,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vs(e,t),e=t.pendingProps;var a=Ll(t,_l.current);Pi(t,n),a=vo(null,t,r,e,a,n);var l=go();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,zl(r)?(l=!0,Fl(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fi(t),a.updater=as,t.stateNode=a,a._reactInternals=t,ss(t,r,e,n),t=_s(null,t,r,!0,l,n)):(t.tag=0,ai&&l&&ei(t),xs(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vs(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return zc(e)?1:0;if(null!=e){if((e=e.$$typeof)===_e)return 11;if(e===Le)return 14}return 2}(r),e=ns(r,e),a){case 0:t=Cs(null,t,r,e,n);break e;case 1:t=Ns(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ns(r.type,e),n);break e}throw Error(le(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Cs(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ns(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 3:e:{if(Ps(t),null===e)throw Error(le(387));r=t.pendingProps,a=(l=t.memoizedState).element,Mi(e,t),Bi(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Ts(e,t,r,n,a=us(Error(le(423)),t));break e}if(r!==a){t=Ts(e,t,r,n,a=us(Error(le(424)),t));break e}for(ri=ul(t.stateNode.containerInfo.firstChild),ni=t,ai=!0,li=null,n=wi(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pi(),r===a){t=Ws(e,t,n);break e}xs(e,t,r,n)}t=t.child}return t;case 5:return Xi(t),null===e&&ui(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,i=a.children,nl(r,a)?i=null:null!==l&&nl(r,l)&&(t.flags|=32),js(e,t),xs(e,t,i,n),t.child;case 6:return null===e&&ui(t),null;case 13:return Ms(e,t,n);case 4:return Yi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xi(t,null,r,n):xs(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ws(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 7:return xs(e,t,t.pendingProps,n),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,i=a.value,Cl(ki,r._currentValue),r._currentValue=i,null!==l)if(oa(l.value,i)){if(l.children===a.children&&!Pl.current){t=Ws(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var o=l.dependencies;if(null!==o){i=l.child;for(var s=o.firstContext;null!==s;){if(s.context===r){if(1===l.tag){(s=Ii(-1,n&-n)).tag=2;var u=l.updateQueue;if(null!==u){var c=(u=u.shared).pending;null===c?s.next=s:(s.next=c.next,c.next=s),u.pending=s}}l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),_i(l.return,n,t),o.lanes|=n;break}s=s.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(le(341));i.lanes|=n,null!==(o=i.alternate)&&(o.lanes|=n),_i(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}xs(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Pi(t,n),r=r(a=Ti(a)),t.flags|=1,xs(e,t,r,n),t.child;case 14:return a=ns(r=t.type,t.pendingProps),ks(e,t,r,a=ns(r.type,a),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ns(r,a),Vs(e,t),t.tag=1,zl(r)?(e=!0,Fl(t)):e=!1,Pi(t,n),is(t,r,a),ss(t,r,a,n),_s(null,t,r,!0,e,n);case 19:return Bs(e,t,n);case 22:return Es(e,t,n)}throw Error(le(156,t.tag))};var qc="function"==typeof reportError?reportError:function(e){console.error(e)};function Kc(e){this._internalRoot=e}function Yc(e){this._internalRoot=e}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Jc(){}function Zc(e,t,n,r,a){var l=n._reactRootContainer;if(l){var i=l;if("function"==typeof a){var o=a;a=function(){var e=Wc(i);o.call(e)}}Vc(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"==typeof r){var l=r;r=function(){var e=Wc(i);l.call(e)}}var i=Bc(t,r,e,0,null,!1,0,"",Jc);return e._reactRootContainer=i,e[hl]=i.current,Ba(8===e.nodeType?e.parentNode:e),cc(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"==typeof r){var o=r;r=function(){var e=Wc(s);o.call(e)}}var s=Ac(e,0,!1,null,0,!1,0,"",Jc);return e._reactRootContainer=s,e[hl]=s.current,Ba(8===e.nodeType?e.parentNode:e),cc((function(){Vc(t,s,n,r)})),s}(n,t,e,a,r);return Wc(i)}Yc.prototype.render=Kc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(le(409));Vc(e,t,null,null)},Yc.prototype.unmount=Kc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cc((function(){Vc(null,e,null,null)})),t[hl]=null}},Yc.prototype.unstable_scheduleHydration=function(e){if(e){var t=jn();e={blockedOn:null,target:e,priority:t};for(var n=0;n<On.length&&0!==t&&t<On[n].priority;n++);On.splice(n,0,e),0===n&&In(e)}},kn=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fn(t.pendingLanes);0!==n&&(bn(t,1|n),rc(t,Xt()),!(6&_u)&&(Bu=Xt()+500,Bl()))}break;case 13:cc((function(){var t=Oi(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}})),Qc(e,1)}},Sn=function(e){if(13===e.tag){var t=Oi(e,134217728);if(null!==t)nc(t,e,134217728,ec());Qc(e,134217728)}},En=function(e){if(13===e.tag){var t=tc(e),n=Oi(e,t);if(null!==n)nc(n,e,t,ec());Qc(e,t)}},jn=function(){return xn},Cn=function(e,t){var n=xn;try{return xn=e,t()}finally{xn=n}},kt=function(e,t,n){switch(t){case"input":if(Je(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wl(r);if(!a)throw Error(le(90));qe(r),Je(r,a)}}}break;case"textarea":lt(e,n);break;case"select":null!=(t=n.value)&&nt(e,!!n.multiple,t,!1)}},_t=uc,Pt=cc;var ed={usingClientEntryPoint:!1,Events:[bl,xl,wl,Ct,Nt,uc]},td={findFiberByHostInstance:yl,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nd={bundleType:td.bundleType,version:td.version,rendererPackageName:td.rendererPackageName,rendererConfig:td.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xe.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ht(e))?null:e.stateNode},findFiberByHostInstance:td.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rd=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rd.isDisabled&&rd.supportsFiber)try{an=rd.inject(nd),ln=rd}catch(ct){}}Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ed,Z.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gc(t))throw Error(le(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:ke,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},Z.createRoot=function(e,t){if(!Gc(e))throw Error(le(299));var n=!1,r="",a=qc;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Ac(e,1,!1,null,0,n,0,r,a),e[hl]=t.current,Ba(8===e.nodeType?e.parentNode:e),new Kc(t)},Z.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(le(188));throw e=Object.keys(e).join(","),Error(le(268,e))}return e=null===(e=Ht(t))?null:e.stateNode},Z.flushSync=function(e){return cc(e)},Z.hydrate=function(e,t,n){if(!Xc(t))throw Error(le(200));return Zc(null,e,t,!0,n)},Z.hydrateRoot=function(e,t,n){if(!Gc(e))throw Error(le(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",i=qc;if(null!=n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Bc(t,null,e,1,null!=n?n:null,a,0,l,i),e[hl]=t.current,Ba(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Yc(t)},Z.render=function(e,t,n){if(!Xc(t))throw Error(le(200));return Zc(null,e,t,!1,n)},Z.unmountComponentAtNode=function(e){if(!Xc(e))throw Error(le(40));return!!e._reactRootContainer&&(cc((function(){Zc(null,null,e,!1,(function(){e._reactRootContainer=null,e[hl]=null}))})),!0)},Z.unstable_batchedUpdates=uc,Z.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xc(n))throw Error(le(200));if(null==e||void 0===e._reactInternals)throw Error(le(38));return Zc(e,t,n,!1,r)},Z.version="18.3.1-next-f1338f8080-20240426",function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),J.exports=Z;var ad,ld,id=J.exports;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function od(){return od=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},od.apply(this,arguments)}X.createRoot=id.createRoot,X.hydrateRoot=id.hydrateRoot,(ld=ad||(ad={})).Pop="POP",ld.Push="PUSH",ld.Replace="REPLACE";const sd="popstate";function ud(e){return void 0===e&&(e={}),function(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,o=ad.Pop,s=null,u=c();null==u&&(u=0,i.replaceState(od({},i.state,{idx:u}),""));function c(){return(i.state||{idx:null}).idx}function d(){o=ad.Pop;let e=c(),t=null==e?null:e-u;u=e,s&&s({action:o,location:m.location,delta:t})}function f(e,t){o=ad.Push;let r=pd(m.location,e,t);n&&n(r,e),u=c()+1;let d=fd(r,u),f=m.createHref(r);try{i.pushState(d,"",f)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;a.location.assign(f)}l&&s&&s({action:o,location:m.location,delta:1})}function p(e,t){o=ad.Replace;let r=pd(m.location,e,t);n&&n(r,e),u=c();let a=fd(r,u),d=m.createHref(r);i.replaceState(a,"",d),l&&s&&s({action:o,location:m.location,delta:0})}function h(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:hd(e);return n=n.replace(/ $/,"%20"),cd(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let m={get action(){return o},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(sd,d),s=e,()=>{a.removeEventListener(sd,d),s=null}},createHref:e=>t(a,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:f,replace:p,go:e=>i.go(e)};return m}((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return pd("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:hd(t)}),null,e)}function cd(e,t){if(!1===e||null==e)throw new Error(t)}function dd(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function fd(e,t){return{usr:e.state,key:e.key,idx:t}}function pd(e,t,n,r){return void 0===n&&(n=null),od({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?md(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function hd(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function md(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var vd,gd;function yd(e,t,n){return void 0===n&&(n="/"),function(e,t,n,r){let a="string"==typeof t?md(t):t,l=zd(a.pathname||"/",n);if(null==l)return null;let i=bd(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let o=null;for(let s=0;null==o&&s<i.length;++s){let e=Ld(l);o=Pd(i[s],e,r)}return o}(e,t,n,!1)}function bd(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let i={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(cd(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let o=Fd([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(cd(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),bd(e.children,t,s,o)),(null!=e.path||e.index)&&t.push({path:o,score:_d(o,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of xd(e.path))a(e,t,r);else a(e,t)})),t}function xd(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let i=xd(r.join("/")),o=[];return o.push(...i.map((e=>""===e?l:[l,e].join("/")))),a&&o.push(...i),o.map((t=>e.startsWith("/")&&""===t?"/":t))}(gd=vd||(vd={})).data="data",gd.deferred="deferred",gd.redirect="redirect",gd.error="error";const wd=/^:[\w-]+$/,kd=3,Sd=2,Ed=1,jd=10,Cd=-2,Nd=e=>"*"===e;function _d(e,t){let n=e.split("/"),r=n.length;return n.some(Nd)&&(r+=Cd),t&&(r+=Sd),n.filter((e=>!Nd(e))).reduce(((e,t)=>e+(wd.test(t)?kd:""===t?Ed:jd)),r)}function Pd(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",i=[];for(let o=0;o<r.length;++o){let e=r[o],s=o===r.length-1,u="/"===l?t:t.slice(l.length)||"/",c=Td({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=Td({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:Fd([l,c.pathname]),pathnameBase:Md(Fd([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=Fd([l,c.pathnameBase]))}return i}function Td(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);dd("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=o[n]||"";i=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=o[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:l,pathnameBase:i,pattern:e}}function Ld(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return dd(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function zd(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function Rd(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Od(e,t){let n=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function Dd(e,t,n,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=md(e):(a=od({},e),cd(!a.pathname||!a.pathname.includes("?"),Rd("?","pathname","search",a)),cd(!a.pathname||!a.pathname.includes("#"),Rd("#","pathname","hash",a)),cd(!a.search||!a.search.includes("#"),Rd("#","search","hash",a)));let l,i=""===e||""===a.pathname,o=i?"/":a.pathname;if(null==o)l=n;else{let e=t.length-1;if(!r&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"==typeof e?md(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:Id(r),hash:Ud(a)}}(a,l),u=o&&"/"!==o&&o.endsWith("/"),c=(i||"."===o)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}const Fd=e=>e.join("/").replace(/\/\/+/g,"/"),Md=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Id=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Ud=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Ad=["post","put","patch","delete"];new Set(Ad);const $d=["get",...Ad];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function Bd(){return Bd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Bd.apply(this,arguments)}new Set($d);const Vd=A.createContext(null),Wd=A.createContext(null),Hd=A.createContext(null),Qd=A.createContext(null),qd=A.createContext({outlet:null,matches:[],isDataRoute:!1}),Kd=A.createContext(null);function Yd(){return null!=A.useContext(Qd)}function Gd(){return Yd()||cd(!1),A.useContext(Qd).location}function Xd(e){A.useContext(Hd).static||A.useLayoutEffect(e)}function Jd(){let{isDataRoute:e}=A.useContext(qd);return e?function(){let{router:e}=function(){let e=A.useContext(Vd);return e||cd(!1),e}(lf.UseNavigateStable),t=sf(of.UseNavigateStable),n=A.useRef(!1);return Xd((()=>{n.current=!0})),A.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"==typeof r?e.navigate(r):e.navigate(r,Bd({fromRouteId:t},a)))}),[e,t])}():function(){Yd()||cd(!1);let e=A.useContext(Vd),{basename:t,future:n,navigator:r}=A.useContext(Hd),{matches:a}=A.useContext(qd),{pathname:l}=Gd(),i=JSON.stringify(Od(a,n.v7_relativeSplatPath)),o=A.useRef(!1);return Xd((()=>{o.current=!0})),A.useCallback((function(n,a){if(void 0===a&&(a={}),!o.current)return;if("number"==typeof n)return void r.go(n);let s=Dd(n,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:Fd([t,s.pathname])),(a.replace?r.replace:r.push)(s,a.state,a)}),[t,r,i,l,e])}()}function Zd(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=A.useContext(Hd),{matches:a}=A.useContext(qd),{pathname:l}=Gd(),i=JSON.stringify(Od(a,r.v7_relativeSplatPath));return A.useMemo((()=>Dd(e,JSON.parse(i),l,"path"===n)),[e,i,l,n])}function ef(e,t){return function(e,t,n,r){Yd()||cd(!1);let{navigator:a}=A.useContext(Hd),{matches:l}=A.useContext(qd),i=l[l.length-1],o=i?i.params:{};!i||i.pathname;let s=i?i.pathnameBase:"/";i&&i.route;let u,c=Gd();if(t){var d;let e="string"==typeof t?md(t):t;"/"===s||(null==(d=e.pathname)?void 0:d.startsWith(s))||cd(!1),u=e}else u=c;let f=u.pathname||"/",p=f;if("/"!==s){let e=s.replace(/^\//,"").split("/");p="/"+f.replace(/^\//,"").split("/").slice(e.length).join("/")}let h=yd(e,{pathname:p}),m=function(e,t,n,r){var a;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===r&&(r=null);if(null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=r)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,o=null==(a=n)?void 0:a.errors;if(null!=o){let e=i.findIndex((e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id])));e>=0||cd(!1),i=i.slice(0,Math.min(i.length,e+1))}let s=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<i.length;c++){let e=i[c];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=c),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){s=!0,i=u>=0?i.slice(0,u+1):[i[0]];break}}}return i.reduceRight(((e,r,a)=>{let l,c=!1,d=null,f=null;var p;n&&(l=o&&r.route.id?o[r.route.id]:void 0,d=r.route.errorElement||nf,s&&(u<0&&0===a?(p="route-fallback",!1||uf[p]||(uf[p]=!0),c=!0,f=null):u===a&&(c=!0,f=r.route.hydrateFallbackElement||null)));let h=t.concat(i.slice(0,a+1)),m=()=>{let t;return t=l?d:c?f:r.route.Component?A.createElement(r.route.Component,null):r.route.element?r.route.element:e,A.createElement(af,{match:r,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?A.createElement(rf,{location:n.location,revalidation:n.revalidation,component:d,error:l,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}(h&&h.map((e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:Fd([s,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?s:Fd([s,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);if(t&&m)return A.createElement(Qd.Provider,{value:{location:Bd({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:ad.Pop}},m);return m}(e,t)}function tf(){let e=function(){var e;let t=A.useContext(Kd),n=function(){let e=A.useContext(Wd);return e||cd(!1),e}(of.UseRouteError),r=sf(of.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return A.createElement(A.Fragment,null,A.createElement("h2",null,"Unexpected Application Error!"),A.createElement("h3",{style:{fontStyle:"italic"}},t),n?A.createElement("pre",{style:r},n):null,null)}const nf=A.createElement(tf,null);class rf extends A.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?A.createElement(qd.Provider,{value:this.props.routeContext},A.createElement(Kd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function af(e){let{routeContext:t,match:n,children:r}=e,a=A.useContext(Vd);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),A.createElement(qd.Provider,{value:t},r)}var lf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(lf||{}),of=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(of||{});function sf(e){let t=function(){let e=A.useContext(qd);return e||cd(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||cd(!1),n.route.id}const uf={};function cf(e){let{to:t,replace:n,state:r,relative:a}=e;Yd()||cd(!1);let{future:l,static:i}=A.useContext(Hd),{matches:o}=A.useContext(qd),{pathname:s}=Gd(),u=Jd(),c=Dd(t,Od(o,l.v7_relativeSplatPath),s,"path"===a),d=JSON.stringify(c);return A.useEffect((()=>u(JSON.parse(d),{replace:n,state:r,relative:a})),[u,d,a,n,r]),null}function df(e){cd(!1)}function ff(e){let{basename:t="/",children:n=null,location:r,navigationType:a=ad.Pop,navigator:l,static:i=!1,future:o}=e;Yd()&&cd(!1);let s=t.replace(/^\/*/,"/"),u=A.useMemo((()=>({basename:s,navigator:l,static:i,future:Bd({v7_relativeSplatPath:!1},o)})),[s,o,l,i]);"string"==typeof r&&(r=md(r));let{pathname:c="/",search:d="",hash:f="",state:p=null,key:h="default"}=r,m=A.useMemo((()=>{let e=zd(c,s);return null==e?null:{location:{pathname:e,search:d,hash:f,state:p,key:h},navigationType:a}}),[s,c,d,f,p,h,a]);return null==m?null:A.createElement(Hd.Provider,{value:u},A.createElement(Qd.Provider,{children:n,value:m}))}function pf(e){let{children:t,location:n}=e;return ef(hf(t),n)}function hf(e,t){void 0===t&&(t=[]);let n=[];return A.Children.forEach(e,((e,r)=>{if(!A.isValidElement(e))return;let a=[...t,r];if(e.type===A.Fragment)return void n.push.apply(n,hf(e.props.children,a));e.type!==df&&cd(!1),e.props.index&&e.props.children&&cd(!1);let l={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=hf(e.props.children,a)),n.push(l)})),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function mf(){return mf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mf.apply(this,arguments)}new Promise((()=>{}));const vf=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(cp){}const gf=B.startTransition;function yf(e){let{basename:t,children:n,future:r,window:a}=e,l=A.useRef();null==l.current&&(l.current=ud({window:a,v5Compat:!0}));let i=l.current,[o,s]=A.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},c=A.useCallback((e=>{u&&gf?gf((()=>s(e))):s(e)}),[s,u]);return A.useLayoutEffect((()=>i.listen(c)),[i,c]),A.useEffect((()=>{return null==(e=r)||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),void(t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation));var e,t}),[r]),A.createElement(ff,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:i,future:r})}const bf="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,xf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,wf=A.forwardRef((function(e,t){let n,{onClick:r,relative:a,reloadDocument:l,replace:i,state:o,target:s,to:u,preventScrollReset:c,viewTransition:d}=e,f=function(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,vf),{basename:p}=A.useContext(Hd),h=!1;if("string"==typeof u&&xf.test(u)&&(n=u,bf))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=zd(t.pathname,p);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:h=!0}catch(cp){}let m=function(e,t){let{relative:n}=void 0===t?{}:t;Yd()||cd(!1);let{basename:r,navigator:a}=A.useContext(Hd),{hash:l,pathname:i,search:o}=Zd(e,{relative:n}),s=i;return"/"!==r&&(s="/"===i?r:Fd([r,i])),a.createHref({pathname:s,search:o,hash:l})}(u,{relative:a}),v=function(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:o}=void 0===t?{}:t,s=Jd(),u=Gd(),c=Zd(e,{relative:i});return A.useCallback((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==r?r:hd(u)===hd(c);s(e,{replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:o})}}),[u,s,c,r,a,n,e,l,i,o])}(u,{replace:i,state:o,target:s,preventScrollReset:c,relative:a,viewTransition:d});return A.createElement("a",mf({},f,{href:n||m,onClick:h||l?r:function(e){r&&r(e),e.defaultPrevented||v(e)},ref:t,target:s}))}));var kf,Sf,Ef,jf;(Sf=kf||(kf={})).UseScrollRestoration="useScrollRestoration",Sf.UseSubmit="useSubmit",Sf.UseSubmitFetcher="useSubmitFetcher",Sf.UseFetcher="useFetcher",Sf.useViewTransitionState="useViewTransitionState",(jf=Ef||(Ef={})).UseFetcher="useFetcher",jf.UseFetchers="useFetchers",jf.UseScrollRestoration="useScrollRestoration";const Cf="https://git.chcit.org/database-service/api",Nf={"Content-Type":"application/json",Accept:"application/json"};async function _f(e){if(!e.ok){const t=await e.json().catch((()=>({status:e.status,message:e.statusText})));throw{status:e.status,message:t.message||e.statusText,details:t.details}}return 204===e.status?{}:await e.json()}function Pf(){return localStorage.getItem("auth_token")}function Tf(){const e=Pf();return e?{...Nf,Authorization:`Bearer ${e}`}:Nf}const Lf=async e=>{const t=await fetch(`${Cf}/auth/login`,{method:"POST",headers:Nf,body:JSON.stringify(e)}),n=await _f(t);return n.token&&localStorage.setItem("auth_token",n.token),n},zf=async()=>{if(Pf())try{await fetch(`${Cf}/auth/logout`,{method:"POST",headers:Tf()})}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("auth_token")}},Rf=async()=>{if(!Pf())return!1;try{return(await fetch(`${Cf}/auth/validate`,{method:"GET",headers:Tf()})).ok}catch(e){return console.error("Error validating token:",e),!1}},Of=async()=>_f(await fetch(`${Cf}/credentials`,{method:"GET",headers:Tf()})),Df=async e=>_f(await fetch(`${Cf}/credentials`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Ff=async(e,t)=>_f(await fetch(`${Cf}/credentials/${e}`,{method:"PUT",headers:Tf(),body:JSON.stringify(t)})),Mf=async e=>_f(await fetch(`${Cf}/credentials/${e}`,{method:"DELETE",headers:Tf()})),If=async e=>_f(await fetch(`${Cf}/credentials/${e}/test`,{method:"POST",headers:Tf()})),Uf=async()=>_f(await fetch(`${Cf}/metrics/dashboard`,{method:"GET",headers:Tf()})),Af=async e=>_f(await fetch(`${Cf}/metrics/detailed?timeRange=${e}`,{method:"GET",headers:Tf()})),$f=async()=>_f(await fetch(`${Cf}/settings`,{method:"GET",headers:Tf()})),Bf=async e=>_f(await fetch(`${Cf}/settings`,{method:"PUT",headers:Tf(),body:JSON.stringify(e)})),Vf=A.createContext(void 0),Wf=({children:e})=>{const[t,n]=A.useState(!1),[r,a]=A.useState(null),[l,i]=A.useState(!0),[o,s]=A.useState(null),u=Jd();A.useEffect((()=>{(async()=>{if("true"===sessionStorage.getItem("dev_bypass_active"))return a({id:"1",username:"admin",role:"admin"}),n(!0),i(!1),void("/"===window.location.pathname&&u("/dashboard",{replace:!0}));try{await Rf()?(a({id:"1",username:"admin",role:"admin"}),n(!0),"/"===window.location.pathname&&u("/dashboard",{replace:!0})):(n(!1),a(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&u("/",{replace:!0}))}catch(e){console.error("Error checking authentication status:",e),n(!1),a(null),sessionStorage.removeItem("dev_bypass_active")}finally{i(!1)}})()}),[u]),A.useEffect((()=>{t&&"/"===window.location.pathname&&u("/dashboard",{replace:!0})}),[t,u]);return G.jsx(Vf.Provider,{value:{isAuthenticated:t,user:r,isLoading:l,login:async e=>{if(console.log("[AuthContext] login function initiated. Credentials:",e),i(!0),s(null),console.log("[AuthContext] Checking dev bypass condition..."),"admin"===e.username&&"admin"===e.password){return s("DEV_BYPASS_DEBUG_CHECK"+" (dev bypass active)"),window.__DEV_BYPASS_ACTIVE__=!0,sessionStorage.setItem("dev_bypass_active","true"),setTimeout((()=>{const e=document.createElement("div");e.innerText="⚠️ DEV LOGIN BYPASS ACTIVE: admin/admin",e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.right="0",e.style.background="#ffcc00",e.style.color="#000",e.style.padding="8px",e.style.fontWeight="bold",e.style.zIndex="9999",document.body.appendChild(e)}),100),console.log("[AuthContext] Dev bypass condition MET. Setting user and will navigate after state update."),a({id:"1",username:"admin",role:"admin"}),n(!0),void i(!1)}console.log("[AuthContext] Dev bypass condition NOT MET. Proceeding with API login.");try{const t=await Lf(e);a(t.user),n(!0),u("/dashboard")}catch(t){console.error("Login error:",t),s("Invalid username or password"),n(!1),a(null)}finally{i(!1)}},logout:async()=>{i(!0);try{await zf()}catch(e){console.error("Logout error:",e)}finally{n(!1),a(null),i(!1),sessionStorage.removeItem("dev_bypass_active"),u("/")}},error:o},children:e})},Hf=()=>{const e=A.useContext(Vf);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Qf=()=>{const{isAuthenticated:e,user:t,logout:n}=Hf(),[r,a]=A.useState(!1);A.useEffect((()=>{t&&"admin"===t.role?a(!0):a(!1)}),[t]);return G.jsxs("header",{className:"header",children:[G.jsx("div",{className:"logo",children:G.jsx(wf,{to:e?"/dashboard":"/",children:"Database Service"})}),G.jsx("nav",{className:"nav",children:e?G.jsxs(G.Fragment,{children:[G.jsx(wf,{to:"/dashboard",className:"nav-link",children:"Dashboard"}),G.jsx(wf,{to:"/databases",className:"nav-link",children:"Databases"}),G.jsx(wf,{to:"/credentials",className:"nav-link",children:"Credentials"}),G.jsx(wf,{to:"/metrics",className:"nav-link",children:"Metrics"}),r&&G.jsx(wf,{to:"/settings",className:"nav-link",children:"Settings"}),G.jsx("button",{onClick:async()=>{await n()},className:"logout-btn",children:"Logout"})]}):G.jsx(wf,{to:"/",className:"nav-link",children:"Login"})})]})},qf=()=>{const e=(new Date).getFullYear();return G.jsx("footer",{className:"footer",children:G.jsxs("div",{className:"footer-content",children:[G.jsxs("p",{children:["© ",e," Database Service. All rights reserved."]}),G.jsxs("div",{className:"footer-links",children:[G.jsx(wf,{to:"/privacy",className:"footer-link",children:"Privacy Policy"}),G.jsx(wf,{to:"/terms",className:"footer-link",children:"Terms of Service"}),G.jsx("a",{href:"mailto:<EMAIL>",className:"footer-link",children:"Contact"})]})]})})},Kf=()=>{const[e,t]=A.useState(!0);return A.useEffect((()=>{window.__DEV_BYPASS_ACTIVE__||"true"===sessionStorage.getItem("dev_bypass_active")||t(!1)}),[]),e?G.jsxs("div",{className:"dev-bypass-banner",children:[G.jsx("span",{children:"⚠️ DEV LOGIN BYPASS ACTIVE: admin/admin"}),G.jsx("button",{className:"close-btn",onClick:()=>t(!1),title:"Close banner",children:"×"})]}):null},Yf=()=>G.jsxs("div",{className:"legal-page-container",children:[G.jsx("h1",{children:"Privacy Policy"}),G.jsx("p",{children:"This Privacy Policy describes how your information is collected, used, and protected when you use the Database Service application."}),G.jsx("h2",{children:"Information Collection"}),G.jsx("p",{children:"We collect only the information necessary to provide authentication and database management functionality. No personal data is sold or shared with third parties."}),G.jsx("h2",{children:"Usage of Information"}),G.jsx("p",{children:"Your information is used solely for the purpose of providing secure access to the database service and ensuring system integrity."}),G.jsx("h2",{children:"Data Security"}),G.jsx("p",{children:"We implement industry-standard security measures to protect your data from unauthorized access."}),G.jsx("h2",{children:"Contact"}),G.jsx("p",{children:"If you have any questions about this Privacy Policy, please contact your administrator."})]}),Gf=()=>G.jsxs("div",{className:"legal-page-container",children:[G.jsx("h1",{children:"Terms of Service"}),G.jsx("p",{children:"By using the Database Service application, you agree to the following terms and conditions."}),G.jsx("h2",{children:"Acceptable Use"}),G.jsx("p",{children:"You agree to use the application only for authorized and legal purposes. Unauthorized access or misuse is strictly prohibited."}),G.jsx("h2",{children:"Account Responsibility"}),G.jsx("p",{children:"You are responsible for maintaining the confidentiality of your login credentials and for all activities under your account."}),G.jsx("h2",{children:"Changes to Terms"}),G.jsx("p",{children:"We reserve the right to update these Terms of Service at any time. Continued use of the service constitutes acceptance of the updated terms."}),G.jsx("h2",{children:"Contact"}),G.jsx("p",{children:"If you have any questions about these Terms of Service, please contact your administrator."})]}),Xf=()=>{const[e,t]=A.useState([]),[n,r]=A.useState(!0),[a,l]=A.useState(null),[i,o]=A.useState(!1),s=async()=>{r(!0),l(null);try{const e=await fetch(`${Cf}/databases`,{method:"GET",headers:Tf()});if(!e.ok)throw new Error("Failed to fetch databases");const n=await e.json();t(n.databases||[])}catch(e){e instanceof Error?l(e.message):l("An unknown error occurred")}finally{r(!1),o(!1)}};A.useEffect((()=>{s()}),[]);return G.jsxs("div",{className:"db-management-container",children:[G.jsx("h1",{children:"Database Management"}),G.jsxs("div",{className:"db-management-actions",children:[G.jsx("button",{onClick:()=>{o(!0),s()},disabled:i,children:i?"Refreshing...":"Refresh"}),G.jsx("button",{onClick:async()=>{const e=prompt("Enter new database name:");if(e){r(!0),l(null);try{if(!(await fetch(`${Cf}/databases`,{method:"POST",headers:Tf(),body:JSON.stringify({name:e})})).ok)throw new Error("Failed to create database");await s()}catch(t){t instanceof Error?l(t.message):l("An unknown error occurred")}finally{r(!1)}}},children:"Create Database"})]}),n?G.jsx("div",{className:"db-management-loading",children:"Loading databases..."}):a?G.jsx("div",{className:"db-management-error",children:a}):G.jsxs("table",{className:"db-table",children:[G.jsx("thead",{children:G.jsxs("tr",{children:[G.jsx("th",{children:"Name"}),G.jsx("th",{children:"Owner"}),G.jsx("th",{children:"Size"}),G.jsx("th",{children:"Encoding"}),G.jsx("th",{children:"Collate"}),G.jsx("th",{children:"Ctype"}),G.jsx("th",{children:"Access"}),G.jsx("th",{children:"Actions"})]})}),G.jsx("tbody",{children:e.map((e=>G.jsxs("tr",{children:[G.jsx("td",{children:e.name}),G.jsx("td",{children:e.owner}),G.jsx("td",{children:e.size}),G.jsx("td",{children:e.encoding}),G.jsx("td",{children:e.collate}),G.jsx("td",{children:e.ctype}),G.jsx("td",{children:e.access}),G.jsx("td",{children:G.jsx("button",{className:"drop-btn",onClick:()=>(async e=>{if(window.confirm(`Are you sure you want to drop database '${e}'? This action cannot be undone.`)){r(!0),l(null);try{if(!(await fetch(`${Cf}/databases/${encodeURIComponent(e)}`,{method:"DELETE",headers:Tf()})).ok)throw new Error("Failed to drop database");await s()}catch(t){t instanceof Error?l(t.message):l("An unknown error occurred")}finally{r(!1)}}})(e.name),children:"Drop"})})]},e.name)))})]})]})},Jf=()=>{const[e,t]=A.useState(""),[n,r]=A.useState(""),{login:a,isLoading:l,error:i}=Hf();return G.jsx("div",{className:"login-container",children:G.jsxs("div",{className:"login-card",children:[G.jsxs("div",{className:"login-header",children:[G.jsx("h1",{children:"Database Service"}),G.jsx("p",{children:"Sign in to access your database management dashboard"})]}),G.jsxs("form",{className:"login-form",onSubmit:async t=>{t.preventDefault(),e.trim()&&n.trim()&&await a({username:e,password:n})},children:[i&&G.jsx("div",{className:"error-message",children:i}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"username",children:"Username"}),G.jsx("input",{type:"text",id:"username",value:e,onChange:e=>t(e.target.value),disabled:l})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"password",children:"Password"}),G.jsx("input",{type:"password",id:"password",value:n,onChange:e=>r(e.target.value),disabled:l})]}),G.jsx("button",{type:"submit",className:"login-button",disabled:l,children:l?"Signing in...":"Sign In"})]}),G.jsx("div",{className:"login-footer",children:G.jsx("p",{children:"Forgot your password? Contact your administrator."})})]})})},Zf=({title:e,value:t,change:n,icon:r="chart-line",color:a="blue"})=>G.jsxs("div",{className:`metrics-card ${a}`,children:[G.jsx("div",{className:"metrics-icon",children:G.jsx("i",{className:`fas fa-${r}`})}),G.jsxs("div",{className:"metrics-content",children:[G.jsx("h3",{className:"metrics-title",children:e}),G.jsxs("div",{className:"metrics-value-container",children:[G.jsx("p",{className:"metrics-value",children:t}),void 0===n?null:G.jsxs("div",{className:"metrics-change "+(void 0===n?"":n>0?"positive":n<0?"negative":"neutral"),children:[G.jsx("span",{className:"change-symbol",children:void 0===n?"":n>0?"↑":n<0?"↓":"–"}),G.jsxs("span",{className:"change-value",children:[Math.abs(n),"%"]})]})]})]})]}),ep=()=>{const[e,t]=A.useState({activeConnections:0,totalQueries:0,averageResponseTime:0,successRate:0,databaseCount:0}),[n,r]=A.useState(!0);return A.useEffect((()=>{(async()=>{r(!0);try{const e=await Uf();t({activeConnections:e.activeConnections,totalQueries:e.dailyQueries,averageResponseTime:e.averageResponseTime,successRate:e.successRate,databaseCount:0})}catch(e){console.error("Error fetching dashboard data:",e)}finally{r(!1)}})()}),[]),n?G.jsx("div",{className:"dashboard-loading",children:G.jsx("p",{children:"Loading dashboard data..."})}):G.jsxs("div",{className:"dashboard-container",children:[G.jsx("h1",{className:"dashboard-title",children:"Database Service Dashboard"}),G.jsxs("div",{className:"metrics-grid",children:[G.jsx(Zf,{title:"Active Connections",value:e.activeConnections,change:5.2,icon:"plug",color:"blue"}),G.jsx(Zf,{title:"Total Queries",value:e.totalQueries.toLocaleString(),change:12.7,icon:"database",color:"green"}),G.jsx(Zf,{title:"Avg. Response Time",value:`${e.averageResponseTime} ms`,change:-8.3,icon:"clock",color:"purple"}),G.jsx(Zf,{title:"Success Rate",value:`${e.successRate}%`,change:.2,icon:"check-circle",color:"orange"})]}),G.jsxs("div",{className:"dashboard-sections",children:[G.jsxs("div",{className:"dashboard-section",children:[G.jsx("h2",{children:"Recent Activity"}),G.jsxs("div",{className:"activity-list",children:[G.jsxs("div",{className:"activity-item",children:[G.jsx("span",{className:"activity-time",children:"10:45 AM"}),G.jsx("span",{className:"activity-description",children:"Database connection established: Production DB"})]}),G.jsxs("div",{className:"activity-item",children:[G.jsx("span",{className:"activity-time",children:"10:32 AM"}),G.jsx("span",{className:"activity-description",children:"Query executed: SELECT * FROM users LIMIT 100"})]}),G.jsxs("div",{className:"activity-item",children:[G.jsx("span",{className:"activity-time",children:"10:15 AM"}),G.jsx("span",{className:"activity-description",children:"New credential added: Analytics Database"})]}),G.jsxs("div",{className:"activity-item",children:[G.jsx("span",{className:"activity-time",children:"09:58 AM"}),G.jsx("span",{className:"activity-description",children:"System backup completed successfully"})]})]})]}),G.jsxs("div",{className:"dashboard-section",children:[G.jsx("h2",{children:"System Status"}),G.jsxs("div",{className:"status-items",children:[G.jsxs("div",{className:"status-item",children:[G.jsx("span",{className:"status-label",children:"Service Status:"}),G.jsx("span",{className:"status-value online",children:"Online"})]}),G.jsxs("div",{className:"status-item",children:[G.jsx("span",{className:"status-label",children:"Memory Usage:"}),G.jsx("span",{className:"status-value",children:"42%"})]}),G.jsxs("div",{className:"status-item",children:[G.jsx("span",{className:"status-label",children:"CPU Usage:"}),G.jsx("span",{className:"status-value",children:"28%"})]}),G.jsxs("div",{className:"status-item",children:[G.jsx("span",{className:"status-label",children:"Disk Space:"}),G.jsx("span",{className:"status-value",children:"68%"})]}),G.jsxs("div",{className:"status-item",children:[G.jsx("span",{className:"status-label",children:"Last Backup:"}),G.jsx("span",{className:"status-value",children:"Today, 09:58 AM"})]})]})]})]})]})},tp=({onSubmit:e,initialValues:t={name:"",username:"",password:"",server:"",port:"",database:""},formTitle:n="Add New Credential"})=>{const[r,a]=A.useState(t),[l,i]=A.useState({}),o=e=>{const{name:t,value:n}=e.target;a((e=>({...e,[t]:n}))),l[t]&&i((e=>{const n={...e};return delete n[t],n}))};return G.jsxs("div",{className:"credential-form-container",children:[G.jsx("h2",{children:n}),G.jsxs("form",{className:"credential-form",onSubmit:n=>{n.preventDefault(),(()=>{const e={};return r.name.trim()||(e.name="Name is required"),r.username.trim()||(e.username="Username is required"),r.server.trim()||(e.server="Server is required"),r.database.trim()||(e.database="Database name is required"),r.port&&!/^\d+$/.test(r.port)&&(e.port="Port must be a number"),i(e),0===Object.keys(e).length})()&&(e(r),t.id||a({name:"",username:"",password:"",server:"",port:"",database:""}))},children:[G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"name",children:"Connection Name"}),G.jsx("input",{type:"text",id:"name",name:"name",value:r.name,onChange:o,className:l.name?"error":""}),l.name&&G.jsx("span",{className:"error-message",children:l.name})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"server",children:"Server"}),G.jsx("input",{type:"text",id:"server",name:"server",value:r.server,onChange:o,className:l.server?"error":""}),l.server&&G.jsx("span",{className:"error-message",children:l.server})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"port",children:"Port"}),G.jsx("input",{type:"text",id:"port",name:"port",value:r.port,onChange:o,className:l.port?"error":"",placeholder:"Optional"}),l.port&&G.jsx("span",{className:"error-message",children:l.port})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"database",children:"Database Name"}),G.jsx("input",{type:"text",id:"database",name:"database",value:r.database,onChange:o,className:l.database?"error":""}),l.database&&G.jsx("span",{className:"error-message",children:l.database})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"username",children:"Username"}),G.jsx("input",{type:"text",id:"username",name:"username",value:r.username,onChange:o,className:l.username?"error":""}),l.username&&G.jsx("span",{className:"error-message",children:l.username})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"password",children:"Password"}),G.jsx("input",{type:"password",id:"password",name:"password",value:r.password,onChange:o,className:l.password?"error":"",placeholder:t.id?"••••••••":""}),l.password&&G.jsx("span",{className:"error-message",children:l.password})]}),G.jsx("div",{className:"form-actions",children:G.jsxs("button",{type:"submit",className:"submit-button",children:[t.id?"Update":"Add"," Credential"]})})]})]})},np=({credentials:e,onEdit:t,onDelete:n,onTest:r})=>0===e.length?G.jsx("div",{className:"credential-list-empty",children:G.jsx("p",{children:"No credentials found. Add a new credential to get started."})}):G.jsx("div",{className:"credential-list-container",children:G.jsxs("table",{className:"credential-table",children:[G.jsx("thead",{children:G.jsxs("tr",{children:[G.jsx("th",{children:"Name"}),G.jsx("th",{children:"Server"}),G.jsx("th",{children:"Database"}),G.jsx("th",{children:"Username"}),G.jsx("th",{children:"Last Used"}),G.jsx("th",{children:"Actions"})]})}),G.jsx("tbody",{children:e.map((e=>G.jsxs("tr",{children:[G.jsx("td",{children:e.name}),G.jsxs("td",{children:[e.server,e.port?`:${e.port}`:""]}),G.jsx("td",{children:e.database}),G.jsx("td",{children:e.username}),G.jsx("td",{children:e.lastUsed||"Never"}),G.jsxs("td",{className:"action-buttons",children:[G.jsx("button",{onClick:()=>r(e.id),className:"test-button",title:"Test Connection",children:"Test"}),G.jsx("button",{onClick:()=>t(e.id),className:"edit-button",title:"Edit Credential",children:"Edit"}),G.jsx("button",{onClick:()=>n(e.id),className:"delete-button",title:"Delete Credential",children:"Delete"})]})]},e.id)))})]})}),rp=()=>{const[e,t]=A.useState([]),[n,r]=A.useState(!0),[a,l]=A.useState(null),[i,o]=A.useState(!1);A.useEffect((()=>{(async()=>{r(!0);try{const e=await Of();t(e)}catch(e){console.error("Error fetching credentials:",e)}finally{r(!1)}})()}),[]);return n?G.jsx("div",{className:"credentials-loading",children:G.jsx("p",{children:"Loading credentials..."})}):G.jsxs("div",{className:"credentials-container",children:[G.jsxs("div",{className:"credentials-header",children:[G.jsx("h1",{children:"Database Credentials"}),!i&&G.jsx("button",{className:"add-credential-button",onClick:()=>o(!0),children:"Add New Credential"})]}),i?G.jsxs(G.Fragment,{children:[G.jsx(tp,{initialValues:a?{...a,port:a.port??"",password:""}:void 0,onSubmit:async n=>{a?await(async n=>{r(!0);try{const r=await Ff(n.id,n);t(e.map((e=>e.id===r.id?r:e))),l(null),o(!1)}catch(a){console.error("Error updating credential:",a)}finally{r(!1)}})(n):await(async n=>{r(!0);try{const r=await Df(n);t([...e,r]),o(!1)}catch(a){console.error("Error adding credential:",a)}finally{r(!1)}})(n)}}),G.jsx("div",{className:"form-actions",children:G.jsx("button",{className:"cancel-button",onClick:()=>{o(!1),l(null)},children:"Cancel"})})]}):G.jsx(np,{credentials:e,onEdit:t=>{const n=e.find((e=>e.id===t));n&&(l(n),o(!0))},onDelete:n=>{(async n=>{if(window.confirm("Are you sure you want to delete this credential?")){r(!0);try{await Mf(n),t(e.filter((e=>e.id!==n)))}catch(a){console.error("Error deleting credential:",a)}finally{r(!1)}}})(n)},onTest:e=>{(async e=>{r(!0);try{const t=await If(e);alert(t.message)}catch(t){alert("Failed to test connection.")}finally{r(!1)}})(e)}}),i&&G.jsx("div",{className:"form-actions",children:G.jsx("button",{className:"cancel-button",onClick:()=>{o(!1),l(null)},children:"Cancel"})})]})},ap=()=>{const[e,t]=A.useState(null),[n,r]=A.useState(!0),[a,l]=A.useState("7d");return A.useEffect((()=>{(async()=>{r(!0);try{const e=await Af(a);t(e)}catch(e){console.error("Error fetching metrics data:",e)}finally{r(!1)}})()}),[a]),n||!e?G.jsx("div",{className:"metrics-loading",children:G.jsx("p",{children:"Loading metrics data..."})}):G.jsxs("div",{className:"metrics-container",children:[G.jsxs("div",{className:"metrics-header",children:[G.jsx("h1",{children:"Database Metrics"}),G.jsxs("div",{className:"time-range-selector",children:[G.jsx("button",{className:"time-range-button "+("24h"===a?"active":""),onClick:()=>l("24h"),children:"24 Hours"}),G.jsx("button",{className:"time-range-button "+("7d"===a?"active":""),onClick:()=>l("7d"),children:"7 Days"}),G.jsx("button",{className:"time-range-button "+("30d"===a?"active":""),onClick:()=>l("30d"),children:"30 Days"})]})]}),G.jsxs("div",{className:"metrics-summary",children:[G.jsx("h2",{children:"Performance Summary"}),G.jsxs("div",{className:"metrics-grid",children:[G.jsx(Zf,{title:"Daily Queries",value:e.dailyQueries.toLocaleString(),change:8.3,icon:"database",color:"blue"}),G.jsx(Zf,{title:"Avg. Response Time",value:`${e.averageResponseTime} ms`,change:-5.2,icon:"clock",color:"green"}),G.jsx(Zf,{title:"Success Rate",value:`${e.successRate}%`,change:.1,icon:"check-circle",color:"purple"}),G.jsx(Zf,{title:"Active Connections",value:e.activeConnections,change:15.4,icon:"plug",color:"orange"})]})]}),G.jsxs("div",{className:"metrics-details",children:[G.jsxs("div",{className:"metrics-section",children:[G.jsx("h2",{children:"Query Statistics"}),G.jsx("table",{className:"metrics-table",children:G.jsxs("tbody",{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Daily Queries"}),G.jsx("td",{children:e.dailyQueries.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Weekly Queries"}),G.jsx("td",{children:e.weeklyQueries.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Monthly Queries"}),G.jsx("td",{children:e.monthlyQueries.toLocaleString()})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Success Rate"}),G.jsxs("td",{children:[e.successRate,"%"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Error Rate"}),G.jsxs("td",{children:[e.errorRate,"%"]})]})]})})]}),G.jsxs("div",{className:"metrics-section",children:[G.jsx("h2",{children:"Performance Metrics"}),G.jsx("table",{className:"metrics-table",children:G.jsxs("tbody",{children:[G.jsxs("tr",{children:[G.jsx("td",{children:"Average Response Time"}),G.jsxs("td",{children:[e.averageResponseTime," ms"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Peak Response Time"}),G.jsxs("td",{children:[e.peakResponseTime," ms"]})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Active Connections"}),G.jsx("td",{children:e.activeConnections})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Database Size"}),G.jsx("td",{children:e.databaseSize})]}),G.jsxs("tr",{children:[G.jsx("td",{children:"Last Backup"}),G.jsx("td",{children:e.lastBackupTime})]})]})})]})]})]})},lp=()=>{const[e,t]=A.useState(null),[n,r]=A.useState(!1),[a,l]=A.useState(""),[i,o]=A.useState(null);A.useEffect((()=>{(async()=>{r(!0),o(null);try{const e=await $f();t(e)}catch(e){e instanceof Error?o(e.message):o("Failed to load settings.")}finally{r(!1)}})()}),[]);const s=e=>{const{name:n,value:r,type:a}=e.target;t((t=>{if(!t)return t;let l;return l="checkbox"===a?e.target.checked:"number"===a?""===r?"":Number(r):r,{...t,[n]:l}}))};return n&&!e?G.jsx("div",{className:"settings-loading",children:G.jsx("p",{children:"Loading settings..."})}):i?G.jsx("div",{className:"settings-error",children:G.jsx("p",{children:i})}):e?G.jsxs("div",{className:"settings-container",children:[G.jsx("h1",{className:"settings-title",children:"Database Service Settings"}),G.jsxs("form",{className:"settings-form",onSubmit:async t=>{t.preventDefault(),r(!0),l(""),o(null);try{if(!e)throw new Error("Settings not loaded");await Bf(e),l("Settings saved successfully!"),setTimeout((()=>{l("")}),3e3)}catch(n){o("Error saving settings. Please try again.")}finally{r(!1)}},children:[G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Connection Settings"}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"connectionTimeout",children:"Connection Timeout (seconds)"}),G.jsx("input",{type:"number",id:"connectionTimeout",name:"connectionTimeout",value:e.connectionTimeout,onChange:s,min:"5",max:"300"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"maxConnections",children:"Maximum Connections"}),G.jsx("input",{type:"number",id:"maxConnections",name:"maxConnections",value:e.maxConnections,onChange:s,min:"10",max:"1000"})]})]}),G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Logging Settings"}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"enableLogging",name:"enableLogging",checked:e.enableLogging,onChange:s}),G.jsx("label",{htmlFor:"enableLogging",children:"Enable Logging"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"logLevel",children:"Log Level"}),G.jsxs("select",{id:"logLevel",name:"logLevel",value:e.logLevel,onChange:s,disabled:!e.enableLogging,children:[G.jsx("option",{value:"debug",children:"Debug"}),G.jsx("option",{value:"info",children:"Info"}),G.jsx("option",{value:"warning",children:"Warning"}),G.jsx("option",{value:"error",children:"Error"})]})]})]}),G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Backup Settings"}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"backupEnabled",name:"backupEnabled",checked:e.backupEnabled,onChange:s}),G.jsx("label",{htmlFor:"backupEnabled",children:"Enable Automatic Backups"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"backupFrequency",children:"Backup Frequency"}),G.jsxs("select",{id:"backupFrequency",name:"backupFrequency",value:e.backupFrequency,onChange:s,disabled:!e.backupEnabled,children:[G.jsx("option",{value:"hourly",children:"Hourly"}),G.jsx("option",{value:"daily",children:"Daily"}),G.jsx("option",{value:"weekly",children:"Weekly"}),G.jsx("option",{value:"monthly",children:"Monthly"})]})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"backupLocation",children:"Backup Location"}),G.jsx("input",{type:"text",id:"backupLocation",name:"backupLocation",value:e.backupLocation,onChange:s,disabled:!e.backupEnabled})]})]}),G.jsxs("div",{className:"settings-section",children:[G.jsx("h2",{children:"Notification Settings"}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"notificationsEnabled",name:"notificationsEnabled",checked:e.notificationsEnabled,onChange:s}),G.jsx("label",{htmlFor:"notificationsEnabled",children:"Enable Notifications"})]}),G.jsxs("div",{className:"form-group checkbox-group",children:[G.jsx("input",{type:"checkbox",id:"emailNotifications",name:"emailNotifications",checked:e.emailNotifications,onChange:s,disabled:!e.notificationsEnabled}),G.jsx("label",{htmlFor:"emailNotifications",children:"Email Notifications"})]}),G.jsxs("div",{className:"form-group",children:[G.jsx("label",{htmlFor:"emailAddress",children:"Email Address"}),G.jsx("input",{type:"email",id:"emailAddress",name:"emailAddress",value:e.emailAddress,onChange:s,disabled:!e.notificationsEnabled||!e.emailNotifications,placeholder:"<EMAIL>"})]})]}),G.jsxs("div",{className:"settings-actions",children:[G.jsx("button",{type:"submit",className:"save-button",disabled:n,children:n?"Saving...":"Save Settings"}),a&&G.jsx("div",{className:"save-message "+(a.includes("Error")?"error":"success"),children:a})]})]})]}):null},ip=()=>G.jsx("div",{className:"not-found-container",children:G.jsxs("div",{className:"not-found-content",children:[G.jsx("h1",{children:"404"}),G.jsx("h2",{children:"Page Not Found"}),G.jsx("p",{children:"The page you are looking for doesn't exist or has been moved."}),G.jsx(wf,{to:"/",className:"back-home-button",children:"Back to Home"})]})}),op=({children:e})=>{const{isAuthenticated:t,isLoading:n}=Hf();return n?G.jsx("div",{className:"loading-container",children:"Loading..."}):t?e:G.jsx(cf,{to:"/",replace:!0})},sp=()=>{const{isAuthenticated:e}=Hf();return G.jsxs("div",{className:"app-container",children:[G.jsx(Kf,{}),G.jsx(Qf,{}),G.jsx("main",{className:"main-content",children:G.jsxs(pf,{children:[G.jsx(df,{path:"/",element:e?G.jsx(cf,{to:"/dashboard",replace:!0}):G.jsx(Jf,{})}),G.jsx(df,{path:"/dashboard",element:G.jsx(op,{children:G.jsx(ep,{})})}),G.jsx(df,{path:"/credentials",element:G.jsx(op,{children:G.jsx(rp,{})})}),G.jsx(df,{path:"/metrics",element:G.jsx(op,{children:G.jsx(ap,{})})}),G.jsx(df,{path:"/settings",element:G.jsx(op,{children:G.jsx(lp,{})})}),G.jsx(df,{path:"/databases",element:G.jsx(op,{children:G.jsx(Xf,{})})}),G.jsx(df,{path:"/privacy",element:G.jsx(Yf,{})}),G.jsx(df,{path:"/terms",element:G.jsx(Gf,{})}),G.jsx(df,{path:"*",element:G.jsx(ip,{})})]})}),G.jsx(qf,{})]})};function up(){return G.jsx(yf,{children:G.jsx(Wf,{children:G.jsx(sp,{})})})}X.createRoot(document.getElementById("root")).render(G.jsx($.StrictMode,{children:G.jsx(up,{})}));
