#include "nginx_parser.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace logging {

NginxParser::NginxParser() {
    // Initialize regular expressions for Nginx log formats
    
    // Error log format: 2023/01/01 12:34:56 [error] 12345#12345: *1 error message
    errorLogFormat_ = std::regex(
        R"((\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2})\s+\[(\w+)\]\s+(\d+)(?:#\d+)?:\s+(?:\*\d+\s+)?(.*))"
    );
    
    // Access log format: *********** - user [01/Jan/2023:12:34:56 +0000] "GET /path HTTP/1.1" 200 1234 "referer" "user-agent"
    accessLogFormat_ = std::regex(
        R"((\S+) (\S+) (\S+) \[([\w:/]+\s[+\-]\d{4})\] "(\S+) (.*?) (\S+)" (\d{3}) (\d+)(?: "([^"]*)" "([^"]*)")?)");
}

std::optional<LogEntry> NginxParser::parse(const std::string& line, const std::string& source) {
    std::smatch matches;
    
    // Try error log format first
    if (std::regex_search(line, matches, errorLogFormat_)) {
        LogEntry entry;
        
        // Parse timestamp
        std::string timestampStr = matches[1].str();
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%Y/%m/%d %H:%M:%S");
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set other fields
        std::string level = matches[2].str();
        entry.component = "nginx";
        entry.unit = matches[3].str();
        entry.message = matches[4].str();
        entry.source = source.empty() ? "nginx" : source;
        entry.rawLine = line;
        entry.category = LogCategory::APPLICATION;
        
        // Determine log type based on level
        if (level == "error" || level == "crit" || level == "alert" || level == "emerg") {
            entry.type = LogType::ERROR;
        } else if (level == "warn" || level == "notice") {
            entry.type = LogType::WARNING;
        } else if (level == "info") {
            entry.type = LogType::INFO;
        } else if (level == "debug") {
            entry.type = LogType::DEBUG;
        } else {
            entry.type = LogType::INFO;
        }
        
        return entry;
    }
    
    // Try access log format
    if (std::regex_search(line, matches, accessLogFormat_)) {
        LogEntry entry;
        
        // Parse timestamp
        std::string timestampStr = matches[4].str();
        std::tm tm = {};
        std::istringstream ss(timestampStr);
        ss >> std::get_time(&tm, "%d/%b/%Y:%H:%M:%S %z");
        
        // Convert to time_point
        auto timeT = std::mktime(&tm);
        entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
        
        // Set other fields
        entry.component = "nginx";
        entry.message = matches[5].str() + " " + matches[6].str() + " " + matches[7].str() + " - " + matches[8].str();
        entry.source = source.empty() ? "nginx" : source;
        entry.rawLine = line;
        entry.category = LogCategory::APPLICATION;
        
        // Add metadata
        entry.metadata["ip"] = matches[1].str();
        entry.metadata["user"] = matches[3].str();
        entry.metadata["method"] = matches[5].str();
        entry.metadata["path"] = matches[6].str();
        entry.metadata["protocol"] = matches[7].str();
        entry.metadata["status"] = matches[8].str();
        entry.metadata["size"] = matches[9].str();
        
        if (matches.size() > 10 && matches[10].matched) {
            entry.metadata["referer"] = matches[10].str();
        }
        
        if (matches.size() > 11 && matches[11].matched) {
            entry.metadata["user_agent"] = matches[11].str();
        }
        
        // Determine log type based on status code
        int statusCode = std::stoi(matches[8].str());
        if (statusCode >= 500) {
            entry.type = LogType::ERROR;
        } else if (statusCode >= 400) {
            entry.type = LogType::WARNING;
        } else if (statusCode >= 300) {
            entry.type = LogType::INFO;
        } else if (statusCode >= 200) {
            entry.type = LogType::SUCCESS;
        } else {
            entry.type = LogType::INFO;
        }
        
        return entry;
    }
    
    // No match
    return std::nullopt;
}

bool NginxParser::canParse(const std::string& line) {
    // Check if line matches any of the Nginx log formats
    return std::regex_search(line, errorLogFormat_) ||
           std::regex_search(line, accessLogFormat_);
}

} // namespace logging
