<#
.SYNOPSIS
Windows build server script for verifying Project Tracker build environment.

.DESCRIPTION
This PowerShell script runs on the Windows build server to verify the build environment
and package structure before deployment package creation. It handles:
- Storage space verification
- PowerShell version verification
- Write permissions verification

.NOTES
Environment: Windows Build Server
Version: 1.0.0
Requires: PowerShell 7+

.EXAMPLE
.\verify-build.ps1
#>

# Function to write colored output
function Write-Status {
    param(
        [string]$Message,
        [string]$Status,
        [string]$Color = 'Green'
    )
    Write-Host $Message -NoNewline
    Write-Host " [$Status]" -ForegroundColor $Color
}

# Verify storage space
$drive = (Get-Item $PSScriptRoot).PSDrive.Name
$freeSpace = (Get-PSDrive $drive).Free / 1GB
Write-Status "Checking free space on drive ${drive}:" "$($freeSpace.ToString('0.00'))GB"
if ($freeSpace -lt 5) {
    Write-Status "ERROR: Insufficient disk space. Need at least 5GB free." "Failed" "Red"
    exit 1
}

# Verify PowerShell version
$requiredVersion = 7
if ($PSVersionTable.PSVersion.Major -lt $requiredVersion) {
    Write-Status "ERROR: PowerShell version $requiredVersion+ required." "Failed" "Red"
    exit 1
}
Write-Status "PowerShell version check" "OK"

# Verify write permissions in deployment directory
$deployRoot = Join-Path (Split-Path (Split-Path $PSScriptRoot -Parent) -Parent) "project-tracker-deploy"
try {
    $testFile = Join-Path $deployRoot ".write-test"
    New-Item -ItemType File -Path $testFile -Force | Out-Null
    Remove-Item $testFile -Force
    Write-Status "Write permissions check" "OK"
}
catch {
    Write-Status "ERROR: No write permissions in deployment directory" "Failed" "Red"
    exit 1
}

Write-Host "`nBuild environment verification completed successfully!" -ForegroundColor Green
