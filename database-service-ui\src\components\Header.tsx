import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { isAuthenticated, isAdmin, clearAuthData } from '../utils/auth';
import { authAPI } from '../services/api';
import './Header.css';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const authenticated = isAuthenticated();
  const admin = isAdmin();

  const handleLogout = async () => {
    try {
      await authAPI.logout();
      clearAuthData();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if the API call fails, clear local storage
      clearAuthData();
      navigate('/login');
    }
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <div className="logo">
            <Link to="/">Database Service</Link>
          </div>
          <nav className="nav">
            {authenticated ? (
              <>
                <Link to="/dashboard" className="nav-link">Dashboard</Link>
                <Link to="/metrics" className="nav-link">Metrics</Link>
                {admin && (
                  <>
                    <Link to="/credentials" className="nav-link">Credentials</Link>
                    <Link to="/settings" className="nav-link">Settings</Link>
                  </>
                )}
                <button onClick={handleLogout} className="nav-link logout-btn">Logout</button>
              </>
            ) : (
              <Link to="/login" className="nav-link">Login</Link>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
