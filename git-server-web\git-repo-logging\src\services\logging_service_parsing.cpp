#include "logging_service.hpp"
#include "../api/api_server.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <regex>
#include <chrono>
#include <thread>
#include <filesystem>
#include <jsoncpp/json/json.h>

// Log parsing and processing methods
LogEntry LoggingService::parseLogLine(const std::string& line, const std::string& source) {
    // Default log entry
    LogEntry entry;
    entry.source = source;
    
    try {
        // Different parsing logic based on source
        if (source == "system") {
            // System log format: May 20 10:15:30 hostname service[pid]: message
            std::regex systemLogRegex(R"((\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+([^:]+):\s+(.*))");
            std::smatch matches;
            
            if (std::regex_search(line, matches, systemLogRegex)) {
                // Parse timestamp
                std::string timestampStr = matches[1].str();
                std::tm tm = {};
                std::istringstream ss(timestampStr);
                ss >> std::get_time(&tm, "%b %d %H:%M:%S");
                
                // Add current year (since syslog doesn't include year)
                auto now = std::chrono::system_clock::now();
                auto nowTm = std::chrono::system_clock::to_time_t(now);
                auto nowTmStruct = *std::localtime(&nowTm);
                tm.tm_year = nowTmStruct.tm_year;
                
                // Convert to time_point
                auto timeT = std::mktime(&tm);
                entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
                
                // Set other fields
                entry.hostname = matches[2].str();
                entry.component = matches[3].str();
                entry.message = matches[4].str();
                
                // Determine log type based on content
                if (line.find("error") != std::string::npos || 
                    line.find("fail") != std::string::npos || 
                    line.find("critical") != std::string::npos) {
                    entry.type = LogType::ERROR;
                } else if (line.find("warning") != std::string::npos || 
                           line.find("warn") != std::string::npos) {
                    entry.type = LogType::WARNING;
                } else {
                    entry.type = LogType::INFO;
                }
            }
            
        } else if (source == "nginx") {
            // Nginx error log format: 2023/05/20 10:15:30 [error] 12345#12345: *1 message
            std::regex nginxLogRegex(R"((\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2})\s+\[(\w+)\]\s+(\d+)#\d+:\s+(.*))");
            std::smatch matches;
            
            if (std::regex_search(line, matches, nginxLogRegex)) {
                // Parse timestamp
                std::string timestampStr = matches[1].str();
                std::tm tm = {};
                std::istringstream ss(timestampStr);
                ss >> std::get_time(&tm, "%Y/%m/%d %H:%M:%S");
                
                // Convert to time_point
                auto timeT = std::mktime(&tm);
                entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
                
                // Set other fields
                std::string level = matches[2].str();
                entry.component = "nginx";
                entry.unit = matches[3].str();
                entry.message = matches[4].str();
                
                // Determine log type based on level
                if (level == "error" || level == "crit" || level == "alert" || level == "emerg") {
                    entry.type = LogType::ERROR;
                } else if (level == "warn" || level == "notice") {
                    entry.type = LogType::WARNING;
                } else {
                    entry.type = LogType::INFO;
                }
            }
            
        } else if (source == "git") {
            // Git log format: commit_hash|author|timestamp|message
            std::regex gitLogRegex(R"(([a-f0-9]{40})\|([^|]+)\|([^|]+)\|(.*))");
            std::smatch matches;
            
            if (std::regex_search(line, matches, gitLogRegex)) {
                // Parse timestamp
                std::string timestampStr = matches[3].str();
                std::tm tm = {};
                std::istringstream ss(timestampStr);
                ss >> std::get_time(&tm, "%a %b %d %H:%M:%S %Y %z");
                
                // Convert to time_point
                auto timeT = std::mktime(&tm);
                entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
                
                // Set other fields
                entry.commit = matches[1].str();
                entry.author = matches[2].str();
                entry.message = matches[4].str();
                entry.component = "git";
                
                // Determine log type based on message content
                if (entry.message->find("fix") != std::string::npos || 
                    entry.message->find("bug") != std::string::npos || 
                    entry.message->find("issue") != std::string::npos) {
                    entry.type = LogType::WARNING;
                } else if (entry.message->find("feat") != std::string::npos || 
                           entry.message->find("add") != std::string::npos || 
                           entry.message->find("new") != std::string::npos) {
                    entry.type = LogType::SUCCESS;
                } else {
                    entry.type = LogType::INFO;
                }
            }
            
        } else if (source == "auth") {
            // Auth log format: May 20 10:15:30 hostname sshd[pid]: message
            std::regex authLogRegex(R"((\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+(\S+)\[(\d+)\]:\s+(.*))");
            std::smatch matches;
            
            if (std::regex_search(line, matches, authLogRegex)) {
                // Parse timestamp
                std::string timestampStr = matches[1].str();
                std::tm tm = {};
                std::istringstream ss(timestampStr);
                ss >> std::get_time(&tm, "%b %d %H:%M:%S");
                
                // Add current year (since auth log doesn't include year)
                auto now = std::chrono::system_clock::now();
                auto nowTm = std::chrono::system_clock::to_time_t(now);
                auto nowTmStruct = *std::localtime(&nowTm);
                tm.tm_year = nowTmStruct.tm_year;
                
                // Convert to time_point
                auto timeT = std::mktime(&tm);
                entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
                
                // Set other fields
                entry.hostname = matches[2].str();
                entry.component = matches[3].str();
                entry.unit = matches[4].str();
                entry.message = matches[5].str();
                
                // Determine log type based on content
                if (line.find("Failed") != std::string::npos || 
                    line.find("error") != std::string::npos || 
                    line.find("invalid") != std::string::npos) {
                    entry.type = LogType::ERROR;
                } else if (line.find("warning") != std::string::npos || 
                           line.find("warn") != std::string::npos) {
                    entry.type = LogType::WARNING;
                } else if (line.find("Accepted") != std::string::npos || 
                           line.find("session opened") != std::string::npos) {
                    entry.type = LogType::SUCCESS;
                } else {
                    entry.type = LogType::INFO;
                }
            }
            
        } else if (source == "dashboard") {
            // Dashboard log format: [2023-05-20 10:15:30,123] INFO in app: message
            std::regex dashboardLogRegex(R"(\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,.]\d{3})\]\s+(\w+)\s+in\s+(\w+):\s+(.*))");
            std::smatch matches;
            
            if (std::regex_search(line, matches, dashboardLogRegex)) {
                // Parse timestamp
                std::string timestampStr = matches[1].str();
                // Replace comma with dot for proper parsing
                std::replace(timestampStr.begin(), timestampStr.end(), ',', '.');
                
                std::tm tm = {};
                std::istringstream ss(timestampStr);
                ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S.%f");
                
                // Convert to time_point
                auto timeT = std::mktime(&tm);
                entry.timestamp = std::chrono::system_clock::from_time_t(timeT);
                
                // Set other fields
                std::string level = matches[2].str();
                entry.component = matches[3].str();
                entry.message = matches[4].str();
                
                // Determine log type based on level
                if (level == "ERROR" || level == "CRITICAL") {
                    entry.type = LogType::ERROR;
                } else if (level == "WARNING") {
                    entry.type = LogType::WARNING;
                } else if (level == "SUCCESS") {
                    entry.type = LogType::SUCCESS;
                } else if (level == "DEBUG") {
                    entry.type = LogType::DEBUG;
                } else {
                    entry.type = LogType::INFO;
                }
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error parsing log line: " << e.what() << std::endl;
        
        // Set default values for unparseable line
        entry.timestamp = std::chrono::system_clock::now();
        entry.type = LogType::ERROR;
        entry.message = "Error parsing log: " + line;
    }
    
    return entry;
}

void LoggingService::processLogEntry(const LogEntry& entry) {
    // Add entry to logs
    {
        std::lock_guard<std::mutex> lock(logsMutex_);
        logs_.push_back(entry);
    }
    
    // Notify WebSocket subscribers
    notifySubscribers(entry);
}

void LoggingService::notifySubscribers(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(subscriptionsMutex_);
    
    // Convert entry to JSON
    Json::Value logJson;
    logJson["timestamp"] = formatTimestamp(entry.timestamp);
    logJson["type"] = getLogTypeString(entry.type);
    logJson["message"] = entry.message;
    logJson["source"] = entry.source;
    
    // Add optional fields if present
    if (entry.component) {
        logJson["component"] = *entry.component;
    }
    
    if (entry.unit) {
        logJson["unit"] = *entry.unit;
    }
    
    if (entry.hostname) {
        logJson["hostname"] = *entry.hostname;
    }
    
    if (entry.commit) {
        logJson["commit"] = *entry.commit;
    }
    
    if (entry.author) {
        logJson["author"] = *entry.author;
    }
    
    // Add metadata
    for (const auto& [key, value] : entry.metadata) {
        logJson[key] = value;
    }
    
    // Convert to string
    Json::StreamWriterBuilder writer;
    std::string logStr = Json::writeString(writer, logJson);
    
    // Send to subscribers
    for (const auto& [session, subscription] : subscriptions_) {
        // Filter by source
        if (subscription.source != "all" && entry.source != subscription.source) {
            continue;
        }
        
        // Filter by level
        if (subscription.level != "all") {
            std::string entryLevel = getLogTypeString(entry.type);
            if (entryLevel != subscription.level) {
                continue;
            }
        }
        
        // Send to subscriber
        session->send(logStr);
    }
}
