# Git Server Scripts

This directory contains the essential scripts for managing the centralized Git server setup. These scripts have been consolidated from the original `scripts` directory to provide a cleaner, more organized structure.

## Scripts Overview

### 1. test-windsurf-git-integration.ps1

Tests the integration between Windsurf and the Git server by:
- Verifying SSH connection to the Git server
- Creating a test file
- Committing and pushing the file
- Verifying the commit on the server

### 2. real-time-commit.ps1

Monitors file changes in the repository and automatically commits them after a brief period of inactivity (2 seconds). Features include:
- File system monitoring for any changes
- Debouncing to avoid multiple commits for rapid changes
- Automatic pushing to the remote Git server
- Logging of all commits to auto-commit.log

### 3. start-real-time-commit.ps1

Launcher script that starts the real-time commit monitor in a separate PowerShell window.

### 4. setup-git-server.sh

Bash script for setting up the Git server on Ubuntu, including:
- Git, SSH, Apache, and GitWeb installation
- Git user and repositories directory configuration
- SSH access setup
- Git daemon configuration for anonymous access
- GitWeb setup for web-based repository browsing

### 5. migrate-to-git-server.ps1

PowerShell script for migrating repositories from Windows to the Ubuntu Git server:
- Tests connection to the Ubuntu server
- Creates repositories on the remote server
- Pushes all branches and tags
- Updates the origin remote to point to the new server

### 6. direct-ssh-fix.sh

Bash script that resolves SSH key authentication issues for the git user on the Ubuntu server by:
- Setting proper permissions for SSH directories and files
- Configuring SSH keys directly as the git user
- Ensuring proper ownership of SSH keys

## Usage

Refer to the main [GIT-SERVER-SETUP-GUIDE.md](../GIT-SERVER-SETUP-GUIDE.md) for detailed instructions on how to use these scripts as part of the Git server setup and maintenance process.
