# Simple verification script
$RemoteUser = "btaylor-admin"
$RemoteHost = "git.chcit.org"
$RemotePath = "/opt/git-dashboard"

Write-Host "Verifying deployment..." -ForegroundColor Yellow

# Check if files exist using a single SSH command to avoid connection issues
ssh "${RemoteUser}@${RemoteHost}" @"
echo "Checking files on server...";

echo "\nJavaScript files:";
[ -f "${RemotePath}/js/git-utils.js" ] && echo "- git-utils.js: ✓" || echo "- git-utils.js: ✗";
[ -f "${RemotePath}/js/git-commit-history-chart.js" ] && echo "- git-commit-history-chart.js: ✓" || echo "- git-commit-history-chart.js: ✗";
[ -f "${RemotePath}/js/git-size-comparison-chart.js" ] && echo "- git-size-comparison-chart.js: ✓" || echo "- git-size-comparison-chart.js: ✗";
[ -f "${RemotePath}/js/git-repository-list.js" ] && echo "- git-repository-list.js: ✓" || echo "- git-repository-list.js: ✗";
[ -f "${RemotePath}/js/git-repository-modal.js" ] && echo "- git-repository-modal.js: ✓" || echo "- git-repository-modal.js: ✗";
[ -f "${RemotePath}/js/git-repository-manager.js" ] && echo "- git-repository-manager.js: ✓" || echo "- git-repository-manager.js: ✗";
[ -f "${RemotePath}/js/git-repositories.js.bak" ] && echo "- git-repositories.js.bak: ✓" || echo "- git-repositories.js.bak: ✗";

echo "\nCSS files:";
[ -f "${RemotePath}/css/repositories.css" ] && echo "- repositories.css: ✓" || echo "- repositories.css: ✗";

echo "\nHTML files:";
[ -f "${RemotePath}/index.html" ] && echo "- index.html: ✓" || echo "- index.html: ✗";

echo "\nService status:";
sudo systemctl status git-dashboard.service | grep -q "active (running)" && echo "- git-dashboard service: ✓ RUNNING" || echo "- git-dashboard service: ✗ NOT RUNNING";
"@
