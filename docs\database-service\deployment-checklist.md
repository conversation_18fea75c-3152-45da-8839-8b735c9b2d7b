# Database Service Deployment Checklist

This document provides a comprehensive checklist for deploying the Database Service and UI integration to production environments.

## Pre-Deployment Checklist

### Infrastructure Requirements

- [ ] **Server Specifications Met**
  - [ ] Ubuntu 22.04+ LTS installed
  - [ ] 4+ CPU cores available
  - [ ] 8+ GB RAM available
  - [ ] 100+ GB SSD storage available
  - [ ] Network connectivity verified

- [ ] **Software Dependencies Installed**
  - [ ] Nginx 1.18+ installed and configured
  - [ ] PostgreSQL 14+ installed and running
  - [ ] Build tools installed (gcc-14, cmake, make)
  - [ ] Required libraries installed (boost, openssl, nlohmann-json, pqxx)
  - [ ] Node.js 18+ and npm 8+ installed

- [ ] **Security Prerequisites**
  - [ ] Let's Encrypt certificates configured for chcit.org
  - [ ] SSL certificates accessible to database service user
  - [ ] Firewall rules configured (ports 22, 80, 443, 5432)
  - [ ] SSH access configured for deployment user

### Database Preparation

- [ ] **PostgreSQL Configuration**
  - [ ] Database user `database_service_user` created
  - [ ] Database `gitdashboard` created
  - [ ] Proper permissions granted to service user
  - [ ] SSL configuration enabled
  - [ ] Connection limits configured

- [ ] **Database Schema**
  - [ ] Required tables created (users, permissions, etc.)
  - [ ] Initial admin user created
  - [ ] Database indexes created
  - [ ] Foreign key constraints applied

### Configuration Files

- [ ] **Backend Configuration**
  - [ ] Production config file created (`config-prod.json`)
  - [ ] Database connection parameters configured
  - [ ] JWT secret generated and configured
  - [ ] SSL settings configured for database connections
  - [ ] Logging configuration set for production

- [ ] **Frontend Configuration**
  - [ ] Production environment file created (`.env.production`)
  - [ ] API URLs configured for production
  - [ ] Build configuration optimized
  - [ ] Security settings configured

## Deployment Process

### Phase 1: Backend Deployment

- [ ] **Build Database Service**
  ```bash
  cd database-service
  mkdir build && cd build
  cmake -DCMAKE_BUILD_TYPE=Release \
        -DCMAKE_INSTALL_PREFIX=/opt/database-service \
        -DBUILD_TESTS=OFF ..
  make -j$(nproc)
  ```

- [ ] **Install Database Service**
  ```bash
  sudo make install
  sudo chown -R database-service:database-service /opt/database-service
  sudo chmod +x /opt/database-service/bin/database-service
  ```

- [ ] **Configure Systemd Service**
  ```bash
  sudo systemctl enable database-service
  sudo systemctl daemon-reload
  ```

- [ ] **Test Backend Installation**
  ```bash
  sudo systemctl start database-service
  sudo systemctl status database-service
  curl http://localhost:8080/api/health
  ```

### Phase 2: Frontend Deployment

- [ ] **Build React UI**
  ```bash
  cd database-service-ui
  npm ci --production
  npm run build
  ```

- [ ] **Deploy UI Files**
  ```bash
  sudo mkdir -p /var/www/html/database-ui
  sudo cp -r build/* /var/www/html/database-ui/
  sudo chown -R www-data:www-data /var/www/html/database-ui
  sudo chmod -R 644 /var/www/html/database-ui
  ```

- [ ] **Test UI Deployment**
  ```bash
  curl http://localhost/database-ui/
  ```

### Phase 3: Nginx Integration

- [ ] **Backup Existing Configuration**
  ```bash
  sudo cp /etc/nginx/sites-available/git.chcit.org \
         /etc/nginx/sites-available/git.chcit.org.backup.$(date +%Y%m%d)
  ```

- [ ] **Integrate Database Service**
  ```bash
  sudo ./scripts/integrate-with-git-server.sh
  ```

- [ ] **Test Nginx Configuration**
  ```bash
  sudo nginx -t
  sudo systemctl reload nginx
  ```

- [ ] **Verify Integration**
  ```bash
  curl https://git.chcit.org/database-api/health
  curl https://git.chcit.org/database-ui/
  ```

## Post-Deployment Verification

### Functional Testing

- [ ] **API Endpoints**
  - [ ] Health check: `GET /database-api/health`
  - [ ] Authentication: `POST /database-api/auth/login`
  - [ ] Database list: `GET /database-api/databases`
  - [ ] Metrics: `GET /database-api/database/metrics`
  - [ ] Credentials: `POST /database-api/credentials/store`

- [ ] **UI Functionality**
  - [ ] Login page loads and functions
  - [ ] Dashboard displays metrics
  - [ ] Database management page works
  - [ ] Metrics visualization displays
  - [ ] Credentials management functions
  - [ ] Settings page accessible

- [ ] **Authentication Flow**
  - [ ] User can log in successfully
  - [ ] JWT tokens are generated and stored
  - [ ] Token refresh works automatically
  - [ ] Protected routes require authentication
  - [ ] Logout clears tokens properly

### Security Verification

- [ ] **SSL/TLS Configuration**
  - [ ] HTTPS redirects work properly
  - [ ] SSL certificate is valid and trusted
  - [ ] Security headers are present
  - [ ] HSTS is enabled

- [ ] **API Security**
  - [ ] CORS is properly configured
  - [ ] Rate limiting is active
  - [ ] Authentication is required for protected endpoints
  - [ ] SQL injection protection is active

- [ ] **Access Controls**
  - [ ] Admin-only endpoints are restricted
  - [ ] Metrics endpoint is restricted to private networks
  - [ ] File permissions are correct
  - [ ] Database permissions are minimal

### Performance Verification

- [ ] **Response Times**
  - [ ] Health check responds < 100ms
  - [ ] Authentication responds < 500ms
  - [ ] Database queries respond < 1s
  - [ ] UI loads < 3s

- [ ] **Resource Usage**
  - [ ] CPU usage < 50% under normal load
  - [ ] Memory usage < 70% of available
  - [ ] Database connections < 80% of pool
  - [ ] Disk usage < 80% of available

- [ ] **Scalability**
  - [ ] Connection pool handles concurrent requests
  - [ ] Rate limiting prevents abuse
  - [ ] Nginx handles static file serving
  - [ ] Database queries are optimized

## Monitoring Setup

### Log Configuration

- [ ] **Application Logs**
  - [ ] Database service logs to `/var/log/database-service/`
  - [ ] Log rotation configured
  - [ ] Log levels appropriate for production
  - [ ] Error logs are monitored

- [ ] **System Logs**
  - [ ] Nginx access logs configured
  - [ ] Nginx error logs configured
  - [ ] PostgreSQL logs configured
  - [ ] Systemd journal retention set

### Health Monitoring

- [ ] **Automated Health Checks**
  - [ ] Service status monitoring script installed
  - [ ] API health check script installed
  - [ ] Database connectivity check script installed
  - [ ] Cron jobs configured for regular checks

- [ ] **Alerting**
  - [ ] Email alerts configured for service failures
  - [ ] Disk space monitoring configured
  - [ ] Memory usage monitoring configured
  - [ ] SSL certificate expiration monitoring

### Metrics Collection

- [ ] **Performance Metrics**
  - [ ] Metrics collection script installed
  - [ ] Metrics storage configured
  - [ ] Metrics visualization accessible
  - [ ] Historical data retention configured

## Backup Configuration

### Database Backups

- [ ] **Automated Backups**
  - [ ] Daily database backup script installed
  - [ ] Backup retention policy configured (30 days)
  - [ ] Backup integrity verification enabled
  - [ ] Backup storage location secured

- [ ] **Configuration Backups**
  - [ ] Configuration backup script installed
  - [ ] Weekly configuration backups scheduled
  - [ ] Backup includes all critical files
  - [ ] Restore procedure documented and tested

### Disaster Recovery

- [ ] **Recovery Procedures**
  - [ ] Disaster recovery script created
  - [ ] Recovery procedure documented
  - [ ] Recovery time objectives defined
  - [ ] Recovery point objectives defined

- [ ] **Testing**
  - [ ] Backup restore tested successfully
  - [ ] Service recovery tested
  - [ ] Data integrity verified after recovery
  - [ ] Recovery documentation updated

## Security Hardening

### System Security

- [ ] **Access Controls**
  - [ ] Service runs as non-root user
  - [ ] File permissions are minimal
  - [ ] Database access is restricted
  - [ ] SSH access is secured

- [ ] **Network Security**
  - [ ] Firewall rules are restrictive
  - [ ] Unnecessary services are disabled
  - [ ] Network interfaces are secured
  - [ ] VPN access configured if needed

### Application Security

- [ ] **Configuration Security**
  - [ ] Secrets are not in version control
  - [ ] Configuration files have restricted permissions
  - [ ] Default passwords are changed
  - [ ] Debug modes are disabled

- [ ] **Runtime Security**
  - [ ] Input validation is active
  - [ ] SQL injection protection is enabled
  - [ ] XSS protection is enabled
  - [ ] CSRF protection is enabled

## Documentation Updates

### Technical Documentation

- [ ] **Deployment Documentation**
  - [ ] Installation procedures updated
  - [ ] Configuration examples updated
  - [ ] Troubleshooting guide updated
  - [ ] API documentation updated

- [ ] **Operational Documentation**
  - [ ] Monitoring procedures documented
  - [ ] Backup procedures documented
  - [ ] Recovery procedures documented
  - [ ] Maintenance schedules documented

### User Documentation

- [ ] **User Guides**
  - [ ] Login procedures documented
  - [ ] Feature usage documented
  - [ ] Troubleshooting guide for users
  - [ ] FAQ updated

## Final Verification

### Integration Testing

- [ ] **End-to-End Testing**
  - [ ] Complete user workflow tested
  - [ ] All features function correctly
  - [ ] Performance meets requirements
  - [ ] Security controls are effective

- [ ] **Load Testing**
  - [ ] Concurrent user testing completed
  - [ ] Performance under load verified
  - [ ] Resource limits tested
  - [ ] Failure scenarios tested

### Sign-Off

- [ ] **Technical Sign-Off**
  - [ ] Development team approval
  - [ ] Operations team approval
  - [ ] Security team approval
  - [ ] Performance requirements met

- [ ] **Business Sign-Off**
  - [ ] Functional requirements met
  - [ ] User acceptance testing completed
  - [ ] Documentation complete
  - [ ] Training completed

## Post-Deployment Tasks

### Immediate Tasks (First 24 Hours)

- [ ] Monitor service stability
- [ ] Check error logs for issues
- [ ] Verify backup procedures
- [ ] Monitor performance metrics
- [ ] Confirm user access

### Short-Term Tasks (First Week)

- [ ] Review performance trends
- [ ] Optimize configuration if needed
- [ ] Update monitoring thresholds
- [ ] Collect user feedback
- [ ] Plan any necessary adjustments

### Long-Term Tasks (First Month)

- [ ] Analyze usage patterns
- [ ] Plan capacity adjustments
- [ ] Review security logs
- [ ] Update documentation based on experience
- [ ] Plan future enhancements

---

**Deployment Completed By:** _________________ **Date:** _________________

**Verified By:** _________________ **Date:** _________________

**Approved By:** _________________ **Date:** _________________
