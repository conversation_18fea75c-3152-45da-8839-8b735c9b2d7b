# Real-time auto-commit script for Project Tracker
# This script monitors file changes and commits them immediately

$repoPath = "D:\Codeium\CHCIT\project-tracker"
Set-Location $repoPath

# Create a FileSystemWatcher to monitor the repository
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = $repoPath
$watcher.IncludeSubdirectories = $true
$watcher.EnableRaisingEvents = $false  # We'll use WaitForChanged instead of events

# Function to add and commit changes
function Add-GitCommit {
    param (
        [string]$changeType,
        [string]$filePath
    )
    
    # Get relative path for the commit message
    $relativePath = $filePath.Replace("$repoPath\\", "")
    
    # Only commit if the change is not in the .git directory
    if ($relativePath -notlike ".git*") {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $commitMessage = "Auto-commit: $changeType to $relativePath at $timestamp"
        
        # Add all changes (including the specific file and any other changes)
        git add .
        
        # Check if there are staged changes before committing
        $status = git status --porcelain
        if ($status) {
            # Commit changes
            git commit -m $commitMessage
            
            # Push changes to remote repository
            git push
            
            # Log the commit
            $logEntry = "$timestamp - $commitMessage"
            Add-Content -Path "$repoPath\auto-commit.log" -Value $logEntry
            
            Write-Host "Committed changes at $timestamp" -ForegroundColor Green
            Write-Host "Commit message: $commitMessage" -ForegroundColor Cyan
            Write-Host "Pushed changes to remote repository" -ForegroundColor Green
        }
    }
}

# Function to handle debouncing (avoid multiple commits for rapid changes)
$pendingChanges = @{}
$debounceSeconds = 2  # Wait for 2 seconds of inactivity before committing
$lastChangeTime = Get-Date

function Update-PendingChanges {
    $now = Get-Date
    
    # If we have pending changes and enough time has passed since the last change
    if ($pendingChanges.Count -gt 0 -and ($now - $lastChangeTime).TotalSeconds -ge $debounceSeconds) {
        # Commit all pending changes
        $changeTypes = $pendingChanges.Values | Sort-Object -Unique
        $changeTypeStr = $changeTypes -join ", "
        $fileCount = $pendingChanges.Count
        
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $commitMessage = "Auto-commit: $changeTypeStr to $fileCount files at $timestamp"
        
        # Add all changes
        git add .
        
        # Check if there are staged changes before committing
        $status = git status --porcelain
        if ($status) {
            # Commit changes
            git commit -m $commitMessage
            
            # Push changes to remote repository
            git push
            
            # Log the commit
            $logEntry = "$timestamp - $commitMessage"
            Add-Content -Path "$repoPath\auto-commit.log" -Value $logEntry
            
            Write-Host "Committed changes at $timestamp" -ForegroundColor Green
            Write-Host "Commit message: $commitMessage" -ForegroundColor Cyan
            Write-Host "Pushed changes to remote repository" -ForegroundColor Green
        }
        
        # Clear pending changes
        $pendingChanges.Clear()
    }
}

# Main monitoring loop
Write-Host "Starting real-time auto-commit monitoring for $repoPath" -ForegroundColor Cyan
Write-Host "Changes will be committed automatically after $debounceSeconds seconds of inactivity" -ForegroundColor Yellow
Write-Host "Changes will be pushed to the remote Git server automatically" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop monitoring" -ForegroundColor Yellow

try {
    while ($true) {
        # Monitor for changes with a short timeout
        $result = $watcher.WaitForChanged([System.IO.WatcherChangeTypes]::All, 1000)
        
        # If a change was detected
        if (-not $result.TimedOut) {
            $filePath = Join-Path -Path $repoPath -ChildPath $result.Name
            $changeType = $result.ChangeType.ToString()
            
            # Only process if the change is not in the .git directory
            if ($result.Name -notlike ".git*") {
                Write-Host "Change detected: $changeType to $($result.Name)" -ForegroundColor Yellow
                
                # Update the pending changes
                $pendingChanges[$result.Name] = $changeType
                $lastChangeTime = Get-Date
            }
        }
        
        # Process any pending changes
        Update-PendingChanges
    }
} catch {
    Write-Host "Real-time auto-commit monitoring stopped" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
