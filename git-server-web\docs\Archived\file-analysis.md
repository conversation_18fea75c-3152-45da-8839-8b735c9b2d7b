# Git Dashboard Files Analysis

## Files to Keep

### Core Documentation
- `git-dashboard-architecture.md` - Comprehensive documentation of the system
- `git-dashboard-backup.tar.gz` - Complete backup of the system

### Key Fixed Files
- `dashboard-fixed.html` - Latest fixed version of the dashboard HTML
- `dashboard-fix.js` - Fixed dashboard JavaScript for the dashboard
- `metrics-history-fix.js` - Fixed metrics history JavaScript file

### Debugging Tools
- `debug-index.html` - Debugging page for testing API endpoints
- `status-page.html` - Status page for system monitoring

## Files That Can Be Deleted

### Redundant or Obsolete Files
- `fixed-dashboard-new.js` - Superseded by dashboard-fix.js
- `fixed-dashboard.js` - Superseded by dashboard-fix.js
- `fixed-git-dashboard-nginx.conf` - Included in backup
- `fixed-git-dashboard-nginx2.conf` - Included in backup
- `fixed-git-dashboard.conf` - Included in backup
- `fixed-git-dashboard.service` - Included in backup
- `nginx-git-dashboard.conf` - Included in backup
- `simple-git-dashboard.conf` - Included in backup
- `test-dashboard.html` - Testing file, no longer needed
- `debug-patch.js` - Superseded by more comprehensive fixes
- `debug.js` - Superseded by debug-index.html
- `simple-debug.js` - Superseded by debug-index.html
- `fixed-collect-metrics-v2.sh` - Included in backup
- `fixed-collect-metrics-v3.sh` - Included in backup
- `fixed-collect-metrics.sh` - Included in backup
- `fixed-metrics-history-new.js` - Superseded by metrics-history-fix.js
- `fixed-metrics-history.js` - Superseded by metrics-history-fix.js

## Recommendation

All temporary and development files should be moved to the `git-server-web-temp` directory to keep the project root clean. The backup file and architecture documentation should be kept for reference.
