# Frontend Development Setup Guide

## Local Development Environment

### Prerequisites

- Node.js 16.x or higher
- npm 8.x or higher

### Initial Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd project-tracker
   ```

2. Install frontend dependencies:
   ```bash
   cd frontend
   npm install --package-lock-only front-package.json
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your local configuration
   ```

### Development Workflow

#### Running the Development Server

```bash
# Start the development server with hot reload
npm run dev

# Run in production mode locally
npm run build
npm run preview
```

#### Code Quality Tools

We use pre-commit hooks for code quality checks:

```bash
# Install pre-commit hooks
npm run prepare

# Run linting manually
npm run lint

# Run formatting manually
npm run format
```

#### Testing

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate code coverage report
npm run test:coverage
```

### Hot Reload Configuration

The development server supports hot module replacement (HMR) for faster development:

- React components update without page refresh
- CSS changes apply instantly
- TypeScript type checking runs in background

### Connecting to Backend Services

The frontend connects to the backend server through:

1. REST API for data operations
2. WebSocket for real-time updates

During local development:
- Backend API is proxied through development server to avoid CORS issues
- WebSocket connection uses the same proxy

### Building for Production

```bash
# Create production build
npm run build

# Preview production build locally
npm run preview
```

### Troubleshooting

#### Common Issues

1. **Module resolution errors**:
   - Verify `front-tsconfig.json` paths are correct
   - Run `npm install` to ensure all dependencies are installed

2. **Type errors**:
   - Run `npm run type-check` to identify type issues
   - Ensure types are properly defined in `shared/types`

3. **WebSocket connection issues**:
   - Check that backend WebSocket server is running
   - Verify WebSocket URL in environment configuration
