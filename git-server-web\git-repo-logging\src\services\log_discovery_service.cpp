#include "log_discovery_service.hpp"
#include <iostream>
#include <fstream>
#include <regex>
#include <algorithm>
#include <jsoncpp/json/json.h>

namespace logging {

LogDiscoveryService::LogDiscoveryService(const std::string& logSourcesPath)
    : logSourcesPath_(logSourcesPath) {
    initializeLogPatterns();
}

LogDiscoveryService::~LogDiscoveryService() {
}

bool LogDiscoveryService::scanLogSources() {
    std::lock_guard<std::mutex> lock(mutex_);
    discoveredLogs_.clear();

    try {
        if (!std::filesystem::exists(logSourcesPath_)) {
            std::cerr << "Log sources path does not exist: " << logSourcesPath_ << std::endl;
            return false;
        }

        std::cout << "Scanning for log files in: " << logSourcesPath_ << std::endl;

        // Recursively scan the directory
        for (const auto& entry : std::filesystem::recursive_directory_iterator(logSourcesPath_)) {
            if (!std::filesystem::is_regular_file(entry)) {
                continue;
            }

            const auto& path = entry.path().string();
            
            // Skip very large files (> 100MB) for performance
            auto fileSize = std::filesystem::file_size(entry);
            if (fileSize > 100 * 1024 * 1024) {
                std::cout << "Skipping large file: " << path << " (" << fileSize / (1024 * 1024) << " MB)" << std::endl;
                continue;
            }

            // Check if this is a log file
            bool isLogFile = false;
            
            // Check file extension
            auto extension = entry.path().extension().string();
            if (extension == ".log" || extension == ".txt") {
                isLogFile = true;
            }
            
            // Check file name patterns
            if (!isLogFile) {
                std::string filename = entry.path().filename().string();
                for (const auto& pattern : logPatterns_) {
                    std::regex nameRegex(pattern.namePattern);
                    if (std::regex_search(filename, nameRegex)) {
                        isLogFile = true;
                        break;
                    }
                }
            }
            
            // If still not identified, check content
            if (!isLogFile) {
                std::string content = sampleFileContent(path);
                for (const auto& pattern : logPatterns_) {
                    if (!pattern.contentPattern.empty()) {
                        std::regex contentRegex(pattern.contentPattern);
                        if (std::regex_search(content, contentRegex)) {
                            isLogFile = true;
                            break;
                        }
                    }
                }
            }
            
            if (isLogFile) {
                LogFileInfo info;
                info.path = path;
                info.name = entry.path().filename().string();
                info.binary = isBinaryFile(path);
                info.lastModified = std::filesystem::last_write_time(entry);
                info.size = fileSize;
                info.enabled = true;
                
                // Sample content for categorization and parser detection
                std::string content = sampleFileContent(path);
                info.category = categorizeLogFile(path, content);
                info.parser = determineParser(path, content);
                
                discoveredLogs_.push_back(info);
                std::cout << "Discovered log file: " << path << " (Category: " << info.category << ", Parser: " << info.parser << ")" << std::endl;
            }
        }

        std::cout << "Log discovery completed. Found " << discoveredLogs_.size() << " log files." << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error scanning log sources: " << e.what() << std::endl;
        return false;
    }
}

std::vector<LogFileInfo> LogDiscoveryService::getDiscoveredLogs() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return discoveredLogs_;
}

std::vector<LogFileInfo> LogDiscoveryService::getLogsByCategory(const std::string& category) const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<LogFileInfo> result;
    
    for (const auto& log : discoveredLogs_) {
        if (log.category == category) {
            result.push_back(log);
        }
    }
    
    return result;
}

bool LogDiscoveryService::setLogEnabled(const std::string& path, bool enabled) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& log : discoveredLogs_) {
        if (log.path == path) {
            log.enabled = enabled;
            return true;
        }
    }
    
    return false;
}

bool LogDiscoveryService::saveConfiguration(const std::string& configPath) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        Json::Value config;
        
        // Add discovered logs to configuration
        for (const auto& log : discoveredLogs_) {
            Json::Value logConfig;
            logConfig["path"] = log.path;
            logConfig["name"] = log.name;
            logConfig["category"] = log.category;
            logConfig["parser"] = log.parser;
            logConfig["binary"] = log.binary;
            logConfig["enabled"] = log.enabled;
            
            config["logs"].append(logConfig);
        }
        
        // Write configuration to file
        std::ofstream configFile(configPath);
        if (!configFile) {
            std::cerr << "Failed to open configuration file for writing: " << configPath << std::endl;
            return false;
        }
        
        Json::StreamWriterBuilder writer;
        configFile << Json::writeString(writer, config);
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error saving configuration: " << e.what() << std::endl;
        return false;
    }
}

bool LogDiscoveryService::loadConfiguration(const std::string& configPath) {
    try {
        if (!std::filesystem::exists(configPath)) {
            std::cout << "Configuration file does not exist: " << configPath << std::endl;
            return false;
        }
        
        std::ifstream configFile(configPath);
        if (!configFile) {
            std::cerr << "Failed to open configuration file: " << configPath << std::endl;
            return false;
        }
        
        Json::Value config;
        Json::CharReaderBuilder reader;
        std::string errors;
        
        if (!Json::parseFromStream(reader, configFile, &config, &errors)) {
            std::cerr << "Failed to parse configuration file: " << errors << std::endl;
            return false;
        }
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Clear existing logs
        discoveredLogs_.clear();
        
        // Load logs from configuration
        const Json::Value& logs = config["logs"];
        for (const auto& logConfig : logs) {
            LogFileInfo info;
            info.path = logConfig["path"].asString();
            info.name = logConfig["name"].asString();
            info.category = logConfig["category"].asString();
            info.parser = logConfig["parser"].asString();
            info.binary = logConfig["binary"].asBool();
            info.enabled = logConfig["enabled"].asBool();
            
            // Update file metadata
            if (std::filesystem::exists(info.path)) {
                info.lastModified = std::filesystem::last_write_time(info.path);
                info.size = std::filesystem::file_size(info.path);
                discoveredLogs_.push_back(info);
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error loading configuration: " << e.what() << std::endl;
        return false;
    }
}

std::string LogDiscoveryService::detectLogType(const std::string& path) {
    // Default to "unknown"
    std::string logType = "unknown";
    
    // Check file name patterns
    std::string filename = std::filesystem::path(path).filename().string();
    for (const auto& pattern : logPatterns_) {
        std::regex nameRegex(pattern.namePattern);
        if (std::regex_search(filename, nameRegex)) {
            return pattern.category;
        }
    }
    
    // Check content patterns
    std::string content = sampleFileContent(path);
    for (const auto& pattern : logPatterns_) {
        if (!pattern.contentPattern.empty()) {
            std::regex contentRegex(pattern.contentPattern);
            if (std::regex_search(content, contentRegex)) {
                return pattern.category;
            }
        }
    }
    
    return logType;
}

std::string LogDiscoveryService::categorizeLogFile(const std::string& path, const std::string& content) {
    // Default category is "other"
    std::string category = "other";
    
    // Check path for common categories
    if (path.find("/syslog") != std::string::npos || 
        path.find("/messages") != std::string::npos ||
        path.find("/kern.log") != std::string::npos ||
        path.find("/dmesg") != std::string::npos) {
        category = "system";
    } else if (path.find("/auth.log") != std::string::npos || 
               path.find("/secure") != std::string::npos ||
               path.find("/audit") != std::string::npos) {
        category = "security";
    } else if (path.find("/nginx") != std::string::npos || 
               path.find("/apache") != std::string::npos ||
               path.find("/httpd") != std::string::npos) {
        category = "web";
    } else if (path.find("/mysql") != std::string::npos || 
               path.find("/postgresql") != std::string::npos ||
               path.find("/mariadb") != std::string::npos) {
        category = "database";
    } else if (path.find("/mail") != std::string::npos || 
               path.find("/postfix") != std::string::npos ||
               path.find("/dovecot") != std::string::npos) {
        category = "mail";
    } else if (path.find("/git") != std::string::npos) {
        category = "git";
    } else if (path.find("/dashboard") != std::string::npos || 
               path.find("/git-dashboard") != std::string::npos) {
        category = "dashboard";
    }
    
    // Check content patterns if still categorized as "other"
    if (category == "other") {
        // Check for system log patterns
        if (std::regex_search(content, std::regex("kernel:|systemd\\[|systemd-journald|systemd-udevd"))) {
            category = "system";
        }
        // Check for security log patterns
        else if (std::regex_search(content, std::regex("authentication|authorized|permission|sudo:|su:|login:|ssh[d]?:|failed password"))) {
            category = "security";
        }
        // Check for web server log patterns
        else if (std::regex_search(content, std::regex("GET /|POST /|HTTP/[0-9]\\.[0-9]|nginx|apache"))) {
            category = "web";
        }
        // Check for database log patterns
        else if (std::regex_search(content, std::regex("mysql|postgresql|mariadb|query|transaction|database"))) {
            category = "database";
        }
        // Check for mail log patterns
        else if (std::regex_search(content, std::regex("postfix|dovecot|mail|smtp|imap|pop3"))) {
            category = "mail";
        }
        // Check for git log patterns
        else if (std::regex_search(content, std::regex("git|repository|commit|branch|merge|pull|push"))) {
            category = "git";
        }
    }
    
    return category;
}

std::string LogDiscoveryService::determineParser(const std::string& path, const std::string& content) {
    // Default parser is "generic"
    std::string parser = "generic";
    
    // Check file name patterns
    std::string filename = std::filesystem::path(path).filename().string();
    for (const auto& pattern : logPatterns_) {
        std::regex nameRegex(pattern.namePattern);
        if (std::regex_search(filename, nameRegex)) {
            return pattern.parser;
        }
    }
    
    // Check content patterns
    for (const auto& pattern : logPatterns_) {
        if (!pattern.contentPattern.empty()) {
            std::regex contentRegex(pattern.contentPattern);
            if (std::regex_search(content, contentRegex)) {
                return pattern.parser;
            }
        }
    }
    
    // Determine parser based on content analysis
    if (std::regex_search(content, std::regex("^[A-Za-z]{3}\\s+\\d{1,2}\\s+\\d{2}:\\d{2}:\\d{2}"))) {
        parser = "syslog";
    } else if (std::regex_search(content, std::regex("\\d{4}/\\d{2}/\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}"))) {
        parser = "nginx";
    } else if (std::regex_search(content, std::regex("\\[\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}\\]"))) {
        parser = "apache";
    } else if (std::regex_search(content, std::regex("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}"))) {
        parser = "iso8601";
    } else if (std::regex_search(content, std::regex("\\{.*\\}"))) {
        parser = "json";
    }
    
    return parser;
}

bool LogDiscoveryService::isBinaryFile(const std::string& path) {
    std::ifstream file(path, std::ios::binary);
    if (!file) {
        return false;
    }
    
    char buffer[1024];
    file.read(buffer, sizeof(buffer));
    std::streamsize bytesRead = file.gcount();
    
    // Check for null bytes or non-printable characters
    for (std::streamsize i = 0; i < bytesRead; ++i) {
        if (buffer[i] == 0 || (static_cast<unsigned char>(buffer[i]) < 32 && 
                               buffer[i] != '\n' && 
                               buffer[i] != '\r' && 
                               buffer[i] != '\t')) {
            return true;
        }
    }
    
    return false;
}

std::string LogDiscoveryService::sampleFileContent(const std::string& path, size_t maxBytes) {
    std::ifstream file(path, std::ios::binary);
    if (!file) {
        return "";
    }
    
    std::string content;
    content.resize(maxBytes);
    
    file.read(&content[0], maxBytes);
    content.resize(file.gcount());
    
    return content;
}

void LogDiscoveryService::initializeLogPatterns() {
    // System logs
    logPatterns_.push_back({"syslog", "^[A-Za-z]{3}\\s+\\d{1,2}\\s+\\d{2}:\\d{2}:\\d{2}", "system", "syslog"});
    logPatterns_.push_back({"messages", "^[A-Za-z]{3}\\s+\\d{1,2}\\s+\\d{2}:\\d{2}:\\d{2}", "system", "syslog"});
    logPatterns_.push_back({"kern.log", "kernel:", "system", "syslog"});
    logPatterns_.push_back({"dmesg", "\\[\\s*\\d+\\.\\d+\\]", "system", "dmesg"});
    logPatterns_.push_back({"boot.log", "", "system", "syslog"});
    
    // Security logs
    logPatterns_.push_back({"auth.log", "authentication|authorized|permission|sudo:|su:", "security", "syslog"});
    logPatterns_.push_back({"secure", "authentication|authorized|permission|sudo:|su:", "security", "syslog"});
    logPatterns_.push_back({"audit.log", "", "security", "audit"});
    logPatterns_.push_back({"faillog", "", "security", "binary"});
    logPatterns_.push_back({"lastlog", "", "security", "binary"});
    logPatterns_.push_back({"btmp", "", "security", "binary"});
    logPatterns_.push_back({"wtmp", "", "security", "binary"});
    
    // Web server logs
    logPatterns_.push_back({"access.log", "GET /|POST /|HTTP/[0-9]\\.[0-9]", "web", "nginx-access"});
    logPatterns_.push_back({"error.log", "", "web", "nginx-error"});
    
    // Database logs
    logPatterns_.push_back({"mysql.log", "", "database", "mysql"});
    logPatterns_.push_back({"mariadb.log", "", "database", "mysql"});
    logPatterns_.push_back({"postgresql-.*\\.log", "", "database", "postgresql"});
    
    // Mail logs
    logPatterns_.push_back({"mail.log", "", "mail", "syslog"});
    logPatterns_.push_back({"maillog", "", "mail", "syslog"});
    
    // Git logs
    logPatterns_.push_back({"git-.*\\.log", "", "git", "git"});
    
    // Dashboard logs
    logPatterns_.push_back({"git-dashboard.log", "", "dashboard", "dashboard"});
}

} // namespace logging
