-- Git Repository Logging Agent Database Schema

-- Create schema_version table to track schema changes
CREATE TABLE IF NOT EXISTS schema_version (
  id SERIAL PRIMARY KEY,
  version VARCHAR(50) NOT NULL,
  description TEXT,
  applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert current schema version
INSERT INTO schema_version (version, description)
VALUES ('1.0.0', 'Initial schema version')
ON CONFLICT DO NOTHING;

-- <PERSON>reate logs table
CREATE TABLE IF NOT EXISTS logs (
  id BIGSERIAL PRIMARY KEY,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  source VARCHAR(255) NOT NULL,
  log_level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  raw_data TEXT,
  metadata JSONB
);

-- Create log_stats table for caching statistics
CREATE TABLE IF NOT EXISTS log_stats (
  id SERIAL PRIMARY KEY,
  stat_name VARCHAR(255) NOT NULL UNIQUE,
  stat_value JSONB NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create log_retention_policies table
CREATE TABLE IF NOT EXISTS log_retention_policies (
  id SERIAL PRIMARY KEY,
  source VARCHAR(255) NOT NULL UNIQUE,
  retention_days INTEGER NOT NULL DEFAULT 30,
  max_entries INTEGER,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for common queries
CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);
CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(log_level);
CREATE INDEX IF NOT EXISTS idx_logs_metadata ON logs USING GIN (metadata);

-- Insert default retention policies
INSERT INTO log_retention_policies (source, retention_days, max_entries)
VALUES
  ('system', 30, 100000),
  ('security', 90, 200000),
  ('web', 15, 50000),
  ('database', 15, 50000),
  ('mail', 30, 50000),
  ('git', 60, 100000),
  ('dashboard', 30, 50000),
  ('other', 15, 25000)
ON CONFLICT (source) DO NOTHING;

-- Create function to automatically prune old logs
CREATE OR REPLACE FUNCTION prune_old_logs()
RETURNS void AS $$
DECLARE
  source_rec RECORD;
BEGIN
  FOR source_rec IN SELECT source, retention_days FROM log_retention_policies LOOP
    -- Delete logs older than retention_days
    EXECUTE format('DELETE FROM logs WHERE source = %L AND timestamp < NOW() - INTERVAL ''%s days''',
                  source_rec.source, source_rec.retention_days);

    -- If max_entries is set, keep only the most recent max_entries
    IF source_rec.max_entries IS NOT NULL THEN
      EXECUTE format('DELETE FROM logs WHERE source = %L AND id NOT IN (
                      SELECT id FROM logs WHERE source = %L ORDER BY timestamp DESC LIMIT %s)',
                    source_rec.source, source_rec.source, source_rec.max_entries);
    END IF;
  END LOOP;

  -- Update log stats
  DELETE FROM log_stats WHERE stat_name = 'last_pruned';
  INSERT INTO log_stats (stat_name, stat_value, updated_at)
  VALUES ('last_pruned', json_build_object('timestamp', NOW()), NOW());
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at field
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_log_retention_policies_updated_at
BEFORE UPDATE ON log_retention_policies
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE logs IS 'Stores all log entries collected by the logging agent';
COMMENT ON TABLE log_stats IS 'Stores statistics and metadata about the logs';
COMMENT ON TABLE log_retention_policies IS 'Defines retention policies for different log sources';
COMMENT ON FUNCTION prune_old_logs() IS 'Automatically prunes old logs based on retention policies';
