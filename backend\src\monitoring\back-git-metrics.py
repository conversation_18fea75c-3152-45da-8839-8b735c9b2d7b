#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Git Metrics Processing Module

This module processes and stores Git operation metrics collected from the frontend.
It integrates with PostgreSQL for data storage and Redis for caching frequently accessed metrics.
"""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union

import psycopg2
import redis
from psycopg2.extras import Json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('git_metrics.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('git_metrics')


class GitMetricsProcessor:
    """Processes and stores Git operation metrics"""

    def __init__(self, db_config: Dict[str, str], redis_config: Dict[str, Union[str, int]]):
        """Initialize the Git metrics processor

        Args:
            db_config: PostgreSQL database configuration
            redis_config: Redis configuration
        """
        self.db_config = db_config
        self.redis_config = redis_config
        self.redis_client = self._init_redis()
        self._ensure_tables()

    def _init_redis(self) -> redis.Redis:
        """Initialize Redis connection

        Returns:
            Redis client instance
        """
        try:
            client = redis.Redis(
                host=self.redis_config.get('host', 'localhost'),
                port=self.redis_config.get('port', 6379),
                db=self.redis_config.get('db', 0),
                decode_responses=True
            )
            client.ping()  # Test connection
            logger.info("Redis connection established")
            return client
        except redis.ConnectionError as e:
            logger.error(f"Redis connection error: {e}")
            logger.warning("Continuing without Redis caching")
            return None

    def _get_db_connection(self):
        """Get a connection to the PostgreSQL database

        Returns:
            Database connection object
        """
        return psycopg2.connect(
            dbname=self.db_config.get('dbname'),
            user=self.db_config.get('user'),
            password=self.db_config.get('password'),
            host=self.db_config.get('host', 'localhost'),
            port=self.db_config.get('port', 5432)
        )

    def _ensure_tables(self) -> None:
        """Ensure required database tables exist"""
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Create git_operations table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS git_operations (
                    id SERIAL PRIMARY KEY,
                    operation_type VARCHAR(50) NOT NULL,
                    duration FLOAT NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    repository VARCHAR(255),
                    branch VARCHAR(100),
                    user_id VARCHAR(100) NOT NULL,
                    project_id VARCHAR(100),
                    commit_count INTEGER,
                    file_count INTEGER,
                    metadata JSONB,
                    timestamp TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_git_operations_operation_type 
                ON git_operations(operation_type);
                
                CREATE INDEX IF NOT EXISTS idx_git_operations_timestamp 
                ON git_operations(timestamp);
                
                CREATE INDEX IF NOT EXISTS idx_git_operations_user_id 
                ON git_operations(user_id);
            """)

            # Create git_operation_stats table for aggregated statistics
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS git_operation_stats (
                    id SERIAL PRIMARY KEY,
                    operation_type VARCHAR(50) NOT NULL,
                    time_period VARCHAR(20) NOT NULL,  -- 'hourly', 'daily', 'weekly'
                    period_start TIMESTAMP NOT NULL,
                    count INTEGER NOT NULL,
                    avg_duration FLOAT NOT NULL,
                    min_duration FLOAT NOT NULL,
                    max_duration FLOAT NOT NULL,
                    success_count INTEGER NOT NULL,
                    error_count INTEGER NOT NULL,
                    warning_count INTEGER NOT NULL,
                    metadata JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(operation_type, time_period, period_start)
                );
            """)

            conn.commit()
            logger.info("Database tables verified")
        except Exception as e:
            logger.error(f"Database initialization error: {e}")
            raise
        finally:
            if conn:
                cursor.close()
                conn.close()

    def store_metrics(self, metrics: List[Dict[str, Any]]) -> None:
        """Store Git operation metrics in the database

        Args:
            metrics: List of metrics to store
        """
        if not metrics:
            return

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            for metric in metrics:
                if metric.get('category') != 'git':
                    continue

                metadata = metric.get('metadata', {})
                
                cursor.execute("""
                    INSERT INTO git_operations 
                    (operation_type, duration, status, repository, branch, 
                     user_id, project_id, commit_count, file_count, metadata, timestamp)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    metric.get('operation'),
                    metric.get('duration'),
                    metric.get('status'),
                    metadata.get('repository'),
                    metadata.get('branch'),
                    metadata.get('userId'),
                    metadata.get('projectId'),
                    metadata.get('commitCount'),
                    metadata.get('fileCount'),
                    Json(metadata),
                    datetime.fromisoformat(metric.get('timestamp').replace('Z', '+00:00'))
                ))

            conn.commit()
            logger.info(f"Stored {len(metrics)} git operation metrics")
        except Exception as e:
            logger.error(f"Error storing metrics: {e}")
            conn.rollback()
            raise
        finally:
            if conn:
                cursor.close()
                conn.close()

    def get_recent_operations(self, operation_type: Optional[str] = None, 
                             limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent Git operations

        Args:
            operation_type: Filter by operation type (optional)
            limit: Maximum number of operations to return

        Returns:
            List of recent operations
        """
        # Try to get from cache first
        cache_key = f"recent_operations:{operation_type or 'all'}:{limit}"
        if self.redis_client:
            cached = self.redis_client.get(cache_key)
            if cached:
                return json.loads(cached)

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            query = """
                SELECT id, operation_type, duration, status, repository, branch,
                       user_id, project_id, commit_count, file_count, metadata, timestamp
                FROM git_operations
            """
            
            params = []
            if operation_type:
                query += " WHERE operation_type = %s"
                params.append(operation_type)
                
            query += " ORDER BY timestamp DESC LIMIT %s"
            params.append(limit)

            cursor.execute(query, params)
            columns = [desc[0] for desc in cursor.description]
            operations = [dict(zip(columns, row)) for row in cursor.fetchall()]

            # Cache the results
            if self.redis_client:
                self.redis_client.setex(
                    cache_key,
                    300,  # Cache for 5 minutes
                    json.dumps(operations, default=str)
                )

            return operations
        except Exception as e:
            logger.error(f"Error retrieving recent operations: {e}")
            return []
        finally:
            if conn:
                cursor.close()
                conn.close()

    def get_operation_stats(self, operation_type: Optional[str] = None, 
                           time_period: str = 'daily',
                           days: int = 30) -> List[Dict[str, Any]]:
        """Get Git operation statistics

        Args:
            operation_type: Filter by operation type (optional)
            time_period: Time period for aggregation ('hourly', 'daily', 'weekly')
            days: Number of days to include

        Returns:
            List of operation statistics
        """
        # Try to get from cache first
        cache_key = f"operation_stats:{operation_type or 'all'}:{time_period}:{days}"
        if self.redis_client:
            cached = self.redis_client.get(cache_key)
            if cached:
                return json.loads(cached)

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            query = """
                SELECT operation_type, time_period, period_start, count, 
                       avg_duration, min_duration, max_duration,
                       success_count, error_count, warning_count
                FROM git_operation_stats
                WHERE time_period = %s
                AND period_start >= %s
            """
            
            params = [time_period, datetime.now() - timedelta(days=days)]
            
            if operation_type:
                query += " AND operation_type = %s"
                params.append(operation_type)
                
            query += " ORDER BY period_start DESC"

            cursor.execute(query, params)
            columns = [desc[0] for desc in cursor.description]
            stats = [dict(zip(columns, row)) for row in cursor.fetchall()]

            # Cache the results
            if self.redis_client:
                self.redis_client.setex(
                    cache_key,
                    600,  # Cache for 10 minutes
                    json.dumps(stats, default=str)
                )

            return stats
        except Exception as e:
            logger.error(f"Error retrieving operation stats: {e}")
            return []
        finally:
            if conn:
                cursor.close()
                conn.close()

    def calculate_and_store_stats(self, time_period: str = 'daily') -> None:
        """Calculate and store aggregated statistics

        Args:
            time_period: Time period for aggregation ('hourly', 'daily', 'weekly')
        """
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Define time grouping based on period
            if time_period == 'hourly':
                time_group = "DATE_TRUNC('hour', timestamp)"
                lookback = "timestamp >= NOW() - INTERVAL '48 hours'"
            elif time_period == 'weekly':
                time_group = "DATE_TRUNC('week', timestamp)"
                lookback = "timestamp >= NOW() - INTERVAL '12 weeks'"
            else:  # daily is default
                time_group = "DATE_TRUNC('day', timestamp)"
                lookback = "timestamp >= NOW() - INTERVAL '60 days'"

            # Calculate statistics for each operation type and time period
            cursor.execute(f"""
                INSERT INTO git_operation_stats
                (operation_type, time_period, period_start, count, 
                 avg_duration, min_duration, max_duration,
                 success_count, error_count, warning_count, metadata)
                SELECT 
                    operation_type,
                    %s as time_period,
                    {time_group} as period_start,
                    COUNT(*) as count,
                    AVG(duration) as avg_duration,
                    MIN(duration) as min_duration,
                    MAX(duration) as max_duration,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_count,
                    SUM(CASE WHEN status = 'warning' THEN 1 ELSE 0 END) as warning_count,
                    jsonb_build_object(
                        'repositories', jsonb_agg(DISTINCT repository),
                        'branches', jsonb_agg(DISTINCT branch)
                    ) as metadata
                FROM git_operations
                WHERE {lookback}
                GROUP BY operation_type, {time_group}
                ON CONFLICT (operation_type, time_period, period_start)
                DO UPDATE SET
                    count = EXCLUDED.count,
                    avg_duration = EXCLUDED.avg_duration,
                    min_duration = EXCLUDED.min_duration,
                    max_duration = EXCLUDED.max_duration,
                    success_count = EXCLUDED.success_count,
                    error_count = EXCLUDED.error_count,
                    warning_count = EXCLUDED.warning_count,
                    metadata = EXCLUDED.metadata
            """, (time_period,))

            conn.commit()
            logger.info(f"Calculated and stored {time_period} statistics")
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
            conn.rollback()
        finally:
            if conn:
                cursor.close()
                conn.close()

    def cleanup_old_data(self, retention_days: Dict[str, int]) -> None:
        """Clean up old metrics data based on retention policy

        Args:
            retention_days: Dictionary with retention days for different data types
        """
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Clean up detailed metrics
            detailed_days = retention_days.get('detailed', 30)
            cursor.execute("""
                DELETE FROM git_operations
                WHERE timestamp < NOW() - INTERVAL %s DAY
            """, (detailed_days,))
            detailed_deleted = cursor.rowcount

            # Clean up hourly stats
            hourly_days = retention_days.get('hourly', 7)
            cursor.execute("""
                DELETE FROM git_operation_stats
                WHERE time_period = 'hourly'
                AND period_start < NOW() - INTERVAL %s DAY
            """, (hourly_days,))
            hourly_deleted = cursor.rowcount

            # Clean up daily stats
            daily_days = retention_days.get('daily', 90)
            cursor.execute("""
                DELETE FROM git_operation_stats
                WHERE time_period = 'daily'
                AND period_start < NOW() - INTERVAL %s DAY
            """, (daily_days,))
            daily_deleted = cursor.rowcount

            conn.commit()
            logger.info(f"Cleaned up old data: {detailed_deleted} detailed records, "
                       f"{hourly_deleted} hourly stats, {daily_deleted} daily stats")
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            conn.rollback()
        finally:
            if conn:
                cursor.close()
                conn.close()


# Example usage
if __name__ == "__main__":
    # Configuration
    db_config = {
        'dbname': 'project_tracker',
        'user': 'postgres',
        'password': 'password',
        'host': 'localhost',
        'port': 5432
    }
    
    redis_config = {
        'host': 'localhost',
        'port': 6379,
        'db': 0
    }
    
    # Initialize processor
    processor = GitMetricsProcessor(db_config, redis_config)
    
    # Example: Calculate and store statistics
    processor.calculate_and_store_stats('hourly')
    processor.calculate_and_store_stats('daily')
    processor.calculate_and_store_stats('weekly')
    
    # Example: Clean up old data
    retention_policy = {
        'detailed': 30,  # Keep detailed metrics for 30 days
        'hourly': 7,     # Keep hourly stats for 7 days
        'daily': 90      # Keep daily stats for 90 days
    }
    processor.cleanup_old_data(retention_policy)
